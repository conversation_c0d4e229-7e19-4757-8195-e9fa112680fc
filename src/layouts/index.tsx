import { useEffect } from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { history, Outlet, useRouteProps } from 'umi';

import { WHITE_LIST } from '@/components/route-guard';
import RouteAuthGuard from '@/components/route-guard/auth';
import { PROJECT } from '@/constants';
import Request from '@/request';
import { useAuthStore } from '@/stores/useAuthStore';
import { usePermissionStore } from '@/stores/usePermissionStore';
import { useRightsStore } from '@/stores/useRightsStore';
import { theme } from '@/themes';

import { HeaderPortal } from './components/HeaderPortal/HeaderPortal';
import { Header, HocProvider } from './components';

export default function Layout() {
  const fetchAuthMenu = usePermissionStore(state => state.fetchData);
  const fetchAuthRights = useRightsStore(state => state.fetchData);
  const fetchAuthProject = useAuthStore(state => state.fetchData);
  const route = useRouteProps();

  const projectAuthEnabled = useAuthStore(state => state.projectAuthEnabled);
  const authProjects = useAuthStore(state => state.projects);

  const serviceName = PROJECT.serviceName;

  useEffect(() => {
    fetchAuthProject();
  }, []);

  useEffect(() => {
    if (projectAuthEnabled === undefined) return;

    if (projectAuthEnabled) {
      // 看projects是否有project, 如果有，把第一个projectId写入header
      // 如果没有，提示用户需要有项目才可以进
      let currentProject = localStorage.getItem('__CURRENT_PROJECT__');

      if (!authProjects.find(x => x.id === currentProject)) {
        if (authProjects?.[0]?.id) {
          localStorage.setItem('__CURRENT_PROJECT__', authProjects?.[0]?.id);
          currentProject = authProjects?.[0]?.id;
        }
      }

      Request.extendOptions({
        headers: {
          'Jax-Super-Project-Id': currentProject,
        },
      });
    }
    fetchAuthMenu();
    fetchAuthRights();

    // 不在白名单中的路由，判断是否有项目
    if (!WHITE_LIST.includes(route.path)) {
      if (projectAuthEnabled && authProjects.length === 0) {
        history.push('/no-project');
      }
    }
  }, [projectAuthEnabled, authProjects]);

  return (
    <ConfigProvider theme={theme} locale={zhCN}>
      <HocProvider>
        <RouteAuthGuard>
          {serviceName ? (
            <HeaderPortal projectAuthEnabled={projectAuthEnabled} />
          ) : (
            <Header projectAuthEnabled={projectAuthEnabled} />
          )}
          <Outlet />
        </RouteAuthGuard>
      </HocProvider>
    </ConfigProvider>
    // <StyleProvider
    //   hashPriority='high'
    //   transformers={[legacyLogicalPropertiesTransformer]}
    // >
    // </StyleProvider>
  );
}
