import { Drawer } from 'antd';

import { usePermissionStore } from '@/stores/usePermissionStore';

import { Row } from './Row';

interface Props {
  open: boolean;
  onClose: () => void;
}

export const LeftDrawer = ({ open, onClose }: Props) => {
  const { menuTree } = usePermissionStore();
  return (
    <Drawer placement='left' closable={false} open={open} className='left-drawer w-[620px] flex pl-1' onClose={onClose}>
      {menuTree
        .filter(x => x.isDisplay)
        .sort((cur, next) => cur.sort - next.sort)
        .map(route => {
          return <Row key={route.id} route={route} onClose={onClose} />;
        })}
    </Drawer>
  );
};
