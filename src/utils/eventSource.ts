interface EventSourceOptions {
  onmessage?: (event) => void;
  onerror?: (event) => void;
  onopen?: (event) => void;
}

export class SseEvent {
  _source = null as EventSource;
  url = '';
  options = {};
  constructor(url, options: EventSourceOptions) {
    this.url = url;
    this.options = options;
  }

  private _setCb() {
    if(!this._source) return;
    this._source.onmessage = this.options.onmessage;
    this._source.onerror = this.options.onerror;
    this._source.onopen = this.options.onopen;
  }

  start() {
    this._source = new EventSource(this.url);
    this._setCb();
    
  }

  restart(newUrl?: string) {
    this._source.close();
    this._source = new EventSource(newUrl || this.url);
    this._setCb();
  }

  close() {
    this._source.close();
  }
}
