import type { Dayjs } from 'dayjs';

export function getTimeZone(): string {
  const date = new Date();
  const offset = date.getTimezoneOffset();
  if (offset === 0) {
    return 'UTC+0';
  }

  const delta = Math.abs(offset) / 60;

  if (offset > 0) {
    return `UTC-${delta}`;
  }

  return `UTC+${delta}`;
}

export const TIME_ZONE = getTimeZone();

export function getLocalISOString(dayjs: Dayjs) {
  const [zone, ...values] = dayjs.format('YYYY-MM-DDTHH:mm:ss.SSSZ').split(':').reverse();
  return `${values.reverse().join(':')}${zone}`;
}

export function getTimeFormatFromRange(min: number, max: number) {
  if (min && max) {
    const range = max - min;
    const secPerTick = range / 5 / 1000;
    const oneDay = 86400000;
    const oneYear = 31536000000;

    if (secPerTick <= 60) {
      return 'HH:mm:ss';
    }
    if (secPerTick <= 7200 || range <= oneDay) {
      return 'HH:mm';
    }
    if (secPerTick <= 80000) {
      return 'HH:mm';
    }
    if (secPerTick <= 2419200 || range <= oneYear) {
      return 'MM/DD';
    }
    return 'YYYY-MM';
  }

  return 'HH:mm';
}

export function getRunDurationTime(beginTime, endTime) {
  const start = new Date(beginTime).getTime();
  const end = new Date(endTime).getTime();
  // 两个时间戳相差的毫秒数
  const time = end - start;
  // 计算相差的天数
  const day = Math.floor(time / (24 * 3600 * 1000));
  // 计算天数后剩余的毫秒数
  const msec = time % (24 * 3600 * 1000);
  // 计算出小时数
  const hour = Math.floor(msec / (3600 * 1000));
  // 计算小时数后剩余的毫秒数
  const msec2 = msec % (3600 * 1000);
  // 计算相差分钟数
  const minute = Math.floor(msec2 / (60 * 1000));
  // 计算分钟数后剩余的毫秒数
  const msec3 = msec2 % (60 * 1000);
  // 计算相差秒数
  const second = Math.floor(msec3 / 1000);
  let result: string = '';
  if (day > 0) {
    result = `${day}天${hour}时${minute}分${second}秒`;
  } else if (hour > 0) {
    result = `${hour}时${minute}分${second}秒`;
  } else if (minute > 0) {
    result = `${minute}分${second}秒`;
  } else {
    result = `${second}秒`;
  }

  return result;
}

export function second2minute(second) {
  if (!second) return;

  const s = second % 60;
  const m = Math.floor(second / 60);
  return `${m < 10 ? `0${m}` : m}:${s < 10 ? `0${s}` : s}`;
}

export function getRunDurationTimeByTime(time) {
  if (time === 0) return '0毫秒';
  if (time < 1000) return `${time}毫秒`;
  // 计算相差的天数
  const day = Math.floor(time / (24 * 3600 * 1000));
  // 计算天数后剩余的毫秒数
  const msec = time % (24 * 3600 * 1000);
  // 计算出小时数
  const hour = Math.floor(msec / (3600 * 1000));
  // 计算小时数后剩余的毫秒数
  const msec2 = msec % (3600 * 1000);
  // 计算相差分钟数
  const minute = Math.floor(msec2 / (60 * 1000));
  // 计算分钟数后剩余的毫秒数
  const msec3 = msec2 % (60 * 1000);
  // 计算相差秒数
  const second = Math.floor(msec3 / 1000);
  let result: string = '';
  if (day > 0) {
    result = `${day}天${hour}时${minute}分${second}秒`;
  } else if (hour > 0) {
    result = `${hour}时${minute}分${second}秒`;
  } else if (minute > 0) {
    result = `${minute}分${second}秒`;
  } else {
    result = `${second}秒`;
  }

  return result;
}
