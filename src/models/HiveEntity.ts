// 消费策略，全量-earliest、首次最新-latest
export enum HiveStrategyEnum {
  'earliest' = '全量',
  'latest' = '首次最新',
}

// 数据格式，JSON
export enum HiveDataFormat {
  'JSON' = 'JSON',
}

// 字段映射，json
export interface HiveSchemaMappingListItem {
  id: string;
  sourceField: string;
  sourceType: string;
  sinkField: string;
  sinkType: string;
  customSql: string;
}

// 提交分区后通知下游的触发策略，更新源数据: metastore, 生成标识文件: success-file
export enum SinkPartitionCommitTrigger {
  'process-time' = '系统时间',
  'partition-time' = '分区时间',
}

// 提交分区后通知下游的触发策略，更新源数据: metastore, 生成标识文件: success-file
export enum SinkPartitionCommitPolicyKind {
  'metastore' = '更新源数据',
  'success-file' = 'success-file',
}

// hive 高级配置高级配置，json
export interface HiveFileSystemSetting {
  sinkRollingPolicyFileSize: number;
  sinkRollingPolicyRolloverInterval: number;
  sinkRollingPolicyCheckInterval: number;
  autoCompaction: boolean;
  compactionFileSize: number;
  sinkPartitionCommitTrigger: keyof typeof SinkPartitionCommitTrigger;
  partitionTimeExtractorTimestampPattern: string;
  sinkPartitionCommitDelay: number;
  sinkPartitionCommitPolicyKind: Array<keyof typeof SinkPartitionCommitPolicyKind>;
}

// hive业务实体
export interface HiveEntity {
  id: string;
  businessFlowId: string;
  name: string;
  kafkaTbId: string;
  strategy: keyof typeof HiveStrategyEnum;
  consumeGroup: string;
  dataFormat: keyof typeof HiveDataFormat;
  ignoreParseError: boolean;
  ignoreMissingField: boolean;
  hiveTbId: string;
  hiveVersion: string;

  schemaMappingList: HiveSchemaMappingListItem[];
  fileSystemSetting: HiveFileSystemSetting;

  clusterId: string;
  yarnSessionId: string;
  optsId: string;
  customSetting: Record<string, any>;
  script: string;
  published: boolean;
  projectAuth?: ProjectAuthModel;
}

export interface HiveCustomSqlQuickOption {
  fn: string;
  name: string;
}
