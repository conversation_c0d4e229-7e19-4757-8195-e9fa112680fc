import Request from '@/request';
import { downloadFile } from '@/utils';

const baseUrl = '/api/v2';
const url = '/api/v2/ingestion/cluster';
const clusterFileUrl = '/api/v2/ingestion/jax-cluster-file';
const hadoopFileUrl = `${url}/hadoop/conf/files`;
export const ClusterApi = {
  /**
   * 集群列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 创建集群
   */
  async create(data: Omit<ClusterEntity, 'id'>, options?) {
    return await Request.post(url, { data, ...options });
  },
  /**
   * 更新集群
   * @param data
   * @returns
   */
  async update(data: ClusterEntity, options?) {
    return await Request.put(`${url}/${data.id}`, { data, ...options });
  },
  /**
   * 删除集群
   * @param id 集群id
   * @returns
   */
  async delete(id: string, options?) {
    return await Request.delete(`${url}/${id}`, { ...options });
  },
  /**
   * 获取集群详情
   * @param id 集群id
   * @returns
   */
  async getDetail(id: string, options?) {
    return await Request.get(`${url}/${id}`, { ...options });
  },
  /**
   * 获取全部集群
   */
  async getAll(params?: { clusterType?: ClusterType; supportOpts?: OptsType }, options?) {
    return await Request.get(`${url}/list`, {
      params,
      ...options,
    });
  },
  /**
   * 获取集群动态配置项
   */
  async getClusterOptions(clusterType: ClusterType) {
    return await Request.get(`${baseUrl}/ingestion/cluster-type/${clusterType}/options`);
  },
  /**
   * 获取集群Yarn application日志
   */
  getYarnApplicationLog: async (clusterId: string, applicationId: string) => {
    return await Request.get<Response<YarnApplicationLog>>(
      `${baseUrl}/ingestion/cluster/${clusterId}/yarn-application/${applicationId}/log`,
    );
  },
  /**
   * 获取集群Yarn application日志内容
   */
  async getYarnApplicationLogContent(params: { url: string; clusterId: string }) {
    const { clusterId, url } = params;
    return await Request.get(`${baseUrl}/ingestion/cluster/${clusterId}/yarn-application-log`, { params: { url } });
  },
  /**
   * 下载集群Yarn application日志内容
   */
  async downloadYarnApplicationLog(params: { url: string; clusterId: string }) {
    const { clusterId, url } = params;
    downloadFile(
      `${baseUrl}/ingestion/cluster/${clusterId}/yarn-application-log/download`,
      {
        url,
      },
      'get',
    );
  },
  /**
   * 获取默认集群
   */
  async getDefaultCluster(clusterType: 'flink' | 'spark' | 'marayarn') {
    return await Request.get(`${url}/default/${clusterType}`);
  },
  async updateFileContent(id, data) {
    return await Request.post(`${hadoopFileUrl}/${id}/content/update`, { data });
  },
  async getFileVersionList(id, fileName) {
    return await Request.get(`${hadoopFileUrl}/${id}/all-versions`, { params: { fileName } });
  },
  async getFileContentWithVersion(id) {
    return await Request.get(`${clusterFileUrl}/${id}`);
  },
  async getClusterNodeList() {
    return await Request.get(`${baseUrl}/jax/cluster/nodes`);
  },
  async getFileListFromIp(id, serverId) {
    return await Request.get(`${hadoopFileUrl}/${id}/fromServer?serverId=${serverId}`);
  },
  async getFileContentFromIp(id, params) {
    return await Request.get(`${hadoopFileUrl}/${id}/content/fromServer`, { params });
  },
  async getFileListFromDb(id) {
    return await Request.get(`${hadoopFileUrl}/${id}/fromDb`);
  },
  async getFileContentFromDb(id, params) {
    return await Request.get(`${hadoopFileUrl}/${id}/content/fromDb`, { params });
  },
  async importClusterFiles(id, data) {
    return await Request.post(`${hadoopFileUrl}/${id}/import/fromServer`, { data });
  },
  async uploadClusterFiles(id, data) {
    return await Request.post(`${hadoopFileUrl}/${id}/upload`, { data });
  },
};
