import { useEffect, useMemo } from 'react';
import { Form, Input, InputNumber, message, Modal, ModalProps, Radio, Switch, TreeSelect } from 'antd';

import { PermissionEntity } from '@/models';
import { PermissionApi } from '@/services';
import { findPath } from '@/utils/treeHelper';

interface Props extends ModalProps {
  item?: PermissionEntity;
  treeData: PermissionEntity[];
  onCallback: () => void;
  selectedId?: string;
}
export const EditAuth: React.FC<Props> = ({ item, treeData, selectedId, onCallback, ...otherProps }) => {
  const [form] = Form.useForm();
  const externalLink = Form.useWatch('externalLink', form);
  const type = Form.useWatch('type', form);
  const parentId = Form.useWatch('parentId', form);
  const paths = useMemo(() => {
    return findPath(treeData, node => {
      return node.id === parentId;
    });
  }, [parentId]);

  const parent = paths?.at(-1);
  const parentIsExternalLink = parent?.externalLink;
  const cannotBeExternalLinkAndOpenInNewWindow = paths?.length >= 2;
  const allTreeData = useMemo(() => {
    return [{
      name: '根路径',
      id: '0',
      children: treeData,
    }];
  }, [treeData]);

  useEffect(() => {
    if (item) {
      form.setFieldsValue(item);
    } else {
      form.setFieldsValue({
        isDisplay: true,
        type: parent?.type === 'menu' ? 'menu' : undefined,
        parentId: selectedId ?? '0',
        externalLink: false,
        openType: undefined,
      });
    }
  }, []);

  const onOk = async () => {
    const values = await form.validateFields();
    if (item) {
      PermissionApi.update({
        id: item.id,
        ...values,
      }).then(res => {
        if (res?.code === '0000') {
          message.success('更新成功');
          onCallback && onCallback();
        }
      });
    } else {
      PermissionApi.create({
        ...values,
      }).then(res => {
        if (res?.code === '0000') {
          message.success('创建成功');
          onCallback && onCallback();
        }
      });
    }
  };
  return (
    <Modal title='配置' width={760} onOk={() => onOk()} {...otherProps}>
      <Form form={form} labelCol={{ span: 4 }}>
        <Form.Item label='名称' name='name' rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item label='唯一编码' name='permission' rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item label='父节点' name='parentId'>
          <TreeSelect treeData={allTreeData} fieldNames={{ label: 'name', value: 'id' }}></TreeSelect>
        </Form.Item>

        <Form.Item label='类型' name='type' rules={[
          { required: true },
          () => {
            return {
              validator(_, value) {
                if (value === 'menu' && parentIsExternalLink) {
                  return Promise.reject(new Error('父节点是外链，无法创建子菜单'));
                }
                if (parent && parent.type !== 'menu') {
                  return Promise.reject(new Error('父节点不是菜单，无法创建子项'));
                }
                return Promise.resolve();
              },
            };
          },
        ]}
        >
          <Radio.Group onChange={() => { form.validateFields(); }} >
            <Radio value='menu' disabled={ parentIsExternalLink || (parent && parent?.type !== 'menu') }>菜单</Radio>
            <Radio value='api' disabled={parent && parent?.type !== 'menu'}>接口</Radio>
            <Radio value='button' disabled={parent && parent?.type !== 'menu'}>按钮</Radio>
            <Radio value='resource' disabled={parent && parent?.type !== 'menu'}>资源</Radio>
          </Radio.Group>
        </Form.Item>
        { type === 'api' && <Form.Item label='请求方式' name='httpMethod' rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value='post'>post</Radio>
            <Radio value='get'>get</Radio>
            <Radio value='put'>put</Radio>
            <Radio value='delete'>delete</Radio>
          </Radio.Group>
        </Form.Item>}
        { type === 'menu' && <Form.Item label='外部链接' name='externalLink' valuePropName="checked">
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </Form.Item>
        }
        {externalLink && <Form.Item
          label='打开方式'
          name='openType'
          rules={[
            { required: true },
            () => {
              return {
                validator(_, value) {
                  if (value === 'newWindow' && cannotBeExternalLinkAndOpenInNewWindow) {
                    return Promise.reject(new Error('三级菜单不能采用新窗口打开'));
                  }
                  return Promise.resolve();
                },
              };
            },
          ]}
        >
          <Radio.Group>
            <Radio value='newWindow'>新窗口打开</Radio>
            <Radio value='iframe'>iframe嵌入</Radio>
          </Radio.Group>
        </Form.Item>}
        {['menu', 'api'].includes(type) && <Form.Item
          label='URI'
          name='uri'
          rules={[{ required: externalLink || type === 'api' }]}
        >
          <Input />
        </Form.Item>}
        {type === 'menu' && <>
          <Form.Item label='图标' name='icon'>
            <Input />
          </Form.Item>
          <Form.Item label='排序号' name='sort'>
            <InputNumber />
          </Form.Item>
          <Form.Item label='是否显示' name='isDisplay'>
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </>}
        { type === 'resource' && <Form.Item label='资源类型' name='resourceType' rules={[{ required: true }]}>
          <Input />
        </Form.Item>}
        <Form.Item label='权限代码' name='code' rules={[{ required: type && type !== 'menu' }]}>
          <Input />
        </Form.Item>
        <Form.Item label='权限代码名' name='codeName' rules={[{ required: type && type !== 'menu' }]}>
          <Input />
        </Form.Item>

        <Form.Item label='描述' name='description'>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};
