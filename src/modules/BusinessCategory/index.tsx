import { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Allotment, LayoutPriority } from 'allotment';
import { Button, message, SelectProps, TableProps, Tooltip } from 'antd';
import { history, useParams } from 'umi';

import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { Detail, Icon } from '@/components/ui';
import { DataBusinessCategoryConfig, DataMartConfig } from '@/constants/deleteUrl';
import { useGlobalHook, useRightsHook } from '@/hooks';
import useDeleteConfirm from '@/hooks/useDeleteConfirm';

import { DataMartFormModal } from '../AppLayer/pages/components/DataMartFormModal';
import { useDmAction } from '../AppLayer/stores/data-mart';
import Empty from '../DataStandard/components/Empty';

import { BizModal } from './components/BizModal';
import { BizTree } from './components/BizTree';
import DatadomainSelect from './components/form/DatadomainSelect';
import { DataRelatingDomainConfig } from './constants/delUrl';
import { BizDetail } from './utils/detail';
import { addNode, deleteNode, transformTreeData, updateNode } from './utils/tree';
import dataMartService from './services';
import { useActions } from './stores';

type TreeNode = Biz.TreeNode;

const {
  queryBizTree,
  queryBiz,
  queryBizTreeChild,
  queryDataDomain,
  queryDataMartList,
  queryrelatingDataDomain,
  createRelatingDomainToBiz,
  createAllRelatingBiz,
} = dataMartService;

export default function BusinessCategory() {
  const { id } = useParams();
  const { hasRoleRights, hasRights } = useRightsHook();

  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [modalTitle, setModalTitle] = useState('');
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>();
  const [dataDomainOptions, setDataDomainOptions] = useState<any>();

  const {
    pagination: domainPagination,
    setPagination: setDomainPagination,
    handleTableChange: handleDomainTableChange,
  } = useCustomTableHook({
    pageSize: 5,
  });
  const {
    pagination: dataMartPagination,
    setPagination: setDataMartPagination,
    handleTableChange: handleDataMartTableChange,
  } = useCustomTableHook({
    pageSize: 5,
  });
  const { pagination, setPagination, handleTableChange } = useCustomTableHook({
    pageSize: 5,
  });
  const { setPageInfo, resetPageInfo } = useGlobalHook();

  const { setCurrentNode, setActionType, toggleModal } = useActions();
  const { toggleModal: tgDatamartModal, setActionType: setType, setCurrentNode: setDatamartNode } = useDmAction();
  const deleteConfirm = useDeleteConfirm(DataBusinessCategoryConfig);
  const queryClient = useQueryClient();
  const { data: treeResult } = useQuery(['getBizTree'], queryBizTree);
  const { data: treeChildResult } = useQuery(
    ['getBizTreeChild', selectedNode?.id],
    () => queryBizTreeChild(selectedNode?.id as string),
    {
      enabled: !!selectedNode?.id,
      onSuccess(data) {
        setPagination({ ...pagination, total: data.length });
      },
    },
  );
  const { data: detailResult } = useQuery(
    ['getBizDetail', selectedNode?.id],
    () => queryBiz(id ?? (selectedNode?.id as string)),
    {
      enabled: !!id ?? !!selectedNode?.id,
      onSuccess(data) {
        setSelectedNode(data?.data as TreeNode);
      },
    },
  );
  // 获取数据集市
  const { data: DataMartResult } = useQuery(
    ['getDataMartList', selectedNode?.id],
    () => queryDataMartList(selectedNode?.id as string),
    {
      enabled: !!selectedNode?.id,
      onSuccess(data) {
        setDataMartPagination({ ...dataMartPagination, total: data.length });
      },
    },
  );
  // 获取没有关联的数据域
  const { data: allDataDomain } = useQuery(
    ['getDataDomain', selectedNode?.id],
    () => queryDataDomain(selectedNode?.id as string),
    {
      enabled: !!selectedNode?.id,
      onSuccess(data) {
        setDataDomainOptions(getDataDomainOptions(data?.data ?? []));
      },
    },
  );
  // 一键将所有数据域建立关联
  const relatingAllMutation = useMutation((bizId: string) => createAllRelatingBiz(bizId));
  // 单个数据域建立关联
  const relatingMutation = useMutation((data: { domId: string; bizId: string }) => createRelatingDomainToBiz(data));
  // 获取有关联的数据域
  const { data: relatingDataDomain } = useQuery(
    ['getRelatingDataDomain', selectedNode?.id],
    () => queryrelatingDataDomain(selectedNode?.id as string),
    {
      enabled: !!selectedNode?.id,
      onSuccess(data) {
        setDomainPagination({ ...domainPagination, total: data.length });
      },
    },
  );
  useEffect(() => {
    setPageInfo({
      title: '业务分类',
      description: '业务分类指对数据进行业务区分，便于对业务数据进行更方便的处理，如销售业务、运维业务',
    });
    return () => {
      resetPageInfo();
    };
  }, []);
  useEffect(() => {
    queryClient.invalidateQueries(['getBizDetail', selectedNode?.id]);
    queryClient.invalidateQueries(['getDataDomain', selectedNode?.id]);
    queryClient.invalidateQueries(['getBizTreeChild', selectedNode?.id]);
    queryClient.invalidateQueries(['getRelatingDataDomain', selectedNode?.id, id]);
    queryClient.invalidateQueries(['getDataMartList', selectedNode?.id]);
  }, [id]);
  useEffect(() => {
    if (treeResult?.code == '0000') {
      init();
    }
  }, [treeResult]);
  // 初始化树结构
  const init = () => {
    try {
      setTreeData(
        transformTreeData(treeResult.data, (d: DMTreeNode) => ({
          ...d,
          icon: <Icon name='grid_view-line' size={14} />,
        })) as DMTreeNode[],
      );
    } catch (e) {
      console.error(e);
    }
  };
  // 获取数据域下拉列表,
  const getDataDomainOptions = (data: any) => {
    const options = data.map((x: any) => {
      return {
        label: x.name,
        value: x.id,
      };
    });

    return options;
  };
  // 点击树操作
  const handleTreeSelect = (node: TreeNode) => {
    setCurrentNode(node);
    setSelectedNode(node);
    history.push(`/data-modeling/warehouse-plan/business-category/${node.id}`);

    queryClient.invalidateQueries(['getDataDomain', selectedNode?.id]);
    queryClient.invalidateQueries(['getBizTreeChild', selectedNode?.id]);
    queryClient.invalidateQueries(['getRelatingDataDomain', selectedNode?.id]);
    queryClient.invalidateQueries(['getDataMartList', selectedNode?.id]);
  };
  // 创建一级业务分类
  const handleClick = () => {
    toggleModal();
    setModalTitle('新增一级业务分类');
    setActionType('create');
    setCurrentNode(null);
  };
  // 编辑操作
  const onEdit = (data: Biz.CurrentNodeType) => {
    setCurrentNode(data);
    setActionType('edit');
    setModalTitle('编辑业务分类');
    toggleModal();
  };
  // treeNode菜单actiontype
  const getMenuType = (type: string) => {
    switch (type) {
    case 'cteateChildDataMart':
      setModalTitle('创建子业务分类');
      return 'create';
    case 'editDataMart':
      setModalTitle('编辑业务分类');
      return 'edit';
    case 'deleteDataMart':
      return 'delete';
    default:
      return null;
    }
  };
  // tree组件增删改查，modal确认回调函数
  const getTreeRoot = (type: 'add' | 'update' | 'delete', node: DMTreeNode) => {
    let result;
    //   对树进行操作
    if (type === 'add') {
      result = addNode(node, treeData, String(node.parentId));
    } else if (type === 'update') {
      if (node.id === selectedNode?.id) {
        // 更新最新的currentnode
        setCurrentNode(node);
      }
      result = updateNode(node, treeData, String(node.parentId));
    } else {
      result = deleteNode(node, treeData);
    }
    // refresh api
    queryClient.invalidateQueries(['getBizTreeChild', selectedNode?.id]);
    setTreeData(
      transformTreeData(result, (d: DMTreeNode) => ({
        ...d,
        icon: <i className='iconfont icon-folder-line' />,
      })) as DMTreeNode[],
    );
  };
  // 菜单操作
  const handleMenuSelect = async (type: string, record: TreeNode) => {
    const actionType = getMenuType(type);
    setActionType(actionType);
    setCurrentNode(record);
    if (actionType === 'delete') {
      const config = DataBusinessCategoryConfig;
      const res = await deleteConfirm(record?.id, `${config.title}「${record?.name}」吗?`, config);

      if (res.code === '0000') {
        message.success('删除成功');
        getTreeRoot('delete', res?.data);
        if (selectedNode?.id === record.id) {
          setSelectedNode(null);
          history.push('/data-modeling/warehouse-plan/business-category');
        } else {
          queryClient.invalidateQueries(['getBizDetail']);
        }
      }
    } else {
      toggleModal();
    }
  };
  // 渲染标题组件
  const renderTitle = (text: string) => {
    return (
      <div className='flex items-center px-4 pt-3 pb-4 font-bold'>
        <i className='w-[6px] h-5 rounded-sm mr-[10px] block bg-primary-3'></i>
        <span>{text}</span>
      </div>
    );
  };
  // 新增子业务分类
  const handleCrateChild = () => {
    toggleModal();
    setModalTitle('新增子业务分类');
    setActionType('create');
  };
  const renderCreateButton = () => {
    return (
      <div className='px-4 pt-0 pb-3'>
        <Button
          block={false}
          type='primary'
          onClick={handleCrateChild}
          disabled={!hasRights('business_category:write')}
        >
          创建子业务分类
        </Button>
      </div>
    );
  };

  // 一键加载数据域
  const handleLodingDataDomain = async () => {
    const res = await relatingAllMutation.mutateAsync(selectedNode?.id as string);
    if (res.code === '0000') {
      // setDataDomainOptions([])
      queryClient.invalidateQueries(['getRelatingDataDomain', selectedNode?.id]);
      queryClient.invalidateQueries(['getDataDomain', selectedNode?.id]);
    }
  };
  // 下拉选择操作
  const handleDataDomainSelectChange: SelectProps['onChange'] = async value => {
    const res = await relatingMutation.mutateAsync({
      domId: value,
      bizId: selectedNode?.id as string,
    });
    if (res.code === '0000') {
      queryClient.invalidateQueries(['getRelatingDataDomain', selectedNode?.id]);
      queryClient.invalidateQueries(['getDataDomain', selectedNode?.id]);
    }
  };

  // 渲染关联数据域
  const renderSelectRow = () => {
    return (
      <div className='flex items-center px-4 pt-0 pb-4 gap-4 '>
        <div className='text-gray-6'>可选数据域:</div>
        <div>
          <DatadomainSelect
            options={dataDomainOptions}
            onChange={handleDataDomainSelectChange}
            disabled={!hasRights('business_category:write', detailResult?.data?.projectAuth)}
            allowClear={false}
          />
        </div>
        <Button
          type='link'
          size='small'
          className='text-base'
          disabled={!hasRights('business_category:write', detailResult?.data?.projectAuth)}
          onClick={handleLodingDataDomain}
        >
          一键加载所有数据域
        </Button>
      </div>
    );
  };
  // 子业务分类表格编辑操作
  const handleTableEdit = async (record: any) => {
    await Promise.resolve().then(() => {
      setCurrentNode(record);
    });
    setActionType('edit');
    setModalTitle('编辑业务分类');
    toggleModal();
  };
  // 子业务分类表格删除操作
  const hadleTableDelete = async (record: any) => {
    const config = DataBusinessCategoryConfig;
    const { data, code } = await deleteConfirm(record?.id, `${config.title}「${record?.name}」吗?`, config);
    if (code === '0000') {
      message.success('删除成功');
      getTreeRoot('delete', data);
    }
  };
  // 删除数据域表格列表
  const hadleDataDomainDelete = async (record: any) => {
    //  setSlectedDataDomain()
    // 删除关联
    const config = DataRelatingDomainConfig;
    const res = await deleteConfirm(record?.id, `${config.title}「${record?.domName}」的关联吗?`, config);
    // 刷新表格和下拉选项
    if (res.code === '0000') {
      message.success('删除成功');
      queryClient.invalidateQueries(['getRelatingDataDomain', selectedNode?.id]);
      queryClient.invalidateQueries(['getDataDomain', selectedNode?.id]);
    }
  };
  // 业务分类列选项配置
  const bizChildCol: TableProps<any>['columns'] = [
    {
      key: 'name',
      dataIndex: 'name',
      title: '业务名称',
    },
    {
      key: 'code',
      dataIndex: 'code',
      title: '英文缩写',
    },
    {
      key: 'createTime',
      dataIndex: 'createTime',
      title: '创建时间',
    },
    {
      key: 'description',
      dataIndex: 'description',
      title: '备注',
    },
    {
      key: 'operate',
      dataIndex: 'operate',
      title: '操作',
      width: '120px',

      render: (value, record, index) => {
        return (
          <div key={record.id}>
            <Button
              type='link'
              size='small'
              className='mr-4 p-0'
              onClick={() => handleTableEdit(record)}
              disabled={!hasRights('business_category:write', record.projectAuth)}
            >
              编辑
            </Button>
            <Button
              type='link'
              size='small'
              className='p-0'
              disabled={!hasRights('business_category:write', record.projectAuth)}
              onClick={() => hadleTableDelete(record)}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
  // 数据域列选项配置
  const dataDomainCol: TableProps<any>['columns'] = [
    {
      key: 'domName',
      dataIndex: 'domName',
      title: '中文名',
      ellipsis: true,
    },
    {
      key: 'domNameEn',
      dataIndex: 'domNameEn',
      title: '英文名',
      ellipsis: true,
    },
    {
      key: 'domCode',
      dataIndex: 'domCode',
      title: '英文缩写',
      ellipsis: true,
    },

    {
      key: 'domDescription',
      dataIndex: 'domDescription',
      title: '备注',
      ellipsis: true,
    },
    {
      key: 'operate',
      dataIndex: 'operate',
      title: '操作',
      width: '120px',

      render: (value, record, index) => {
        return (
          <div key={record.id}>
            <Button
              type='link'
              className='p-0'
              size='small'
              disabled={!hasRights('business_category:write', detailResult?.data?.projectAuth)}
              onClick={() => hadleDataDomainDelete(record)}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
  const editDataMart = (record: any) => {
    setType('edit');
    setDatamartNode(record);
    tgDatamartModal();
  };
  const deleteDataMart = (record: any) => {
    const config = DataMartConfig;
    deleteConfirm(record?.id, `${config.title}「${record?.name}」吗?`, config);
    queryClient.invalidateQueries(['getDataMartList', selectedNode?.id]);
  };
  const submitDataMart = (_type: 'add' | 'update' | 'delete', data: DataMart) => {
    if (data) {
      queryClient.invalidateQueries(['getDataMartList', selectedNode?.id]);
    }
  };
  // 数据集市列选项配置
  const dataMartCol: TableProps<any>['columns'] = [
    {
      key: 'name',
      dataIndex: 'name',
      title: '集市名称',
    },
    {
      key: 'code',
      dataIndex: 'code',
      title: '英文缩写',
    },
    {
      key: 'createTime',
      dataIndex: 'createTime',
      title: '创建时间',
    },
    {
      key: 'description',
      dataIndex: 'description',
      title: '备注',
    },
    {
      key: 'operate',
      dataIndex: 'operate',
      title: '操作',
      width: '120px',

      render: (value, record) => {
        return (
          <div key={record.id}>
            <Button
              size='small'
              type='link'
              className='mr-4 p-0'
              disabled={!hasRights('data_mart:write', record.projectAuth)}
              onClick={() => editDataMart(record)}
            >
              编辑
            </Button>
            <Button
              size='small'
              type='link'
              className='mr-4 p-0'
              disabled={!hasRights('data_mart:write', record.projectAuth)}
              onClick={() => deleteDataMart(record)}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
  return (
    <div className='h-full overflow-hidden bg-white-4'>
      <div className='flex  overflow-hidden  h-full rounded-[2px] '>
        <Allotment proportionalLayout={false}>
          <Allotment.Pane preferredSize='320px' minSize={120}>
            <div className='w-full h-full bg-neutral-50 flex flex-col'>
              <div className='h-[48px] p-[8px] flex justify-between items-center'>
                <div className='text-gray-6 pl-2'>业务分类树</div>
                <Tooltip placement='bottom' title='创建一级业务分类'>
                  {hasRights('business_category:write') && (
                    <Button
                      type='ghost'
                      disabled={!hasRoleRights('business_category:write')}
                      icon={<Icon name='add_box-line leading-6' size={24} className='text-gray-12' />}
                      onClick={handleClick}
                    ></Button>
                  )}
                </Tooltip>
              </div>
              <BizTree
                treeData={treeData}
                onTreeSelect={handleTreeSelect}
                onMenuSelect={handleMenuSelect}
                selectedNode={selectedNode}
              />
            </div>
          </Allotment.Pane>
          <Allotment.Pane priority={LayoutPriority.High}>
            {!selectedNode?.id ? (
              <div className='flex w-full h-full'>
                <Empty />
              </div>
            ) : (
              <div className='flex-grow h-full bg-white overflow-scroll'>
                <Detail
                  fields={BizDetail}
                  disabled={!hasRights('business_category:write', detailResult?.data?.projectAuth)}
                  className='pt-0 pb-0 mb-0'
                  detail={detailResult?.data ?? null}
                  onEdit={() => onEdit(detailResult?.data)}
                  loading={!detailResult?.data}
                ></Detail>
                <div>
                  {renderCreateButton()}
                  <CustomTable
                    dataSource={treeChildResult?.data ?? []}
                    columns={bizChildCol}
                    pagination={{
                      ...pagination,
                      showSizeChanger: false,
                    }}
                    onChange={handleTableChange}
                    scroll={{ x: '100%', y: 'auto' }}
                  ></CustomTable>
                </div>
                <div>
                  {renderTitle('关联数据域')}
                  {renderSelectRow()}
                  <CustomTable
                    dataSource={relatingDataDomain?.data ?? []}
                    columns={dataDomainCol}
                    pagination={{
                      ...domainPagination,
                      showSizeChanger: false,
                    }}
                    // scroll={false}
                    scroll={{ x: '100%', y: 'auto' }}
                    onChange={handleDomainTableChange}
                  ></CustomTable>
                </div>
                <div>
                  {renderTitle('数据集市管理')}
                  <CustomTable
                    dataSource={DataMartResult?.data ?? []}
                    columns={dataMartCol}
                    pagination={{
                      ...dataMartPagination,
                      showSizeChanger: false,
                    }}
                    // scroll={false}
                    scroll={{ x: '100%', y: 'auto' }}
                    onChange={handleDataMartTableChange}
                  ></CustomTable>
                </div>
              </div>
            )}
          </Allotment.Pane>
        </Allotment>

        <BizModal onSubmit={getTreeRoot} title={modalTitle} />
        <DataMartFormModal onSubmit={submitDataMart} title='编辑数据集市'></DataMartFormModal>
      </div>
    </div>
  );
}
