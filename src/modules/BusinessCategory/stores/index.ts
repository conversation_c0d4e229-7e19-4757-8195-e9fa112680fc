import { create } from 'zustand';

const useStore = create<Biz.BizState>((set, get) => ({
  showModal: false,
  currentNode: null,
  actionType: null,
  actions: {
    toggleModal() {
      const isShow = get().showModal;
      set({
        showModal: !isShow,
      });
    },
    setActionType(type) {
      set({
        actionType: type,
      });
    },
    setCurrentNode(data) {
      set({
        currentNode: data,
      });
    },
  },
}));

export const useShowModal = () => useStore(state => state.showModal);
export const useActionType = () => useStore(state => state.actionType);
export const useCurrentNode = () => useStore(state => state.currentNode);
export const useActions = () => useStore(state => state.actions);
