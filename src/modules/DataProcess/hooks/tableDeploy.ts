import { useQuery } from '@/hooks';
import { TableDeployApi } from '@/services';

export function useTableDeployLastSuccessListByPlatform(platform: string, enabled: boolean) {
  const { data, isLoading, refetch } = useQuery({
    queryKey: [TableDeployApi.getTableDeployLastSuccessListByPlatformUrl, platform],
    queryFunc: () => TableDeployApi.getTableDeployLastSuccessListByPlatform(platform),
    queryOptions: { enabled },
  });

  return { tableDeployList: data, isLoading, refetch };
}

export function useTableDeployByTbId(tbId: string, platform = 'CLICKHOUSE', enabled = !!platform) {
  const { tableDeployList = [], isLoading } = useTableDeployLastSuccessListByPlatform(platform, enabled);
  const tableDeploy = tableDeployList.find(item => item.tbId === tbId);

  return { tableDeploy, isLoading, tableDeployList };
}
