import { useEffect, useState } from 'react';
import { Descriptions } from 'antd';

import { ClusterApi } from '@/modules/Cluster';
import { ClusterOptsApi } from '@/modules/ClusterOpts';

interface Props {
  detail: ProcessModel;
}

const LabelWidth = 136;
const labelStyle = { width: LabelWidth, textAlign: 'right', display: 'inline-block' };

export const RunningTimeDetail = (props: Props) => {
  const { detail } = props;
  const [clusterOptions, setClusterOptions] = useState([]);
  const [optsMasterList, setOptsMasterList] = useState<ClusterOptsEntity[]>([]);
  const items = [
    {
      label: '集群名称',
      children: clusterOptions?.find(item => item?.id === detail?.clusterId)?.clusterName,
    },
    {
      label: '框架名称',
      children: optsMasterList?.find(item => item.id === detail?.optsId)?.optsName,
    },
  ];
  
  const getClusterList = () => {
    ClusterApi.getAll({ supportOpts: 'flink' }).then(({ data }) => {
      setClusterOptions(data);
    });
  };
  
  const fetchOptsMasterList = async () => {
    ClusterOptsApi.getOptsListHasOptions('flink', 'pipeline').then(({ data }) => {
      setOptsMasterList(data);
    });
  };

  useEffect(() => {
    getClusterList();
    fetchOptsMasterList();
  }, []);

  
  return (
    <>
      <Descriptions labelStyle={labelStyle} column={1} items={items} className='pt-[20px]' />
    </>
  );
};
