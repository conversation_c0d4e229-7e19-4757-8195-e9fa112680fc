import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Checkbox, Col, Form, FormProps, Input, Radio, Row, Switch } from 'antd';
import { NamePath } from 'antd/es/form/interface';

import { InputNumberHasSuffix } from './InputNumberHasSuffix';
import { SectionHeader } from './SectionHeader';

interface Props extends FormProps {
  formLayout: Pick<FormProps, 'labelCol' | 'wrapperCol' | 'layout'>;
  values: Record<string, any>;
}

export interface BasicConfigFormRef {
  validateFields: () => (nameList?: NamePath[] | undefined) => Promise<any>;
}

export const _AdvanceConfigForm = (props: Props, ref: ForwardedRef<BasicConfigFormRef>) => {
  const [slideUp, setSlideUp] = useState(true);
  const { formLayout, values, ...otherProps } = props;
  // const initialValues = {
  //   // 滚动文件大小, 默认128MB
  //   sinkRollingPolicyFileSize: 128,
  //   // 按时间滚动间隔, 默认30min
  //   sinkRollingPolicyRolloverInterval: 30,
  //   // 按时间滚动间隔检查时间, 默认1min
  //   sinkRollingPolicyCheckInterval: 1,
  //   // 文件压缩策略, 默认false
  //   autoCompaction: true,
  //   // 压缩目标文件大小，默认为滚动文件大小128MB
  //   compactionFileSize: 128,
  //   // 分区提交触发方式, 系统时间(默认): process-time, 分区时间: partition-time
  //   sinkPartitionCommitTrigger: 'process-time',
  //   partitionTimeExtractorTimestampPattern: '$dt 00:00:00',
  //   // 分区提交延迟
  //   sinkPartitionCommitDelay: 0,
  //   sinkPartitionCommitPolicyKind: ['metastore', 'success-file']
  // }
  const [form] = Form.useForm();

  const sinkPartitionCommitTrigger = Form.useWatch('sinkPartitionCommitTrigger', form);

  const rules: Record<string, FormItemProps['rules']> = {
    sinkRollingPolicyFileSize: [{ required: true, type: 'integer' }],
    sinkRollingPolicyRolloverInterval: [{ required: true, type: 'integer' }],

    sinkRollingPolicyCheckInterval: [{ required: true, type: 'integer' }],
    autoCompaction: [{ required: true, type: 'boolean' }],

    compactionFileSize: [{ required: true }],
    sinkPartitionCommitTrigger: [{ required: true }],

    sinkPartitionCommitDelay: [{ required: true, type: 'integer' }],
    partitionTimeExtractorTimestampPattern: [{ required: true, type: 'string' }],
    sinkPartitionCommitPolicyKind: [{ required: false, type: 'array' }],
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        validateFields: async () => {
          try {
            return await form.validateFields();
          } catch (e) {
            throw new Error('请完成输出配置中的高级配置');
          }
        },
      };
    },
    [form],
  );

  useEffect(() => {
    if (values) {
      form.setFieldsValue(values);
    }
  }, [values]);

  return (
    <div className='mx-3'>
      <div className='text-primary mb-3'>
        <span className='cursor-pointer' onClick={() => setSlideUp(status => !status)}>
          高级配置{' '}
          <i
            className={`iconfont text-sm ${
              slideUp ? 'icon-keyboard_arrow_right-line' : 'icon-keyboard_arrow_down-line'
            } `}
          ></i>
        </span>
      </div>
      <Form {...formLayout} form={form} style={{ display: slideUp ? 'none' : 'block' }} {...otherProps}>
        <SectionHeader className='mb-4'>
          文件滚动策略
          <div className='text-xs text-gray-5'>滚动策略同时受到checkpoint控制</div>
        </SectionHeader>
        <Form.Item
          label='按文件大小滚动'
          name='sinkRollingPolicyFileSize'
          rules={rules.sinkRollingPolicyFileSize}
          help='sink.rolling-policy.file-size，单个文件达到该值后产生新文件'
        >
          <InputNumberHasSuffix suffix='MB' />
        </Form.Item>
        <Form.Item
          label='按时间滚动'
          wrapperCol={{ span: 20 }}
          help='sink.rolling-policy.rollover-interval，单个文件持续打开超过该时间值后产生新文件；每sink.rolling-policy.check-interval检查一次'
        >
          <Row>
            <Col>
              <Form.Item name='sinkRollingPolicyRolloverInterval' rules={rules.sinkRollingPolicyRolloverInterval}>
                <InputNumberHasSuffix suffix='min' />
              </Form.Item>
            </Col>
            <Col className='ml-6'>
              <Form.Item
                label='检查时间'
                name='sinkRollingPolicyCheckInterval'
                rules={rules.sinkRollingPolicyCheckInterval}
              >
                <InputNumberHasSuffix suffix='min' />
              </Form.Item>
            </Col>
          </Row>
        </Form.Item>
        <Form.Item
          label='文件压缩策略'
          wrapperCol={{ span: 20 }}
          help='auto-compaction，在checkpoint时触发时将多个文件压缩为一个文件，旨在将小文件合并为较大的文件；'
        >
          <Row>
            <Col>
              <Form.Item name='autoCompaction' rules={rules.autoCompaction} valuePropName='checked'>
                <Switch defaultChecked />
              </Form.Item>
            </Col>
            <Col className='ml-6'>
              <Form.Item label='压缩文件大小' name='compactionFileSize' rules={rules.compactionFileSize}>
                <InputNumberHasSuffix suffix='MB' />
              </Form.Item>
            </Col>
          </Row>
        </Form.Item>
        <SectionHeader className='my-4'>
          分区提交策略
          <div className='text-xs text-gray-5'>控制分区可见的条件</div>
        </SectionHeader>
        <Form.Item
          label='触发方式'
          wrapperCol={{ span: 20 }}
          name='sinkPartitionCommitTrigger'
          rules={rules.sinkPartitionCommitTrigger}
          help='sink.partition-commit.trigger，系统时间无需水位；分区时间需要生成水位、设置事件时间模式(timeCharacteristic)、设置分区时间提取模式'
        >
          <Radio.Group>
            <Radio value='process-time'>系统时间</Radio>
            <Radio value='partition-time'>分区时间</Radio>
          </Radio.Group>
        </Form.Item>
        {sinkPartitionCommitTrigger === 'partition-time' && (
          <Form.Item
            label='分区时间提取模式'
            name='partitionTimeExtractorTimestampPattern'
            rules={rules.partitionTimeExtractorTimestampPattern}
            wrapperCol={{ span: 20 }}
            help="partition.time-extractor.timestamp-pattern，将分区字段组合成 'yyyy-mm-dd hh:mm:ss' 格式的规则。
  例如：如果分区有dt和hr两个字段，分别表示日期和小时，那么可以可以设置为'$dt $hr:00:00'"
          >
            <Input placeholder='请输入' />
          </Form.Item>
        )}
        <Form.Item
          label='分区提交延迟'
          wrapperCol={{ span: 20 }}
          name='sinkPartitionCommitDelay'
          rules={rules.sinkPartitionCommitDelay}
          help={
            <>
              sink.partition-commit.delay，如果希望尽快查到数据，则保持0即可，否则建议设置为与分区时间颗粒度相同。
              <br />
              例如：分区颗粒度为小时，那么这里可设置为 &apos;1 h&apos;
            </>
          }
        >
          <InputNumberHasSuffix suffix='h' />
        </Form.Item>
        <Form.Item
          label='触发方式'
          wrapperCol={{ span: 20 }}
          name='sinkPartitionCommitPolicyKind'
          rules={rules.sinkPartitionCommitPolicyKind}
          help='sink.partition-commit.policy.kind，当分区提交时如何通知下游'
        >
          <Checkbox.Group>
            <Checkbox value='metastore'>更新元数据</Checkbox>
            <Checkbox value='success-file'>生成标识文件</Checkbox>
          </Checkbox.Group>
        </Form.Item>
      </Form>
    </div>
  );
};

export const AdvanceConfigForm = forwardRef(_AdvanceConfigForm);
