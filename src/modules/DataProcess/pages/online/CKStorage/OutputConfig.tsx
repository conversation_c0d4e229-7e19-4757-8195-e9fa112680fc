import { useEffect, useMemo } from 'react';
import { Form, FormInstance, Select } from 'antd';
import { eq } from 'lodash';

import {
  <PERSON>FieldMap,
  DynamicFieldMap,
  FieldBlackList,
  FieldMap,
  FieldWhiteList,
  MaxFieldNumber,
  ModelSelect,
  NewIndexSelect,
  PlatformModelSelect,
  ShardingKey,
  WriteMode,
} from '@/modules/DataProcess/components';
import { useTableDeployByTbId } from '@/modules/DataProcess/hooks';

export const OutputConfig = ({ form }: { form: FormInstance }) => {
  const enableAutoSchema = Form.useWatch('enableAutoSchema', form) ?? true;
  const writeMode = Form.useWatch('writeMode', form) ?? 'STANDARD';
  const dimTbId = Form.useWatch('dimTbId', form);
  const idxTbId = Form.useWatch('idxTbId', form);
  const enableDynamicSchema = Form.useWatch('enableDynamicSchema', form);
  const isIndexMode = writeMode === 'INDEX';
  const isNewIndexMode = writeMode === 'NEW_INDEX';

  const { tableDeploy: dimRow, tableDeployList } = useTableDeployByTbId(dimTbId);

  const idxTbIdOptions = useMemo(() => {
    return tableDeployList
      .filter(tb => {
        if (!dimRow) {
          return true;
        }
        const isDsSame = eq(tb.dsId, dimRow.dsId);
        const isDatabaseSame = tb.database === dimRow.database;
        return isDsSame && isDatabaseSame && tb.tbId != dimRow.tbId;
      })
      .map(tb => ({
        label: `${tb.tbAlias}（${tb.tbName}）`,
        value: tb.tbId,
      }));
  }, [tableDeployList, dimRow]);

  useEffect(() => {
    if (!idxTbIdOptions.length || !idxTbId) {
      return;
    }

    const option = idxTbIdOptions.find(item => item.value === idxTbId);
    !option && form?.setFieldValue('idxTbId', undefined);
  }, [idxTbId, idxTbIdOptions]);

  return (
    <div className='pt-5'>
      <WriteMode form={form} />
      {!isIndexMode && !isNewIndexMode && (
        <>
          <PlatformModelSelect
            name='ckTbId'
            label='Clickhouse模型'
            title='Clickhouse模型'
            platform='CLICKHOUSE'
            tips='写入目标Clickhouse模型（表）'
            ruleMsg='请选择Clickhouse模型'
            type='sink'
          />
          <AutoFieldMap />
          {!!enableAutoSchema && <DynamicFieldMap form={form} />}
          {!enableAutoSchema && (
            <>
              <div className='hidden'>
                <DynamicFieldMap form={form} />
              </div>
              <FieldMap form={form} />
            </>
          )}
          {!!enableAutoSchema && !!enableDynamicSchema && (
            <>
              <FieldBlackList />
              <FieldWhiteList />
            </>
          )}
          {!!enableAutoSchema && <MaxFieldNumber />}
          <ShardingKey form={form} />
        </>
      )}
      {isIndexMode && (
        <>
          <ModelSelect required name='dimTbId' label='指标维度表' platform='CLICKHOUSE' />
          <Form.Item name='idxTbId' label='指标值表' className='fixed-width pl-9'>
            <Select disabled={!dimTbId} options={idxTbIdOptions} />
          </Form.Item>
        </>
      )}

      {isNewIndexMode && <NewIndexSelect className='fixed-width pl-9' />}
    </div>
  );
};
