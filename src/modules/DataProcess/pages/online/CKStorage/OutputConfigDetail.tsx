/**
 * @module 数据开发-实时数据处理-输入配置
 */
import { useEffect, useMemo, useState } from 'react';
import { Descriptions } from 'antd';
import { eq } from 'lodash-es';

import { CustomTable, SectionCaptain } from '@/components';
import { SourceModelName } from '@/components/business/form/SourceModelSelect/SourceModalName';
import { FIELD_MAP_COLUMNS } from '@/modules/DataProcess/constants';
import { useTableDeployByTbId } from '@/modules/DataProcess/hooks';
import { TableDeployApi } from '@/services';

const LabelWidth = 136;
const labelStyle = { width: LabelWidth, textAlign: 'right', display: 'inline-block' };

interface Props {
  detail: STORAGE_TASK.CK.APIS.Model
}

const whiteModeMap = {
  'STANDARD': '标准模式',
  'INDEX': '指标模式',
  'NEW_INDEX': '指标模式（新）',
};

export const OutputConfigDetail = (props: Props) => {
  const { detail } = props;
  const isIndexMode = detail?.writeMode === 'INDEX';
  const isNewIndexMode = detail?.writeMode === 'NEW_INDEX';
  const enableAutoSchema = detail?.enableAutoSchema;
  const enableDynamicSchema = detail?.enableDynamicSchema;
  const [tableDeploy, setTableDeploy] = useState(''); 
  const { tableDeploy: dimRow, tableDeployList } = useTableDeployByTbId(detail?.dimTbId);
  const [indexTableList, setIndexTableList] = useState<DefaultOptionType[]>([]);

  const idxTbIdOptions = useMemo(() => {
    return tableDeployList
      .filter(tb => {
        if (!dimRow) {
          return true;
        }
        const isDsSame = eq(tb.dsId, dimRow.dsId);
        const isDatabaseSame = tb.database === dimRow.database;
        return isDsSame && isDatabaseSame && tb.tbId != dimRow.tbId;
      })
      .map(tb => ({
        label: `${tb.tbAlias}（${tb.tbName}）`,
        value: tb.tbId,
      }));
  }, [tableDeployList, dimRow]);

  useEffect(() => {
    if (isIndexMode) {
      getTableDeployLastSuccessListByPlatform();
    }
    if (isNewIndexMode) {
      fetchIndexTableList();
    }
  }, [detail]);


  const fetchIndexTableList = async () => {
    const res = await TableDeployApi.getIndexTable();
    if (res?.code) {
      setIndexTableList(
        res?.data?.map(item => {
          return {
            label: `${item.tbAlias}（${item.tbName}）`,
            value: item.tbId,
          };
        }),
      );
    }
  };

  const getTableDeployLastSuccessListByPlatform = async () => {
    const data = await TableDeployApi.getTableDeployLastSuccessListByPlatform('CLICKHOUSE');
    const item = data.find(item => item.tbId === detail?.dimTbId);
    setTableDeploy(item ? `${item?.tbAlias}（${item?.tbName}）` : '');
  };

  const items = [
    {
      label: '写入模式',
      children: whiteModeMap[detail?.writeMode],
    },
    ...(!isIndexMode && !isNewIndexMode) ? [
      {
        label: 'Clickhouse模型',
        children: <SourceModelName value={detail?.ckTbId} />,
      },
      {
        label: '自动字段映射',
        children: detail?.enableAutoSchema ? '开启' : '关闭',
      },
      ...enableAutoSchema ? [
        {
          label: '动态字段适应',
          children: detail?.enableDynamicSchema ? '开启' : '关闭',
        },
      ] : [],
      ...enableAutoSchema && enableDynamicSchema ? [
        {
          label: '字段黑名单',
          children: detail?.blacklist,
        },
        {
          label: '字段白名单',
          children: detail?.whitelist,
        },
      ] : [],
      ...enableAutoSchema ? [
        {
          label: '最大字段数',
          children: detail?.maxDims,
        },
      ] : [],
      {
        label: 'Sharding Key',
        children: detail?.shardingKey,
      },
    ] : [],
    ...isIndexMode ? [
      {
        label: '指标维度表',
        children: tableDeploy,
      },
      {
        label: '指标值表',
        children: idxTbIdOptions?.find(item => item.value === detail?.idxTbId)?.label,
      },
    ] : [],
    ...isNewIndexMode ? [
      {
        label: '指标表',
        children: indexTableList?.find(item => item.value === detail?.ckTbId)?.label,
      },
    ]: [],
  ];

  return (
    <>
      <Descriptions labelStyle={labelStyle} column={1} items={items} className='pt-[20px] pl-[36px]' />
      {
        !isIndexMode && !isNewIndexMode && !enableAutoSchema && 
        <>
          <SectionCaptain title='字段映射' className='ml-5' />
          <CustomTable
            rowKey='tableName'
            dataSource={detail?.schemaMappingList ?? []}
            columns={FIELD_MAP_COLUMNS}
            pagination={false}
          />
        </>
      }
    </>
  );
};
