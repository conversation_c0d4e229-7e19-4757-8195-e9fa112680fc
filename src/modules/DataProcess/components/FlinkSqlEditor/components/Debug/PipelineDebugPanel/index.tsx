import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Tabs } from 'antd';
import PubSub from 'pubsub-js';

import { DebugLog, DebugLogRef } from '@/components/business/ui/DebugLog/DebugLog';
import { DebugObserverTable } from '@/components/business/ui/DebugObserver/DebugObserverTable';
import { DEBUG_OUTER, DEBUG_START, RECEIVE_DEBUG_MESSAGE } from '@/constants/eventTypes';

import './index.less';

// eslint-disable-next-line no-shadow
export enum RunningLogType {
  success,
  error,
}

export interface JobDebugRunningLog {
  type: RunningLogType;
  message: string;
  id?: number;
  display?: string;
  typeNumber?: number;
}

export interface RunningLogsTable {
  tableIndex: number;
  columns: any[];
  rows?: any[];
}

export interface ObserveDebugList extends WebSocketData {
  display: string;
  time: number;
}

interface Props {
  channelId: string;
  style: React.CSSProperties;
}

export const PipelineDebugPanel = (props: Props) => {
  const [trackUrl, setTrackUrl] = useState<string | undefined>();
  const [activeKey, setActiveKey] = useState<string>();
  const logRef = useRef<DebugLogRef>();
  const [runningLogsTable, setRunningLogsTable] = useState<RunningLogsTable[]>([]);

  // 从运行日志的表数据信息中，生成tab页签
  const runningLogsTableTabs = useMemo(() => {
    const tabs = runningLogsTable.map((table: RunningLogsTable) => {
      const { tableIndex } = table;
      const key = `table_${tableIndex}`;
      return {
        key,
        label: key,
        children: <DebugObserverTable listData={table} />,
      };
    });
    return tabs;
  }, [runningLogsTable]);

  // 接收到表数据后，转化数据
  const receiveRunningLogsTable = ({ tableIndex, data }: { tableIndex: number; data: string[] }) => {
    setRunningLogsTable(runningLogsTable => {
      const findTable = runningLogsTable.find(table => table.tableIndex === tableIndex);
      if (!findTable) {
        runningLogsTable.push({
          tableIndex,
          columns: data,
        });
      } else {
        if (!findTable.rows) {
          findTable.rows = [];
        }
        const { columns } = findTable;
        const item: Record<string, unknown> = {};
        columns.forEach((column, colIndex) => {
          item[column] = data[colIndex];
        });
        try {
          findTable.rows.push(item);
        } catch (error) {
          console.log(error);
        }
        
      }
      return [...runningLogsTable];
    });
  };

  const HandleMessage = (wsData: WebSocketData) => {
    const { code, message } = wsData;
    switch (code) {
    case '1004':
      // 表数据
      if (message.indexOf('__DataRow[') === 0) {
        const tableIndex = parseInt(message.replace(/^__DataRow\[(\d+)\]/, '$1'), 10);
        const str = message.replace(/^__DataRow\[(\d+)\]:/, '');
        receiveRunningLogsTable({
          tableIndex,
          data: str?.split('###') || [],
        });
      } else {
        // 普通日志
        logRef.current?.writeln?.(message);
      }
      break;
    case '9999':
      // 出错日志
      logRef.current?.writeError?.(message);
      break;
    case '1005':
      setTrackUrl(message);
      break;
    }
  };

  useEffect(() => {
    const pubListener = (msg, data: { channelId: string; wsData: WebSocketData }) => {
      if (data.channelId === props.channelId) {
        HandleMessage(data.wsData);
      }
    };

    const clearAll = (msg, data: { channelId: string }) => {
      if (data.channelId === props.channelId) {
        setRunningLogsTable([]);
        logRef.current?.clear();
      }
    };

    PubSub.subscribe(RECEIVE_DEBUG_MESSAGE, pubListener);

    PubSub.subscribe(DEBUG_START, clearAll);

    PubSub.subscribe(DEBUG_OUTER, clearAll);

    return () => {
      PubSub.unsubscribe(pubListener);
      PubSub.unsubscribe(clearAll);
    };
  }, []);

  return (
    <div className='pipeline-debug-panel' style={props.style}>
      <Tabs
        defaultActiveKey='log-panel'
        activeKey={activeKey}
        items={[
          {
            key: 'log-panel',
            label: '运行日志',
            children: <DebugLog ref={logRef} />,
          },
          ...runningLogsTableTabs,
        ]}
        onChange={setActiveKey}
        tabBarStyle={{ marginLeft: '20px' }}
        tabBarExtraContent={
          <>
            {trackUrl && (
              <a href={trackUrl} rel='noreferrer' target='_blank' className='mr-2'>
                追踪
              </a>
            )}
          </>
        }
      ></Tabs>
    </div>
  );
};
