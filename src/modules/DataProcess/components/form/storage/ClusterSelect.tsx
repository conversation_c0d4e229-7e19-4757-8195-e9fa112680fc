import { useEffect, useMemo } from 'react';
import { Form, FormInstance } from 'antd';
import { eq } from 'lodash';

import { Select } from '@/components';
import { useAllStorageCluster, useTableDeployByTbId } from '@/modules/DataProcess/hooks';
import { isClusterRunning } from '@/modules/DataProcess/utils';
import { requiredByMsg } from '@/utils';

export const StorageClusterSelect = ({ form }: { form: FormInstance }) => {
  const ckTbId = Form.useWatch('ckTbId', form);
  const dimTbId = Form.useWatch('dimTbId', form);
  const kafkaTbId = Form.useWatch('kafkaTbId', form);
  const writeMode = Form.useWatch('writeMode', form);
  const storageClusterId = Form.useWatch('storageClusterId', form);
  const isIndexMode = writeMode === 'INDEX';

  const { tableDeploy: kafkaRow, isLoading: kafkaLoading } = useTableDeployByTbId(kafkaTbId, 'KAFKA');

  const { tableDeploy: ckRow, isLoading: ckLoading } = useTableDeployByTbId(
    isIndexMode ? dimTbId : ckTbId,
    'CLICKHOUSE',
  );
  const { clusters = [], isLoading } = useAllStorageCluster();
  const dsIdProp = obj => obj?.dsId;
  const options = useMemo(
    () =>
      clusters.map(({ name, id, status, ckDsId, kafkaDsId }) => {
        const isKafkaDsSame = eq(kafkaDsId, dsIdProp(kafkaRow));
        const isCkDsSame = eq(ckDsId, dsIdProp(ckRow));
        const isRunning = isClusterRunning(status);
        const selectable = isKafkaDsSame && isCkDsSame;
        const enabled = isRunning && selectable;

        return {
          label: name,
          value: id,
          disabled: !enabled,
        };
      }),
    [clusters, kafkaRow, ckRow],
  );

  useEffect(() => {
    if (!options.length || !storageClusterId) {
      return;
    }
    const option = options.find(opt => opt.value === storageClusterId);
    !option && form?.setFieldValue('storageClusterId', undefined);
  }, [options, storageClusterId]);

  const getHelp = () => {
    if (!kafkaTbId) {
      return '请先在输入配置中设置【Kafka模型】';
    }
    if (!ckTbId && isIndexMode) {
      return '请先在输出配置中设置【指标表】';
    }
    if (!ckTbId && !isIndexMode) {
      return '请先在输出配置中设置【Clickhouse模型】';
    }
    return '确保已基于源Kafka和目的CK创建了“存储集群(CK)”，如果没有合适存储集群，请至平台管理-集群管理配置和启动一个Sinker集群';
  };

  const enabled = kafkaTbId && (ckTbId || isIndexMode);

  return (
    <Form.Item
      required
      name='storageClusterId'
      label='存储集群'
      className='pl-9 fixed-width'
      rules={[{ validator: requiredByMsg('请选择存储集群') }]}
      help={getHelp()}
    >
      <Select
        showSearch
        disabled={!enabled}
        loading={kafkaLoading || ckLoading || isLoading}
        options={options}
        placeholder='请选择'
      ></Select>
    </Form.Item>
  );
};
