// import { useForm } from '@/modules/DataIngestion/hooks';
import { Form, FormInstance, Radio } from 'antd';

import { requiredByMsg } from '@/utils';

export const WriteMode = ({ form }: { form: FormInstance }) => {
  const writeMode = Form.useWatch('writeMode', form);

  return (
    <Form.Item
      required
      name='writeMode'
      label='写入模式'
      className='pl-9'
      initialValue='STANDARD'
      rules={[{ validator: requiredByMsg('请选择') }]}
      help={
        <>
          {writeMode === 'STANDARD' && <>直接将结构化的json写入对应的表</>}
          {(writeMode === 'INDEX' || writeMode === 'NEW_INDEX') && (
            <>指标模式要求指标维度表和指标值表具有约定的表结构</>
          )}
        </>
      }
    >
      <Radio.Group
        onChange={() => {
          form.setFieldValue('ckTbId', null);
        }}
      >
        <Radio value='STANDARD'>标准模式</Radio>
        <Radio value='INDEX'>指标模式</Radio>
        <Radio value='NEW_INDEX'>指标模式（新）</Radio>
      </Radio.Group>
    </Form.Item>
  );
};
