import { useEffect, useState } from 'react';
import { Form, Select } from 'antd';

import { TableDeployApi } from '@/services';

interface Props {
  labelKey?: string;
  valueKey?: string;
  className?: string;
}

export const NewIndexSelect = (props: Props) => {
  const { valueKey = 'tbId', ...otherProps } = props;
  const [options, setOptions] = useState<DefaultOptionType[]>([]);

  const fetchIndexTableList = async () => {
    const res = await TableDeployApi.getIndexTable();
    if (res?.code) {
      setOptions(
        res?.data?.map(item => {
          return {
            label: `${item.tbAlias}（${item.tbName}）`,
            value: item[valueKey],
          };
        }),
      );
    }
  };

  useEffect(() => {
    fetchIndexTableList();
  }, []);

  return (
    <Form.Item label='指标表' name='ckTbId' rules={[{ required: true }]} {...otherProps}>
      <Select options={options} showSearch allowClear optionFilterProp='label' />
    </Form.Item>
  );
};
