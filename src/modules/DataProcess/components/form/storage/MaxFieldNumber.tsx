/**
 * @module 数据开发-实时开发-数据处理-存储任务-最大字段数
 */
import { useState } from 'react';
import { Form, FormItemProps } from 'antd';

import { requiredByMsg } from '@/utils';

import { InputNumber } from './InputNumber';

type State = Pick<FormItemProps, 'validateStatus' | 'help'>;

export const MaxFieldNumber = () => {
  const [{ help, validateStatus }, setState] = useState<State>({
    help: '0为不限制字段数量',
    validateStatus: 'success',
  });

  return (
    <Form.Item
      required
      initialValue={0}
      rules={[
        { validator: requiredByMsg('请输入') },
        {
          validator: (_, value) => {
            if (value < 0) {
              setState({
                help: '请输入大于零的值',
                validateStatus: 'error',
              });
              return Promise.reject(new Error('请输入大于零的值'));
            }
            setState({
              help: '0为不限制字段数量',
              validateStatus: 'success',
            });
            return Promise.resolve();
          },
        },
      ]}
      name='maxDims'
      label='最大字段数'
      className='pl-9 fixed-width'
      validateStatus={validateStatus}
      help={help}
    >
      <InputNumber desc='设置允许动态字段的数量，防止维度爆炸' placeholder='0' />
    </Form.Item>
  );
};
