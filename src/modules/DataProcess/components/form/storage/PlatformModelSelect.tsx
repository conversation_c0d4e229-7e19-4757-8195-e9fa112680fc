import { Form } from 'antd';
import cs from 'classnames';

import { SourceModelSelect } from '@/components/business/form/SourceModelSelect';
import { CatalogType } from '@/constants';
import { requiredByMsg } from '@/utils';

interface Props {
  tips?: string;
  onChange?: (id: string, tableDeploy: any) => void;
  className?: string;
  tipsClassName?: string;
  cellId?: string;
  catalog?: CatalogType;
  name: string;
  label: string;
  platform: DATA_SOURCE_TYPE;
  title: string;
  fullScreen?: boolean;
  ruleMsg?: string;
  type?: JobModel['jobRole'];
}

export const PlatformModelSelect = (props: Props) => {
  const {
    tips,
    name,
    label,
    platform,
    title,
    onChange,
    className,
    cellId,
    catalog,
    fullScreen = false,
    ruleMsg,
    type,
  } = props;

  const handleChange = (id: string, tableDeploy: any) => {
    onChange?.(id, tableDeploy);
  };

  return (
    <div>
      <Form.Item
        wrapperCol={{ xxl: fullScreen ? 24 : 12, xl: fullScreen ? 24 : 12, lg: 24, xs: 24, sm: 24 }}
        required
        label={label ?? 'kafka模型'}
        name={name ?? 'kafkaTbId'}
        className={cs('pl-9 flex-1 mb-0', className)}
        rules={[{ validator: requiredByMsg(ruleMsg ?? '请选择') }]}
        help={tips}
      >
        <SourceModelSelect
          platform={platform ?? 'KAFKA'}
          title={title ?? 'kafka模型'}
          onChange={handleChange}
          cellId={cellId}
          catalog={catalog}
          type={type}
        />
      </Form.Item>
    </div>
  );
};
