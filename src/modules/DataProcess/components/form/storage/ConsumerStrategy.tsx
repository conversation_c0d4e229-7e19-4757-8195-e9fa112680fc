/**
 * @module 数据开发-实时开发-数据处理-存储作业-消费策略
 */
import { Form, Radio } from 'antd';

import { requiredByMsg } from '@/utils';

interface Props {
  radioValue: string[];
}

export const ConsumerStrategy = ({ radioValue }: Props) => {
  return (
    <Form.Item
      required
      name='strategy'
      label='消费策略'
      className='pl-9'
      initialValue={radioValue[0]}
      rules={[{ validator: requiredByMsg('请选择') }]}
      help='设置首次启动时，消费存量数据，还是从最新数据开始消费'
    >
      <Radio.Group>
        <Radio value={radioValue[0]}>全量</Radio>
        <Radio value={radioValue[1]}>首次最新</Radio>
      </Radio.Group>
    </Form.Item>
  );
};
