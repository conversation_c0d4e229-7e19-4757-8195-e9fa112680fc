import { AlarmLevelEnum } from '@/models';
import { SelectedPipelineTaskModel } from '@/modules/DataQuality/realtime/CheckTask/models';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export type TabActiveKey = 'StatisticalReport' | 'AlarmList';

export interface AlertInformation {
  tableAlert: TableAlert;
  pipelineAlert: TableAlert;
  tableAlertTop: TableAlert;
  pipelineAlertTop: TableAlert;
  ruleAlertTop: TableAlert;
}

export interface InfoItems {
  classifyName: string;
  total: number;
  recordId: string;
}

export interface TableAlert {
  category: string;
  items: InfoItems[];
  totalNum?: number;
}

export interface ChartData {
  category: string;
  classifyName: string;
  total: number;
  title?: string;
  alarmStartTime?: string;
  alarmEndTime?: string;
  warnLevel?: keyof typeof AlarmLevelEnum;
}

export interface AlarmTableData {
  id: string;
  content: string;
  level: keyof typeof AlarmLevelEnum;
  firedTime: string;
  tableNum: number;
  pipelineName: string;
  ruleTemplateName: string;
  alertKey: string;
  createTime: string;
  createUserName: string | null;
  labels: Labels;
  pipelineId: string;
  resolvedTime: string | null;
  ruleId: string;
  ruleName: string;
  ruleTemplateId: string;
  status: 'OPEN' | 'CLOSED';
  taskId: string;
  updateTime: string;
  updateUserName: string | null;
}

export interface Labels {
  jax_pipeline_id: string;
  jax_rule_id: string;
  jaxg_host: string;
}

export interface RuleTemplateData {
  baseValueN: string | null;
  baseValueType: string;
  calculationRule: string;
  category: string;
  createTime: string;
  createUserName: string | null;
  description: string;
  expectedValueList: string | null;
  extraSetting: string | null;
  id: string;
  isBuiltIn: boolean;
  name: string;
  referenceCount: string | null;
  sampleMethod: string;
  samplePeriod: number;
  updateTime: string;
  updateUserName: string | null;
}

export interface CheckTaskData {
  checkObj: string | null;
  checkRuleList: string | null;
  clusterId: string | null;
  createTime: string;
  createUserName: string | null;
  customSetting: string | null;
  description: string;
  id: string;
  jobDisplay: string;
  jobName: string;
  jobSlot: number;
  modelAssociationStrategy: string | null;
  name: string;
  optsId: string | null;
  ruleCount: string | null;
  runMode: string;
  selectMode: string;
  selectedPipelineTask: SelectedPipelineTaskModel;
  taskName: string | null;
  tbId: string | null;
  updateTime: string;
  updateUserName: string | null;
  yarnSessionId: string | null;
}

export interface AssociationModel {
  tabelAliasName: string;
  tabelName: string;
  tableId: string;
}
