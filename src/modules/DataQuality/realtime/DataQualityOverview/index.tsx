import { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import { useSearchParams } from 'umi';

import { useGlobalHook } from '@/hooks';
import { TabActiveKey } from '@/modules/DataQuality/realtime/DataQualityOverview/constants';

import './index.less';

import AlarmList from './components/AlarmList';
import StatisticalReport from './components/StatisticalReport';
import { useDataQualityOverviewStore } from './stores/useDataQualityOverviewStore';

export const DataQualityOverview = () => {
  const { range, setRange, resetPage } = useDataQualityOverviewStore();
  const [searchParams, setSearchParams] = useSearchParams();
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const taskId = searchParams.get('taskId');

  const tabName = searchParams.get('tabName') as TabActiveKey;
  const [activeKey, setActiveKey] = useState<TabActiveKey>(tabName ?? 'StatisticalReport');
  const items = [
    {
      key: 'StatisticalReport',
      label: '实时统计报告',
      children: <StatisticalReport range={range} setRange={setRange} />,
    },
    {
      key: 'AlarmList',
      label: '告警列表',
      children: <AlarmList range={range} setRange={setRange} />,
    },
  ];

  useEffect(() => {
    setPageInfo({ title: '质量概览', description: !taskId });
    return () => {
      resetPageInfo();
    };
  }, [taskId]);

  useEffect(() => {
    return () => {
      resetPage();
    };
  }, []);

  const handleChange = (key: TabActiveKey) => {
    const searchParams: { tabName: string; taskId?: string } = {
      tabName: key,
    };
    if (taskId) {
      searchParams.taskId = taskId;
    }
    setSearchParams(searchParams);
  };

  useEffect(() => {
    if (tabName) {
      setActiveKey(tabName);
    } else {
      setActiveKey('StatisticalReport');
    }
  }, [tabName]);

  return (
    <div className='data-quality-overview'>
      <Tabs items={items} activeKey={activeKey} onChange={handleChange} style={{ height: '100%' }} />
    </div>
  );
};
