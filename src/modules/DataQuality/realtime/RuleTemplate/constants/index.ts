// 运算符
export enum Operator {
  gt = '大于',
  ge = '大于等于',
  lt = '小于',
  le = '小于等于',
}

// 关系符
export enum LogicalOperator {
  and = '且',
  or = '或',
}

// 模板类别
export enum SampleCategoryEnum {
  ACCURACY = '准确性',
  COMPLETENESS = '完整性',
  VALIDITY = '有效性',
  UNIQUENESS = '唯一性',
  CONSISTENCY = '一致性',
  TIMELINESS = '及时性',
  OTHERS = '其他',
}

// 采样方式枚举值
export enum SampleMethodEnum {
  MESSAGE_NUMBER_INCREMENT = '消息条数增量',
  MESSAGE_RATE = '消息速率（条/秒）',
  AVG_QUEUE_TIME_DELAY = '队列时间延迟（ms）',
  AVG_BIZ_TIME_DELAY = '业务时间延迟（ms）',
  FIELD_SPECIFICATION_EXCEPTION_INCREMENT = '字段规范异常数增量',
  BIZ_TIME_OUT_OF_ORDER_NUMBER_INCREMENT = '消息乱序数增量',
  DATA_FREQUENCY_DEVIATION = '周期性偏差',
}

// 采样方式描述信息枚举值
export enum SampleMethodDescEnum {
  MESSAGE_NUMBER_INCREMENT = '实时对消息条数进行计数，采样消息在每个采样周期内的条数增量',
  MESSAGE_RATE = '实时对消息条数进行计数，采样消息在每个采样周期内的速率',
  AVG_QUEUE_TIME_DELAY = '采样每条消息的“处理时间”与消息“写入Kafka的时间”的差值（延迟），计算采样周期内的延迟的平均值（此采样方式要求消息必需包含Kafka元数据信息）',
  AVG_BIZ_TIME_DELAY = '采样每条消息的“处理时间”与消息的“业务时间”的差值（延迟），计算采样周期内的延迟的平均值（此采样方式要求配置检查任务时设置业务时间字段）',
  FIELD_SPECIFICATION_EXCEPTION_INCREMENT = '实时对不符合模型中字段规范定义的消息进行计数，采样每个采样周期内的此种情况的增量',
  BIZ_TIME_OUT_OF_ORDER_NUMBER_INCREMENT = '通过对比前后两条数据业务时间，在采样周期内统计出现乱序的次数',
  DATA_FREQUENCY_DEVIATION = '针对强周期性数据，采样数据的周期与告警阈值的偏差',
}

// 检查规则枚举值
export enum CalcRuleTypeEnum {
  SAMPLE_VALUE = '采样值',
  SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY = '与基准值计算波动率',
  SAMPLE_VALUE_AND_BASE_VALUE_RATIO = '与基准值计算比率',
  SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE = '与基准值计算差值',
}

// 检查规则描述信息枚举值
export enum CalcRuleTypeDescEnum {
  SAMPLE_VALUE = '选择采样结果的计算方式（采样结果将与告警阈值对比产生告警）',
  SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY = '选择采样结果的计算方式（采样结果将与告警阈值对比产生告警）；计算结果值=(采样结果-基准值)*100/基准值',
  SAMPLE_VALUE_AND_BASE_VALUE_RATIO = '选择采样结果的计算方式（采样结果将与告警阈值对比产生告警）；计算结果值=采样结果*100/基准值',
  SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE = '选择采样结果的计算方式（采样结果将与告警阈值对比产生告警）；计算结果值=采样结果-基准值',
}

// 告警阈值枚举值
export enum RealtimeBaseValueTypeEnum {
  N_PERIOD_VALUE = 'N个周期前值(同比)',
  AVG_N_PERIOD_VALUE = '前N周期平均值(移动平均)',
  PERIOD_MESSAGE_NUMBER_EXCLUDE_FILTER = '周期内消息条数(不包括过滤条件)',
  PERIOD_MESSAGE_NUMBER_INCLUDE_FILTER = '周期内消息条数(包括过滤条件)',
}

// 数据时间枚举值
export enum DataTimeEnum {
  'process-time' = '处理时间',
  'event-time' = '业务时间',
}

// 数据时间描述信息枚举值
export enum DataTimeDescEnum {
  'process-time' = '以数据到达时间为准，适用于实时/准实时场景，数据无合适的业务时间字段',
  'event-time' = '以数据的业务时间为准，适用于批量场景，且数据中有时间字段表示业务时间，配置检测任务时需设置业务时间字段',
}

// 基准时间枚举值
export enum RealtimeBaseTimeModeEnum {
  'data-field' = '基于数据中字段',
  'fixed-threshold' = '固定阈值',
}

// 基准时间描述信息枚举值
export enum RealtimeBaseTimeModeDescEnum {
  'data-field' = '以数据中的某字段值作为数据周期的基准值，字段必需存在且单位为秒，适用于数据流中存在多种不同周期的数据，配置检测时需设置对应字段',
  'fixed-threshold' = '某个固定阈值为数据周期的基准，适用于数据流中的数据周期都相同的情况，配置检测时需设置固定阈值（单位秒）',
}

// 统计规则枚举值
export enum RealtimeStaticMethodEnum {
  distinction = '区分规则',
  'no-distinction' = '不区分规则',
}

// 检查规则名单枚举值
export enum RealtimeValidateRuleEnum {
  ERR_MISSING = '主键/非空字段缺失检查',
  ERR_TYPE = '字段类型不匹配检查',
  ERR_ENUM = '字段数据字典检查',
  ERR_NOTNULL = '非空字段检查',
  ERR_LENGTH = '字段长度检查',
  ERR_PRECISION = '字段精度检查',
}

// 模板类别选项，用于初始化下拉菜单
export const SampleCategoryOptions = Object.entries(SampleCategoryEnum).map(([value, label]) => ({ value, label }));
// 模板采样方式选项，用于初始化下拉菜单
export const SampleMethodOptions = Object.entries(SampleMethodEnum).map(([value, label]) => ({ value, label }));
// 模板计算规则选项，用于初始化下拉菜单
export const CheckRuleTypeOptions = Object.entries(CalcRuleTypeEnum).map(([value, label]) => ({ value, label }));
// 告警阈值选项
export const RealtimeBaseValueTypeOptions = Object.entries(RealtimeBaseValueTypeEnum).map(([value, label]) => ({
  value,
  label,
}));
// 数据时间选项
export const RealtimeDataTimeModeOptions = Object.entries(DataTimeEnum).map(([value, label]) => ({
  value,
  label,
}));
// 基准时间选项
export const RealtimeBaseTimeModeOptions = Object.entries(RealtimeBaseTimeModeEnum).map(([value, label]) => ({
  value,
  label,
}));
// 统计规则选项
export const RealtimeStatisticalMethodOptions = Object.entries(RealtimeStaticMethodEnum).map(([value, label]) => ({
  value,
  label,
}));
// 检查规则选项
export const RealtimeValidateRuleOptions = Object.entries(RealtimeValidateRuleEnum).map(([value, label]) => ({
  value,
  label,
}));
