export const config = {
  // 采样方式
  // 消息条数增量
  MESSAGE_NUMBER_INCREMENT: {
    sampleRule: {
      samplePeriod: true, // 采样周期
    },
    calculationRule: {
      // 检测规则 - 采样值
      SAMPLE_VALUE: {
        // 无基准值计算方式
        baseValue: false,
        expectedValue: 'range',
      },
      // 检测规则 - 与基准值计算波动率
      SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算比率
      SAMPLE_VALUE_AND_BASE_VALUE_RATIO: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 周期内消息条数(不包括过滤条件)
          PERIOD_MESSAGE_NUMBER_EXCLUDE_FILTER: {
            templateExtra: {
              baseValueN: false,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算差值
      SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE: {
        expectedValue: 'range',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
    },
  },
  // 消息速率（条/秒）
  MESSAGE_RATE: {
    sampleRule: {
      samplePeriod: true, // 采样周期
    },
    calculationRule: {
      // 检测规则 - 采样值
      SAMPLE_VALUE: {
        // 无基准值计算方式
        baseValue: false,
        expectedValue: 'range',
      },
      // 检测规则 - 与基准值计算波动率
      SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算比率
      SAMPLE_VALUE_AND_BASE_VALUE_RATIO: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算差值
      SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE: {
        expectedValue: 'range',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
    },
  },
  // 队列时间延迟（ms）
  AVG_QUEUE_TIME_DELAY: {
    sampleRule: {
      samplePeriod: true, // 采样周期
    },
    calculationRule: {
      // 检测规则 - 采样值
      SAMPLE_VALUE: {
        // 无基准值计算方式
        baseValue: false,
        expectedValue: 'range',
      },
      // 检测规则 - 与基准值计算波动率
      SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算比率
      SAMPLE_VALUE_AND_BASE_VALUE_RATIO: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算差值
      SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE: {
        expectedValue: 'range',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
    },
  },
  // 业务时间延迟（ms）
  AVG_BIZ_TIME_DELAY: {
    sampleRule: {
      samplePeriod: true, // 采样周期
    },
    ruleExtra: {
      bizDateTimeField: true,
      bizDateTimeFormat: true,
    },
    calculationRule: {
      // 检测规则 - 采样值
      SAMPLE_VALUE: {
        // 无基准值计算方式
        baseValue: false,
        expectedValue: 'range',
      },
      // 检测规则 - 与基准值计算波动率
      SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算比率
      SAMPLE_VALUE_AND_BASE_VALUE_RATIO: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算差值
      SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE: {
        expectedValue: 'range',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
    },
  },
  // 字段规范异常数增量
  FIELD_SPECIFICATION_EXCEPTION_INCREMENT: {
    sampleRule: {
      samplePeriod: true, // 采样周期
      staticsMethod: true,
      validateRules: true,
    },
    templateExtra: {
      staticsMethod: true,
      validateRules: true,
    },
    ruleExtra: {
      selectModel: true,
      whiteBlack: true,
    },
    calculationRule: {
      // 检测规则 - 采样值
      SAMPLE_VALUE: {
        // 无基准值计算方式
        baseValue: false,
        expectedValue: 'range', // 值范围
        templateExtra: {},
        ruleExtra: {},
      },
      // 检测规则 - 与基准值计算波动率
      SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算比率
      SAMPLE_VALUE_AND_BASE_VALUE_RATIO: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 周期内消息条数(包括过滤条件)
          PERIOD_MESSAGE_NUMBER_INCLUDE_FILTER: {
            templateExtra: {
              baseValueN: false,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算差值
      SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE: {
        expectedValue: 'range',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
    },
  },
  // 消息乱序数增量
  BIZ_TIME_OUT_OF_ORDER_NUMBER_INCREMENT: {
    sampleRule: {
      samplePeriod: true, // 采样周期
    },
    ruleExtra: {
      bizDateTimeField: true,
      bizDateTimeFormat: true,
    },
    calculationRule: {
      // 检测规则 - 采样值
      SAMPLE_VALUE: {
        // 无基准值计算方式
        baseValue: false,
        expectedValue: 'range',
      },
      // 检测规则 - 与基准值计算波动率
      SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算比率
      SAMPLE_VALUE_AND_BASE_VALUE_RATIO: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 周期内消息条数(包括过滤条件)
          PERIOD_MESSAGE_NUMBER_INCLUDE_FILTER: {
            templateExtra: {
              baseValueN: false,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算差值
      SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE: {
        expectedValue: 'range',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
    },
  },
  // 周期性偏差
  DATA_FREQUENCY_DEVIATION: {
    sampleRule: {
      sampleNumber: true, // 采样点数
      samplePeriod: false, // 采样周期
      dataTimeMode: true, // 数据时间
      baseDateTime: true, // 基准时间
    },
    calculationRule: {
      // 检测规则 - 采样值
      SAMPLE_VALUE: {
        // 无基准值计算方式
        baseValue: false,
        expectedValue: 'range',
        ruleExtra: {},
      },
      // 检测规则 - 与基准值计算波动率
      SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算比率
      SAMPLE_VALUE_AND_BASE_VALUE_RATIO: {
        expectedValue: 'percentRange',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
            ruleExtra: {
              bizDateTimeField: true,
              bizDateTimeFormat: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
      // 检测规则 - 与基准值计算差值
      SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE: {
        expectedValue: 'range',
        baseValue: {
          // 基准值 - N个周期前值(同比)
          N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
          // 基准值 - 前N周期平均值(移动平均)
          AVG_N_PERIOD_VALUE: {
            templateExtra: {
              baseValueN: true,
            },
          },
        },
      },
    },
  },
};
