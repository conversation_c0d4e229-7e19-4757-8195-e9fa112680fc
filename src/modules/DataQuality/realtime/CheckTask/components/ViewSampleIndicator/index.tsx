import React, { useState } from 'react';
import { Button, ButtonProps } from 'antd';

import { SampleIndicatorModal } from './SampleIndicatorModal';

interface Props extends ButtonProps {
  taskId: string;
  ruleId: string;
  pipelineId?: string;
  alertId?: string;
  firedTime?: string;
  jaxErrorCode?: string;
}
export const ViewSampleIndicator: React.FC<Props> = props => {
  const { taskId, ruleId, pipelineId, alertId, firedTime, jaxErrorCode, children, ...otherProps } = props;
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Button type='link' {...otherProps} onClick={() => setVisible(true)}>
        {children ?? '查看采样指标'}
      </Button>
      {visible && (
        <SampleIndicatorModal
          taskId={taskId}
          ruleId={ruleId}
          pipelineId={pipelineId}
          alertId={alertId}
          firedTime={firedTime}
          jaxErrorCode={jaxErrorCode}
          open={visible}
          onClose={() => setVisible(false)}
        />
      )}
    </>
  );
};
