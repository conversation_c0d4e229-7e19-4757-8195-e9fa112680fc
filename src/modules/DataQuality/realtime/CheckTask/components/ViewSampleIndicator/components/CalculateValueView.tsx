import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { Select, Space } from 'antd';
import { isEqual } from 'lodash';
import moment from 'moment';

import { RefreshRangeSelector, RefreshRate } from '@/components/visual/tool/RefreshRateSelector';
import { Range, TimeRangeSelector } from '@/components/visual/tool/TimeRangerSelector';
import { getRealRangeTime } from '@/components/visual/tool/TimeRangerSelector/helper';
import { AlarmLevelMap } from '@/models';
import { RealtimeValidateRuleOptions } from '@/modules/DataQuality/realtime/RuleTemplate/constants';
import { RealtimeRuleEntity } from '@/modules/DataQuality/realtime/RuleTemplate/models';
import { RealtimeCheckRuleMetricApi } from '@/services';

import { useRuleChartStore } from '../useRuleChartStore';

import { ExceptionChart, ExceptionSourceData } from './ExceptionChart';

interface Props {
  taskId: string;
  ruleId: string;
  pipelineList: SelectOptions;
  pipelineId?: string;
  ruleDetail?: RealtimeRuleEntity;
  isCurrentTab: boolean;
  alertId?: string;
  firedTime?: string;
  jaxErrorCode?: string;
}
export const CalculateValueView: React.FC<Props> = props => {
  const {
    taskId,
    ruleId,
    pipelineId: propsPipelineId,
    alertId,
    firedTime,
    jaxErrorCode,
    pipelineList,
    ruleDetail,
    isCurrentTab,
  } = props;

  const { setState, rate, range } = useRuleChartStore();
  const [data, setData] = useState<Record<'calculateValue', ExceptionSourceData | undefined>>({
    calculateValue: undefined,
  });
  const [pipelineId, setPipelineId] = useState<string | undefined>(propsPipelineId);
  const [fieldErrTypeList, setFieldErrTypeList] = useState<string | undefined>(jaxErrorCode);
  const [rangeTime, setRangeTime] = useState<string[]>(getRealRangeTime(range)!);
  const intervalIdRef = useRef<NodeJS.Timer>();
  const width = 700;
  const errorOptions = RealtimeValidateRuleOptions.concat([{ label: '不区分规则', value: 'ERROR' }]);

  const isShowErrList = useMemo(() => {
    return ruleDetail?.sampleMethod === 'FIELD_SPECIFICATION_EXCEPTION_INCREMENT' || !!jaxErrorCode;
  }, [ruleDetail]);

  // 根据告警阈值生成图基带配置信息
  const annotations = useMemo(() => {
    if (!ruleDetail) return;
    const { expectedValueList } = ruleDetail;
    const list: any = [];
    expectedValueList?.forEach(item => {
      const { alarmLevel, condition } = item;
      const { logicalOperator, operatorList } = condition;
      const filterNullList = operatorList.filter(x => x.value !== undefined && x.value != null);
      if (filterNullList.length === 2) {
        const a = filterNullList[0];
        const b = filterNullList[1];
        if (logicalOperator === 'and') {
          list.push({
            type: 'region',
            start: ['min', a.value],
            end: ['max', b.value],
            style: {
              fill: AlarmLevelMap[alarmLevel].color,
            },
          });
        } else {
          list.push({
            type: 'region',
            start: ['min', 'min'],
            end: ['max', a.value],
            style: {
              fill: AlarmLevelMap[alarmLevel].color,
            },
          });

          list.push({
            type: 'region',
            start: ['min', b.value],
            end: ['max', 'max'],
            style: {
              fill: AlarmLevelMap[alarmLevel].color,
            },
          });
        }
      } else if (filterNullList.length === 1) {
        const a = filterNullList[0];
        // 大于
        if (a.operator?.includes('g')) {
          list.push({
            type: 'region',
            start: ['min', a.value],
            end: ['max', 'max'],
            style: {
              fill: AlarmLevelMap[alarmLevel].color,
            },
          });
        } else {
          // 小于
          list.push({
            type: 'region',
            start: ['min', 'min'],
            end: ['max', a.value],
            style: {
              fill: AlarmLevelMap[alarmLevel].color,
            },
          });
        }
      }
    });
    return list;
  }, [ruleDetail]);

  const firedTimeRange = useMemo(() => {
    if (firedTime) {
      let endDate = moment(firedTime).add(30, 'minute');
      if (moment().isBefore(endDate)) {
        endDate = moment();
      }
      return [
        moment(firedTime).add(-30, 'minute').format('YYYY-MM-DD HH:mm:SS'),
        endDate.format('YYYY-MM-DD HH:mm:SS'),
      ];
    }
    return [];
  }, [firedTime]);

  const isRealTime = useMemo(() => {
    if (rate === 'off') {
      return false;
    }
    return true;
  }, [rate]);

  useEffect(() => {
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
    }

    if (typeof rate === 'number') {
      intervalIdRef.current = setInterval(() => {
        refresh();
      }, rate * 1000);
    }

    return () => {
      clearInterval(intervalIdRef.current);
    };
  }, [rate, range]);

  const refresh = () => {
    setRangeTime([...(getRealRangeTime(range) ?? [])]);
  };

  useEffect(() => {
    setRangeTime(getRealRangeTime(range)!);
  }, [range]);

  const fetchData = () => {
    if (!isCurrentTab) return;
    if (!pipelineId) return;
    const [beginTime, endTime] = rangeTime;
    const params = {
      id: taskId,
      ruleId,
      pipelineId,
      beginTime,
      endTime,
      width,
    };
    if (isShowErrList && fieldErrTypeList) {
      params.fieldErrTypeList = [fieldErrTypeList];
    }
    if (alertId) {
      params.alertId = alertId;
    }
    RealtimeCheckRuleMetricApi.getCalculateValue(params).then(({ data }) => {
      setData(data);
    });
  };

  useLayoutEffect(() => {
    fetchData();
  }, [taskId, ruleId, pipelineId, fieldErrTypeList, rangeTime]);

  useLayoutEffect(() => {
    if (!pipelineList) return;
    if (!pipelineId && pipelineList.length > 0) {
      setPipelineId(pipelineList[0].value as string);
    }
  }, [pipelineList]);

  useLayoutEffect(() => {
    if (firedTimeRange.length > 0) {
      if (!isEqual(firedTimeRange, range)) {
        setState('range', firedTimeRange);
      }
    }
  }, []);
  return (
    <div>
      <div className='flex w-full mb-3 mt-3'>
        {!propsPipelineId && (
          <div className='flex-1 flex flex-col'>
            <label className='text-gray-6 mb-1'>作业名称</label>
            <Select
              placeholder='请选择'
              value={pipelineId}
              options={pipelineList}
              onChange={(value: string) => setPipelineId(value)}
              showSearch
              optionFilterProp='label'
            />
          </div>
        )}
        {isShowErrList && !jaxErrorCode && (
          <div className='flex flex-col w-[245px] ml-4 flex-1'>
            <label className='text-gray-6 mb-1'>字段错误类型</label>
            <Select
              placeholder='请选择'
              value={fieldErrTypeList}
              className='w-full'
              options={errorOptions}
              allowClear
              onChange={setFieldErrTypeList}
              showSearch
              optionFilterProp='label'
            />
          </div>
        )}
      </div>
      <Space className='flex justify-end mb-2'>
        <TimeRangeSelector range={range} onChange={(range: Range) => setState('range', range)} />
        <RefreshRangeSelector
          rate={rate}
          onChange={(rate: RefreshRate) => setState('rate', rate)}
          onRefresh={refresh}
        />
      </Space>
      <ExceptionChart
        sourceData={data.calculateValue}
        rangeTime={rangeTime}
        isRealTime={false}
        annotations={annotations}
      />
    </div>
  );
};
