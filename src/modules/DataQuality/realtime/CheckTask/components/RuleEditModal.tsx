import React, { useEffect, useMemo, useState } from 'react';
import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Drawer,
  DrawerProps,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Space,
} from 'antd';
import { cloneDeep } from 'lodash';

import { QuickInsertLabel } from '@/components/business/ui';
import { CommonApi } from '@/services';

import { ExpectValueList } from '../../RuleTemplate/components/ExpectValueList';
import { config } from '../../RuleTemplate/config';
import {
  CalcRuleTypeDescEnum,
  CalcRuleTypeEnum,
  DataTimeDescEnum,
  DataTimeEnum,
  RealtimeBaseTimeModeDescEnum,
  RealtimeBaseTimeModeEnum,
  RealtimeBaseTimeModeOptions,
  RealtimeBaseValueTypeOptions,
  RealtimeDataTimeModeOptions,
  RealtimeStatisticalMethodOptions,
  RealtimeValidateRuleOptions,
  SampleMethodDescEnum,
  SampleMethodEnum,
} from '../../RuleTemplate/constants';
import { RealtimeRuleEntity, RealtimeRuleTemplateEntity } from '../../RuleTemplate/models';
import { FieldFormatOptions, ModelSelectOptions } from '../constants';
import type { AlarmFieldTemplate } from '../models';

interface Props extends DrawerProps {
  item?: RealtimeRuleEntity;
  onSave: (values) => void;
  runMode: 'ADDITIONAL' | 'STANDALONE';
  ruleTemplateList: RealtimeRuleTemplateEntity[];
}

export const RuleEditModal: React.FC<Props> = props => {
  const { ruleTemplateList, item: editItem, onSave, runMode, ...otherProps } = props;
  const [form] = Form.useForm();
  const [fields, setFields] = useState([]);
  const sampleMethod = Form.useWatch('sampleMethod', form);
  const dataTimeType: keyof typeof DataTimeEnum = Form.useWatch(['extraSetting', 'dataTimeType'], form);
  const baseTimeType = Form.useWatch(['extraSetting', 'baseTimeType'], form);
  const calculationRule = Form.useWatch('calculationRule', form);
  const baseValueType: keyof typeof RealtimeBaseTimeModeEnum = Form.useWatch('baseValueType', form);
  const [alarmFieldTemplate, setAlarmFieldTemplate] = useState<AlarmFieldTemplate[]>([]);

  const sampleMethodConfig = useMemo(() => {
    if (!sampleMethod) return;
    return config[sampleMethod];
  }, [sampleMethod]);

  const calculationRuleConfig = useMemo(() => {
    if (!sampleMethodConfig) return;
    return sampleMethodConfig.calculationRule[calculationRule];
  }, [sampleMethodConfig, calculationRule]);

  const baseValueTypeOptions = useMemo(() => {
    if (!calculationRuleConfig) return false;
    const { baseValue } = calculationRuleConfig;
    if (!baseValue) return false;
    const keys = Object.keys(baseValue);
    return RealtimeBaseValueTypeOptions.filter(x => keys.includes(x.value));
  }, [calculationRuleConfig]);

  // 根据基准值类型 获取 告警阈值类型
  const expectedValueType = useMemo(() => {
    if (!calculationRuleConfig) return 'range';
    const { expectedValue } = calculationRuleConfig;
    return expectedValue;
  }, [calculationRuleConfig, baseValueType]);

  // 基准值旁边的周期输入框，只有在baseValueN为true是才显示
  const baseValueNVisible = useMemo(() => {
    if (!calculationRuleConfig) return false;
    const { baseValue } = calculationRuleConfig;
    if (!baseValue) return false;
    if (!baseValueType) return false;
    const baseValueTypeConfig = baseValue[baseValueType];
    if (!baseValueTypeConfig) return false;
    return baseValueTypeConfig.templateExtra.baseValueN;
  }, [calculationRuleConfig, baseValueType]);

  // 统计方式是否显示
  const staticsMethodVisible = useMemo(() => {
    if (!sampleMethodConfig) return false;
    const { sampleRule } = sampleMethodConfig;
    if (!sampleRule) return false;
    return sampleRule.staticsMethod;
  }, [sampleMethodConfig, baseValueType]);

  // 规则白名单是否显示
  const whiteBlackVisible = useMemo(() => {
    if (!sampleMethodConfig) return false;
    const { sampleRule } = sampleMethodConfig;
    if (!sampleRule) return false;
    return sampleRule.validateRules;
  }, [sampleMethodConfig]);

  // 采样周期是否显示
  const samplePeriodVisible = useMemo(() => {
    if (!sampleMethodConfig) return false;
    const { sampleRule } = sampleMethodConfig;
    if (!sampleRule) return false;
    return sampleRule.samplePeriod;
  }, [sampleMethodConfig]);

  // 采样点数是否显示
  const sampleNumberVisible = useMemo(() => {
    if (!sampleMethodConfig) return false;
    const { sampleRule } = sampleMethodConfig;
    if (!sampleRule) return false;
    return sampleRule.sampleNumber;
  }, [sampleMethodConfig]);

  // 数据时间是否显示
  const dataTimeModeVisible = useMemo(() => {
    if (!sampleMethodConfig) return false;
    const { sampleRule } = sampleMethodConfig;
    if (!sampleRule) return false;
    return sampleRule.dataTimeMode;
  }, [sampleMethodConfig]);

  // 基准时间是否显示
  const baseDateTimeVisible = useMemo(() => {
    if (!sampleMethodConfig) return false;
    const { sampleRule } = sampleMethodConfig;
    if (!sampleRule) return false;
    return sampleRule.baseDateTime;
  }, [sampleMethodConfig]);

  // 业务时间格式是否显示
  const bizDateTimeFieldVisible = useMemo(() => {
    if (!sampleMethodConfig) return false;
    const { ruleExtra } = sampleMethodConfig;
    if (!ruleExtra) return false;
    return ruleExtra.bizDateTimeField;
  }, [sampleMethodConfig]);

  const rules = {
    templateId: [{ required: true }],
    name: [{ required: true }],
    samplePeriod: [{ required: true }],
    sampleNumber: [{ required: true }],
    filterCondition: [{ required: false }],
    calculationRule: [{ required: true }],
    dataTimeType: [{ required: true }],
    modelSelect: [{ required: true }],
    bizDatetimeField: [{ required: true, message: '请输入业务时间字段' }],
    bizDatetimeFormat: [{ required: true, message: '请输入业务时间格式' }],
    fieldErrTypeList: [{ required: true }],
    statisticalMethod: [{ required: true }],
  };

  const onValuesChange = (changedValue, values) => {
    const keys = Object.keys(changedValue);
    if (keys.includes('calculationRule') || keys.includes('sampleMethod')) {
      form.setFieldsValue({
        baseValueType: undefined,
        baseValueN: undefined,
      });
    }
    if (keys.includes('templateId')) {
      const item = ruleTemplateList.find(x => x.id === changedValue.templateId);
      if (item) {
        const cloneItem: any = cloneDeep(item);
        if (editItem) {
          delete cloneItem.name;
        }
        delete cloneItem.id;
        form.setFieldsValue({
          ...cloneItem,
        });
      }
    }
  };

  const handleSave = async e => {
    try {
      const values = await form.validateFields();
      onSave(values);
      otherProps.onClose?.(e);
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    if (editItem) {
      editItem.groupFieldList = editItem.groupFieldList ?? [];
      if (editItem.extraSetting) {
        editItem.extraSetting.whiteList = editItem.extraSetting.whiteList ?? [];
        editItem.extraSetting.blackList = editItem.extraSetting.blackList ?? [];
      }
      form.setFieldsValue(editItem);
    }
  }, [editItem]);

  const fetchAlarmFieldTemplateEnum = async () => {
    const res = await CommonApi.getEnumByCode2('AlarmFieldTemplateEnum');
    if (res?.code == '0000') {
      setAlarmFieldTemplate(res?.data ?? []);
    }
  };

  useEffect(() => {
    fetchAlarmFieldTemplateEnum();
  }, []);

  return (
    <Drawer
      width={830}
      styles={{ body: { padding: 0, display: 'flex', flexDirection: 'column' } }}
      title={editItem ? '编辑规则' : '添加规则'}
      {...otherProps}
    >
      <Form
        form={form}
        className='flex-1 overflow-auto p-3'
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        onValuesChange={onValuesChange}
      >
        <div className='custom-header'>基础信息</div>
        <Form.Item label='规则id' name='id' hidden>
          <Input />
        </Form.Item>
        <Form.Item label='规则模板' name='templateId' rules={rules.templateId}>
          <Select
            placeholder='请选择'
            showSearch
            options={ruleTemplateList}
            optionFilterProp='name'
            fieldNames={{ label: 'name', value: 'id' }}
          ></Select>
        </Form.Item>
        <Form.Item label='规则名称' name='name' rules={rules.name}>
          <Input placeholder='请输入' />
        </Form.Item>
        <Form.Item label='过滤条件' name='filterCondition' rules={rules.filterCondition}>
          <Input.TextArea placeholder='请输入' rows={5} />
        </Form.Item>
        <Form.Item label='分组字段' name='groupFieldList'>
          <Select mode='tags' placeholder='请选择 或 输入' options={fields} />
        </Form.Item>
        <Form.Item label='规则详情' name='description'>
          <Input.TextArea placeholder='请输入' rows={3} />
        </Form.Item>

        <div className='custom-header'>采样规则</div>

        <Form.Item label='采样方式' help={SampleMethodDescEnum[sampleMethod] ?? ''}>
          <Space className='items-center flex'>
            <Form.Item name='sampleMethod' noStyle hidden>
              <Input />
            </Form.Item>
            {SampleMethodEnum[sampleMethod] ?? '请先选择规则模板'}
          </Space>
        </Form.Item>

        {staticsMethodVisible && (
          <Form.Item
            label='统计方式'
            name={['extraSetting', 'statisticalMethod']}
            help='区分规则：按不同规则分别统计异常数；不区分规则：仅统计总体异常数'
            rules={rules.statisticalMethod}
          >
            <Radio.Group options={RealtimeStatisticalMethodOptions}></Radio.Group>
          </Form.Item>
        )}

        {whiteBlackVisible && (
          <>
            <Form.Item
              label='规则白名单'
              wrapperCol={{ span: 22 }}
              name={['extraSetting', 'fieldErrTypeList']}
              help='选择需要检查的字段标准'
              rules={rules.fieldErrTypeList}
            >
              <Checkbox.Group>
                <Row>
                  {RealtimeValidateRuleOptions.map(x => (
                    <Col key={x.value} span={7}>
                      <Checkbox value={x.value}>{x.label}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            </Form.Item>
          </>
        )}

        {samplePeriodVisible && (
          <Form.Item label='采样周期' name='samplePeriod' rules={rules.samplePeriod}>
            <Space className='flex item-center'>
              <Form.Item name='samplePeriod' noStyle>
                <InputNumber className='w-32' />
              </Form.Item>
              <span>分钟</span>
            </Space>
          </Form.Item>
        )}
        {sampleNumberVisible && (
          <Form.Item label='采样点数' name={['extraSetting', 'sampleNumber']} rules={rules.sampleNumber}>
            <Space>
              <Form.Item label='采样点数' name={['extraSetting', 'sampleNumber']} noStyle>
                <InputNumber placeholder='请输入' className='w-32' />
              </Form.Item>
              <span>条</span>
            </Space>
          </Form.Item>
        )}
        {dataTimeModeVisible && (
          <Form.Item
            label='数据时间'
            name={['extraSetting', 'dataTimeType']}
            help={DataTimeDescEnum[dataTimeType] ?? ''}
            rules={rules.dataTimeType}
          >
            <Radio.Group options={RealtimeDataTimeModeOptions}></Radio.Group>
          </Form.Item>
        )}
        {
          // 业务时间字段
          dataTimeType === 'event-time' && (
            <Form.Item wrapperCol={{ offset: 4, span: 20 }}>
              <Space>
                <Form.Item
                  labelCol={{ span: 0 }}
                  name={['extraSetting', 'bizDatetimeField']}
                  rules={rules.bizDatetimeField}
                >
                  <AutoComplete options={fields} placeholder='请输入业务时间字段' className='w-52' />
                </Form.Item>
                <Form.Item
                  labelCol={{ span: 0 }}
                  name={['extraSetting', 'bizDatetimeFormat']}
                  rules={rules.bizDatetimeFormat}
                >
                  <AutoComplete placeholder='请输入业务时间格式' options={FieldFormatOptions} className='w-72' />
                </Form.Item>
              </Space>
            </Form.Item>
          )
        }
        {baseDateTimeVisible && (
          <Form.Item
            label='基准时间'
            name={['extraSetting', 'baseTimeType']}
            help={RealtimeBaseTimeModeDescEnum[baseTimeType] ?? ''}
            required
          >
            <Radio.Group options={RealtimeBaseTimeModeOptions}></Radio.Group>
          </Form.Item>
        )}

        {
          // 基准时间 是 基于数据中字段时，需选择基准时间数据字段
          baseTimeType === 'data-field' && (
            <Form.Item wrapperCol={{ offset: 4, span: 8 }} name={['extraSetting', 'baseTimeDataField']}>
              <AutoComplete options={fields} placeholder='请输入字段名' />
            </Form.Item>
          )
        }

        {
          // 基准时间 是 基于阈值时，需输入阈值
          baseTimeType === 'fixed-threshold' && (
            <Form.Item wrapperCol={{ offset: 4, span: 8 }}>
              <Space className='flex items-center'>
                <Form.Item name={['extraSetting', 'baseTimeFixedThreshold']} noStyle>
                  <InputNumber className='w-32' />
                </Form.Item>
                <span>秒</span>
              </Space>
            </Form.Item>
          )
        }

        <div className='custom-header'>计算及告警规则</div>

        <Form.Item label='计算规则' help={CalcRuleTypeDescEnum[calculationRule] ?? ''}>
          <Space className='flex items-center'>
            <Form.Item name='calculationRule' noStyle hidden>
              <Input />
            </Form.Item>
            {CalcRuleTypeEnum[calculationRule] ?? '请先选择规则模板'}
          </Space>
        </Form.Item>

        {bizDateTimeFieldVisible && (
          <Form.Item label='业务时间格式' required>
            <Space>
              <Form.Item
                labelCol={{ span: 0 }}
                name={['extraSetting', 'bizDatetimeField']}
                rules={rules.bizDatetimeField}
              >
                <AutoComplete options={fields} placeholder='请输入业务时间字段' className='w-52' />
              </Form.Item>
              <Form.Item
                labelCol={{ span: 0 }}
                name={['extraSetting', 'bizDatetimeFormat']}
                rules={rules.bizDatetimeFormat}
              >
                <AutoComplete placeholder='请输入业务时间格式' options={FieldFormatOptions} className='w-72' />
              </Form.Item>
            </Space>
          </Form.Item>
        )}

        {baseValueTypeOptions && (
          <Form.Item label='基准值' name='baseValueConfig'>
            <Space>
              <Form.Item name='baseValueType' noStyle>
                <Select placeholder='请选择' options={baseValueTypeOptions} className='w-72' />
              </Form.Item>
              {baseValueNVisible && (
                <>
                  <Form.Item name='baseValueN' noStyle>
                    <InputNumber min={1} />
                  </Form.Item>
                  <span>个周期</span>
                </>
              )}
            </Space>
          </Form.Item>
        )}

        {whiteBlackVisible && (
          <>
            {runMode === 'ADDITIONAL' && (
              <Form.Item label='模型选择' name={['extraSetting', 'modelSelect']} rules={rules.modelSelect}>
                <Select options={ModelSelectOptions} placeholder='请选择' />
              </Form.Item>
            )}
            <Form.Item label='字段白名单' name={['extraSetting', 'whiteList']}>
              <Select mode='tags' placeholder='请选择 或 输入' options={fields} />
            </Form.Item>
            <Form.Item label='字段黑名单' name={['extraSetting', 'blackList']}>
              <Select mode='tags' placeholder='请选择 或 输入' options={fields} />
            </Form.Item>
          </>
        )}

        <Form.Item label='告警阈值' name='expectedValueList'>
          <ExpectValueList type={expectedValueType} />
        </Form.Item>
        <Form.Item label='告警压缩策略'>
          <>
            <Space className='flex items-center flex-wrap mb-2'>
              <span>在</span>
              <Form.Item noStyle name={['alarmStrategy', 'duration']}>
                <InputNumber placeholder='请输入' className='w-32' />
              </Form.Item>
              <span>秒内，连续发现异常则触发告警</span>
            </Space>
            <Space>
              <span>在</span>
              <Form.Item noStyle name={['alarmStrategy', 'suppression']}>
                <InputNumber placeholder='请输入' className='w-32' />
              </Form.Item>
              <span>秒内，发现异常消失则自动关闭告警</span>
            </Space>
          </>
        </Form.Item>
        <Form.Item
          label='告警内容模板'
          name='alarmContentTemplate'
          help={
            <QuickInsertLabel
              className='mt-2'
              list={alarmFieldTemplate}
              handleClick={item => {
                let str: string = form.getFieldValue('alarmContentTemplate');
                str = str ? str + ` ${item.code},` : `${item.code},`;
                form.setFieldValue('alarmContentTemplate', str);
              }}
            />
          }
        >
          <Input.TextArea rows={5} placeholder='请输入' />
        </Form.Item>
      </Form>
      <Divider type='horizontal' className='my-0' />
      <div className='px-4 py-3 text-center'>
        <Button className='mr-2' onClick={otherProps.onClose}>
          取消
        </Button>
        <Button type='primary' onClick={handleSave}>
          {editItem ? '保存' : '确定添加'}
        </Button>
      </div>
    </Drawer>
  );
};
