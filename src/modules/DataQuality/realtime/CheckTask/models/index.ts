import { RealtimeRuleEntity } from '../../RuleTemplate/models';

export interface SelectedPipelineTaskModel {
  fuzzyName?: string;
  pipelineIdList?: string[];
  tagIdList?: string[];
  examplePipelineId?: string;
}

export interface CheckTaskListItemModel {
  checkRuleList: RealtimeRuleEntity[];
  checkObj: string;
  clusterId: string;
  createTime: string;
  createUserName: string;
  customSetting: Record<string, any>;
  description: string;
  id: string;
  jobDisplay: string;
  jobName: string;
  jobSlot: number;
  name: string;
  optsId: string;
  ruleCount: string;
  runMode: string;
  selectMode: string;
  selectedPipelineTask: SelectedPipelineTaskModel;
  taskName: string;
  tbId: string;
  updateTime: string;
  updateUserName: string;
  yarnSessionId: string;
  projectAuth?: ProjectAuthModel;
}

export interface PipelineListItemModel {
  clusterId: string;
  clusterName: string;
  id: string;
  pipelineAlias: string;
  pipelineStatus: string;
  updateTime: string;
  pipelineName: string;
  pipelineType: string;
  processId: string;
  processType: string;
}

export interface AlgListItemModel {
  display: string;
  jobName: string;
}

type AlgDetailOutTypesItemModel = Record<
  string,
  {
    description: string;
  }
>;

export interface AlgDetailModel {
  outTypes: AlgDetailOutTypesItemModel[];
}

export interface TagListItemModel {
  id: string;
  name: string;
}

export interface AlarmFieldTemplate {
  code: string;
  name: string;
  message: string;
  enumType: string;
}
