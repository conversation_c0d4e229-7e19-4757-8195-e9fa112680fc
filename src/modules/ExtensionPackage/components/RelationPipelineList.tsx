import React, { useEffect, useState } from 'react';
import { Modal, ModalProps } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { PipelineApi } from '@/modules/PipelineDevOps/services/PipelineApi';

interface Props extends ModalProps {
  job: JobModel;
}
export const RelationPipelineList: React.FC<Props> = ({ job, ...otherProps }) => {
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const { pagination, setPagination, queryParams, handleTableChange } = useCustomTableHook({
    filter: {
      jobName: job.jobName,
    },
  });
  const columns: Array<ColumnType<JobModel>> = [
    {
      title: '任务名称',
      dataIndex: 'pipelineAlias',
      key: 'pipelineAlias',
    },
    {
      title: '任务标识',
      dataIndex: 'pipelineName',
      key: 'pipelineName',
    },
  ];

  const fetchData = () => {
    setLoading(true);
    PipelineApi.getList(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <Modal title={`${job.jobDisplay} 关联任务`} {...otherProps} width={776} footer={null}>
      <div>
        <CustomTable
          dataSource={list}
          columns={columns}
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
          scroll={{ x: undefined, y: 500 }}
          className='bordered'
          id='id'
        ></CustomTable>
      </div>
    </Modal>
  );
};
