import { useState } from 'react';
import { DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps, Upload, UploadFile } from 'antd';

import { EXTENSION_TYPE_LIST_ITEM } from '../constant';
import { <PERSON>ar<PERSON><PERSON> } from '../services/JarApi';

interface Props {
  extensionType: EXTENSION_TYPE_LIST_ITEM;
  onChange: (file?: File) => void;
}
export const UploadExtensionPackage: React.FC<Props> = ({ extensionType, onChange }) => {
  const [file, setFile] = useState<File>();
  const [systemJarList, setSystemJarList] = useState<MenuProps['items']>([]);

  const onChangeFile = ({ file, fileList }: { file: UploadFile; fileList: UploadFile[] }) => {
    if (file.status === 'removed') {
      setFile(undefined);
      onChange(undefined);
    } else {
      setFile(fileList[0].originFileObj);
      onChange(fileList[0].originFileObj);
    }
  };

  const fetchSystemJarList = async () => {
    if (!systemJarList) return;
    if (systemJarList.length > 0) return;
    const { data } = await JarApi.getJarBuiltIn();
    setSystemJarList(
      data.map((x: string) => ({
        label: x,
        key: x,
      })),
    );
  };

  const onClickItem = (item: any) => {
    setFile(item.key);
    onChange(item.key);
  };

  return (
    <div>
      {file && (
        <span>
          {file?.name ?? file} <DeleteOutlined className='cursor-pointer' onClick={() => setFile(undefined)} />
        </span>
      )}
      {!file && (
        <div className='flex items-center'>
          <Upload name='file' onChange={onChangeFile} accept={extensionType?.accept}>
            <Button icon={<UploadOutlined />}>选择{extensionType?.accept}文件</Button>
          </Upload>
          <span className='mx-2'>或</span>
          <Dropdown menu={{ items: systemJarList!, onClick: onClickItem }}>
            <Button onClick={() => fetchSystemJarList()}>导入内置扩展包</Button>
          </Dropdown>
        </div>
      )}
    </div>
  );
};
