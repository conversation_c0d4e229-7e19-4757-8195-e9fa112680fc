import React, { useEffect, useState } from 'react';
import { Button, message, Modal, ModalProps, Space, Spin } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';

import { JarApi } from '../services/JarApi';

interface Props extends ModalProps {
  id: string;
}
export const ExtensionPackageHistoryModal: React.FC<Props> = ({ id, ...rest }) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const { queryParams, pagination, handleTableChange, setTotal } = useCustomTableHook({
    filter: {
      jarId: id,
    },
    pageSize: 10,
  });
  const columns: Array<ColumnType<WH_LAYER.BusinessProcessEntity>> = [
    {
      title: '文件名称',
      dataIndex: 'jarFile',
      key: 'jarFile',
    },
    {
      title: '版本号',
      dataIndex: 'jarVersion',
      key: 'jarVersion',
      width: 80,
    },
    {
      title: '上传时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
    },
    {
      title: '操作',
      width: 100,
      align: 'center',
      render: (_, record) => {
        return (
          <Space>
            <Button size='small' type='link' onClick={() => handleDownload(record)}>
              下载
            </Button>
            <Button size='small' type='link' onClick={() => handleDelete(record)}>
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  const handleDownload = record => {
    JarApi.downloadHistory(record.id);
  };

  const handleDelete = async record => {
    await JarApi.deleteHistory(record.id);
    message.success('删除成功');
    fetchData();
  };

  const fetchData = () => {
    setLoading(true);
    JarApi.getHistory(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setTotal(total);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);
  return (
    <Modal title='扩展包上传历史' width={1000} okText='关闭' footer={null} {...rest}>
      {loading ? (
        <div className='flex justify-center items-center py-10'>
          <Spin />
        </div>
      ) : (
        <CustomTable
          pagination={pagination}
          dataSource={list}
          columns={columns}
          className='bordered'
          rowKey='id'
          scroll={{ x: undefined, y: 500 }}
          onChange={handleTableChange}
        ></CustomTable>
      )}
    </Modal>
  );
};
