type JarType = 'jar' | 'python' | 'general' | 'flink_sql' | 'api_handler';

type JobType = 'streaming' | 'batch';

// 算子slot数据模型
interface JobSlotEntity {
  description?: string;
  raw: string;
  parameterizedTypes?: string[];
}

interface JarSqlEntity {
  extClass: string;
  extType: 'connector' | 'udf';
  functionName?: string;
  functionType?: string;
  identifier: string;
  optionalParams?: string[];
  requiredParams?: string[];
  sink: boolean;
  source: boolean;
  id: string;
}

interface JarEntity {
  id: string;
  jarName: string;
  jarVersion: string;
  jarFile: string;
  jarDescription: string;
  clusterId: string;
  jarType: string;
  file?: File;
  jarBuildFile?: string;
  fileNotExists: boolean;
  sqls?: JarSqlEntity[];
}

interface JobModel {
  id: string;
  apiVersion: string;
  docUrl?: string;
  hasDoc: boolean;
  hasIcon: boolean;
  iconUrl: string;
  internal: boolean;
  jarId: string;
  jobCategory: string;
  jobDescription: string;
  jobDisplay: string;
  jobName: string;
  jobRole: 'sink' | 'process' | 'source';
  jobType: 'batch' | 'streaming';
  inTypes: JobSlotEntity[];
  outTypes: JobSlotEntity[];
  updateTime: string;
  updateUserName: string;
}
