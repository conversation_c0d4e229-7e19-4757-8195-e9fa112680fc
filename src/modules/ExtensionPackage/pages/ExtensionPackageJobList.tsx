import { useEffect, useState } from 'react';
import { Button } from 'antd';
import { Link, useSearchParams } from 'umi';

import { SearchInput } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable/';
import { useGlobalHook } from '@/hooks';

import { JobParametersModal } from '../components/JobParametersModal';
import { RelationPipelineList } from '../components/RelationPipelineList';
import { JobApi } from '../services/JobApi';

export const ExtensionPackageJobList = () => {
  const [searchParams] = useSearchParams();
  const jarId = searchParams.get('jarId');
  const jarName = searchParams.get('jarName');
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [viewParameterVisible, setViewParameterVisible] = useState(false);
  const [job, setJob] = useState<JobModel>();
  const [pipelineListVisible, setPipelineListVisible] = useState(false);

  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const { pagination, setPagination, queryParams, filter, setFilter, handleTableChange } = useCustomTableHook({
    filter: {
      jarId,
    },
  });
  const columns: Array<ColumnType<JobModel>> = [
    {
      title: '名称',
      dataIndex: 'jobDisplay',
      key: 'jobDisplay',
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'jobType',
      key: 'jobType',
      width: 100,
      render: (jobType: 'batch' | 'streaming') => {
        return jobType === 'batch' ? '批' : '流';
      },
    },
    {
      title: '类名',
      dataIndex: 'jobName',
      key: 'jobName',
      width: 400,
    },
    {
      title: '描述',
      dataIndex: 'jobDescription',
      key: 'jobDescription',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 250,
      render(record) {
        const { id, jobDisplay: display, jobName, docUrl } = record;
        return (
          <>
            <a className='mr-4' onClick={() => viewParameters(record)}>
              查看参数
            </a>
            <a className='mr-4' onClick={() => viewRelationPipeline(record)}>
              关联作业
            </a>

            <Link
              target='_blank'
              to={`/job/doc?display=${display}&jobName=${jobName}&docUrl=${encodeURIComponent(docUrl)}`}
              type='link'
              className={`mr-4 ${docUrl ? '' : 'invisible'}`}
            >
              文档
            </Link>
          </>
        );
      },
    },
  ];

  const viewParameters = (item: JobModel) => {
    setJob(item);
    setViewParameterVisible(true);
  };

  const viewRelationPipeline = (item: JobModel) => {
    setJob(item);
    setPipelineListVisible(true);
  };

  const fetchData = () => {
    setLoading(true);
    JobApi.getList(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    setPageInfo({ title: `查看${jarName ?? ''}算子` });
    return () => {
      resetPageInfo();
    };
  }, []);
  return (
    <div className='cluster-list bg-white h-full flex flex-col pt-3'>
      <div className='px-3 mb-3 h-8 flex justify-between'>
        <SearchInput
          className='w-[300px]'
          placeholder='按名称、类型、描述进行筛选'
          defaultValue={filter.search}
          onSearch={value => setFilter({ ...filter, search: value })}
          allowClear
        />
        <Button type='text' onClick={fetchData}>
          <i className='iconfont icon-refresh-line' />
        </Button>
      </div>
      <CustomTable
        dataSource={list}
        columns={columns}
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 1200 }}
        className={'flex-1'}
        id='id'
      ></CustomTable>

      {viewParameterVisible && (
        <JobParametersModal job={job!} visible={viewParameterVisible} onClose={() => setViewParameterVisible(false)} />
      )}
      {pipelineListVisible && (
        <RelationPipelineList
          job={job!}
          open={pipelineListVisible}
          onCancel={() => {
            setPipelineListVisible(false);
          }}
        />
      )}
    </div>
  );
};
