export interface EXTENSION_TYPE_LIST_ITEM {
  label: string;
  value: JarType;
  desc: string;
  accept: '.zip' | '.jar';
  icon: string;
}
export const EXTENSION_TYPE_LIST: EXTENSION_TYPE_LIST_ITEM[] = [
  {
    label: 'Java算子包',
    value: 'jar',
    desc: '使用Java语言开发，用于扩展管线作业的算子，支持jar包上传',
    icon: 'icon-java-line',
    accept: '.jar',
  },
  {
    label: 'Python算法包',
    value: 'python',
    desc: '使用Python语言开发，用于扩展管线作业的算子，支持zip包上传',
    icon: 'icon-python-line',
    accept: '.zip',
  },
  {
    label: 'SQL扩展包',
    value: 'flink_sql',
    desc: '使用Java语言开发，用于扩展Flink SQL作业的SQL能力（例如：UDF），支持jar包上传',
    icon: 'icon-flink-sql-line',
    accept: '.jar',
  },
  {
    label: '一般扩展包',
    value: 'general',
    desc: '扩展作业的外部对接范围，例如额外的JDBC驱动、连机器等，支持jar包上传',
    icon: 'icon-puzzle-line',
    accept: '.jar',
  },
  {
    label: '数据服务扩展包',
    value: 'api_handler',
    desc: '扩展平台数据服务的能力，支持jar包上传',
    icon: 'icon-application-line',
    accept: '.jar',
  },
];
