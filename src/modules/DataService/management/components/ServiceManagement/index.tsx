import { useEffect, useState } from 'react';
import { Button, message, Modal, Tooltip } from 'antd';
import { history, useSearchParams } from 'umi';

import { CustomTable, TagSelect, useCustomTableHook } from '@/components';
import { useRightsHook } from '@/hooks';
import { ApiModeEnum } from '@/modules/DataService/models';
import { DataService } from '@/services';

import './index.less';

import { ApiEntity, ApiStatusEnum } from '../../../models';

import { AuthModal, ServiceManagementSearch } from './components';

export const ServiceManagement = () => {
  const { hasRights } = useRightsHook();
  const permissionCode = 'api_service:operate';
  const [searchParams] = useSearchParams();
  const name = searchParams.get('name');

  const tableHook = useCustomTableHook({
    pageSize: 20,
    cacheId: name ? undefined : 'apiManagement',
    ...name && { filter: { name } },
  });

  const {
    pagination,
    queryParams,
    selectedRowKeys,
    selectedRows,
    filter,
    sort,
    setPagination,
    setFilter,
    handleTableChange,
    onRowSelectionChange,
  } = tableHook;

  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<ApiEntity[]>([]);
  const [authOpen, setAuthOpen] = useState(false);
  const [viewId, setViewId] = useState<string | undefined>();

  const columns: Array<ColumnType<ApiEntity>> = [
    {
      title: 'API名称',
      dataIndex: 'name',
      render: (name, record) => (
        <div className='flex items-center'>
          <Tooltip title={name}>
            <a className='flex-1 overflow-hidden text-ellipsis api-name' onClick={() => toServiceDev(record)}>
              {name}
            </a>
          </Tooltip>
          {!record.isPublished && (
            <Tooltip title={`当前服务变更内容未发布（当前上线版本v${record.version}）`}>
              <i className='iconfont icon-error-line' style={{ color: '#FFB232' }} />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 120,
      render: status => <span className={`service-management-status ${status}`}>{ApiStatusEnum[status]}</span>,
    },
    {
      title: '分组',
      width: 200,
      dataIndex: 'apiGroupName',
    },
    {
      title: '消费模式',
      width: 120,
      dataIndex: 'apiMode',
      render: value => <span>{value === 'KAFKA' ? 'kafka消费' : ApiModeEnum[value]}</span>,
    },
    {
      title: '描述',
      width: 200,
      dataIndex: 'description',
    },
    {
      title: '标签',
      width: 200,
      dataIndex: 'tags',
      render: (_, record) => (
        <TagSelect value={record.tags} labelKey='name' valueKey='name' position='right' addable={false} data={[]} />
      ),
    },
    {
      title: '授权次数',
      width: 120,
      dataIndex: 'authTimes',
      render: (authTimes, record) => <a onClick={() => viewAuth(record)}>{authTimes}</a>,
    },
    {
      title: '操作',
      dataIndex: 'operator',
      fixed: 'right',
      width: 200,
      render: (_: any, record) => (
        <div className='flex gap-x-2'>
          <Button
            type='link'
            size='small'
            disabled={!hasRights(permissionCode, record?.projectAuth) || isOnline(record.status)}
            onClick={() => handleOnline(record)}
          >
            上线
          </Button>
          <Button
            type='link'
            size='small'
            disabled={!hasRights(permissionCode, record?.projectAuth) || !isOnline(record.status)}
            onClick={() => handleOffline(record)}
          >
            下线
          </Button>
          {isHttp(record.consumeMode) && (
            <Button type='link' size='small' onClick={() => isHttp(record.consumeMode) && toServiceOverview(record)}>
              调用统计
            </Button>
          )}
        </div>
      ),
    },
  ];

  const isOnline = (status: string) => status === 'ONLINE';
  const isHttp = (consumeMode: string) => consumeMode === 'HTTP';

  const handleOnline = async (record: ApiEntity) => {
    let title = '';
    if (!record.isPublished) {
      title = '当前服务变更内容未发布，确认上线版本旧版本配置信息吗?';
    } else {
      title = `确认上线服务「${record.name}」吗?`;
    }

    Modal.confirm({
      title,
      onOk: async () => {
        const { code, msg } = await DataService.apiService.online({ apis: [{ id: record.id }] });
        if (code === '0000') {
          message.success('上线成功');
          fetchData();
        } else {
          message.error(msg);
        }
      },
    });
  };

  const handleOffline = async (record: ApiEntity) => {
    Modal.confirm({
      title: `确认下线服务「${record.name}」吗?`,
      onOk: async () => {
        await DataService.apiService
          .offline({ apis: [{ id: record.id }] })
          .then(() => {
            message.success('下线成功');
            fetchData();
          })
          .catch(e => {
            console.log(e);
          });
      },
    });
  };

  const toServiceDev = (record: ApiEntity) => {
    history.push(
      `/data-service/dev/detail/${record.id}?consumeMode=${record?.consumeMode}&apiMode=${record?.apiMode}`,
      { title: record.name },
    );
  };

  const toServiceOverview = (record: ApiEntity) => {
    history.push(`/data-service/management?tabName=apiOverview&serviceId=${record.id}`);
  };

  const viewAuth = (record: ApiEntity) => {
    setViewId(record.id);
    setAuthOpen(true);
  };

  const cancelViewAuth = () => {
    setAuthOpen(false);
    setViewId(undefined);
  };

  const fetchData = () => {
    setLoading(true);
    DataService.apiService
      .getList(queryParams)
      .then(res => {
        const { data, total } = res;
        setList(data);
        setPagination({ ...pagination, total });
        onRowSelectionChange([], []);
      })
      .finally(() => setLoading(false))
      .catch(() => {});
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='h-full flex flex-col'>
      <ServiceManagementSearch
        filter={filter}
        setFilter={setFilter}
        fetchData={fetchData}
        sort={sort}
        selectedRows={selectedRows}
        selectedRowKeys={selectedRowKeys}
      />
      <CustomTable
        className='flex-1'
        scroll={{ x: 1400 }}
        loading={loading}
        columns={columns}
        rowSelection={{
          selectedRowKeys,
        }}
        dataSource={list}
        pagination={pagination}
        onChange={handleTableChange}
        onRowSelectionChange={onRowSelectionChange}
      />
      {authOpen && <AuthModal id={viewId!} open={authOpen} onCancel={cancelViewAuth} />}
    </div>
  );
};
