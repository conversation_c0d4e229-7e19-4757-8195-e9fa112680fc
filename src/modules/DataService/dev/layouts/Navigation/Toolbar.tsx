// 数据开发树顶部的操作栏
import React from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { Dropdown } from 'antd';

import { useRightsHook } from '@/hooks';
import { useDataServiceStore } from '@/modules/DataService/dev/stores/useDataServiceStore';

interface Props {
  onRefresh: () => void;
  loading?: boolean;
}

export const Toolbar: React.FC<Props> = ({ onRefresh, loading }) => {
  const { hasRights } = useRightsHook();

  const setGroupModalOpen = useDataServiceStore(state => state.setGroupModalOpen);
  const setApiModalOpen = useDataServiceStore(state => state.setApiModalOpen);
  return (
    <div className='flex justify-between items-center p-3'>
      <span className='text-gray-6'>服务开发</span>
      <div className='flex items-center'>
        <Dropdown
          menu={{
            items: [
              {
                key: 'createGroup',
                disabled: !hasRights('api_service:write'),
                label: (
                  <div
                    className='w-26'
                    onClick={() => {
                      if (!hasRights('api_service:write')) return;
                      setGroupModalOpen(true);
                    }}
                  >
                    新建分组
                  </div>
                ),
              },
              {
                key: 'createApi',
                disabled: !hasRights('api_service:write'),
                label: (
                  <div
                    className='w-26'
                    onClick={() => {
                      if (!hasRights('api_service:write')) return;
                      setApiModalOpen(true);
                    }}
                  >
                    新建服务接口
                  </div>
                ),
              },
            ],
          }}
          placement='bottomLeft'
        >
          <i className='iconfont icon-add_box-line text-2xl leading-6 mr-2 text-gray-12 cursor-pointer' />
        </Dropdown>
        {loading ? (
          <LoadingOutlined className='text-2xl leading-6' />
        ) : (
          <i
            className='iconfont icon-refresh-line text-2xl leading-6 text-gray-12 cursor-pointer'
            title='刷新'
            onClick={() => onRefresh()}
          />
        )}
      </div>
    </div>
  );
};
