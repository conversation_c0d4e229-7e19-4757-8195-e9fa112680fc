import { SearchInput } from '@/components';
import { useDataServiceStore } from '@/modules/DataService/dev/stores/useDataServiceStore';

import { GroupTree } from '../../components/GroupTree';

import { Toolbar } from './Toolbar';

export const Navigation = () => {
  const fetchTreeData = useDataServiceStore(state => state.fetchTreeData);
  const setSearchValue = useDataServiceStore(state => state.setSearchValue);

  return (
    <div className='flex h-full flex-col overflow-hidden bg-neutral-50'>
      <div className='flex-1 overflow-hidden flex-col flex'>
        <Toolbar onRefresh={fetchTreeData} />
        <div className='w-full flex flex-1 px-2 flex-col overflow-auto'>
          <SearchInput
            placeholder='搜索服务名称或apiPath'
            onSearch={value => {
              setSearchValue(value);
            }}
          />
          <div className='mt-2 w-full flex-1'>
            <GroupTree />
          </div>
        </div>
      </div>
    </div>
  );
};
