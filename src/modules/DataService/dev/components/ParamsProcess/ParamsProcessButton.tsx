import { useState } from 'react';
import { Button, ButtonProps } from 'antd';

import { useRightsHook } from '@/hooks';
import { RequestColumn, ResponseColumn } from '@/modules/DataService/models';

import { ParamsProcessModal } from './ParamsProcessModal';

interface Props extends Omit<ButtonProps, 'onChange'> {
  requestColumns: RequestColumn[];
  responseColumns: ResponseColumn[];
  prevScript: string;
  postScript: string;
  onChange: (data: { prevScript: string; postScript: string }) => void;
}
export const ParamsProcessButton = (props: Props) => {
  const { children, requestColumns, responseColumns, prevScript, postScript, onChange, ...otherProps } = props;

  const { hasRights } = useRightsHook();

  const [open, setOpen] = useState(false);
  return (
    <>
      <Button
        type='text'
        size='small'
        className={hasRights('api_service:write') ? 'text-gray-6' : ''}
        disabled={!hasRights('api_service:write')}
        onClick={() => {
          setOpen(true);
        }}
        {...otherProps}
      >
        {children ?? (
          <>
            <i className='text-sm iconfont icon-pmmanage-line mr-1' />
            参数处理
          </>
        )}
      </Button>
      {open && (
        <ParamsProcessModal
          requestColumns={requestColumns}
          responseColumns={responseColumns}
          prevScript={prevScript}
          postScript={postScript}
          onChange={onChange}
          open={open}
          onCancel={() => setOpen(false)}
        />
      )}
    </>
  );
};
