import { useEffect, useState } from 'react';
import { useLocation } from 'umi';

import { DataService } from '@/services';

import { CompareDetail, Title } from './components';

export const VersionCompare = () => {
  const location = useLocation();
  const { selectedRowKeys, name, apiId } = location.state as any;

  const [data, setData] = useState<any>();
  const [versionList, setVersionList] = useState<any[]>([]);

  useEffect(() => {
    fetchVersionList();
    fetchComPareData();
  }, [selectedRowKeys]);

  const fetchVersionList = async () => {
    DataService.apiHistoryService
      .getAll(apiId)
      .then(({ data }) => {
        setVersionList(data);
      })
      .catch(() => {});
  };
  const fetchComPareData = async () => {
    const params =
      selectedRowKeys.length === 1
        ? {
          targetId: selectedRowKeys[0],
        }
        : {
          sourceId: selectedRowKeys[0],
          targetId: selectedRowKeys[1],
        };
    DataService.apiHistoryService
      .versionCompare(params)
      .then(({ data }) => {
        setData(data);
      })
      .catch(() => {});
  };

  const getOptions = () => {
    const options = versionList?.map(item => ({ label: `V${item.version}`, value: item.id }));
    if (selectedRowKeys.length === 1) {
      return [...options, { label: '当前保存内容', value: apiId, isCurrent: true }];
    }
    return options;
  };

  return (
    <div className='h-full flex flex-col overflow-auto'>
      <div className='mb-2 bg-white px-4 py-3'>
        <Title title={name} />
      </div>
      <div className='flex-1 mx-4 mb-2 grid grid-cols-2 gap-2 overflow-auto'>
        <CompareDetail versionOptions={getOptions()} detail={data?.sourceHistoryResp} />
        <CompareDetail versionOptions={getOptions()} detail={data?.targetHistoryResp} />
      </div>
    </div>
  );
};
