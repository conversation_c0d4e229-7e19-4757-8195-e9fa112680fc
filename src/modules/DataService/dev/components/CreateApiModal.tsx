import { useEffect } from 'react';
import { Form, Modal, ModalProps, Radio, Select } from 'antd';
import { history } from 'umi';

import { ApiModeOptions, ConsumeModeOptions } from '@/modules/DataService/models';

import { useDataServiceStore } from '../stores/useDataServiceStore';

const formLayout = {
  labelCol: { style: { width: '138px' } },
  wrapperCol: { xxl: 24, xl: 24, lg: 24, xs: 24, sm: 24 },
};

interface Props extends ModalProps {
  handleCancel: () => void;
  apiGroupId?: string;
}

export const CreateApiModal = (props: Props) => {
  const { handleCancel, open, apiGroupId } = props;
  const groupList = useDataServiceStore(state => state.groupList);
  const [form] = Form.useForm();

  const consumeMode = Form.useWatch('consumeMode', form);

  const handleOk = async () => {
    const values = await form.validateFields();
    if (values.apiMode) {
      history.replace(
        `/data-service/dev/create?apiGroupId=${values.apiGroupId}&consumeMode=${values.consumeMode}&apiMode=${values.apiMode}`,
      );
    } else {
      history.replace(`/data-service/dev/create?apiGroupId=${values.apiGroupId}&consumeMode=${values.consumeMode}`);
    }
    handleCancel();
  };

  useEffect(() => {
    if (apiGroupId) {
      form.setFieldValue('apiGroupId', apiGroupId);
    }
  }, [apiGroupId]);

  return (
    <Modal title='创建API服务' onCancel={handleCancel} open={open} width={760} onOk={handleOk}>
      <Form form={form} {...formLayout}>
        <Form.Item name='apiGroupId' label='分组名称' rules={[{ required: true }]}>
          <Select
            disabled={!!apiGroupId}
            allowClear
            options={groupList}
            showSearch
            fieldNames={{ label: 'name', value: 'id' }}
            optionFilterProp='name'
          />
        </Form.Item>
        <Form.Item name='consumeMode' label='消费模式' rules={[{ required: true }]}>
          <Radio.Group options={ConsumeModeOptions} />
        </Form.Item>
        {consumeMode === 'HTTP' && (
          <Form.Item name='apiMode' label='消费模式' rules={[{ required: true }]}>
            <Radio.Group options={ApiModeOptions.filter(item => ['STANDARD', 'SQL', 'CUSTOM'].includes(item.value))} />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};
