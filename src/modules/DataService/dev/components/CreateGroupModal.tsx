import { useEffect, useState } from 'react';
import { Form, Input, message, Modal, ModalProps } from 'antd';

import { DataService } from '@/services';

import { useDataServiceStore } from '../stores/useDataServiceStore';

const formLayout = {
  labelCol: { style: { width: '138px' } },
  wrapperCol: { xxl: 24, xl: 24, lg: 24, xs: 24, sm: 24 },
};

interface Props extends ModalProps {
  handleCancel: () => void;
  record?: { id: string; name: string };
}

export const CreateGroupModal = (props: Props) => {
  const { title, handleCancel, open, record } = props;
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const fetchTreeData = useDataServiceStore(state => state.fetchTreeData);

  const handleOk = async () => {
    const values = await form.validateFields();
    setConfirmLoading(true);
    const res = record?.id
      ? await DataService.apiGroupService.update({ ...values, id: record.id }).finally(() => setConfirmLoading(false))
      : await DataService.apiGroupService.create(values).finally(() => setConfirmLoading(false));
    if (res?.code === '0000') {
      fetchTreeData();
      handleCancel();
      message.success('操作成功');
    }
  };

  useEffect(() => {
    if (record?.id) {
      form.setFieldsValue(record);
    }
  }, [record]);

  return (
    <Modal
      title={title}
      onCancel={handleCancel}
      open={open}
      width={760}
      onOk={handleOk}
      confirmLoading={confirmLoading}
    >
      <Form form={form} {...formLayout}>
        <Form.Item name='name' label='分组名称' rules={[{ required: true }]}>
          <Input placeholder='请输入' allowClear />
        </Form.Item>
      </Form>
    </Modal>
  );
};
