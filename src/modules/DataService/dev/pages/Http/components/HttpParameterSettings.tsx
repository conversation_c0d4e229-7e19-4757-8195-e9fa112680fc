import { useEffect, useState } from 'react';
import { Button, Checkbox, Tooltip } from 'antd';
import { ColumnType } from 'antd/es/table';
import { useParams } from 'umi';

import { CustomTable, useCustomTableHook, useKeepAliveTabs } from '@/components';
import { DsColumnTypeEnum, RequestColumn, ResponseColumn, sortColumn } from '@/modules/DataService/models';

type ActivaKey = 'resErr' | 'reqErr' | 'res' | 'req' | 'all';
type onChangeType =
  | 'setRequestKey'
  | 'setResponseKey'
  | 'setSortColumns'
  | 'setRequestColumn'
  | 'setResponseColumn'
  | 'setDataSource';

type onChangeValueType = RequestColumn[] | string[] | sortColumn[] | ResponseColumn[];

interface Props {
  requestSource: RequestColumn[];
  requestKey: string[];
  responseKey: string[];
  sortColumns: sortColumn[];
  requestColumn: RequestColumn[];
  responseColumn: ResponseColumn[];
  requestType;
  onChange: (type: onChangeType, value: onChangeValueType) => void;
}

export const HttpParameterSettings = (props: Props) => {
  const { requestSource, requestKey, responseKey, sortColumns, requestColumn, responseColumn, requestType, onChange } =
    props;
  const { apiId, editId } = useParams();
  const { setPageInfo } = useKeepAliveTabs();
  const [activekey, setActiveKey] = useState<ActivaKey>('all');
  const [onlyShowErr, setOnlyErrShow] = useState<boolean>(false);
  const [dataSource, setDataSoure] = useState<RequestColumn[]>([]);
  const [allErr, setAllErr] = useState<RequestColumn[] | ResponseColumn[]>([]);
  const [allSouceLength, setAllSourceLength] = useState<number>(0);

  const [requestErr, setRequestErr] = useState<RequestColumn[]>([]);
  const [responceErr, setResponceErr] = useState<ResponseColumn[]>([]);

  const { pagination, handleTableChange } = useCustomTableHook({ pageSize: 10 });

  useEffect(() => {
    setRequestErr(
      requestColumn
        .filter(item => requestSource.every(temp => temp.mappingField !== item.mappingField))
        .map(item => {
          item.idErrPamars = true;
          return item;
        }),
    );
  }, [requestColumn, requestSource]);

  useEffect(() => {
    setResponceErr(
      responseColumn
        .filter(item => requestSource.every(temp => temp.mappingField !== item.mappingField))
        .map(item => {
          item.idErrPamars = true;
          return item;
        }),
    );
  }, [responseColumn, requestSource]);

  useEffect(() => {
    let arr;
    // eslint-disable-next-line max-len
    const noRepeat = responceErr.filter(item => requestErr.every(temp => temp.mappingField !== item.mappingField));
    setAllErr(requestErr.concat(noRepeat));
    const allCol = requestErr.concat(noRepeat).concat(requestSource);
    if (onlyShowErr) {
      arr = requestErr.concat(noRepeat);
    } else {
      switch (activekey) {
      case 'all':
        arr = allCol;
        break;
      case 'resErr':
        arr = responceErr;
        break;
      case 'reqErr':
        arr = requestErr;
        break;
      case 'res':
        arr = allCol.filter(item => responseKey.includes(item.mappingField));
        break;
      case 'req':
        arr = allCol.filter(item => requestKey.includes(item.mappingField));
        break;
      default:
        arr = [];
        break;
      }
    }

    setDataSoure(arr);
    setAllSourceLength(allCol.length);
  }, [requestSource, responceErr, requestErr, activekey, onlyShowErr]);

  const updateColumn = () => {
    const resetReqCol: RequestColumn[] = requestColumn.filter(item => requestKey.includes(item.mappingField));
    const resetResCol: ResponseColumn[] = responseColumn.filter(item => responseKey.includes(item.mappingField));
    dataSource.forEach(item => {
      if (
        requestKey.includes(item.mappingField) &&
        resetReqCol.every(temp => temp.mappingField !== item.mappingField)
      ) {
        item.operation = item.operation ?? '=';
        item.position = item.position ?? requestType === 'GET' ? 'QUERY' : 'BODY';
        resetReqCol.push(item);
      }
      if (
        responseKey.includes(item.mappingField) &&
        resetResCol.every(temp => temp.mappingField !== item.mappingField)
      ) {
        resetResCol.push(item);
      }
    });
    onChange('setRequestColumn', resetReqCol);
    onChange('setResponseColumn', resetResCol);
  };

  useEffect(() => {
    updateColumn();
  }, [requestKey, responseKey]);

  const showStatistics = (keys: string[], type: 'res' | 'req') => {
    return (
      <Tooltip
        title={
          <>
            <div>无效字段：{type === 'res' ? responceErr.length : requestErr.length}</div>
            <div>{`已选择字段：${keys.length}`}</div>
            <div>{`字段总数量：${requestSource.length}`}</div>
          </>
        }
        placement='bottom'
      >
        <div className='flex h-8 leading-8  bg-[#f2f2f2] items-center px-0.5'>
          <div
            className={`px-2 h-7 cursor-pointer ${activekey === `${type}Err` ? 'bg-[#ffffff]' : ''}`}
            onClick={() => {
              setActiveKey(`${type}Err`);
              setOnlyErrShow(false);
            }}
          >
            {type === 'res' ? responceErr.length : requestErr.length}
          </div>
          <div
            onClick={() => {
              setActiveKey(type);
              setOnlyErrShow(false);
            }}
            className={`px-2 h-7 cursor-pointer ${activekey === `${type}` ? 'bg-[#ffffff]' : ''}`}
          >
            {keys.length}
          </div>
          <div
            onClick={() => setActiveKey('all')}
            className={`px-2 h-7 cursor-pointer ${activekey === 'all' ? 'bg-[#ffffff]' : ''}`}
          >
            {requestSource.length}
          </div>
        </div>
      </Tooltip>
    );
  };

  const columns: Array<ColumnType<any>> = [
    {
      title: '字段名称',
      dataIndex: 'mappingField',
    },
    {
      title: '字段类型',
      dataIndex: 'columnType',
      render: (text, record) => <span>{DsColumnTypeEnum[text] || DsColumnTypeEnum[record.type]}</span>,
    },
    {
      title: '字段描述',
      dataIndex: 'desc',
    },
    {
      title: (
        <>
          <Checkbox
            disabled={!!apiId || dataSource.length !== allSouceLength}
            className='mr-1'
            checked={allSouceLength > 0 && requestKey.length >= allSouceLength}
            onChange={({ target: { checked } }) => {
              setPageInfo({ isDirty: true });
              if (checked) {
                onChange(
                  'setRequestKey',
                  dataSource?.map(item => item.mappingField),
                );
              } else {
                onChange('setRequestKey', []);
              }
            }}
          />
          <span>设为请求参数</span>
        </>
      ),
      render: (_, record) => (
        <Checkbox
          checked={requestKey.includes(record.mappingField)}
          disabled={!!apiId}
          onChange={({ target: { checked } }) => {
            setPageInfo({ isDirty: true });
            if (checked) {
              onChange('setRequestKey', [...requestKey, record.mappingField]);
            } else {
              onChange(
                'setRequestKey',
                requestKey.filter(item => item !== record.mappingField),
              );
            }
          }}
        />
      ),
    },
    {
      title: (
        <>
          <Checkbox
            disabled={!!apiId || dataSource.length !== allSouceLength}
            className='mr-1'
            checked={allSouceLength > 0 && responseKey.length >= allSouceLength}
            onChange={({ target: { checked } }) => {
              setPageInfo({ isDirty: true });
              if (checked) {
                onChange(
                  'setResponseKey',
                  dataSource?.map(item => item.mappingField),
                );
              } else {
                onChange('setResponseKey', []);
              }
            }}
          />
          <span>设为返回参数</span>
        </>
      ),
      render: (_, record) => (
        <Checkbox
          checked={responseKey.includes(record.mappingField)}
          disabled={!!apiId}
          onChange={({ target: { checked } }) => {
            setPageInfo({ isDirty: true });
            if (checked) {
              onChange('setResponseKey', [...responseKey, record.mappingField]);
            } else {
              onChange(
                'setResponseKey',
                responseKey.filter(item => item !== record.mappingField),
              );
            }
          }}
        />
      ),
    },
    {
      title: (
        <>
          <Checkbox
            disabled={!!apiId || dataSource.length !== allSouceLength}
            className='mr-1'
            checked={allSouceLength > 0 && sortColumns.length == allSouceLength}
            onChange={({ target: { checked } }) => {
              setPageInfo({ isDirty: true });
              if (checked) {
                onChange(
                  'setSortColumns',
                  dataSource?.map(item => {
                    return {
                      name: item.mappingField ?? '',
                      mappingField: item.mappingField ?? '',
                      order: 'DESC',
                    };
                  }) as sortColumn[],
                );
              } else {
                onChange('setSortColumns', []);
              }
            }}
          />
          <span>设为排序参数</span>
        </>
      ),
      render: (_, record) => (
        <Checkbox
          checked={sortColumns.some(item => item.name === record.mappingField)}
          disabled={!!apiId}
          onChange={({ target: { checked } }) => {
            if (checked) {
              onChange('setSortColumns', [
                ...sortColumns,
                {
                  name: record.mappingField,
                  mappingField: record.mappingField,
                  order: 'DESC',
                },
              ]);
            } else {
              onChange(
                'setSortColumns',
                sortColumns.filter(item => item.name !== record.mappingField),
              );
            }
          }}
        />
      ),
    },
    {
      title: '操作',
      render: (_, record: RequestColumn) => (
        <>
          {record.idErrPamars && !apiId && (
            <Button
              type='link'
              className='p-0 text-[#FF4D4F]'
              onClick={() => {
                setPageInfo({ isDirty: true });
                onChange(
                  'setDataSource',
                  dataSource.filter(item => item.mappingField !== record.mappingField),
                );
                onChange(
                  'setRequestKey',
                  requestKey.filter(item => item !== record.mappingField),
                );
                onChange(
                  'setResponseKey',
                  responseKey.filter(item => item !== record.mappingField),
                );
                onChange(
                  'setSortColumns',
                  sortColumns.filter(item => item.mappingField !== record.mappingField),
                );
              }}
            >
              移除
            </Button>
          )}
        </>
      ),
    },
  ];

  return (
    <>
      <div className='flex justify-between px-4 py-2'>
        <div className='flex'>
          <div className='flex items-center'>
            <div>请求参数：</div>
            <div>{showStatistics(requestKey, 'req')}</div>
          </div>
          <div className='flex ml-[34px] items-center'>
            <div>返回参数：</div>
            <div>{showStatistics(responseKey, 'res')}</div>
          </div>
        </div>
        {(apiId || editId) && (
          <div>
            <Checkbox
              checked={onlyShowErr}
              onChange={({ target: { checked } }) => {
                if (checked) {
                  setActiveKey('all');
                }
                setOnlyErrShow(checked);
              }}
            >
              {`仅显示无效字段（${allErr.length}）`}
            </Checkbox>
          </div>
        )}
      </div>

      <div>
        <CustomTable
          columns={columns}
          dataSource={dataSource}
          rowKey='name'
          rowClassName={(record: RequestColumn) => (record.idErrPamars ? 'text-[#FF4D4F]' : '')}
          onChange={handleTableChange}
          pagination={pagination}
        />
      </div>
    </>
  );
};
