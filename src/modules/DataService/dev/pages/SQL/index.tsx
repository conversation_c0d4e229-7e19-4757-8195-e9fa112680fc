import { useEffect, useRef, useState } from 'react';
import { ReactCodeMirrorRef } from '@uiw/react-codemirror';
import { Allotment, LayoutPriority } from 'allotment';
import { Alert, Button, Form, Input, message, Radio, Select, Spin, Tooltip } from 'antd';
import { format } from 'sql-formatter';
import { Link, useParams, useSearchParams } from 'umi';

import { CodeMirror, KeepAliveToTab, SectionCaptain, useKeepAliveTabs } from '@/components';
// import { PROJECT } from '@/constants';
import { DataTag, HttpToolbar } from '@/modules/DataService/dev/components';
import { showIcon } from '@/modules/DataService/dev/stores/useDataServiceStore';
import {
  ApiEntity,
  ApiRequestTypeEnum,
  BASE_SERVICE_URL,
  PasteSourceTable,
  SourceTableIcon,
  SourceTableIconColor,
} from '@/modules/DataService/models';
import { DataService, DataSourceApi } from '@/services';

import './index.less';

import { PreviewDsButton } from '../../components/PreviewDsModal/PreviewDsButton';

import { DataBaseTree, SqlDetail } from './components';

const formLayout = {
  labelCol: { style: { width: '200px' } },
  wrapperCol: { xxl: 12, xl: 12, lg: 24, xs: 24, sm: 24 },
};

const RequestTypeOptions: Array<Record<'value' | 'label', string>> = Object.entries(ApiRequestTypeEnum).map(
  ([value, label]) => ({ value, label }),
);

export const Page = () => {
  const { setPageInfo, closeCurrentTab } = useKeepAliveTabs();
  const editorRef = useRef<ReactCodeMirrorRef>();
  const [form] = Form.useForm();
  const [createApiLoading, setCreateApiLoading] = useState<boolean>(false);
  const [spinning, setSpinning] = useState<boolean>(false);
  const [sourceTable, setSourceTable] = useState<PasteSourceTable[]>([]);
  const [databaseTableTree, setDatabaseTableTree] = useState<DefaultOptionType[]>([]);
  const { apiId, editId } = useParams();
  const [searchParams] = useSearchParams();
  const backApiId = searchParams.get('backApiId');

  const [sql, setSql] = useState<string>('');
  const [dsId, setDsId] = useState<string>('');
  const [dsType, setDsType] = useState<string>('');

  const [detail, setDetail] = useState<ApiEntity>();

  useEffect(() => {
    if (detail?.name) {
      setPageInfo({
        title: (
          <span>
            <i className={`iconfont ${showIcon('SQL', 'icon')} mr-1`} style={{ color: showIcon('SQL', 'color') }} />
            {detail.name}
          </span>
        ),
      });
      form.setFieldsValue(detail);
      changeSourceTable(detail?.dsId);
      if (detail?.setting?.script) {
        setSql(detail?.setting?.script ?? '');
      }
    }
  }, [detail, apiId, editId]);

  const fetchSourceTable = async () => {
    const res = await DataSourceApi.getListByPlatform(
      'MYSQL,HIVE,CLICKHOUSE,DB2,SQLSERVER,ORACLE,POSTGRESQL,SYBASE,DAMENG');

    if (res?.code == '0000') {
      setSourceTable(res?.data ?? []);
    }
  };

  useEffect(() => {
    if (apiId || editId) {
      fetchDetails();
    }
  }, [apiId, editId]);

  const fetchDetails = async () => {
    try {
      setSpinning(true);
      let res = {} as any;

      if (backApiId) {
        res = await DataService.apiHistoryService.getHistoryDetail(backApiId);
      } else {
        res = await DataService.apiService.getDetail(apiId ?? editId ?? '');
      }

      if (res?.code === '0000') {
        setDetail(res?.data ?? {});
      }
      setSpinning(false);
    } catch (error) {
      setSpinning(false);
    }
  };

  useEffect(() => {
    fetchSourceTable();
  }, []);

  const changeSourceTable = async (value, option?) => {
    setDsType(option ? option.platform : detail?.dsType);
    setDsId(value);
    try {
      const res = await DataService.apiMetadataService.getDatabaseTableTree(value);
      if (res?.code === '0000') {
        setDatabaseTableTree(
          res?.data?.map(item => {
            item.title = item.database;
            item.icon = <i className='iconfont icon-data-line'></i>;
            item.isLeaf = false;
            item.key = (Math.random() * 100000).toString();
            item.children = item.modelTableList.map(child => {
              child.title = child.tbAlias ? `${child.tableName}(${child.tbAlias})` : child.tableName;
              child.database = item.database;
              child.key = (Math.random() * 100000).toString();
              child.isLeaf = false;
              child.icon = <i className='iconfont icon-view_table-line text-primary text-sm'></i>;
              return child;
            });

            return item;
          }) ?? [],
        );
      }
    } catch (e) {
      setDatabaseTableTree([]);
    }
  };

  // 粘贴生成的sql到编辑器中
  const pasteSql = (sql: string) => {
    const view = editorRef?.current?.view;
    const state = view?.state;
    const range = state?.selection?.ranges[0];
    view?.dispatch({
      changes: {
        from: range?.from ?? 0,
        to: range?.to ?? 0,
        insert: sql,
      },
      selection: { anchor: range?.from ?? 0 + sql.length },
    });
    setPageInfo({ isDirty: true });
  };

  const handleCreateApiPath = async () => {
    try {
      setCreateApiLoading(true);
      const res = await DataService.apiService.getApiPath();
      if (res?.code === '0000') {
        form.setFieldValue('apiPath', res?.data ?? '');
        form.validateFields(['apiPath']);
      }
      setCreateApiLoading(false);
    } catch (error) {
      setCreateApiLoading(false);
    }
  };

  const formatCode = () => {
    try {
      const newSql = format(sql);
      setSql(newSql);
    } catch (e: any) {
      message.warning(e?.message.substring(0, 100) ?? '格式化失败');
    }
  };

  const transformSql = (sql: string) => {
    const needTransformDatabase = ['DB2', 'ORACLE', 'SYBASE', 'SQLSERVER', 'POSTGRESQL'];
    if (needTransformDatabase.includes(dsType)) {
      return sql.replace(/`/g, '');
    }
    return sql;
  };

  return (
    <div className='sql h-full w-full overflow-hidden'>
      <Spin spinning={spinning} wrapperClassName='h-full'>
        <div className='flex flex-col w-full h-full'>
          <HttpToolbar
            closeCurrentTab={closeCurrentTab}
            form={form}
            dsType={dsType}
            detail={detail}
            fetchDetails={fetchDetails}
            isDetail={!!apiId}
            dataSourceId={dsId}
            apiSql={sql}
          />
          <div className='overflow-auto flex flex-col'>
            <SectionCaptain title='基本信息' className='mt-2 ml-4' />
            {apiId ? (
              <SqlDetail detail={detail} />
            ) : (
              <Form form={form} {...formLayout} onValuesChange={() => setPageInfo({ isDirty: true })}>
                <Form.Item label='API名称' name='name' rules={[{ required: true }]}>
                  <Input placeholder='请输入' allowClear />
                </Form.Item>

                <Form.Item label='API路径' required className='apiPathBox'>
                  <Form.Item
                    name='apiPath'
                    rules={[
                      { required: true },
                      {
                        pattern: /^[a-zA-Z0-9_]+$/,
                        message: '只能由数字、字母、_ 组成',
                      },
                    ]}
                  >
                    <Input addonBefore={BASE_SERVICE_URL} placeholder='请输入' allowClear />
                  </Form.Item>
                  <Button type='link' loading={createApiLoading} onClick={handleCreateApiPath} className='createApi'>
                    自动生成
                  </Button>
                </Form.Item>

                <Form.Item label='请求方式' name='requestType' rules={[{ required: true }]}>
                  <Radio.Group options={RequestTypeOptions} />
                </Form.Item>
                <DataTag className='tag-form' />
                <Form.Item label='描述' name='description'>
                  <Input.TextArea placeholder='请输入' allowClear />
                </Form.Item>
              </Form>
            )}
            <SectionCaptain title='SQL语句' className='mt-4 ml-4' />
            <Alert
              className='mx-4'
              message='为确保数据服务详情的参数说明与实际调用结果一致，请根据SQL脚本，在上方操作栏的「参数设置」中，手动添加所有参数'
              type='warning'
              showIcon
            />
            <div className='flex-1 w-full mt-2' style={{ borderTop: '1px solid #f0f0f0', minHeight: 800 }}>
              <Allotment proportionalLayout={false}>
                <Allotment.Pane preferredSize='320px' minSize={272}>
                  <div className='flex flex-col overflow-hidden h-full'>
                    <div
                      className='h-11 bg-[#fafafa] px-3 py-[10px] flex shrink-0'
                      style={{ borderBottom: '1px solid #f0f0f0' }}
                    >
                      <div className='label w-[56px] flex-shrink-0'>
                        <span className='text-[red] mr-1'>*</span>
                        数据源
                      </div>
                      <div className='pl-2 flex flex-1'>
                        <Select
                          value={dsId}
                          size='small'
                          className='flex-1 w-0'
                          disabled={!!apiId}
                          onChange={changeSourceTable}
                          showSearch
                          optionFilterProp='label'
                          placeholder='请选择'
                        >
                          {sourceTable?.map(item => (
                            <Select.Option key={item.id} value={item.id} label={item.name} platform={item.platform}>
                              <i
                                className={`iconfont mr-1 ${SourceTableIcon[item.platform]} text-[${
                                  SourceTableIconColor[item.platform]
                                }]`}
                              />
                              <span>{item.name}</span>
                            </Select.Option>
                          ))}
                        </Select>
                        <div
                          // eslint-disable-next-line max-len
                          className='w-6 h-6 flex justify-center items-center'
                          style={{ border: '1px solid #d9d9d9', marginLeft: '-1px' }}
                        >
                          <PreviewDsButton
                            dataSource={sourceTable.find(x => x.id === dsId) as DATA_SOURCE.DataSourceEntity}
                          />
                        </div>
                      </div>
                    </div>
                    <DataBaseTree
                      dsId={dsId}
                      pasteSql={pasteSql}
                      databaseTableTree={databaseTableTree}
                      setDatabaseTableTree={setDatabaseTableTree}
                    />
                  </div>
                </Allotment.Pane>
                <Allotment.Pane priority={LayoutPriority.High}>
                  <div
                    className='h-11 bg-[#fafafa] px-3 py-[10px] label flex items-center justify-between'
                    style={{ borderBottom: '1px solid #f0f0f0' }}
                  >
                    <span>SQL查询语句</span>
                    <span>
                      <Tooltip title='格式化SQL' placement='bottom'>
                        <Button type='text' onClick={() => formatCode()}>
                          <i className='iconfont icon-format-line1 text-sm text-primary'></i>
                        </Button>
                      </Tooltip>
                      <Link
                        to={`/job/doc?docUrl=${encodeURIComponent(
                          '/api/v2/doc/api/helper/doc_data_service_sql',
                        )}&display=说明文档&jobName=SQL书写说明文档`}
                        target='_blank'
                        className='ml-2'
                      >
                        <Tooltip title='SQL书写说明文档'>
                          <i className='iconfont icon-help-line'></i>
                        </Tooltip>
                      </Link>
                    </span>
                  </div>
                  <div className='w-full overflow-auto' style={{ height: 'calc(100% - 44px)' }}>
                    <CodeMirror
                      className='h-full editor_border'
                      value={sql}
                      lang='sql'
                      ref={editorRef}
                      readOnly={!!apiId}
                      onChange={(value: string) => {
                        setSql(transformSql(value));
                        setPageInfo({ isDirty: true });
                      }}
                      // extensions={[color, loadLanguage('sql')!]}
                    />
                  </div>
                </Allotment.Pane>
              </Allotment>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  );
};

export const SQL = () => {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
};

export default SQL;
