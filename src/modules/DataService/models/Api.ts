export const BASE_SERVICE_URL = '/api/open/data-service/';

// api消费模式
export enum ApiConsumeModeEnum {
  'HTTP' = 'http请求',
  'KAFKA' = 'kafka消费',
}

export const ConsumeModeOptions: Array<Record<'value' | 'label', string>> = Object.entries(ApiConsumeModeEnum).map(
  ([value, label]) => ({ value, label }),
);

// 请求方式
export enum ApiRequestTypeEnum {
  'GET' = 'get请求',
  'POST' = 'post请求',
}

// 脱敏措施
export enum DataMaskingEnum {
  'BASE' = '基础脱敏',
  'EMAIL' = '电子邮件',
  'PHONE' = '手机号码',
  'TELEPHONE' = '国内电话',
  'IDENTITY_CARD' = '身份证',
  'IP_ADDR' = 'IP地址',
  'BANK_CARD' = '银行账号',
  'POSTAL_CODE' = '邮政编码',
  'CUSTOM' = '自定义',
  'REGEX' = '正则匹配脱敏',
}

export const DataMaskStrategyDefaultConfig: Record<
  keyof typeof DataMaskingEnum,
  {
    type: 'text' | 'range' | 'regex';
    help?: string;
    frontNum?: number;
    maskLetter?: string;
    backNum?: number;
    expression?: string[];
  }
> = {
  BASE: {
    type: 'text',
    help: '字符串替换为4个*，数字替换成0',
  },
  EMAIL: {
    type: 'text',
    help: '保留首字母及根域名，其他替换成4个*',
  },
  PHONE: {
    type: 'range',
    frontNum: 3,
    maskLetter: '*',
    backNum: 3,
  },
  TELEPHONE: {
    type: 'range',
    frontNum: 5,
    maskLetter: '*',
    backNum: 3,
  },
  IDENTITY_CARD: {
    type: 'range',
    frontNum: 4,
    maskLetter: '*',
    backNum: 3,
  },
  IP_ADDR: {
    type: 'range',
    frontNum: 2,
    maskLetter: '*',
    backNum: 2,
  },
  BANK_CARD: {
    type: 'range',
    frontNum: 4,
    maskLetter: '*',
    backNum: 4,
  },
  POSTAL_CODE: {
    type: 'range',
    frontNum: 2,
    maskLetter: '*',
    backNum: 2,
  },
  CUSTOM: {
    type: 'range',
    frontNum: 2,
    maskLetter: '*',
    backNum: 2,
  },
  REGEX: {
    type: 'regex',
    maskLetter: '*',
    expression: [],
    help: '将正则匹配的内容替换为*',
  },
};

// 排序
export enum OrderEnum {
  'ASC' = '升序',
  'DESC' = '降序 ',
}

// api模式
export enum ApiModeEnum {
  'KAFKA' = 'api模式',
  'STANDARD' = '标准模式',
  'SQL' = 'SQL模式',
  'UQ' = 'uq模式',
  'CUSTOM' = '自定义模式',
}

export const ApiModeOptions: Array<Record<'value' | 'label', string>> = Object.entries(ApiModeEnum).map(
  ([value, label]) => ({ value, label }),
);

export enum ApiStatusEnum {
  'ONLINE' = '已上线',
  'OFFLINE' = '已下线',
  'DRAFT' = '草稿',
}

export const ApiStatusOptions: Array<Record<'value' | 'label', string>> = Object.entries(ApiStatusEnum).map(
  ([value, label]) => ({ value, label }),
);

export enum ApiStateIconEnum {
  'DRAFT' = 'icon-draft-line',
  'OFFLINE' = 'icon-offline-line1',
  'ONLINE' = 'icon-success-line',
}

export enum SourceTableIcon {
  'CLICKHOUSE' = 'icon-ck',
  'HIVE' = 'icon-hive-line',
  'MYSQL' = 'icon-mysql',
  'KAFKA' = 'icon-kafka',
  'ELASTICSEARCH' = 'icon-es',
  'REDIS' = 'icon-redis-line',
  'VICTORIA_METRICS' = 'icon-vic-line',
  'NEBULA' = 'icon-nebulagraph-line',
  'DB2' = 'icon-DB2',
  'SQLSERVER' = 'icon-SqlServer',
  'ORACLE' = 'icon-ORACLE',
  'POSTGRESQL' = 'icon-PostgreSQL',
  'SYBASE' = 'icon-sybase',
  'DAMENG' = 'icon-DAMENG'
}

export enum SourceTableIconColor {
  'CLICKHOUSE' = '#23C259',
  'HIVE' = '#F25605',
  'MYSQL' = '#FFB232',
  'KAFKA' = '#096DD9',
  'ELASTICSEARCH' = '#597EF7',
  'REDIS' = '#FF7A45',
  'VICTORIA_METRICS' = '#9254DE',
  'NEBULA' = '#40A9FF',
  'DB2' = '#ED6BAE',
  'SQLSERVER' = '#13C2C2',
  'ORACLE' = '#D4B106',
  'POSTGRESQL' = '#A0D911',
  'SYBASE' = '#389E0D',
  'DAMENG' = '#4f4fe4'
}

export const RequestTypeOptions: Array<Record<'value' | 'label', string>> = Object.entries(ApiRequestTypeEnum).map(
  ([value, label]) => ({ value, label }),
);

export enum DsColumnTypeEnum {
  INT = '整型',
  LONG = '长整型',
  DOUBLE = '浮点类型',
  STRING = '字符串',
  BOOLEAN = '布尔',
  LIST_INT = '数字列表',
  LIST_LONG = '长整型列表',
  LIST_DOUBLE = '浮点类型列表',
  LIST_STRING = '字符串列表',
  LIST_BOOLEAN = '布尔类型列表',
  LIST_OBJECT = '对象列表',
  OBJECT = '对象',
}

export enum DsRespColumnTypeEnum {
  INT = 'INT',
  LONG = 'LONG',
  DOUBLE = 'DOUBLE',
  STRING = '字符串',
  BOOLEAN = '布尔',
  LIST = '列表',
  OBJECT = '对象',
}

export const DsColumnTypeEnumOptions: Array<Record<'value' | 'label', string>> = Object.entries(DsColumnTypeEnum).map(
  ([value, label]) => ({ value, label }),
);

export enum DsOperationEnum {
  '=' = '等于',
  'like' = '模糊匹配包含',
  'not like' = '模糊匹配不包含',
  'in' = '列表包含',
  'not in' = '列表不包含',
  '!=' = '不等于',
  '>' = '大于',
  '<' = '小于',
  '>=' = '大于等于',
  '<=' = '小于等于',
  'between' = 'between',
  'not between' = 'not between',
}

export const DsOperationEnumOptions: Array<Record<'value' | 'label', string>> = Object.entries(DsOperationEnum).map(
  ([value, label]) => ({ value, label }),
);

export enum DsOperationCodeEnum {
  EQUALS = '=',
  LIKE = 'like',
  NOT_LIKE = 'not like',
  IN = 'in',
  NOT_IN = 'not in',
  NOT_EQUALS = '!=',
  GT = '>',
  LT = '<',
  GE = '>=',
  LE = '<=',
}

export enum PositionEnum {
  QUERY = 'QUERY',
  BODY = 'BODY',
}

/**
 * Api业务实体
 * @interface ApiEntity
 * @property { name } 接口名
 * @property { apiGroupId } api分组Id
 * @property { consumeMode } 消费模式 HTTP: http请求， KAFKA: kafka消费
 * @property { apiPath } api路径
 * @property { requestType } 请求方式 GET: get请求，POST: post请求
 * @property { apiMode } api模式， KAFKA: api模式，STANDARD：标准模式，SQL：基础模式 UQ：uq模式，CUSTOM：自定义模式
 * @property { dsId } 数据源id
 * @property { topic } kafka主题
 * @property { databaseName } 数据库
 * @property { dataTable } 数据表
 * @property { status } 状态 ONLINE: on-line上线，OFFLINE: off-line下线
 * @property { setting } 配置信息，api不同模式有不同的配置
 * @property { handleClass } 自定义处理的全限定类名
 * @property { tags } 标签
 * @property { description } 描述信息
 */
export interface ApiEntity {
  id: string;
  clientApiId: string;
  name: string;
  apiGroupId: string;
  apiId: string; // TODO:版本回退查询api原始id
  consumeMode: keyof typeof ApiConsumeModeEnum;
  apiPath: string;
  requestType: keyof typeof ApiRequestTypeEnum;
  apiMode: keyof typeof ApiModeEnum;
  dsType: string;
  dsId: string;
  dsName: string;
  topic: string;
  databaseName: string;
  dataTable: string;
  version: number;
  status: keyof typeof ApiStatusEnum;
  setting: ApiSetting;
  extraSetting?: ApiExtraSetting;
  handleClass: string;
  description?: string;
  tags?: string[];
  isPublished: number;
  publishedId: string;
  apiRateLimitPeriod?: number | null;
  projectAuth: ProjectAuthModel;
  clientApiRateLimitPeriod?: number | null;
}

export interface RequestColumn {
  id: string;
  name: string;
  mappingField: string;
  type: keyof typeof DsColumnTypeEnum;
  position: 'QUERY' | 'BODY';
  required: boolean;
  operation?: keyof typeof DsOperationEnum;
  sampleValue: string;
  idErrPamars?: boolean | undefined;
  defaultValue: string;
  desc?: string;
}

export interface ResponseColumn {
  id: string;
  name: string;
  mappingField: string;
  sampleValue: string;
  defaultValue?: string;
  type: keyof typeof DsColumnTypeEnum;
  desc?: string;
  idErrPamars?: boolean | undefined;
  masking: MaskStrategyEntity;
}

export type activeKeyType = 'responseParams' | 'requestParams' | 'serviceParams';

export interface ServiceSettingsColumn {
  id?: number;
  name: string;
  value: string;
  type: keyof typeof DsColumnTypeEnum;
  desc?: string;
}

export interface MaskStrategyEntity {
  type: keyof typeof DataMaskingEnum;
  rule: MaskRuleType | undefined | MaskRegexRule;
}

export interface MaskRuleType {
  frontNum: number;
  maskLetter: string;
  backNum: number;
}

export interface DsSetting {
  name: string;
  type: 'HIVE' | 'MYSQL' | 'ELASTICSEARCH' | 'KAFKA' | 'CLICKHOUSE';
  display: string;
  description: string;
}

export interface DataSources {
  name: string;
  type: 'HIVE' | 'MYSQL' | 'ELASTICSEARCH' | 'KAFKA' | 'CLICKHOUSE';
  description: string;
  display: string;
  dsId?: string;
}

export interface MaskRegexRule {
  maskLetter: '*';
  expression: string[];
}

export interface PageColumn {
  name: 'pageNum' | 'pageSize';
  type: keyof typeof DsColumnTypeEnum;
  required: boolean;
  operation?: keyof typeof DsOperationCodeEnum;
  position?: 'QUERY' | 'BODY';
  sampleValue: string;
  defaultValue: string;
  desc: string;
}

export interface sortColumn {
  name: string;
  mappingField: string;
  order: 'ASC' | 'DESC';
}

/**
 * 标准模式下api配置
 * @interface StandardSetting
 * @property { enablePaged } 是否开启分页
 * @property { prevScript } 前置处理脚本
 * @property { postScript } 后置处理脚本
 * @property { script } 查询SQL语句 sql模式下必填
 * @property { customFilter } 自定义过滤 标准模式下字段，sql模式下无
 * @property { requestColumns } 请求参数列表
 * @property { responseColumns } 返回列表
 */
export interface ApiSetting {
  enablePaged: boolean;
  prevScript?: string;
  postScript?: string;
  script?: string;
  customFilter?: string;
  requestColumns: RequestColumn[];
  responseColumns: ResponseColumn[];
  serviceSettings: ServiceSettingsColumn[];
  pageColumns: PageColumn[];
  sortColumns: sortColumn[];
  datasources?: DataSources[];
  accessJaxMeta?: boolean;
}

export type ApiExtraSetting = Record<string, ExtraSettingItem>;

export interface ExtraSettingItem {
  enable: boolean;
  unit?: string;
  period: number;
}

export interface PasteSourceTable {
  id: string;
  name: string;
  code: string;
  platform: 'MYSQL' | 'HIVE' | 'CLICKHOUSE';
}

export interface ModelTableList {
  comment: string;
  deployId: string;
  tableName: string;
  tbAlias: string;
  tbId: string;
  tbName: string;
}

export interface DatabaseTable {
  database: string;
  modelTableList: ModelTableList[];
}

export interface MetadataTable {
  columnName: string;
  type: keyof typeof DsColumnTypeEnum;
  comment: string;
}
