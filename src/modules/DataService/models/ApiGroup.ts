/**
 * api分组实体
 * @interface ApiGroupEntity
 * @property { name } 接口名
 * @property { parentId } 上级节点id, 通常填写0
 * @property { description } 描述信息
 */
export interface ApiGroupEntity {
  id: string;
  name: string;
  parentId: number;
  description?: string;
}

export interface ApiGroupTreeEntity {
  children: ApiGroupTreeEntity[];
  title: string;
  key: string;
  id: string;
  name: string;
  parentId: string;
  description?: string;
  type: 'GROUP' | 'KAFKA' | 'SQL' | 'CUSTOM' | 'STANDARD';
  parentPath?: string;
  child: ApiGroupTreeEntity[];
  selectable?: boolean;
  projectAuth: ProjectAuthModel;
}
