import { createElement } from 'react';
import { TableProps, Tag } from 'antd';

type RecordType = DATA_INGESTION.CellAgent;
type ColumnType = NonNullable<TableProps<RecordType>['columns']>[number];

export const CollectionStatus = {
  0: '未连接',
  1: '健康',
  2: '离线',
};

export const CollectionStatusMap = {
  0: {
    label: '未连接',
  },
  1: {
    label: '健康',
    color: 'success',
  },
  2: {
    label: '离线',
    color: 'danger',
  },
};

export const ADD_CELL_AGENT_COLUMNS: ColumnType[] = [
  {
    title: '主机名',
    dataIndex: 'hostname',
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
  },
  {
    title: '状态',
    dataIndex: 'connection',
    render: val => {
      return (<span className={`text-${CollectionStatusMap?.[val]?.color}`}>{CollectionStatusMap?.[val]?.label}</span>);
    },
  },
  {
    title: '采集器标签',
    render: (_, { groupList }) =>
      createElement(
        'div',
        {
          className: 'flex items-center flex-wrap',
        },
        groupList?.map(({ name, id }) => createElement(Tag, { key: id }, name)),
      ),
  },
  {
    title: '版本',
    dataIndex: 'version',
  },
  {
    title: 'OS类型',
    dataIndex: 'os',
  },
];

export const PREVIEW_AGENT_FILE_COLUMNS = [
  {
    title: 'No',
    dataIndex: 'no',
    width: 80,
  },
  {
    title: '源No',
    dataIndex: 'rn',
    width: 80,
  },
  {
    title: '内容',
    dataIndex: 'c',
    width: 500,
    ellipsis: false,
  },
];
