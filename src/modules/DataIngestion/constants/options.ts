import { createElement } from 'react';

import { Icon } from '@/components';
import { buildTrueFalseOptions, buildZeroOneOptions } from '@/utils';

export const CHARSET_OPTIONS = [
  {
    label: '自动推荐',
    value: 'RECOMMEND',
  },
  {
    label: '逐行自动识别',
    value: 'AUTO',
  },
  {
    label: 'UTF-8',
    value: 'UTF-8',
  },
  {
    label: 'GBK',
    value: 'GBK',
  },
  {
    label: 'ASCII',
    value: 'ASCII',
  },
  {
    label: 'GB18030',
    value: 'GB18030',
  },
  {
    label: 'UTF-16LE',
    value: 'UTF-16LE',
  },
];

export const CODING_OPTIONS = [
  {
    label: 'BSD',
    value: 'BSD',
  },
  {
    label: 'IETF',
    value: 'IETF',
  },
];

export const OS_OPTIONS = [
  {
    label: 'Linux',
    value: 'Linux',
  },
  {
    label: 'Windows',
    value: 'Windows',
  },
  {
    label: 'AIX',
    value: 'AIX',
  },
  {
    label: 'HPUX',
    value: 'HPUX',
  },
];

export const FILEFILTER_OPTIONS = [
  {
    label: '文件黑名单',
    value: 'file_blacklist',
  },
  {
    label: '文件白名单',
    value: 'file_whitelist',
  },
];

export const CONTENTFILTER_OPTIONS = [
  {
    label: '排除内容',
    value: 'excludes',
  },
  {
    label: '仅包含内容',
    value: 'includes',
  },
];

export const TIME_OPTIONS = [
  {
    label: '天',
    value: 'day',
  },
  {
    label: '小时',
    value: 'hour',
  },
];

export const SELECTED_COLUMN = [
  {
    dataIndex: 'icon',
    render: () => {
      return createElement(Icon, {
        name: 'charts-line1',
        className: 'text-primary-1',
      });
    },
  },
  {
    dataIndex: 'tbAlias',
    ellipsis: true,
  },
  {
    dataIndex: 'tbName',
    ellipsis: true,
  },
];

export const OPEN_CLOSE_OPTIONS = buildTrueFalseOptions('打开', '关闭');
export const OPEN_CLOSE_NUMBER_OPTIONS = buildZeroOneOptions('打开', '关闭');
export const EXPIRED_TIME_OPTIONS = buildTrueFalseOptions('过期时间', '不过期');
export const MAX_ADD_FILE_OPTIONS = buildTrueFalseOptions('限制', '不限制');

export const OPEN_FILE_SORT_OPTIONS = [
  {
    label: '无',
    value: '',
  },
  {
    label: '文件最后更新时间',
    value: 'mtime',
  },
];

export const ACTION_WHEN_EOF_OPTIONS = [
  {
    label: '不处理',
    value: 'NONE',
  },
  {
    label: '重命名',
    value: 'RENAME',
  },
  {
    label: '删除',
    value: 'DELETE',
  },
];

export const PROTOCOL_OPTIONS = [
  {
    label: 'TCP',
    value: 'TCP',
  },
  {
    label: 'UDP',
    value: 'UDP',
  },
];

export const FIELD_DELIMITER_OPTIONS = [
  {
    label: '按包分割',
    value: 'package',
  },
  {
    label: '按行分割',
    value: 'linebase',
  },
  {
    label: '多行合并',
    value: 'multiline',
  },
];

export const CONFIG_FIELDS_FILE = [
  'ignoreOlderThan',
  'incremental',
  'renamecheck',
  'flushPartial',
  'pollInterval',
  'dircheckInterval',
  'backoff',
  'actionWhenEof',
  'workTimeInterval',
  'flushCacheInterval',
  'hashEnable',
  'hashDataLength',
  'hashWaitData',
  'openfiles',
  'msgMaxLength',
  'batch',
  'openFileSort',
  'ignoreSymlink',
  'bandwidthweight',
  'fileExpiredEnable',
  'waitOnEof',
];

export const CONFIG_FIELDS_ARCHIVE = ['openFileSort', 'msgMaxLength'];

export const CONFIG_FIELDS_TCP_UDP = ['bandwidthweight'];

export const CONFIG_FIELDS_KAFKA = [''];
export const CONFIG_FIELDS_JDBC = [''];
export const CONFIG_FIELDS_LOGSTASH = [''];
export const CONFIG_FIELDS_SYSLOG = ['bandwidthweight'];

export const CONTENT_OPTIONS = [
  {
    label: '@sourceip',
    value: '@sourceip',
  },
  {
    label: 'raw_event',
    value: 'raw_event',
  },
  {
    label: 'Hostname',
    value: 'Hostname',
  },
  {
    label: 'SourceName',
    value: 'SourceName',
  },
  {
    label: 'Message',
    value: 'Message',
  },
  {
    label: 'Severity',
    value: 'Severity',
  },
  {
    label: 'SyslogSeverity',
    value: 'SyslogSeverity',
  },
  {
    label: 'SyslogFacility',
    value: 'SyslogFacility',
  },
];

export const DATABASE_TYPE_OPTIONS = [
  { value: 'MYSQL', label: 'MySQL' },
  { value: 'DB2', label: 'IBM DB2' },
  { value: 'SQLSERVER', label: 'SQL Server' },
  { value: 'ORACLE', label: 'Oracle' },
  { value: 'SYBASE', label: 'Sybase' },
  { value: 'POSTGRESQL', label: 'PostgreSQL' },
  { value: 'CUSTOM', label: '自定义' },
];

export const DATABASE_CHARSET_OPTIONS = [
  { value: 'utf-8', label: 'UTF-8' },
  { value: 'gbk', label: 'GBK' },
  { value: 'ascii', label: 'ASCII' },
  { value: 'gb18030', label: 'GB18030' },
  { value: 'utf-16le', label: 'UTF-16LE' },
];

export const COLLECT_TYPE_OPTIONS = [
  { value: 'whole', label: '全量模式' },
  { value: 'incremental', label: '增量模式' },
];
export const TRACKING_COLUMN_TYPE = [
  { value: 'timestamp', label: '时间戳' },
  { value: 'numeric', label: '数值类型' },
];
