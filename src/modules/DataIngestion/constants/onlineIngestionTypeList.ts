import { PROJECT } from '@/constants';

export const INGESTION_TYPE_LIST_ONLINE = [
  {
    title: '文件同步',
    key: 'file',
    icon: `${PROJECT.baseUrl}/images/3d-image/file-available.png`,
    rightsCode: 'file',
  },
  {
    title: '归档文件同步',
    key: 'archive',
    icon: `${PROJECT.baseUrl}/images/3d-image/archive-available.png`,
    rightsCode: 'archive',
  },
  {
    title: 'windows事件日志同步',
    key: 'mseventlog',
    icon: `${PROJECT.baseUrl}/images/3d-image/windows-disable.png`,
    rightsCode: 'win_event_log',
  },
  {
    title: 'TCP/UDP同步',
    key: 'tcp-udp',
    icon: `${PROJECT.baseUrl}/images/3d-image/tcp-available.png`,
    rightsCode: 'tcp_udp',
  },
  {
    title: '数据库同步',
    key: 'jdbc',
    icon: `${PROJECT.baseUrl}/images/3d-image/data-available.png`,
    rightsCode: 'jdbc',
  },
  {
    title: 'Kafka接入',
    key: 'kafka',
    icon: `${PROJECT.baseUrl}/images/3d-image/Kafka-available.png`,
    rightsCode: 'kafka',
  },
  {
    title: 'Syslog同步',
    key: 'syslog',
    icon: `${PROJECT.baseUrl}/images/3d-image/syslog-available.png`,
    rightsCode: 'syslog',
  },
  {
    title: '自定义同步',
    key: 'logstash',
    icon: `${PROJECT.baseUrl}/images/3d-image/logstash-available.png`,
    rightsCode: 'logstash',
  },
];

export default INGESTION_TYPE_LIST_ONLINE;
