/**
 * @module 数据集成-数据接入-文件预览模态框
 */
import { useState } from 'react';
import { Form, FormInstance, Modal } from 'antd';

import type { GetFileContent, GetFileList } from '@/modules/DataIngestion/services';

import './style.less';

import { SearchForm } from './SearchForm';
import { Table } from './Table';

interface Props {
  agentSetting: DATA_INGESTION.AgentSettingListItem<any>; // TODO: type
  onClose: () => void;
  globalForm: FormInstance;
  cell?: string;
}

export const FilePreviewModal = ({ agentSetting, onClose, globalForm, cell }: Props) => {
  const [form] = Form.useForm();
  const [from, setFrom] = useState<number>(1);
  const path = Form.useWatch('path', form);
  const cellId = cell || Form.useWatch('cellId', globalForm);
  const {
    agentId,
    setting: { input },
  } = agentSetting;

  const getFileParams: GetFileList = {
    cellId,
    agentId,
    path: input.path,
    ignoreOlderThan: input.ignoreOlderThan,
    recursive: input.recursive,
    blacklist: input.blacklist,
    whitelist: input.whitelist,
    fileNumber: 100,
  };

  const filter: GetFileContent = {
    size: 100,
    from,
    cellId,
    agentId,
    path,
    charset: input.charset,
    multiline: input.multiline,
    include: input.include,
    exclude: input.exclude,
  };

  return (
    <Modal open className='w-auto max-w-[70%]' title='文件预览' onCancel={onClose} footer={null}>
      <SearchForm form={form} onSearch={setFrom} getFileParams={getFileParams} />
      {cellId && path && <Table filter={filter} />}
    </Modal>
  );
};
