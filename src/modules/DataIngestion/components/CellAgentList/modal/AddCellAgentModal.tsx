/**
 * @module 数据集成-数据接入-添加采集器模态框
 */
import { useEffect, useMemo, useRef, useState } from 'react';
import { FormInstance, Modal, Radio, Select } from 'antd';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { SortType } from '@/constants';
import { useTableOnRow } from '@/hooks';
import { ADD_CELL_AGENT_COLUMNS, OS_OPTIONS } from '@/modules/DataIngestion/constants';
import { useIngestionType } from '@/modules/DataIngestion/hooks';
import { AgentSettingListItem } from '@/modules/DataIngestion/models';
import { cellListService } from '@/modules/DataIngestion/services';
import { toggleStringInArray } from '@/utils';

interface Props {
  open: boolean;
  onClose: () => void;
  onAddCellAgent: (agents: DATA_INGESTION.CellAgent[]) => void;
  cellId: string;
  cellForm: FormInstance;
}

const statusOptions = [
  { label: '健康', value: 1 },
  { label: '离线', value: 2 },
];

export const AddCellAgentModal = (props: Props) => {
  const debouncedRef = useRef<any>();
  const ingestionType = useIngestionType();
  const { open, cellId, onClose, onAddCellAgent, cellForm } = props;
  const {
    queryParams,
    selectedRowKeys,
    selectedRows,
    pagination,
    handleTableChange,
    setTotal,
    setSelectedRowKeys,
    setSelectedRows,
    setFilter,
  } = useCustomTableHook({
    filter: {
      osAlias: 'Linux',
    },
  });
  const [isLoading, setLoading] = useState<boolean>(false);
  const [cellList, setCellList] = useState<Array<AgentSettingListItem<unknown>>>();
  const [groups, setGroups] = useState<DefaultOptionType[]>([]);
  const capability = useMemo(() => {
    if (ingestionType === 'tcp-udp') {
      return '';
    }
    if (ingestionType === 'logstash') {
      return 'custom';
    }
    return ingestionType;
  }, [ingestionType]);

  const onOk = () => {
    onAddCellAgent(selectedRows);
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const onRow = useTableOnRow<DATA_INGESTION.CellAgent>({
    onClick: row => () => {
      if (row.connection === 2) return;
      const newKeys = toggleStringInArray(row.agentId, selectedRowKeys);
      const newRows = cellList?.filter(val => newKeys.includes(val.agentId));
      setSelectedRowKeys?.(newKeys);
      setSelectedRows(newRows ?? []);
    },
  });

  useEffect(() => {
    if (debouncedRef.current) clearTimeout(debouncedRef.current);
    debouncedRef.current = setTimeout(() => {
      fetchData();
    }, 300);
  }, [queryParams]);

  const fetchAllGroup = async () => {
    const res = await cellListService.getAllGroup(cellId);
    if (res?.code === '0000') {
      setGroups(
        res?.data?.map(item => {
          return {
            value: item.id,
            label: item.name,
          };
        }),
      );
    }
  };

  useEffect(() => {
    fetchAllGroup();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    const res = await cellListService
      .queryAll({
        page: pagination.current - 1,
        size: pagination.pageSize,
        filter: {
          excludeIds: cellForm.getFieldValue('agentSettingList')?.map(x => x.agentId),
          cellId,
          ...queryParams?.filter,
          capability,
        },
        sort: {
          connection: SortType.ascend,
        },
      })
      .finally(() => {
        setLoading(false);
      });
    if (res?.code === '0000') {
      setCellList(res?.data ?? []);
      setTotal(res?.total ?? 0);
    }
  };

  return (
    <Modal
      // className='w-[80%]'
      width={'80%'}
      title='添加采集器'
      open={open}
      onCancel={onClose}
      onOk={onOk}
      styles={{
        body: {
          paddingBottom: 48,
          height: 'calc(80vh - 142px)',
        },
      }}
    >
      <div className='flex items-center flex-nowrap'>
        <SearchInput
          className='w-[230px] mr-10'
          onSearch={value => {
            const temp = queryParams.filter;
            if (temp) {
              temp.hostnameOrIp = value;
              setFilter(temp);
            }
          }}
          placeholder='输入主机名或IP地址按Enter键搜索'
        />
        <div className='flex items-center mr-10'>
          <span>标签：</span>
          <Select
            optionFilterProp='label'
            mode='multiple'
            allowClear
            options={groups}
            className='w-[230px]'
            onChange={value => {
              const temp = queryParams.filter;
              if (temp) {
                temp.groups = value;
                setFilter(temp);
              }
            }}
          />
        </div>
        <div className='flex items-center mr-10'>
          <span>状态：</span>
          <Select
            optionFilterProp='label'
            allowClear
            options={statusOptions}
            className='w-[180px]'
            onChange={value => {
              setFilter({ ...queryParams.filter, connection: value });
            }}
          />
        </div>
        <Radio.Group
          buttonStyle='solid'
          value={queryParams?.filter?.osAlias}
          onChange={({ target: { value } }) => {
            const temp = queryParams.filter;
            if (temp) {
              temp.osAlias = value;
              setFilter(temp);
            }
          }}
        >
          {OS_OPTIONS.map(({ label, value }) => (
            <Radio.Button key={value} value={value} disabled={value === 'Windows' && ingestionType === 'archive'}>
              {label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </div>
      <div className='h-full mt-2'>
        {queryParams?.filter?.osAlias && (
          <div className='h-full overflow-hidden'>
            <CustomTable
              className='bordered'
              rowKey='agentId'
              onRow={onRow}
              loading={isLoading}
              dataSource={cellList ?? []}
              columns={ADD_CELL_AGENT_COLUMNS}
              size='small'
              scroll={{ x: 300 }}
              pagination={{
                ...pagination,
              }}
              onChange={handleTableChange}
              rowSelection={{
                selectedRowKeys,
                getCheckboxProps: record => {
                  const { connection } = record;
                  if (connection === 2) {
                    return {
                      disabled: true,
                    };
                  }
                  return {
                    disabled: false,
                  };
                },
                onChange: (keys: string[], rows) => {
                  setSelectedRowKeys(keys);
                  setSelectedRows(rows);
                },
              }}
            />
          </div>
        )}
      </div>
    </Modal>
  );
};
