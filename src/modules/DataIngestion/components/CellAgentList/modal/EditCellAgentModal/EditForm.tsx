import { PropsWithChildren } from 'react';
import { Form, FormInstance } from 'antd';
import { FormLayout } from 'antd/es/form/Form';

import { useIngestionType } from '@/modules/DataIngestion/hooks';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
// eslint-disable-next-line max-len
import { InputConfigForm as ArchiveInput } from '@/modules/DataIngestion/pages/online/Archive/components/InputConfigForm';
import { InputConfigForm as FileInput } from '@/modules/DataIngestion/pages/online/File/components/InputConfigForm';
import { InputConfigForm as JdbcInput } from '@/modules/DataIngestion/pages/online/Jdbc/components/InputConfigForm';
// eslint-disable-next-line max-len
import { InputConfigForm as KafkaInput } from '@/modules/DataIngestion/pages/online/Kafka/components/InputConfigForm';
// eslint-disable-next-line max-len
import { InputConfigForm as LogstashInput } from '@/modules/DataIngestion/pages/online/Logstash/components/InputConfigForm';
// eslint-disable-next-line max-len
import { InputConfigForm as SyslogInput } from '@/modules/DataIngestion/pages/online/Syslog/components/InputConfigForm';
// eslint-disable-next-line max-len
import { InputConfigForm as TcpUdpInput } from '@/modules/DataIngestion/pages/online/TcpUdp/components/InputConfigForm';

type Props = PropsWithChildren<{
  form: FormInstance;
  detail: IngestionCellBasedInfo;
  inputForm;
  isMultiple?: boolean;
  isPublished?: boolean;
}>;

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 24, xl: 24, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

export const EditForm = ({ form, detail, inputForm, isMultiple, isPublished }: Props) => {
  const type = useIngestionType();

  return (
    <Form className='data-ingestion-form' form={form} {...formLayout}>
      {type === 'tcp-udp' && (
        <TcpUdpInput form={form} ref={inputForm} useAgentList={false} isMultiple={isMultiple} detail={detail} />
      )}
      {type === 'file' && (
        <FileInput form={form} ref={inputForm} useAgentList={false} isMultiple={isMultiple} detail={detail} />
      )}
      {type === 'archive' && (
        <ArchiveInput form={form} ref={inputForm} useAgentList={false} isMultiple={isMultiple} detail={detail} />
      )}
      {type === 'kafka' && (
        <KafkaInput form={form} ref={inputForm} useAgentList={false} isMultiple={isMultiple} detail={detail} />
      )}
      {type === 'syslog' && (
        <SyslogInput form={form} ref={inputForm} useAgentList={false} isMultiple={isMultiple} detail={detail} />
      )}
      {type === 'logstash' && (
        <LogstashInput form={form} ref={inputForm} useAgentList={false} isMultiple={isMultiple} detail={detail} />
      )}
      {type === 'jdbc' && (
        <JdbcInput
          form={form}
          isMultiple={isMultiple}
          ref={inputForm}
          detail={detail}
          useAgentList={false}
          isPublished={isPublished}
        />
      )}
    </Form>
  );
};
