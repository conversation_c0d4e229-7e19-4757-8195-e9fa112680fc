/* eslint-disable max-len */
import { PropsWithChildren, useMemo } from 'react';

import { useIngestionType } from '@/modules/DataIngestion/hooks';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { InputConfigDetail as ArchiveDetail } from '@/modules/DataIngestion/pages/online/Archive/components/InputConfigDetail';
import { InputConfigDetail as FileDetail } from '@/modules/DataIngestion/pages/online/File/components/InputConfigDetail';
import { InputConfigDetail as JdbcDetail } from '@/modules/DataIngestion/pages/online/Jdbc/components/InputConfigDetail';
import { InputConfigDetail as KafkaDetail } from '@/modules/DataIngestion/pages/online/Kafka/components/InputConfigDetail';
import { InputConfigDetail as LogstashDetail } from '@/modules/DataIngestion/pages/online/Logstash/components/InputConfigDetail';
import { InputConfigDetail as SyslogDetail } from '@/modules/DataIngestion/pages/online/Syslog/components/InputConfigDetail';
import { InputConfigDetail as TcpUdpDetail } from '@/modules/DataIngestion/pages/online/TcpUdp/components/InputConfigDetail';

type Props = PropsWithChildren<{
  detail: IngestionCellBasedInfo;
}>;

export const AgentConfigDetail = ({ detail }: Props) => {
  const type = useIngestionType();

  const Comp = useMemo(() => {
    switch (type) {
    case 'file':
      return FileDetail;
    case 'archive':
      return ArchiveDetail;
    case 'jdbc':
      return JdbcDetail;
    case 'kafka':
      return KafkaDetail;
    case 'logstash':
      return LogstashDetail;
    case 'syslog':
      return SyslogDetail;
    case 'tcp-udp':
      return TcpUdpDetail;
    }
    return <></>;
  }, [type]);

  return (
    <Comp detail={detail} />
  );
};
