import { useMemo, useState } from 'react';
import { Button, message, Modal } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components';
import { AgentSetting, AgentSettings, FilePreviewModal } from '@/modules/DataIngestion/components/CellAgentList/modal';
import { useAgentListTableColumns, useIngestionType } from '@/modules/DataIngestion/hooks';
import { filterTrue } from '@/utils';

import { AgentConfigDetail } from './AgentConfigDetail';

interface Props<T> {
  value?: AgentSettings<T>;
  cellId?: string;
}

export function AgentListTableView<T>(props: Props<T>) {
  const { pagination } = useCustomTableHook({});
  const { value, cellId } = props;
  const getCharsetCache = useMemo(() => new Map(), []);
  const [open, setOpen] = useState(false);
  const [detail, setDetail] = useState();
  const ingestionType = useIngestionType();
  const isFileIngestion = ingestionType === 'file';
  const partialColumns = useAgentListTableColumns<T>(getCharsetCache, () => {});
  const [agentSettingToPreview, setAgentSettingToPreview] = useState<AgentSetting<T>>();

  const onPreview = (agentSetting: AgentSetting<T>) => {
    
    const ok = agentSetting?.setting?.input?.path;

    if (!ok) return message.error('无路径信息');
    setAgentSettingToPreview(agentSetting);
  };

  const columns = [
    ...partialColumns,
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      render: (_: unknown, record: AgentSetting<T>) => {
        return (
          <div className='flex gap-x-1'>
            {isFileIngestion && (
              <Button type='link' size='small' onClick={() => onPreview(record)}>
                预览
              </Button>
            )}
            <Button
              type='link'
              size='small'
              onClick={e => {
                setOpen(true);
                setDetail(record);
              }}
            >
              查看
            </Button>
          </div>
        );
      },
    },
  ];

  if (!value?.length) {
    return null;
  }

  return (
    <>
      <CustomTable
        rowKey='agentId'
        className='cell-agent-list-table'
        dataSource={value}
        size='small'
        columns={columns.filter(filterTrue<TableColumn<AgentSetting<T>>>)}
        pagination={{
          ...pagination,
          total: value?.length ?? 0,
        }}
      />
      <Modal
        open={open}
        className='w-auto max-w-[55%]'
        title='查看'
        footer={null}
        onCancel={() => setOpen(false)}
        styles={{ body: { padding: '0 12px 12px 12px' } }}
      >
        <AgentConfigDetail
          detail={detail}
        />
      </Modal>
      
      {agentSettingToPreview && (
        <FilePreviewModal
          onClose={() => setAgentSettingToPreview(undefined)}
          agentSetting={agentSettingToPreview}
          cell={cellId}
        />
      )}
    </>
  );
}
