import { MouseEvent, useMemo, useState } from 'react';
import { Button, FormInstance, message, Modal } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components';
import { useRightsHook, useTableOnRow } from '@/hooks';
import { AgentSetting, AgentSettings, FilePreviewModal } from '@/modules/DataIngestion/components/CellAgentList/modal';
import { useAgentListTableColumns, useIngestionType } from '@/modules/DataIngestion/hooks';
import { cellListService } from '@/modules/DataIngestion/services';
import { buildGetRecommendCharsetBody, getNewAgentConfig } from '@/modules/DataIngestion/utils';
import { filterTrue, toggleStringInArray } from '@/utils';

type Lists = Array<DATA_INGESTION.AgentSettingListItem<unknown>>;

interface Props<T> {
  value?: AgentSettings<T>;
  onChange?: (value: AgentSettings<T>) => void;
  // rowSelection: TableProps<AgentSetting<T>>['rowSelection']
  setSelectedKeys: (key: string[]) => void;
  onEdit: (agentSetting: AgentSetting<T>) => void;
  form: FormInstance;
  inputForm: FormInstance;
  cellForm: FormInstance;
}

export function AgentListTable<T>(props: Props<T>) {
  const { hasRights } = useRightsHook();
  const permissionCode = 'data_develop:write';

  const { selectedRowKeys, pagination, handleTableChange, setSelectedRowKeys } = useCustomTableHook({});
  const ingestionType = useIngestionType();
  const isFileIngestion = ingestionType === 'file';
  const { value, onChange, onEdit, form, setSelectedKeys, cellForm, inputForm } = props;
  const getCharsetCache = useMemo(() => new Map(), []);
  const [agentSettingToPreview, setAgentSettingToPreview] = useState<AgentSetting<T>>();

  const updateAgentConfig = params => {
    const newAgentSetting = getNewAgentConfig(params);
    onChange?.(newAgentSetting);
  };

  const onPreview = (agentSetting: AgentSetting<T>) => {
    return (e: MouseEvent) => {
      e.stopPropagation();
      const ok = agentSetting?.setting?.input?.path;

      if (!ok) return message.error('请输入路径');
      setAgentSettingToPreview(agentSetting);
    };
  };

  const clearCache = () => getCharsetCache.clear();

  const removeAgentSetting = (ids: string[]) => {
    if (!cellForm) return;
    const agentSettingList: Lists = cellForm.getFieldValue('agentSettingList');
    const filter = ({ agentId }: Lists[number]) => !ids.includes(agentId);
    const newSettinsList = agentSettingList.filter(filter);
    cellForm.setFieldValue('agentSettingList', newSettinsList);
    setSelectedKeys(selectedRowKeys.filter(id => !ids.includes(id)));
    setSelectedRowKeys?.(selectedRowKeys.filter(id => !ids.includes(id)));
  };

  const onRemove = (agentSetting: AgentSetting<T>) => {
    Modal.confirm({
      title: `确认移除${agentSetting.setting.input.hostname}?`,
      onOk: () => {
        removeAgentSetting([agentSetting.agentId]);
        clearCache();
      },
    });
  };

  const updateCache = (agentId: string, doing: boolean, failed: boolean) => {
    getCharsetCache.set(agentId, { doing, failed });
  };

  const onRow = useTableOnRow<DATA_INGESTION.CellAgent>({
    onClick: row => () => {
      const newKeys = toggleStringInArray(row.agentId, selectedRowKeys);
      setSelectedRowKeys?.(newKeys);
      setSelectedKeys?.(newKeys);
    },
  });

  const processAutoCharset = async (agentId: string, input: T) => {
    const cellId = form?.getFieldValue('cellId');
    const body = buildGetRecommendCharsetBody({
      input: input as DATA_INGESTION.FILE_SYNC.InfoInput,
      cellId,
      agentId,
    });
    const {
      data: [{ charset }],
    } = await cellListService.getRecommendCharset([body]);

    updateAgentConfig({
      id: agentId,
      path: 'setting.input',
      data: { charset },
      value,
      onMatch: agentSetting => {
        if (charset === '失败') {
          if (agentSetting?.setting?.input?.charset) {
            agentSetting.setting.input.charset = '失败';
          }
          updateCache(agentId, false, true);
          return agentSetting;
        }
        updateCache(agentId, false, false);
      },
    });
  };

  const partialColumns = useAgentListTableColumns<T>(getCharsetCache, processAutoCharset);

  const columns = [
    ...partialColumns,
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      render: (_: unknown, record: AgentSetting<T>) => {
        return (
          <div className='flex gap-x-1'>
            {isFileIngestion && (
              <Button type='link' size='small' onClick={onPreview(record)}>
                预览
              </Button>
            )}
            <Button
              type='link'
              size='small'
              disabled={!hasRights(permissionCode)}
              onClick={e => {
                e.stopPropagation();
                onEdit(record);
              }}
            >
              编辑
            </Button>
            <Button
              type='link'
              size='small'
              disabled={!hasRights(permissionCode)}
              onClick={e => {
                e.stopPropagation();
                onRemove(record);
              }}
            >
              移除
            </Button>
          </div>
        );
      },
    },
  ];

  if (!value?.length) {
    return null;
  }

  return (
    <>
      <CustomTable
        rowKey='agentId'
        className='cell-agent-list-table'
        dataSource={value}
        size='small'
        columns={columns.filter(filterTrue<TableColumn<AgentSetting<T>>>)}
        onRow={onRow}
        onChange={handleTableChange}
        pagination={{
          ...pagination,
          total: value?.length ?? 0,
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys: string[], rows) => {
            setSelectedRowKeys(keys);
            setSelectedKeys(keys);
          },
        }}
      />
      {agentSettingToPreview && (
        <FilePreviewModal
          onClose={() => setAgentSettingToPreview(undefined)}
          agentSetting={agentSettingToPreview}
          globalForm={form}
        />
      )}
    </>
  );
}
