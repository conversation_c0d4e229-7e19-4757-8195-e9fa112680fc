/**
 * @module 数据集成-数据接入-采集器列表
 */
import { useState } from 'react';
import { Form, FormInstance, message } from 'antd';

import {
  AgentListTable,
  AgentSetting,
  AgentSettings,
  EditCellAgentModal,
} from '@/modules/DataIngestion/components/CellAgentList/modal';

import './style.less';

import { Toolbar } from './Toolbar';

interface Props {
  form: FormInstance;
  inputForm: FormInstance;
  cellForm: FormInstance;
  seniorCellForm: FormInstance;
  isPublished?: boolean;
}

export function CellAgentList<T>({ form, cellForm, seniorCellForm, inputForm, isPublished }: Props) {
  const [rowToEdit, setRowToEdit] = useState<AgentSetting<T>>();
  const [showEdit, setEditShow] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const values = Form.useWatch('agentSettingList', cellForm);

  const selectedRows: AgentSettings<T> | undefined = values?.filter((val: AgentSetting<T>) =>
    selectedRowKeys.includes(val.agentId),
  );

  const cellId = form?.getFieldValue('cellId');

  // 打开批量编辑弹窗
  const onBatchEdit = () => {
    if (!selectedRowKeys.length) {
      return message.error('请选择采集器');
    }
    setEditShow(true);
  };

  // 打开编辑弹窗
  const onEdit = (rowToEdit: AgentSetting<T>) => {
    setRowToEdit(rowToEdit);
    setEditShow(true);
  };

  const onEditClose = () => {
    setRowToEdit(undefined);
    setEditShow(false);
  };

  const onSave = (agentSettings: AgentSettings<T>) => {
    const values: AgentSettings<T> = cellForm?.getFieldValue('agentSettingList');
    const valueMap = agentSettings.reduce((acc: Record<string, AgentSetting<T>>, cur) => {
      acc[cur.agentId] = cur;
      return acc;
    }, {});
    const newValues = values.map(value => {
      const newValue = valueMap[value.agentId];
      return newValue ?? value;
    });
    cellForm?.setFieldValue('agentSettingList', newValues);
    onEditClose();
  };

  return (
    <div className='flex flex-col'>
      <Toolbar
        clearSelected={() => setSelectedRowKeys([])}
        onBatchEdit={onBatchEdit}
        form={form}
        cellForm={cellForm}
        inputForm={inputForm}
        seniorCellForm={seniorCellForm}
        selectedKeys={selectedRowKeys}
      />
      <Form.Item name='agentSettingList' wrapperCol={{ xxl: 24, xl: 24, lg: 24, xs: 24, sm: 24 }}>
        <AgentListTable
          onEdit={onEdit}
          form={form}
          cellForm={cellForm}
          setSelectedKeys={setSelectedRowKeys}
          inputForm={inputForm}
        />
      </Form.Item>

      {showEdit && (
        <EditCellAgentModal
          onClose={onEditClose}
          cellId={cellId}
          isPublished={isPublished}
          agentSettings={rowToEdit ? [rowToEdit] : selectedRows ?? []}
          onSave={onSave}
        />
      )}
    </div>
  );
}
