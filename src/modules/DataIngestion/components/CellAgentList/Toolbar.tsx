import { useState } from 'react';
import { Button, Form, FormInstance, message, Modal } from 'antd';
import cs from 'classnames';
import { omit } from 'lodash';

import { useRightsHook } from '@/hooks';
import { AddCellAgentModal } from '@/modules/DataIngestion/components/CellAgentList/modal';

type Lists = Array<DATA_INGESTION.AgentSettingListItem<unknown>>;

interface Props {
  selectedKeys: string[];
  clearSelected: () => void;
  onBatchEdit: () => void;
  form: FormInstance;
  cellForm: FormInstance;
  seniorCellForm: FormInstance;
  inputForm: FormInstance;
}

export function Toolbar(props: Props) {
  const { selectedKeys, clearSelected, onBatchEdit, form, cellForm, seniorCellForm, inputForm } = props;
  const { hasRights } = useRightsHook();
  const permissionCode = 'data_develop:write';

  const [open, setOpen] = useState(false);
  const toggleOpen = () => setOpen(!open);
  const cellId = Form.useWatch('cellId', form);

  const removeAgentSetting = (ids: string[]) => {
    if (!cellForm) return;
    const agentSettingList: Lists = cellForm.getFieldValue('agentSettingList');
    const newSettinsList = agentSettingList.filter(({ agentId }: Lists[number]) => !ids.includes(agentId));
    cellForm.setFieldValue('agentSettingList', newSettinsList);
  };

  const getInputConfig = () => {
    const formValues = {
      ...form?.getFieldsValue(),
      ...inputForm?.getFieldsValue(),
      ...seniorCellForm?.getFieldsValue(),
    };
    return omit(formValues, ['cellId', 'tbId', 'tbDeployId', 'agentSettingList', 'name']);
  };

  const onAdd = (rows: DATA_INGESTION.CellAgent[]) => {
    const newRows = rows.map(({ agentId, hostname, ip, version, os, groupList, connection }) => ({
      agentId,
      setting: {
        input: {
          hostname,
          ip,
          version,
          os,
          groupList,
          connection,
          ...getInputConfig(),
        },
      },
    }));
    const agentSettingList: Lists = cellForm.getFieldValue('agentSettingList') ?? [];
    cellForm?.setFieldValue('agentSettingList', [...agentSettingList, ...newRows]);
    toggleOpen();
  };

  const onBatchRemove = () => {
    if (!selectedKeys.length) {
      return;
    }
    Modal.confirm({
      title: '确认要批量移除吗?',
      onOk: () => {
        removeAgentSetting(selectedKeys);
        clearSelected();
      },
    });
  };

  const handleAdd = () => {
    if (!cellId) {
      return message.error('请选择采集网关');
    }
    toggleOpen();
  };

  const cls = cs({
    'cursor-not-allowed': !selectedKeys.length,
  });

  return (
    <div className='flex items-center gap-x-1 self-end mb-2'>
      <Button
        type='primary'
        onClick={handleAdd}
        className={cs({
          'cursor-not-allowed': !cellId,
        })}
        disabled={!hasRights(permissionCode)}
      >
        添加采集器
      </Button>
      <Button
        type='default'
        onClick={onBatchEdit}
        className={cls}
        disabled={!selectedKeys?.length || !hasRights(permissionCode)}
      >
        批量编辑
      </Button>
      <Button
        danger
        onClick={onBatchRemove}
        className={cs('mr-[13.5px]', cls)}
        disabled={!selectedKeys?.length || !hasRights(permissionCode)}
      >
        批量移除
      </Button>
      {open && (
        <AddCellAgentModal
          open={open}
          onClose={toggleOpen}
          onAddCellAgent={onAdd}
          cellForm={cellForm}
          cellId={cellId}
        />
      )}
    </div>
  );
}
