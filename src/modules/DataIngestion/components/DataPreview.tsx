import { useEffect, useMemo, useRef, useState } from 'react';
import { ReactCodeMirrorRef } from '@uiw/react-codemirror';
import { Allotment, LayoutPriority } from 'allotment';
import { Button, message, Tooltip } from 'antd';
import { toString } from 'lodash';
import { format } from 'sql-formatter';
import { Link } from 'umi';

import { CodeMirror, CustomTable, useCustomTableHook } from '@/components';
import { DataBaseTree } from '@/modules/DataService';
import { DataService } from '@/services';
import { ApiService } from '@/services/data-service/apiService';

interface Props {
  dsId?: string;
  showDatabase?: boolean;
  params?: any;
  previewSql?: string;
  databaseType?: string;
  onSqlChange: (sql) => void;
}

export const DataPreview = (props: Props) => {
  const { dsId, showDatabase = true, params = {}, onSqlChange, previewSql = '', databaseType } = props;
  const editorRef = useRef<ReactCodeMirrorRef>();
  const [loading, setLoading] = useState(false);
  const [databaseTableTree, setDatabaseTableTree] = useState<DefaultOptionType[]>([]);
  const [previewResult, setPreviewResult] = useState();
  const [errorMessage, setErrorMessage] = useState();
  const { pagination, handleTableChange, setTotal } = useCustomTableHook({
    pageSize: 5,
  });

  const tableColumns = useMemo(() => {
    if (!previewResult) return [];
    const item = previewResult.rows?.[0] ?? {};
    return Object.keys(item).map(item => ({
      title: item,
      dataIndex: item,
      render: val => {
        return toString(val);
      },
    }));
  }, [previewResult]);

  useEffect(() => {
    if (showDatabase) {
      fetchDataBaseTreeData();
    }
  }, [dsId, showDatabase]);

  useEffect(() => {
    if (previewSql) {
      handlePreview();
    }
  }, []);

  const fetchDataBaseTreeData = async () => {
    const { data } = await DataService.apiMetadataService.getDatabaseTableTree(dsId, { database: params.database });
    setDatabaseTableTree(
      data?.map(item => {
        item.title = item.database;
        item.icon = <i className='iconfont icon-data-line'></i>;
        item.isLeaf = false;
        item.key = (Math.random() * 100000).toString();
        item.children = item.modelTableList.map(child => {
          child.title = child.tbAlias ? `${child.tableName}(${child.tbAlias})` : child.tableName;
          child.database = item.database;
          child.key = (Math.random() * 100000).toString();
          child.isLeaf = false;
          child.icon = <i className='iconfont icon-view_table-line text-primary text-sm'></i>;
          return child;
        });

        return item;
      }) ?? [],
    );
  };

  const transformSql = (sql: string) => {
    const needTransformDatabase = ['DB2', 'ORACLE', 'SYBASE', 'SQLSERVER', 'POSTGRESQL'];
    if (needTransformDatabase.includes(databaseType)) {
      return sql.replace(/`/g, '');
    }
    return sql;
  };

  // 粘贴生成的sql到编辑器中
  const pasteSql = (sql: string) => {
    const view = editorRef?.current?.view;
    const state = view?.state;
    const range = state?.selection?.ranges[0];
    view?.dispatch({
      changes: {
        from: range?.from ?? 0,
        to: range?.to ?? 0,
        insert: sql,
      },
      selection: { anchor: range?.from ?? 0 + sql.length },
    });
  };

  const formatCode = () => {
    try {
      const newSql = format(previewSql);
      onSqlChange(newSql);
    } catch (e: any) {
      message.warning(e?.message.substring(0, 100) ?? '格式化失败');
    }
  };

  const handlePreview = async () => {
    if (!previewSql) {
      message.error('请输入sql语句');
      return;
    }
    setLoading(true);
    try {
      const { data } = await ApiService.getSqlPreview({ ...params, sql: previewSql }).finally(() => setLoading(false));
      setPreviewResult(data);
      setErrorMessage(undefined);
      setTotal(data.totalNum);
    } catch (e: any) {
      setPreviewResult(undefined);
      setTotal(0);
      setErrorMessage(e?.msg);
    }
  };

  return (
    <div className='w-full overflow-y-auto max-h-[650px]'>
      <Allotment proportionalLayout={false} className='h-[400px] w-full'>
        {showDatabase && (
          <Allotment.Pane preferredSize='320px' minSize={272}>
            <div className='flex flex-col overflow-hidden h-full'>
              <DataBaseTree
                dsId={dsId}
                pasteSql={pasteSql}
                databaseTableTree={databaseTableTree}
                setDatabaseTableTree={setDatabaseTableTree}
              />
            </div>
          </Allotment.Pane>
        )}

        <Allotment.Pane priority={LayoutPriority.High}>
          <div
            className='h-11 bg-[#fafafa] px-3 py-[10px] label flex items-center justify-between'
            style={{ borderBottom: '1px solid #f0f0f0' }}
          >
            <span>SQL查询语句（界面支持最多预览1000条数据）</span>
            <span>
              <Tooltip title='格式化SQL' placement='bottom'>
                <Button type='text' onClick={() => formatCode()}>
                  <i className='iconfont icon-format-line1 text-sm text-primary'></i>
                </Button>
              </Tooltip>
              <Link
                to={`/job/doc?docUrl=${encodeURIComponent(
                  '/api/v2/doc/api/helper/doc_data_service_sql',
                )}&display=说明文档&jobName=SQL书写说明文档`}
                target='_blank'
                className='ml-2'
              >
                <Tooltip title='SQL书写说明文档'>
                  <i className='iconfont icon-help-line'></i>
                </Tooltip>
              </Link>
              <Button className='ml-6' type='primary' onClick={handlePreview}>
                预览
              </Button>
            </span>
          </div>
          <div className='w-full overflow-auto' style={{ height: 'calc(100% - 44px)' }}>
            <CodeMirror
              className='h-full editor_border'
              value={previewSql ?? ''}
              lang='sql'
              ref={editorRef}
              onChange={(value: string) => {
                onSqlChange?.(transformSql(value));
              }}
            />
          </div>
        </Allotment.Pane>
      </Allotment>
      <div className='flex justify-between items-center w-full'>
        <div className='custom-header'>预览内容</div>
      </div>
      {previewResult && (
        <CustomTable
          columns={tableColumns}
          dataSource={previewResult?.rows ?? []}
          pagination={pagination}
          onChange={handleTableChange}
          className='bordered'
          style={{ borderTop: 'none' }}
          loading={loading}
        />
      )}
      {errorMessage && (
        <CodeMirror
          value={errorMessage}
          lang='sql'
          readOnly={true}
          lineWrapping={true}
          className='border-1 min-h-[200px]'
        />
      )}
    </div>
  );
};
