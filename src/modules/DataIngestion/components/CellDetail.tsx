/**
 * @module 数据集成-同步配置-选择采集网关
 */
import { useEffect, useMemo, useState } from 'react';

import { cellListService } from '@/modules/DataIngestion/services';

export const CellDetail = (props: {
  id: string;
  type?: 'CELL' | 'ROUTER';
}) => {
  const { type, id } = props;
  const [data, setData] = useState([]);
  const label = useMemo(() => {
    return data.find(item => item.value === id)?.label;
  }, [data, id]);

  useEffect(() => {
    getAllCell();
  }, []);

  const getAllCell = () => {
    try {
      cellListService.getAll(type).then(({ data }) => {
        setData(
          data.map(item => {
            return {
              label: item.name,
              value: item.id,
            };
          }),
        );
      });
    } catch (error) {}
  };

  return <span>{ label }</span>;
};
