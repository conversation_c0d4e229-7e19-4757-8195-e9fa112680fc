/**
 * @module 数据集成-数据输出-贴源表选择器
 */
import { Form, FormInstance, Input } from 'antd';
import cs from 'classnames';

import { CatalogType } from '@/constants';
import { PlatformModelSelect } from '@/modules/DataProcess/components';

interface Props {
  label?: string;
  cellId?: string;
  form: FormInstance;
  readonly?: boolean;
}

export const SourceTableSelect = ({ label = '贴源表', cellId, form, readonly }: Props) => {
  const tbDeployId = Form.useWatch<string>('tbDeployId', form);

  const onChange = (_: any, { id }: { id: string }) => {
    form?.setFieldValue('tbDeployId', id);
  };

  return (
    <div className='h-full pt-5'>
      <div className='flex gap-2'>
        <Form.Item name='tbDeployId' label='模型发布id' hidden>
          <Input value={tbDeployId} className='w-full' />
        </Form.Item>
        <PlatformModelSelect
          className={cs(
            {
              'pointer-events-none cursor-not-allowed': !cellId || readonly,
            },
            'w-[500px]',
          )}
          cellId={cellId}
          fullScreen={true}
          catalog={CatalogType.DS}
          name='tbId'
          label={label}
          ruleMsg='请选择贴源表'
          title='kafka模型'
          platform='KAFKA'
          onChange={onChange}
          type='sink'
        />
      </div>
    </div>
  );
};
