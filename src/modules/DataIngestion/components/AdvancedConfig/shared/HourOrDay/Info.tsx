import { TIME_UNIT_MAP } from '@/modules/DataIngestion/constants';

import type { ReadProps } from '../../shared';

type Props = Partial<ReadProps<DATA_INGESTION.FILE_SYNC.TIME>>;

export const Info = ({ onClick, value }: Props) => {
  const timeUnit = value?.unit ? TIME_UNIT_MAP[value.unit] : '';

  return (
    <div className='flex items-center ingestion-file-advance-config-info' onClick={onClick}>
      <span>{value?.num}</span>
      <span>{timeUnit}</span>
    </div>
  );
};
