/**
 * @module 采集高级配置-打开或关闭
 */
import { FormInstance } from 'antd';

import { Field } from '../Field';

import { Info } from './Info';
import { Input } from './Input';

interface Props {
  type?: 'number' | 'boolean';
  onChange?: (value: boolean | number) => void;
  name: string;
  readonly?: boolean;
  initialValue?: boolean | number;
  form: FormInstance;
}

export function OpenCloseSelect({ type, name, onChange, readonly, initialValue, form }: Props) {
  const presetInitialValue = type === 'number' ? 0 : false;
  const distInitialValue = initialValue ?? presetInitialValue;

  return (
    <Field<boolean | number> name={name} form={form} initialValue={distInitialValue} readonly={readonly}>
      <Input onOpenChange={onChange} type={type} />
      <Info />
    </Field>
  );
}
