/**
 * @module 采集高级配置-检查过期文件是否存在间隔
 */
import { FormInstance } from 'antd';

import { InputNumber } from '../shared';

interface Props {
  readonly?: boolean;
  form: FormInstance;
}

export const ExpiredCheckInterval = ({ readonly, form }: Props) => {
  return (
    <InputNumber
      form={form}
      readonly={readonly}
      name='expiredCheckInterval'
      infoDesc='秒'
      min={1}
      max={100000}
      initialValue={3600}
    />
  );
};
