/**
 * @module 数据集成-数据接入-协议
 */
import { Radio } from 'antd';

import { PROTOCOL_OPTIONS } from '@/modules/DataIngestion/constants';

interface Props {
  value?: string;
  onChange?: (value: string) => void;
}

export const Protocol = ({ value, onChange }: Props) => {
  return (
    <Radio.Group buttonStyle='solid' value={value} onChange={({ target: { value } }) => onChange?.(value)}>
      {PROTOCOL_OPTIONS.map(({ label, value }) => (
        <Radio.Button className='mb-1' key={value} value={value}>
          {label}
        </Radio.Button>
      ))}
    </Radio.Group>
  );
};
