/**
 * @module 数据集成-数据接入-主题
 */
import { Select } from '@/components';
import { cellListService } from '@/modules/DataIngestion/services';

interface Props {
  dsId: string;
  value?: string;
  onChange?: (value: string, selectedOptions: any[]) => void;
}

export const Topic = ({ dsId, value, onChange }: Props) => {
  // 查询 kafka 集群下的 topic
  const getTopicOfKafkaByDsId = async (dsId: string) => {
    const { data }: { data: DATA_INGESTION.KAFKA_SYNC.TOPIC[] } = await cellListService.getTopicOfKafkaByDsId(dsId);
    return data;
  };

  return (
    <Select
      showSearch
      disabled={!dsId}
      value={value}
      queryOptions={{
        enabled: !!dsId,
      }}
      queryKey={[cellListService.getTopicOfKafkaByDsIdUrl(dsId)]}
      queryFunc={() => getTopicOfKafkaByDsId(dsId)}
      placeholder='请选择'
      onChange={onChange}
      toOption={topic => ({
        label: topic,
        value: topic,
      })}
    />
  );
};
