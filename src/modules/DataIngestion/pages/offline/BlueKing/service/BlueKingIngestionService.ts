import Request from '@/request';
import { downloadFile } from '@/utils';

import { BlueKingConfigEntity, FieldMappingItem, RelationMappingItem } from '../model';

const url = '/api/v2/ingestion/blueking';


export interface ModelTreeParams {
  dsId: string;
  sourceModel: string;
  targetModel: string;
  currentFieldMapping: FieldMappingItem[]
  currentRelationMapping: RelationMappingItem[];
}

export interface AutoMappingParams {
  dsId: string;
  sourceModelSet: string[];
}

export interface FieldMetaItem {
  fieldName: string;
  fieldNameZh: string;
  isNotNull: boolean;
}

export interface RelationMetaItem {
  typeCode: string;
  typeName?: string;
  modelRelation: string;
}

export interface MappingMetaResult {
  currentFieldMappingCheckResult: FieldMappingItem[];
  currentRelationMappingCheckResult: RelationMappingItem[];
  sourceFieldList: FieldMetaItem[];
  sourceRelationList: RelationMetaItem[];
  targetFieldList: FieldMetaItem[];
  targetRelationList: RelationMetaItem[];
}

export const BlueKingIngestionService = {
  /**
   * 蓝鲸接入列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建蓝鲸接入
   */
  async create(data: Omit<BlueKingConfigEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新蓝鲸接入
   * @param data
   * @returns
   */
  async update(data: BlueKingConfigEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除蓝鲸接入
   * @param id 蓝鲸接入id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取蓝鲸接入详情
   * @param id 蓝鲸接入id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取蓝鲸模型数
   * @param dsId 蓝鲸数据源Id
   * @returns
   */
  async getModelTree(dsId: string) {
    return await Request.post(`${url}/model-tree`, {
      data: {
        dsId,
      },
    });
  },
  /**
   * 获取模型字段和关系
   * @param ModelTreeParams
   * @returns
   */
  async getModelFieldRelation(data: ModelTreeParams): Promise<Response<MappingMetaResult>> {
    return await Request.post(`${url}/model-field-relation`, {
      data,
    });
  },
  /**
   * 获取一键加载模型
   * @param AutoMappingParams
   * @returns
   */
  async getAutoMapping(data: AutoMappingParams) {
    return await Request.post(`${url}/auto-mapping`, {
      data,
    });
  },

  /**
   * 获取接入指南zip包
   */
  async downloadExample(id?: string) {
    downloadFile(`${url}/export-example`, { id });
  },
  /**
   * 查询全部蓝鲸接入配置
   * @param businessFlowId string
   * @returns
   */
  async getAll(businessFlowId: string) {
    return await Request.get(`${url}/list`, {
      params: {
        businessFlowId,
      } });
  },
  /**
   * 检查aviatorScript
   */
  async checkAviatorScript(data: {
    dsId: string;
    sourceModel: string;
    sourceField: string;
  }) {
    return await Request.post(`${url}/check-field`, {
      data,
    });
  },
};
