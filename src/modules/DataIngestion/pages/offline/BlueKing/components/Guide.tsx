import { Button } from 'antd';
import { useParams } from 'umi';

import { PROJECT } from '@/constants';

import { BlueKingIngestionService } from '../service';

const baseUrl = `${ PROJECT.baseUrl }/images`;

export function Guide() {
  const { id } = useParams();
  function handleDownload() {
    BlueKingIngestionService.downloadExample(id);
  }

  if (!id) {
    return <div className='px-4 text-sm'>请您配置并保存当前任务。</div>;
  }

  return <div className="px-4 pb-4">
    您可以<Button type='link' size='small' onClick={handleDownload}>下载示例</Button>，通过导入功能导入示例工作流作业并配置，完成数据同步。
    <dl className='mt-4 leading-8'>
      <dt className='text-base mb-2'>配置步骤：</dt>
      <dd className='mb-1'>1. 在导入页面，选择数据开发-数据开发作业，选择当前业务流程，上传已下载的示例工作流。</dd>
      <dd className='mb-1'>2. 在当前业务流程下，上传资源程序包jax-integration-xxx.jar<br/>
        （1）重命名为jax-integration.jar。<br />
        （2）需上传到HDFS，根据部署文档完成dolphinscheduler中HDFS配置，
        相关配置文件为：<br/>
        <div className='ml-8'>
          worker-server/conf/common.properties 、 master-server/conf/common.properties<br/>
          如下图：<br />
          <img src={`${baseUrl}/blueking/step2.png`} width={570} className='border-1 my-2'></img>
        </div>
      </dd>
      <dd className='mb-1'>3. 进入数据开发，业务流程，离线开发-数据处理，选择导入的工作流。</dd>
      <dd className='mb-1'>4. 选择“蓝鲸数据同步”节点，选择资源jax-integration.jar。如下图<br/>
        <img src={`${baseUrl}/blueking/step4.png`} className='border-1 my-2' width={570}></img>
      </dd>
      <dd className='mb-1'>5. 选择“同步到宽表和图空间”节点，选择资源jax-integration.jar。<br />
        <img src={`${baseUrl}/blueking/step5.png`} className='border-1 my-2' width={570}></img>
      </dd>
      <dd className='mb-1'>6. 点击“保存”按钮，完成同步工作流初步配置。</dd>
      <dd className='mb-1'>7. 点击“试跑”，试跑完成后，在【数据资产-资产目录】中可查看试跑效果，如下图：<br />
        <img src={`${baseUrl}/blueking/step7.png`} className='border-1 my-2' width={570}></img>
      </dd>
      <dd className='mb-1'>8. 确定配置后，点击“定时配置”设置定时时间，最后点击“发布”，蓝鲸数据源同步工作流配置生效。<br />
        <img src={`${baseUrl}/blueking/step8.png`} className='border-1 my-2' width={570}></img>
      </dd>
    </dl>
  </div>;
}
