import { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Button, DatePicker } from 'antd';
import dayjs from 'dayjs';
import { groupBy } from 'lodash';

import { CustomTable } from '@/components';
import { objectPropertyApi } from '@/modules/DataProperty/service/objectProperty';

import './StaticsPanel.less';

type DateRangeType = [dayjs.Dayjs | null, dayjs.Dayjs | null] | null;

interface Props {
  configId: string;
  sourceType: 'blueking';
}
export function StaticsPanel(props: Props) {
  const { configId, sourceType } = props;
  // 默认筛选最近三天
  const [dateRange, setDateRange] = useState<DateRangeType>();
  const { data, refetch, isLoading } = useQuery({
    queryKey: [dateRange, configId, sourceType, 'instanceCount'],
    queryFn: async () => {
      const { data } = await objectPropertyApi.getInstanceCount({
        configId,
        sourceType,
        startDate: dateRange?.[0]?.format('YYYY-MM-DD') ?? '',
        endDate: dateRange?.[1]?.format('YYYY-MM-DD') ?? '',
      });
      return data.map((item, index) => {
        return {
          ...item,
          id: index,
        };
      });
    },
  });

  const groupData = groupBy(data, 'date');

  const list = useMemo(() => {
    return Object.values(groupData).reduce((prev, next) => {
      return [...prev, ...next];
    }, []);
  }, [data]);

  const columns = [
    {
      key: 'date',
      title: '接入批次',
      dataIndex: 'date',
      onCell: record => {
        const children = groupData[record.date];
        const isFirst = children[0].id === record.id;
        return {
          rowSpan: isFirst ? children.length : 0,
        };
      },
    },
    {
      key: 'instanceName',
      title: '对象/关系模型',
      dataIndex: 'instanceName',
      onCell: record => {
        return {
          className: record.instanceType === 'RELATION_INSTANCE' ? 'text-primary ' : 'text-success ',
        };
      },
    },
    {
      key: 'count',
      title: '实例数量',
      dataIndex: 'count',
      onCell: record => {
        return {
          className: record.instanceType === 'RELATION_INSTANCE' ? 'text-primary' : 'text-success',
        };
      },
    },
  ];

  return <div>
    <div className='px-4 mb-4 flex items-center'>
      <DatePicker.RangePicker
        value={dateRange}
        onChange={dates => {
          setDateRange(dates);
        }}
      />
      <Button
        type='text'
        loading={isLoading}
        onClick={() => refetch()}
      >
        <i className='iconfont icon-refresh-line'></i>
      </Button>
    </div>
    <CustomTable
      bordered
      columns={columns}
      dataSource={list}
      pagination={false}
      className='statics-panel-table mx-4'
    />
  </div>;
}
