import { useState } from 'react';
import { ArrowRightOutlined } from '@ant-design/icons';
import { useMutation } from '@tanstack/react-query';
import { Alert, Button, message, Popover, Space, TableColumnProps } from 'antd';
import { cloneDeep } from 'lodash';

import { CustomTable } from '@/components';

import type { BlueKingMappingListItem, RelationMappingItem } from '../model';
import { BlueKingIngestionService } from '../service/BlueKingIngestionService';

import { ModelMappingModal } from './ModelMappingModal';

function generateId() {
  return (Math.random() * 1000000).toFixed(0);
}

interface Props {
  isEditing: boolean;
  bkDsId?: string;
  value?: BlueKingMappingListItem[];
  onChange?: (value: BlueKingMappingListItem[]) => void;
}

export function MappingSetting(props: Props) {
  const { bkDsId, isEditing, value: mappingList = [], onChange } = props;
  const [action, setAction] = useState<'create' | 'edit' | 'clone' | undefined>();
  const [mappingItem, setMappingItem] = useState<BlueKingMappingListItem | undefined>();
  const { mutate: fetchAutoMapping, isLoading } = useMutation({
    mutationFn: async () => {
      const { data } = await BlueKingIngestionService.getAutoMapping({
        dsId: bkDsId!,
        sourceModelSet: Array.from(new Set(mappingList.map(x => x.sourceModel))),
      });
      return data;
    },
    // 不能采用useEffect监听
    onSuccess(autoMappingData) {
      onChange?.([...mappingList??[], ...autoMappingData.map(item => {
        return {
          ...item,
          id: generateId(),
          isNew: true, // 标记isNew，并生成id，方便前端渲染，保存时需判断，如果是isNew，清除id
        };
      })]);
    },
    onError(error) {
      if (error?.msg) {
        message.error(error?.msg);
      }
    },
  });

  function handleAutoMapping() {
    if (!bkDsId) return;
    fetchAutoMapping();
  }

  const customTableColumns: Array<TableColumnProps<BlueKingMappingListItem>> = [
    {
      title: '源模型',
      dataIndex: 'sourceModelPathZh',
      key: 'sourceModelPathZh',
    },
    {
      title: '目标模型',
      dataIndex: 'targetModelPathZh',
      key: 'targetModelPathZh',
    },
    {
      title: '过滤条件',
      dataIndex: 'filterScript',
      key: 'filterScript',
      render(text) {
        return <div className='w-48 whitespace-break-spaces'>{ text}</div>;
      },
    },
    {
      title: '字段映射',
      dataIndex: 'fieldMapping',
      key: 'fieldMapping',
      render(fieldMapping) {
        return fieldMapping?.map((field, index) => {
          return <span key={index}>
            {field.sourceField}
            <ArrowRightOutlined className='text-gray mx-1 text-xs' />
            {field.targetField}
            <br />
          </span>;
        });
      },
    },
    {
      title: '关系映射',
      dataIndex: 'relationMapping',
      key: 'relationMapping',
      render(relationMapping: RelationMappingItem[]) {
        return relationMapping?.map((relation, index) => {
          return <span key={index}>
            { relation.sourceModelRelation}({relation.sourceRelationType})
            <ArrowRightOutlined className='text-gray mx-1 text-xs' />
            {relation.targetRelationType}
            { relation.reverse ? '(反转)' : '' }
            <br />
          </span>;
        });
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      hidden: !isEditing,
      render(_, record) {
        return <Space>
          <Button type='link' size='small' onClick={() => handleClone(record)}>克隆</Button>
          <Button type='link' size='small' onClick={() => handleEdit(record)}>编辑</Button>
          <Button type='link' size='small' onClick={() => handleDelete(record)}>删除</Button>
        </Space>;
      },
    },
  ];

  function handleEdit(record: BlueKingMappingListItem) {
    setAction('edit');
    setMappingItem(record);
  }

  function handleClone(record: BlueKingMappingListItem) {
    setAction('clone');
    setMappingItem({
      ...record,
      id: undefined,
    });
  }

  function handleDelete(record: BlueKingMappingListItem) {
    onChange?.(mappingList?.filter(x => x.id !== record.id)??[]);
  }

  function handleModalMappingOk(mappingItem: BlueKingMappingListItem) {
    switch (action) {
    case 'create':
    case 'clone': {
      const cloneList = cloneDeep(mappingList??[]);
      cloneList.unshift({
        ...cloneDeep(mappingItem),
        isNew: true,
        id: generateId(),
      });
      onChange?.(cloneList);
      break;
    }

    case 'edit': {
      onChange?.(mappingList.map(x => {
        if (x.id === mappingItem.id) {
          return {
            ...x,
            ...cloneDeep(mappingItem),
          };
        }
        return x;
      }));
      break;
    }
    }
  }

  // console.log(mappingList);

  return <div>
    <Alert
      closable
      showIcon
      className='whitespace-pre-wrap mb-4 mx-4'
      message={`设置蓝鲸配置管理系统中的配置项模型与对象模型的对应关系
• 源模型：蓝鲸中的配置项模型。如果已经配置了模型映射，则保存后不能再更改
• 目标模型：选择同步到中台中的对象模型
• 过滤条件：Aviator表达式，满足此条件的配置项才会同步
• 字段映射：配置源字段和目标字段的对应关系
• 关系映射：源关系和目标关系的对应`}
    />
    { isEditing && <div className='flex justify-between mb-2 mx-4'>
      <Popover content={ bkDsId ? null : '请先切换到数据源映射，选择蓝鲸数据源' }>
        <Button
          type='primary'
          onClick={handleAutoMapping}
          loading={ isLoading }
          disabled={ !bkDsId }
        >一键加载蓝鲸模型</Button>
      </Popover>
      <Popover content={ bkDsId ? null : '请先切换到数据源映射，选择蓝鲸数据源' }>
        <Button
          type='primary'
          onClick={() => setAction('create')}
          disabled={ !bkDsId }
        >新建模型映射</Button>
      </Popover>
    </div>
    }
    <CustomTable
      columns={customTableColumns}
      dataSource={mappingList}
      pagination={false}
    />
    {action && <ModelMappingModal
      open={!!action}
      dsId={bkDsId!}
      mappingItem={ mappingItem }
      onCancel={() => {
        setAction(undefined);
        setMappingItem(undefined);
      }}
      onOk={ handleModalMappingOk }
    />}
  </div>;
}
