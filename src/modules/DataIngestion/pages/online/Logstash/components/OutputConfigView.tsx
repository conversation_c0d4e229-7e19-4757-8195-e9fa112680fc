/* eslint-disable max-len */
import { useEffect, useMemo, useState } from 'react';
import { Descriptions } from 'antd';

import { CodeMirror } from '@/components';
import { useConfigDetail } from '@/modules/DataIngestion/hooks/useConfigDetail';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { TableDeployApi } from '@/services';

interface Props {
  detail: IngestionCellBasedInfo;
}

const outputOptions = ['elasticsearch', 'kafka'];

export const OutputConfigView = (props: Props) => {
  const { detail } = props;
  const { detailInfo } = useConfigDetail('JDBC', detail);
  const [selectedItem, setSelectedItem] = useState<any>();
  const modelName = useMemo(() => {
    if (selectedItem) {
      const { tbName, tbAlias } = selectedItem;
      return `${tbName}${tbAlias ? `(${tbAlias})` : ''}`;
    } else {
      return '';
    }
  }, [selectedItem]);

  useEffect(() => {
    if (detailInfo?.tbId) {
      getTbDetail();
    }
  }, [detailInfo?.tbId]);

  const getTbDetail = async () => {
    TableDeployApi.getLastSuccessDeployDetail(detailInfo?.tbId).then(({ data }) => {
      setSelectedItem(data);
    });
  };

  const InputItems = [
    {
      key: 'inputType',
      label: '选择类型',
      children: <div>{outputOptions.find(item => item === detailInfo?.outputType)}</div>,
    },
    {
      key: 'inputTbId',
      label: '选择模型',
      children: <div>{modelName}</div>,
    },
    {
      key: 'input',
      label: '输出配置',
      children: <CodeMirror
        value={detailInfo?.output || ''}
        className='h-[200px] w-[650px] border-1'
        readOnly
      />,
    },
  ];

  return <>
    <Descriptions
      column={1}
      items={InputItems}
      className='mt-5'
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
  </>;
};
