/* eslint-disable max-len */
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Button, Form, Select, Tooltip } from 'antd';
import { FormLayout } from 'antd/es/form/Form';

import { CodeMirror, SectionCaptain, useKeepAliveTabs } from '@/components';
import { SourceModelSelect } from '@/components/business/form/SourceModelSelect';
import { CellAgentList } from '@/modules/DataIngestion/components';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { cellListService } from '@/modules/DataIngestion/services';
import { ingestionDataToForm } from '@/modules/DataIngestion/utils';
import { handleOnBeforeUnload } from '@/utils';

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 16, xl: 24, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

interface Props {
  useAgentList?: boolean;
  form: FormInstance;
  detail?: IngestionCellBasedInfo;
  isMultiple?: boolean;
  initialValues?: object;
  isPublished?: boolean;
}

const importTemplateOptions = ['elasticsearch','kafka', 'http', 'jdbc', 'rabbitmq', 'snmp', 'graphite', 'http_poller', 'redis', 'snmptrap'].map(item => {
  return {
    label: item,
    value: item,
  };
});

const filterTemplateOptions = ['csv', 'drop', 'json', 'ruby', 'truncate', 'uuid', 'date', 'grok', 'kv', 'split', 'useragent', 'xml'].map(item => {
  return {
    label: item,
    value: item,
  };
});

export const _InputConfigForm = (props: Props, ref: ForwardedRef<{ validateFields }>) => {
  const { useAgentList = true, form, detail, isMultiple = false, initialValues, isPublished } = props;
  const { setPageInfo } = useKeepAliveTabs();
  const modelSelectRef = useRef(null);
  const [inputForm] = Form.useForm();
  const [cellForm] = Form.useForm();
  const [seniorCellForm] = Form.useForm();
  const inputRules = useMemo(() => (isMultiple ? [] : [{ required: true, message: '请填写配置' }]), [isMultiple]);
  const [filterConfig, setFilterConfig] = useState('');
  const importTemplate = Form.useWatch('inputType', inputForm);
  const inputTbId = Form.useWatch('inputTbId', inputForm);
  const filterTemplate = Form.useWatch('filterType', inputForm);
  const showModelSelect = useMemo(() => ['elasticsearch', 'kafka'].includes(importTemplate), [importTemplate]);

  const setConfirm = (isDirty: boolean) => {
    setPageInfo({ isDirty });
    window.onbeforeunload = isDirty ? handleOnBeforeUnload('当前内容未保存，确定离开此界面？') : null;
  };

  useImperativeHandle(ref, () => {
    return {
      validateFields: async () => {
        const cellValues = await cellForm.validateFields();
        const seniorCellValues = await seniorCellForm.validateFields();
        const inputValues = await inputForm.validateFields();
        return {
          ...cellValues,
          ...seniorCellValues,
          ...inputValues,
        };
      },
    };
  });

  useEffect(() => {
    if (detail) {
      const formData = ingestionDataToForm(detail);
      cellForm?.setFieldsValue(formData);
      seniorCellForm?.setFieldsValue(formData);
      inputForm?.setFieldsValue({
        ...formData,
        filter: formData?.filter || '',
      });
    }
  }, [detail]);

  const getTemplate = async (type, template, tbId) => {
    const { data } = await cellListService.getLogstashTemplate({
      type,
      template,
      tbId,
    });
    return data;
  };

  const handleChangeInputTemplate = async val => {
    inputForm.setFieldValue('inputTbId', undefined);
    if (val) {
      const data = await getTemplate('input', val, inputTbId);
      inputForm.setFieldValue('input', data);
      modelSelectRef.current?.openModal();
    }
  };

  const handleChangeFilterTemplate = async val => {
    if (val) {
      const data = await getTemplate('filter', val, undefined);
      inputForm.setFieldValue('filter', data);
    }
  };

  const handleChangeInputModel = async val => {
    const data = await getTemplate('input', importTemplate, val);
    inputForm.setFieldValue('input', data);
  };

  const handleSetInputConfig = val => {
    inputForm.setFieldValue('input', val);
  };

  const handleSetFilterConfig = val => {
    setFilterConfig(val);
    inputForm.setFieldValue('filter', val);
  };

  const gotoDoc = (type, template) => {
    window.open(`/data-dev/logstash-document?type=${type}&template=${template}`);
  };

  return (
    <>
      <Form
        onChange={() => setConfirm(true)}
        form={inputForm}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
        initialValues={initialValues}
      >
        <div className='pt-5 pl-9'>
          <SectionCaptain title='输入' />
          <Form.Item label='快速模板'>
            <div className='flex items-center'>
              <div>
                <Form.Item name='inputType'>
                  <Select
                    placeholder='请选择'
                    showSearch
                    options={importTemplateOptions}
                    allowClear
                    className='w-[260px]'
                    onChange={handleChangeInputTemplate}
                  />
                </Form.Item>
              </div>
              {
                importTemplate && <Button type='link' className='mb-[8px]' onClick={() => gotoDoc('input', importTemplate)}>
                  <Tooltip title='配置帮助' placement='bottom'>
                    <i className='iconfont icon-help-line'  />
                  </Tooltip>
                </Button>
              }
              
            </div>
          </Form.Item>
          {showModelSelect &&
            <div className='flex items-center'>
              <div>
                <Form.Item label='选择模型' name='inputTbId'>
                  <SourceModelSelect
                    ref={modelSelectRef}
                    className='w-[460px]'
                    platform={importTemplate}
                    title={'选择模型'}
                    allowClear
                    onChange={handleChangeInputModel}
                  />
                </Form.Item>
              </div>
              <Tooltip title='可选择模型以填充数据源等配置' placement='bottom'>
                <i className='iconfont icon-help-line text-primary ml-3 mb-[8px]'  />
              </Tooltip>
            </div>
          }
          <Form.Item name='input' label='输入配置' rules={inputRules} >
            <CodeMirror
              className='h-[200px] w-[650px] border-1'
              onChange={handleSetInputConfig}
            />
          </Form.Item>
          <SectionCaptain title='过滤' className='mt-2' />

          <Form.Item label='快速模板'>
            <div className='flex items-center'>
              <div>
                <Form.Item name='filterType'>
                  <Select
                    placeholder='请选择'
                    showSearch
                    options={filterTemplateOptions}
                    allowClear
                    className='w-[260px]'
                    onChange={handleChangeFilterTemplate}
                  />
                </Form.Item>
              </div>
              {
                filterTemplate && <Button type='link' className='mb-[8px]' onClick={() => gotoDoc('filter', filterTemplate)}>
                  <Tooltip title='配置帮助' placement='bottom'>
                    <i className='iconfont icon-help-line'  />
                  </Tooltip>
                </Button>
              }
            </div>
          </Form.Item>
          <Form.Item name='filter' label='过滤配置' >
            <CodeMirror
              value={filterConfig}
              lang='json'
              className='h-[200px] w-[650px] border-1'
              onChange={handleSetFilterConfig}
            />
          </Form.Item>
        </div>
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={cellForm}
        className='flex flex-col pb-3 px-0 data-ingestion-form'
        {...formLayout}
      >
        {useAgentList && (
          <CellAgentList cellForm={cellForm} form={form} isPublished={isPublished} seniorCellForm={seniorCellForm} inputForm={inputForm} />
        )}
      </Form>
    </>
  );
};

export const InputConfigForm = forwardRef(_InputConfigForm);
