/* eslint-disable max-len */
import { useEffect, useMemo, useState } from 'react';
import { Descriptions } from 'antd';

import { CodeMirror, SectionCaptain } from '@/components';
import { AgentListTableView } from '@/modules/DataIngestion/components/CellAgentList/modal/AgentListTableView';
import { useConfigDetail } from '@/modules/DataIngestion/hooks/useConfigDetail';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { TableDeployApi } from '@/services';

interface Props {
  detail: IngestionCellBasedInfo;
}

const importTemplateOptions = ['elasticsearch','kafka', 'http', 'jdbc', 'rabbitmq', 'snmp', 'graphite', 'http_poller', 'redis', 'snmptrap'];

const filterTemplateOptions = ['csv', 'drop', 'json', 'ruby', 'truncate', 'uuid', 'date', 'grok', 'kv', 'split', 'useragent', 'xml'];

export const InputConfigDetail = (props: Props) => {
  const { detail } = props;
  const { detailInfo } = useConfigDetail('LOGSTASH', detail);
  const [selectedItem, setSelectedItem] = useState<any>();
  const modelName = useMemo(() => {
    if (selectedItem) {
      const { tbName, tbAlias } = selectedItem;
      return `${tbName}${tbAlias ? `(${tbAlias})` : ''}`;
    } else {
      return '';
    }
  }, [selectedItem]);

  useEffect(() => {
    if (detailInfo?.inputTbId) {
      getTbDetail();
    }
  }, [detailInfo?.inputTbId]);

  const getTbDetail = async () => {
    TableDeployApi.getLastSuccessDeployDetail(detailInfo?.inputTbId).then(({ data }) => {
      setSelectedItem(data);
    });
  };

  const InputItems = [
    {
      key: 'inputType',
      label: '快速模板',
      children: <div>{importTemplateOptions.find(item => item === detailInfo?.inputType)}</div>,
    },
    {
      key: 'inputTbId',
      label: '选择模型',
      children: <div>{modelName}</div>,
    },
    {
      key: 'input',
      label: '输入配置',
      children: <CodeMirror
        value={detailInfo?.input || ''}
        className='h-[200px] w-[650px] border-1'
        readOnly
      />,
    },
  ];
  const filterItems = [
    {
      key: 'inputType',
      label: '快速模板',
      children: <div>{filterTemplateOptions.find(item => item === detailInfo?.filterType)}</div>,
    },
    {
      key: 'filter',
      label: '过滤配置',
      children: <CodeMirror
        value={detailInfo?.filter || ''}
        className='h-[200px] w-[650px] border-1'
        readOnly
      />,
    },
  ];

  return <>
    
    <SectionCaptain title='输入' className='pt-5 pl-9' />
    <Descriptions
      column={1}
      items={InputItems}
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
    <SectionCaptain title='过滤' className='pl-9' />
    <Descriptions
      column={1}
      items={filterItems}
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
    <AgentListTableView value={detail?.agentSettingList} />
  </>;
};
