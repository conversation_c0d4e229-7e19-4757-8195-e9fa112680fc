/**
 * @module 创建数据集成页
 */
import { useEffect, useMemo, useState } from 'react';
import { Col, Row } from 'antd';
import cs from 'classnames';
import { useParams } from 'umi';

import { Loading, useKeepAliveTabs } from '@/components';
import { useRightsHook } from '@/hooks';
import { INGESTION_TYPE_LIST_ONLINE } from '@/modules/DataIngestion/constants';
import { cellListService } from '@/modules/DataIngestion/services';

type INGESTION_TYPE_LIST_TYPE = typeof INGESTION_TYPE_LIST_ONLINE;
type INGESTION_ITEM = INGESTION_TYPE_LIST_TYPE[number];

export const Create = () => {
  const [cellList, setCellList] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const { businessFlowId } = useParams();
  const { closeCurrentTab, openTab } = useKeepAliveTabs();

  // 过滤掉没有权限的模块
  const { hasRoleRights } = useRightsHook();
  const authList = useMemo(() => {
    return INGESTION_TYPE_LIST_ONLINE.filter(x => hasRoleRights(x.rightsCode));
  }, [INGESTION_TYPE_LIST_ONLINE]);

  const fetchCellList = async () => {
    try {
      setLoading(true);
      const res = await cellListService.getAll();
      if (res?.code === '0000') {
        setCellList(res?.data ?? []);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCellList();
  }, []);

  const hasCell = !!cellList?.length;
  const enabledIngestionTypes = ['file', 'tcp-udp', 'kafka', 'syslog', 'archive', 'jdbc', 'logstash'];

  const onCreateDataIngestion = (currentItem: INGESTION_ITEM) => {
    return () => {
      if (isCellTypeAndNoCell(currentItem.key)) {
        return;
      }
      if (!enabledIngestionTypes.includes(currentItem.key)) {
        return;
      }
      closeCurrentTab(() => {
        openTab(`/data-dev/dev/data-ingestion/online/${currentItem.key}?businessFlowId=${businessFlowId}`, {
          state: {
            title: `创建${currentItem.title}`,
          },
        });
      });
    };
  };

  const isCellTypeAndNoCell = (key: string) => {
    const cellTypes = INGESTION_TYPE_LIST_ONLINE.map(x => x.key);
    return cellTypes.includes(key) && !hasCell;
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className='p-5'>
      <Row gutter={[16, 16]}>
        {authList.map(item => (
          <Col key={item.key} span={8}>
            <div
              onClick={onCreateDataIngestion(item)}
              className={cs('box-shadow rounded-sm cursor-pointer pb-2 h-full flex flex-col text-center items-center', {
                grayscale: isCellTypeAndNoCell(item.key),
              })}
            >
              <img src={item.icon} width={109} className='flex-1 my-4' />
              <div className='font-medium text-gray-8 text-base'>{item.title}</div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};
