import { useEffect, useState } from 'react';
import { Descriptions, Tag } from 'antd';

import { AgentListTableView } from '@/modules/DataIngestion/components/CellAgentList/modal/AgentListTableView';
import { useConfigDetail } from '@/modules/DataIngestion/hooks/useConfigDetail';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { DataSourceApi } from '@/services';

interface Props {
  detail: IngestionCellBasedInfo;
}

const collectOffSetOptions = {
  earliest: '全量',
  latest: '首次最新',
};

export const InputConfigDetail = (props: Props) => {
  const { detail } = props;
  const { detailInfo } = useConfigDetail('KAFKA', detail);
  const [dataSourceOptions, setDataSourceOptions] = useState([]);

  useEffect(() => {
    getDataSource();
  }, []);

  const getDataSource = async () => {
    const { data } = await DataSourceApi.getListByPlatform('KAFKA');
    setDataSourceOptions(data);
  };

  const baseItems = [
    {
      key: '数据标签',
      label: '数据标签',
      children: <>
        {(detailInfo?.tagList || []).map(value => <Tag key={value}>
          {value}
        </Tag>)}
      </>,
    },
    {
      key: 'dsId',
      label: '数据源',
      children: <div>{dataSourceOptions.find(item => item.id === detailInfo?.dsId)?.name}</div>,
    },
    {
      key: 'topicName',
      label: '主题',
      children: <div>{detailInfo?.topicName}</div>,
    },
    {
      key: 'collectOffSet',
      label: '缓存策略',
      children: <div>{collectOffSetOptions?.[detailInfo?.collectOffSet]}</div>,
    },
  ];

  return <>
    <Descriptions
      column={1}
      items={baseItems}
      className='pt-5'
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
    <AgentListTableView value={detail?.agentSettingList} />
  </>;
};
