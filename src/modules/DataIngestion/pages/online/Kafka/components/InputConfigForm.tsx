import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Button, Form, Radio } from 'antd';
import { FormLayout } from 'antd/es/form/Form';
import { Link } from 'umi';

import { useKeepAliveTabs } from '@/components';
import { useRightsHook } from '@/hooks';
import { CellAgentList, DataSource, DataTag, Topic } from '@/modules/DataIngestion/components';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { ingestionDataToForm } from '@/modules/DataIngestion/utils';
import { dataSourceManagement } from '@/routes';
import { handleOnBeforeUnload, requiredByMsg } from '@/utils';

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 16, xl: 24, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

interface Props {
  useAgentList?: boolean;
  form: FormInstance;
  detail: IngestionCellBasedInfo;
  isMultiple?: boolean;
  initialValues?: object;
}

export const _InputConfigForm = (props: Props, ref: ForwardedRef<{ validateFields }>) => {
  const { useAgentList = true, form, detail, initialValues, isMultiple } = props;
  const { setPageInfo } = useKeepAliveTabs();
  const { hasRights } = useRightsHook();
  const [cellForm] = Form.useForm();
  const [seniorCellForm] = Form.useForm();
  const [inputForm] = Form.useForm();

  const [dsId, setDsId] = useState<string>('');

  const setConfirm = (isDirty: boolean) => {
    setPageInfo({ isDirty });
    window.onbeforeunload = isDirty ? handleOnBeforeUnload('当前内容未保存，确定离开此界面？') : null;
  };

  useImperativeHandle(ref, () => {
    return {
      validateFields: async () => {
        const cellValues = await cellForm.validateFields();
        const seniorCellValues = await seniorCellForm.validateFields();
        const inputValues = await inputForm.validateFields();
        return {
          ...cellValues,
          ...seniorCellValues,
          ...inputValues,
        };
      },
    };
  });

  useEffect(() => {
    if (detail) {
      setDsId(detail?.setting?.input?.dsId);
      cellForm?.setFieldsValue(ingestionDataToForm(detail));
      seniorCellForm?.setFieldsValue(ingestionDataToForm(detail));
      inputForm?.setFieldsValue(ingestionDataToForm(detail));
    }
  }, [detail]);

  return (
    <>
      <Form
        onChange={() => setConfirm(true)}
        form={inputForm}
        initialValues={initialValues}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
      >
        <div className='pt-5'>
          <Form.Item
            name='tagList'
            label='数据标签'
            className='pl-9'
            help='支持用户通过标签功能对数据进行定义，通过检索系统字段@tags轻松搜索到该类型数据。'
          >
            <DataTag />
          </Form.Item>

          <div className='flex items-center flex-nowrap'>
            <Form.Item
              wrapperCol={{ xxl: 24, xl: 24, lg: 24, xs: 24, sm: 24 }}
              required={!isMultiple}
              name='dsId'
              label='数据源'
              className='data-source fixed-width pl-9 items-center'
              rules={isMultiple ? [] : [{ validator: requiredByMsg('请选择') }]}
            >
              <DataSource form={inputForm} onChange={setDsId} />
            </Form.Item>
            <Form.Item>
              <Button ghost type='primary' className='ml-3' disabled={!hasRights('P005002001')}>
                {hasRights('P005002001') ? (
                  <Link to={dataSourceManagement} target='_blank'>
                    快速创建地址
                  </Link>
                ) : (
                  '快速创建地址'
                )}
              </Button>
            </Form.Item>
          </div>
          <Form.Item
            required={!isMultiple}
            name='topicName'
            wrapperCol={{ xxl: 24, xl: 24, lg: 24, xs: 24, sm: 24 }}
            label='主题'
            className='fixed-width pl-9 items-center'
            rules={isMultiple ? [] : [{ validator: requiredByMsg('请选择') }]}
          >
            <Topic dsId={dsId} />
          </Form.Item>

          <Form.Item
            required={!isMultiple}
            name='collectOffSet'
            label='缓存策略'
            className='pl-9'
            rules={isMultiple ? [] : [{ validator: requiredByMsg('请选择') }]}
            help='配置队列存量数据处理策略，创建时设置，以后不可修改。
            全量:从全量的队列缓存数据开始处理。'
          >
            <Radio.Group>
              <Radio value='earliest'>全量</Radio>
              <Radio value='latest'>首次最新</Radio>
            </Radio.Group>
          </Form.Item>
        </div>
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={cellForm}
        className='flex flex-col pb-3 px-0 data-ingestion-form'
        {...formLayout}
      >
        {useAgentList && (
          <CellAgentList cellForm={cellForm} form={form} seniorCellForm={seniorCellForm} inputForm={inputForm} />
        )}
      </Form>
    </>
  );
};

export const InputConfigForm = forwardRef(_InputConfigForm);
