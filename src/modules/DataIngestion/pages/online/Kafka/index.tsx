import { useEffect, useRef } from 'react';
import { Form } from 'antd';
import { cloneDeep } from 'lodash';
import { useParams } from 'umi';

import { KeepAliveToTab, useKeepAliveTabs } from '@/components';
import { useIngestionForm } from '@/modules/DataIngestion/hooks';
import { ingestionDataToForm } from '@/modules/DataIngestion/utils';

import '../index.less';

import { CommonConfig } from '../shared';

import { InputConfigDetail } from './components/InputConfigDetail';
import { InputConfigForm } from './components';

const Page = () => {
  const { cloneId } = useParams();
  const { setPageInfo } = useKeepAliveTabs();
  const { detail, onSave, onPublish, isEdit, onRefresh, setIsEdit } = useIngestionForm();

  const [form] = Form.useForm();
  const cellId = Form.useWatch('cellId', form);
  const title = new URLSearchParams(window.location.search).get('title');
  const inputForm = useRef(null);
  const initialValues = {
    topicName: [],
    collectOffSet: 'earliest',
    tagList: [],
  };

  useEffect(() => {
    if (detail) {
      const initialValues = cloneDeep(detail);
      if (cloneId) {
        Object.assign(initialValues, { name: title });
      }
      setPageInfo({ title: initialValues?.name });
      form?.setFieldsValue(ingestionDataToForm(initialValues));
    }
  }, [detail]);

  return (
    <CommonConfig
      form={form}
      inputForm={inputForm}
      onSave={onSave}
      onPublish={onPublish}
      onRefresh={onRefresh}
      detail={detail!}
      cellId={cellId}
      isEdit={isEdit}
      setIsEdit={setIsEdit}
    > 
      {
        isEdit ? <InputConfigForm detail={detail!} initialValues={initialValues} form={form} ref={inputForm} />
          : <InputConfigDetail detail={detail} />
      }
    </CommonConfig>
  );
};

export const Kafka = () => {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
};
