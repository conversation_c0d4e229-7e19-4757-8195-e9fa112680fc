import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Checkbox, Form, Input } from 'antd';
import { FormLayout } from 'antd/es/form/Form';

import { SingleCheckbox, useKeepAliveTabs } from '@/components';
import {
  CellAgentList,
  Charset,
  CharsetProps,
  CustomField,
  DataTag,
  FilterList,
} from '@/modules/DataIngestion/components';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { ingestionDataToForm } from '@/modules/DataIngestion/utils';
import { buildAntdFormValidator, handleOnBeforeUnload, isRegExp, requiredByMsg } from '@/utils';

import { AdvancedConfig } from './AdvancedConfig';

const formLayout = {
  labelCol: { style: { width: '172px' } },
  wrapperCol: { xxl: 16, xl: 24, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

interface Props {
  useAgentList?: boolean;
  form: FormInstance;
  detail: IngestionCellBasedInfo;
  isMultiple?: boolean;
  initialValues?: object;
}

export const _InputConfigForm = (props: Props, ref: ForwardedRef<{ validateFields }>) => {
  const { useAgentList = true, form, detail, initialValues, isMultiple } = props;
  const { setPageInfo } = useKeepAliveTabs();
  const [cellForm] = Form.useForm();
  const [seniorCellForm] = Form.useForm();
  const [inputForm] = Form.useForm();

  const setConfirm = (isDirty: boolean) => {
    setPageInfo({ isDirty });
    window.onbeforeunload = isDirty ? handleOnBeforeUnload('当前内容未保存，确定离开此界面？') : null;
  };

  const excludeList = Form.useWatch('exclude', inputForm);
  const includeList = Form.useWatch('include', inputForm);
  const innerBlackList = Form.useWatch('innerBlackList', inputForm);
  const blackList = Form.useWatch('blacklist', inputForm);
  const innerWhiteList = Form.useWatch('innerWhiteList', inputForm);
  const whiteList = Form.useWatch('whitelist', inputForm);

  const [useBlacklist, setBlacklist] = useState<boolean>(false);
  const [useWhitelist, setWhitelist] = useState<boolean>(false);
  const [useInnerWhiteList, setInnerWhiteList] = useState<boolean>(false);
  const [useInnerBlackList, setInnerBlackList] = useState<boolean>(false);
  const [useExclude, setExclude] = useState<boolean>(false);
  const [useInclude, setInclude] = useState<boolean>(false);

  useEffect(() => {
    if (!innerBlackList?.length) {
      inputForm.setFieldValue('useInnerBlackList', false);
    } else {
      inputForm.setFieldValue('useInnerBlackList', true);
    }
  }, [innerBlackList]);

  useEffect(() => {
    if (!innerWhiteList?.length) {
      inputForm.setFieldValue('useInnerWhiteList', false);
    } else {
      inputForm.setFieldValue('useInnerWhiteList', true);
    }
  }, [innerWhiteList]);

  useEffect(() => {
    if (!excludeList?.length) {
      inputForm.setFieldValue('useExclude', false);
    } else {
      inputForm.setFieldValue('useExclude', true);
    }
  }, [excludeList]);

  useEffect(() => {
    if (!includeList?.length) {
      inputForm.setFieldValue('useInclude', false);
    } else {
      inputForm.setFieldValue('useInclude', true);
    }
  }, [includeList]);

  useEffect(() => {
    if (!blackList?.length) {
      inputForm.setFieldValue('useBlacklist', false);
    } else {
      inputForm.setFieldValue('useBlacklist', true);
    }
  }, [blackList]);

  useEffect(() => {
    if (!whiteList?.length) {
      inputForm.setFieldValue('useWhitelist', false);
    } else {
      inputForm.setFieldValue('useWhitelist', true);
    }
  }, [whiteList]);

  useImperativeHandle(ref, () => {
    return {
      validateFields: async () => {
        const cellValues = await cellForm.validateFields();
        const seniorCellValues = await seniorCellForm.validateFields();
        const inputValues = await inputForm.validateFields();
        return {
          ...cellValues,
          ...seniorCellValues,
          ...inputValues,
        };
      },
    };
  });

  useEffect(() => {
    if (detail) {
      setBlacklist(detail?.setting?.input?.useBlacklist);
      setWhitelist(detail?.setting?.input?.useWhitelist);
      setInnerWhiteList(detail?.setting?.input?.useInnerWhiteList);
      setInnerBlackList(detail?.setting?.input?.useInnerBlackList);
      setExclude(detail?.setting?.input?.useExclude);
      setInclude(detail?.setting?.input?.useInclude);
      cellForm?.setFieldsValue(ingestionDataToForm(detail));
      seniorCellForm?.setFieldsValue(ingestionDataToForm(detail));
      inputForm?.setFieldsValue(ingestionDataToForm(detail));
    }
  }, [detail]);

  const charsetFilter: CharsetProps['optionFilter'] = opt => {
    return !['RECOMMEND'].includes(opt.value);
  };

  return (
    <>
      <Form
        onChange={() => setConfirm(true)}
        form={inputForm}
        initialValues={initialValues}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
      >
        <div className='pt-5'>
          <div className='flex items-start pl-9'>
            <Form.Item
              required={!isMultiple}
              wrapperCol={{ span: 24 }}
              name='path'
              label='路径'
              className='file-path'
              rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入文件路径') }]}
            >
              <Input.TextArea style={{ resize: 'both' }} placeholder='请填写文件路径，多路径以“；”间隔' />
            </Form.Item>
            <Form.Item name='recursive' className='ml-2 relative -top-1'>
              <SingleCheckbox label='包含子文件' />
            </Form.Item>
          </div>
          <Form.Item
            name='tagList'
            label='数据标签'
            className='pl-9'
            help='支持用户通过标签功能对数据进行定义，通过检索系统字段@tags轻松搜索到该类型数据。'
          >
            <DataTag />
          </Form.Item>
          <div className='pl-9'>
            <CustomField name='customParam' label='自定义字段' className='fixed-width' />
          </div>
          <Form.Item required={!isMultiple} name='charset' label='字符集' className='pl-9'>
            <Charset optionFilter={charsetFilter} />
          </Form.Item>
          <Form.Item
            name='multiline'
            label='多行合并'
            className='fixed-width pl-9'
            rules={
              isMultiple
                ? []
                : [
                  {
                    validator: buildAntdFormValidator(v => (!isRegExp(v) ? '请输入正则表达式' : true)),
                  },
                ]
            }
          >
            <Input placeholder='请填写正则表达式' className='w-[460px]' />
          </Form.Item>
          <Form.Item className='pl-9' label='过滤文件'>
            <div className='flex items-center justify-start'>
              <div className='flex'>
                <Form.Item
                  className='m-0 flex-1 whitespace-nowrap '
                  style={{ width: '120px' }}
                  name='useBlacklist'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setBlacklist(checked);
                      const blacklist = inputForm.getFieldValue('blacklist');
                      inputForm.setFieldValue('blacklist', blacklist?.length ? blacklist : [undefined]);
                    }}
                  >
                    文件黑名单
                  </Checkbox>
                </Form.Item>
                <Form.Item
                  className='m-0 flex-1 whitespace-nowrap'
                  style={{ width: '120px' }}
                  name='useWhitelist'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setWhitelist(checked);
                      const whitelist = inputForm.getFieldValue('whitelist');
                      inputForm.setFieldValue('whitelist', whitelist?.length ? whitelist : [undefined]);
                    }}
                  >
                    文件白名单
                  </Checkbox>
                </Form.Item>
              </div>
            </div>
          </Form.Item>

          {useBlacklist && (
            <FilterList name='blacklist' label='文件黑名单' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入文件路径') }]}>
                <Input placeholder='输入文件路径，需含通配符或具体的文件名，多路径以；间隔' />
              </Form.Item>
            </FilterList>
          )}

          {useWhitelist && (
            <FilterList name='whitelist' label='文件白名单' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入文件路径') }]}>
                <Input placeholder='输入文件路径，需含通配符或具体的文件名，多路径以；间隔' />
              </Form.Item>
            </FilterList>
          )}

          <Form.Item className='pl-9' label='过滤子文件'>
            <div className='flex items-center justify-start'>
              <div className='flex'>
                <Form.Item
                  className='m-0 flex-1 whitespace-nowrap'
                  style={{ width: '120px' }}
                  name='useInnerBlackList'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setInnerBlackList(checked);
                      const innerBlackList = inputForm.getFieldValue('innerBlackList');
                      inputForm.setFieldValue('innerBlackList', innerBlackList?.length ? innerBlackList : [undefined]);
                    }}
                  >
                    子文件黑名单
                  </Checkbox>
                </Form.Item>
                <Form.Item
                  className='m-0 flex-1 whitespace-nowrap'
                  style={{ width: '120px' }}
                  name='useInnerWhiteList'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setInnerWhiteList(checked);
                      const innerWhiteList = inputForm.getFieldValue('innerWhiteList');
                      inputForm.setFieldValue('innerWhiteList', innerWhiteList?.length ? innerWhiteList : [undefined]);
                    }}
                  >
                    子文件白名单
                  </Checkbox>
                </Form.Item>
              </div>
            </div>
          </Form.Item>

          {useInnerBlackList && (
            <FilterList name='innerBlackList' label='子文件黑名单' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入文件路径') }]}>
                <Input placeholder='输入文件路径，需含通配符或具体的文件名，多路径以；间隔' />
              </Form.Item>
            </FilterList>
          )}

          {useInnerWhiteList && (
            <FilterList name='innerWhiteList' label='子文件白名单' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入文件路径') }]}>
                <Input placeholder='输入文件路径，需含通配符或具体的文件名，多路径以；间隔' />
              </Form.Item>
            </FilterList>
          )}

          <Form.Item className='pl-9' label='过滤内容'>
            <div className='flex items-center justify-start'>
              <div className='flex justify-between'>
                <Form.Item
                  className='m-0 flex-none whitespace-nowrap '
                  style={{ width: '110px' }}
                  name='useExclude'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setExclude(checked);
                      const exclude = inputForm.getFieldValue('exclude');
                      inputForm.setFieldValue('exclude', exclude?.length ? exclude : [undefined]);
                    }}
                  >
                    排除内容
                  </Checkbox>
                </Form.Item>
                <Form.Item
                  className='m-0 flex-none whitespace-nowrap'
                  style={{ width: '110px' }}
                  name='useInclude'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setInclude(checked);
                      const include = inputForm.getFieldValue('include');
                      inputForm.setFieldValue('include', include?.length ? include : [undefined]);
                    }}
                  >
                    仅包含内容
                  </Checkbox>
                </Form.Item>
              </div>
            </div>
          </Form.Item>

          {useExclude && (
            <FilterList name='exclude' label='排除内容' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入内容') }]}>
                <Input placeholder='输入内容，需含通配符或具体的内容，多内容以；间隔' />
              </Form.Item>
            </FilterList>
          )}

          {useInclude && (
            <FilterList name='include' label='仅包含内容' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入内容') }]}>
                <Input placeholder='输入内容，需含通配符或具体的内容，多内容以；间隔' />
              </Form.Item>
            </FilterList>
          )}
        </div>
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={seniorCellForm}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
      >
        <AdvancedConfig form={seniorCellForm} />
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={cellForm}
        className='flex flex-col pb-3 px-0 data-ingestion-form'
        {...formLayout}
      >
        {useAgentList && (
          <CellAgentList cellForm={cellForm} form={form} seniorCellForm={seniorCellForm} inputForm={inputForm} />
        )}
      </Form>
    </>
  );
};

export const InputConfigForm = forwardRef(_InputConfigForm);
