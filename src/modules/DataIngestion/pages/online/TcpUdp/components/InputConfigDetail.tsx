import { Descriptions, Tag } from 'antd';

import { AgentListTableView } from '@/modules/DataIngestion/components/CellAgentList/modal/AgentListTableView';
import { CHARSET_OPTIONS, FIELD_DELIMITER_OPTIONS, PROTOCOL_OPTIONS } from '@/modules/DataIngestion/constants';
import { useConfigDetail } from '@/modules/DataIngestion/hooks/useConfigDetail';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';

import { AdvancedConfig } from './AdvancedConfig';

interface Props {
  detail: IngestionCellBasedInfo;
}

export const InputConfigDetail = (props: Props) => {
  const { detail } = props;
  const { detailInfo, advancedConfig } = useConfigDetail('TCP_UDP', detail);

  const baseItems = [
    {
      key: 'port',
      label: '监听窗口',
      children: <div>{ detailInfo?.port }</div>,
    },
    {
      key: 'tagList',
      label: '数据标签',
      children: <>
        {(detailInfo?.tagList || []).map(value => <Tag key={value}>
          {value}
        </Tag>)}
      </>,
    },
    {
      key: 'protocol',
      label: '协议',
      children: <div>{PROTOCOL_OPTIONS.find(item => item.value === detailInfo?.protocol)?.label}</div>,
    },
    {
      key: 'charset',
      label: '字符集',
      children: <div>{CHARSET_OPTIONS.find(item => item.value === detailInfo?.charset)?.label}</div>,
    },
    {
      key: 'fieldDelimiter',
      label: '分割方式',
      children: <div>{FIELD_DELIMITER_OPTIONS.find(item => item.value === detailInfo?.fieldDelimiter)?.label}</div>,
    },
    ...detailInfo?.fieldDelimiter === 'multiline' ? [
      {
        key: 'multiline',
        label: '多行合并',
        children: <div>{ detailInfo?.multiline }</div>,
      },
    ] : [],
  ];

  return <>
    <Descriptions
      column={1}
      items={baseItems}
      className='pt-5'
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
    <AdvancedConfig readonly configData={advancedConfig} />
    <AgentListTableView value={detail?.agentSettingList} />
  </>;
};
