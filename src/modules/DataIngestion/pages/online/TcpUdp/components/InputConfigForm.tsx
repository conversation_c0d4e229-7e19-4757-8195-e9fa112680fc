import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Form, Input, InputNumber } from 'antd';
import { FormLayout } from 'antd/es/form/Form';

import { useKeepAliveTabs } from '@/components';
import {
  CellAgentList,
  Charset,
  CharsetProps,
  DataTag,
  FieldDelimiter,
  Protocol,
} from '@/modules/DataIngestion/components';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { ingestionDataToForm } from '@/modules/DataIngestion/utils';
import { buildAntdFormValidator, handleOnBeforeUnload, isRegExp, requiredByMsg } from '@/utils';

import { AdvancedConfig } from './AdvancedConfig';

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 16, xl: 24, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

interface Props {
  useAgentList?: boolean;
  form: FormInstance;
  detail: IngestionCellBasedInfo;
  isMultiple?: boolean;
  initialValues?: object;
}

export const _InputConfigForm = (props: Props, ref: ForwardedRef<{ validateFields }>) => {
  const { useAgentList = true, form, detail, initialValues, isMultiple } = props;
  const { setPageInfo } = useKeepAliveTabs();
  const [cellForm] = Form.useForm();
  const [seniorCellForm] = Form.useForm();
  const [inputForm] = Form.useForm();

  const setConfirm = (isDirty: boolean) => {
    setPageInfo({ isDirty });
    window.onbeforeunload = isDirty ? handleOnBeforeUnload('当前内容未保存，确定离开此界面？') : null;
  };

  useImperativeHandle(ref, () => {
    return {
      validateFields: async () => {
        const cellValues = await cellForm.validateFields();
        const seniorCellValues = await seniorCellForm.validateFields();
        const inputValues = await inputForm.validateFields();
        return {
          ...cellValues,
          ...seniorCellValues,
          ...inputValues,
        };
      },
    };
  });
  const [protocol, setProtocol] = useState<string>('TCP');
  const [fieldDelimiter, setFieldDelimiter] = useState<string>('linebase');

  const charsetFilter: CharsetProps['optionFilter'] = opt => {
    return !['RECOMMEND', 'AUTO'].includes(opt.value);
  };

  useEffect(() => {
    if (detail) {
      setProtocol(detail?.setting?.input?.protocol);
      setFieldDelimiter(detail?.setting?.input?.fieldDelimiter);
      cellForm?.setFieldsValue(ingestionDataToForm(detail));
      seniorCellForm?.setFieldsValue(ingestionDataToForm(detail));
      inputForm?.setFieldsValue(ingestionDataToForm(detail));
    }
  }, [detail]);

  return (
    <>
      <Form
        onChange={() => setConfirm(true)}
        form={inputForm}
        initialValues={initialValues}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
      >
        <div className='pt-5'>
          <Form.Item
            name='port'
            required={!isMultiple}
            label='监听窗口'
            className='pl-9 fixed-width'
            rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入') }]}
          >
            <InputNumber min={0} max={65533} placeholder='端口范围0-65533' />
          </Form.Item>

          <Form.Item
            name='tagList'
            label='数据标签'
            className='pl-9'
            help='支持用户通过标签功能对数据进行定义，通过检索系统字段@tags轻松搜索到该类型数据。'
          >
            <DataTag />
          </Form.Item>

          <Form.Item
            required={!isMultiple}
            name='protocol'
            label='协议'
            className='pl-9'
            rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入') }]}
          >
            <Protocol onChange={setProtocol} />
          </Form.Item>

          <Form.Item required={!isMultiple} name='charset' label='字符集' className='pl-9'>
            <Charset optionFilter={charsetFilter} />
          </Form.Item>

          <Form.Item required={!isMultiple} name='fieldDelimiter' label='分割方式' className='pl-9'>
            <FieldDelimiter
              form={inputForm}
              fieldDelimiter={fieldDelimiter}
              protocol={protocol}
              onChange={setFieldDelimiter}
            />
          </Form.Item>

          {fieldDelimiter == 'multiline' && (
            <Form.Item
              name='multiline'
              label='多行合并'
              className='fixed-width pl-9'
              rules={[
                {
                  validator: buildAntdFormValidator(v => (!isRegExp(v) ? '请输入正则表达式' : true)),
                },
              ]}
            >
              <Input placeholder='请填写正则表达式' className='w-[460px]' />
            </Form.Item>
          )}
        </div>
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={seniorCellForm}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
      >
        <AdvancedConfig form={seniorCellForm} />
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={cellForm}
        className='flex flex-col pb-3 px-0 data-ingestion-form'
        {...formLayout}
      >
        {useAgentList && (
          <CellAgentList cellForm={cellForm} form={form} seniorCellForm={seniorCellForm} inputForm={inputForm} />
        )}
      </Form>
    </>
  );
};

export const InputConfigForm = forwardRef(_InputConfigForm);
