/**
 * @module 数据集成-数据输出配置
 */
import { useEffect, useMemo, useState } from 'react';
import { Button, Form, FormInstance, message } from 'antd';
import { FormLayout } from 'antd/es/form/Form';
import { history } from 'umi';

import { QuicklyCreateKafka } from '@/components/business/ui/QuicklyCreateKafka';
import { useRightsHook } from '@/hooks';
import { DEPLOY_STATUS_MAP } from '@/modules/DataDev/constants';
import { useTableDeployHook } from '@/modules/DataDev/hooks/useTableDeployHook';
import { SourceTableSelect } from '@/modules/DataIngestion/components';
import { cellListService } from '@/modules/DataIngestion/services';
import { TableDeployApi } from '@/services/TableDeployApi';

import './style.less';

interface Props {
  businessFlowId: string;
  form: FormInstance;
  cellId: string;
  setConfirm: (boolean) => void;
}

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 12, xl: 16, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

export const OutputConfig = (props: Props) => {
  const { businessFlowId, form, setConfirm, cellId } = props;
  const { hasRights } = useRightsHook();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const { renovate } = useTableDeployHook();
  const [data, setData] = useState<any[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const onModalOk = async temp => {
    try {
      temp.tableDeploy.businessFlowId = businessFlowId;
      setConfirmLoading(true);
      const { code, msg, data } = await TableDeployApi.createTableDeploy(temp);
      if (code == '0000') {
        const { status, id, businessFlowId, name, tbId, tbName, platform, actionType, deployType, dsId } = data;
        if (status === 'FAILURE') {
          history.push(`/data-dev/dev/management/kafka/${id}`, {
            actionType,
            deployType,
            id,
            dsId,
            tbId,
            name,
            tbName,
            platform,
            businessFlowId,
            flag: true,
          });
        } else if (status === 'DEPLOYING') {
          message.info(DEPLOY_STATUS_MAP.DEPLOYING);
        } else {
          form?.setFieldValue('tbDeployId', id);
          form?.setFieldValue('tbId', tbId);
          message.success(DEPLOY_STATUS_MAP.SUCCESS);
          renovate();
        }
      } else {
        message.error(msg);
      }
      setOpen(false);
      setConfirmLoading(false);
    } catch (error) {
      message.error(error.msg ?? error.message);
      setConfirmLoading(false);
    }
  };

  const getCellData = async () => {
    try {
      const { data } = await cellListService.getAll();
      setData(data ?? []);
    } catch (error) {}
  };

  useEffect(() => {
    getCellData();
  }, []);

  const dsId = useMemo(() => {
    let str = '';
    let index: number = -1;
    if (!data) return '';
    index = data.findIndex(item => item.id === cellId);
    if (index > -1 && data) {
      str = data[index].dsId ?? '';
    }
    return str;
  }, [cellId, data]);

  return (
    <Form
      onChange={() => setConfirm(true)}
      form={form}
      className='flex flex-col pb-3 px-0 data-ingestion-form'
      {...formLayout}
    >
      <div className='outInputConfig'>
        <SourceTableSelect cellId={cellId} form={form} />
        <div className='customCreate pt-5'>
          <Button
            className='customCreateBtn'
            type='link'
            disabled={!hasRights('data_model:write') || !hasRights('data_model:deploy')}
            onClick={() => {
              if (!cellId) return message.error('未选择采集网关');
              setOpen(true);
            }}
          >
            快速创建
          </Button>
        </div>

        {open && (
          <QuicklyCreateKafka
            open={open}
            confirmLoading={confirmLoading}
            dsId={dsId}
            onCancel={() => setOpen(false)}
            onModalOk={onModalOk}
          />
        )}
      </div>
    </Form>
  );
};
