/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useMemo, useState } from 'react';
import { Button, Descriptions, Divider, Form, FormInstance, Input, message, Modal, Spin, Tabs, TabsProps } from 'antd';
import { FormLayout } from 'antd/es/form/Form';
import { useParams } from 'umi';

import { SectionCaptain, useKeepAliveTabs } from '@/components';
import { useRightsHook } from '@/hooks';
import { useBusinessFlowStore } from '@/modules/DataDev/store/useBusinessFlowStore';
import { CellSelect } from '@/modules/DataIngestion/components';
import { CellDetail } from '@/modules/DataIngestion/components/CellDetail';
import { useIngestionType, useRemoveIngestion } from '@/modules/DataIngestion/hooks';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { handleOnBeforeUnload, requiredByMsg } from '@/utils';

import '../index.less';

import { OutputConfig as LogstashOutputConfig } from '../Logstash/components/OutputConfig';
import { OutputConfigView as LogstashOutputConfigView } from '../Logstash/components/OutputConfigView';
import { OutputConfig } from '../shared';

import { OutputConfigView } from './OutputConfigView';

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 12, xl: 16, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

enum collectorTitle {
  'tcp-udp' = 'TCP/UDP同步配置',
  'file' = '文件同步配置',
  'kafka' = 'Kafka同步配置',
  'syslog' = 'SYSLOG同步配置',
  'archive' = '归档文件采集配置',
  'jdbc' = '数据库同步配置',
  'logstash' = '自定义同步配置',
}

interface Props {
  children: ReactNode;
  form: FormInstance;
  inputForm;
  onPublish: (info) => void;
  onSave: (info) => void;
  onRefresh: () => void;
  cellId: string;
  detail: IngestionCellBasedInfo;
  hideInsert?: boolean;
  isEdit?: boolean;
  setIsEdit: (isEdit: boolean) => void;
}

export const CommonConfig = (props: Props) => {
  const { id } = useParams();
  const { hasRights } = useRightsHook();
  const { setPageInfo } = useKeepAliveTabs();
  // eslint-disable-next-line max-len
  const { children, detail, form, inputForm, onSave, onPublish, onRefresh, cellId, hideInsert = false,  isEdit, setIsEdit } = props;
  const ingestionType = useIngestionType();
  const [outputForm] = Form.useForm();

  const routerId = Form.useWatch('routerId', form);
  const [loading, setLoading] = useState<boolean>(false);
  const [publishLoading, setPublishLoading] = useState<boolean>(false);
  const [refreshLoading, setRefreshLoading] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState<string>();
  const removeIngestion = useRemoveIngestion();
  const { closeCurrentTab } = useKeepAliveTabs();
  const store = useBusinessFlowStore();

  const outputCellId = useMemo(() => {
    if (routerId) return routerId;
    return cellId;
  }, [cellId, routerId]);

  const setConfirm = (isDirty: boolean) => {
    setPageInfo({ isDirty });
    window.onbeforeunload = isDirty ? handleOnBeforeUnload('当前内容未保存，确定离开此界面？') : null;
  };

  useEffect(() => {
    if (detail) {
      outputForm?.setFieldsValue(detail);
    }
  }, [detail]);

  const businessFlowId = useMemo(() => {
    return new URLSearchParams(window.location.search).get('businessFlowId') ?? detail?.businessFlowId ?? '';
  }, [detail]);

  const formatValues = values => {
    if (ingestionType == 'syslog') {
      values.excludes = values.excludes?.map(item => {
        if (item.field) {
          return { [item.field]: item.value };
        } else {
          return item;
        }
      });
      values.includes = values?.includes?.map(item => {
        if (item.field) {
          return { [item.field]: item.value };
        } else {
          return item;
        }
      });
      values?.agentSettingList?.forEach(item => {
        if (item.setting?.input?.excludes) {
          item.setting.input.excludes = item.setting.input.excludes.map(child => {
            if (child.field) {
              return { [child.field]: child.value };
            } else {
              return child;
            }
          });
        }
        if (item.setting?.input?.includes) {
          item.setting.input.includes = item.setting.input.includes.map(child => {
            if (child.field) {
              return { [child.field]: child.value };
            } else {
              return child;
            }
          });
        }
      });
    }
    if (ingestionType === 'logstash') {
      values?.agentSettingList?.forEach(item => {
        item.setting.input.output = values.output;
        item.setting.input.outputType = values.outputType;
      });
    }
    
    return values;
  };

  const getValues = async () => {
    try {
      const formValues = await form.validateFields();
      const inputValues = await inputForm.current?.validateFields();
      const outputValues = await outputForm.validateFields();
      const values = {
        ...formValues,
        ...inputValues,
        ...outputValues,
      };
      return formatValues(values);
    } catch (e: any) {
      if (e?.errorFields?.length) {
        if (e.errorFields[0]?.name.includes('tbId')) {
          setActiveKey('outputConfig');
        } else {
          setActiveKey('inputConfig');
        }
      }
    }
  };

  const getPublishValues = () => {
    const {
      createTime,
      createUserName,
      createUser,
      updateTime,
      updateUser,
      updateUserName,
      published,
      status,
      setting,
      ...values
    } = detail;

    return formatValues({
      ...values,
      ...setting.input,
    });
  };
  
  const OutputConfigComp = useMemo(() => {
    if (isEdit) {
      return ingestionType === 'logstash' ? LogstashOutputConfig : OutputConfig;
    } else {
      return ingestionType === 'logstash' ? LogstashOutputConfigView : OutputConfigView;
    }
  }, [ingestionType, isEdit]); 

  const tabs: TabsProps['items'] = [
    {
      key: 'inputConfig',
      label: '输入配置',
      forceRender: true,
      children,
    },
    {
      key: 'outputConfig',
      label: '输出配置',
      forceRender: true,
      children: (
        <OutputConfigComp
          businessFlowId={businessFlowId}
          form={outputForm}
          setConfirm={setConfirm}
          cellId={outputCellId}
          detail={detail}
          readonly={!isEdit}
        />
      ),
    },
  ];
  const baseItems = [
    {
      key: '1',
      label: '任务名称',
      children: <div>{detail?.name}</div>,
    },
    {
      key: '2',
      label: '采集网关',
      children: <CellDetail id={detail?.cellId } type='CELL' />,
    },
    ...!hideInsert ? [
      {
        key: '3',
        label: '接入网关',
        children: <CellDetail id={detail?.routerId } type='ROUTER' />,
      },
    ]: [],
  ];

  const onChangeCellId = () => {
    outputForm?.setFieldValue('tbId', undefined);
    outputForm?.setFieldValue('tbDeployId', undefined);
  };

  const handleRefresh = async () => {
    setRefreshLoading(true);
    await onRefresh();
    setRefreshLoading(false);
  };

  const handleDelete = async () => {
    Modal.confirm({
      title: `确定删除「${detail?.name}」`,
      onOk: async () => {
        await removeIngestion?.(detail?.id);
        message.success('删除成功');
        store.fetchData();
        closeCurrentTab();
      },
    });
  };

  return (
    <>
      {
        isEdit && <div className='px-4 py-2 flex items-center'>
          <Button
            className='mr-2'
            loading={loading}
            disabled={!hasRights('data_develop:write', id ? detail?.projectAuth : undefined)}
            onClick={async () => {
              const values = await getValues();
              onSave({ values, setLoading });
            }}
          >
          保存
          </Button>
          <Button
            type='primary'
            className='mr-2'
            loading={publishLoading}
            disabled={!hasRights('data_develop:deploy', id ? detail?.projectAuth : undefined)}
            onClick={async () => {
              const values = await getValues();
              onPublish({ values, setPublishLoading });
            }}
          >
            发布
          </Button>
          {id && <Button type="text" onClick={() => setIsEdit(false)}>取消</Button>}
        </div>
      }
      {
        !isEdit && <div className='px-4 py-2 flex items-center'>
          <Button
            type='text'
            size='small'
            onClick={() => setIsEdit(true)}
            className='mr-2'
            disabled={!hasRights('data_develop:write', id ? detail?.projectAuth : undefined)
              && !hasRights('data_develop:deploy', id ? detail?.projectAuth : undefined)}
          >
            <i className='iconfont icon-rename-line text-sm mr-2'></i>编辑
          </Button>
          <Button
            type='text'
            size='small'
            className='mr-2'
            loading={publishLoading}
            disabled={!hasRights('data_develop:deploy', id ? detail?.projectAuth : undefined)}
            onClick={async () => {
              const values = await getPublishValues();
              onPublish({ values, setPublishLoading });
            }}
          >
            <i className='iconfont icon-near_me-line text-sm mr-2'></i>
            发布
          </Button>
          <Button type='text' size='small' className='mr-2' onClick={handleRefresh}>
            <span className='text-gray-6'>
              <i className='iconfont icon-refresh-line text-sm mr-2'></i>刷新
            </span>
          </Button>
          <Button
            type='text'
            size='small'
            onClick={() => handleDelete()}
            disabled={!hasRights('data_develop:write', detail?.projectAuth)}
          >
            <i className='iconfont icon-bin-line text-sm mr-2'></i>删除
          </Button>
        </div>
      }
      <Divider orientation='left' className='mt-0 mb-0' />
      <Spin spinning={refreshLoading}>
        <SectionCaptain title={collectorTitle[ingestionType]} className='mb-[11px] ml-[15px] mt-2' />
        {
          isEdit && <Form
            onChange={() => setConfirm(true)}
            form={form}
            className='flex flex-col pb-3 px-0 data-ingestion-form'
            {...formLayout}
          >
            <div className='pl-9'>
              <Form.Item
                required
                label='任务名称'
                name='name'
                rules={[{ validator: requiredByMsg('请输入任务名称') }]}
                className='fixed-width'
              >
                <Input placeholder='作为任务显示名称,可以输入任何内容,不允许重名' />
              </Form.Item>
              <Form.Item
                required
                name='cellId'
                label='选择采集网关'
                rules={[{ validator: requiredByMsg('请选择采集网关') }]}
                className='fixed-width'
              >
                <CellSelect type='CELL' onChange={onChangeCellId} />
              </Form.Item>
              {
                !hideInsert && 
                <Form.Item
                  name='routerId'
                  label='选择接入网关'
                  className='fixed-width'
                  help='如果希望当前任务的采集器将数据上送给另外一个router，请选择接入网关，输出的贴源kafka模型将会限定为该接入网关关联的kafka。接入网关由管理员在网关管理中维护'
                >
                  <CellSelect type='ROUTER' onChange={onChangeCellId} />
                </Form.Item>
              }
            </div>
          </Form>
        }
        {
          !isEdit && <Descriptions
            column={1}
            items={baseItems}
            labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
          />
        }
        <Tabs items={tabs} tabBarStyle={{ paddingLeft: '16px' }} activeKey={activeKey} onChange={setActiveKey}></Tabs>
      </Spin>
      
    </>
  );
};
