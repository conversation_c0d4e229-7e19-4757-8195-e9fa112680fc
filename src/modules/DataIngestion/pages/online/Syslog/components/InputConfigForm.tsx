import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Checkbox, Form, InputNumber } from 'antd';
import { FormLayout } from 'antd/es/form/Form';

import { useKeepAliveTabs } from '@/components';
import {
  CellAgentList,
  Charset,
  CharsetProps,
  CodingStandards,
  ContentInput,
  DataTag,
  FilterList,
  Protocol,
} from '@/modules/DataIngestion/components';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { AdvancedConfig } from '@/modules/DataIngestion/pages/online/TcpUdp/components/AdvancedConfig';
import { ingestionDataToForm } from '@/modules/DataIngestion/utils';
import { handleOnBeforeUnload, requiredByMsg } from '@/utils';

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 16, xl: 24, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

interface Props {
  useAgentList?: boolean;
  form: FormInstance;
  detail: IngestionCellBasedInfo;
  isMultiple?: boolean;
  initialValues?: object;
}

export const _InputConfigForm = (props: Props, ref: ForwardedRef<{ validateFields }>) => {
  const { useAgentList = true, form, detail, initialValues, isMultiple } = props;
  const { setPageInfo } = useKeepAliveTabs();
  const [cellForm] = Form.useForm();
  const [seniorCellForm] = Form.useForm();
  const [inputForm] = Form.useForm();

  const setConfirm = (isDirty: boolean) => {
    setPageInfo({ isDirty });
    window.onbeforeunload = isDirty ? handleOnBeforeUnload('当前内容未保存，确定离开此界面？') : null;
  };

  const excludeList = Form.useWatch('excludes', inputForm);
  const includeList = Form.useWatch('includes', inputForm);

  useEffect(() => {
    if (!excludeList?.length) {
      inputForm.setFieldValue('useExclude', false);
    } else {
      inputForm.setFieldValue('useExclude', true);
    }
  }, [excludeList]);

  useEffect(() => {
    if (!includeList?.length) {
      inputForm.setFieldValue('useInclude', false);
    } else {
      inputForm.setFieldValue('useInclude', true);
    }
  }, [includeList]);

  useImperativeHandle(ref, () => {
    return {
      validateFields: async () => {
        const cellValues = await cellForm.validateFields();
        const seniorCellValues = await seniorCellForm.validateFields();
        const inputValues = await inputForm.validateFields();
        return {
          ...cellValues,
          ...seniorCellValues,
          ...inputValues,
        };
      },
    };
  });

  const [useExclude, setExclude] = useState<boolean>(false);
  const [useInclude, setInclude] = useState<boolean>(false);

  const charsetFilter: CharsetProps['optionFilter'] = opt => {
    return !['RECOMMEND', 'AUTO'].includes(opt.value);
  };

  useEffect(() => {
    if (detail) {
      setExclude(detail?.setting?.input?.useExclude);
      setInclude(detail?.setting?.input?.useInclude);
      cellForm?.setFieldsValue(ingestionDataToForm(detail));
      seniorCellForm?.setFieldsValue(ingestionDataToForm(detail));
      inputForm?.setFieldsValue(ingestionDataToForm(detail));
    }
  }, [detail]);

  return (
    <>
      <Form
        onChange={() => setConfirm(true)}
        form={inputForm}
        initialValues={initialValues}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
      >
        <div className='pt-5'>
          <Form.Item
            name='port'
            required={!isMultiple}
            label='监听窗口'
            className='pl-9 fixed-width'
            rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入') }]}
          >
            <InputNumber min={0} max={65533} placeholder='端口范围0-65533' />
          </Form.Item>

          <Form.Item
            required={!isMultiple}
            name='protocol'
            label='协议'
            className='pl-9'
            rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入') }]}
          >
            <Protocol />
          </Form.Item>

          <Form.Item required={!isMultiple} name='charset' label='字符集' className='pl-9'>
            <Charset optionFilter={charsetFilter} />
          </Form.Item>

          <Form.Item required={!isMultiple} name='standard' label='编码规范' className='pl-9'>
            <CodingStandards />
          </Form.Item>

          <Form.Item
            name='tagList'
            label='数据标签'
            className='pl-9'
            help='支持用户通过标签功能对数据进行定义，通过检索系统字段@tags轻松搜索到该类型数据。'
          >
            <DataTag />
          </Form.Item>

          <Form.Item className='pl-9' label='过滤内容'>
            <div className='flex items-center justify-start'>
              <div className='flex justify-between'>
                <Form.Item
                  className='m-0 flex-none whitespace-nowrap '
                  style={{ width: '110px' }}
                  name='useExclude'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setExclude(checked);
                      const excludes = inputForm.getFieldValue('excludes');
                      inputForm.setFieldValue('excludes', excludes?.length ? excludes : [undefined]);
                    }}
                  >
                    排除内容
                  </Checkbox>
                </Form.Item>
                <Form.Item
                  className='m-0 flex-none whitespace-nowrap'
                  style={{ width: '110px' }}
                  name='useInclude'
                  valuePropName='checked'
                >
                  <Checkbox
                    onChange={({ target: { checked } }) => {
                      setInclude(checked);
                      const includes = inputForm.getFieldValue('includes');
                      inputForm.setFieldValue('includes', includes?.length ? includes : [undefined]);
                    }}
                  >
                    仅包含内容
                  </Checkbox>
                </Form.Item>
              </div>
            </div>
          </Form.Item>

          {useExclude && (
            <FilterList name='excludes' label='排除内容' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入内容') }]}>
                <ContentInput placeholder='输入内容，需含通配符或具体的内容，多内容以；间隔' />
              </Form.Item>
            </FilterList>
          )}

          {useInclude && (
            <FilterList name='includes' label='仅包含内容' className='pl-9'>
              <Form.Item className='mb-0' rules={isMultiple ? [] : [{ validator: requiredByMsg('请输入内容') }]}>
                <ContentInput placeholder='输入内容，需含通配符或具体的内容，多内容以；间隔' />
              </Form.Item>
            </FilterList>
          )}
        </div>
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={seniorCellForm}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
      >
        <AdvancedConfig form={seniorCellForm} />
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={cellForm}
        className='flex flex-col pb-3 px-0 data-ingestion-form'
        {...formLayout}
      >
        {useAgentList && (
          <CellAgentList cellForm={cellForm} form={form} seniorCellForm={seniorCellForm} inputForm={inputForm} />
        )}
      </Form>
    </>
  );
};

export const InputConfigForm = forwardRef(_InputConfigForm);
