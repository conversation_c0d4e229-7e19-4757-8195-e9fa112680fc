import { Descriptions, Tag } from 'antd';

import { CustomFieldDetail } from '@/components/ui/CustomFieldDetail';
import { AdvancedConfig } from '@/modules/DataIngestion/components';
import { AgentListTableView } from '@/modules/DataIngestion/components/CellAgentList/modal/AgentListTableView';
import { CHARSET_OPTIONS, CODING_OPTIONS, PROTOCOL_OPTIONS } from '@/modules/DataIngestion/constants';
import { useConfigDetail } from '@/modules/DataIngestion/hooks/useConfigDetail';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';

interface Props {
  detail: IngestionCellBasedInfo;
}

export const InputConfigDetail = (props: Props) => {
  const { detail } = props;
  const { detailInfo, advancedConfig } = useConfigDetail('SYSLOG', detail);

  const baseItems = [
    {
      key: 'port',
      label: '监听窗口',
      children: <div>{ detailInfo?.port }</div>,
    },
    {
      key: 'protocol',
      label: '协议',
      children: <div>{PROTOCOL_OPTIONS.find(item => item.value === detailInfo?.protocol)?.label}</div>,
    },
    {
      key: 'charset',
      label: '字符集',
      children: <div>{CHARSET_OPTIONS.find(item => item.value === detailInfo?.charset)?.label}</div>,
    },
    {
      key: 'standard',
      label: '编码规范',
      children: <div>{CODING_OPTIONS.find(item => item.value === detailInfo?.standard)?.label}</div>,
    },
    {
      key: 'tagList',
      label: '数据标签',
      children: <>
        {(detailInfo?.tagList || []).map(value => <Tag key={value}>
          {value}
        </Tag>)}
      </>,
    },
    ...detailInfo?.excludes?.length ? [
      {
        key: 'excludes',
        label: '排除过滤内容',
        children: <CustomFieldDetail
          fieldNames={{ key: 'field', value: 'value' }}
          labelNames={{ key: 'field', value: 'value' }}
          list={detailInfo?.excludes}
        />,
      },
    ] : [],
    ...detailInfo?.includes?.length ? [
      {
        key: 'includes',
        label: '仅包含过滤内容',
        children: <CustomFieldDetail
          fieldNames={{ key: 'field', value: 'value' }}
          labelNames={{ key: 'field', value: 'value' }}
          list={detailInfo?.includes}
        />,
      },
    ] : [],
    
  ];

  return <>
    <Descriptions
      column={1}
      items={baseItems}
      className='pt-5'
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
    <AdvancedConfig readonly configData={advancedConfig} />
    <AgentListTableView value={detail?.agentSettingList} />
  </>;
};
