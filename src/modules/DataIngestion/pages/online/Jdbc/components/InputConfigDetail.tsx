import { useEffect, useState } from 'react';
import { Descriptions, Tag } from 'antd';

import { CodeMirror } from '@/components';
import { AgentListTableView } from '@/modules/DataIngestion/components/CellAgentList/modal/AgentListTableView';
import { COLLECT_TYPE_OPTIONS, DATABASE_TYPE_OPTIONS, TRACKING_COLUMN_TYPE } from '@/modules/DataIngestion/constants';
import { useConfigDetail } from '@/modules/DataIngestion/hooks/useConfigDetail';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { DataSourceApi } from '@/services';

interface Props {
  detail: IngestionCellBasedInfo;
}

export const InputConfigDetail = (props: Props) => {
  const { detail } = props;
  const { detailInfo } = useConfigDetail('JDBC', detail);
  const [dataSourceOptions, setDataSourceOptions] = useState([]);

  useEffect(() => {
    if (detailInfo?.type && detailInfo.type !== 'CUSTOM') {
      getDataSource();
    }
  }, [detailInfo?.type]);

  const getDataSource = async () => {
    const { data } = await DataSourceApi.getListByPlatform(detailInfo?.type);
    setDataSourceOptions(data);
  };

  const baseItems = [
    {
      key: 'type',
      label: '数据库类型',
      children: <div>{DATABASE_TYPE_OPTIONS.find(item => item.value === detailInfo?.type)?.label}</div>,
    },
    ...detailInfo?.type === 'CUSTOM' ? [
      {
        key: 'jdbcUrl',
        label: 'jdbcUrl',
        children: <div>{detailInfo?.jdbcUrl}</div>,
      },
      {
        key: 'jdbcDriver',
        label: 'jdbcDriver',
        children: <div>{detailInfo?.jdbcDriver}</div>,
      },
      {
        key: 'jdbcJar',
        label: 'jdbcJar',
        children: <div>{detailInfo?.jdbcJar}</div>,
      },
      {
        key: 'userName',
        label: 'userName',
        children: <div>{detailInfo?.userName}</div>,
      },
      {
        key: 'password',
        label: 'password',
        children: <div>{detailInfo?.password}</div>,
      },
    ] : [
      {
        key: 'datasourceId',
        label: '数据源',
        children: <div>{dataSourceOptions.find(item => item.id === detailInfo?.datasourceId)?.name}</div>,
      },
      {
        key: 'database',
        label: '数据库名',
        children: <div>{detailInfo?.database}</div>,
      },
    ],
    {
      key: 'syncMode',
      label: '采集方式',
      children: <div>{COLLECT_TYPE_OPTIONS.find(item => item.value === detailInfo?.syncMode)?.label}</div>,
    },
    ...detailInfo?.syncMode === 'incremental' ? [
      {
        key: 'trackingColumnType',
        label: '同步关键字类型',
        children: <div>{TRACKING_COLUMN_TYPE.find(item => item.value === detailInfo?.trackingColumnType)?.label}</div>,
      },
      {
        key: 'trackingColumn',
        label: '同步关键字',
        children: <div>{detailInfo?.trackingColumn}</div>,
      },
    ] : [],
    {
      key: 'sql',
      label: 'SQL语句',
      children: <CodeMirror
        lang='sql'
        value={detailInfo?.sql || ''}
        readOnly
        className='h-[178px] overflow-scroll border-solid border border-white-2 w-[460px]'
      />,
    },
    {
      key: 'cron',
      label: '采集周期',
      children: <div>{detailInfo?.cron}</div>,
    },
    {
      key: '数据标签',
      label: '数据标签',
      children: <>
        {(detailInfo?.tagList || []).map(value => <Tag key={value}>
          {value}
        </Tag>)}
      </>,
    },
  ];

  return <>
    <Descriptions
      column={1}
      items={baseItems}
      className='pt-5'
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
    <AgentListTableView value={detail?.agentSettingList} />
  </>;
};
