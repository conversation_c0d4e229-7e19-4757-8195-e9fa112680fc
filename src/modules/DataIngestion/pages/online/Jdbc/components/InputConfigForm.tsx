/* eslint-disable max-len */
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { Button, Form, Input, message, Modal, Radio, Select } from 'antd';
import { FormLayout } from 'antd/es/form/Form';

import { CodeMirror, CronScheduleExpression, DataSourceSelect, useKeepAliveTabs } from '@/components';
import { CellAgentList, DataTag } from '@/modules/DataIngestion/components';
import { DatabaseTypeSelect } from '@/modules/DataIngestion/components/DatabaseTypeSelect';
import { COLLECT_TYPE_OPTIONS, TRACKING_COLUMN_TYPE } from '@/modules/DataIngestion/constants';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';
import { cellListService } from '@/modules/DataIngestion/services';
import { ingestionDataToForm } from '@/modules/DataIngestion/utils';
import { ApiMetadataService } from '@/services/data-service/apiMetadataService';
import { handleOnBeforeUnload } from '@/utils';

import { DataPreview } from '../../../../components/DataPreview';

const formLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { xxl: 16, xl: 24, lg: 24, xs: 24, sm: 24 },
  layout: 'horizontal' as FormLayout,
};

interface Props {
  useAgentList?: boolean;
  form: FormInstance;
  detail?: IngestionCellBasedInfo;
  isMultiple?: boolean;
  initialValues?: object;
  isPublished?: boolean;
}

export const _InputConfigForm = (props: Props, ref: ForwardedRef<{ validateFields }>) => {
  const { useAgentList = true, form, detail, isMultiple = false, initialValues, isPublished } = props;
  const { setPageInfo } = useKeepAliveTabs();
  const [isOpenPreview, setIsOpenPreview] = useState(false);
  const [previewParams, setPreviewParams] = useState();
  const [databaseList, setDatabaseList] = useState([]);
  const [cellForm] = Form.useForm();
  const [seniorCellForm] = Form.useForm();
  const [inputForm] = Form.useForm();
  const [previewSql, setPreviewSql] = useState('');
  const requiredRules = useMemo(() => (isMultiple ? [] : [{ required: true }]), [isMultiple]);

  const setConfirm = (isDirty: boolean) => {
    setPageInfo({ isDirty });
    window.onbeforeunload = isDirty ? handleOnBeforeUnload('当前内容未保存，确定离开此界面？') : null;
  };

  const databaseType = Form.useWatch('type', inputForm);
  const syncMode = Form.useWatch('syncMode', inputForm);
  const dsId = Form.useWatch('datasourceId', inputForm);
  const isIncremental = useMemo(() => syncMode === 'incremental', [syncMode]);
  const isCustomType = useMemo(() => databaseType === 'CUSTOM', [databaseType]);

  useImperativeHandle(ref, () => {
    return {
      validateFields: async () => {
        const cellValues = await cellForm.validateFields();
        const seniorCellValues = await seniorCellForm.validateFields();
        const inputValues = await inputForm.validateFields();
        return {
          ...cellValues,
          ...seniorCellValues,
          ...inputValues,
          previewSql,
        };
      },
    };
  });

  useEffect(() => {
    if (detail) {
      cellForm?.setFieldsValue(ingestionDataToForm(detail));
      seniorCellForm?.setFieldsValue(ingestionDataToForm(detail));
      inputForm?.setFieldsValue(ingestionDataToForm(detail));
      setPreviewSql(detail?.setting?.input?.previewSql);
    }
  }, [detail]);

  useEffect(() => {
    if (dsId) {
      fetchDatabaseList();
    }
  }, [dsId]);

  const handleChangeType = () => {
    inputForm.setFieldValue('datasourceId', '');
  };

  const handleOpenPreview = async () => {
    if (!isCustomType) {
      const customParams = await inputForm.validateFields(['database', 'datasourceId']);
      setPreviewParams({ database: customParams.database, dsId: customParams.datasourceId });
    } else {
      const customParams = await inputForm.validateFields(['jdbcUrl', 'jdbcDriver', 'jdbcJar', 'userName', 'password']);
      setPreviewParams(customParams);
    }
    setIsOpenPreview(true);
  };

  const handleChangeDs = async () => {
    inputForm.setFieldValue('database', '');
  };

  const fetchDatabaseList = async () => {
    const { data } = await ApiMetadataService.getDatabasesByDsId(dsId);
    setDatabaseList(data.map(item => ({ label: item, value: item })));
  };

  const onOk = () => {
    setIsOpenPreview(false);
  };

  const handleTestCustom = async () => {
    const inputValues = await inputForm.validateFields(['jdbcUrl', 'jdbcDriver', 'jdbcJar', 'userName', 'password']);
    const { jdbcUrl, jdbcDriver, jdbcJar, userName, password } = inputValues;
    await cellListService.testCustomJdbc({ jdbcUrl, jdbcDriver, jdbcJar, userName, password });
    message.success('成功');
  };

  const handleChangeDb = () => {
    setPreviewSql('');
  };

  return (
    <>
      <Form
        onChange={() => setConfirm(true)}
        form={inputForm}
        className='flex flex-col px-0 data-ingestion-form'
        {...formLayout}
        initialValues={initialValues}
      >
        <div className='pt-5 pl-9'>
          <Form.Item name='type' label='数据库类型' rules={[{ required: true }]}>
            <DatabaseTypeSelect onChange={handleChangeType} disabled={isPublished} />
          </Form.Item>
          {databaseType ? (
            <>
              {isCustomType ? (
                <>
                  <Form.Item name='jdbcUrl' label='JDBC URL' rules={requiredRules}>
                    <Input placeholder='示例：*********************************' className='w-[460px]' />
                  </Form.Item>
                  <Form.Item name='jdbcDriver' label='JDBC Driver' rules={requiredRules}>
                    <Input placeholder='示例：com.mysql.jdbc.Driver' className='w-[460px]' />
                  </Form.Item>
                  <Form.Item name='jdbcJar' label='JDBC JAR' rules={requiredRules}>
                    <Input placeholder='示例：mysql-connector-java-5.1.46.jar' className='w-[460px]' />
                  </Form.Item>
                  <Form.Item name='userName' label='用户名' rules={requiredRules}>
                    <Input placeholder='输入用户名' autoComplete='off' className='w-[460px]' />
                  </Form.Item>
                  <Form.Item name='password' label='密码' rules={requiredRules}>
                    <Input.Password placeholder='输入密码' autoComplete='new-password' className='w-[460px]' />
                  </Form.Item>
                </>
              ) : (
                <>
                  <Form.Item label='数据源' name='datasourceId' rules={requiredRules}>
                    <DataSourceSelect
                      platformList={databaseType ? [databaseType] : []}
                      className='w-[460px]'
                      onChange={handleChangeDs}
                    />
                  </Form.Item>
                  <Form.Item label='数据库名' name='database' rules={requiredRules}>
                    <Select
                      placeholder='请输入'
                      showSearch
                      options={databaseList}
                      allowClear
                      className='w-[460px]'
                      onChange={handleChangeDb}
                    />
                  </Form.Item>
                </>
              )}
              <Form.Item className='ml-[136px]'>
                {isCustomType && (
                  <Button onClick={handleTestCustom} className='mr-2'>
                    连接测试
                  </Button>
                )}
                <Button onClick={handleOpenPreview}>数据预览</Button>
              </Form.Item>
            </>
          ) : null}

          <Form.Item name='syncMode' label='采集方式' rules={requiredRules}>
            <Radio.Group options={COLLECT_TYPE_OPTIONS} disabled={isPublished} />
          </Form.Item>
          {isIncremental && (
            <>
              <Form.Item name='trackingColumnType' label='同步关键字类型' rules={requiredRules}>
                <Radio.Group options={TRACKING_COLUMN_TYPE} disabled={isPublished} />
              </Form.Item>
              <Form.Item label='同步关键字' name='trackingColumn' rules={requiredRules}>
                <Input placeholder='输入同步关键字' className='w-[460px]' />
              </Form.Item>
            </>
          )}
          {isIncremental ? (
            <Form.Item
              name='sql'
              label='SQL语句'
              help={`增量示例：
            1、增量字段为数值 SELECT * FROM tablexxx WHERE xxxnum > :sql_last_value
            2、增量字段为datetime、timestamp等时间类型，无需转换直接使用 SELECT * FROM tbl_time WHERE time > :sql_last_value
            3、增量字段类型为date、time，根据不同数据库要求，使用datediff等函数转换为数值 SELECT *,datediff(date, '1970-01-01') AS days FROM tbl_time WHERE datediff(date, '1970-01-01') > :sql_last_value
            `}
              rules={requiredRules}
            >
              <CodeMirror
                lang='sql'
                placeholder={
                  'SQL语句中表建议采用全限定的方式，即\'database.table\'、\'database.schema.table\'形式，保证准确引用'
                }
                className='h-[178px] overflow-scroll border-solid border border-white-2 w-[460px]'
              />
            </Form.Item>
          ) : (
            <Form.Item name='sql' label='SQL语句' rules={requiredRules}>
              <CodeMirror
                lang='sql'
                placeholder={
                  'SQL语句中表建议采用全限定的方式，即\'database.table\'、\'database.schema.table\'形式，保证准确引用'
                }
                className='h-[178px] overflow-scroll border-solid border border-white-2 w-[460px]'
              />
            </Form.Item>
          )}

          <Form.Item name='cron' label='采集周期' rules={requiredRules}>
            <CronScheduleExpression className='w-[460px]' options={['MINUTE', 'HOUR', 'DAY', 'WEEK', 'MONTH']} />
          </Form.Item>

          <Form.Item
            name='tagList'
            label='数据标签'
            help='支持用户通过标签功能对数据进行定义，通过检索系统字段@tags轻松搜索到该类型数据。'
          >
            <DataTag />
          </Form.Item>
        </div>
      </Form>
      <Form
        onChange={() => setConfirm(true)}
        form={cellForm}
        className='flex flex-col pb-3 px-0 data-ingestion-form'
        {...formLayout}
      >
        {useAgentList && (
          <CellAgentList cellForm={cellForm} form={form} isPublished={isPublished} seniorCellForm={seniorCellForm} inputForm={inputForm} />
        )}
      </Form>
      {isOpenPreview && (
        <Modal
          title='数据预览'
          open={isOpenPreview}
          width={1060}
          styles={{ body: { paddingTop: 0, paddingBottom: 10 } }}
          onCancel={() => setIsOpenPreview(false)}
          onOk={onOk}
          footer={null}
        >
          <DataPreview
            databaseType={databaseType}
            previewSql={previewSql}
            onSqlChange={setPreviewSql}
            params={previewParams}
            dsId={dsId}
            showDatabase={!isCustomType}
          />
        </Modal>
      )}
    </>
  );
};

export const InputConfigForm = forwardRef(_InputConfigForm);
