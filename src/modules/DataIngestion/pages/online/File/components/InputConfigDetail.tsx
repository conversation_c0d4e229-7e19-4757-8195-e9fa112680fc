import { Descriptions, Tag } from 'antd';

import { CustomFieldDetail } from '@/components/ui/CustomFieldDetail';
import { AdvancedConfig } from '@/modules/DataIngestion/components';
import { AgentListTableView } from '@/modules/DataIngestion/components/CellAgentList/modal/AgentListTableView';
import { CHARSET_OPTIONS } from '@/modules/DataIngestion/constants';
import { useConfigDetail } from '@/modules/DataIngestion/hooks/useConfigDetail';
import { IngestionCellBasedInfo } from '@/modules/DataIngestion/models';

interface Props {
  detail: IngestionCellBasedInfo;
}

export const InputConfigDetail = (props: Props) => {
  const { detail } = props;
  const { detailInfo, advancedConfig } = useConfigDetail('FILE', detail);
  const charsetItem = CHARSET_OPTIONS.find(item => item.value === detailInfo?.charset);

  const baseItems = [
    {
      key: 'path',
      label: 'path',
      children: <div>{detailInfo?.path}（{ detailInfo?.recursive ? '包含子路径' : '不包含子路径' }）</div>,
    },
    {
      key: '数据标签',
      label: '数据标签',
      children: <>
        {(detailInfo?.tagList || []).map(value => <Tag key={value}>
          {value}
        </Tag>)}
      </>,
    },
    {
      key: 'customParam',
      label: '自定义字段',
      children: <CustomFieldDetail list={detailInfo?.customParam} />,
    },
    {
      key: 'charset',
      label: '字符集',
      children: <span>{ charsetItem?.label }</span>,
    },
    {
      key: 'multiline',
      label: '多行合并',
      children: <span>{ detailInfo?.multiline }</span>,
    },
    ...detailInfo.blacklist?.length ? [{
      key: 'blacklist',
      label: '过滤文件黑名单',
      children: <div>
        {
          detailInfo.blacklist.map((item, index) => <div key={index}>{item}</div>)
        }
      </div>,
    }] : [],
    ...detailInfo.whitelist?.length ? [{
      key: 'whitelist',
      label: '过滤文件白名单',
      children: <div>
        {
          detailInfo.whitelist.map((item, index) => <div key={index}>{item}</div>)
        }
      </div>,
    }] : [],
    ...detailInfo.innerBlackList?.length ? [{
      key: 'innerBlackList',
      label: '过滤子文件黑名单',
      children: <div>
        {
          detailInfo.innerBlackList.map((item, index) => <div key={index}>{item}</div>)
        }
      </div>,
    }] : [],
    ...detailInfo.innerWhiteList?.length ? [{
      key: 'innerWhiteList',
      label: '过滤子文件白名单',
      children: <div>
        {
          detailInfo.innerWhiteList.map((item, index) => <div key={index}>{item}</div>)
        }
      </div>,
    }] : [],
    ...detailInfo.exclude?.length ? [{
      key: 'exclude',
      label: '排除过滤内容',
      children: <div>
        {
          detailInfo.exclude.map((item, index) => <div key={index}>{item}</div>)
        }
      </div>,
    }] : [],
    ...detailInfo.include?.length ? [{
      key: 'include',
      label: '仅包含过滤内容',
      children: <div>
        {
          detailInfo.include.map((item, index) => <div key={index}>{item}</div>)
        }
      </div>,
    }] : [],

  ];

  return <>
    <Descriptions
      column={1}
      items={baseItems}
      className='pt-5'
      labelStyle={{ width: 166, textAlign: 'right', display: 'inline-block', paddingLeft: '2.25rem' }}
    />
    <AdvancedConfig readonly configData={advancedConfig} />
    <AgentListTableView value={detail?.agentSettingList} cellId={detail?.cellId} />
  </>;
};
