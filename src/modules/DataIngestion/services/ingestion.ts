import Request from '@/request';

export interface IngestionClone {
  id: string;
  businessFlowId: string;
  name: string;
}

export type IngestionMove = Omit<IngestionClone, 'name'>;
export type QueryAllJobs = OPS_MANAGEMENT.INGESTION_JOB.Form;
export type QueryAllJobTasks = OPS_MANAGEMENT.INGESTION_JOB_TASK.Form;
export type DataSourceListItem = STORAGE_CLUSTER_MANAGEMENT_CK.API.DataSourceItem;
export type StorageClusterItem = STORAGE_CLUSTER_MANAGEMENT_CK.API.StorageClusterItem;
export type QueryAllStorageCluterList = STORAGE_CLUSTER_MANAGEMENT_CK.STORAGE_CLUSTER_LIST.Form;

export type CLUSTER_LIST = STORAGE_CLUSTER_MANAGEMENT_CK.CREATE_EDIT_STORAGE_CLUSTER.ClusterList;
export type STORAGE_CLUSTER_FORM = STORAGE_CLUSTER_MANAGEMENT_CK.API.CreateCKStorageClusterParams;

export interface GetStorageMetricById {
  beginTime: string;
  endTime: string;
  width: number;
}

export type GetStorageMetricByIdResData<T> = T & {
  values: Array<[number, string]>;
  le?: string;
};
export type GetStorageCpuMetricByIdRes = Array<
  GetStorageMetricByIdResData<{
    appId: string;
    containerId: string;
  }>
>;
export type GetStorageInstanceMemoryMetricByIdRes = GetStorageCpuMetricByIdRes;
export type GetStorageVirtualMemoryMetricByIdRes = GetStorageCpuMetricByIdRes;
export type GetStorageConsumerLagsMetricByIdRes = Array<
  GetStorageMetricByIdResData<{
    appId: string;
    containerId: string;
    topic: string;
    consumer: string;
    task: string;
  }>
>;
export type GetStorageConsumerSpeedMetricByIdRes<T = {}> = Array<
  GetStorageMetricByIdResData<
    T & {
      appId: string;
      containerId: string;
      task: string;
    }
  >
>;
export type GetStorageCkWriteSpeedMetricByIdRes<T = {}> = GetStorageConsumerSpeedMetricByIdRes<T>;
export type GetStorageCkAveWriteDelayMetricByIdRes<T = {}> = GetStorageCkWriteSpeedMetricByIdRes<
  T & {
    table: string;
  }
>;
export type GetStorageCkAveWriteErrorMetricByIdRes = GetStorageCkWriteSpeedMetricByIdRes;
export type GetStorageCkWriteDelayMetricByIdRes = GetStorageCkAveWriteDelayMetricByIdRes<{
  mergedContainerIds: string[];
  le: string;
}>;

// 数据集成服务
class IngestionService {
  constructor(public url: string) {}

  get createTcpUdpUrl() {
    return `${this.url}/info/tcp-udp`;
  }

  // TCP/UDP 同步创建
  createTcpUdp = async (data: any /* TODO: type */) => {
    const res = await Request.post<Response<any /* TODO: type */>>(this.createTcpUdpUrl, { data });
    return res.data;
  };

  get updateTcpUdpUrl() {
    return this.createTcpUdpUrl;
  }

  // TCP/UDP 同步更新
  updateTcpUdp = async (data: any /* TODO: type */) => {
    const res = await Request.put<Response<any /* TODO: type */>>(this.updateTcpUdpUrl, { data });
    return res.data;
  };

  get createFileUrl() {
    return `${this.url}/info/file`;
  }

  // 文件同步创建
  createFile = async (data: DATA_INGESTION.FILE_SYNC.Info) => {
    const res = await Request.post<Response<DATA_INGESTION.FILE_SYNC.Info>>(this.createFileUrl, { data });
    return res.data;
  };

  get updateFileUrl() {
    return this.createFileUrl;
  }

  // 文件同步更新
  updateFile = async (data: DATA_INGESTION.FILE_SYNC.Info) => {
    const res = await Request.put<Response<DATA_INGESTION.FILE_SYNC.Info>>(this.updateFileUrl, { data });
    return res.data;
  };

  get createKafkaUrl() {
    return `${this.url}/info/kafka`;
  }

  // Kafka同步创建
  createKafka = async (data: any /* TODO: type */) => {
    const res = await Request.post<Response<any /* TODO: type */>>(this.createKafkaUrl, { data });
    return res.data;
  };

  get updateKafkaUrl() {
    return this.createKafkaUrl;
  }

  // Kafka同步更新
  updateKafka = async (data: any /* TODO: type */) => {
    const res = await Request.put<Response<any /* TODO: type */>>(this.updateKafkaUrl, { data });
    return res.data;
  };

  get createSyslogUrl() {
    return `${this.url}/info/syslog`;
  }

  // Syslog同步创建
  createSyslog = async (data: any /* TODO: type */) => {
    const res = await Request.post<Response<any /* TODO: type */>>(this.createSyslogUrl, { data });
    return res.data;
  };

  get updateSyslogUrl() {
    return this.createSyslogUrl;
  }

  // Syslog同步更新
  updateSyslog = async (data: any /* TODO: type */) => {
    const res = await Request.put<Response<any /* TODO: type */>>(this.updateSyslogUrl, { data });
    return res.data;
  };

  get queryAllUrl() {
    return `${this.url}/manager/query`;
  }

  // 数据集成分页查询
  queryAll = async (params: QueryParams) => {
    const { data } = await Request.post<Response<any /* TODO: type */>>(this.queryAllUrl, { params });
    return data;
  };

  get moveUrl() {
    return `${this.url}/manager/move`;
  }

  // 数据集成移动
  move = async (data: IngestionMove) => {
    const res = await Request.post<Response<any /* TODO: type */>>(this.moveUrl, {
      data,
    });
    return res.data;
  };

  get cloneUrl() {
    return `${this.url}/manager/clone`;
  }

  // 数据集成克隆
  clone = async (data: IngestionClone) => {
    const res = await Request.post<Response<any /* TODO: type */>>(this.cloneUrl, { data });
    return res.data;
  };

  get publishUrl() {
    return `${this.url}/info/publish`;
  }

  // 数据集成发布
  // todo 已废弃，待删除
  publish = async (data: any /* TODO: type */) => {
    const res = await Request.post<Response<any /* TODO: type */>>(this.publishUrl, { data });
    return res.data;
  };

  // 数据集成发布
  createAndPublish = async (data: any, type) => {
    const res = await Request.post<Response<any>>(`${this.url}/info/${type}/publish`, { data });
    return res.data;
  };

  // 数据集成发布
  updateAndPublish = async (data: any, type) => {
    const res = await Request.put<Response<any>>(`${this.url}/info/${type}/publish`, { data });
    return res.data;
  };

  get getAllUrl() {
    return `${this.url}/manager/list`;
  }

  // 数据集成查询全部
  getAll = async (params: { businessFlowId: string }) => {
    const { data } = await Request.get<Response<DATA_INGESTION.IngestionListItem[]>>(this.getAllUrl, {
      params,
    });
    return data;
  };

  getByIdUrl = (id: string) => {
    return `${this.url}/info/${id}`;
  };

  // 根据 id 查询详情
  getById = async (id: string) => {
    const { data } = await Request.get<Response<DATA_INGESTION.IngestionCellBasedInfo>>(this.getByIdUrl(id));
    return data;
  };

  deleteByIdUrl = (id: string) => {
    return `${this.url}/manager/${id}`;
  };

  // 根据 id 删除
  deleteById = async (id: string) => {
    const { data } = await Request.delete<Response<any /* TODO: type */>>(this.deleteByIdUrl(id));
    return data;
  };

  getTableIdInfo = async (id: string) => {
    const { data } = await Request.get(`/api/v2/data/modeling/table/${id}`);

    return data;
  };

  get queryAllJobsUrl() {
    return `${this.url}/collection/job/query`;
  }

  // 查询数据集成任务列表
  queryAllIngestionJobs = (data: QueryParams<QueryAllJobs>) => {
    return Request.post<Response<any /* TODO: type */>>(this.queryAllJobsUrl, { data });
  };

  getIngestionJobByIdUrl = (id: string) => {
    return `${this.url}/collection/job/${id}`;
  };

  // 根据 id 查询数据集成 Job
  getIngestionJobById = async (id: string) => {
    const { data } = await Request.get<Response<OPS_MANAGEMENT.INGESTION_JOB.Item>>(this.getIngestionJobByIdUrl(id));
    return data;
  };

  startIngestionJobUrl = () => {
    return `${this.url}/collection/job/start`;
  };

  // 启动采集任务
  startIngestionJob = async (id: string) => {
    const { data } = await Request.post<Response<any /* TODO: type */>>(this.startIngestionJobUrl(), {
      params: { id },
    });
    return data;
  };

  stopIngestionJobUrl = () => {
    return `${this.url}/collection/job/stop`;
  };

  // 停止采集任务
  stopIngestionJob = async (id: string) => {
    const { data } = await Request.post<Response<any /* TODO: type */>>(this.stopIngestionJobUrl(), { params: { id } });
    return data;
  };

  // 数据集成task分页查询
  getJobTaskList = async (data: any) => {
    const res = await Request.post<Response<any>>(`${this.url}/collection/task/query`, {
      data,
    });
    return res;
  };

  get getDataSourceByPlatformUrl() {
    return `${this.url}/datasource/listByPlatform`;
  }

  // 根据数据类型查询所有数据源
  getDataSourceByPlatform = async (platform: string) => {
    const { data } = await Request.get<Response<DATA_INGESTION.KAFKA_SYNC.DATA_SOURCE[]>>(
      this.getDataSourceByPlatformUrl,
      { params: { platform } },
    );
    return data;
  };

  getTopicOfKafkaByDsIdUrl(dsId: string) {
    return `${this.url}/kafka/topic/${dsId}`;
  }

  // 查询 kafka 集群下的 topic
  getTopicOfKafkaByDsId = async (dsId: string) => {
    const { data } = await Request.get<Response<DATA_INGESTION.KAFKA_SYNC.TOPIC[]>>(
      this.getTopicOfKafkaByDsIdUrl(dsId),
    );
    return data;
  };

  // 数据集成-task-input 参数配置 地址
  get getTaskInputConfigUrl() {
    return `${this.url}/collection/task/input-param`;
  }

  // 数据集成-task-input 参数配置
  getTaskInputConfig = async (data: { ingestionJobId: string; agentId: string }) => {
    const res = await Request.post<Response<OPS_MANAGEMENT.INGESTION_JOB_TASK.INPUT_CONFIG>>(
      `${this.url}/collection/task/input-param`,
      {
        data,
      },
    );

    return res.data;
  };

  get queryCkStorageTaskListUrl() {
    return `${this.url}/storage-ck/maintenance/query`;
  }

  // ck存储任务列表查询
  queryCkStorageTaskList = async (data: OPS_MANAGEMENT.STORAGE.CK.TableList.Filter) => {
    const res = await Request.post<Response<OPS_MANAGEMENT.STORAGE.CK.APIS.Model[]>>(this.queryCkStorageTaskListUrl, {
      data,
    });
    return res;
  };

  getStorageCpuMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/cpu`;
  };

  getStorageCpuMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageCpuMetricByIdRes>>(this.getStorageCpuMetricByIdUrl(type, id), {
      data,
    });
    return res.data;
  };

  getStorageInstanceMemoryMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/instance-memory`;
  };

  getStorageInstanceMemoryMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageInstanceMemoryMetricByIdRes>>(
      this.getStorageInstanceMemoryMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getStorageVirtualMemoryMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/virtual-memory`;
  };

  getStorageVirtualMemoryMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageVirtualMemoryMetricByIdRes>>(
      this.getStorageVirtualMemoryMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getStorageConsumerLagsMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/consumer-lags`;
  };

  getStorageConsumerLagsMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageConsumerLagsMetricByIdRes>>(
      this.getStorageConsumerLagsMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getStorageConsumerSpeedMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/consumer-rate`;
  };

  getStorageConsumerSpeedMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageConsumerSpeedMetricByIdRes>>(
      this.getStorageConsumerSpeedMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getStorageCkWriteSpeedMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/ck-flush-rate`;
  };

  getStorageCkWriteSpeedMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageCkWriteSpeedMetricByIdRes>>(
      this.getStorageCkWriteSpeedMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getStorageCkAveWriteDelayMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/ck-flush-delay-avg`;
  };

  getStorageCkAveWriteDelayMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageCkAveWriteDelayMetricByIdRes>>(
      this.getStorageCkAveWriteDelayMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getStorageCkAveWriteErrorMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/ck-flush-error`;
  };

  getStorageCkAveWriteErrorMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageCkAveWriteErrorMetricByIdRes>>(
      this.getStorageCkAveWriteErrorMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getStorageCkWriteDelayMetricByIdUrl = (type: string, id: string) => {
    return `${this.url}/storage-${type}/maintenance/metric/${id}/ck-flush-delay`;
  };

  getStorageCkWriteDelayMetricById = async (type: string, id: string, data: GetStorageMetricById) => {
    const res = await Request.post<Response<GetStorageCkWriteDelayMetricByIdRes>>(
      this.getStorageCkWriteDelayMetricByIdUrl(type, id),
      {
        data,
      },
    );
    return res.data;
  };

  getCkStorageDetailByIdUrl = (id: string) => {
    return `${this.url}/storage-ck/maintenance/${id}`;
  };

  // 根据id获取ck存储任务详情
  getCkStorageDetailById = async (id: string, options?) => {
    const res = await Request.get<Response<OPS_MANAGEMENT.STORAGE.CK.APIS.ModalDetails>>(
      this.getCkStorageDetailByIdUrl(id),
      { ...options },
    );
    return res.data;
  };

  getCkStorageByIdUrl = (id: string) => {
    return `${this.url}/storage-ck/${id}`;
  };

  // 根据 id 获取ck 存储任务
  getCkStorageById = async (id: string) => {
    const res = await Request.get<Response<STORAGE_TASK.CK.APIS.Model>>(this.getCkStorageByIdUrl(id));
    return res.data;
  };

  get createCkStorageUrl() {
    return `${this.url}/storage-ck`;
  }

  // 创建 ck 存储任务
  createCkStorage = async (data: STORAGE_TASK.CK.APIS.Model) => {
    const res = await Request.post<Response<STORAGE_TASK.CK.APIS.Model>>(this.createCkStorageUrl, { data });
    return res.data;
  };

  updateCkStorageByIdUrl(id: string) {
    return `${this.createCkStorageUrl}/${id}`;
  }

  // 更新 ck 存储任务
  updateCkStorageById = async (id: string, data: STORAGE_TASK.CK.APIS.Model) => {
    const res = await Request.put<Response<STORAGE_TASK.CK.APIS.Model>>(this.updateCkStorageByIdUrl(id), { data });
    return res.data;
  };

  updateStartCkStorageByIdUrl(id: string) {
    return `${this.createCkStorageUrl}/${id}/update-start`;
  }

  // 更新并启动 ck 存储任务
  updateStartCkStorageById = async (id: string, data: STORAGE_TASK.CK.APIS.Model) => {
    const res = await Request.put<Response<STORAGE_TASK.CK.APIS.Model>>(this.updateStartCkStorageByIdUrl(id), { data });
    return res.data;
  };

  get createStartCkStorageUrl() {
    return `${this.createCkStorageUrl}/create-start`;
  }

  // 创建并启动 ck 存储任务
  createStartCkStorageBy = async (data: STORAGE_TASK.CK.APIS.Model) => {
    const res = await Request.post<Response<STORAGE_TASK.CK.APIS.Model>>(this.createStartCkStorageUrl, { data });
    return res.data;
  };

  removeCkStorageByIdUrl(id: string) {
    return this.updateCkStorageByIdUrl(id);
  }

  // 删除 ck 存储任务
  removeCkStorageById = async (id: string) => {
    const res = await Request.delete<Response<void>>(this.removeCkStorageByIdUrl(id));
    return res.data;
  };

  startupCkStorageByIdUrl(id: string) {
    return `${this.url}/storage-ck/${id}/start`;
  }

  // 启动ck存储任务
  startupCkStorageById = async (id: string) => {
    const res = await Request.get<Response<any /* TODO: type */>>(this.startupCkStorageByIdUrl(id));
    return res;
  };

  stopCkStorageByIdUrl(id: string) {
    return `${this.url}/storage-ck/${id}/stop`;
  }

  // 停止ck存储任务
  stopCkStorageById = async (id: string) => {
    const res = await Request.get<Response<any /* TODO: type */>>(this.stopCkStorageByIdUrl(id));
    return res;
  };

  getKafkaLagsByCkIdUrl(id: string) {
    return `${this.url}/storage-ck/maintenance/${id}/kafka-lags`;
  }

  // ck 存储任务-从kafka获取kafka积压消息数量
  getKafkaLagsByCkId = async (id: string) => {
    const res = await Request.get<Response<{ consumerLag?: number }>>(this.getKafkaLagsByCkIdUrl(id));
    return res.data;
  };

  get getCKStorageConsumersUrl() {
    return `${this.url}/storage-ck/consumers`;
  }

  // ck存储作业-获取所有消费者组
  getCKStorageConsumers = async () => {
    const res = await Request.get<Response<string[]>>(this.getCKStorageConsumersUrl);
    return res.data;
  };

  // 分页查询ck存储集群列表
  get getCKStorageClusterListUrl() {
    return `${this.url}/storage-cluster/query`;
  }

  queryCKStorageClusterList = async (data: QueryParams<QueryAllStorageCluterList>, options?) => {
    return Request.post<Response<StorageClusterItem[]>>(this.getCKStorageClusterListUrl, { data, ...options });
  };

  // 创建ck存储集群
  get createCkStorageClusterUrl() {
    return `${this.url}/storage-cluster`;
  }

  createCkStorageCluster = async (data: STORAGE_CLUSTER_FORM, options?) => {
    const res = await Request.post(this.createCkStorageClusterUrl, { data, ...options });
    return res.data;
  };

  // 更新ck存储集群
  updateCkStorageClusterByIdUrl = (id: string) => {
    return `${this.url}/storage-cluster/${id}`;
  };

  updateCkStorageClusterById = async (id: string, data: STORAGE_CLUSTER_FORM) => {
    const res = await Request.put(this.updateCkStorageClusterByIdUrl(id), {
      data,
    });
    return res.data;
  };

  // 获取ck存储集群详情
  getCkStorageClusterByIdUrl = (id: string) => {
    return `${this.url}/storage-cluster/${id}`;
  };

  getCkStorageClusterById = async (id: string, options?) => {
    const res = await Request.get(this.updateCkStorageClusterByIdUrl(id), { ...options });
    return res.data;
  };

  updateStartCkStorageClusterByIdUrl = (id: string) => {
    return `${this.url}/storage-cluster/${id}/update-start`;
  };

  // 更新并启动ck存储集群
  updateStartCkStorageClusterById = async (id: string, data: STORAGE_CLUSTER_FORM, options?) => {
    const res = await Request.put(this.updateStartCkStorageClusterByIdUrl(id), {
      data,
      ...options,
    });
    return res.data;
  };

  // 删除ck存储集群
  deleteCkStorageClusterUrl = (id: string) => {
    return `${this.url}/storage-cluster/${id}`;
  };

  deleteCkStorageCluster = async (id: string, options?) => {
    const res = await Request.delete(this.deleteCkStorageClusterUrl(id), { ...options });
    return res.data;
  };

  // 启动ck存储集群
  startCkStorageClusterUrl = (id: string, forceStart: boolean = false) => {
    return `${this.url}/storage-cluster/${id}/start?forceStart=${forceStart}`;
  };

  startCkStorageCluster = async (id: string, forceStart: boolean = false, options?) => {
    const res = Request.get(this.startCkStorageClusterUrl(id, forceStart), { ...options });
    return res;
  };

  // 停止ck存储集群
  stopCkStorageClusterUrl = (id: string) => {
    return `${this.url}/storage-cluster/${id}/stop`;
  };

  stopCkStorageCluster = async (id: string, options?) => {
    const res = Request.get(this.stopCkStorageClusterUrl(id), { ...options });
    return res;
  };

  // 刷新ck存储集群
  refreshCkStorageClusterUrl = (id: string) => {
    return `${this.url}/storage-cluster/${id}/refresh`;
  };

  refreshCkStorageCluster = async (id: string) => {
    const res = Request.get(this.refreshCkStorageClusterUrl(id));
    return res;
  };

  // 数据源分页查询
  get queryAllDataSourceByPlatformUrl() {
    return `${this.url}/datasource/query`;
  }

  queryAllDataSoureByPlatform = async (bodyData: QueryParams) => {
    const { data } = await Request.post<Response<DataSourceListItem[]>>(this.queryAllDataSourceByPlatformUrl, {
      data: bodyData,
    });
    return data;
  };

  // Nacos数据源查询
  get queryAllNacosListByTypeUrl() {
    return `${this.url}/register-center/query`;
  }

  queryAllNacosListByType = async (bodyData: QueryParams) => {
    const { data } = await Request.post(this.queryAllNacosListByTypeUrl, {
      data: bodyData,
    });
    return data;
  };

  // 请求运行集群列表
  get getAllClusterListUrl() {
    return `${this.url}/cluster/list`;
  }

  getAllClusterList = async (params?: { clusterType: string; supportOpts: string }) => {
    const { data } = await Request.get<Response<CLUSTER_LIST[]>>(this.getAllClusterListUrl, {
      params: {
        supportOpts: 'marayarn',
        ...params,
      },
    });
    return data;
  };

  get getAllStorageClusterUrl() {
    return `${this.url}/storage-cluster/list`;
  }

  // 获取全部存储集群
  getAllStorageCluster = async (options: any) => {
    const res = await Request.get<Response<STORAGE_TASK.CK.APIS.STORAGE_CLUSTER[]>>(this.getAllStorageClusterUrl, {
      ...options,
    });
    return res.data;
  };

  // 获取ES索引名称
  getEsIndexNameUrl(id: string) {
    return `${this.url}/elasticsearch/index/${id}`;
  }

  async getEsIndexName(id: string) {
    const res = await Request.get<Response>(this.getEsIndexNameUrl(id));
    return res.data;
  }
}

export const ingestionService = new IngestionService('/api/v2/ingestion');

export default ingestionService;
