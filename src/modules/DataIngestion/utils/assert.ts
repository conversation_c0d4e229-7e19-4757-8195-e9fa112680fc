import { ingestionTypeToLowerCase } from './ingestion';

export function isOnlineIngestion(type = '', id = '') {
  if (id.includes('REALTIME-REAL_TIME_DATA_INGESTION')) {
    return isOnlineIngestionByType(type);
  }
  return false;
}

export function isOnlineIngestionByType(type = '') {
  return ['file', 'tcp-udp', 'syslog', 'kafka', 'archive', 'jdbc', 'logstash'].includes(ingestionTypeToLowerCase(type));
}
