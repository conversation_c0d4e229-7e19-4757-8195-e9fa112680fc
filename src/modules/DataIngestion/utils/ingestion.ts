import { updateValueByPath } from '@/utils';

import { AgentSettings, customParamConvertor, customParamDeConvertor } from '../components';

const includesExcludesConvertor = (param: Array<Record<string, string>>) => {
  if (Array.isArray(param)) {
    const arr: Array<{ field: string; value: string }> = [];
    param.forEach(item => {
      arr.push({ field: Object.keys(item)[0], value: Object.values(item)[0] });
    });

    return arr;
  }
};

function agentSettingListToData<T>(isSyslog: boolean, settings?: AgentSettings<T>) {
  if (!settings) {
    return [];
  }

  return settings.map(setting => {
    let newSetting = updateValueByPath('setting.input.customParam', customParamConvertor, setting);
    if (isSyslog) {
      newSetting = updateValueByPath('setting.input.excludes', includesExcludesConvertor, newSetting);
      newSetting = updateValueByPath('setting.input.includes', includesExcludesConvertor, newSetting);
    }
    return newSetting;
  });
}

function dataToAgentSettingList<T>(data: AgentSettings<T>) {
  return data?.map(setting => updateValueByPath('setting.input.customParam', customParamDeConvertor, setting));
}

export function ingestionFormToData(
  values: DATA_INGESTION.FormType,
  businessFlowId: string,
  ingestionType: string,
): DATA_INGESTION.IngestionCellBasedInfo {
  const isSyslog = ingestionType === 'syslog';
  const {
    agentSettingList,
    customParam,
    name,
    tbDeployId,
    tbId,
    cellId,
    routerId,
    id,
    includes,
    excludes,
    useBlacklist,
    useExclude,
    useInclude,
    useWhitelist,
    ...input
  } = values;

  return {
    cellId,
    tbId,
    tbDeployId,
    routerId,
    name,
    id,
    setting: {
      input: {
        ...input,
        customParam: customParamConvertor(customParam),
        includes: isSyslog ? includesExcludesConvertor(includes) : includes,
        excludes: isSyslog ? includesExcludesConvertor(excludes) : excludes,
        useBlacklist: useBlacklist ?? false,
        useExclude: useExclude ?? false,
        useInclude: useInclude ?? false,
        useWhitelist: useWhitelist ?? false,
      },
    },
    agentSettingList: agentSettingListToData(isSyslog, agentSettingList),
    businessFlowId,
    ingestionType: ingestionType.replace('-', '_').toUpperCase(),
  };
}

export function ingestionDataToForm(data: DATA_INGESTION.IngestionCellBasedInfo): DATA_INGESTION.FormType {
  const {
    setting,
    agentSettingList,
    ingestionType,
    createTime,
    createUserName,
    id,
    updateTime,
    updateUserName,
    businessFlowId,
    ...values
  } = data ?? {};
  const { customParam, ...input } = setting?.input ?? {};

  return {
    ...values,
    ...input,
    agentSettingList: dataToAgentSettingList(agentSettingList),
    customParam: customParamDeConvertor(customParam),
  };
}

export function ingestionTypeToLowerCase(type: string) {
  return type.replace('_', '-').toLowerCase();
}
