export interface AgentSettingListItem<T> {
  agentId: string; // 采集器 id
  setting: Setting<
    T & {
      hostname: string;
      ip: string;
      groupList: Array<{ name: string; id: string }>;
      os: string;
      version: string;
    }
  >;
}

export interface Setting<T> {
  input: T; // 数据输入
}

export interface IngestionCellBasedInfo<T = any> extends IngestionInfo {
  cellId: string; // 网关id
  setting: Setting<T>;
  agentSettingList: Array<AgentSettingListItem<T>>;
  [key: string]: any;
}

export interface IngestionInfo {
  name: string; // 数据集成名称
  ingestionType: string; // 数据集成类型
  businessFlowId: string; // 业务流程id
}

export interface QueryCellAgentParams {
  cellId: string;
  capability: DATA_INGESTION.IngestionType;
  hostnameOrIp: string;
  osAlias: string;
  excludeIds: string[];
}
