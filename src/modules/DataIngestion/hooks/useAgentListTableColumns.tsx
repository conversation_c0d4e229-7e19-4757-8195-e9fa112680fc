import { Tag } from 'antd';

import { Loading } from '@/components';
import { AgentSetting } from '@/modules/DataIngestion/components/CellAgentList/modal';
import { buildTableColumn } from '@/utils/table';

import { CollectionStatusMap, FIELD_DELIMITER_OPTIONS } from '../constants';

import { useIngestionType } from './ingestion';

const baseColumn = [
  buildTableColumn('主机名', 'hostname'),
  buildTableColumn('IP', 'ip'),
  buildTableColumn('状态', (_, record) => {
    const val = record.setting.input.connection;
    return (<span className={`text-${CollectionStatusMap?.[val]?.color}`}>{CollectionStatusMap?.[val]?.label}</span>);
  }),
];

function useFileColumns<T>(cacher: Map<string, { doing: boolean; failed: boolean }>, processAutoCharset: Function) {
  return [
    ...baseColumn,
    buildTableColumn<AgentSetting<T>>('路径', 'path'),
    buildTableColumn<AgentSetting<T>>('字符集', (_, { setting, agentId }) => {
      const { input } = setting as any;
      if (input.charset === 'RECOMMEND' || input.charset === '失败') {
        const cache = cacher.get(agentId);
        if (cache?.failed) {
          return <span className='text-warning'>解析失败</span>;
        }
        if (!cache?.doing) {
          cacher.set(agentId, { doing: true, failed: false });
          processAutoCharset(agentId, input);
        }
        return <Loading />;
      }
      return input.charset;
    }),
    buildTableColumn<T>('多行合并', 'multiline'),
  ];
}

function useTcpUdpColumns<T extends { fieldDelimiter: string }>() {
  return [
    ...baseColumn,
    buildTableColumn<AgentSetting<T>>('端口', 'port'),
    buildTableColumn<AgentSetting<T>>('协议', 'protocol'),
    buildTableColumn<AgentSetting<T>>('分割方式', (_, { setting }) => {
      const option = FIELD_DELIMITER_OPTIONS.find(option => option.value === setting.input.fieldDelimiter);
      return option?.label;
    }),
  ];
}

function useKafkaColumns<T>() {
  return [
    ...baseColumn,
    buildTableColumn<AgentSetting<T>>('采集器标签', (_, { setting }) => {
      const groupList = setting.input.groupList ?? [];
      return groupList.map(({ name, id }) => <Tag key={id}>{name}</Tag>);
    }),
    buildTableColumn<AgentSetting<T>>('版本', 'version'),
    buildTableColumn<AgentSetting<T>>('OS类型', 'os'),
  ];
}

function useSyslogColumns<T>() {
  return [
    ...baseColumn,
    buildTableColumn<AgentSetting<T>>('端口', 'port'),
    buildTableColumn<AgentSetting<T>>('协议', 'protocol'),
    buildTableColumn<AgentSetting<T>>('字符集', 'charset'),
  ];
}

function useJdbcColumns<T>() {
  return [
    ...baseColumn,
    buildTableColumn<AgentSetting<T>>('采集器标签', (_, { setting }) => {
      const groupList = setting.input.groupList ?? [];
      return groupList.map(({ name, id }) => <Tag key={id}>{name}</Tag>);
    }),
    buildTableColumn<AgentSetting<T>>('版本', 'version'),
    buildTableColumn<AgentSetting<T>>('OS类型', 'os'),
  ];
}

function useLogstashColumns<T>() {
  return [
    ...baseColumn,
    buildTableColumn<AgentSetting<T>>('采集器标签', (_, { setting }) => {
      const groupList = setting.input.groupList ?? [];
      return groupList.map(({ name, id }) => <Tag key={id}>{name}</Tag>);
    }),
    buildTableColumn<AgentSetting<T>>('版本', 'version'),
    buildTableColumn<AgentSetting<T>>('OS类型', 'os'),
  ];
}

export function useAgentListTableColumns<T>(
  cache: Map<string, { failed: boolean; doing: boolean }>,
  processAutoCharset: Function,
) {
  const fileColumns = useFileColumns<T>(cache, processAutoCharset);
  const tcpUdpColumns = useTcpUdpColumns<T & { fieldDelimiter: string }>();
  const kafkaColumns = useKafkaColumns<T>();
  const syslogColumns = useSyslogColumns<T>();
  const ingestionType = useIngestionType();
  const jdbcColumns = useJdbcColumns();
  const logstashColumns = useLogstashColumns();

  const getColumns = (ingestionType: string) => {
    if (ingestionType === 'file') return fileColumns;
    if (ingestionType === 'archive') return fileColumns;
    if (ingestionType === 'tcp-udp') return tcpUdpColumns;
    if (ingestionType === 'kafka') return kafkaColumns;
    if (ingestionType === 'syslog') return syslogColumns;
    if (ingestionType === 'jdbc') return jdbcColumns;
    if (ingestionType === 'logstash') return logstashColumns;
    return [];
  };

  return getColumns(ingestionType);
}
