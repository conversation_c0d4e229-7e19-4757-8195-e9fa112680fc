declare namespace DATA_INGESTION {
  interface UseSaveParams {
    businessFlowId?: string;
    onError?: OnFormValidateError;
    isPublish?: boolean;
    freshFlowTree?: boolean;
    toDetailAfterCreate?: boolean;
    form: FormInstance | null;
  }

  // 网关下关联的数据类型
  namespace CELL {
    interface ConnectDetail {
      normalUrls: string[];
      abnormalUrlMap: Record<string, string>;
      status: string; // NORMAL, ABNORMAL
    }

    interface GroupCountResultListItem {
      id: string;
      name: StringOrNull;
      number: number;
      type: string; // DATASOURCE, REGISTER_CENTER
      typeDesc: string;
    }

    interface DataCenterListItem {
      createTime: StringOrNull;
      createUserName: StringOrNull;
      description: StringOrNull;
      groupCountResultList: GroupCountResultListItem[];
      id: string;
      name: StringOrNull;
      updateTime: StringOrNull;
      updateUserName: StringOrNull;
    }
  }

  namespace KAFKA_SYNC {
    interface DATA_SOURCE {
      createTime: StringOrNull;
      createUserName: StringOrNull;
      id: string;
      name: string;
      tagType: string;
      updateTime: StringOrNull;
      updateUserName: StringOrNull;
    }
    type TOPIC = string;
  }

  // 文件同步
  namespace FILE_SYNC {
    interface TIME {
      num: number; // 具体时间
      unit: 'hour' | 'day'; // 单位
    }

    // 采集高级配置
    interface InfoAdvance {
      // 不采集此时间前的文件
      ignoreOlderThan?: TIME;
      incremental: boolean; // 仅采集增量数据
      renamecheck: boolean; // 文件重命名检查
      flushPartial: number; // 文件尾无结束标志时等待时长
      pollInterval: number; // 已经打开文件检查新数据间隔
      dircheckInterval: number; // 检测新文件产生的时间间隔
      backoff: number; // 已读文件延后检查
      backoffFactor?: number; // TODO: desc
      maxBackoff?: number; // TODO: desc
      actionWhenEof: 'NONE' | 'RENAME' | 'DELETE'; // 文件读后处理
      // 文件读后处理等待时间
      actionMinWait: TIME;
      workTimeInterval: string; // 日志定时采集
      flushCacheInterval: number; // 缓存刷新间隔
      hashEnable: boolean; // 日志哈希标识
      hashDataLength: number; // 哈希计算长度
      hashWaitData: boolean; // 过滤小于“哈希计算长度”的文件
      openfiles: number; // 同时采集文件数量
      msgMaxLength: number; // 单条消息最大长度
      batch: number; // 单次发送最大消息数量
      openFileSort: string; // 指定文件采集顺序
      ignoreSymlink: boolean; // 忽略软链接文件
      bandwidthweight: number; // 网络带宽权重
      // 海量文件处理策略,为true时才展示maxAddFiles、fileExpiredTime
      // dirExpiredTime、expiredCheckInterval四个参数
      fileExpiredEnable: boolean;
      maxAddFiles: number; // 单次扫描最多文件数
      fileExpiredTime: number; // 文件过期时间
      dirExpiredTime: number; // 目录过期时间
      expiredCheckInterval: number; // 过期文件是否存在检查间隔
      waitOnEof: boolean; // 多行合并等待
      savepos: boolean; // TODO: desc
      innerWhiteList: string[]; // TODO: desc
      innerBlackList: string[]; // TODO: desc
    }

    interface InfoInput extends InfoAdvance {
      customParam?: Record<string, string>; // 自定义参数
      recursive?: boolean; // 是否包含子文件
      multiline?: string; // 多行合并
      whitelist?: string[]; // 文件白名单
      blacklist?: string[]; // 文件黑名单
      include?: string[]; // 包含内容
      exclude?: string[]; // 排除文件内容
      path?: string; // 文件路径
      tagList: string[]; // 标签列表
      charset: string; // 字符集
    }

    interface Info extends IngestionCellBasedInfo<InfoInput> {
      tbId?: string; // 文件同步-数据输出-数据kafka模型id
      tbDeployId?: string;
      id?: string;
    }
  }

  interface IngestionInfo {
    name: string; // 数据集成名称
    ingestionType: string; // 数据集成类型
    businessFlowId: string; // 业务流程id
  }

  interface Setting<T> {
    input: T; // 数据输入
  }

  interface AgentSettingListItem<T> {
    agentId: string; // 采集器 id
    setting: Setting<
      T & {
        hostname: string;
        ip: string;
        groupList: Array<{ name: string; id: string }>;
        os: string;
        version: string;
      }
    >;
  }

  interface IngestionCellBasedInfo<T = any> extends IngestionInfo {
    cellId: string; // 网关id
    setting: Setting<T>;
    agentSettingList: Array<AgentSettingListItem<T>>;
    [key: string]: any;
  }

  type IngestionCellBasedInfoForm<T = any> = T &
    Omit<IngestionCellBasedInfo<T>, 'setting' | 'ingestionType' | 'businessFlowId'>;

  type FormType = IngestionCellBasedInfoForm<Record<string, any>>;

  // 网关类型
  interface Cell {
    id: string;
    name: StringOrNull;
    description: StringOrNull;
    connectMode: StringOrNull;
    connectDetail: CELL.ConnectDetail;
    lastConnectTime: StringOrNull;
    status: string;
    address: StringOrNull;
    groupName: StringOrNull;
    registerCenterId: StringOrNull;
    dsId: StringOrNull;
    dataCenterList: CELL.DataCenterListItem[];
    cellManagementUrl: StringOrNull;
    createTime: StringOrNull;
    updateTime: StringOrNull;
    createUserName: StringOrNull;
    updateUserName: StringOrNull;
  }

  // 数据集成列表项
  interface IngestionListItem {
    id: string;
    businessFlowId: string;
    name: string;
    ingestionType: IngestionType;
    status: IngestionStatus;
    createTime: StringOrNull;
    updateTime: StringOrNull;
    createUserName: StringOrNull;
    updateUserName: StringOrNull;
    published: 0 | 1;
  }

  type IngestionStatus = 'RUNNING' | 'STOPPED' | 'EXCEPTION' | 'UNSTART' | 'FINISHED';

  namespace CELL_AGENT {
    interface Profile {
      bandwidthMode: 'exclusive';
      memory: 200;
      bandwidth: 1048576;
      compress: true;
      cpukiller: false;
      cpu: 2;
      clientPortRange: null;
      killerTriggerTime: 0;
      compressLevel: 3;
      usessl: false;
    }

    interface RecommendCharset {
      charset: string;
      agentId: string;
    }

    interface Capability {
      archive: boolean;
      bandwidth: boolean;
      compress: boolean;
      cpu: boolean;
      custom: boolean;
      dogmetric: boolean;
      dogprobe: boolean;
      dogprocess: boolean;
      file: boolean;
      flow2: boolean;
      jdbc: boolean;
      kafka: boolean;
      memory: boolean;
      metric: boolean;
      msevent: boolean;
      retrieval: boolean;
      script: boolean;
      snapshoot: boolean;
      ssl: boolean;
      syslog: boolean;
      tcp: boolean;
      udp: boolean;
    }

    interface GroupListItem {
      id: string;
      name: string;
    }

    interface FileContent {
      c: string;
      rn: number;
      no: number;
    }
  }

  // 采集器
  export interface CellAgent {
    agentId: string;
    version: string;
    revision: string;
    hostname: string;
    os: string;
    osAlias: string;
    ip: string;
    serverIp: string;
    cwd: string;
    createTime: StringOrNull;
    lastHbTime: StringOrNull;
    profile: CELL_AGENT.Profile;
    capability: CELL_AGENT.Capability;
    status: 'StartSucceeded';
    connection: number;
    deployStatus: 'Succeeded';
    lastOp: 'restart';
    centerName: StringOrNull;
    centerId: StringOrNull;
    groupList: CELL_AGENT.GroupListItem[];
  }

  interface State<T> {
    form?: import('antd').FormInstance<T>;
    fileExpiredEnabled?: boolean;
    showActionMinWait?: boolean;
    businessFlowId?: string;
    esRunTimeConfig?: Partial<STORAGE_TASK.ES.STORE.RunTimeInitValue>;
  }

  interface LocationState {
    businessFlowId?: string;
    title?: string;
    cloneId?: string;
  }

  interface Actions<T> {
    updateState: (partialNewState: Partial<State<T>>) => void;
  }

  interface Store<T> {
    state: State<T>;
    actions: Actions<T>;
  }

  interface LocationState {
    businessFlowId?: string;
    ingestionType?: string;
  }

  interface DataTag {
    id: string;
    name: StringOrNull;
    tagType: StringOrNull;
    createTime: StringOrNull;
    updateTime: StringOrNull;
    createUserName: StringOrNull;
    updateUserName: StringOrNull;
  }

  // 采集类型
  type IngestionType = 'file' | 'archive' | '' | 'mseventlog' | 'kafka' | 'jdbc' | 'syslog' | 'script';

  // TODO: move to data output namespace
  interface LayerListItem {
    id: string;
    name: string;
    code: string;
    nameEn: string;
    description: string;
    catalog: string;
    isBuiltIn: boolean;
    tbType: string;
    createTime: string;
    createUserName: string;
    updateTime: string;
    updateUserName: string;
  }

  interface ModelTableListItem {
    id: string;
    platform: string;
    deployType: string;
    actionType: string;
    businessFlowId: string;
    tbId: string;
    tbName: string;
    tbAlias: string;
  }
}
