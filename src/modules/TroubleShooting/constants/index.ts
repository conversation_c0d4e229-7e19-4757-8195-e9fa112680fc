import { URL_MAP } from '@/modules/DataDev/utils';

export const ALERT_LEVEL = {
  severe: {
    label: '严重',
    value: 'severe',
    rank: 60,
    icon: 'icon-notification_important-fill',
    color: '#ff4d4f',
  },
  critical: {
    label: '重要',
    value: 'critical',
    rank: 50,
    icon: 'icon-notification_important-fill',
    color: '#fe9836',
  },
  warning: {
    label: '警告',
    value: 'warning',
    rank: 40,
    icon: 'icon-notification_important-fill',
    color: '#ffe400',
  },
  ordinary: {
    label: '一般',
    value: 'ordinary',
    rank: 30,
    icon: 'icon-notification_important-fill',
    color: '#096dd9',
  },
  info: {
    label: '通知',
    value: 'info',
    rank: 20,
    icon: 'icon-notification_important-fill',
    color: '#8c8c8c',
  },
};

export const ALERT_LEVEL_LIST = Object.values(ALERT_LEVEL);

export const THRESHOLD_SYMBOL = {
  '>=': {
    label: '>=',
    value: '>=',
  },
  '>': {
    label: '>',
    value: '>',
  },
  '==': {
    label: '==',
    value: '==',
  },
  '<=': {
    label: '<=',
    value: '<=',
  },
  '<': {
    label: '<',
    value: '<',
  },
};

export const THRESHOLD_SYMBOL_LIST = Object.values(THRESHOLD_SYMBOL);

export const DATA_SOURCE_MAP = {
  data_quality: '数据质量告警',
  self_monitor: '自监控',
  other: '其他',
};

export const OBJECT_TYPE_PAGE_URL = {
  EXECUTOR_APP: '/job-timing/actuator',
  WORKFLOW_WORKER: '/worker-group/management',
  KAFKA: '/platform-management/address/data-source/list',
  NEBULA: '/platform-management/address/data-source/list',
  ELASTICSEARCH: '/platform-management/address/data-source/list',
  CLICKHOUSE: '/platform-management/address/data-source/list',
  REDIS: '/platform-management/address/data-source/list',
  VICTORIA_METRICS: '/platform-management/address/data-source/list',
  HADOOP: '/platform-management/cluster/management/list',
  FLINK_TASK: '/ops/realtime/pipeline/devOps',
  CK_STORAGE: '/ops/realtime/storage',
  HIVE_STORAGE: '/ops/realtime/pipeline/devOps',
  DATA_SERVICE: '/data-service/management?tabName=apiManagement',
  XXL_JOB: '/job-timing/dispatch-log',
  WORKFLOW_JOB: '/ops/offline/workflow',
  OFFLINE_PIPELINE_JOB: '/ops/offline/pipeline',
  OFFLINE_WORKFLOW: '/ops/offline/workflow',
  TABLE_DEPLOY: '/data-modeling/dimension-modeling/modeling/table/edit',
  SYSTEM_CONFIG: '/system/config',
  EXTENSIO_PACK: '/platform-management/extension-package/list',
  TABLE: '/data-modeling/dimension-modeling/modeling/table/edit',
  APPLICATION_JOB: '/flexible-application/ops',
  LIFECYCLE_EXECUTOR: '/life-cycle/actuator/list',
};

export const getObjectTypeUrl = ({ objectType, objectName, objectId, ...others }) => {
  if ([
    'HADOOP',
    'FLINK_TASK',
    'CK_STORAGE',
    'HIVE_STORAGE',
    'XXL_JOB',
    'KAFKA',
    'ELASTICSEARCH',
    'CLICKHOUSE',
    'REDIS',
    'VICTORIA_METRICS',
    'NEBULA',
    'OFFLINE_PIPELINE_JOB',
    'OFFLINE_WORKFLOW',
    'EXTENSIO_PACK',
    'WORKFLOW_JOB',
    'EXECUTOR_APP',
    'APPLICATION_JOB',
    'LIFECYCLE_EXECUTOR',
  ].includes(objectType)) {
    return `${OBJECT_TYPE_PAGE_URL[objectType]}?name=${objectName}`;
  }
  if (['DATA_SERVICE'].includes(objectType)) {
    return `${OBJECT_TYPE_PAGE_URL[objectType]}&name=${objectName}`;
  }
  if (['XXL_JOB', 'TABLE'].includes(objectType)) {
    return `${OBJECT_TYPE_PAGE_URL[objectType]}/${objectId}`;
  }
  if (['TABLE_DEPLOY'].includes(objectType)) {
    const { instanceInfo } = others;
    if (instanceInfo && instanceInfo?.platform) {
      return `${URL_MAP[instanceInfo.platform as keyof typeof URL_MAP]}/${objectId}`;
    }
    return '/data-dev/dev/management';
  }
  return OBJECT_TYPE_PAGE_URL[objectType];
};

export const INSPECT_DASHBOARD_TASK_URL = {
  ingestionJob: {
    url: '/ops/ingestion/cell-agent',
    status: ['RUNNING', 'STOPPED', 'ABNORMAL'],
  },
  realtimePipeline: {
    url: '/ops/realtime/pipeline/devOps',
    status: ['RUNNING', 'STOPPED', 'FAILED'],
  },
  storageCk: {
    url: '/ops/realtime/storage',
    status: ['RUNNING', 'STOPPED', 'FAILED'],
  },
  offlinePipeline: {
    url: '/ops/offline/pipeline',
    status: ['NORMAL', 'RUNNING', 'ABNORMAL'],
  },
  workflow: {
    url: '/ops/offline/processInstance',
    status: ['NORMAL', 'RUNNING', 'ABNORMAL'],
  },
  xxlJob: {
    url: '/job-timing/dispatch-log',
    status: ['1', '3', '2'],
  },
};

export const getInspectDashboardTaskUrl = (type, statusIndex) => {
  const url = INSPECT_DASHBOARD_TASK_URL[type].url;
  const status = INSPECT_DASHBOARD_TASK_URL[type].status[statusIndex];
  if (type === 'xxlJob') {
    return `${url}?status=${status}&hour=24`;
  }
  return `${url}?status=${status}`;
};

export const INSPECT_RUN_TYPE_MAP = {
  MANUAL: {
    label: '手动',
    value: 'MANUAL',
  },
  SCHEDULER: {
    label: '自动',
    value: 'SCHEDULER',
  },
};

export const INSPECT_RUN_TYPE_LIST = Object.values(INSPECT_RUN_TYPE_MAP);

export const INSPECT_STATUS_MAP = {
  EXCEPTION: {
    label: '异常',
    value: 'EXCEPTION',
  },
  NORMAL: {
    label: '正常',
    value: 'NORMAL',
  },
  SUGGESTION: {
    label: '建议',
    value: 'SUGGESTION',
  },
};

export const INSPECT_STATUS_LIST = Object.values(INSPECT_STATUS_MAP);

export const CLOSE_TYPE_MAP = {
  MANUAL: {
    label: '手动关闭',
    value: 'MANUAL',
  },
  AUTO: {
    label: '自动恢复',
    value: 'AUTO',
  },
};
