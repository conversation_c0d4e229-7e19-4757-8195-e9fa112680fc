import { memo, useEffect, useState } from 'react';
import { Spin, Tooltip } from 'antd';

import './LogDownTale.less';

import { TroubleShootingService } from '../../services';

interface Props {
  serverId: string;
  fileName: string;
  module: string;
}

const _LogDownTable = (props: Props) => {
  const { serverId, fileName, module } = props;
  const [fileList, setFileList] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (serverId) {
      getFileList();
    }
  }, [serverId, module, fileName]);
  
  const getFileList = async () => {
    setLoading(true);
    const { data } = await TroubleShootingService.getFileListByServerId(serverId, {
      file: fileName,
      module,
    }).finally(() => setLoading(false));
    setFileList(data.files);
  };

  const downloadFile = (e, item) => {
    e.stopPropagation();
    TroubleShootingService.getDownloadFileByServerId(serverId, item.file);
  };

  const handleOpenLogDetail = file => {
    window.open(
      `/trouble-shooting/log/view?id=${serverId}&file=${file}&line=-100`,'_blank',
    );
  };

  return <div className="file-list flex-1 mt-2 overflow-y-auto flex flex-wrap">
    {loading
      ? <div className="loading-box w-full h-full flex items-center justify-center">
        <Spin />
      </div> :
      fileList.map(item =>
        <Tooltip title={item.file} key={item.file}>
          <div className='logs-item' onClick={() => handleOpenLogDetail(item.file)}>
            <i className='iconfont icon-article-line text-xs mr-1'></i>
            <span className='flex-1 line-clamp-1'>{item.file}</span>
            <i className='iconfont icon-save_alt-line text-xs' onClick={e => downloadFile(e, item)}></i>
          </div>
        </Tooltip>,
      )
    }
  </div>;
};

export const LogDownTable = memo(_LogDownTable);
