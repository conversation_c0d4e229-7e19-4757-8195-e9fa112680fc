import './LogCustomTable.less';

interface Props {
  list: any[];
  className?: string;
  onOpenLog: (record) => void;
}

export const LogCustomTable = (props: Props) => {
  const { list, className, onOpenLog } = props;

  const handleOpenLogDetail = record => {
    onOpenLog(record);
  };

  return <div className={`log-custom-table w-full h-full flex flex-col ${className}`}>
    <div className="header flex">
      <div className='w-[160px]'>时间</div>
      <div className='w-[140px]'>来源</div>
      <div className='w-[80px]'>行号</div>
      <div className='flex-1'>日志</div>
    </div>
    <div className='flex-1 overflow-y-auto'>
      {
        list.map(record => <div key={record.id} className='table-item flex'>
          <div className='item-content w-[160px]'>{ record.time }</div>
          <div className='item-content w-[140px]'>
            <div
              className='cursor-pointer text-primary'
              onClick={() => handleOpenLogDetail(record)}
            >{record.file}</div>
          </div>
          <div className='item-content w-[80px]'>{ record.line }</div>
          <div className='item-content flex-1'>
            <div className='flex'>
              <div className='text-wrap log-item-line' dangerouslySetInnerHTML={{ __html: record.message }}></div>
            </div>
          </div>
        </div>)
      }
    </div>
  </div>;
};
