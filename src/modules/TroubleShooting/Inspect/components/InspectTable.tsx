import { useState } from 'react';
import { Button } from 'antd';
import { orderBy } from 'lodash-es';

import { CustomTable, SectionCaptain } from '@/components';

import { AbnormalInstance } from './AbnormalInstance';

interface Props {
  data: {
    componentCheck: any[];
    jobCheck: any[];
    dataCheck: any[];
    modelCheck: any[];
    configCheck: any[];
  }
}

export const InspectTable = (props: Props) => {
  const { data = {}, onlyShowAbnormal } = props;
  const { componentCheck = [], jobCheck = [], dataCheck = [], modelCheck = [], configCheck = [] } = data;
  const [showAbnormal, setShowAbnormal] = useState(false);
  const [selectedItem, setSelectedItem] = useState('');
  const tableList = [
    {
      title: '组件自检',
      data: orderBy(componentCheck, ['exceptionNum'], ['desc']),
    },
    {
      title: '作业自检',
      data: orderBy(jobCheck, ['exceptionNum'], ['desc']),
    },
    {
      title: '数据自检',
      data: orderBy(dataCheck, ['exceptionNum'], ['desc']),
    },
    {
      title: '模型自检',
      data: orderBy(modelCheck, ['exceptionNum'], ['desc']),
    },
    {
      title: '配置自检',
      data: orderBy(configCheck, ['exceptionNum'], ['desc']),
    },
  ].map(item => {
    const filterData = onlyShowAbnormal ? item.data.filter(item => item.exceptionNum > 0) : item.data;
    return {
      ...item, 
      normalNum: item.data.reduce((acc, cur) => acc + Number(cur.normalNum), 0),
      suggestionNum: item.data.reduce((acc, cur) => acc + Number(cur.suggestionNum), 0),
      exceptionNum: item.data.reduce((acc, cur) => acc + Number(cur.exceptionNum), 0),
      data: filterData,
    };
  });
  
  const columns = [
    {
      title: '组件分类',
      dataIndex: 'component',
    },
    {
      title: '检查项',
      dataIndex: 'checkItem',
    },
    {
      title: '实例数量',
      dataIndex: 'instanceNum',
      width: 150,
    },
    {
      title: '正常',
      dataIndex: 'normalNum',
      width: 120,
      render(val) {
        return <div className={val ? 'text-success' : 'text-gray-4'}>
          <i className='iconfont icon-su mr-1'></i>
          <span>{val}</span>
        </div>;
      },
    },
    {
      title: '建议',
      dataIndex: 'suggestionNum',
      width: 120,
      render(val) {
        return <div className={val ? 'text-warning' : 'text-gray-4'}>
          <i className='iconfont icon-alert-a mr-1'></i>
          <span>{val}</span>
        </div>;
      },
    },
    {
      title: '异常',
      dataIndex: 'exceptionNum',
      width: 120,
      render(val) {
        return <div className={val ? 'text-danger' : 'text-gray-4'}>
          <i className='iconfont icon-alert-octagram mr-1'></i>
          <span>{val}</span>
        </div>;
      },
    },
    {
      title: '操作',
      dataIndex: 'id',
      width: 120,
      render(val) {
        return <Button  size='small' type='link' onClick={() => openAbnormal(val)}>查看实例</Button>;
      },
    },
  ];

  const openAbnormal = val => {
    setShowAbnormal(true);
    setSelectedItem(val);
  };

  return <div className='inspect-table-box overflow-y-auto flex-1'>
    
    {tableList.map(
      (item, index) => <div key={index}>
        <SectionCaptain title={<div className='w-full flex justify-between'>
          <div>
            <span>{item.title}</span>
            <span>（正常：
              <span className='text-success'>{item.normalNum}</span>，
            建议：
              <span className='text-warning'>{item.suggestionNum}</span>，
            异常：
              <span className='text-danger'>{item.exceptionNum}</span>
            ）
            </span>
          </div>
          {/* { 
            index === 0 && <div className='ml-6'>
              <Checkbox checked={onlyShowAbnormal} onChange={e => setOnlyShowAbnormal(e.target.checked)}>只看异常</Checkbox>
            </div>
          } */}
        </div>} className={`ml-3 ${index === 0 ? 'mt-3' : 'mt-1'}`}
        />
        <div>
          <CustomTable columns={columns}
            dataSource={item.data}
            pagination={false}
            // onChange={page => handleChange(page, item)}
          ></CustomTable>
        </div>
      </div>)}
    {
      showAbnormal && 
      <AbnormalInstance id={selectedItem} open={showAbnormal} onCancel={() => setShowAbnormal(false)} />
    }
  </div>;
};
