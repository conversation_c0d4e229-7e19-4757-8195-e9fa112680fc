import { useEffect, useState } from 'react';
import { But<PERSON>, DatePicker, Modal, Select } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components';

import { INSPECT_RUN_TYPE_LIST, INSPECT_RUN_TYPE_MAP } from '../../constants';
import { AlertService } from '../../services/alert';

interface Props {
  open: boolean;
  current: string;
  onCancel: () => void;
  onChange: (id: string) => void;
}
export const InspectHistory = (props: Props) => {
  const { open, current, onCancel, onChange } = props;
  const [list, setList] = useState([]);
  const { pagination, queryParams, setFilter, filter, setPagination, handleTableChange } = useCustomTableHook({
    sort: {
      startTime: 'DESC' as SortTypes,
    },
  });
  const columns = [
    {
      title: '开始时间',
      dataIndex: 'startTime',
      width: 200,
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      width: 200,
    },
    {
      title: '自检方式',
      dataIndex: 'runType',
      render(val) {
        return <span>{ INSPECT_RUN_TYPE_MAP?.[val]?.label }</span>;
      },
    },
    {
      title: '实例数',
      dataIndex: 'instanceNum',
    },
    {
      title: '正常',
      dataIndex: 'normalNum',
      render(val) {
        return <div className='text-success'>
          <span>{val}</span>
        </div>;
      },
    },
    {
      title: '建议',
      dataIndex: 'suggestionNum',
      render(val) {
        return <div className='text-warning'>
          <span>{val}</span>
        </div>;
      },
    },
    {
      title: '异常',
      dataIndex: 'exceptionNum',
      render(val) {
        return <div className='text-danger'>
          <span>{val}</span>
        </div>;
      },
    },
    {
      title: '操作',
      dataIndex: 'id',
      width: 100,
      render(val) {
        return <Button type='link' disabled={current === val} size='small' onClick={() => onChange(val)}>查看</Button>;
      },
    },
  ];

  useEffect(() => {
    getHistory();
  }, [queryParams]);

  const getHistory = async () => {
    const { data, total } = await AlertService.getHealthCheckHistory(queryParams);
    setList(data);
    setPagination({ ...pagination, total });
  };

  const handleCancel = () => {
    onCancel();
  };

  return <Modal
    title='自检历史'
    footer={null}
    width={980}
    open={open}
    classNames={{
      body: 'flex flex-col overflow-hidden pt-2 pb-0 h-[500px]',
    }}
    onCancel={handleCancel}
  >
    <div className='flex mb-2'>
      <div className='flex items-center'>
        <span className='text-[14px] opacity-[0.85]'>时间范围：</span>
        <DatePicker.RangePicker
          showTime
          format='YYYY-MM-DD HH:mm:ss'
          className='w-[330px]'
          onChange={(_, dateString) => {
            setFilter({
              ...filter,
              startTime: dateString[0],
              endTime: dateString[1],
            });
          }}
        />
      </div>
      <div className='ml-3'>
        <label>自检方式：</label>
        <Select
          className='ml-3 w-[150px]'
          placeholder='请选择自检方式'
          defaultValue={filter.runType}
          onChange={value => setFilter({ ...filter, runType: value })}
          allowClear
          options={INSPECT_RUN_TYPE_LIST}
        />
      </div>
    </div>
    <CustomTable columns={columns}
      dataSource={list}
      scroll={true}
      pagination={pagination}
      onChange={handleTableChange}
    ></CustomTable>
  </Modal>;
};
