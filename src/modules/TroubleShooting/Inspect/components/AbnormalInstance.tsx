import { useEffect, useState } from 'react';
import { Button, Modal, Select, Tooltip } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components';

import { getObjectTypeUrl,INSPECT_STATUS_LIST, INSPECT_STATUS_MAP } from '../../constants';
import { AlertService } from '../../services/alert';

interface Props {
  open: boolean;
  id: string;
  onCancel: () => void;
}

const statusClass = {
  'EXCEPTION': 'text-danger',
  'NORMAL': 'text-success',
  'SUGGESTION': 'text-warning',
};
export const AbnormalInstance = (props: Props) => {
  const { open, id, onCancel } = props;
  const [list, setList] = useState([{}]);
  const { pagination, queryParams, setFilter, filter, setPagination, handleTableChange } = useCustomTableHook({
    sort: {
      startTime: 'DESC' as SortTypes,
    },
    filter: {
      status: 'EXCEPTION',
      itemId: id,
    },
  });
  const columns = [
    {
      title: '实例名称',
      dataIndex: 'instanceName',
    },
    {
      title: '异常内容',
      dataIndex: 'content',
      width: 300,
      render(val) {
        return <Tooltip title={val}>
          <div style={{ wordWrap: 'break-word', wordBreak: 'break-word' }} className='w-full truncate'>
            {val || '--'}
          </div>
        </Tooltip>;
      },
    },
    {
      title: '处置建议',
      dataIndex: 'solution',
      width: 400,
      render(val) {
        return <Tooltip title={val}>
          <div style={{ wordWrap: 'break-word', wordBreak: 'break-word' }} className='w-full truncate'>
            {val || '--'}
          </div>
        </Tooltip>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render(val) {
        return <div className={statusClass?.[val]}>
          <span>{INSPECT_STATUS_MAP?.[val]?.label}</span>
        </div>;
      },
    },
    {
      title: '操作',
      dataIndex: 'time',
      width: 100,
      render(_, record) {
        const url = getObjectTypeUrl({
          ...record,
          objectType: record.instanceType,
          objectName: record.instanceName,
          objectId: record.instanceId,
        });
        return <Button type='link' size='small' disabled={!url} onClick={() => window.open(url)}>查看</Button>;
      },
    },
  ];

  useEffect(() => {
    getList();
  }, [queryParams]);

  const getList = async () => {
    const { data, total } = await AlertService.getHealthCheckInstance(queryParams);
    setList(data);
    setPagination({ ...pagination, total });
  };

  const handleCancel = () => {
    onCancel();
  };

  return <Modal
    title='查看实例'
    footer={null}
    width={1180}
    open={open}
    classNames={{
      body: 'flex flex-col overflow-hidden pt-2 pb-0 h-[500px]',
    }}
    onCancel={handleCancel}
  >
    <div className='ml-3 mb-2'>
      <label>状态：</label>
      <Select
        className='ml-3 w-[268px]'
        placeholder='请选择状态'
        defaultValue={filter.status}
        onChange={value => setFilter({ ...filter, status: value })}
        allowClear
        options={INSPECT_STATUS_LIST}
      />
    </div>
    <CustomTable columns={columns}
      dataSource={list}
      pagination={pagination}
      scroll={{ x: undefined }}
      rowKey='id'
      onChange={handleTableChange}
    ></CustomTable>
  </Modal>;
};
