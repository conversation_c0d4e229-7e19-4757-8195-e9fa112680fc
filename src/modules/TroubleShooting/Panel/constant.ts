import { LineOptions } from '@antv/g2plot';

import { DataSourceConfigMap } from '@/modules/AddressManagement/DataSource';
import { CLUSTER_TYPE_LIST } from '@/modules/Cluster/constant';

export const chartOptions: LineOptions = {
  legend: {},
  seriesField: 'name',
  fields: ['x', 'name', 'origin', 'y'],
  tooltip: {
    formatter: data => {
      const { y, name } = data;
      return {
        name,
        value: y,
      };
    },
  },
};

export const CompStatusMap = {
  ...DataSourceConfigMap,
  ...CLUSTER_TYPE_LIST.reduce((res, cur) => {
    res[cur.value] = {
      ...cur,
      name: cur.label,
    };
    return res;
  }, {}),
};
