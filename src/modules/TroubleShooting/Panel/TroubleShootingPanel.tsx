import { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Checkbox, Collapse, Popover, Radio, Tooltip } from 'antd';
import { history } from 'umi';

import { getRealRangeTime } from '@/components/visual/tool/TimeRangerSelector/helper';
import { useGlobalHook } from '@/hooks';

import './TroubleShootingPanel.less';

import { InspectCompItem } from '../models/Alert';
import { AlertService } from '../services/alert';
import { generateCountChartData, generateNormalChartData, generateStorageChartData } from '../utils/charts';
import { getCompStatusTooltips } from '../utils/helps';

import { CKCharts } from './components/CKCharts';
import { EsCharts } from './components/EsCharts';
import { HadoopCharts } from './components/HadoopCharts';
import { TaskCard } from './components/TaskCard';
import { CompStatusMap } from './constant';

const chartNameList = [
  { label: ' Clickhouse数据存储趋势', value: 'CLICKHOUSE' },
  { label: ' Elasticsearch数据存储趋势', value: 'ELASTICSEARCH' },
  { label: ' Hadoop集群容量趋势', value: 'HADOOP' },
];
const dayOptions = [
  { label: '1天', value: 'now-1d' },
  { label: '3天', value: 'now-3d' },
  { label: '7天', value: 'now-7d' },
  { label: '30天', value: 'now-30d' },
];

export const TroubleShootingPanel = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [taskInfo, setTaskInfo] = useState<any>();
  const [checkedChart, setCheckedChart] = useState(['CLICKHOUSE', 'ELASTICSEARCH', 'HADOOP']);
  const [selectedDay, setSelectedDay] = useState('now-1d');
  const [insideComp, setInsideComp] = useState<InspectCompItem[]>([]);
  const [outsideComp, setOutsideComp] = useState<InspectCompItem[]>([]);
  const [chartData, setChartData] = useState<any>();
  const chartContainer = useRef<HTMLElement>();
  const chartContainerWidth = useRef<number>();
  const dayRangeParam = useMemo(() => {
    return getRealRangeTime([selectedDay, 'now'])!;
  }, [selectedDay]);

  useEffect(() => {
    setPageInfo({ title: '巡检仪表板', description: <></> });
    getDashboard();
    return () => {
      resetPageInfo();
    };
  }, []);
  
  useLayoutEffect(() => {
    chartContainerWidth.current = chartContainer.current?.offsetWidth ?? 1200;
  }, []);
  
  useEffect(() => {
    if(!checkedChart.length) return;
    getCharts();
  }, [dayRangeParam, checkedChart]);

  const getDashboard = async () => {
    const { data } = await AlertService.getInspectDashboard();
    setInsideComp(data.internalComponentStatus);
    setOutsideComp(data.externalComponentStatus);
    setTaskInfo(data);
  };

  const getCharts = async () => {
    const { data } = await AlertService.getInspectDashboardCharts({
      width: chartContainerWidth.current,
      beginTime: dayRangeParam[0],
      endTime: dayRangeParam[1],
      type: checkedChart,
    });
    setChartData({
      ck: data?.ckStorageCount ? {
        compressedData: generateStorageChartData(data?.ckStorageCount?.compressedChart),
        uncompressedData: generateStorageChartData(data?.ckStorageCount?.uncompressedChart),
        ...data.ckStorageCount,
      } : null,
      es: data?.esStorageCount ? {
        storageData: generateStorageChartData(data?.esStorageCount?.storageChart),
        countData: generateCountChartData(data?.esStorageCount?.docCountChart),
        ...data.esStorageCount,
      } : null,
      hadoop: data?.hadoopCount ? {
        storageData: generateStorageChartData(data?.hadoopCount?.storageChart),
        cpuData: generateNormalChartData(data?.hadoopCount?.yarnCpuChart),
        memoryData: generateStorageChartData(data?.hadoopCount?.yarnMemoryChart),
        ...data.hadoopCount,
      } : null,
    });
  };

  const gotoAlert = (event: Event, item, isSub) => {
    event.stopPropagation();
    let query = '';
    if (isSub) {
      query = `?objectType=${item.objectType}&objectName=${item.name}`;
    } else {
      query = `?objectType=${item.objectType}`;
    }
    history.push(`/trouble-shooting/alert-center/list${query}`);
  };

  const onChangeChart = val => {
    setCheckedChart(val);
  };
  const handleChangeDay = e => {
    setSelectedDay(e.target.value);
  };

  const DataSourceInfo = ({ list }) => {
    return <>
      {list?.length ? list?.map((item, index) => {
        return <div className='flex' key={index}>
          <div className='shrink-0'>{item.label}：</div>
          <div>{item.value}</div>
        </div>;
      },
      ) : null}
    </>;
  };

  const CompItem = ({ item, isSub, type }) => {
    const itemType = item?.type || type;
    const name = !isSub ? CompStatusMap[itemType]?.name || item.name : item.name;
    const icon = !isSub ? CompStatusMap[itemType]?.icon : null;
    const info = item?.description ? item?.description : getCompStatusTooltips(itemType, item.detail);

    return <div className="comp-item h-[32px] flex items-center cursor-pointer overflow-hidden" key={item.name}>
      <div className={`status-icon status-${item.disabled ? 'disabled' : item.status}`}></div>
      <div
        className={`items-center comp-item-label text-gray-12 flex-1 overflow-hidden pl-3 flex 
        ${item.disabled ? 'text-gray-4' : ''}`
        }
      >
        {icon && <i className={`iconfont comp-icon ${icon}`}></i>}
        <div className='ml-1 text-sm flex-1 truncate'>
          {
            isSub
              ? <Tooltip overlayClassName='max-w-[1000px]' title={<DataSourceInfo list={info} />}>
                <span title={name} onClick={e => gotoAlert(e, item, isSub)}>{ name }</span>
              </Tooltip>
              : <>
                <span title={name} onClick={e => gotoAlert(e, item, isSub)}>{ name }</span>
                {
                  info &&
                  <Tooltip title={info}>
                    <QuestionCircleOutlined className='ml-1 text-gray-8 text-[12px]' />
                  </Tooltip>
                }
              </>
          }
        </div>
      </div>
    </div>;
  };

  return <div className="w-full h-full flex">
    <div className='left-comp-list w-[240px] h-full bg-white flex flex-col'>
      <div className='h-[40px] header  w-full pl-3 flex items-center font-medium'>组件状态</div>
      {
        insideComp?.length
          ?<div className='inside-comp-list py-2'>
            {insideComp.map(item =><CompItem key={item.name} item={item}></CompItem>)}
          </div>
          : null
      }
      <div className='outside-comp-list py-2 flex-1 overflow-y-auto'>
        {outsideComp.map(item => {
          return item?.datasourceStatus
            ?
            <Collapse
              ghost
              className='comp-collapse'
              items={[{
                key: item.name,
                label: <CompItem key={item.name} item={item} />,
                children: item.datasourceStatus.map(child =>
                  <CompItem key={child.name} type={item?.type} isSub item={child} />),
              }]}
              key={item.name}
              expandIconPosition='end'
            /> 
            : <CompItem key={item.name} item={item}></CompItem>;
        })
        }
      </div>
    </div>
    <div className="right-content ml-2 flex-1 h-full flex flex-col bg-white min-w-0">
      <div className="card-box flex py-4">
        <TaskCard
          type='continuous'
          icon='collect-line'
          className='border-card'
          info={taskInfo?.cellJobCount}
          title='采集任务'
        />
        <TaskCard type='continuous'
          icon='timetask-line' className='border-card' info={taskInfo?.realtimeJobCount} title='实时处理任务'
        />
        <TaskCard type='single'
          icon='wifi_off-fill'
          className='border-card'
          info={taskInfo?.offlineJobCount}
          title='离线处理任务'
        />
        <TaskCard type='single' icon='restore-line' info={taskInfo?.xxlJobCount} title='定时任务' />
      </div>
      <div className="chat-config mt-3 px-3 flex justify-between">
        <Popover
          content={
            <Checkbox.Group
              options={chartNameList}
              onChange={onChangeChart}
              value={checkedChart}
            />
          }
          placement="bottom"
          trigger="click"
          rootClassName='trouble-panel-chart-config-popover'
        >
          <Button type='primary'>
            <div className='flex items-center'>
              <i className='iconfont icon-settings-line mr-1'></i>
              图表配置({ checkedChart.length }/3)
            </div>
          </Button>
        </Popover>
        <div className='flex'>
          <Radio.Group
            options={dayOptions}
            value={selectedDay}
            onChange={handleChangeDay}
            optionType='button'
            buttonStyle="solid"
          />
        </div>
      </div>
      <div className="charts-content flex-1 mt-3 px-3 overflow-y-auto" ref={chartContainer}>
        {
          checkedChart.length ? <>
            {chartData?.ck && <CKCharts value={chartData?.ck} />}
            {chartData?.es && <EsCharts value={chartData?.es} />}
            {chartData?.hadoop && <HadoopCharts value={chartData?.hadoop} />}
          </> : null
        }
      </div>
    </div>
  </div>;
};
