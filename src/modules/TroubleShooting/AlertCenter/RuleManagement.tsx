import { useEffect, useMemo, useRef, useState } from 'react';
import { ExclamationCircleFilled, FolderOutlined } from '@ant-design/icons';
import Split from '@uiw/react-split/cjs';
import { Button, Form, message,Modal, Switch, Tree } from 'antd';
import dayjs from 'dayjs';

import { CustomTable } from '@/components';
import { useGlobalHook } from '@/hooks';

import './RuleManagement.less';

import { AlertLevelTag } from '../components/AlertLevelTag';
import { RuleDetail, RuleTreeItem, ThresholdInfo } from '../models/Alert';
import { AlertService } from '../services/alert';

import { RuleModal } from './components/RuleModal';

const formItemLayout = {
  labelCol: { style: { width: '86px' } },
  wrapperCol: { span: 20 },
};

export const RuleManagement = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [selectedThreshold, setSelectedThreshold] = useState<ThresholdInfo>();
  const [selectedTreeKeys, setSelectedTreeKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [showRuleModal, setShowRuleModal] = useState(false);
  const [form] = Form.useForm();
  const [monitorTree, setMonitorTree] = useState<RuleTreeItem[]>();
  const [ruleDetail, setRuleDetail] = useState<RuleDetail>({});
  const flatTreeData = useRef<Record<string, RuleTreeItem>>({});
  const [enableLoading, setEnableLoading] = useState(false);
  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name',
    },
    {
      title: '时间段',
      dataIndex: 'startTime',
      render(_, record) {
        return <span>{record.startTime} - { record.endTime }</span>;
      },
    },
    {
      title: '告警等级',
      dataIndex: 'alarmLevel',
      render(value) {
        return <AlertLevelTag value={value} />;
      },
    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      render(threshold) {
        if (thresholdMethod === 'LIVENESS') return '--';
        if (thresholdMethod === 'NORMAL') return threshold?.threshold
          ? `${threshold.operator || '>='}${threshold.threshold}`
          : '--';
        if(thresholdMethod === 'KAFKA_LAG_TOLERANCE') return threshold?.threshold ? `${threshold.threshold}分钟` : '--';
      },
    },
    {
      title: '操作',
      dataIndex: 'operate',
      render: (_, record) => {
        return <>
          <Button type='link' size='small' onClick={() => handleEditRule(record)}>编辑</Button>
          <Button type='link' size='small' className='ml-2' onClick={() => handleDeleteRule(record)}>删除</Button>
        </>;
      },
    },
  ];
  const thresholdMethod = useMemo(() => ruleDetail?.thresholdMethod, [ruleDetail]);
  const selectedRuleId = useMemo(() => {
    if (selectedTreeKeys?.length) {
      const id = selectedTreeKeys[0];
      return flatTreeData.current[id].ruleId;
    }
  }, [selectedTreeKeys]);
  const ruleName = useMemo(() => {
    if (selectedTreeKeys?.length) {
      const res = [];
      const id = selectedTreeKeys[0];
      generateRuleName(id, res);
      return res.join('/');
    }
  }, [selectedTreeKeys]);

  useEffect(() => {
    setPageInfo({ title: '规则管理', description: <></> });
    getMonitorTree();
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    if (selectedRuleId) {
      getRuleDetail();
    }
  }, [selectedRuleId]);

  const handleFlatTree = list => {
    list.forEach(item => {
      if (item?.children?.length) {
        handleFlatTree(item.children);
      }
      flatTreeData.current[item.id] = item;
    });
  };

  function generateRuleName (id, res: any[]) {
    const item = flatTreeData.current[id];
    res.unshift(item.name);
    if (item.parentId) {
      generateRuleName(item.parentId, res);
    }
  };

  const getMonitorTree = async () => {
    const { data } = await AlertService.getMonitorTree();
    setMonitorTree(data);
    handleFlatTree(data);
    if (data?.length) {
      const item = findFirstRule(data);
      if (item) {
        setSelectedTreeKeys([item.id]);
      }
      setExpandedKeys(Object.keys(flatTreeData.current));
    }
  };

  const findFirstRule = list => {
    if(!list?.length) return;
    const item = list.find(item => item.ruleId);
    if (item) {
      return item;
    } else {
      return findFirstRule(list[0].children || []);
    }
  };

  const handleSelectMonitor = (_, event) => {
    if (event.node.ruleId) {
      setSelectedTreeKeys([event.node.id]);
    }
    if (!event.node.isLeaf) {
      const id = event.node.id;
      if (expandedKeys.includes(id)) {
        setExpandedKeys(keys => keys.filter(item => item !== id));
      } else {
        setExpandedKeys(keys => [...keys, id]);
      }
    }
  };

  const getRuleDetail = async () => {
    const { data } = await AlertService.getRuleDetail(selectedRuleId);
    setRuleDetail(data);
  };

  const handleChangeChecked = async checked => {
    setEnableLoading(true);
    if (checked) {
      await AlertService.enableRule(ruleDetail.id).finally(() => setEnableLoading(false));
    } else {
      await AlertService.disableRule(ruleDetail.id).finally(() => setEnableLoading(false));
    }
    setRuleDetail(item => ({
      ...item,
      isEnabled: checked,
    }));
  };

  const handleEditRule = record => {
    setShowRuleModal(true);
    setSelectedThreshold(record);
  };
  const handleOpenAddRule = () => {
    setShowRuleModal(true);
    setSelectedThreshold(undefined);
  };

  const handleThreshold = async val => {
    const params = {
      alarmLevel: val.alarmLevel,
      name: val.name,
      startTime: dayjs(val.time[0]).format('HH:mm'),
      endTime: dayjs(val.time[1]).format('HH:mm'),
      threshold: {},
    };
    if (thresholdMethod !== 'LIVENESS') {
      if (thresholdMethod === 'NORMAL') {
        params.threshold = { threshold: val.thresholdValue, operator: val.operator };
      } else {
        params.threshold = { threshold: val.thresholdValue };
      }
    }
    if (selectedThreshold) { // 编辑
      await AlertService.updateThreshold(selectedThreshold.id, params);
      message.success('编辑成功');
    } else {
      params.ruleId = selectedRuleId;
      await AlertService.addThreshold(params);
      message.success('添加成功');
    }
    setShowRuleModal(false);
    getRuleDetail();
  };

  const handleDeleteRule = async item => {
    Modal.confirm({
      title: '确定删除此阈值策略吗?',
      icon: <ExclamationCircleFilled />,
      async onOk() {
        await AlertService.deleteThreshold(item.id);
        message.success('删除成功');
        getRuleDetail();
      },
    });
  };

  const setIcon = props => {
    if (!props.isLeaf) {
      return <FolderOutlined />;
    }
    return <div className='w-full h-full flex items-center justify-center'>
      <div className='bg-primary w-[6px] h-[6px] rounded-full'></div>
    </div>;
  };

  const setTitleRender = node => {
    if (!node.isLeaf) {
      return <div className='w-full truncate'>{ node.name }</div>;
    }
    return <div className='w-full truncate' title={node.name}>
      {node.name}
    </div>;
  };

  return <div className="alert-list-container w-full h-full bg-white">
    <Split lineBar className='w-full h-full flex overflow-hidden cluster-files bg-white'>
      <div style={{ width: 280, minWidth: 200 }} className='py-2 overflow-y-auto'>
        {monitorTree && <Tree
          className='rule-tree-content'
          expandedKeys={expandedKeys}
          onSelect={handleSelectMonitor}
          onExpand={keys => setExpandedKeys(keys)}
          treeData={monitorTree}
          icon={setIcon}
          titleRender={setTitleRender}
          showIcon
          selectedKeys={selectedTreeKeys}
          fieldNames={{ title: 'name', key: 'id' }}
          blockNode
        />}
      </div>
      <div className='flex-1 px-4 py-1.5 flex flex-col overflow-hidden'>
        <Form
          {...formItemLayout}
          form={form}
          labelAlign='left'
          className='w-full'
        >
          <Form.Item label="监控项">
            <div>{ruleName}</div>
          </Form.Item>
          <Form.Item label="描述">
            <div>{ruleDetail?.description}</div>
          </Form.Item>
          <div className='flex'>
            <Form.Item label="监控开关" className='w-[30%]'>
              <Switch
                loading={enableLoading}
                disabled={!ruleDetail}
                checked={ruleDetail?.isEnabled}
                onChange={handleChangeChecked}
              />
            </Form.Item>
            <Form.Item label="采样周期">
              <div>{ ruleDetail?.monitorInterval}s</div>
            </Form.Item>
          </div>
          <Form.Item label="处置建议">
            <div>{ruleDetail?.solution}</div>
          </Form.Item>
        </Form>
        <Button
          type='primary'
          className='mt-2 mb-2 w-fit'
          disabled={!ruleDetail}
          onClick={handleOpenAddRule}
        >添加阈值策略</Button>
        <CustomTable
          columns={columns}
          dataSource={ruleDetail?.threshold}
          pagination={false}
          scroll={true}
        />
      </div>
    </Split>
    {
      showRuleModal && <RuleModal
        ruleDetail={ruleDetail}
        open={showRuleModal}
        detail={selectedThreshold}
        onCancel={() => setShowRuleModal(false)}
        onOk={handleThreshold}
      ></RuleModal>
    }
    
  </div>;
};
