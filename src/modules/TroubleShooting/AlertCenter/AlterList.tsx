import { useEffect, useState } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Col, Dropdown, Form, Input, Modal, Row, Select, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useSearchParams } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { TimeRangeSelector } from '@/components/visual/tool/TimeRangerSelector';
import { getRealTime } from '@/components/visual/tool/TimeRangerSelector/helper';
import { useGlobalHook } from '@/hooks';
import { CommonApi } from '@/services';

import { AlertLevelSelect } from '../components/AlertLevelSelect';
import { AlertLevelTag } from '../components/AlertLevelTag';
import { AlterItem } from '../models/Alert';
import { AlertService } from '../services/alert';

import { SubAlertInfo } from './SubAlterInfo';

export const AlterList = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [openDelete, setOpenDelete] = useState(false);
  const [list, setList] = useState<AlterItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<AlterItem>();
  const [form] = Form.useForm();
  const [range, setRange] = useState(['now-24h', 'now']);
  const [objectTypeEnum, setObjectTypeEnum] = useState([]);
  const [solutionsList, setSolutionsList] = useState({});
  const [searchParams] = useSearchParams();
  const objectType = searchParams.get('objectType');
  const objectName = searchParams.get('objectName');

  const {
    pagination,
    queryParams,
    selectedRowKeys,
    setPagination,
    handleTableChange,
    setSelectedRowKeys,
    setFilter,
    filter,
    onRowSelectionChange,
  } = useCustomTableHook({
    // cacheId: !objectType && !objectName ? 'alertList' : undefined,
    sort: {
      lastTime: 'DESC' as SortTypes,
    },
    filter: {
      status: 'OPEN',
      startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      objectType,
      objectName,
    },
  });
  const columns = [
    {
      title: '告警内容',
      dataIndex: 'content',
      width: 350,
      render(val, record) {
        return <div className='flex'>
          <div className='flex-1 mr-1 truncate'>
            <Tooltip title={val}>
              <span className='inline-block max-w-full truncate'>{val}</span>
            </Tooltip>
          </div>
          {
            expandedRowKeys.includes(record.id)
            && <UpOutlined className='text-xs text-gray-6' onClick={() => handleExpend(record, false)} />
          }
          {
            !expandedRowKeys.includes(record.id)
            && <DownOutlined className='text-xs text-gray-6' onClick={() => handleExpend(record, true)} />
          }
          
        </div>;
      },
    },
    {
      title: '告警等级',
      dataIndex: 'severity',
      width: 80,
      sorter: true,
      render(value) {
        return <AlertLevelTag value={value} />;
      },
    },
    {
      title: '告警状态',
      dataIndex: 'status',
      width: 80,
      render(value) {
        return <span>{ value === 'OPEN' ? '打开' : '已关闭'}</span>;
      },
    },
    {
      title: '重复次数',
      width: 80,
      dataIndex: 'occurCount',
      sorter: true,
    },
    {
      title: '首次告警时间',
      width: 160,
      dataIndex: 'occurTime',
      sorter: true,
    },
    {
      title: '最近告警时间',
      width: 160,
      dataIndex: 'lastTime',
      sorter: true,
    },
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      dataIndex: 'operate',
      render(_, record) {
        return <div className='flex items-center'>
          <Button size='small' className='mr-2' type="link" onClick={() => handleExpend(record, true)}>查看</Button>
          <Button
            size='small'
            className='mr-2'
            type="link"
            disabled={record.status === 'CLOSED'}
            onClick={() => openCloseModal(record)}
          >关闭</Button>
          {/* <Button size='small' type="link">查看事件</Button> */}
        </div>;
      },
    },
  ];

  const expandConfig = {
    expandedRowRender: record =>
      <SubAlertInfo info={record} objectTypeEnum={objectTypeEnum} solutionsList={solutionsList} />,
    expandedRowKeys,
    showExpandColumn: false,
  };

  useEffect(() => {
    setPageInfo({ title: '告警列表', description: <></> });
    getObjectTypeEnum();
    getSolutionList();
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    getAlertList();
  }, [queryParams]);

  const handleChangeTime = range => {
    setRange(range);
    const [start, end] = range;
    setFilter({
      ...filter,
      startTime: getRealTime(start).format('YYYY-MM-DD HH:mm:ss'),
      endTime: getRealTime(end).format('YYYY-MM-DD HH:mm:ss'),
    });
  };

  const getObjectTypeEnum = async () => {
    const { data } = await CommonApi.getEnumByCode2('SelfMonitorObjectTypeEnum');
    setObjectTypeEnum((data || []).reduce((res, cur) => {
      res[cur.name] = cur.message;
      return res;
    }, {}));
  };

  const getSolutionList = async () => {
    const { data } = await AlertService.getSolutionsList();
    setSolutionsList(data);
  };

  const handleExpend = (record, ifExpend) => {
    if (ifExpend) {
      if (expandedRowKeys.includes(record.id)) {
        return;
      }
      setExpandedRowKeys(val => {
        return [...val, record.id];
      });
    } else {
      if (!expandedRowKeys.includes(record.id)) {
        return;
      }
      setExpandedRowKeys(keys => {
        return keys.filter(key => key !== record.id);
      });
    }
  };

  const handleBranch = val => {
    if (val.key === 'close') {
      setOpenDelete(true);
    }
  };

  const getAlertList = async () => {
    setLoading(true);
    const [start, end] = range;
    const { data, total } = await AlertService.getAlertList({
      ...queryParams,
      filter: {
        ...queryParams.filter,
        startTime: getRealTime(start).format('YYYY-MM-DD HH:mm:ss'),
        endTime: getRealTime(end).format('YYYY-MM-DD HH:mm:ss'),
      },
    }).finally(() => setLoading(false));
    
    setPagination({
      ...pagination,
      total,
    });
    setList(data);
  };

  const openCloseModal = async item => {
    setOpenDelete(true);
    setSelectedAlert(item);
  };

  const handleClose = async () => {
    const value = await form.validateFields();
    if (selectedAlert) { // 单个
      await AlertService.closeAlert(selectedAlert.id, value);
    } else {
      await AlertService.batchClose({
        ...value,
        ids: selectedRowKeys,
      });
    }
    getAlertList();
    setOpenDelete(false);
    setSelectedAlert(undefined);
    setSelectedRowKeys([]);
  };

  const getRowClassName = record => {
    if (record.status === 'CLOSED') {
      return 'text-gray-5';
    }
    return '';
  };

  const rowSelection = {
    getCheckboxProps: record => ({
      disabled: record.status === 'CLOSED',
    }),
    selectedRowKeys,
  };

  const handleClearFilter = () => {
    setRange(['now-24h', 'now']);
    setFilter({
      status: 'OPEN',
      startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    });
  };

  const handleRefresh = () => {
    const [start, end] = range;
    const realStart = getRealTime(start).format('YYYY-MM-DD HH:mm:ss');
    const realEnd = getRealTime(end).format('YYYY-MM-DD HH:mm:ss');
    if (realStart === filter.startTime && realEnd === filter.endTime) {
      getAlertList();
      return;
    }
    setFilter({
      ...filter,
      startTime: realStart,
      endTime: realEnd,
    });
  };

  return <div className="alert-list-container w-full h-full bg-white flex-col flex">
    <div className='px-3 py-3 flex justify-between'>
      <Row gutter={[8, 8]}>
        <Col>
          <Dropdown
            disabled={!selectedRowKeys.length}
            menu={{
              items: [
                { label: '关闭', key: 'close' },
              ],
              onClick: handleBranch,
            }}
          >
            <Button>
              <Space>
                  批量操作
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </Col>
        <Col>
          <SearchInput
            placeholder='查询告警内容'
            onSearch={val => setFilter({ ...filter, content: val })}
          />
        </Col>
        <Col>
          <span>告警等级：</span>
          <AlertLevelSelect
            className='min-w-[240px]'
            multiple
            value={filter.severity}
            allowClear
            onChange={val => setFilter({ ...filter, severity: val })}
          />
        </Col>
        <Col>
          <span>告警状态：</span>
          <Select
            className='w-[140px]'
            value={filter.status}
            allowClear
            placeholder='请选择告警状态'
            options={[{ value: 'OPEN', label: '打开' }, { value: 'CLOSED', label: '关闭' }]}
            onChange={val => setFilter({ ...filter, status: val })}
          ></Select>
        </Col>
        <Col>
          <div className='flex items-center'>
            <span>时间范围：</span>
            <TimeRangeSelector
              range={range}
              onChange={handleChangeTime}
            />
          </div>
        </Col>
      </Row>
      <div className='flex items-center'>
        <Button type='link' size='small' onClick={handleClearFilter}>重置</Button>
        <Button
          type='text'
          onClick={handleRefresh}
        >
          <i className='iconfont icon-refresh-line'></i>
        </Button>
      </div>
    </div>
    <CustomTable
      className='flex-1'
      rowKey='id'
      loading={loading}
      columns={columns}
      // cacheId='alertList'
      resizable
      pagination={pagination}
      expandable={expandConfig}
      dataSource={list}
      rowSelection={rowSelection}
      rowClassName={getRowClassName}
      scroll={{ x: 1200 }}
      onRowSelectionChange={onRowSelectionChange}
      onChange={handleTableChange}
    />
    <Modal
      title='确认关闭'
      onOk={handleClose}
      onCancel={() => {
        setOpenDelete(false);
      }}
      open={openDelete}
    >
      <Form form={form}>
        <Form.Item
          label="关闭原因"
          name="closeReason"
          rules={[{ required: true }]}
        >
          <Input.TextArea />
        </Form.Item>
      </Form>
    </Modal>
  </div>;
};
