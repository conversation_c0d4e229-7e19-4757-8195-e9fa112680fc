.index-page {
  overflow: auto;

  @media only screen
    and (max-width: 1280px) {
      .index-page-header-title {
        font-size: 40px !important;
      }

      .index-page-header-subtitle {
        font-size: 20px !important;
      }

      .index-page-content {
        margin: 28px;
      }
    }
  @media only screen
    and (min-width: 1600px) {
      .index-page-content {
        margin: 40px 52px;

        .item-content {
          padding: 20px;
        }

        .arrow-icon {
          margin: 0 16px;
        }
      }
    }
  @media only screen
    and (max-height: 800px) {
      .inner-header {
        padding: 20px 32px;
      }

      .index-page-header-title {
        margin: 24px 0;
      }

      .index-page-content {
        .item-content {
          margin-top: 0;
        }
      }
    }
  @media only screen
    and (min-height: 800px) {
      .index-page-header-title {
        margin: 60px 0 38px;
      }

      .index-page-content {
        .item-content {
          margin-top: 0;
        }

        .item {
          height: 500px;
        }
      }

      .inner-header {
        padding: 32px 52px;
      }
    }

  &-header {
    background-repeat: no-repeat;
    background-size: cover;

    .inner-header {
      background-image: linear-gradient(99deg, rgb(9 109 217 / 53%) 0%, rgb(8 75 147 / 53%) 100%);
      padding: 20px 32px;
    }
  }

  &-content {
    margin: 28px;

    .item {
      flex: 1;
      min-height: 400px;
      padding: 20px 8px;
      background: #FFF;
      border-radius: 2px;
      display: flex;
      flex-direction: column;

      &-title-box {
        .item-title {
          font-size: 24px;
          line-height: 32px;
          font-weight: 500px;
          margin-top: 20px;

          &-icon {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background-color: #D0DDFE;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;

            .iconfont {
              font-size: 48px !important;
            }
          }

          &-desc {
            font-size: 12px;
            color: rgb(0 0 0 / 45%);
            text-align: center;
            line-height: 20px;
          }
        }
      }

      &-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-top: 12px;
        list-style: none;

        li {
          flex: 1;
          display: flex;
          margin-top: 12px;
          padding: 0 12px;
        }

        &-title-box {
          display: flex;
          align-items: center;
        }

        &-title-icon {
          width: 48px;
          height: 48px;
          border-radius: 24px;
          background-color: #F0F0F0;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 12px;

          .iconfont {
            font-size: 32px !important;
            color: #000;
            opacity: 0.65;
          }
        }

        &-title {
          font-size: 16px;
          color: rgb(0 0 0 / 65%);
          line-height: 24px;
          font-weight: 500;
        }

        &-title-desc {
          margin-top: 4px;
          font-size: 12px;
          color: rgb(0 0 0 / 45%);
          line-height: 20px;
          font-weight: 400;
        }
      }
    }

    .arrow-icon {
      margin: 0 4px;
      display: flex;
      align-items: center;
    }
  }
}
