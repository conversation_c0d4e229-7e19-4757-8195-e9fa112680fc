import Request from '@/request';

import { AlgorithmLabInfo } from '../models';

const url = '/api/v2/ingestion/algorithm-lab';

export const AlgorithmLabApi = {
  /**
   * 查询ck集群下的数据库
   * @param dsId 数据库Id
   * @returns
   */
  async getList(data, options?: object) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  async createLab(data: AlgorithmLabInfo) {
    return await Request.post(`${url}`, { data });
  },
  async updateLab(data: AlgorithmLabInfo) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  async deleteLab(id: number) {
    return await Request.delete(`${url}/${id}`);
  },
  async checkStatus() {
    return await Request.get(`${url}/check-jupyterhub-status`);
  },
  async getLabUrl(id) {
    return await Request.get(`${url}/jupyterhub-access-url/${id}`);
  },
  async checkItemStatus(id) {
    return await Request.get(`${url}/sync-one-jupyterhub-server-status/${id}`);
  },
  async stopLab(id) {
    return await Request.get(`${url}/stop-jupyterhub-server/${id}`);
  },
};
