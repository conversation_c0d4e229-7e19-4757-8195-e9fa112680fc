import Request from '@/request';

class ImportService {
  constructor(public url: string) {}
  // 预览文件
  async getPreviewFile(type: IMPORT.Type, file: FormData, flowId?: string, otherParams = {}) {
    const res = await Request.post<Response<IMPORT.ExcelExample[]>>(`${this.url}/preview`, {
      params: { type, flowId, ...otherParams },
      data: file,
    });

    return res;
  }

  // 导入文件
  async uploadImportFile(params: { type: IMPORT.Type; mode: string }, file: FormData) {
    const res = await Request.post<Response<IMPORT.ImportExcel>>(`${this.url}/import`, {
      params,
      data: file,
    });
    return res;
  }

  // 数据样例
  async getExcelExample(type: IMPORT.Type, otherParams={}) {
    const { data } = await Request.get(`${this.url}/example`, {
      params: { type, ...otherParams },
    });

    return data;
  }

  // 查询导入列表

  getImportList = async (data: IMPORT.API.ListQueryParam) => {
    const res = await Request.post<Response<IMPORT.API.ListItem[]>>(`${this.url}/list/query`, {
      data,
    });

    return res;
  };

  // 查询导入列表详情
  getImportListDetail = async (id: string) => {
    const res = await Request.get<Response<IMPORT.API.ListItem>>(`${this.url}/${id}`);
    return res.data;
  };
}

class DataDevImportService {
  constructor(public url: string) {}
  // 预览文件
  async getPreviewFile(type: IMPORT.Type, flowId: string, file: FormData) {
    const res = await Request.post<Response<IMPORT.ExcelExample[]>>(`${this.url}/preview`, {
      params: { type, flowId },
      data: file,
    });

    return res;
  }

  // 导入文件
  async uploadImportFile(params: { type: IMPORT.Type; flowId: string; mode: string }, file: FormData) {
    const res = await Request.post<Response<IMPORT.ImportExcel>>(`${this.url}/import`, {
      params,
      data: file,
    });
    return res;
  }
}

export const importServices = new ImportService('/api/v2/data/modeling/excel');
export const dataDevImportServices = new DataDevImportService('/api/v2/data/modeling/data-development');

export default importServices;
