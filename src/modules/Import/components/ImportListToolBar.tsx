import { ClockCircleOutlined } from '@ant-design/icons';
import { Button, DatePicker, Select, SelectProps, TimeRangePickerProps } from 'antd';
import { useNavigate } from 'umi';

import { ImportTypeData } from '../constant';

interface Props {
  value: IMPORT.API.ListQueryParam;
  onChange: (value: {}) => void;
}

const { RangePicker } = DatePicker;

export const ImportListToolBar = ({ value, onChange }: Props) => {
  const navigate = useNavigate();

  const handleCreateList = () => {
    navigate('/import/management/create');
  };

  const handleDateChange: TimeRangePickerProps['onChange'] = (dates, dateString) => {
    if (dates) {
      onChange({
        ...value,
        filter: {
          ...value?.filter,
          startTime: `${dateString[0]} 00:00:00`,
          endTime: `${dateString[1]} 23:59:59`,
        },
      });
    } else {
      onChange({
        ...value,
        filter: null,
      });
    }
  };

  const handleTypeChange: SelectProps['onChange'] = type => {
    onChange({
      ...value,
      filter: {
        ...value?.filter,
        type,
      },
    });
  };

  return (
    <div className='flex px-5 py-3 items-center text-header gap-6'>
      <Button type='primary' onClick={handleCreateList}>
        创建
      </Button>
      <div className='flex items-center'>
        <span>操作日期：</span>
        <RangePicker
          style={{ width: '224px' }}
          suffixIcon={<ClockCircleOutlined />}
          format='YYYY-MM-DD'
          onChange={handleDateChange}
        />
      </div>
      <div className='flex items-center'>
        <span>导入类型：</span>
        <Select
          placeholder='请选择'
          style={{ width: '280px' }}
          fieldNames={{ label: 'label', options: 'children' }}
          options={ImportTypeData}
          onChange={handleTypeChange}
          allowClear
        />
      </div>
    </div>
  );
};

export default ImportListToolBar;
