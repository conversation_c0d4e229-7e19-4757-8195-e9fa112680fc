import { Radio } from 'antd';

import { SectionCaptain } from '@/components/ui';

import { ImportModules } from '../constant';

interface ModuleTypeProps {
  functionalModule: string;
  setFunctionalModule: (val) => void;
}

export const ModuleType = (props: ModuleTypeProps) => {
  const { functionalModule, setFunctionalModule } = props;
  return (
    <>
      <SectionCaptain title='功能模块' />
      <div className='pl-2 py-2 flex items-center'>
        <div className='w-[94px] mr-5 text-black text-sm'>选择模块:</div>
        <Radio.Group
          options={ImportModules}
          onChange={({ target: { value } }) => {
            setFunctionalModule(value);
          }}
          value={functionalModule}
        ></Radio.Group>
      </div>
    </>
  );
};
