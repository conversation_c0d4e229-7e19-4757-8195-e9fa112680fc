import React, { useRef } from 'react';
import { Modal, ModalProps } from 'antd';

import { CatalogType } from '@/constants';

import './SourceModal.less';

import { ModalSearch } from './ModalSearch';

interface Props extends ModalProps {
  tbId?: string;
  catalog?: keyof typeof CatalogType;
  cellId?: string;
  platform: DATA_SOURCE_TYPE;
  onCallback: (id: string, item: TableDeployEntity) => void;
  selectedItem?: any;
}

export const SourceModel: React.FC<Props> = props => {
  const selected = useRef<{ value: string; record: TableDeployEntity }>();
  const { open, onCancel, tbId, platform, catalog, onCallback, cellId, selectedItem } = props;

  const onOk = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    const { value, record } = selected.current ?? {};
    if (!value) {
      onCancel?.(e);
      return;
    }
    onCallback(value, record);
  };

  const handleOnChange = (value, record) => {
    selected.current = {
      value,
      record,
    };
  };

  return (
    <Modal title='选择模型' open={open} onCancel={onCancel} width={850} className='source-modal' onOk={onOk}>
      <div className='flex flex-col h-full'>
        <div className='pt-1 pl-4 text-[12px] text-danger'>可选择已发布的，且具有数据写入权限的模型</div>
        <ModalSearch
          platform={platform}
          catalog={catalog}
          cellId={cellId}
          tbId={tbId}
          rowKey='tbId'
          selectedItem={selectedItem}
          onChange={handleOnChange}
        />
      </div>
    </Modal>
  );
};
