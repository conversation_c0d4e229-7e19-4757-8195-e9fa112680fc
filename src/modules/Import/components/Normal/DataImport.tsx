import { useState } from 'react';
import { UploadProps } from 'antd';

import { ImportKeyLabel } from '../../constant';
import { importServices } from '../../services';
import { createFormData } from '../../utils';
import { FilePreview } from '../FilePreview';
import { UploadFile } from '../UploadFile';

interface ImportProps {
  importType: IMPORT.Type;
  onUpload: (file, formData) => void;
}

const DataImport = (importProps: ImportProps) => {
  const { importType } = importProps;
  const [previewData, setPreviewData] = useState<IMPORT.ExcelExample[]>([]);

  const handleCustomUpload: UploadProps['customRequest'] = async obj => {
    const { onSuccess, onError, filename, file } = obj;
    try {
      const formData = createFormData(filename as string, file as Blob);
      const res = await importServices.getPreviewFile(importType, formData);

      if (res.code === '0000') {
        onSuccess?.(res?.data);
        setPreviewData(res.data);
        importProps?.onUpload(file, createFormData('excelFile', file as Blob));
      }
    } catch (error: any) {
      onError?.(error);
    }
  };

  return (
    <div className='px-5'>
      <UploadFile
        type='xlsx'
        importType={importType}
        alertMessage={`请上传需要导入的 ${
          ImportKeyLabel[importType]
        }${`文件（需按照最新的${ImportKeyLabel[importType]}模版整理）`} `}
        uploadProps={{
          accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          action: '/api/v2/data/modeling/excel/preview',
        }}
        onCustomUpload={handleCustomUpload}
      ></UploadFile>
      <FilePreview data={previewData} showSameFilter={true} showSetMode={true} />
    </div>
  );
};

export default DataImport;
