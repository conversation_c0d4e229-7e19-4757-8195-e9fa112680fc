import { message } from 'antd';

import { importServices } from '../../services';
import { ConfirmBtn } from '../ConfirmBtn';

interface Props {
  importType: IMPORT.Type;
  formData: FormData;
  importMode: string;
  onOverImport: (res) => void;
}

const ImportBtn = (props: Props) => {
  const { importType, formData, importMode, onOverImport } = props;

  const handleFetchUpload = async () => {
    if (!formData) {
      message.error('请上传需要导入的文件！');
      return false;
    }

    return importServices.uploadImportFile({ type: importType, mode: importMode }, formData);
  };

  return <ConfirmBtn fetchUpload={handleFetchUpload} onOverImport={onOverImport}></ConfirmBtn>;
};

export default ImportBtn;
