import { useRef, useState } from 'react';
import { UploadProps } from 'antd';

import { ImportKeyLabel } from '../../constant';
import { dataDevImportServices } from '../../services';
import { createFormData } from '../../utils';
import { FilePreview } from '../FilePreview';
import { SelectBusiness } from '../SelectBusiness';
import { UploadFile } from '../UploadFile';

interface ImportProps {
  importType: IMPORT.Type;
  onUpload: (file, formData) => void;
  businessFlowId: string;
  onChangeBusinessFlowId: (val) => void;
}

export const DataImport = (importProps: ImportProps) => {
  const { importType, businessFlowId, onChangeBusinessFlowId } = importProps;
  const [previewData, setPreviewData] = useState<IMPORT.ExcelExample[]>([]);
  const ref = useRef(null);

  const handleCustomUpload: UploadProps['customRequest'] = async obj => {
    const { onSuccess, onError, filename, file } = obj;
    try {
      const formData = createFormData(filename as string, file as Blob);
      await ref.current?.validate();
      const res = await dataDevImportServices.getPreviewFile(importType, businessFlowId, formData);

      if (res.code === '0000') {
        onSuccess?.(res?.data);
        setPreviewData(res.data);
        importProps?.onUpload(file, createFormData('file', file as Blob));
      }
    } catch (error: any) {
      onError?.(error);
    }
  };

  return (
    <div className='px-5'>
      <UploadFile
        type='json'
        isSuccess={true}
        importType={importType}
        alertMessage={`请上传需要导入的 ${ImportKeyLabel[importType]} `}
        uploadProps={{
          accept: '.json',
          action: '/api/v2/data/modeling/pipeline/preview',
        }}
        onCustomUpload={handleCustomUpload}
      >
        <SelectBusiness ref={ref} onChangeBusinessFlowId={onChangeBusinessFlowId}></SelectBusiness>
      </UploadFile>
      <FilePreview data={previewData} showSameFilter={false} showSetMode={false} />
    </div>
  );
};

export default DataImport;
