import { useEffect, useMemo, useState } from 'react';
import { Select } from 'antd';
import { useParams } from 'umi';

import { isNumberString, toMemoryUnit } from '@/utils';

import { MonitorApi } from '../services/MonitorApi';

import { ManagerBlock } from './ManagerBlock';

export const TaskManager = () => {
  const { id = '' } = useParams();
  const [tmIdList, setTmIdList] = useState<string[]>([]);
  const [tmId, setTmId] = useState<string | undefined>();
  const [data, setData] = useState<any>({});

  const fetchManagerDetail = (tmId: string) => {
    MonitorApi.getTaskManagerDetail({ id, tmId }).then(({ code, data }) => {
      if (code === '0000') {
        setData(data);
      }
    });
  };

  const jvmDatas = useMemo(() => {
    const {
      memoryHeapCommitted,
      memoryHeapMax,
      memoryHeapUsed,
      memoryNonHeapCommitted,
      memoryNonHeapMax,
      memoryNonHeapUsed,
    } = data;
    return [
      {
        type: 'Heap',
        committed: memoryHeapCommitted,
        used: memoryHeapUsed,
        maximum: memoryHeapMax,
      },
      {
        type: 'Non-Heap',
        committed: memoryNonHeapCommitted,
        used: memoryNonHeapUsed,
        maximum: memoryNonHeapMax,
      },
      {
        type: 'Total',
        committed:
          isNumberString(memoryHeapCommitted) && isNumberString(memoryNonHeapCommitted)
            ? +memoryHeapCommitted + +memoryNonHeapCommitted
            : '-',
        used:
          isNumberString(memoryHeapUsed) && isNumberString(memoryNonHeapUsed)
            ? +memoryHeapUsed + +memoryNonHeapUsed
            : '-',
        maximum:
          isNumberString(memoryHeapMax) && isNumberString(memoryNonHeapMax) ? +memoryHeapMax + +memoryNonHeapMax : '-',
      },
    ];
  }, [data]);

  const noJvmDatas = useMemo(() => {
    const {
      memoryDirectCount,
      memoryDirectTotalCapacity,
      memoryDirectUsed,
      memoryMappedCount,
      memoryMappedTotalCapacity,
      memoryMappedUsed,
    } = data;
    return [
      {
        type: 'Direct',
        count: memoryDirectCount,
        used: memoryDirectUsed,
        capacity: memoryDirectTotalCapacity,
      },
      {
        type: 'Mapped',
        count: memoryMappedCount,
        used: memoryMappedUsed,
        capacity: memoryMappedTotalCapacity,
      },
    ];
  }, [data]);

  const nmsDatas = useMemo(() => {
    const { availableMemorySegments, totalMemorySegments } = data;
    return [
      {
        type: 'Available',
        count: availableMemorySegments,
      },
      {
        type: 'Total',
        count: totalMemorySegments,
      },
    ];
  }, [data]);

  const ngcDatas = useMemo(() => {
    const { gcMarkSweepCount, gcMarkSweepTime, gcScavengeCount, gcScavengeTime } = data;
    return [
      {
        collector: 'PS_MarkSweep',
        count: gcMarkSweepCount,
        time: gcMarkSweepTime,
      },
      {
        collector: 'PS_Scavenge',
        count: gcScavengeCount,
        time: gcScavengeTime,
      },
    ];
  }, [data]);

  const G1Datas = useMemo(() => {
    const { g1YoungGenerationTime, g1YoungGenerationCount, g1OldGenerationCount, g1OldGenerationTime } = data;
    return [
      {
        collector: 'G1_Young_Generation',
        count: g1YoungGenerationCount,
        time: g1YoungGenerationTime,
      },
      {
        collector: 'G1_Old_Generation',
        count: g1OldGenerationCount,
        time: g1OldGenerationTime,
      },
    ];
  }, [data]);

  const render = (val: string) => {
    if (isNumberString(val)) {
      return toMemoryUnit(val);
    }
    return '-';
  };

  const emptyRender = (val: string) => {
    if (!isNumberString(val)) {
      return '-';
    }
    return val;
  };

  useEffect(() => {
    MonitorApi.getTmIdList({ id }).then(({ data }) => {
      setTmIdList(data.tmIdList ?? []);
      if (data.tmIdList?.length > 0) {
        setTmId(data.tmIdList[0]);
      }
    });
  }, []);

  useEffect(() => {
    if (!tmId) return;
    fetchManagerDetail(tmId);
  }, [tmId]);

  return (
    <div className='pt-3'>
      <div className='flex items-center px-4'>
        <label className='mr-2 font-medium'>选择TaskManager:</label>
        <Select
          className='w-[450px]'
          placeholder='请选择'
          value={tmId}
          options={tmIdList.map((x: string) => ({ label: x, value: x }))}
          onChange={setTmId}
        />
      </div>
      <ManagerBlock
        caption={<span className='text-primary text-title'>Memory JVM(HEAP/Non-Heap)</span>}
        columns={[
          { title: 'Type', dataIndex: 'type' },
          { title: 'Committed', dataIndex: 'committed', render },
          { title: 'Used', dataIndex: 'used', render },
          { title: 'Maximum', dataIndex: 'maximum', render },
        ]}
        rowKey='type'
        pagination={false}
        dataSource={jvmDatas}
      />
      <ManagerBlock
        caption={<span className='text-primary text-title'>Memory Outside JVM</span>}
        columns={[
          { title: 'Type', dataIndex: 'type' },
          { title: 'Count', dataIndex: 'count', render: emptyRender },
          { title: 'Used', dataIndex: 'used', render },
          { title: 'Capacity', dataIndex: 'capacity', render },
        ]}
        rowKey='type'
        pagination={false}
        dataSource={noJvmDatas}
      />
      <div className='grid lg:grid-cols-2 gap-x-4 grid-cols-1'>
        <ManagerBlock
          wrapperClassName='mb-0 pb-[18px]'
          caption={<span className='text-primary text-title'>Network Memory Segments</span>}
          columns={[
            { title: 'Type', dataIndex: 'type' },
            { title: 'Count', dataIndex: 'count', render: emptyRender },
          ]}
          rowKey='type'
          pagination={false}
          dataSource={nmsDatas}
        />
        {data?.gcType !== 'G1' ? (
          <ManagerBlock
            wrapperClassName='mb-0 pb-[18px]'
            caption={<span className='text-primary text-title'>Garbage Collection</span>}
            columns={[
              { title: 'Collector', dataIndex: 'collector' },
              { title: 'Count', dataIndex: 'count', render: emptyRender },
              { title: 'Time', dataIndex: 'time', render: emptyRender },
            ]}
            rowKey='collector'
            pagination={false}
            dataSource={ngcDatas}
          />
        ) : (
          <ManagerBlock
            wrapperClassName='mb-0 pb-[18px]'
            caption={<span className='text-primary text-title'>Garbage Collection</span>}
            columns={[
              { title: 'Collector', dataIndex: 'collector' },
              { title: 'Count', dataIndex: 'count', render: emptyRender },
              { title: 'Time', dataIndex: 'time', render: emptyRender },
            ]}
            rowKey='collector'
            pagination={false}
            dataSource={G1Datas}
          />
        )}
      </div>
    </div>
  );
};
