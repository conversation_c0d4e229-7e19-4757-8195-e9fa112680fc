import React, { useMemo } from 'react';
import { AreaOptions } from '@antv/g2plot';
import { Empty } from 'antd';

import { AreaChart } from '@/components/visual/chart/AreaChart';
import { ChartWrapper } from '@/components/visual/chart/ChartWrapper';
import { formatNumber } from '@/utils';

interface Props {
  values: any;
  range: string[];
  isRealTime: boolean;
}

export const SourceRateChart: React.FC<Props> = props => {
  const { values, range, isRealTime } = props;

  const data = useMemo(() => {
    return (
      values?.map(([x, y]) => {
        return {
          x: x * 1000,
          y: Number(y),
        };
      }) ?? []
    );
  }, [values]);

  const options: Partial<AreaOptions> = {
    isStack: false,
    seriesField: undefined,
    yAxis: {
      label: {
        formatter: val => {
          const obj = formatNumber(Number(val));
          return `${obj?.value.toFixed(2)}${obj?.symbol}条/秒`;
        },
      },
    },
    tooltip: {
      formatter: data => {
        const obj = formatNumber(Number(data.y));
        return {
          name: '写入速率',
          value: `${obj?.value.toFixed(2)}${obj?.symbol}条/秒`,
        };
      },
    },
  };

  return (
    <ChartWrapper
      displayName={
        <span className='text-gray-5'>
          <b className='text-gray-6'>Source Operator</b>数据源算子
        </span>
      }
    >
      {data.length === 0 ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : isRealTime ? (
        <AreaChart key='1' options={options} data={data} range={range} isRealTime={isRealTime} />
      ) : (
        <AreaChart key='2' options={options} data={data} range={range} />
      )}
    </ChartWrapper>
  );
};
