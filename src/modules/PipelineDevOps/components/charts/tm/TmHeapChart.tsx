import React, { useMemo } from 'react';
import { AreaOptions } from '@antv/g2plot';
import { Empty } from 'antd';

import { AreaChart } from '@/components/visual/chart/AreaChart';
import { ChartWrapper } from '@/components/visual/chart/ChartWrapper';
import { getByteConvert } from '@/utils';

const formatArrayToObj = (values: Array<[number, string]>, key: string) => {
  return values.map(([time, value]) => {
    return {
      x: time * 1000,
      category: key,
      y: Number(value),
    };
  });
};

interface Props {
  values: any;
  range: string[];
  isRealTime: boolean;
}

export const TmHeapChart: React.FC<Props> = props => {
  const { values, range, isRealTime } = props;

  const { heapMax, heapUsed, heapCommitted } = values;
  const data = useMemo(() => {
    const heapMaxObjList = formatArrayToObj(heapMax?.values ?? [], 'Max');
    const heapUsedObjList = formatArrayToObj(heapUsed?.values ?? [], 'Used');
    const heapCommittedObjList = formatArrayToObj(heapCommitted?.values ?? [], 'Committed');
    const newObjList = heapMaxObjList.concat(heapUsedObjList).concat(heapCommittedObjList);
    return newObjList;
  }, [values]);

  const options: Partial<AreaOptions> = {
    data,
    isStack: false,
    seriesField: 'category',
    yAxis: {
      label: {
        formatter: val => {
          const { bytes, symbol } = getByteConvert(val);
          return `${Number(bytes)?.toFixed(2)}${symbol}`;
        },
      },
    },
    tooltip: {
      formatter: data => {
        const { bytes, symbol } = getByteConvert(data.y);
        return {
          name: data.category,
          value: `${Number(bytes)?.toFixed(2)}${symbol}`,
        };
      },
    },
  };

  return (
    <ChartWrapper
      displayName={
        <span className='text-gray-5'>
          <b className='text-gray-6'>Heap</b>：Used Cmt Max
        </span>
      }
    >
      {data.length === 0 ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : isRealTime ? (
        <AreaChart key='1' options={options} data={data} range={range} isRealTime={isRealTime} />
      ) : (
        <AreaChart key='2' options={options} data={data} range={range} />
      )}
    </ChartWrapper>
  );
};
