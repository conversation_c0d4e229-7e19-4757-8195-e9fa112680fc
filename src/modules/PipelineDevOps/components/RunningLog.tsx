import React, { useEffect, useState } from 'react';
import { Empty } from 'antd';

import { ClusterApi } from '@/modules/Cluster/services/ClusterApi';

import { RunningLogList } from './RunningLogList';

interface Props {
  pipelineData: PipelineModel;
}
export const RunningLog: React.FC<Props> = ({ pipelineData }) => {
  const { yarnAppId: applicationId, clusterId } = pipelineData;
  const [containers, setContainers] = useState<YarnSessionLogItem[]>([]);

  useEffect(() => {
    if (!applicationId) return;
    ClusterApi.getYarnApplicationLog(clusterId, applicationId).then(({ data }) => {
      const { amContainer, containers } = data;
      setContainers([amContainer].concat(containers));
    });
  }, []);

  return (
    <>
      {!applicationId && <Empty className='pt-10' />}
      {applicationId && (
        <div className='p-4 running-log'>
          <div className='mb-2'>
            <i className='iconfont icon-hadoop-line mr-2 text-xl'></i>
            <span className='text-base text-gray-7'>
              应用：<b>{applicationId}</b>
            </span>
          </div>
          <RunningLogList pipelineData={pipelineData} containers={containers} />
        </div>
      )}
    </>
  );
};
