import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';

import './index.less';

import { PipelineStatus } from '../../constant';
import { PipelineApi } from '../../services/PipelineApi';

interface Props {
  id: string;
  pipelineStatus: keyof typeof PipelineStatus;
  noCheck?: boolean;
  onChangeStatus?: (status: keyof typeof PipelineStatus) => void;
}

export const PipelineStatusComponent: React.FC<Props> = props => {
  const { pipelineStatus, id, noCheck } = props;
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState(pipelineStatus);

  const fetchPipelineStatus = (id: string) => {
    setLoading(true);
    PipelineApi.checkStatus(id)
      .then(({ data: { pipelineStatus } }) => {
        setStatus(pipelineStatus);
        props?.onChangeStatus?.(pipelineStatus);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    setStatus(pipelineStatus);
  }, [pipelineStatus]);

  return loading ? (
    <span className='text-primary'>
      <Spin size='small' className='mr-2' />
      状态检查中
    </span>
  ) : (
    <>
      <span className={`pipeline-status ${status}`}>{PipelineStatus[status]}</span>
      {!noCheck && (
        <i
          onClick={() => {
            fetchPipelineStatus(id);
          }}
          className='iconfont icon-cheak text-primary ml-2 text-sm cursor-pointer'
        ></i>
      )}
    </>
  );
};
