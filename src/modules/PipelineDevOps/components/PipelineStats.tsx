import React, { useEffect, useMemo, useState } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Progress, Tooltip } from 'antd';

import { PipelineStatus, PipelineType } from '../constant';
import { PipelineApi } from '../services/PipelineApi';

interface Props {
  pipelineType: Array<keyof typeof PipelineType>;
  handleClick?: (value: Array<keyof typeof PipelineStatus>) => void;
}

export const PipelineStats: React.FC<Props> = ({ pipelineType, handleClick }) => {
  const [state, setState] = useState({
    total: 0,
    running: 0,
    failed: 0,
    stopped: 0,
  });

  const isFinishedVisible = useMemo(() => {
    return pipelineType.includes('batch');
  }, [pipelineType]);

  useEffect(() => {
    PipelineApi.getPipelineStats(pipelineType).then(({ data }) => {
      setState(data);
    });
  }, []);
  return (
    <div className='bg-white mb-2 flex'>
      <div
        className='w-[110px] mx-4 pt-3 cursor-pointer'
        onClick={() => {
          typeof handleClick === 'function' && handleClick(['RUNNING']);
        }}
      >
        <div className='text-sm font-medium text-gray-6'>运行中</div>
        <div className='text-[32px] text-gray-7 font-medium'>{state.running}</div>
        <Progress percent={(state.running * 100) / state.total} size='small' showInfo={false} />
      </div>
      <div
        className='w-[110px] mx-4 pt-3 cursor-pointer'
        onClick={() => {
          typeof handleClick === 'function' && handleClick(['FAILED', 'START_FAILED', 'STOP_FAILED', 'DELETE_FAILED']);
        }}
      >
        <div className='text-sm font-medium text-gray-6 flex items-center'>
          失败
          <Tooltip title='运行失败和启动失败任务数量汇总'>
            <ExclamationCircleOutlined className='text-xs ml-1' />
          </Tooltip>
        </div>
        <div className='text-[32px] text-gray-7 font-medium'>{state.failed}</div>
        <Progress percent={(state.failed * 100) / state.total} size='small' status='exception' showInfo={false} />
      </div>
      <div
        className='w-[110px] mx-4 pt-3 cursor-pointer'
        onClick={() => {
          typeof handleClick === 'function' && handleClick(['STOPPED']);
        }}
      >
        <div className='text-sm font-medium text-gray-6'>已停止</div>
        <div className='text-[32px] text-gray-7 font-medium'>{state.stopped}</div>
        <Progress
          percent={(state.stopped * 100) / state.total}
          size='small'
          showInfo={false}
          strokeColor='rgba(0, 0, 0, .45)'
        />
      </div>
      {isFinishedVisible && (
        <div className='w-[110px] mx-4 pt-3'>
          <div className='text-sm font-medium text-gray-6'>已完成</div>
          <div className='text-[32px] text-gray-7 font-medium'>{state.finished}</div>
          <Progress
            percent={(state.finished * 100) / state.total}
            size='small'
            showInfo={false}
            strokeColor='var(--color-success)'
          />
        </div>
      )}
    </div>
  );
};
