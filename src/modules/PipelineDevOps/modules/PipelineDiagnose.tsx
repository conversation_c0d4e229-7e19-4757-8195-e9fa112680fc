import React, { useEffect, useMemo, useState } from 'react';
import { Tabs } from 'antd';
import { useParams, useSearchParams } from 'umi';

import { useGlobalHook } from '@/hooks';

import './PipelineDiagnose.less';

import { JobManager } from '../components/JobManager';
import { OperationLog } from '../components/OperationLog';
import { PipelineStatusComponent } from '../components/PipelineStatusComponent';
import { RunningLog } from '../components/RunningLog';
import { TaskChain } from '../components/TaskChain';
import { TaskIndicator } from '../components/TaskIndicator';
import { TaskManager } from '../components/TaskManager';
import { PipelineApi } from '../services/PipelineApi';

interface Props {
  tabKeys: string[];
}

export const PipelineDiagnose: React.FC<Props> = ({ tabKeys }) => {
  const { id } = useParams();
  const [pipelineData, setPipelineData] = useState<PipelineModel>();
  const [searchParams] = useSearchParams();
  const [activeKey, setActiveKey] = useState(searchParams.get('active') ?? tabKeys[0] ?? 'task-chain');

  useEffect(() => {
    if (!id) return;
    PipelineApi.getDetail(id)
      .then(({ data }) => {
        setPipelineData(data);
        const { pipelineAlias, id, pipelineStatus, trackUrl } = data;
        setPageInfo({
          title: (
            <span className='items-center flex'>
              <span className='mr-5'>诊断：{pipelineAlias}</span>
              <PipelineStatusComponent
                id={id}
                pipelineStatus={pipelineStatus}
                noCheck={true}
                className='align-baseline'
              />
              {trackUrl && (
                <a className='ml-5 text-primary no-underline' href={trackUrl} target='_blank' rel='noreferrer'>
                  追踪
                </a>
              )}
            </span>
          ),
        });
      })
      .catch(() => {});
  }, [id]);

  const { setPageInfo, resetPageInfo } = useGlobalHook();
  useEffect(() => {
    return () => {
      resetPageInfo();
    };
  }, []);

  const items = useMemo(() => {
    const keys = tabKeys;
    if (pipelineData?.pipelineType === 'flink_cmd') {
      const index = keys.findIndex(item => item === 'task-chain');
      if (index !== -1) {
        keys.splice(index, 1);
        if (activeKey === 'task-chain') {
          setActiveKey(keys[0]);
        }
      }
    }
    return [
      { label: '任务链路', key: 'task-chain', children: <TaskChain /> },
      { label: '任务指标', key: 'task-indicator', children: <TaskIndicator currentTab={activeKey} /> },
      {
        label: '运行日志',
        key: 'running-log',
        children: pipelineData && <RunningLog pipelineData={pipelineData} />,
      },
      { label: '操作日志', key: 'operation-log', children: <OperationLog /> },
      { label: 'JobManager', key: 'job-manager', children: <JobManager /> },
      { label: 'TaskManager', key: 'task-manager', children: <TaskManager /> },
    ].filter(x => tabKeys.includes(x.key));
  }, [activeKey, tabKeys, pipelineData]);
  return (
    <div className='h-full bg-white online-task-diagnose'>
      <Tabs activeKey={activeKey} destroyInactiveTabPane onChange={setActiveKey} items={items} className='h-full' />
    </div>
  );
};
