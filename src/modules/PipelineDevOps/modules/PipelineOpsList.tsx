import React, { useEffect, useMemo, useState } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Checkbox, Divider, Dropdown, message, Modal, Select, Space, Spin, Tooltip } from 'antd';
import { Link, useSearchParams } from 'umi';

import { SearchInput, TagSelect } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable/';
import { useRightsHook } from '@/hooks';
import { ClusterApi } from '@/modules/Cluster/services/ClusterApi';
import { tagService } from '@/modules/DataIngestion/services/tag';
import { handleCheckResultResult } from '@/utils/handleCheckResourceResult';

import { BatchAddTagModal } from '../components/BatchAddTagModal';
import { KafkaConsumerLags } from '../components/KafkaConsumerLags';
import { PipelineStats } from '../components/PipelineStats';
import { PipelineStatusComponent } from '../components/PipelineStatusComponent';
import { StartOrStopTaskModal } from '../components/StartOrStopTaskModal';
import { PipelineStatus, PipelineType } from '../constant';
import { PipelineApi } from '../services/PipelineApi';

import { PreviewTagMatchedTask } from './PreviewMatchedTask';

type ListItemProps = PipelineModel & {
  loading: boolean;
  starting: boolean;
  stopping: boolean;
};

interface Props {
  pipelineType: Array<keyof typeof PipelineType>;
  type: 'realtime' | 'offline';
  actionKeys: string[];
}

const DiagnosisDropDown = ({ record, type, actionKeys }) => {
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const { id, yarnAppId, clusterId } = record;
  const getDropdownItems = (record: ListItemProps) => {
    const { trackUrl, id, yarnAppId, clusterId, pipelineType } = record;
    const items = [
      { key: 'task-chain', label: '任务链路' },
      { key: 'task-indicator', label: '任务指标' },
      { key: 'running-log', label: '运行日志' },
      { key: 'operation-log', label: '操作日志' },
      { key: 'historical-diagnosis', label: '历史诊断', link: `/ops/realtime/pipeline/historical-diagnosis/${id}` },
    ].filter(x => actionKeys.includes(x.key));
    if (pipelineType === 'flink_cmd') {
      const index = items.findIndex(item => item.key === 'task-chain');
      if (index !== -1) {
        items.splice(index, 1);
      }
    }

    return [
      ...items.map(({ key, label, link }) => {
        const linkUrl =
          link ||
          `/ops/${type}/pipeline/diagnose/${id}?active=${key}&applicationId=${yarnAppId}&clusterId=${clusterId}`;
        return {
          key,
          label: <Link to={linkUrl}>{label}</Link>,
        };
      }),
      {
        label: (
          <a href={trackUrl} className='mr-4' target='_blank' rel='noreferrer'>
            追踪
          </a>
        ),
        key: 'check-status',
        disabled: !trackUrl,
      },
    ];
  };
  return (
    <Dropdown
      menu={{ items: getDropdownItems(record) }}
      placement='bottom'
      open={dropdownOpen}
      onOpenChange={() => setDropdownOpen(!dropdownOpen)}
    >
      <Link
        to={`/ops/${type}/pipeline/diagnose/${id}?applicationId=${yarnAppId}&clusterId=${clusterId}`}
        className='mr-4'
      >
        诊断
        {dropdownOpen ? (
          <UpOutlined className='text-xs ml-1' onClick={e => e.preventDefault()} />
        ) : (
          <DownOutlined className='text-xs ml-1' onClick={e => e.preventDefault()} />
        )}
      </Link>
    </Dropdown>
  );
};

const statusParams = {
  RUNNING: ['RUNNING'],
  STOPPED: ['STOPPED'],
  FAILED: ['FAILED', 'START_FAILED', 'STOP_FAILED', 'DELETE_FAILED'],
  NORMAL: ['FINISHED', 'STOPPED'],
  ABNORMAL: ['FAILED', 'START_FAILED', 'STOP_FAILED', 'DELETE_FAILED'],
};

export const PipelineOpsList: React.FC<Props> = ({ pipelineType, type, actionKeys }) => {
  const { hasRights } = useRightsHook();
  const permissionCode = pipelineType.includes('batch') ? 'pipeline_batch:operate' : 'pipeline_streaming:operate';

  const [searchParams] = useSearchParams();
  const pipelineId = searchParams.get('pipelineId');
  const name = searchParams.get('name');
  const status = searchParams.get('status');
  const [clusterList, setClusterList] = useState<Array<{ text: string; value: string }>>([]);
  const [agentTags, setAgentTags] = useState<DATA_INGESTION.DataTag[]>();

  const [list, setList] = useState<ListItemProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [batchAddTag, setBatchAddTag] = useState(false);
  const [batchAction, setBatchAction] = useState<'START' | 'STOP' | undefined>();

  const {
    pagination,
    setPagination,
    queryParams,
    selectedRowKeys,
    selectedRows,
    filter,
    setFilter,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    sort: {
      updateTime: 'DESC',
    },
    filter: {
      id: pipelineId,
      search: name,
      ...status && {
        pipelineStatus: statusParams[status],
      },
    },
    // 当路由中由pipelineId时，表示搜索状态，这时不使用缓存
    cacheId: pipelineId || name || status ? undefined : `${type}TaskOpsList`,
  });

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    const res: DATA_INGESTION.DataTag[] = await tagService.queryByType({ tagType: 'TASK' });
    if (Array.isArray(res)) {
      setAgentTags(res);
    }
  };

  const specialColumns = useMemo<Array<ColumnType<ListItemProps>>>(() => {
    if (type === 'realtime') {
      return [
        {
          title: '消费积压',
          dataIndex: 'consumerLag',
          key: 'consumerLag',
          align: 'center',
          width: 120,
          ellipsis: false,
          render(consumerLag, record) {
            return <KafkaConsumerLags pipelineId={record.id} consumerLag={consumerLag} />;
          },
        },
      ];
    }
    return [];
  }, [type]);

  const columns: Array<ColumnType<ListItemProps> | { visible?: boolean }> = [
    {
      title: '任务名称',
      dataIndex: 'pipelineAlias',
      key: 'pipelineAlias',
      width: 150,
      render(pipelineAlias: string, record: ListItemProps) {
        const { processId, processType, pipelineType } = record;
        let url = '';
        switch (processType ?? pipelineType) {
        case 'ELASTICSEARCH':
          // todo 设想的路由，需要根据真实路由更改
          url = `/data-dev/dev/data-process/online/storage/es/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        case 'HIVE':
          url = `/data-dev/dev/data-process/online/hive/edit/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        case 'flink_sql':
          url = `/data-dev/dev/data-process/online/flink-sql/edit/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        case 'flink_cmd':
          url = `/data-dev/dev/data-process/online/flink-custom/edit/${processId}?title=${
            pipelineAlias ?? '未命名*'
          }`;
          break;
        case 'streaming':
          url = `/data-dev/dev/data-process/online/pipeline/edit/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        case 'batch':
          url = `/data-dev/dev/data-process/batch-process/edit/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        }

        return (
          <Tooltip title={pipelineAlias}>
            {url && (
              <Link target='_blank' to={url}>
                {pipelineAlias ?? '未命名*'}
              </Link>
            )}
            {!url && (pipelineAlias ?? '未命名*')}
          </Tooltip>
        );
      },
    },
    {
      title: '任务标识',
      dataIndex: 'pipelineName',
      key: 'pipelineName',
      visible: false,
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'pipelineStatus',
      key: 'pipelineStatus',
      width: 120,
      render(pipelineStatus: keyof typeof PipelineStatus, record: ListItemProps) {
        return (
          <PipelineStatusComponent
            id={record.id}
            pipelineStatus={pipelineStatus}
            onChangeStatus={newPipelineStatus =>
              setList(list => {
                return list.map(item => {
                  if (item.id === record.id) {
                    item.pipelineStatus = newPipelineStatus;
                  }
                  return item;
                });
              })
            }
          />
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'pipelineType',
      key: 'pipelineType',
      width: 120,
      render(pipelineType: keyof typeof PipelineType) {
        return PipelineType[pipelineType];
      },
    },
    {
      title: '标签',
      dataIndex: 'tagList',
      width: 220,
      render: (_, record) => (
        <TagSelect
          value={record.tagList}
          labelKey='name'
          valueKey='name'
          position='right'
          data={agentTags ?? []}
          onTagItemClick={value => {
            setFilter({ ...filter, tagIdList: value?.id ? [value.id] : [] });
          }}
          onChange={async (value: string[]) => {
            fetchUpdateTag(value, record);
          }}
        />
      ),
    },
    {
      title: '集群',
      dataIndex: 'clusterId',
      key: 'clusterId',
      filters: clusterList,
      filterMultiple: false,
      visible: false,
      width: 150,
      render(_, record: PipelineModel) {
        const { clusterName, yarnSessionName } = record;
        const txt = `${clusterName}${yarnSessionName ? `/${yarnSessionName}` : ''}`;
        return <Tooltip title={txt}>{txt}</Tooltip>;
      },
    },
    {
      title: '运行框架',
      dataIndex: 'optsName',
      key: 'optsName',
      width: 200,
      ellipsis: true,
    },
    ...specialColumns,
    {
      title: '最后更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 200,
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 200,
      render(record: ListItemProps) {
        const { pipelineStatus, projectAuth } = record;
        return (
          <div className='flex gap-x-2'>
            <Button
              type='link'
              size='small'
              disabled={
                !hasRights(permissionCode, projectAuth) ||
                [
                  'WAITING_START',
                  'STARTING',
                  'WAITING_STOP',
                  'STOPPING',
                  'WAITING_DELETE',
                  'DELETING',
                  'RESTARTING',
                  'RUNNING',
                ].includes(pipelineStatus)
              }
              onClick={() => startPipeline([record.id])}
            >
              {record.starting && <Spin size='small' className='mr-2' />}
              启动
            </Button>
            <Button
              type='link'
              size='small'
              disabled={
                !hasRights(permissionCode, projectAuth) ||
                [
                  'WAITING_START',
                  'STARTING',
                  'WAITING_STOP',
                  'STOPPING',
                  'WAITING_DELETE',
                  'DELETING',
                  'RESTARTING',
                  'STOPPED',
                  'FINISHED',
                  'START_FAILED',
                ].includes(pipelineStatus)
              }
              onClick={() => stopPipeline([record.id])}
            >
              {record.stopping && <Spin size='small' className='mr-2' />}
              停止
            </Button>
            <DiagnosisDropDown record={record} type={type} actionKeys={actionKeys}></DiagnosisDropDown>
          </div>
        );
      },
    },
  ];

  const handleBatchActions = ({ key }: {key: 'start' | 'stop' | 'addTag'}) => {
    switch (key) {
    case 'addTag':
      setBatchAddTag(true);
      break;
    case 'start':
      setBatchAction('START');
      break;
    case 'stop': {
      setBatchAction('STOP');
      break;
    }
    }
  };

  const gotoTask = () => {
    window.open('/data-quality/realtime/task/list', '_blank');
  };

  const fetchUpdateTag = async (values: string[], record: PipelineModel) => {
    try {
      setLoading(true);
      const res = await PipelineApi.updateTag(record.id, values);
      if (res.code === '0000') {
        message.success(res?.data?.message ?? res?.msg ?? '更新成功');
        setList(() => {
          return list.map(item => {
            if (item.id === record.id) {
              item.tagList = values;
            }
            return item;
          });
        });
        if (res.data.matchedCheckTaskList?.length) {
          Modal.confirm({
            title: <div>标签关联的检测任务发生变更，可在<Button
              type='link' className='text-[16px] px-0' size='small'
              onClick={gotoTask}
            >检测任务</Button>中查看</div>,
            content: <div className='overflow-hidden mt-4 flex flex-col' style={{ maxHeight: '400px' }}>
              <div className='flex-col h-full flex overflow-hidden'>
                <PreviewTagMatchedTask list={res.data.matchedCheckTaskList} />
              </div>
            </div>,
            cancelText: '',
            okText: '',
            maskClosable: true,
            width: 780,
            footer: null,
            closable: true,
          });
        }
      }
      // TODU 更新标签下拉数据
      init();
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const startPipeline = (selectedRowKeys: string[]) => {
    Modal.confirm({
      title: '是否确认启动？',
      onOk: () => {
        handleStart(selectedRowKeys);
      },
    });
  };

  const stopPipeline = (selectedRowKeys: string[]) => {
    let isForceStop = false;
    Modal.confirm({
      title: '是否确认停止？',
      content: (
        <div>
          <Checkbox
            onChange={e => {
              isForceStop = e.target.checked;
            }}
          >
            是否强制停止？
          </Checkbox>
        </div>
      ),
      onOk: () => {
        handleStop(selectedRowKeys, isForceStop);
      },
    });
  };

  // 批量启动任务
  const handleBatchStart = async (selectedRowKeys: string[], forceStart: boolean = false) => {
    setList(list => {
      return list.map(item => {
        if (selectedRowKeys.includes(item.id)) {
          item.starting = true;
        }
        return item;
      });
    });

    const res = await PipelineApi.batchStart(selectedRowKeys, forceStart)
      .catch(result => {
        handleCheckResultResult(result, () => handleBatchStart(selectedRowKeys, true));
      })
      .finally(() => {
        setList(list => {
          return list.map(item => {
            if (selectedRowKeys.includes(item.id)) {
              item.starting = false;
            }
            return item;
          });
        });
      });
    if (res?.code === '0000') {
      const successNum = res.data.length;
      const failNum = selectedRowKeys.length - successNum;
      const msg = failNum > 0 ? message.warning : message.success;
      msg(
        `操作成功，成功${successNum}个，失败${failNum}个，请稍候查看结果`,
      );
      fetchData();
    }
  };

  // 启动任务
  const handleStart = async (selectedRowKeys: string[], forceStart: boolean = false) => {
    setList(list => {
      return list.map(item => {
        if (selectedRowKeys.includes(item.id)) {
          item.starting = true;
        }
        return item;
      });
    });

    const results = await Promise.allSettled(selectedRowKeys.map(x =>PipelineApi.start(x, forceStart)))
      .finally(() => {
        setList(list => {
          return list.map(item => {
            if (selectedRowKeys.includes(item.id)) {
              item.starting = false;
            }
            return item;
          });
        });
      });
    if (results.length === 1) {
      if (results[0].status === 'fulfilled' && results[0].value) {
        message.success('操作成功，请稍候查看结果');
      } else {
        handleCheckResultResult(results[0].reason, () => handleStart(selectedRowKeys, true));
      }
    } else {
      const msg = results.some(x => x.status === 'rejected') ? message.warning : message.success;
      msg(
        `操作成功，成功${results.filter(x => x.status === 'fulfilled').length}个，失败${
          results.filter(x => x.status === 'rejected').length
        }个，请稍候查看结果`,
      );
    }
    fetchData();
  };

  // 停止任务
  const handleStop = async (selectedRowKeys: string[], isForceStop = false) => {
    const body = {
      forceStop: isForceStop,
    };

    setList(list => {
      return list.map(item => {
        if (selectedRowKeys.includes(item.id)) {
          item.stopping = true;
        }
        return item;
      });
    });

    const results = await Promise.allSettled(selectedRowKeys.map(x => PipelineApi.stop(x, body))).finally(() => {
      setList(list => {
        return list.map(item => {
          if (selectedRowKeys.includes(item.id)) {
            item.stopping = false;
          }
          return item;
        });
      });
    });
    if (results.length === 1) {
      if (results[0].status === 'fulfilled') {
        message.success('操作成功，请稍候查看结果');
      } else {
        message.error(results[0].reason.msg);
      }
    } else {
      const msg = results.some(x => x.status === 'rejected') ? message.warning : message.success;
      msg(
        `操作成功，成功${results.filter(x => x.status === 'fulfilled').length}个，
        失败${results.filter(x => x.status === 'rejected').length}个，请稍候查看结果`,
      );
    }
    fetchData();
  };

  const fetchData = () => {
    setLoading(true);
    const { filter } = queryParams;
    const newFilter = {
      ...filter,
    };
    if (filter?.clusterId && filter?.clusterId.length > 0) {
      newFilter.clusterId = filter.clusterId[0];
    }
    PipelineApi.getList({
      ...queryParams,
      filter: {
        ...newFilter,
        pipelineType: newFilter.pipelineType ? newFilter.pipelineType : pipelineType,
      },
      fetchConsumerLag: true,
    })
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    ClusterApi.getAll().then(({ data }: { data: ClusterEntity[] }) => {
      setClusterList(data.map(({ id: value, clusterName: text }) => ({ text, value })));
    });
  }, []);

  const getDataRights = () => {
    if (selectedRows.some(item => !hasRights(permissionCode, item.projectAuth))) {
      return true;
    }
    return false;
  };

  return (
    <div className='flex flex-col h-full'>
      <PipelineStats
        pipelineType={pipelineType}
        handleClick={value => {
          setFilter({ ...filter, pipelineStatus: value });
        }}
      />
      <div className='bg-white flex-1 flex flex-col pt-3 overflow-hidden'>
        <div className='px-4 mb-3 flex h-8 items-center justify-between'>
          <Space className='flex items-center'>
            <Dropdown
              menu={{
                items: [
                  {
                    label: '启动',
                    key: 'start',
                    disabled: selectedRowKeys.length === 0 || getDataRights(),
                  },
                  {
                    label: '停止',
                    key: 'stop',
                    disabled: selectedRowKeys.length === 0 || getDataRights(),
                  },
                  {
                    label: '添加标签',
                    key: 'addTag',
                    disabled: selectedRowKeys.length === 0,
                  },
                ],
                onClick: handleBatchActions,
              }}
            >
              <Button>
                批量操作 <DownOutlined />
              </Button>
            </Dropdown>
            <SearchInput
              placeholder='请输入关键字搜索'
              defaultValue={filter.search}
              onSearch={val => {
                setFilter({ ...filter, search: val });
              }}
            />
            <Select
              allowClear
              mode='multiple'
              optionFilterProp='children'
              value={filter.pipelineStatus ?? []}
              maxTagCount={3}
              placeholder='请选择状态'
              className='min-w-[250px] shrink-0 tag-select-show-status'
              onChange={value => {
                setFilter({ ...filter, pipelineStatus: value });
              }}
            >
              {Object.entries(PipelineStatus).map(([value, text]) => (
                <>
                  <Select.Option key={value} value={value}>
                    <span className={`pipeline-status ${value} align-middle`}>{text}</span>
                  </Select.Option>
                  {(value === 'STOPPED' || value === 'WAITING_START') && (
                    <Select.Option disabled className='select-option-divider' key={`${value}-line`}>
                      <Divider type='horizontal' className='my-0 align-middle inline-block' />
                    </Select.Option>
                  )}
                </>
              ))}
            </Select>
            {type === 'realtime' && (
              <>
                <Select
                  allowClear
                  placeholder='请选择类型'
                  optionFilterProp='children'
                  mode='multiple'
                  value={filter.pipelineType ? filter.pipelineType : pipelineType}
                  className='min-w-[250px] shrink-0'
                  onChange={value => {
                    setFilter({ ...filter, pipelineType: value });
                  }}
                >
                  {Object.entries(PipelineType)
                    .filter(([key]) => pipelineType.includes(key as keyof typeof PipelineType))
                    .map(([value, text]) => (
                      <Select.Option key={value} value={value}>
                        {text}
                      </Select.Option>
                    ))}
                </Select>
              </>
            )}
            <Select
              allowClear
              mode='multiple'
              value={filter.tagIdList ?? []}
              optionFilterProp='children'
              placeholder='请选择标签'
              maxTagCount={3}
              className='min-w-[250px] shrink-0'
              onChange={value => {
                setFilter({ ...filter, tagIdList: value });
              }}
            >
              {agentTags?.map(item => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Space>
          <div className='flex items-center'>
            <Button
              type='text'
              onClick={() => {
                fetchData();
              }}
            >
              <i className='iconfont icon-refresh-line'></i>
            </Button>
          </div>
        </div>
        <CustomTable
          dataSource={list}
          cacheId={`${type}PipelineOpsList`}
          resizable
          columns={columns}
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
          onRowSelectionChange={onRowSelectionChange}
          scroll={{ x: 1200 }}
          className={'flex-1 overflow-hidden'}
          rowKey='id'
        ></CustomTable>
      </div>

      {batchAddTag && (
        <BatchAddTagModal
          selectedRowKeys={selectedRowKeys}
          open={batchAddTag}
          onCancel={() => {
            setBatchAddTag(false);
          }}
          data={agentTags ?? []}
          fetchData={fetchData}
        />
      )}

      {
        batchAction && <StartOrStopTaskModal
          open={!!batchAction}
          action={batchAction}
          pipelineList={list.filter(pipeline => selectedRowKeys.includes(pipeline.id))}
          onCancel={() => {
            setBatchAction(undefined);
          }}
          onOK={(action, ids) => {
            if (action === 'START') {
              handleBatchStart(ids);
            } else {
              stopPipeline(ids);
            }
          } }
        >

        </StartOrStopTaskModal>
      }
    </div>
  );
};
