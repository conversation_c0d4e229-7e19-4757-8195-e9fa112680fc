import Request from '@/request';

export interface GetJobManager {
  gcMarkSweepCount: string;
  gcMarkSweepTime: string;
  gcScavengeCount: string;
  gcScavengeTime: string;
  historyData: any;
  memoryDirectCount: string;
  memoryDirectTotalCapacity: string;
  memoryDirectUsed: string;
  memoryHeapCommitted: string;
  memoryHeapMax: string;
  memoryHeapUsed: string;
  memoryMappedCount: string;
  memoryMappedTotalCapacity: string;
  memoryMappedUsed: string;
  memoryNonHeapCommitted: string;
  memoryNonHeapMax: string;
  memoryNonHeapUsed: string;
  numberOfFailedCheckpoints: string;
  totalNumberOfCheckpoints: string;
  gcType: string;
  g1YoungGenerationCount: string;
  g1YoungGenerationTime: string;
  g1OldGenerationCount: string;
  g1OldGenerationTime: string;
}

export interface GetTaskManagerDetail {
  availableMemorySegments: string;
  gcMarkSweepCount: string | null;
  gcMarkSweepTime: string | null;
  gcScavengeCount: string | null;
  gcScavengeTime: string | null;
  historyData: any;
  memoryDirectCount: string;
  memoryDirectTotalCapacity: string;
  memoryDirectUsed: string;
  memoryHeapCommitted: string;
  memoryHeapMax: string;
  memoryHeapUsed: string;
  memoryMappedCount: string;
  memoryMappedTotalCapacity: string;
  memoryMappedUsed: string;
  memoryNonHeapCommitted: string;
  memoryNonHeapMax: string;
  memoryNonHeapUsed: string;
  totalMemorySegments: string;
}

export interface GetTmIdList {
  tmIdList: string[];
}

const url = '/api/v2/monitor';
export const MonitorApi = {
  /**
   * 获取taskManager监控详情
   * @param id
   * @returns
   */
  get getTmIdListUrl() {
    return `${url}/tmIdList`;
  },
  async getTmIdList(data: { id: string; tmId?: string; offline?: boolean }) {
    return await Request.post<Response<GetTmIdList>>(this.getTmIdListUrl, { data });
  },
  /**
   * 获取taskManager监控详情
   * @param id
   * @returns
   */
  get getTaskManagerDetailUrl() {
    return `${url}/task_manager`;
  },
  async getTaskManagerDetail(data: { id: string; tmId: string; offline?: boolean }) {
    return await Request.post<Response<GetTaskManagerDetail>>(this.getTaskManagerDetailUrl, { data });
  },
  get getJobManagerUrl() {
    return `${url}/job_manager`;
  },
  /**
   * 获取jobManager监控详情
   * @param id
   * @returns
   */
  async getJobManager(data: { id: string; offline?: boolean }) {
    const res = await Request.post<Response<GetJobManager>>(this.getJobManagerUrl, { data });
    return res.data;
  },
};
