import Request from '@/request';

import { PipelineType } from '../constant';

const url = '/api/v2/ingestion/pipeline';
export const PipelineApi = {
  /**
   * 启动作业
   * @param pipelineId
   * @returns
   */
  async start(pipelineId: string, forceStart: boolean) {
    return await Request.put(`${url}/${pipelineId}/start?forceStart=${forceStart}`);
  },
  /**
   * 停止作业
   * @param pipelineId
   * @returns
   */
  async stop(pipelineId: string, body: { forceStop: boolean }) {
    return await Request.put(`${url}/${pipelineId}/stop`, { params: body });
  },
  /**
   * 批量启动作业
   */
  async batchStart(ids: string[], forceStart: boolean) {
    return await Request.post(`${url}/batch-start?forceStart=${forceStart}`, {
      data: ids,
    });
  },
  /**
   * 管线作业列表分页查询
   * @param data
   * @param fetchConsumerLag 是否查询消费积压
   * @returns
   */
  async getList(data: QueryParams & { fetchConsumerLag?: boolean }) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 获取管线作业详情
   * @param pipelineId
   * @returns
   */
  async getDetail(pipelineId: string) {
    return await Request.get(`${url}/${pipelineId}`);
  },
  /**
   * 获取管线作业状态
   * @param pipelineId
   * @returns
   */
  async getLog(pipelineId: string) {
    return await Request.get(`${url}/${pipelineId}/log`);
  },
  /**
   * 刷新管线作业状态
   * @param pipelineId
   * @returns
   */
  async checkStatus(pipelineId: string) {
    return await Request.put(`${url}/${pipelineId}/status`);
  },
  /**
   * 刷新kafka消费积压数量
   * @param pipelineId
   * @returns
   */
  async fetchKafkaLags(pipelineId: string) {
    return await Request.get(`${url}/${pipelineId}/kafka-lags`);
  },
  /**
   * 获取管线作业日志详情
   * @param pipelineId
   * @returns
   */
  async getConsole(pipelineId: string, params: { opId: string; pageIndex: number; pageSize: number }) {
    return await Request.get(`${url}/${pipelineId}/console`, { params });
  },
  /**
   * 获取全部管线作业
   * @param pipelineId
   * @returns
   */
  async getAll() {
    return await Request.get(`${url}/list`);
  },
  /**
   * 获取管线作业统计信息
   * @param pipelineId
   * @returns
   */
  async getPipelineStats(pipelineType: Array<keyof typeof PipelineType>) {
    return await Request.post(`${url}/stats`, { data: { pipelineType } });
  },
  /**
   * 任务标签
   * @param
   * @returns
   */
  async updateTag(pipelineId, tags) {
    return await Request.post(`${url}/update-tag`, {
      data: {
        recodeId: pipelineId,
        tags,
      },
    });
  },
  /**
   * 批量添加标签
   * @param pipelineId
   * @returns
   */
  async batchAddTag(data: { recodeIds: string[]; tags: string[] }) {
    return await Request.post(`${url}/batch-add-tag`, {
      data,
    });
  },
};
