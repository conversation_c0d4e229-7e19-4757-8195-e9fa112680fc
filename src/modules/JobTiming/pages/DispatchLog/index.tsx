import { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Col, DatePicker, Modal, Row, Select, Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { Link, useSearchParams } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { useGlobalHook } from '@/hooks';
import { DispatchLogStatus } from '@/modules/JobTiming/models';
import { useDispatchTaskStore } from '@/modules/JobTiming/stores/useDispatchTaskStore';

import './index.less';

import { StatusComponent } from '../../components/StatusComponent';

const DispatchTask = () => {
  const [searchParams] = useSearchParams();
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const debouncedRef = useRef<any>();
  const { fetchData, total, dataSource, loading, fetchDeleteLog } = useDispatchTaskStore();
  const name = searchParams.get('name');
  const status = searchParams.get('status');
  const hour = searchParams.get('hour');
  const start = hour ? Number(hour) * 3600000 : 3600000
  const { queryParams, pagination, handleTableChange, setTotal, setFilter } = useCustomTableHook({
    cacheId: name || status ? undefined : 'DispatchTaskList', // 搜索状态，这时不使用缓存
    filter: {
      triggerTimeStart: (new Date().getTime() - start).toString(),
      triggerTimeEnd: new Date().getTime().toString(),
      jobNameDesc: name,
      ...status && {
        logStatus: Number(status),
      },
    },
  });

  const jobId = searchParams.get('jobId') ?? '';

  const columns: ColumnsType<Record<string, any>> = [
    {
      title: '作业名称',
      width: 200,
      dataIndex: 'jobName',
    },
    {
      title: '执行器',
      width: 200,
      dataIndex: 'executorName',
    },
    {
      title: '调度结果',
      width: 150,
      dataIndex: 'triggerCode',
      render: (text, record) => {
        let state: keyof typeof DispatchLogStatus = 'FAILED';
        if (text == 200) state = 'SUCCESS';
        if (text == 0) state = 'RUNNING';
        return (
          <StatusComponent enumTaskStatus={DispatchLogStatus} pipelineStatus={state} noCheck={true} id={record.id} />
        );
      },
    },
    {
      title: '执行结果',
      width: 150,
      dataIndex: 'handleCode',
      render: (text, record) => {
        let state: keyof typeof DispatchLogStatus = 'FAILED';
        if (text == 200) state = 'SUCCESS';
        if (text == 0) state = 'RUNNING';
        return (
          <StatusComponent enumTaskStatus={DispatchLogStatus} pipelineStatus={state} noCheck={true} id={record.id} />
        );
      },
    },
    {
      title: '调度时间',
      width: 200,
      dataIndex: 'triggerTimeMs',
      render: text => <span>{text ? formatDateTime(new Date(text)) : ''}</span>,
    },
    {
      title: '执行时间',
      width: 200,
      dataIndex: 'handleTime',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space>
          <Link to={`/job-timing/dispatch-log/detail/${record.id}`}>调度明细</Link>
          <Button
            type='link'
            className='px-0'
            onClick={() => {
              Modal.confirm({
                title: '删除确认',
                content: `确认删除日志「${record.jobName}」?`,
                async onOk() {
                  fetchDeleteLog(record.id, () => fetchData(queryParams, jobId));
                },
              });
            }}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const changeFilter = (key: string, value: string) => {
    const temp = queryParams.filter;
    if (temp) {
      temp[key] = value;
      setFilter(temp);
    }
  };

  useEffect(() => {
    setTotal(total);
  }, [total]);

  useEffect(() => {
    setPageInfo({
      title: '调度日志',
      description: !jobId,
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    if (debouncedRef.current) clearTimeout(debouncedRef.current);
    debouncedRef.current = setTimeout(() => {
      fetchData(queryParams, jobId);
    }, 300);
  }, [queryParams]);

  const formatDateTime = date => {
    const y = date.getFullYear();
    let m: number | string = parseInt(date.getMonth()) + 1;
    m = m < 10 ? `0${m}` : m;
    let d = date.getDate();
    d = d < 10 ? `0${d}` : d;
    let h = date.getHours();
    h = h < 10 ? `0${h}` : h;
    let minute = date.getMinutes();
    minute = minute < 10 ? `0${minute}` : minute;
    let second = date.getSeconds();
    second = second < 10 ? `0${second}` : second;
    return `${y}-${m}-${d} ${h}:${minute}:${second}`;
  };

  return (
    <div className='dispatch-task'>
      <div className='flex justify-between items-center p-3'>
        <Row gutter={[8, 8]}>
          <Col>
            <SearchInput
              defaultValue={queryParams?.filter?.jobNameDesc}
              placeholder='搜索作业名称'
              className='w-[224px]'
              onSearch={value => changeFilter('jobNameDesc', value)}
              allowClear
            />
          </Col>
          <Col>
            <span className='title'>日志状态：</span>
            <Select
              className='w-[160px]'
              placeholder='请选择'
              options={[
                { label: '成功', value: 1 },
                { label: '失败', value: 2 },
                { label: '运行中', value: 3 },
              ]}
              value={queryParams?.filter?.logStatus}
              allowClear
              onChange={value => changeFilter('logStatus', value)}
            />
          </Col>

          <Col>
            <span className='title'>调度时间：</span>
            <DatePicker.RangePicker
              showTime
              format='YYYY-MM-DD HH:mm:ss'
              value={[
                queryParams?.filter?.triggerTimeStart
                  ? dayjs(new Date(parseInt(queryParams?.filter?.triggerTimeStart)))
                  : null,
                queryParams?.filter?.triggerTimeEnd
                  ? dayjs(new Date(parseInt(queryParams?.filter?.triggerTimeEnd)))
                  : null,
              ]}
              onChange={(_, dateStrings) => {
                changeFilter('triggerTimeStart', dateStrings[0] ? new Date(dateStrings[0]).getTime().toString() : '');
                changeFilter('triggerTimeEnd', dateStrings[1] ? new Date(dateStrings[1]).getTime().toString() : '');
              }}
            />
          </Col>
        </Row>
        <div>
          <Button type='link' onClick={() => setFilter({})} className='px-0'>
            重置
          </Button>
          <Button type='text' onClick={() => fetchData(queryParams, jobId)}>
            <i className='iconfont icon-refresh-line'></i>
          </Button>
        </div>
      </div>

      <div className='flex-1 overflow-hidden'>
        <CustomTable
          dataSource={dataSource}
          loading={loading}
          columns={columns}
          onChange={data => {
            handleTableChange(data);
          }}
          pagination={pagination}
          rowKey='id'
          scroll={true}
        />
      </div>
    </div>
  );
};

export default DispatchTask;
