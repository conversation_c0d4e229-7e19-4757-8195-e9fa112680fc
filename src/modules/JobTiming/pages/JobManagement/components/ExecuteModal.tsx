import { useEffect, useState } from 'react';
import { Form, Input, Modal } from 'antd';

import { JobTimingDataSource, TriggerJobTiming } from '@/modules/JobTiming/models';
import { useJobListStore } from '@/modules/JobTiming/stores/useJobListStore';
import { JobTimingApi } from '@/services';

interface Props {
  record: JobTimingDataSource;
  open: boolean;
  onCancel: () => void;
}

const formItemLayout = {
  labelCol: { style: { width: '155px' } },
  wrapperCol: { span: 20 },
};

const ExecuteModal = (props: Props) => {
  const { open, record, onCancel } = props;
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const { fetchTriggerJobTiming } = useJobListStore();

  const handleOk = async () => {
    try {
      const values: TriggerJobTiming = await form.validateFields();
      setConfirmLoading(true);
      await fetchTriggerJobTiming(record.id, values, () => onCancel());
      setConfirmLoading(false);
    } catch (error) {
      setConfirmLoading(false);
    }
  };

  const init = async () => {
    const res = await JobTimingApi.getGroupDetail(record.jobGroup.toString());
    if (res?.code === '0000') {
      form.setFieldValue('addressList', res?.data?.addressList ?? '');
    }
  };

  useEffect(() => {
    form.setFieldValue('executorParam', record.executorParam);
    init();
  }, []);

  return (
    <>
      {open && (
        <Modal
          title='执行一次'
          open={open}
          confirmLoading={confirmLoading}
          onOk={handleOk}
          width={760}
          onCancel={onCancel}
          styles={{ body: { padding: '0 0 54px' } }}
        >
          <Form form={form} {...formItemLayout} className='mt-7'>
            <Form.Item label='执行器地址' name='addressList' rules={[{ required: true }]}>
              <Input className='w-[500px]' allowClear placeholder='请输入' />
            </Form.Item>
            <Form.Item label='作业参数' name='executorParam'>
              <Input.TextArea className='w-[500px] min-h-[120px]' allowClear placeholder='请输入' />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
};

export default ExecuteModal;
