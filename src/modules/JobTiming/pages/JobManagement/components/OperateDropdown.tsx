import { useState } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps } from 'antd';
import { Link } from 'umi';

import { useRightsHook } from '@/hooks';
import { JobTimingDataSource } from '@/modules/JobTiming/models';

import ExecuteModal from './ExecuteModal';
import RegisterModal from './RegisterModal';

interface Props {
  record: JobTimingDataSource;
}

export const OperateDropdown = (props: Props) => {
  const { record } = props;

  const { hasRights } = useRightsHook();

  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [executeModalOpen, setExecuteModalOpen] = useState<boolean>(false);
  const [registerModalOpen, setRegisterModalOpen] = useState<boolean>(false);

  const items: MenuProps['items'] = [
    {
      key: 'REGISTER',
      label: (
        <div
          onClick={() => {
            setRegisterModalOpen(true);
          }}
        >
          注册节点
        </div>
      ),
    },
    {
      key: 'EXECUTE',
      disabled: !hasRights('xxl_job:operate', record?.projectAuth),
      label: (
        <div
          onClick={() => {
            if (!hasRights('xxl_job:operate', record?.projectAuth)) return;
            setExecuteModalOpen(true);
          }}
        >
          执行一次
        </div>
      ),
    },
    {
      key: 'track',
      label: <Link to={`/job-timing/dispatch-log?jobId=${record.id}`}>调度日志</Link>,
    },
  ];
  return (
    <div>
      <Dropdown menu={{ items }} open={dropdownOpen} onOpenChange={() => setDropdownOpen(!dropdownOpen)}>
        <Button type='link' style={{ padding: 0 }}>
          <span>更多操作</span>
          {dropdownOpen ? <UpOutlined style={{ fontSize: 12 }} /> : <DownOutlined style={{ fontSize: 12 }} />}
        </Button>
      </Dropdown>
      {executeModalOpen && (
        <ExecuteModal
          open={executeModalOpen}
          record={record}
          onCancel={() => {
            setExecuteModalOpen(false);
          }}
        />
      )}
      {registerModalOpen && (
        <RegisterModal
          open={registerModalOpen}
          record={record}
          onCancel={() => {
            setRegisterModalOpen(false);
          }}
        />
      )}
    </div>
  );
};
