import { useEffect, useState } from 'react';
import { Button, message, Modal } from 'antd';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { useRightsHook } from '@/hooks';

import { AddRelationUserModal, Empty } from '../../components';
import { RoleItemModel } from '../../models/Role';
import { UserItemModel } from '../../models/User';
import { UserApi } from '../../services/UserApi';
import { UserGroupApi } from '../../services/UserGroupApi';

interface Props {
  id?: string;
}

export const RelationUser = ({ id }: Props) => {
  const [loading, setLoading] = useState(false);
  const [userList, setUserList] = useState<UserItemModel[]>([]);
  const [open, setOpen] = useState(false);
  const { hasRoleRights } = useRightsHook();
  const hasRights = hasRoleRights('user_group:write');

  const {
    filter,
    pagination,
    queryParams,
    selectedRowKeys,
    setSelectedRowKeys,
    setPagination,
    setFilter,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    pageSize: 10,
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    cacheId: 'userGroupRelationUser',
  });

  const columns: Array<ColumnType<UserItemModel>> = [
    {
      title: '用户名',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render(roles) {
        return roles?.map((x: RoleItemModel) => x.name)?.join(',');
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '手机',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render(_, record) {
        return (
          <Button size='small' type='link' disabled={!hasRights} className='mr-4' onClick={() => handleDelete(record)}>
            移除用户
          </Button>
        );
      },
    },
  ];

  const handleDelete = (record: UserItemModel) => {
    Modal.confirm({
      title: `确定删除用户「${record.name}」吗`,
      async onOk() {
        try {
          await UserGroupApi.deleteUsers({ id: id!, userIds: [record.id] });
          message.success('删除成功！');
          fetchUserList();
        } catch (e: any) {
          message.error(e.msg);
        }
      },
    });
  };

  const batchDelete = async (userIds: string[]) => {
    Modal.confirm({
      title: '批量删除',
      closable: true,
      width: 464,
      icon: null,
      className: 'delBatchModal',
      content: (
        <p className='font-medium mb-[5px] text-gray-12 text-sm'>
          确定删除勾选对象（共计<span>{userIds.length}</span>个）吗？
        </p>
      ),
      async onOk() {
        try {
          await UserGroupApi.deleteUsers({ id: id!, userIds });
          message.success('删除成功！');
          setSelectedRowKeys([]);
          fetchUserList();
        } catch (e: any) {
          message.error(e.msg);
        }
      },
    });
  };

  const handleCreate = async (ids: string[]) => {
    try {
      await UserGroupApi.addUsers({ id: id!, userIds: ids });
      message.success('添加成功');
      setOpen(false);
      fetchUserList();
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  const fetchUserList = async () => {
    if (id) {
      try {
        setLoading(true);
        const { data, total } = await UserApi.query({
          ...queryParams,
          filter: {
            ...filter,
            groupId: id,
          },
        });
        setUserList(data);
        setPagination({
          ...pagination,
          total,
        });
        setLoading(false);
      } catch (e: any) {
        message.error(e.msg);
      }
    } else {
      setUserList([]);
    }
  };

  useEffect(() => {
    fetchUserList();
  }, [queryParams, id]);

  return (
    <>
      {!id ? (
        <div className='w-full h-full flex justify-center items-center'>
          <Empty />
        </div>
      ) : (
        <div className='h-full w-full flex flex-col'>
          <div className='px-3 py-2 flex justify-between'>
            <SearchInput
              className='w-[240px]'
              placeholder='搜索用户名或姓名'
              defaultValue={filter.search}
              onSearch={val => setFilter({ ...filter, search: val })}
            />
            <div>
              <Button className='mr-2' type='primary' onClick={() => setOpen(true)} disabled={!hasRights}>
                添加用户
              </Button>
              <Button disabled={!selectedRowKeys.length || !hasRights} onClick={() => batchDelete(selectedRowKeys)}>
                批量移除
              </Button>
            </div>
          </div>
          <CustomTable
            className='flex-1'
            scroll={{ x: '100%' }}
            rowKey='id'
            loading={loading}
            columns={columns}
            dataSource={userList}
            pagination={pagination}
            onChange={handleTableChange}
            onRowSelectionChange={hasRights ? onRowSelectionChange : undefined}
          />
        </div>
      )}
      {open && (
        <AddRelationUserModal
          open={open}
          id={id!}
          onCancel={() => {
            setOpen(false);
          }}
          filterKey='neGroupId'
          handleOk={handleCreate}
        />
      )}
    </>
  );
};
