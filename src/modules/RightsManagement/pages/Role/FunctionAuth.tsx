import { useEffect, useMemo, useState } from 'react';
import { Button, Empty, message, Tree } from 'antd';

import { useRightsHook } from '@/hooks';
import { PermissionApi } from '@/services';
import { filter, listToTree } from '@/utils/treeHelper';

import { AddAuthModal } from '../../components';
import { AuthTreeNodeModel } from '../../models/Role';
import { RoleApi } from '../../services/RoleApi';

interface Props {
  id?: string;
}

export const FunctionAuth = ({ id }: Props) => {
  const [permissionCodes, setPermissionCodes] = useState<string[]>([]);
  const [list, setList] = useState<AuthTreeNodeModel[]>([]);
  const [open, setOpen] = useState(false);
  const { hasRoleRights } = useRightsHook();

  const treeData = useMemo(() => {
    const data: AuthTreeNodeModel[] = list.map(item => {
      return {
        ...item,
        id: item.code,
      };
    });
    return listToTree(data, {
      id: 'code',
      pid: 'parentCode',
    });
  }, [list]);

  const formatTreeData = useMemo(() => {
    return filter(
      treeData,
      node => {
        return permissionCodes.includes(node.code);
      },
      {
        id: 'code',
        pid: 'parentCode',
      },
    );
  }, [treeData, permissionCodes]);

  const fetchTreeData = async () => {
    try {
      const { data } = await PermissionApi.getCodeTreePermission();
      setList(data);
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  const getPermissions = async () => {
    if (!id) return;
    try {
      const { data } = await RoleApi.getPermissions(id);
      setPermissionCodes(data?.map(x => x.permissionCode));
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    getPermissions();
  }, [id]);

  useEffect(() => {
    fetchTreeData();
  }, []);

  return (
    <div className='w-full h-full px-3 py-2'>
      <div className='border h-full bordered border-gray-3 flex flex-col'>
        <p className='bg-gray-1 px-3 py-2 flex justify-between items-center'>
          <span>功能范围</span>
          <Button disabled={!hasRoleRights('role:write')} onClick={() => setOpen(true)}>
            编辑权限
          </Button>
        </p>
        {!formatTreeData.length ? (
          <div className='w-full h-full flex justify-center items-center'>
            <Empty />
          </div>
        ) : (
          <div className='flex-1 px-3 py-2 overflow-auto'>
            <Tree
              className='custom-directory-tree'
              showLine
              blockNode
              defaultExpandAll
              showIcon={false}
              treeData={formatTreeData}
              fieldNames={{ title: 'name', key: 'id' }}
              titleRender={node => <p>{node.codeName}-{ node.code}</p>}
            />
          </div>
        )}
      </div>
      {open && (
        <AddAuthModal
          open={open}
          treeData={treeData}
          id={id!}
          permissionCodes={permissionCodes}
          onCancel={() => setOpen(false)}
          callback={getPermissions}
        />
      )}
    </div>
  );
};
