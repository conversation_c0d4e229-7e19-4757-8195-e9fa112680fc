import { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import { useSearchParams } from 'umi';

import { useGlobalHook } from '@/hooks';

import './index.less';

import { RoleItemModel } from '../../models/Role';

import { FunctionAuth } from './FunctionAuth';
import { RoleList } from './RoleList';
import { UserAuth } from './UserAuth';

export const Role = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [searchParams, setSearchParams] = useSearchParams();

  const tabName = searchParams.get('tabName');
  const [activeKey, setActiveKey] = useState(tabName ?? 'userAuth');
  const [chooseRole, setChooseRole] = useState<RoleItemModel>();

  useEffect(() => {
    setPageInfo({
      title: '角色管理',
      description:
        '角色管理用于给予特定职能的人员对应的操作权限，例如数据建模人员和数据开发人员都是角色。建议基于人员的技能或职能进行分派',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    if (tabName) {
      setActiveKey(tabName);
    } else {
      setActiveKey('userAuth');
    }
  }, [tabName]);

  const TabList = [
    {
      key: 'userAuth',
      label: '用户授权',
      children: <UserAuth id={chooseRole?.id} />,
    },
    {
      key: 'functionAuth',
      label: '功能授权',
      children: <FunctionAuth id={chooseRole?.id} />,
    },
  ];

  return (
    <div className='bg-white w-full h-full text-[14px] text-[rgba(0,0,0,0.65)] flex'>
      <RoleList chooseRole={chooseRole} getChooseRole={(role: RoleItemModel) => setChooseRole(role)} />
      <div className='flex-1'>
        <Tabs
          destroyInactiveTabPane
          tabBarStyle={{ paddingLeft: '16px' }}
          style={{ height: '100%' }}
          items={TabList}
          activeKey={activeKey}
          onChange={key => setSearchParams({ tabName: key })}
        />
      </div>
    </div>
  );
};
