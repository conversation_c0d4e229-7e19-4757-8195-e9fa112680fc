// import { CreateUserModal } from '../components';
import { useEffect, useState } from 'react';
import { message, Tooltip } from 'antd';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { useGlobalHook } from '@/hooks';

import { RoleItemModel } from '../models/Role';
import { UserItemModel } from '../models/User';
import { UserApi } from '../services/UserApi';

export const User = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<UserItemModel[]>([]);
  // const [open, setOpen] = useState(false);
  // const [editItem, setEditItem] = useState<UserItemModel>();

  const { filter, pagination, queryParams, setPagination, setFilter, handleTableChange } = useCustomTableHook({
    pageSize: 10,
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    cacheId: 'userList',
  });

  const columns: Array<ColumnType<UserItemModel>> = [
    {
      title: '用户名',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render(roles) {
        return (
          <Tooltip title={roles?.map((x: RoleItemModel) => x.name)?.join(',')}>
            {roles?.map((x: RoleItemModel) => x.name)?.join(',')}
          </Tooltip>
        );
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '手机',
      dataIndex: 'phone',
      key: 'phone',
    },
    // {
    //   title: '操作',
    //   key: 'action',
    //   width: 100,
    //   render(_: any, record) {
    //     return (
    //       <>
    //         <a className='mr-4' onClick={() => handleEdit(record)}>
    //           编辑
    //         </a>
    //       </>
    //     );
    //   }
    // }
  ];

  useEffect(() => {
    setPageInfo({
      title: '用户管理',
      description:
        '接入统一门户后，用户会自动出现在这里。如果采用其他用户信息对接方式，需开发用户信息授权相关接口，详见用户手册说明',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  // const handleCreate = () => {
  //   setOpen(true);
  // };

  // const handleEdit = (record: UserItemModel) => {
  //   setOpen(true);
  //   setEditItem(record);
  // };

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data, total } = await UserApi.query({
        ...queryParams,
        filter,
      });
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
      setLoading(false);
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='bg-white w-full h-full flex flex-col'>
      <div className='p-2 flex'>
        {/* <Button type='primary' onClick={handleCreate}>添加</Button> */}
        <SearchInput
          className='w-[240px] ml-4'
          placeholder='搜索用户名或姓名'
          defaultValue={filter.search}
          onSearch={val => setFilter({ ...filter, search: val })}
        />
      </div>
      <CustomTable
        className='flex-1'
        scroll={{ x: '100%' }}
        loading={loading}
        columns={columns}
        dataSource={list}
        pagination={pagination}
        onChange={handleTableChange}
      />
      {/* {
        open && (
          <CreateUserModal
            open={open}
            id={editItem?.id}
            onCancel={() => {
              setOpen(false);
              setEditItem(undefined);
            }}
            callback={() => {
              setOpen(false);
              setEditItem(undefined);
              fetchData();
            }}
          />
        )
      } */}
    </div>
  );
};
