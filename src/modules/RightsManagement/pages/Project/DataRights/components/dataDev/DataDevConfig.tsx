import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { debounce } from 'lodash';
import { useParams } from 'umi';

import { CustomTable } from '@/components';
import { BusinessFlowApi } from '@/services';
import { eachTree } from '@/utils/treeHelper';

import { useComplexTreeDataHook } from '../../hooks/useComplexTreeDataHook';

const treeConfig = { pid: 'parentId' };
const DataDevConfig = ({ authList, extra, mode }, ref) => {
  const { id } = useParams();
  const { treeData, setTreeData, commonColumns, getData } = useComplexTreeDataHook({ authList, mode, projectId: id });
  const [loading, setLoading] = useState(false);

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: () => fetchData(),
        getData,
      };
    },
    [treeData],
  );

  const fetchData = debounce(async () => {
    setLoading(true);
    const { data } = await BusinessFlowApi.getAuthTree(
      {
        filter: {
          type: extra?.type,
        },
        sort: {},
      },
      {
        headers: {
          'Jax-Super-Project-Manager-Id': id,
          'Jax-Super-Project-Admin': true,
          'Jax-Super-Project-Share': mode === 'share',
        },
      },
    ).finally(() => {
      setLoading(false);
    });

    eachTree(
      data,
      node => {
        node.id = node.referenceId;
        if (node.projectAuth?.resourceType === 'tb_business_flow') {
          delete node.projectAuth;
        }
        if (node.children?.length === 0) {
          delete node.children;
        }
      },
      treeConfig,
    );
    setTreeData(data);
  }, 200);

  useEffect(() => {
    fetchData();
  }, [extra]);

  const columns = [
    {
      title: '',
      dataIndex: 'nodeName',
      width: 400,
      render(name, record) {
        const { nodeType } = record;
        const icons = {
          BIZFLOW: 'icon-tree-fill',
        };
        return (
          <span>
            <i className={`iconfont ${icons[nodeType]} text-sm mr-1`}></i>
            {name}
          </span>
        );
      },
    },
    ...commonColumns,
  ];

  return (
    <div className='flex flex-col h-full'>
      <CustomTable
        loading={loading}
        dataSource={treeData}
        columns={columns}
        pagination={false}
        expandable={{ defaultExpandAllRows: true }}
        className='flex-1 bordered'
        rowKey='nodeId'
        scroll={{ x: 1000 }}
        virtual={true}
        rowClassName={record => {
          if (record.projectAuth) {
            return '';
          }
          return 'bg-[#fafafa]';
        }}
      />
    </div>
  );
};

export default forwardRef(DataDevConfig);
