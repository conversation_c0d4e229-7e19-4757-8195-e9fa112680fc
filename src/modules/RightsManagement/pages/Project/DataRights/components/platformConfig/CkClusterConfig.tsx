import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useParams } from 'umi';

import { CustomTable } from '@/components';
import { ingestionService } from '@/modules/DataIngestion/services';

import { useFlattenDataHook } from '../../hooks/useFlattenDataHook';

const CkClusterConfig = ({ authList, mode }, ref) => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);

  const { list, setList, commonColumns, getData } = useFlattenDataHook({ authList, mode, projectId: id });

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: fetchData,
        getData,
      };
    },
    [list],
  );

  const columns = [
    {
      title: '',
      dataIndex: 'name',
    },
    ...commonColumns,
  ];

  const fetchData = async () => {
    setLoading(true);
    const data = await ingestionService
      .getAllStorageCluster({
        headers: {
          'Jax-Super-Project-Manager-Id': id,
          'Jax-Super-Project-Admin': true,
          'Jax-Super-Project-Share': mode === 'share',
        },
      })
      .finally(() => {
        setLoading(false);
      });
    setList(data);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <CustomTable
      loading={loading}
      dataSource={list}
      columns={columns}
      pagination={false}
      scroll={{ x: 1000 }}
      className='bordered'
    />
  );
};

export default forwardRef(CkClusterConfig);
