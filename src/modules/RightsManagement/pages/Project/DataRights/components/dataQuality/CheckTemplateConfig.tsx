import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { debounce } from 'lodash';
import { useParams } from 'umi';

import { CustomTable, useCustomTableHook } from '@/components';
import { CheckRuleTemplateApi } from '@/services';

import { useFlattenDataHook } from '../../hooks/useFlattenDataHook';

const CheckTemplateConfig = ({ authList, mode }, ref) => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);

  const { pagination, queryParams, handleTableChange, setTotal } = useCustomTableHook({
    sort: {
      updateTime: 'DESC',
    },
  });

  const { list, setList, commonColumns, getData } = useFlattenDataHook({ authList, mode, projectId: id });

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: () => fetchData(queryParams),
        getData,
      };
    },
    [list],
  );

  const columns = [
    {
      title: '',
      dataIndex: 'name',
    },
    ...commonColumns,
  ];

  const fetchData = debounce(async queryParams => {
    setLoading(true);
    const { data, total } = await CheckRuleTemplateApi.getList(queryParams, {
      headers: {
        'Jax-Super-Project-Manager-Id': id,
        'Jax-Super-Project-Admin': true,
        'Jax-Super-Project-Share': mode === 'share',
      },
    }).finally(() => {
      setLoading(false);
    });
    setList(data);
    setTotal(total);
  }, 200);

  useEffect(() => {
    fetchData(queryParams);
  }, [queryParams]);

  return (
    <CustomTable
      dataSource={list}
      columns={columns}
      pagination={pagination}
      loading={loading}
      onChange={handleTableChange}
      scroll={{ x: 1000 }}
      className='bordered'
    />
  );
};

export default forwardRef(CheckTemplateConfig);
