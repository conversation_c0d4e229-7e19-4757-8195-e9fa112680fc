import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { debounce, groupBy } from 'lodash';
import { useParams } from 'umi';

import { CustomTable, useCustomTableHook } from '@/components';
import { CATALOGS } from '@/constants';
import { WarehouseLayerApi } from '@/services';

import { useComplexTreeDataHook } from '../../hooks/useComplexTreeDataHook';

const WarehouseLayerConfig = ({ authList, mode }, ref) => {
  const { id } = useParams();
  const [loading, setLoading] = useState(false);

  const { handleTableChange } = useCustomTableHook({
    sort: {
      updateTime: 'DESC',
    },
  });

  const { treeData, setTreeData, commonColumns, getData } = useComplexTreeDataHook({ authList, mode, projectId: id });

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: fetchData,
        getData,
      };
    },
    [treeData],
  );

  const columns = [
    {
      title: '',
      dataIndex: 'name',
    },
    ...commonColumns,
  ];

  const fetchData = debounce(async () => {
    setLoading(true);
    const data = await WarehouseLayerApi.getList(
      {},
      {
        headers: {
          'Jax-Super-Project-Manager-Id': id,
          'Jax-Super-Project-Admin': true,
          'Jax-Super-Project-Share': mode === 'share',
        },
      },
    ).finally(() => {
      setLoading(false);
    });

    const groups = groupBy(data, 'catalog');

    const treeData = CATALOGS.map(({ label, value }) => {
      return {
        name: label,
        id: value,
        children: groups[value] ?? [],
      };
    });

    setTreeData(treeData);
  }, 200);

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <CustomTable
      dataSource={treeData}
      columns={columns}
      pagination={false}
      loading={loading}
      onChange={handleTableChange}
      scroll={{ x: 1000 }}
      className='bordered'
    />
  );
};

export default forwardRef(WarehouseLayerConfig);
