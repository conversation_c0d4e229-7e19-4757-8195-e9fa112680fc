import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useParams } from 'umi';

import { CustomTable } from '@/components';
import { DataStandardService } from '@/services';
import { eachTree } from '@/utils/treeHelper';

import { useNormalTreeDataHook } from '../../hooks/useNormalTreeDataHook';

const treeConfig = { pid: 'parentId' };
const DataEnumConfig = ({ authList, mode }, ref) => {
  const { id } = useParams();
  const { treeData, setTreeData, getData, commonColumns } = useNormalTreeDataHook({ authList, mode, projectId: id });
  const [loading, setLoading] = useState(false);

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: fetchData,
        getData,
      };
    },
    [treeData],
  );

  const columns = [
    {
      title: '',
      dataIndex: 'name',
      render(name, record) {
        const { nodeType } = record;
        const icons = {
          enum: 'icon-json-line text-[#36cfc9]',
        };
        return (
          <span>
            <i className={`iconfont ${icons[nodeType]} text-sm mr-1`}></i>
            {name}
          </span>
        );
      },
    },
    ...commonColumns,
  ];

  const fetchData = async () => {
    setLoading(true);
    const { data } = await DataStandardService.enumService
      .getAuthTree({
        headers: {
          'Jax-Super-Project-Manager-Id': id,
          'Jax-Super-Project-Admin': true,
          'Jax-Super-Project-Share': mode === 'share',
        },
      })
      .finally(() => {
        setLoading(false);
      });

    eachTree(
      data,
      node => {
        if (!node.children || node.children.length === 0) {
          delete node.children;
        }
      },
      treeConfig,
    );

    setTreeData(data);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className='flex flex-col h-full'>
      <CustomTable
        loading={loading}
        dataSource={treeData}
        columns={columns}
        pagination={false}
        scroll={{ x: 1000 }}
        virtual={true}
        expandable={{ defaultExpandAllRows: true }}
        className='bordered flex-1'
      />
    </div>
  );
};

export default forwardRef(DataEnumConfig);
