import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { debounce } from 'lodash';
import { useParams } from 'umi';

import { CustomTable } from '@/components';
import { TableType } from '@/constants';
import { TableApi } from '@/services';
import { searchInString } from '@/utils';
import { eachTree } from '@/utils/treeHelper';

import { useComplexTreeDataHook } from '../../hooks/useComplexTreeDataHook';

import Filter from './Filter';

const treeConfig = { pid: 'parentId' };
const DimensionTableConfig = ({ authList, mode }, ref) => {
  const { id } = useParams();
  const filterInitial = {
    catalog: 'WH',
    searchType: 'dataDomainType',
    searchKey: '',
  };
  const { treeData, setTreeData, commonColumns, getData } = useComplexTreeDataHook({ authList, mode, projectId: id });
  const [loading, setLoading] = useState(false);
  const filterRef = useRef(filterInitial);

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: () => fetchData(filterRef.current),
        getData,
      };
    },
    [treeData],
  );
  
  function filterTreeDataNodeById(
    nodes: Array<TreeDataNodeWithData<DIMENSION_MODELING.ModelNode>>,
    keyword: string,
    visitor?: (node: TreeDataNodeWithData<DIMENSION_MODELING.ModelNode>) => void,
  ): Array<TreeDataNodeWithData<DIMENSION_MODELING.ModelNode>> {
    return nodes.filter(node => {
      const tokens = searchInString(node?.name ?? '' as string, keyword);
      const keep = !!tokens?.length;
      if (keep) {
        visitor?.(node);
      } else if (node.children) {
        node.children = filterTreeDataNodeById(node.children, keyword, visitor);
      }
      return keep || node.children?.length;
    });
  }

  const fetchData = debounce(async filter => {
    const { catalog, searchType, name = '' } = filter;
    setLoading(true);
    let apiName = 'getTreeByDataDomain';
    if (searchType === 'bizType') {
      apiName = 'getTreeByType';
    }
    const { data } = await TableApi[apiName](catalog, {
      headers: {
        'Jax-Super-Project-Manager-Id': id,
        'Jax-Super-Project-Admin': true,
        'Jax-Super-Project-Share': mode === 'share',
      },
    }).finally(() => {
      setLoading(false);
    });

    eachTree(
      data,
      node => {
        if (node.projectAuth) {
          delete node.children;
        } else {
          node.id = Math.random().toString();
        }
      },
      treeConfig,
    );
    setTreeData(name ? filterTreeDataNodeById(
      data,
      name,
      node => !node.isLeaf,
    ) : data);
  }, 200);

  const onChangeFilter = filter => {
    fetchData(filter);
    filter.current = filter;
  };

  useEffect(() => {
    fetchData(filterInitial);
  }, []);

  const FilterHeader = useMemo(() => {
    return (
      <div className=' max-w-lg'>
        <Filter
          catalogs={[
            { label: '公共层', value: 'WH' },
            { label: '应用层', value: 'APP' },
            { label: '贴源层', value: 'DS' },
          ]}
          filterInitial={filterInitial}
          onChange={onChangeFilter}
        />
      </div>
    );
  }, []);

  const columns = [
    {
      title: '',
      dataIndex: 'name',
      width: 400,
      render(name, record) {
        const { nodeType } = record;
        const isLeaf = Object.keys(TableType).includes(nodeType);
        const icons = {
          dataDomain: 'icon-earth-fill',
          dataSubject: 'icon-subject-line',
          businessProcess: 'icon-process-line',
          businessCategory: 'icon-grid_view-line',
          DIMENSION: 'icon-dimensionwd-line',
          DIM: 'icon-dimensiwd-line',
          ODS: 'icon-article-line',
          IDX: 'icon-preview-line',
          OBJ: 'icon-application-line',
          DWD: 'icon-view_list-fill',
          DWS: 'icon-description-line',
          ADS: 'icon-application-line',
        };
        return (
          <span>
            <i className={`iconfont ${icons[nodeType]} text-sm mr-1`}></i>
            {name}
            {!isLeaf && `(${record.tableCount})`}
          </span>
        );
      },
    },
    ...commonColumns,
  ];

  return (
    <div className='flex flex-col h-full'>
      {FilterHeader}
      <CustomTable
        loading={loading}
        dataSource={treeData}
        columns={columns}
        pagination={false}
        expandable={{ defaultExpandAllRows: true }}
        className='flex-1 bordered'
        scroll={{ x: 1000 }}
        virtual={true}
        rowClassName={record => {
          if (record.projectAuth) {
            return '';
          }
          return 'bg-[#fafafa]';
        }}
      />
    </div>
  );
};

export default forwardRef(DimensionTableConfig);
