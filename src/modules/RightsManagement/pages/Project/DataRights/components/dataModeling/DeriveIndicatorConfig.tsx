import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { debounce } from 'lodash';
import { useParams } from 'umi';

import { CustomTable } from '@/components';
import Filter from '@/modules/DataIndicator/components/TreePanel/Filter';
import { DerivativeIndicatorApi } from '@/services';
import { eachTree } from '@/utils/treeHelper';

import { useComplexTreeDataHook } from '../../hooks/useComplexTreeDataHook';
import { treeFilter } from '../../utils';

const treeConfig = { pid: 'parentId' };
const DeriveIndicatorConfig = ({ authList, mode }, ref) => {
  const { id } = useParams();
  const filterInitial = {
    catalog: 'WH',
    searchType: 'dataDomainType',
    searchKey: '',
  };
  const { treeData, setTreeData, commonColumns, getData } = useComplexTreeDataHook({ authList, mode, projectId: id });
  const [loading, setLoading] = useState(false);
  const filterRef = useRef(filterInitial);

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: () => fetchData(filterRef.current),
        getData,
      };
    },
    [treeData],
  );

  const fetchData = debounce(async filter => {
    const { catalog, searchType } = filter;
    setLoading(true);
    const { data } = await DerivativeIndicatorApi.getTree(
      { catalog, searchType },
      {
        headers: {
          'Jax-Super-Project-Manager-Id': id,
          'Jax-Super-Project-Admin': true,
          'Jax-Super-Project-Share': mode === 'share',
        },
      },
    ).finally(() => {
      setLoading(false);
    });

    eachTree(
      data,
      node => {
        if (node.nodeType.indexOf('Leaf') !== -1 || node.children.length === 0) {
          delete node.children;
        }
      },
      treeConfig,
    );
    setTreeData(treeFilter({ treeData: data, filter, leafNodeType: 'derivativeLeaf' }));
  }, 200);

  const onChangeFilter = filter => {
    fetchData(filter);
    filter.current = filter;
  };

  useEffect(() => {
    fetchData(filterInitial);
  }, []);

  const FilterHeader = useMemo(() => {
    return (
      <div className=' max-w-lg'>
        <Filter
          catalogs={[
            { label: '公共层', value: 'WH' },
            { label: '应用层', value: 'APP' },
          ]}
          filterInitial={filterInitial}
          onChange={onChangeFilter}
        />
      </div>
    );
  }, []);

  const columns = [
    {
      title: '',
      dataIndex: 'name',
      width: 400,
      render(name, record) {
        const { nodeType } = record;
        const icons = {
          dataDomain: 'icon-earth-fill',
          dataSubject: 'icon-subject-line',
          businessProcess: 'icon-process-line',
          derivativeLeaf: 'text-warning icon-charts-line',
          businessCategory: 'icon-grid_view-line',
        };
        return (
          <span>
            <i className={`iconfont ${icons[nodeType]} text-sm mr-1`}></i>
            {name}
          </span>
        );
      },
    },
    ...commonColumns,
  ];

  return (
    <div className='flex flex-col h-full'>
      {FilterHeader}
      <CustomTable
        loading={loading}
        dataSource={treeData}
        columns={columns}
        pagination={false}
        expandable={{ defaultExpandAllRows: true }}
        className='flex-1 bordered'
        scroll={{ x: 1000 }}
        virtual={true}
        rowClassName={record => {
          if (record.projectAuth) {
            return '';
          }
          return 'bg-[#fafafa]';
        }}
      />
    </div>
  );
};

export default forwardRef(DeriveIndicatorConfig);
