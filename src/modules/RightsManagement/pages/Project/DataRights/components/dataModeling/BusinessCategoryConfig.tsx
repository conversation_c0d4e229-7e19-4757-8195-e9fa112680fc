import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useParams } from 'umi';

import { CustomTable } from '@/components';
import { BusinessCategoryApi } from '@/services';
import { eachTree } from '@/utils/treeHelper';

import { useNormalTreeDataHook } from '../../hooks/useNormalTreeDataHook';

const treeConfig = { pid: 'parentId' };
const BusinessCategoryConfig = ({ authList, mode }, ref) => {
  const { id } = useParams();
  const { treeData, setTreeData, getData, commonColumns } = useNormalTreeDataHook({ authList, mode, projectId: id });
  const [loading, setLoading] = useState(false);

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: fetchData,
        getData,
      };
    },
    [treeData],
  );

  const columns = [
    {
      title: '',
      dataIndex: 'name',
      render(name) {
        return (
          <span>
            <i className='iconfont icon-grid_view-line text-sm mr-1'></i>
            {name}
          </span>
        );
      },
    },
    ...commonColumns,
  ];

  const fetchData = async () => {
    setLoading(true);
    const { data } = await BusinessCategoryApi.getTree({
      headers: {
        'Jax-Super-Project-Manager-Id': id,
        'Jax-Super-Project-Admin': true,
        'Jax-Super-Project-Share': mode === 'share',
      },
    }).finally(() => {
      setLoading(false);
    });

    eachTree(
      data,
      node => {
        if (!node.children || node.children.length === 0) {
          delete node.children;
        }
      },
      treeConfig,
    );

    setTreeData(data);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className='h-full overflow-hidden'>
      <CustomTable
        loading={loading}
        dataSource={treeData}
        columns={columns}
        pagination={false}
        scroll={{ x: 1000 }}
        virtual={true}
        className='bordered'
      />
    </div>
  );
};

export default forwardRef(BusinessCategoryConfig);
