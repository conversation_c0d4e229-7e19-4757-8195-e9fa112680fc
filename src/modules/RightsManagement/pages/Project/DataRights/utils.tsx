
const filterByBizId = (data: TreeItem[], bizId: string) => {
  let flag = false;
  let subTree: TreeItem[] = [];
  const loop = (data: TreeItem[]) => {
    if (flag) return;
    if (bizId) {
      const findItem = data.find(x => {
        return x.nodeType === 'businessCategory' && x.id == bizId;
      });
      if (findItem) {
        flag = true;
        subTree = [findItem];
      } else {
        data.forEach(item => {
          item.children && item.children.length > 0 && loop(item.children);
        });
      }
    }
  };
  loop(data);
  return subTree;
};

const filterByDomId = (data: TreeItem[], domId: string) => {
  return data.filter(x => {
    return x.nodeType === 'dataDomain' && x.id == domId;
  });
};

export const treeFilter = ({ filter, leafNodeType, treeData }) => {
  const { searchKey: searchValue, domId, bizId } = filter;
  const loop = (data: TreeItem[]) => {
    return data
      .filter(item => {
        if (item.nodeType === leafNodeType) {
          return item.name.includes(searchValue);
        } else {
          return true;
        }
      });
  };

  if (!domId && !bizId) {
    return loop(treeData);
  }
  if (bizId) {
    return loop(filterByBizId(treeData, bizId));
  } else if (domId) {
    return loop(filterByDomId(treeData, domId));
  }
};
