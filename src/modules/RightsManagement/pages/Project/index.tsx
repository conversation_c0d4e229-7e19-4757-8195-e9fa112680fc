import { useEffect, useState } from 'react';
import { Button, message, Modal, Progress, Tooltip } from 'antd';
import { history, Link } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { useGlobalHook, useRightsHook } from '@/hooks';

import { CreateProjectModal } from '../../components';
import { ProjectItemModel } from '../../models/Project';
import { ProjectApi } from '../../services/ProjectApi';

export const Project = () => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<ProjectItemModel[]>([]);
  const [open, setOpen] = useState(false);
  const [editItem, setEditItem] = useState<ProjectItemModel>();

  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { hasRights } = useRightsHook();

  const { filter, pagination, queryParams, setPagination, handleTableChange, setFilter } = useCustomTableHook({
    pageSize: 10,
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    cacheId: 'projectList',
  });

  const columns: Array<ColumnType<ProjectItemModel>> = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '关联用户组',
      dataIndex: 'userGroups',
      key: 'userGroups',
      render(userGroups) {
        return userGroups?.map(x => x.name).join(',');
      },
    },
    {
      title: '计算资源配额',
      dataIndex: '',
      key: 'resourceCalc',
      width: 400,
      render(_, record) {
        if (record.enableResourceLimit) {
          const { cpu, usedCpu, memory, usedMemory } = record;
          const cpuRate = Number((((usedCpu ?? 0) * 100) / cpu!).toFixed(2));
          const memoryRate = Number((((usedMemory ?? 0) * 100) / memory!).toFixed(2));
          const cpuTooltipContent = `CPU已用${usedCpu}核 / 配额${cpu}核`;
          const memoryTooltipContent = `内存已用${((usedMemory ?? 0) / 1024).toFixed(2)}GB
          / 配额${((memory ?? 0) / 1024).toFixed(2)}GB`;
          return <div className='flex flex-row'>

            <Tooltip title={ cpuTooltipContent }>
              <label className='mr-2'>CPU</label>
              <Progress
                percent={cpuRate}
                status={cpuRate > 100 ? 'exception' : 'success'}
                size='small'
                showInfo={false}
                style={{ width: 80 }}
              ></Progress>
              <span className='text-xs ml-1'>{cpuRate}%</span>
            </Tooltip>

            <Tooltip title={ memoryTooltipContent }>
              <label className='mx-2'>内存</label>
              <Progress
                percent={memoryRate}
                status={memoryRate > 100 ? 'exception' : 'success'}
                size='small'
                showInfo={false}
                style={{ width: 80 }}
              ></Progress>
              <span className='text-xs ml-1'>{memoryRate}%</span>
            </Tooltip>
          </div>;
        }
        return '不限额';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      render(_: any, record) {
        return (
          <div className='flex gap-x-2'>
            <Button type='link' size='small' disabled={!hasRights('project:write')} onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Link to={`/rights-management/project/member-management/${record.id}?projectName=${record.name}`}>
              成员管理
            </Link>
            <Button
              type='link'
              size='small'
              disabled={!hasRights('project_resource:share')}
              onClick={() => {
                history.push(`/rights-management/project/data-share/${record.id}?title=${record.name}`);
              }}
            >
              共享设置
            </Button>
            <Button
              type='link'
              size='small'
              disabled={!hasRights('project_resource:permission')}
              onClick={() => {
                history.push(`/rights-management/project/data-rights/${record.id}?title=${record.name}`);
              }}
            >
              权限设置
            </Button>
            <Button
              type='link'
              size='small'
              disabled={!hasRights('project:write')}
              onClick={() => handleDelete(record)}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];

  const handleCreate = () => {
    setOpen(true);
  };

  const handleEdit = (record: ProjectItemModel) => {
    setOpen(true);
    setEditItem(record);
  };

  const handleDelete = (record: ProjectItemModel) => {
    const { name, id } = record;
    Modal.confirm({
      title: `确定删除项目「${name}」吗`,
      async onOk() {
        try {
          await ProjectApi.delete(id);
          fetchData();
          message.success('删除成功！');
        } catch (e: any) {
          message.error(e.msg);
        }
      },
    });
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data, total } = await ProjectApi.query({
        ...queryParams,
        filter,
      });
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
      setLoading(false);
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    setPageInfo({
      title: '项目管理',
      description: '管理项目团队，用于隔离数据权限。需具备多租户授权',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  return (
    <div className='bg-white w-full h-full flex flex-col'>
      <div className='p-2 flex'>
        <Button type='primary' disabled={!hasRights('project:write')} onClick={handleCreate}>
          创建项目
        </Button>
        <SearchInput
          className='w-[240px] ml-4'
          placeholder='搜索项目名称'
          defaultValue={filter.search}
          onSearch={val => setFilter({ ...filter, name: val })}
        />
      </div>
      <CustomTable
        className='flex-1'
        scroll={{ x: '100%' }}
        loading={loading}
        columns={columns}
        onChange={handleTableChange}
        dataSource={list}
        pagination={pagination}
      />
      {open && (
        <CreateProjectModal
          open={open}
          id={editItem?.id}
          onCancel={() => {
            setOpen(false);
            setEditItem(undefined);
          }}
          callback={() => {
            setOpen(false);
            setEditItem(undefined);
            fetchData();
          }}
        />
      )}
    </div>
  );
};
