import { useEffect, useState } from 'react';
import { message } from 'antd';
import { useParams, useSearchParams } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { useGlobalHook } from '@/hooks';

import { RoleTag } from '../../components/RoleTag';
import { MemberItemModel } from '../../models/Project';
import { ProjectApi } from '../../services/ProjectApi';

export const MemberManagement = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const projectName = searchParams.get('projectName');

  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<MemberItemModel[]>([]);

  const { setPageInfo, resetPageInfo } = useGlobalHook();

  const { pagination, setPagination, queryParams, handleTableChange, filter, setFilter } = useCustomTableHook({
    pageSize: 10,
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    cacheId: 'memberManagement',
  });

  const columns: Array<ColumnType<MemberItemModel>> = [
    {
      title: '项目成员',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
    },
    {
      title: '用户名',
      dataIndex: 'account',
      key: 'account',
      width: '20%',
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render: (_, record) => {
        return <RoleTag memberItem={record} callback={fetchData} />;
      },
    },
  ];

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data, total } = await ProjectApi.queryMember(id!, queryParams);
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
      setLoading(false);
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    setPageInfo({
      title: `成员管理-${projectName}`,
      description: '项目组成员角色默认继承用户的全局角色，可以在这里单独为成员设置在项目组内的角色',
      backUrl: '/rights-management/project',
    });
    return () => resetPageInfo();
  }, []);

  return (
    <div className='bg-white w-full h-full flex flex-col'>
      <div className='py-2 px-3'>
        <SearchInput
          className='w-[240px]'
          placeholder='搜索用户名或姓名'
          defaultValue={filter.search}
          onSearch={val => setFilter({ ...filter, search: val })}
        />
      </div>
      <CustomTable
        className='flex-1'
        scroll={{ x: '100%' }}
        loading={loading}
        onChange={handleTableChange}
        columns={columns}
        dataSource={list}
        pagination={pagination}
      />
    </div>
  );
};
