import { useEffect, useState } from 'react';
import { Checkbox, message, Modal, ModalProps, Tree } from 'antd';

import { findPath } from '@/utils/treeHelper';

import { RoleItemModel } from '../models/Role';
import { RoleApi } from '../services/RoleApi';

interface Props extends ModalProps {
  id: string;
  treeData: RoleItemModel[];
  permissionCodes: string[];
  handleOk?: (ids: string[]) => void;
  callback?: () => void;
}

export const AddAuthModal = ({ id, permissionCodes, treeData, callback, ...otherProps }: Props) => {
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  const onOk = async e => {
    try {
      await RoleApi.updatePermissions({
        roleId: id,
        permissionCodes: checkedKeys.map(x => ({ permissionCode: x })),
      });
      message.success('编辑成功');
      otherProps.onCancel?.(e);
      callback?.();
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  const onCheck = (e, item) => {
    const checked = e.target.checked;
    const parentsKeys = findPath(treeData, node => {
      return node.code === item.code;
    })?.map(x => x.code);
    const childrenKeys = getChildrenKeys(item);

    if (!checked) {
      const filterKeys = [...childrenKeys, item.code];

      const newCheckedKeys = checkedKeys.filter(x => !filterKeys.includes(x));
      return setCheckedKeys(newCheckedKeys);
    }

    setCheckedKeys([...new Set([item.code, ...parentsKeys, ...childrenKeys, ...checkedKeys])]);
  };

  const getChildrenKeys = item => {
    let ids = [] as any[];
    item.children?.forEach(node => {
      ids = [...ids, node.code];
      if (node.children) {
        ids = [...ids, ...getChildrenKeys(node)];
      }
    });

    return ids;
  };

  const renderTitle = node => {
    return (
      <p className='ml-2'>
        <Checkbox
          checked={checkedKeys.some(key => key === node.code)}
          onChange={e => onCheck(e, node)}
          onClick={e => e.stopPropagation()}
        />
        <span className='ml-2'>{node.codeName}-{node.code}</span>
      </p>
    );
  };

  useEffect(() => {
    if (permissionCodes.length) setCheckedKeys(permissionCodes);
  }, [permissionCodes]);

  return (
    <Modal width={760} title='编辑权限' {...otherProps} onOk={onOk}>
      <div className='h-[450px] overflow-auto'>
        <Tree
          className='custom-directory-tree'
          fieldNames={{ title: 'name', key: 'id' }}
          titleRender={renderTitle}
          showLine
          blockNode
          defaultExpandAll
          showIcon={false}
          treeData={treeData}
          checkedKeys={checkedKeys}
        />
      </div>
    </Modal>
  );
};
