import { useEffect, useState } from 'react';
import { message, Modal, ModalProps, Spin } from 'antd';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';

import { RoleItemModel } from '../models/Role';
import { UserItemModel } from '../models/User';
import { UserApi } from '../services/UserApi';

interface Props extends ModalProps {
  id: string;
  filterKey?: string;
  handleOk?: (ids: string[]) => void;
}

export const AddRelationUserModal = ({ id, filterKey = 'neGroupId', handleOk, ...otherProps }: Props) => {
  const [loading, setLoading] = useState(false);
  const [userList, setUserList] = useState<UserItemModel[]>([]);

  const {
    filter,
    pagination,
    queryParams,
    selectedRowKeys,
    setPagination,
    setFilter,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    pageSize: 10,
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    cacheId: 'addRelationUser',
  });

  const columns: Array<ColumnType<UserItemModel>> = [
    {
      title: '用户名',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render(roles) {
        return roles?.map((x: RoleItemModel) => x.name)?.join(',');
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '手机',
      dataIndex: 'phone',
      key: 'phone',
    },
  ];

  const fetchUserList = async () => {
    if (!id) return;
    try {
      setLoading(true);
      const { data, total } = await UserApi.query({
        ...queryParams,
        filter: {
          ...filter,
          [filterKey]: id,
        },
      });
      setUserList(data);
      setPagination({
        ...pagination,
        total,
      });
      setLoading(false);
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchUserList();
  }, [queryParams]);

  return (
    <Modal width={760} title='添加用户' {...otherProps} onOk={() => handleOk?.(selectedRowKeys)}>
      {loading ? (
        <div className='w-full h-full flex justify-center items-center'>
          <Spin />
        </div>
      ) : (
        <div className='h-[450px] flex flex-col'>
          <div>
            <SearchInput
              className='w-[240px]'
              placeholder='搜索用户名或姓名'
              defaultValue={filter.search}
              onSearch={val => setFilter({ ...filter, search: val })}
            />
          </div>
          <CustomTable
            className='flex-1 border mt-3'
            scroll={{ x: '100%' }}
            rowKey='id'
            loading={loading}
            columns={columns}
            dataSource={userList}
            pagination={pagination}
            onChange={handleTableChange}
            onRowSelectionChange={onRowSelectionChange}
          />
        </div>
      )}
    </Modal>
  );
};
