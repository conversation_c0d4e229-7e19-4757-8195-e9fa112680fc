import Request from '@/request';

import { UserGroupItemModel } from '../models/UserGroup';

const baseUrl = '/api/v2/security/user-group';

export const UserGroupApi = {
  /**
   * 分页查询
   * @param data
   * @returns
   */
  async query(data: QueryParams) {
    return await Request.post(`${baseUrl}/query`, { data });
  },
  /**
   * 全部查询
   * @param
   * @returns
   */
  async list(data: QueryParams) {
    return await Request.post(`${baseUrl}/list`, { data });
  },
  /**
   * 详情
   * @param id
   * @returns
   */
  async detail(id: string) {
    return await Request.get(`${baseUrl}/${id}`);
  },
  /**
   * 编辑
   * @param id
   * @returns
   */
  async update(data: UserGroupItemModel & { userIds: string[] }) {
    return await Request.put(`${baseUrl}/${data.id}`, { data });
  },
  /**
   * 创建
   * @param data
   * @returns
   */
  async create(data: Omit<UserGroupItemModel, 'id'> & { userIds: string[] }) {
    return await Request.post(`${baseUrl}`, { data });
  },
  /**
   * 删除
   * @param id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${baseUrl}/${id}`);
  },
  /**
   * 批量添加用户
   * @param data
   * @returns
   */
  async addUsers(data: { id: string; userIds: string[] }) {
    return await Request.put(`${baseUrl}/${data.id}/users`, { data });
  },
  /**
   * 批量移除用户
   * @param data
   * @returns
   */
  async deleteUsers(data: { id: string; userIds: string[] }) {
    return await Request.delete(`${baseUrl}/${data.id}/users`, { data });
  },
};
