import { RoleItemModel } from './Role';
import { UserGroupItemModel } from './UserGroup';

export interface ProjectItemModel {
  createTime: string;
  createUserName: string;
  description: string;
  id: string;
  name: string;
  updateTime: string;
  updateUserName: string;
  userGroups: UserGroupItemModel[];
  enableResourceLimit: boolean;
  cpu?: number;
  memory?: number;
  usedCpu?: number;
  usedMemory?: number;
}

export interface MemberItemModel {
  account: string;
  createTime: string;
  createUserName: string;
  description: string;
  email: string;
  guid: string;
  id: string;
  name: string;
  phone: string;
  platform: string;
  remark: string;
  roles: RoleItemModel[];
}
