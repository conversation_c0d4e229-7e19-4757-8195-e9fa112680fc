import React from 'react';
import { Form, Input, Modal } from 'antd';

import { formCol } from '../constants/common';
import { nameEnRules, nameRules, requiredRule } from '../constants/rules';
import { editFormColumns } from '../constants/tableConfig';

import EditList from './EditList';

const FormItem = Form.Item;

const EnumModal: React.FC<FormModalProps> = props => {
  const { open, onClose, onSave, initialValues, title } = props;
  const [loading, setLoading] = React.useState(false);
  const [form] = Form.useForm(props.form);

  React.useEffect(() => {
    if (!open) return;
    if (!initialValues) {
      form.resetFields();
    } else {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, open]);

  const handleOk = () => {
    form.validateFields().then(value => {
      setLoading(true);
      value.enumValues = value.enumValues
        ?.filter((d: EnumValue | undefined) => {
          if (!d) return false;
          return Object.values(d).filter(d => !!d).length > 0;
        })
        ?.map((d: any) => {
          for (const key in d) {
            if (Object.prototype.hasOwnProperty.call(d, key)) {
              if (d[key] === undefined) {
                d[key] = '';
              }
            }
          }

          return d;
        });
      onSave(value)
        .then(() => {
          handleClose();
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleClose = () => {
    setLoading(false);
    onClose();
  };

  return (
    <Modal
      title={title}
      open={open}
      destroyOnClose
      className='w-[760px]'
      styles={{
        body: {
          maxHeight: 500,
          overflow: 'auto',
        },
      }}
      onCancel={handleClose}
      onOk={handleOk}
      okText='确定'
      cancelText='取消'
      confirmLoading={loading}
    >
      <Form form={form} {...formCol}>
        <FormItem className='mb-2' label='标准编码' name='no' required>
          <Input disabled placeholder='由系统自动生成' />
        </FormItem>
        <FormItem label='代码名称' name='name' rules={[requiredRule('请输入代码名称'), ...nameRules]} className='mb-2'>
          <Input placeholder='请输入' allowClear />
        </FormItem>
        <FormItem
          label='英文名称'
          name='nameEn'
          rules={[requiredRule('请输入英文名称'), ...nameEnRules]}
          className='mb-2'
        >
          <Input placeholder='请输入' allowClear />
        </FormItem>
        <FormItem wrapperCol={{ span: 16 }} label='描述' name='description' className='mb-2'>
          <Input.TextArea autoSize={{ minRows: 3 }} placeholder='请输入' allowClear />
        </FormItem>
        <FormItem wrapperCol={{ span: 18 }} label='编码设置' className='mb-2'>
          <EditList
            className='mt-[5px]'
            name='enumValues'
            initialValues={initialValues?.enumValues}
            columns={editFormColumns}
          />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default EnumModal;
