import { HTMLAttributes } from 'react';
import { Empty } from 'antd';
import cs from 'classnames';

import TreePlaceholder from '@/assets/images/tree-placeholder.svg';

type Props = HTMLAttributes<HTMLDivElement>;

export default function EmptyStatus({ className, style, ...props }: Props) {
  return (
    <div
      {...props}
      className={cs('flex grow justify-center items-center', className)}
      style={{ backgroundColor: 'white', ...style }}
    >
      <Empty
        description={<div>点击左侧树目录查看</div>}
        image={TreePlaceholder}
        imageStyle={{
          width: 256,
          height: 208,
        }}
      />
    </div>
  );
}
