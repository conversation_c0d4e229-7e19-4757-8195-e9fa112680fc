import React from 'react';
import { Form, Input, Modal, Select } from 'antd';

import { formCol } from '../constants/common';
import { codeRules, nameEnRules, nameRules, requiredRule } from '../constants/rules';
import { useLogicHook } from '../hooks/logic';
import { getTreeType } from '../utils/measure';

const FormItem = Form.Item;

const MeasureModal: React.FC<FormModalProps> = props => {
  const { open, onClose, onSave, initialValues, title } = props;
  const [loading, setLoading] = React.useState(false);
  const [form] = Form.useForm(props.form);

  const treeType = getTreeType();
  const { treeData } = useLogicHook(treeType);

  React.useEffect(() => {
    if (!initialValues) {
      form.resetFields();
    } else {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues]);

  const handleOk = () => {
    form.validateFields().then(value => {
      setLoading(true);
      onSave(value)
        .then(() => {
          handleClose();
        })
        .catch(() => {
          setLoading(false);
        });
    });
  };

  const handleClose = () => {
    setLoading(false);
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title={title}
      open={open}
      destroyOnClose
      className='w-[760px]'
      styles={{
        body: {
          maxHeight: 600,
          overflow: 'auto',
        },
      }}
      onCancel={handleClose}
      onOk={handleOk}
      okText='确定'
      cancelText='取消'
      confirmLoading={loading}
    >
      <Form form={form} {...formCol}>
        <FormItem className='mb-2' label='英文缩写' name='code' rules={[requiredRule('请输入英文缩写'), ...codeRules]}>
          <Input disabled={initialValues?.code} placeholder='请输入' allowClear />
        </FormItem>
        <FormItem
          className='mb-2'
          label='英文名称'
          name='nameEn'
          rules={[requiredRule('请输入英文名称'), ...nameEnRules]}
        >
          <Input placeholder='请输入' allowClear />
        </FormItem>
        <FormItem className='mb-2' label='中文名称' name='name' rules={[requiredRule('请输入中文名称'), ...nameRules]}>
          <Input placeholder='请输入' allowClear />
        </FormItem>

        <FormItem className='mb-2' label='分类' name='catalog' rules={[requiredRule('请选择分类')]}>
          <Select
            className='w-[160px]'
            options={treeData}
            fieldNames={{ label: 'name', value: 'id' }}
            placeholder='请选择'
            allowClear
          />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default MeasureModal;
