import { FC, MouseEventHandler, useRef, useState } from 'react';
import { CloseCircleFilled } from '@ant-design/icons';
import { Form, Input, Tooltip } from 'antd';
import classnames from 'classnames';

export interface EditListProps {
  initialValues: any[];
  columns: EditColumn[];
  name: string;
  hasDelete?: boolean;
  className?: string;
}

const EditList: FC<EditListProps> = ({ initialValues, columns, name, className, hasDelete = true }) => {
  const idx = useRef(initialValues?.length ?? 0);
  const [idxs, setIdxs] = useState((initialValues || []).map((v, i) => i));

  const handleAdd: MouseEventHandler = e => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    e.preventDefault();
    setIdxs([...idxs, idx.current]);
    idx.current = idx.current + 1;
  };

  const handleDelete = (delId: number) => () => {
    setIdxs(idxs.filter(idx => idx !== delId));
  };

  return (
    <div className={classnames('w-full', className)}>
      <div style={{ textAlign: 'left' }}>
        <header className={classnames('flex items-center mb-2', hasDelete && 'pr-6')}>
          {columns.map((col, i) => (
            <div className={classnames('flex-1', i === columns.length - 1 ? 'mr-0' : 'mr-2')} key={col.name}>
              {col.label}
              {col.required && <span className='text-danger'>*</span>}
              <Tooltip title={col.help}>
                <i className='iconfont icon-help-line text-sm ml-2 cursor-pointer text-gray-5'></i>
              </Tooltip>
            </div>
          ))}
        </header>
        <div>
          {idxs.map(idx => (
            <div key={idx} className='flex items-center mb-2'>
              {columns.map(col => (
                <Form.Item
                  key={col.name}
                  name={[name, idx, col.name]}
                  rules={col?.rules ?? []}
                  className='flex-1 mb-0 mr-2'
                >
                  <Input placeholder='请输入' />
                </Form.Item>
              ))}
              {hasDelete ? <CloseCircleFilled style={{ color: '#E53737' }} onClick={handleDelete(idx)} /> : null}
            </div>
          ))}
        </div>
      </div>
      <a onClick={handleAdd}>添加</a>
    </div>
  );
};

export default EditList;
