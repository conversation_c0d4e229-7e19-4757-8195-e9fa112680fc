import { Button } from 'antd';

import { SearchInput } from '@/components';

export default function SearchHeader(props: SearchHeaderProps) {
  const { buttonText, onClick, onChange, disabled, placeholder = '请输入搜索关键字' } = props;
  return (
    <header className='flex px-4 py-4 pb-3'>
      <Button type='primary' onClick={onClick} disabled={disabled}>
        {buttonText}
      </Button>
      <SearchInput placeholder={placeholder} className='w-[188px] ml-[8px]' onSearch={onChange} allowClear />
    </header>
  );
}
