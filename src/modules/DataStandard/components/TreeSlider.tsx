import { Key, useEffect, useState } from 'react';
import { Dropdown, Tree } from 'antd';
import { uniq } from 'lodash';

import { Icon, SearchInput } from '@/components';
import { useRightsHook } from '@/hooks';
import { downloadFile } from '@/utils';

import treeConfig from '../constants/treeConfig';
import { getSearchParentIds } from '../utils/tree';

import styles from './TreeSlider.less';

function getTreeDataWithTitle(treeData?: TreeNode[]) {
  if (Array.isArray(treeData)) {
    return treeData.map(d => ({
      ...d,
      title: d.name,
      children: getTreeDataWithTitle(d.children),
    }));
  } else {
    return [];
  }
}

const dataType = ['column', 'enum', 'measure'];

function TreeSlider(props: TreeSliderProps) {
  const { type, treeData, onMenuSelect, onTreeSelect } = props;
  const { hasRights } = useRightsHook();
  const { title, addMenus, nodeMenus, disabledKey } = treeConfig[type];
  const [selectedNode, setSelectedNode] = useState<TreeNode>();
  const [searchText, setSearchText] = useState<string>('');
  const [expandIds, setExpandIds] = useState<never[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(false);

  useEffect(() => {
    if (props.selectedNode && !Object.is(selectedNode, props.selectedNode)) {
      setSelectedNode(props.selectedNode);

      if (props.selectedNode?.parentIds) {
        const newExpandIds = [].concat(expandIds, props.selectedNode.parentIds as never[]);
        setExpandIds(uniq(newExpandIds));
      }
    }
  }, [props.selectedNode?.id]);

  const handleSearch = value => {
    setSearchText(value);
    if (!value) {
      setExpandIds([]);
      setAutoExpandParent(false);
    } else {
      const parentIds = getSearchParentIds(treeData, {
        target: value,
        field: 'name',
      });
      setExpandIds(parentIds as never[]);
      setAutoExpandParent(true);
    }
  };

  const handleSelect = (selectKeys: Key[], { node }: { node: TreeNode }) => {
    setSelectedNode(node);
    onTreeSelect(node);
  };

  const handleExpand = (expandKeys: Key[]) => {
    setAutoExpandParent(false);
    setExpandIds(expandKeys as never[]);
  };

  const selectNodeMenu =
    (node?: TreeNode) =>
      ({ key, domEvent }: any) => {
        domEvent.stopPropagation();
        domEvent.nativeEvent.stopImmediatePropagation();

        onMenuSelect(key, node);
      };
  const renderTitle = (nodeData: TreeNode) => {
    const menuData =
      typeof nodeMenus === 'function'
        ? nodeMenus(nodeData, !hasRights(disabledKey!, nodeData.projectAuth), !hasRights(disabledKey!))
        : nodeMenus ?? [];

    function getTreeLabel(node: TreeNode, searchText: string) {
      const { name } = node;
      const index = name.indexOf(searchText);
      const beforeStr = name.substring(0, index);
      const afterStr = name.substring(index + searchText.length);

      const label =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: '#f50' }}>{searchText}</span>
            {afterStr}
          </span>
        ) : (
          <span className='text-gray-6'>{name}</span>
        );

      return label;
    }

    return (
      <div key={nodeData.id} className={styles.treeItem}>
        <span className={styles.itemWrapper}>{getTreeLabel(nodeData, searchText)}</span>
        <Dropdown
          menu={{
            items: menuData,
            onClick: selectNodeMenu(nodeData),
          }}
        >
          <span onClick={e => e.stopPropagation()}>
            <i className='iconfont icon-more_vert-fill text-sm text-gray-5'></i>
          </span>
        </Dropdown>
      </div>
    );
  };

  const handleExportExcel = () => {
    downloadFile(
      '/api/v2/data/modeling/excel/export',
      {
        type: dataType[type],
      },
      'post',
    );
  };

  return (
    <div className={styles.tree}>
      <header className='flex items-center justify-between my-3 px-2'>
        <h4 className={styles.title}>{title}</h4>
        <div>
          {hasRights(disabledKey!) && (
            <Dropdown
              trigger={['click']}
              menu={{
                items: typeof addMenus === 'function' ? addMenus(selectedNode) : addMenus ?? [],
                onClick: selectNodeMenu(),
                disabled: !hasRights(disabledKey!),
              }}
            >
              <Icon name='add_box-line' size={24} className='ml-2 cursor-pointer' />
            </Dropdown>
          )}
          <Icon
            name='save_alt-line'
            size={24}
            className='ml-2 cursor-pointer'
            title='导出'
            onClick={handleExportExcel}
          />
        </div>
      </header>
      <div className='px-2'>
        <SearchInput className='px-2' placeholder='请输入搜索关键字' allowClear onSearch={handleSearch} />
      </div>

      <Tree
        className={`custom-directory-tree hidden-leaf-switcher ${styles.treeWrapper}`}
        blockNode
        selectable
        showIcon
        autoExpandParent={autoExpandParent}
        treeData={getTreeDataWithTitle(treeData)}
        expandedKeys={expandIds}
        selectedKeys={selectedNode ? [selectedNode?.id] : []}
        fieldNames={{ key: 'id' }}
        onSelect={handleSelect}
        onExpand={handleExpand}
        titleRender={renderTitle}
      />
    </div>
  );
}

export default TreeSlider;
