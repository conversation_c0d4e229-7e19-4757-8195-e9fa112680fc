import { useEffect, useMemo, useReducer, useState } from 'react';
import { Button, Col, message, Modal, Row, Tag, Tooltip } from 'antd';
import { ColumnType } from 'antd/es/table';

import { SearchInput } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import BatchActions from '@/components/business/ui/BatchActions';
import FieldTemplateModal from '@/components/business/ui/FieldsManager/components/FieldTemplate/FieldTemplateModal';
import { FieldTemplateBatchDelConfig } from '@/constants/deleteUrl';
import { useRightsHook } from '@/hooks';
import useDeleteConfirm from '@/hooks/useDeleteConfirm';
import { CommonApi } from '@/services';

import PageBreadcrumb from '../../components/PageBreadcrumb';
import { commonColumns, FieldTemplateColumns } from '../../constants/tableConfig';
import FieldTemplateApi from '../../services/FieldTemplateApi';
import type { FieldTemplateItem } from '../../types/fieldTemplate';

interface ReducerState {
  open: boolean;
  operateType: 'add' | 'view' | 'edit';
  fieldRecord: FieldTemplateItem;
}

const operateTitle = {
  add: '新建',
  edit: '编辑',
  view: '查看',
};

function reducer(state: ReducerState, action) {
  switch (action.type) {
  case 'add': {
    return {
      fieldRecord: null,
      operateType: 'add',
      open: true,
    };
  }
  case 'edit': {
    return {
      fieldRecord: action.fieldRecord,
      operateType: 'edit',
      open: true,
    };
  }
  case 'clone': {
    return {
      fieldRecord: {
        ...action.fieldRecord,
        name: `${action.fieldRecord.name}_copy`,
      },
      operateType: 'add',
      open: true,
    };
  }
  case 'view': {
    return {
      fieldRecord: {
        ...action.fieldRecord,
        columns: (action.fieldRecord.columns || []).map(item => ({
          ...item,
          colAction: 'default', // 禁用状态
        })),
      },
      operateType: 'view',
      open: true,
    };
  }
  case 'close': {
    return {
      ...state,
      fieldRecord: null,
      open: false,
    };
  }
  }
  throw Error(`Unknown action: ${action.type}`);
}

export const FieldTemplate: React.FC = () => {
  const { hasRights } = useRightsHook();
  const [list, setList] = useState<FieldTemplateItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [state, dispatch] = useReducer(reducer, { open: false, operateType: 'add', fieldRecord: null });
  const deleteConfirm = useDeleteConfirm(FieldTemplateBatchDelConfig);
  const [platformOptions, setPlatformOptions] = useState([]);
  const {
    selectedRows,
    setSelectedRows,
    pagination,
    setPagination,
    queryParams,
    filter,
    sort,
    setFilter,
    setSelectedRowKeys,
    selectedRowKeys,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    cacheId: 'fieldTemplateList',
  });
  const disabledDelete = useMemo(() => {
    return selectedRows.some(item => item.isBuiltIn);
  }, [selectedRows]);

  useEffect(() => {
    fetchPlatform();
  }, []);

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  const fetchData = async () => {
    setLoading(true);
    const { data, total } = await FieldTemplateApi.getTemplateList({
      ...(queryParams as any),
      sort: { updateTime: 'DESC' },
    });
    setList(data);
    setPagination({
      ...pagination,
      total,
    });
    setLoading(false);
  };

  const fetchPlatform = async () => {
    const { data } = await CommonApi.getEnumByCode2('ColumnTemplatePlatformEnum');
    setPlatformOptions(data);
  };

  const handleAdd = () => {
    dispatch({ type: 'add' });
  };

  const handleView = record => {
    dispatch({ type: 'view', fieldRecord: record });
  };

  const handleEdit = (record: FieldTemplateItem) => {
    dispatch({ type: 'edit', fieldRecord: record });
  };

  const handleClone = (record: FieldTemplateItem) => {
    dispatch({ type: 'clone', fieldRecord: record });
  };

  const handleDelete = (record: FieldTemplateItem) => {
    Modal.confirm({
      title: '删除确认',
      content: `确认删除字段模板「${record.name}」?`,
      async onOk() {
        await FieldTemplateApi.deleteTemplateItem(record.id);
        fetchData();
        setSelectedRowKeys([]);
        setSelectedRows([]);
      },
    });
  };

  const handleBatchDelete = async () => {
    await deleteConfirm(selectedRows ?? [], '', {
      confirmContent: `确认删除勾选对象(共计${selectedRows?.length}个)吗？`,
    });
    setSelectedRowKeys([]);
    setSelectedRows([]);
    fetchData();
  };

  const handleSave = async (value: FieldTemplateItem) => {
    if (state.operateType === 'view') return;
    if (state.operateType === 'edit') {
      await updateItem(value, state.fieldRecord?.id);
      return;
    }
    await addItem(value);
  };

  const addItem = async (params: FieldTemplateItem) => {
    await FieldTemplateApi.createTemplateItem(params);
    message.success('创建成功');
    fetchData();
  };

  const updateItem = async (params: FieldTemplateItem, id?: string) => {
    await FieldTemplateApi.updateTemplateItem({
      ...state.fieldRecord,
      ...params,
      id,
    });
    message.success('编辑成功');
    fetchData();
  };

  const handleClose = () => {
    dispatch({ type: 'close' });
  };

  const operator = {
    title: '操作',
    dataIndex: 'operator',
    fixed: 'right',
    width: 130,
    render: (_: string, record: FieldTemplateItem) => (
      <>
        <Button
          className='mr-[6px] p-0'
          type='link'
          size='small'
          disabled={!hasRights('column_template:write')}
          onClick={() => handleClone(record)}
        >
          克隆
        </Button>
        <Button
          className='mr-[6px] p-0'
          type='link'
          size='small'
          disabled={!hasRights('column_template:write', record.projectAuth) || record.isBuiltIn}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>
        <Button
          className='mr-[6px] p-0'
          type='link'
          size='small'
          disabled={!hasRights('column_template:write', record.projectAuth) || record.isBuiltIn}
          onClick={() => handleDelete(record)}
        >
          删除
        </Button>
      </>
    ),
  };
  const templateNameColumn = {
    title: '模板名',
    dataIndex: 'name',
    width: 200,
    render(name, record) {
      return (
        <div className='flex'>
          {record.isBuiltIn && <Tag>系统默认</Tag>}
          <div className='flex-1 truncate'>
            <Tooltip placement='topLeft' title={name}>
              <Button className='mr-[6px] p-0 w-full' type='link' size='small' onClick={() => handleView(record)}>
                <span className='w-full truncate text-left'>{name}</span>
              </Button>
            </Tooltip>
          </div>
        </div>
      );
    },
  };
  const platformColumn = {
    title: '适用数据库',
    dataIndex: 'platform',
    width: 150,
    renderText: value => {
      return value
        .map(val => {
          const item = platformOptions.find(opt => opt.code === val);
          return item ? item.message : val;
        })
        .join('，');
    },
  };
  const columns: Array<ColumnType<FieldTemplateItem>> = [
    templateNameColumn,
    ...FieldTemplateColumns,
    platformColumn,
    ...commonColumns.map(item => ({
      ...item,
      width: 100,
    })),
    operator,
  ];

  return (
    <>
      <div className='w-full h-full flex flex-col'>
        <PageBreadcrumb title='字段模板' desc='' />

        <div className='m-[8px] rounded-[2px] flex-1 flex flex-col overflow-hidden' style={{ background: '#fff' }}>
          <header className='flex items-center pt-4 pb-3 px-4'>
            <Row gutter={[8, 8]}>
              <Col>
                <Button type='primary' disabled={!hasRights('column_template:write')} onClick={handleAdd}>
                  新建字段模板
                </Button>
              </Col>
              <Col>
                <BatchActions
                  type='columnTemplate'
                  ids={selectedRowKeys}
                  disabled={disabledDelete}
                  condition={{ filter, sort }}
                  onDeleteItems={handleBatchDelete}
                />
              </Col>
              <Col>
                <SearchInput
                  placeholder='请输入搜索关键字'
                  allowClear
                  onSearch={val => setFilter({ ...filter, name: val })}
                />
              </Col>
            </Row>
          </header>
          <CustomTable
            columns={columns}
            dataSource={list}
            loading={loading}
            cacheId='fieldTemplateList'
            resizable
            scroll={{ x: 1200 }}
            rowSelection={{ selectedRowKeys }}
            pagination={pagination}
            onChange={handleTableChange}
            onRowSelectionChange={onRowSelectionChange}
          />
        </div>
      </div>

      {state.open && (
        <FieldTemplateModal
          disabled={state.operateType === 'view'}
          title={`${operateTitle[state.operateType]}字段模板`}
          open={state.open}
          onClose={handleClose}
          onSave={handleSave}
          templateItem={state.fieldRecord}
        />
      )}
    </>
  );
};
