import { Button } from 'antd';
import { useLocation, useOutletContext } from 'umi';

import { useRightsHook } from '@/hooks';

import SearchTable from '../../components/SearchTable';
import { measureColumns } from '../../constants/tableConfig';
import { EditType } from '../../types/enum';

interface MeasureListProps {
  onEdit?: (record: MeasureListItem) => void;
  onLook?: (record: MeasureListItem) => void;
  onDelete?: (record: MeasureListItem) => void;
  onAdd?: (type: EditType) => void;
  treeData: TreeNode[];
  selectedNode: TreeNode;
}

export const MeasureList: React.FC<MeasureListProps> = () => {
  const { onEdit, onLook, onDelete, onAdd, treeData, selectedNode } = useOutletContext<MeasureListProps>();
  const { hasRights } = useRightsHook();
  const location = useLocation();
  const projectAuth = location.state?.projectAuth;

  const handleLook = (record: MeasureListItem) => () => {
    onLook?.(record);
  };

  const handleEdit = (record: MeasureListItem) => () => {
    onEdit?.(record);
  };

  const handleDelete = (record: MeasureListItem) => () => {
    onDelete?.(record);
  };

  const handleAdd = () => {
    onAdd?.(EditType.CreateItem);
  };

  const getItemList = () => {
    return (
      treeData
        .filter(d => d.id === selectedNode.id)[0]
        ?.children?.map(d => ({
          ...d,
          children: null,
        })) ?? []
    );
  };

  const operator = {
    title: '操作',
    dataIndex: 'operator',
    fixed: 'right',
    width: 130,
    render: (_: string, record: MeasureListItem) => (
      <>
        <Button className='mr-[6px] p-0' type='link' size='small' onClick={handleLook(record)}>
          查看
        </Button>
        <Button
          className='mr-[6px] p-0'
          type='link'
          size='small'
          disabled={!hasRights('measure_unit:write', record.projectAuth)}
          onClick={handleEdit(record)}
        >
          编辑
        </Button>
        <Button
          className='p-0'
          type='link'
          size='small'
          disabled={!hasRights('measure_unit:write', record.projectAuth)}
          onClick={handleDelete(record)}
        >
          删除
        </Button>
      </>
    ),
  };

  const columns = [...measureColumns, operator];

  return (
    <SearchTable
      columns={columns}
      disabled={!hasRights('measure_unit:write', projectAuth)}
      dataSource={getItemList()}
      onAdd={handleAdd}
      buttonText='创建度量单位'
    />
  );
};
