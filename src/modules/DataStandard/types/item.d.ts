type Rule = import('antd/es/form').Rule;

type ColType = 'TIMESTAMP' | 'BIGINT' | 'DOUBLE' | 'DECIMAL' | 'STRING' | 'DATETIME' | 'DATE' | 'INT' | 'BOOLEAN';
interface ColumnItem {
  id: string;
  groupId: string;
  no: string;
  code: string;
  name: string;
  nameEn: string;
  colType: ColType;
  colLength: number;
  numPrecision: number | null;
  defaultValue: string;
  bizDef: string;
  enumId: string;
}

interface CommonItem {
  createUserName: string;
  createTime: string;
  updateUserName: string;
  updateTime: string;
  nodeType: NodeType;
}

interface EnumValue {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  enumValue: string;
}

interface EnumItem {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  folderId: string;
  no: string;
  enumValues: EnumValue[];
}

interface MeasureItem {
  id: string;
  name: string;
  nameEn: string;
  code: string;
  catalog: string;
}

interface NameItem {
  id: string;
  name: string;
  nameEn: string;
  code: string;
}

interface EditColumn {
  name: string;
  label: string;
  width?: number | string;
  rules?: Rule[];
  help?: string;
  required?: boolean;
}

interface FieldListItem extends ColumnItem, CommonItem {
  projectAuth?: ProjectAuthModel;
}
interface EnumListItem extends EnumItem, CommonItem {
  projectAuth: ProjectAuthModel;
}
interface MeasureListItem extends MeasureItem, CommonItem {
  projectAuth?: ProjectAuthModel;
}
interface NameListItem extends NameItem, CommonItem {
  projectAuth?: ProjectAuthModel;
}

interface ReqPageParams {
  filter?: {
    code?: string;
    name?: string;
    nameEn?: string;
  };
  sort?: {
    createTime?: string;
    updateTime?: string;
  };
  page: number;
  size: number;
}

type ResTreeNode = Response<TreeNode[]>;
