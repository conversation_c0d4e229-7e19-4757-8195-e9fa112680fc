declare namespace MODEL_REPOSITORY {
  interface Item {
    id: string;
    name: string;
    description: string;
    platform: string;
    columns: ColumnsItem[];
    createTime: string;
    createUserName: null;
    updateTime: string;
    updateUserName: null;
  }

  interface ColumnsItem {
    id: string;
    modelId: number;
    colType: string;
    colName: string;
    colDisplay: string;
    description: string;
    isPrimaryKey: boolean;
    isNotNull: boolean;
    colCatalog: string;
    unitId: string;
    unitName: string;
    createTime: string;
    createUserName: string;
    updateTime: string;
    updateUserName: string;
  }
}
