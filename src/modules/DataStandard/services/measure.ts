import Request from '@/request';

const baseUrl = '/api/v2';
const url = '/api/v2/data/modeling/data-standard';

const MeasureApi = {
  /**
   * 创建单位
   */
  async createItem(data: Omit<MeasureItem, 'id'>): Promise<Response<MeasureListItem>> {
    return await Request.post(`${url}/measure`, { data });
  },
  /**
   * 更新单位
   * @param data
   * @returns
   */
  async updateItem(data: MeasureItem): Promise<Response<MeasureListItem>> {
    return await Request.put(`${url}/measure/${data.id}`, { data });
  },
  /**
   * 获取单位详情
   * @param id 单位id
   * @returns
   */
  async getItemDetail(id: string): Promise<Response<MeasureListItem>> {
    return await Request.get(`${url}/measure/${id}`);
  },
  /**
   * 获取单位列表
   * @returns
   */
  async getMeasureList(): Promise<Response<MeasureListItem[]>> {
    return await Request.get(`${url}/measure/list`);
  },
  /**
   * 获取根目录所有节点
   * @returns
   */
  async getMeasureUnitCatalog(): Promise<Response<EnumCodeItemModel[]>> {
    return await Request.get(`${baseUrl}/enum-code/list-by-code/MeasureUnitCatalog`);
  },
};

export default MeasureApi;
