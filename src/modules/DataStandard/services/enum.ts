import Request from '@/request';

const url = '/api/v2/data/modeling/data-standard';

const EnumApi = {
  /**
   * 获取根目录下所有节点
   * @returns
   */
  async getNodesFromRoot(): Promise<ResTreeNode> {
    return await Request.get(`${url}/tree/enum/root`);
  },
  /**
   * 创建目录
   */
  async createFolder(data: Omit<Folder, 'id'>) {
    return await Request.post(`${url}/tree/enum`, { data });
  },
  /**
   * 更新目录
   * @param data
   * @returns
   */
  async updateFolder(data: Folder) {
    return await Request.put(`${url}/tree/${data.id}`, { data });
  },
  /**
   * 创建数据字典
   */
  async createItem(data: any) {
    return await Request.post(`${url}/enum`, { data });
  },
  /**
   * 更新数据字典
   * @param data
   * @returns
   */
  async updateItem(data: any) {
    return await Request.put(`${url}/enum/${data.id}`, { data });
  },
  /**
   * 获取数据字典详情
   * @param id 标准id
   * @returns
   */
  async getItemDetail(id: string) {
    return await Request.get(`${url}/enum/${id}`);
  },
  /**
   * 获取数据字典列表
   * @param id 目录id
   * @returns
   */
  async getEnumList(id?: string) {
    return await Request.get(`${url}/enum/list`, {
      params: {
        folderId: id,
      },
    });
  },
};

export default EnumApi;
