import { useEffect, useState } from 'react';
import { Button, Descriptions, Tag } from 'antd';
import { useParams } from 'umi';

import { CATALOGS } from '@/constants';
import { useGlobalHook, useRightsHook } from '@/hooks';
import { WarehouseLayerApi } from '@/services';

import { WarehouseLayerEditModal } from '../components';
import { useWarehouseLayerStore } from '../store';
import { fieldsLabel, getLabel } from '../utils';

import CheckRuleManage from './CheckRuleManage';

export const WarehouseLayerDetailPage = function () {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { hasRights } = useRightsHook();
  const store = useWarehouseLayerStore();
  const { openEditLayer, hideEditLayer, open, item } = store;
  const { id } = useParams();
  const [detail, setDetail] = useState<WAREHOUSE_LAYER.WarehouseLayerEntity>({});

  const onCallback = () => {
    hideEditLayer();
    fetchData();
  };

  const fetchData = () => {
    WarehouseLayerApi.getDetail(id as string)
      .then(({ data }) => {
        setDetail(data);
      })
      .catch(e => {});
  };

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  useEffect(() => {
    setPageInfo({
      title: detail.name,
    });
    return () => {
      resetPageInfo();
    };
  }, [detail.name]);
  return (
    <div className='h-full bg-white pl-4 pr-4 overflow-y-auto'>
      <div className='custom-header mb-2 mt-2 flex justify-between'>
        <span>基本信息</span>
        {detail.isBuiltIn ? (
          <Tag className='absolute top-2 right-0'>系统默认</Tag>
        ) : (
          <Button
            className='custom-header-right'
            disabled={!hasRights('warehouse_layer:write', detail?.projectAuth)}
            onClick={() => openEditLayer(detail)}
          >
            编辑
          </Button>
        )}
      </div>
      <Descriptions labelStyle={{ color: '#999' }} contentStyle={{ color: 'rgba(0, 0, 0, .65)' }} column={2}>
        <Descriptions.Item label={fieldsLabel.code}>{detail.code}</Descriptions.Item>
        <Descriptions.Item label={fieldsLabel.nameEn}>{detail.nameEn}</Descriptions.Item>
        <Descriptions.Item label={fieldsLabel.name}>{detail.name}</Descriptions.Item>
        <Descriptions.Item label={fieldsLabel.catalog}>{getLabel(CATALOGS, detail.catalog)}</Descriptions.Item>
        <Descriptions.Item label={fieldsLabel.tbType}>
          {getLabel(CATALOGS.find(x => x.value === detail.catalog)?.children ?? [], detail.tbType)}
        </Descriptions.Item>
        <Descriptions.Item label={fieldsLabel.updateUserName}>{detail.updateUserName ?? '-'}</Descriptions.Item>
        <Descriptions.Item label={fieldsLabel.updateTime}>{detail.updateTime}</Descriptions.Item>
        <Descriptions.Item label={fieldsLabel.description}>{detail.description}</Descriptions.Item>
      </Descriptions>

      {open && <WarehouseLayerEditModal item={item} open={open} oncancel={hideEditLayer} callback={onCallback} />}
      {/* 检查器管理 */}
      {detail.id && detail.tbType !== 'DIMENSION' && <CheckRuleManage layer={detail} />}
    </div>
  );
};
