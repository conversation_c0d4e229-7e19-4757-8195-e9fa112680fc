import { useEffect, useState } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Button, Col, Dropdown, message, Modal, Row, Select, Space, Spin } from 'antd';
import { history, Link } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { useGlobalHook } from '@/hooks';
import { flinkYarnSessionBaseURl } from '@/routes/flink-yarn-session';

import { BatchActionModal, DetailModal, StatusComponent, ViewWorkModal } from '../components';
import { FlinkStatus } from '../constants';
import type { FlinkListItemModel } from '../model';
import { FlinkApi } from '../services/FlinkApi';

export const FlinkYarnSessionList = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();

  const [loading, setLoading] = useState(false);
  const { pagination, setPagination, queryParams, handleTableChange, setFilter, filter } = useCustomTableHook({
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    cacheId: 'flinkList',
  });

  const [list, setList] = useState<FlinkListItemModel[]>([]);
  const [batchActionOpen, setBatchActionOpen] = useState(false);
  const [detailOpen, setDetailOpen] = useState(false);
  const [workOpen, setWorkOpen] = useState(false);
  const [batchActionType, setBatchActionType] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [item, setItem] = useState<FlinkListItemModel>();

  const columns: Array<ColumnType<FlinkListItemModel>> = [
    {
      title: '会话名称',
      dataIndex: 'yarnSessionAlias',
      key: 'yarnSessionAlias',
      fixed: 'left',
      width: 150,
      ellipsis: true,
      render: (yarnSessionAlias, record) => <a onClick={() => viewDetail(record)}>{yarnSessionAlias}</a>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status, record) => {
        return (
          <StatusComponent
            isCheck={true}
            flinkStatus={status}
            id={record.id}
            onChangeStatus={newStatus =>
              setList(list => {
                return list.map(item => {
                  if (item.id === record.id) {
                    item.status = newStatus;
                  }
                  return item;
                });
              })
            }
          />
        );
      },
    },
    {
      title: '集群',
      dataIndex: 'clusterName',
      key: 'clusterName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '已占用内存(MB)',
      dataIndex: 'totalYarnMemory',
      key: 'totalYarnMemory',
      width: 150,
      ellipsis: true,
    },
    {
      title: '最大SLOT数',
      dataIndex: 'maxSlots',
      key: 'maxSlots',
      width: 100,
      ellipsis: true,
    },
    {
      title: '已使用SLOT数',
      dataIndex: 'usedSlots',
      key: 'usedSlots',
      width: 110,
      ellipsis: true,
    },
    {
      title: '运行中作业数',
      dataIndex: 'runningJob',
      key: 'runningJob',
      width: 100,
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 160,
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 280,
      render(record: FlinkListItemModel) {
        const { id, status, yarnSessionAlias } = record;
        return (
          <>
            {[
              'WAITING_START',
              'STARTING',
              'WAITING_STOP',
              'STOPPING',
              'WAITING_DELETE',
              'DELETING',
              'RESTARTING',
              'RUNNING',
            ].includes(status) ? (
                <span className='px-1 text-gray-4'>启动</span>
              ) : (
                <a className='px-1' onClick={() => startFlink([id], yarnSessionAlias)}>
                  {record.starting && <Spin size='small' className='px-1' />}
                启动
                </a>
              )}
            {[
              'WAITING_START',
              'STARTING',
              'WAITING_STOP',
              'STOPPING',
              'WAITING_DELETE',
              'DELETING',
              'RESTARTING',
              'STOPPED',
              'FINISHED',
            ].includes(status) ? (
                <span className='px-1 text-gray-4'>停止</span>
              ) : (
                <a className='px-1' onClick={() => stopFlink([id], yarnSessionAlias)}>
                  {record.stopping && <Spin size='small' className='px-1' />}
                停止
                </a>
              )}

            <Dropdown menu={{ items: getDropdownItems(record) }} placement='bottom'>
              <Link to={`${flinkYarnSessionBaseURl}/diagnose/${id}`} className='px-1'>
                诊断
                <DownOutlined className='text-xs ml-1' onClick={e => e.preventDefault()} />
              </Link>
            </Dropdown>
            <a className='px-1' onClick={() => viewWork(record)}>
              查看作业
            </a>
            <a className='px-1' onClick={() => editWork(record)}>
              编辑
            </a>
            <a className='px-1' onClick={() => deleteWork(record)}>
              删除
            </a>
          </>
        );
      },
    },
  ];

  const getDropdownItems = record => {
    const { trackUrl, id } = record;
    const items = [
      { key: 'running-log', label: '运行日志' },
      { key: 'operation-log', label: '操作日志' },
    ];

    return [
      ...items.map(({ key, label }) => {
        return {
          key,
          label: <Link to={`${flinkYarnSessionBaseURl}/diagnose/${id}?active=${key}`}>{label}</Link>,
        };
      }),
      {
        label: (
          <a href={trackUrl} className='mr-4' target='_blank' rel='noreferrer'>
            追踪
          </a>
        ),
        key: 'check-status',
        disabled: !trackUrl,
      },
    ];
  };

  const startFlink = (ids: string[], name: string) => {
    Modal.confirm({
      title: `是否确认启动「${name}」？`,
      onOk: () => {
        updateStatus('start', ids);
      },
    });
  };

  const stopFlink = (ids: string[], name: string) => {
    Modal.confirm({
      title: `是否确认停止「${name}」？`,
      onOk: () => {
        updateStatus('stop', ids);
      },
    });
  };

  const updateStatus = async (action: string, ids: string[], isForceStop = false) => {
    const body = {
      forceStop: false,
    };
    if (action === 'stop') body.forceStop = isForceStop;

    setList(list => {
      return list.map(item => {
        if (ids.includes(item.id)) {
          if (action === 'start') {
            item.starting = true;
          } else {
            item.stopping = true;
          }
          return item;
        }
        return item;
      });
    });

    const results = await Promise.allSettled(
      ids.map(x =>
        FlinkApi[action](x, body, {
          headers: {
            'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
            'Jax-Super-Project-Admin': false,
          },
        }),
      ),
    ).finally(() => {
      setList(list => {
        return list.map(item => {
          if (ids.includes(item.id)) {
            if (action === 'start') {
              item.starting = false;
            } else {
              item.stopping = false;
            }
            return item;
          }
          return item;
        });
      });
    });
    if (results.length === 1) {
      if (results[0].status === 'fulfilled') {
        message.success('操作成功，请稍候查看结果');
      } else {
        message.error(results[0].reason.msg);
      }
    } else {
      const msg = results.some(x => x.status === 'rejected') ? message.warning : message.success;
      msg(
        `操作成功，成功${results.filter(x => x.status === 'fulfilled').length}个，
        失败${results.filter(x => x.status === 'rejected').length}个，请稍候查看结果`,
      );
    }
    fetchData();
  };

  const viewDetail = (record: FlinkListItemModel) => {
    setItem(record);
    setDetailOpen(true);
  };

  const viewWork = (record: FlinkListItemModel) => {
    setItem(record);
    setWorkOpen(true);
  };

  const editWork = (record: FlinkListItemModel) => {
    history.push(`${flinkYarnSessionBaseURl}/edit/${record.id}`);
  };

  const deleteWork = (record: FlinkListItemModel) => {
    Modal.confirm({
      title: '删除确认',
      content: `确认删除Flink会话「${record.yarnSessionAlias}」?`,
      async onOk() {
        await FlinkApi.delete(record.id, {
          headers: {
            'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
            'Jax-Super-Project-Admin': false,
          },
        });
        fetchData();
      },
    });
  };

  const fetchData = () => {
    setLoading(true);
    FlinkApi.getList(queryParams, {
      headers: {
        'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
        'Jax-Super-Project-Admin': false,
      },
    })
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false))
      .catch(() => {});
  };

  useEffect(() => {
    setPageInfo({
      title: 'Flink会话管理',
      description:
        'Flink Session集群，该集群可以接受多个作业提交。可以节省申请资源和启动TaskManager的时间，并且集群上运行的作业会共享资源。',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='bg-white h-full flex flex-col'>
      <div className='px-3 py-3 flex justify-between'>
        <Row gutter={[8, 8]}>
          <Col>
            <Button type='primary' onClick={() => history.push(`${flinkYarnSessionBaseURl}/create`)}>
              创建Flink会话
            </Button>
          </Col>
          <Col>
            <Dropdown
              disabled={!selectedRowKeys.length}
              menu={{
                items: [
                  { label: '启动', key: 'start' },
                  { label: '停止', key: 'stop' },
                ],
                onClick: ({ key }) => {
                  setBatchActionOpen(true);
                  setBatchActionType(key);
                },
              }}
            >
              <Button>
                <Space>
                  批量操作
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          </Col>
          <Col>
            <SearchInput
              placeholder='搜索会话名称'
              defaultValue={filter.search}
              onSearch={val => setFilter({ ...filter, search: val })}
            />
          </Col>
          <Col>
            <Select
              className='min-w-[240px] tag-select-show-status'
              placeholder='请选择状态'
              mode='multiple'
              allowClear
              onChange={val => setFilter({ ...filter, status: val })}
            >
              {Object.entries(FlinkStatus).map(([value, label]) => {
                return (
                  <Select.Option key={value} value={value}>
                    <span className={`flink-yarn-session-status ${value} align-middle`}>{label}</span>
                  </Select.Option>
                );
              })}
            </Select>
          </Col>
        </Row>
        <div className='flex items-center'>
          <Button
            type='text'
            onClick={() => {
              fetchData();
            }}
          >
            <i className='iconfont icon-refresh-line'></i>
          </Button>
        </div>
      </div>
      <CustomTable
        key='id'
        loading={loading}
        scroll={{ x: '100%' }}
        dataSource={list}
        columns={columns}
        cacheId='flinkList'
        resizable
        pagination={pagination}
        onChange={handleTableChange}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys,
          onChange: (rowKeys: string[]) => {
            setSelectedRowKeys(rowKeys);
          },
        }}
      />
      {batchActionOpen && (
        <BatchActionModal
          open={batchActionOpen}
          type={batchActionType}
          selectedList={list.filter(x => selectedRowKeys.includes(x.id))}
          onCancel={() => {
            setBatchActionOpen(false);
            setBatchActionType('');
          }}
          onOk={ids => {
            updateStatus(batchActionType, ids);
            setBatchActionOpen(false);
            setSelectedRowKeys([]);
          }}
        />
      )}
      {detailOpen && <DetailModal open={detailOpen} detail={item!} onCancel={() => setDetailOpen(false)} />}
      {workOpen && <ViewWorkModal open={workOpen} id={item!.id} onCancel={() => setWorkOpen(false)} />}
    </div>
  );
};
