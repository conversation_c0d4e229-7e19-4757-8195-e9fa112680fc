export interface FlinkListItemModel {
  canceledJob: number;
  clusterId: string;
  clusterName: string;
  confList: string | string[];
  createTime: string;
  createUserName: string;
  description: string;
  failedJob: number;
  finishedJob: number;
  id: string;
  jobmanagerMemory: number;
  maxMemory: number;
  maxSlots: number;
  optsId: string;
  optsName: string;
  processTime: string;
  processing: string;
  runningJob: number;
  slots: number;
  status: string;
  taskmanagerMemory: number;
  totalSlots: number;
  totalYarnMemory: number;
  trackUrl: string;
  updateTime: string;
  updateUserName: string;
  usedSlots: number;
  yarnAppId: string;
  yarnQueue: string;
  yarnSessionAlias: string;
  yarnSessionName: string;
  starting?: boolean;
  stopping?: boolean;
}

export interface FlinkCreateModel {
  id?: string;
  jobManagerMemory: number;
  taskManagerMemory: number;
  slots: number;
  maxMemory: number;
  yarnSessionName: string;
  yarnSessionAlias: string;
  yarnQueue: string;
  clusterId: string;
  optsId: string;
  description?: string;
  confList?: string[];
}

export interface FlinkConfigListItemModel {
  description?: string;
  default: string;
  key: string;
}
