.list {
  li {
    display: flex;
    border-bottom: 1px solid rgb(0 0 0 / 8%);

    &:last-child {
      border-bottom: none;
    }

    label {
      padding: 8px 12px;
      width: 40%;
      background: rgb(111 111 111 / 8%);
    }

    p {
      flex: 1;
      padding: 8px 12px;
      word-break: break-all;
      display: flex;
      justify-content: right;
    }

    .child-list {
      label {
        padding: 8px 12px;
        width: 40%;
        background: none;
      }
    }
  }
}