import { useEffect, useState } from 'react';
import { Button, Modal, Select, Tag, Tooltip } from 'antd';
import { Link } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { PipelineStatusComponent } from '@/modules/PipelineDevOps/components/PipelineStatusComponent';
import { PipelineStatus } from '@/modules/PipelineDevOps/constant';
import { PipelineApi } from '@/modules/PipelineDevOps/services/PipelineApi';

import './index.less';

type ListItemProps = PipelineModel & {
  loading: boolean;
  starting: boolean;
  stopping: boolean;
};

interface Props {
  open: boolean;
  id: string;
  onCancel?: () => void;
}

export const ViewWorkModal = (props: Props) => {
  const { open, id, onCancel } = props;

  const { pagination, filter, queryParams, setPagination, handleTableChange, setFilter } = useCustomTableHook({
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    cacheId: 'work-modal',
  });

  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<ListItemProps[]>([]);

  const columns: Array<ColumnType<ListItemProps>> = [
    {
      title: '任务名称',
      dataIndex: 'pipelineAlias',
      key: 'pipelineAlias',
      width: 150,
      render(pipelineAlias: string, record: ListItemProps) {
        const { processId, processType, pipelineType } = record;
        let url = '';
        switch (processType ?? pipelineType) {
        case 'ELASTICSEARCH':
          url = `/data-dev/dev/data-process/online/storage/es/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        case 'HIVE':
          url = `/data-dev/dev/data-process/online/hive/edit/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        case 'flink_sql':
          url = `/data-dev/dev/data-process/online/flink-sql/edit/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        case 'streaming':
          url = `/data-dev/dev/data-process/online/pipeline/edit/${processId}?title=${pipelineAlias ?? '未命名*'}`;
          break;
        }
        return (
          <Tooltip title={pipelineAlias}>
            {url && (
              <Link target='_blank' to={url}>
                {pipelineAlias ?? '未命名*'}
              </Link>
            )}
            {!url && (pipelineAlias ?? '未命名*')}
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'pipelineStatus',
      key: 'pipelineStatus',
      width: 60,
      render(pipelineStatus: keyof typeof PipelineStatus, record: ListItemProps) {
        return <PipelineStatusComponent id={record.id} pipelineStatus={pipelineStatus} noCheck={true} />;
      },
    },
    {
      title: '标签',
      dataIndex: 'tagList',
      key: 'tagList',
      width: 150,
      render: tagList => {
        return (
          <div className='flex-wrap flex'>
            {tagList?.map((name: string, index: number) => (
              <Tag className='mx-1 my-1' key={index}>
                {name}
              </Tag>
            ))}
          </div>
        );
      },
    },
    {
      title: '集群',
      dataIndex: 'clusterName',
      key: 'clusterName',
      width: 100,
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 120,
      sorter: true,
    },
  ];

  const fetchData = () => {
    setLoading(true);
    PipelineApi.getList({
      ...queryParams,
      filter: {
        ...filter,
        yarnSessionId: id,
      },
    }).then(({ data, total }) => {
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
    });
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <Modal
      className='work-modal'
      title='查看作业'
      width={1000}
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key='submit' type='primary' onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      <div className='h-full flex flex-col px-3'>
        <div className='mt-3 flex'>
          <div className='w-[240px] mr-2'>
            <SearchInput
              placeholder='搜索任务名称'
              defaultValue={filter.search}
              onSearch={val => {
                setFilter({ search: val });
              }}
            />
          </div>
          <div className='flex items-center'>
            <label>状态：</label>
            <Select
              className='w-[200px]'
              placeholder='请选择'
              mode='multiple'
              allowClear
              options={Object.entries(PipelineStatus).map(([value, label]) => ({ value, label }))}
              onChange={val => setFilter({ ...filter, pipelineStatus: val })}
            />
          </div>
        </div>
        <CustomTable
          className='h-full mt-2 border-y-0 overflow-auto'
          key='id'
          bordered
          scroll={{ x: '100%' }}
          loading={loading}
          dataSource={list}
          columns={columns}
          pagination={pagination}
          onChange={handleTableChange}
        />
      </div>
    </Modal>
  );
};
