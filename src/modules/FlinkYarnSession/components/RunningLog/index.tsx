import React, { useEffect, useState } from 'react';
import { Badge, Divider, Empty } from 'antd';
import { Link } from 'umi';

import { ClusterApi } from '@/modules/Cluster/services/ClusterApi';

import './index.less';

interface Props {
  pipelineData: PipelineModel;
}
export const RunningLog: React.FC<Props> = ({ pipelineData }) => {
  const { yarnAppId: applicationId, clusterId } = pipelineData;
  const [containers, setContainers] = useState<YarnSessionLogItem[]>([]);

  const downloadFile = (e, logUrl) => {
    e.stopPropagation();
    e.preventDefault();
    ClusterApi.downloadYarnApplicationLog({ url: logUrl as string, clusterId });
  };

  useEffect(() => {
    if (!applicationId) return;
    ClusterApi.getYarnApplicationLog(clusterId, applicationId).then(({ data }) => {
      const { amContainer, containers } = data;
      setContainers([amContainer].concat(containers));
    });
  }, []);
  return (
    <>
      {!applicationId && <Empty className='pt-10' />}
      {applicationId && (
        <div className='p-4 running-log'>
          <div className='mb-2'>
            <i className='iconfont icon-hadoop-line mr-2 text-xl'></i>
            <span className='text-base text-gray-7'>
              应用：<b>{applicationId}</b>
            </span>
          </div>
          {containers?.map((container, index) => (
            <div
              key={index}
              className='p-4 mb-3 border border-gray-2 border-solid rounded-sm flex justify-items-stretch'
            >
              <div className='flex items-center'>
                <Badge count={container.logs.length} className='log-badge'></Badge>
                <div className='ml-3'>
                  <div className='text-sm text-gray-6 mb-1'>{container.containerId}</div>
                  <div className='text-xs text-gray-5'>{container.nodeHttpAddress}</div>
                </div>
              </div>
              <Divider type='vertical' className='h-auto' />
              <div className='flex flex-wrap'>
                {container.logs.map(log => (
                  <Link
                    target='_blank'
                    to={`/pipeline/running-log?logName=${log.logName}&logUrl=${log.logUrl}&clusterId=${clusterId}`}
                    key={log.logName}
                    className='logs-item flex items-center'
                  >
                    <i className='iconfont icon-article-line text-xs mr-1'></i>
                    <span className='flex-1 line-clamp-1'>{log.logName}</span>
                    <i className='iconfont icon-save_alt-line text-xs' onClick={e => downloadFile(e, log.logUrl)}></i>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  );
};
