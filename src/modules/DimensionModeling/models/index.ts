import { TableType } from '@/constants';

// 维度表实体定义
export interface DimensionEntity {
  layerId: string; // 数仓分层
  bizId?: string; // 业务分类 公共层下有
  domId?: string; // 数据域 公共层下有
  martId?: string; // 集市 应用层下有
  subjId?: string; // 主题 应用层下有
  code: string; // 英文缩写
  name: string; // 中文名称
  description?: string; // 描述信息
}

// 维度建模 维度详情 数据模型
export interface DimensionDetailEntity extends DimensionEntity {
  id: string;
  layerName: string;
  bizName?: string;
  domName?: string;
  layerCatalog: string;
  martName?: string;
  subjName?: string;
  updateTime: string;
  createTime: string;
  createUserName: string;
  updateUserName: string;
  projectAuth?: ProjectAuthModel;
}

export enum TableFieldTypes {
  STRING = '字符串',
  TIMESTAMP = '日期戳',
  BIGINT = '整数',
  DOUBLE = '双精度',
  DECIMAL = '浮点数',
  DATETIME = '时间',
  DATE = '日期',
  INT = '整数',
  BOOLEAN = '布尔值',
}

export enum StorageModeTypes {
  SINGLE = '单值模型',
  MULTI = '多值模型',
}

// 维度建模 模型表 表字段单项数据结构
export interface ModelTableColumn {
  colType: keyof typeof TableFieldTypes; // 字段类型 todo 统一
  colName: string; // 字段名
  colUsage?: 'DIM' | 'IDX'; // 字段类型 'DIM'：维度表 'IDX'：值表
  colDisplay?: string; // 字段显示名
  description?: string; // 描述信息
  isPrimaryKey?: boolean; // 是否是主键
  isNotNull?: boolean; // 是否允许为空
  colCatalog?: string; // 关联 ？
  unitId?: string; // 关联单位
  dicId?: string; // 关联字典
  enumId?: string; // 关联枚举值
  indicatorType?: string; // 关联类型
  periodId?: string; // 关联 ？
  adjIds?: string[]; // 关联修饰词
  atomicId?: string; // 关联原子指标
  dervId?: string; // 关联派生指标
  dimColId?: string; // 关联？
}

// 维度建模 模型表数据结构
export interface ModelTableEntity extends DimensionEntity {
  tbType: keyof typeof TableType;
  procId: string; // 业务过程
  dimId?: string; // 维度
  storagePolicy?: string; // 存储策略
  ruleId?: string; // 表名校验规则
  tbName: string; // 表名
  tbAlias: string; // 表中文名
  lifecycle: string; // 表生命周期
  periodIds?: string[]; // 时间周期
  adjIds?: string[]; // 修饰词
  columns?: ModelTableColumn[]; // 表字段定义
  layerCatalog: string;
}

// 维度建模 模型表详情
export interface ModelTableEntityDetail extends ModelTableEntity {
  id: string;
  procName?: string;
  ruleName?: string;
  dimName?: string;
  layerName: string;
  bizName?: string;
  domName?: string;
  martName?: string;
  subjName?: string;
  updateTime: string;
  createTime: string;
  createUserName: string;
  updateUserName: string;
  adjList: DATA_INDICATOR.AdjunctEntity[];
  periodList: DATA_INDICATOR.TimePeriodEntity[];
  storageMode?: keyof typeof StorageModeTypes;
  relateDim?: boolean;
  dimTbName?: string;
  dimTbId?: string;
  idxTbName?: string;
  isOnline?: boolean;
  version?: string | number;
  projectAuth?: ProjectAuthModel;
}
