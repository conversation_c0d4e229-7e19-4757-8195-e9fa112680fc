import { useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { cloneDeep } from 'lodash';
import { useLocation } from 'umi';

import { useKeepAliveTabs } from '@/components';
import { LayerCategory, TableType } from '@/constants/enum';
import { QueryOptions, useDeleteConfirm, useQuery } from '@/hooks';
import { RELATION_OBJ_TYPE_NAME } from '@/modules/Dependencies/constants';
import { TableApi } from '@/services';
import { isTrue } from '@/utils';

import { nodeTypeToIconInfo } from '../constants';
import { dimensionService } from '../services';
import { usePanelStore } from '../stores/usePanelStore';
import { filterTreeDataNodeById, isCatalog, isTable, treeStructureToTree } from '../utils';

import { useTableTreeOfBusinessByLayerType, useTableTreeOfDomainByLayerType } from './table';

type ModelNode = DIMENSION_MODELING.ModelNode;

export function useModelTreeData(onSetExpandedKeys: (keys: TreeNodeKey[]) => void) {
  const panelStore = usePanelStore();
  const { currentCatalog, list } = panelStore;
  const layerCategory = currentCatalog;
  const currentState = useMemo(() => {
    return list.find(x => x.catalog === currentCatalog)!;
  }, [currentCatalog, list]);
  const modelFilter = currentState.modelFilter;
  const dataCategory = currentState.category;

  const isDomain = dataCategory === 'data-domain';
  const isBusiness = dataCategory === 'business-category';

  const { tableTreeOfDomain, isLoading: dLoading } = useTableTreeOfDomainByLayerType(layerCategory as LayerCategory, {
    enabled: isTrue(isDomain) && !isTrue(isBusiness),
  });
  const { tableTreeOfBusiness, isLoading: bLoading } = useTableTreeOfBusinessByLayerType(
    layerCategory as LayerCategory,
    {
      enabled: isTrue(isBusiness) && !isTrue(isDomain),
    },
  );

  let tableTree: ModelNode[] = [];
  let loading = false;
  if (isBusiness) {
    tableTree = tableTreeOfBusiness;
    loading = isTrue(bLoading);
  }
  if (isDomain) {
    tableTree = tableTreeOfDomain;
    loading = isTrue(dLoading);
  }

  const treeData = useMemo(() => {
    const result = treeStructureToTree(
      'children',
      (node, path) => {
        const iconInfo = nodeTypeToIconInfo[node.nodeType];
        const newNode = {
          title: node.name,
          key: path,
          selectable: isTable(node) || isCatalog(node.nodeType),
          data: node,
          icon: iconInfo ? (
            <i className={`iconfont icon-${iconInfo.name} text-gray-6 text-sm ${iconInfo.className}`}></i>
          ) : null,
          isLeaf: !!iconInfo?.isLeaf,
        };
        return newNode;
      },
      tableTree,
    );
    return result;
  }, [tableTree]);

  const realTreeData = useMemo(() => {
    let realTreeData = treeData;
    if (modelFilter) {
      const expandedKeys: TreeNodeKey[] = [];
      realTreeData = filterTreeDataNodeById(
        cloneDeep(treeData),
        modelFilter,
        node => !node.isLeaf && expandedKeys.push(node.key),
      );
      onSetExpandedKeys(expandedKeys);
    }
    return realTreeData;
  }, [modelFilter, treeData]);

  return {
    treeData: realTreeData,
    isLoading: loading,
    layerCategory,
  };
}

export function useGetDimension(dimensionId?: string, opt?: QueryOptions<Dimension>) {
  const { data, isLoading } = useQuery<Dimension>({
    queryKey: [dimensionService.getByIdUrl(dimensionId ?? '')],
    queryFunc: () => dimensionService.getById(dimensionId ?? ''),
    queryOptions: opt,
  });

  return { dimension: data, isLoading };
}

interface Params {
  tbType?: TableType;
  values?: any;
  id?: string;
  onSuccess: () => void;
}
export function useDeleteModel(params: Params) {
  const { tbType, values, id, onSuccess } = params;
  const isDimension = tbType === 'DIMENSION';

  const confirmDelete = useDeleteConfirm({
    prefix: '',
    title: `确定删除${RELATION_OBJ_TYPE_NAME[tbType as keyof typeof RELATION_OBJ_TYPE_NAME]}${
      isDimension ? values?.name : values?.tbAlias ?? values?.tbName ?? values?.name
    }`,
    delUrl: isDimension ? dimensionService.deleteByIdUrl() : TableApi.deleteUrl(),
    relyUrl: isDimension ? '/api/v2/data/modeling/dimension' : '/api/v2/data/modeling/table',
  });

  const reloadModelTree = useReloadModelTree();

  if (!tbType || !values || !id) {
    return () => {};
  }

  const handleDelete = () => {
    confirmDelete(id).then(() => {
      reloadModelTree();
      onSuccess();
    });
  };

  return handleDelete;
}

export function useReloadModelTree(force = false) {
  const { currentCatalog } = usePanelStore();
  const queryClient = useQueryClient();

  return () => {
    if (!force) return;
    queryClient.invalidateQueries([TableApi.getTreeByTypeUrl(currentCatalog as LayerCategory)]);
    queryClient.invalidateQueries([TableApi.getTreeByDataDomainUrl(currentCatalog as LayerCategory)]);
  };
}

export function useCloneModel(pathname: string, modelData?: ModelNode) {
  const { openTab } = useKeepAliveTabs();
  const { id, nodeType, name } = modelData ?? {};
  const isDimension = nodeType === 'DIMENSION';
  const { values = {} } = useModelDetail(isDimension, id);

  if (isDimension) {
    const name: string = values.name ?? '';
    Object.assign(values, {
      name: name.endsWith('copy') ? name : `${name}-copy`,
    });
  } else {
    const tbAlias: string = values.tbAlias ?? '';
    Object.assign(values, {
      tbAlias: tbAlias.endsWith('copy') ? tbAlias : `${tbAlias}-copy`,
    });
  }

  return () => {
    openTab(pathname, {
      refresh: true,
      state: {
        title: name?.endsWith('copy') ? name : `${name}-copy`,
        modelValues: values,
      },
    });
  };
}

export function useModelDetail(isDimension: boolean, id = '') {
  const { state } = useLocation();
  const { modelValues } = state ?? ({} as any);

  const {
    data = !id ? modelValues : undefined,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: [isDimension ? dimensionService.getByIdUrl(id) : TableApi.getDetailUrl(id)],
    queryFunc: isDimension ? () => dimensionService.getById(id) : () => TableApi.getById(id).then(({ data }) => data),
    queryOptions: {
      enabled: isTrue(id),
    },
  });

  return { values: data, refetch, isLoading };
}
