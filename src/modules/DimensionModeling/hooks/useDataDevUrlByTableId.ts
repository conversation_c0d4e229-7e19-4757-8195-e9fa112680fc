import { useParams } from 'umi';

import { DataSourceConfigMap } from '@/modules/AddressManagement/DataSource/constants';

import { useTableDeployId } from './table';

export function useDataDevUrlByTableId() {
  const { id } = useParams();
  const { tableDeployId, platform, version, actionType } = useTableDeployId(id);
  if (!tableDeployId) return undefined;
  return {
    url: `/data-dev/dev/management/${DataSourceConfigMap[platform].path}/${tableDeployId}`,
    version,
    actionType,
  };
}
