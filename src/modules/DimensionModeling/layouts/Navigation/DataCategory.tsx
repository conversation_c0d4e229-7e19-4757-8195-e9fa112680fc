import { useMemo, useRef, useState } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, TreeSelect, TreeSelectProps } from 'antd';
import cs from 'classnames';

import { useDataCategoryTreeData } from '@/modules/DimensionModeling/hooks';
import { searchInString } from '@/utils';

import './style.less';

import { DataCategoryOptions } from '../../constants';
import { usePanelStore } from '../../stores/usePanelStore';

export const DataCategorySelect = () => {
  const panelStore = usePanelStore();
  const { currentCatalog, list } = panelStore;

  const currentState = useMemo(() => {
    return list.find(x => x.catalog === currentCatalog)!;
  }, [currentCatalog, list]);

  const items = useMemo(() => {
    const { categoryList } = currentState;
    return DataCategoryOptions.filter(x => categoryList.includes(x.value))!.map(x => {
      x.key = x.value;
      x.icon = <i className={`iconfont icon-${x.iconName} text-base`}></i>;
      return x;
    });
  }, [currentState]);

  const currentItem = useMemo(() => {
    return items.find(x => x.value === currentState.category);
  }, [items, currentState.category]);

  const ref = useRef<BaseSelectRef>(null);

  const { treeData, isLoading } = useDataCategoryTreeData(currentState.category);

  const [keyword, setKeyword] = useState<string>();

  const onSelect = (_, { title }) => {
    panelStore.setModelFilter(typeof title === 'string' ? title : title.props.title);
  };

  const onBlur = () => {
    if (!keyword) {
      return;
    }
    panelStore.setModelFilter(keyword);
  };

  const onKeyDown: TreeSelectProps['onKeyDown'] = e => {
    if (e.code === 'Enter') {
      ref.current?.blur();
    }
  };

  return (
    <section className='flex items-center data-category-selectors mb-[8px]'>
      <Dropdown
        menu={{
          items,
          onClick: ({ key }) => {
            panelStore.setCategory(key);
            panelStore.setModelFilter(undefined);
          },
        }}
        trigger={['click']}
      >
        <div
          className='flex items-center cursor-pointer h-8 w-[61px] bg-white justify-center'
          style={{
            border: '1px solid var(--color-dark-15)',
            borderRight: 'none',
            borderTopLeftRadius: 2,
            borderBottomLeftRadius: 2,
          }}
        >
          <div className='mr-1'>
            <i className={`iconfont icon-${currentItem?.iconName}`}></i>
          </div>
          <DownOutlined className='ml-2 text-sm text-dark-25' />
        </div>
      </Dropdown>

      <TreeSelect
        ref={ref}
        onKeyDown={onKeyDown}
        showSearch
        allowClear
        placeholder='请选择 或 输入关键字搜索'
        className='flex-1 min-w-[158px]'
        popupClassName={cs({
          'data-category-selector': currentState.category === 'data-domain',
        })}
        treeData={treeData}
        loading={isLoading && !!currentState.category}
        filterTreeNode={(inputValue, node) => {
          let title = node.title as any;
          if (typeof title !== 'string') {
            title = title.props.title;
          }
          const tokens = searchInString(title, inputValue);
          const tokensForEn = searchInString(node.nameEn, inputValue);
          return !!tokens?.length || !!tokensForEn?.length;
        }}
        value={currentState.modelFilter}
        onSearch={setKeyword}
        searchValue={keyword}
        onBlur={onBlur}
        onFocus={() => setKeyword(currentState.modelFilter)}
        onClear={() => {
          panelStore.setModelFilter(undefined);
        }}
        onSelect={onSelect}
      />
    </section>
  );
};
