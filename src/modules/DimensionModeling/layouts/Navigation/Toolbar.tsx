import { Dropdown } from 'antd';
import cs from 'classnames';

import { Icon } from '@/components';
import { LayerCategory } from '@/constants';
import { useRightsHook } from '@/hooks';
import { useCreateOptions, useReloadModelTree } from '@/modules/DimensionModeling/hooks';
import { ExcelImportApi } from '@/services';

import { usePanelStore } from '../../stores/usePanelStore';

export const Toolbar = () => {
  const { currentCatalog } = usePanelStore();
  const { hasRights } = useRightsHook();
  const options = useCreateOptions(currentCatalog as LayerCategory);
  const refreshTableTree = useReloadModelTree(true);

  const iconClassName = cs('text-gray-12 cursor-pointer', 'hover:drop-shadow transition-all');

  const onExport = () => {
    ExcelImportApi.export({ type: 'table' });
  };

  return (
    <section className='flex items-center justify-between my-3'>
      <div className='text-gray-6'>维度建模</div>
      <div className='flex items-center gap-2'>
        {hasRights('data_model:write') && (
          <Dropdown menu={{ items: options, disabled: !hasRights('data_model:write') }} trigger={['click']}>
            <Icon size={24} name='add_box-line' className={iconClassName} />
          </Dropdown>
        )}
        <Icon size={24} name='save_alt-line' className={iconClassName} onClick={onExport} title='导出' />
        <Icon size={24} name='refresh-line' onClick={refreshTableTree} className={iconClassName} title='刷新' />
      </div>
    </section>
  );
};
