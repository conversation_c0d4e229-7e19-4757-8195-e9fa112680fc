import React, { useEffect, useState } from 'react';
import { Input, InputProps } from 'antd';

import { TrimInput } from '@/components/form/TrimInput';
import { RuleDefItem } from '@/constants';

interface Props extends Omit<InputProps, 'form'> {
  form: FormInstance;
  codes: Record<string, string | undefined>;
  rule?: WAREHOUSE_LAYER.LayerRuleEntity;
}

interface TableRuleObj {
  key: string;
  value: string | string[];
  label: string;
  code?: string;
}

export const TableNameInput: React.FC<Props> = ({ form, codes, rule, ...props }) => {
  const [tableRules, setTableRules] = useState<TableRuleObj[]>([]);

  // BUSINESS_CATEGORY_CODE= '业务大类英文缩写',
  // DATA_DOMAIN_CODE = '数据域英文缩写',
  // BUSINESS_PROCESS_CODE = '业务过程英文缩写',
  // STORAGE_POLICY_CODE = '存储策略英文缩写',
  // DIM_CODE = '维度定义缩写',
  // TIME_PERIOD_CODE = '统计周期',
  // CUSTOM = '自定义'

  const KEY_MAP: Record<RuleDefItem, TableRuleObj> = {
    BUSINESS_CATEGORY_CODE: {
      key: 'bizCode',
      value: '',
      label: '请选择业务分类',
    },
    DATA_DOMAIN_CODE: {
      key: 'domCode',
      value: '',
      label: '请选择数据域',
    },
    BUSINESS_PROCESS_CODE: {
      key: 'procCode',
      value: '',
      label: '请选择业务过程',
    },
    STORAGE_POLICY_CODE: {
      key: 'storagePolicy',
      value: '',
      label: '请选择存储策略',
    },
    DIM_CODE: {
      key: 'dimCode',
      value: '',
      label: '请选择维度',
    },
    TIME_PERIOD_CODE: {
      key: 'timePeriod',
      value: [],
      label: '请选择时间周期',
    },
    DATA_MART_CODE: {
      key: 'martCode',
      value: '',
      label: '请选择数据集市',
    },
    DATA_SUBJECT_CODE: {
      key: 'subjCode',
      value: '',
      label: '请选择主题域',
    },
    CUSTOM: {
      key: '',
      value: '',
      label: '自定义内容',
    },
  };

  const generateTbName = (tableRules: TableRuleObj[]) => {
    return `${rule?.tablePrefix}_${tableRules
      .map(x => {
        if (typeof x.value === 'object') {
          return x.value.join('_');
        }
        return x.value;
      })
      .join('_')}`;
  };

  const onChangeCustomValue = (val: string, i: number) => {
    setTableRules(
      tableRules.map((item, index) => {
        if (i === index) {
          item.value = val;
        }
        return {
          ...item,
        };
      }),
    );
  };

  const validate = (tableRules: any) => {
    if (!rule) return false;
    const unValidItems = tableRules.filter((x: any) => !x.value);
    if (unValidItems.length > 0) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    const isValid = validate(tableRules);
    if (isValid) {
      form.setFieldValue('tbName', generateTbName(tableRules));
      form.validateFields();
    }
  }, [tableRules]);

  useEffect(() => {
    if (rule) {
      const _tableRules = rule.ruleDef.map((key, index) => {
        const item = KEY_MAP[key as RuleDefItem];
        if (key === 'CUSTOM') {
          item.key = `custom${index}`;
          if (tableRules?.length > 0 && tableRules[index]?.key === `custom${index}`) {
            item.value = tableRules[index]?.value;
          }
        } else {
          item.value = codes[item.key] ?? '';
        }
        return {
          ...item,
          code: key,
        };
      });
      setTableRules(_tableRules);
    }
  }, [codes, rule]);

  useEffect(() => {
    if (!rule) return;
    if (rule.checkMode === 'WEAK') {
      form.setFieldValue('tbName', generateTbName(tableRules));
    }
  }, [rule]);

  return rule && rule.checkMode === 'STRICT' ? (
    <div>
      {rule.tablePrefix}
      {tableRules.map((item, index) => (
        <span key={index}>
          _
          {item.code === 'CUSTOM' && (
            <Input
              required={rule.checkMode === 'STRICT'}
              value={item.value}
              onChange={e => onChangeCustomValue(e.target.value, index)}
              className='w-[132px] inline-block'
              placeholder='自定义内容'
            />
          )}
          {item.code !== 'CUSTOM' && (item.value ?? <span className='text-danger'>{item.label}</span>)}
        </span>
      ))}
    </div>
  ) : (
    <TrimInput {...props} />
  );
};
