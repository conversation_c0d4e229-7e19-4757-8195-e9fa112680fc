import { useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { notification, Select, SelectProps } from 'antd';

import { Loading } from '@/components';
import { TimePeriodApi } from '@/services';
import { isLoading } from '@/utils';

interface Props extends SelectProps {
  onChangeCode?: (codeMap: { timePeriod: string }) => void;
}

export const TimePeriodSelector = (props: Props) => {
  const { onChangeCode, value, ...otherProps } = props;
  const { data, ...queryResult } = useQuery([TimePeriodApi.getAllUrl], TimePeriodApi.getAll.bind(TimePeriodApi));
  const timePeriods = data?.data ?? [];

  const options: SelectOptions = useMemo(() => {
    return timePeriods.map(timePeriod => {
      return {
        label: timePeriod.name,
        value: timePeriod.id,
        key: timePeriod.id,
        code: timePeriod.code,
      };
    });
  }, [timePeriods]);

  useEffect(() => {
    if (data?.code === '9999') {
      notification.error({ message: data.msg, duration: 3 });
    }
  }, [data?.code]);

  useEffect(() => {
    if (typeof props.value === 'object') {
      props.onChangeCode?.({
        timePeriod: options
          .filter(x => props.value?.includes(x.value))
          .map(x => x.code)
          ?.join('_'),
      });
    } else {
      props.onChangeCode?.({
        timePeriod: options.find(x => props.value === x.value)?.code,
      });
    }
  }, [props.value, options]);

  const loading = isLoading(queryResult);

  if (loading) {
    return <Loading />;
  }

  return (
    <Select
      showSearch
      mode='multiple'
      optionFilterProp='label'
      placeholder='请选择'
      options={options}
      value={value ?? []}
      {...otherProps}
    />
  );
};
