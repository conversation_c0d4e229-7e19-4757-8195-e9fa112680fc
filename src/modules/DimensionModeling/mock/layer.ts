import { rest } from 'msw';

import { filterByPropertyValue, uuid } from '@/utils';

interface BaseLayer {
  id: string;
  name: string;
  code: string;
  nameEn: string;
  description: string;
}

type Layer = CataLogWithTbType & BaseLayer;

interface SourceCataLog {
  catalog: 'source';
  tbType: 'source_tb';
}

interface CommonCataLog {
  catalog: 'common';
  tbType: 'detail_tb' | 'dimension_tb' | 'dimension' | 'total';
}

interface ApplyCataLog {
  catalog: 'apply';
  tbType: 'apply_tb' | 'dimension_tb' | 'dimension';
}

type CataLogWithTbType = SourceCataLog | CommonCataLog | ApplyCataLog;

export const layers: Layer[] = [
  {
    id: uuid(),
    name: '公共维度数据层',
    code: 'dimension1',
    nameEn: 'dimensionlevel1',
    description: '公共维度数据层',
    catalog: 'common',
    tbType: 'dimension',
  },
  {
    id: uuid(),
    name: '应用维度数据层2',
    code: 'dimension2',
    nameEn: 'dimensionlevel2',
    description: '应用维度数据层2',
    catalog: 'apply',
    tbType: 'dimension',
  },
];

// get layer list
const handleGetLayers = rest.get('/api/v2/data/modeling/warehouse-layer', (req, res, { json }) => {
  const { searchParams } = req.url;
  const name = searchParams.get('name');
  const code = searchParams.get('code');
  const description = searchParams.get('description');
  const nameEn = searchParams.get('nameEn');
  if (nameEn) {
    return res(json(filterByPropertyValue('nameEn', nameEn, layers)));
  }
  if (name) {
    return res(json(filterByPropertyValue('name', name, layers)));
  }
  if (description) {
    return res(json(filterByPropertyValue('description', description, layers)));
  }
  if (code) {
    return res(json(filterByPropertyValue('code', code, layers)));
  }
});

// get layer by id
const handleGetLayer = rest.get('/api/v2/data/modeling/warehouse-layer/:id', (req, res, { json }) => {
  return res(json(filterByPropertyValue('id', req.params.id, layers)));
});

const handlers = [handleGetLayers, handleGetLayer];

export default handlers;
