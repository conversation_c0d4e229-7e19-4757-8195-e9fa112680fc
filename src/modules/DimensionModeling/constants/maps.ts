import { TableType } from '@/constants/enum';

import { STORAGE_POLICY } from './options';
import { appTableKey, detailTableKey, dimensionKey, dimensionTableKey, sourceTableKey, totalTableKey } from './url';

export const STORAGE_POLICY_VALUE_TO_LABEL_MAP =
  STORAGE_POLICY?.reduce((acc: Record<string, string>, cur) => {
    acc[cur.value as string] = cur.label as string;
    return acc;
  }, {}) ?? {};

export const getTableTypeRoutePath = (
  tableType: keyof typeof TableType,
  action: 'create' | 'edit' | 'clone' = 'create',
) => {
  const type = tableType === 'DIMENSION' ? 'dimension' : 'table';
  const baseUrl = `/data-modeling/dimension-modeling/modeling/${type}`;
  switch (action) {
  case 'create':
    return `${baseUrl}/create?tbType=${tableType}`;
  case 'edit':
    return `${baseUrl}/edit`;
  case 'clone':
    return `${baseUrl}/clone`;
  }
};

export const tableTypeToRoutePath: Record<TableType, string> = {
  [TableType.ADS]: appTableKey,
  [TableType.DIM]: dimensionTableKey,
  [TableType.DIMENSION]: dimensionKey,
  [TableType.ODS]: sourceTableKey,
  [TableType.DWD]: detailTableKey,
  [TableType.DWS]: totalTableKey,
};

export const nodeTypeToIconInfo = {
  [TableType.OBJ]: {
    name: 'application-line',
    className: 'text-success-2',
    isLeaf: true,
  },
  [TableType.IDX]: {
    name: 'preview-line',
    className: 'text-success-2',
    isLeaf: true,
  },
  [TableType.DIM]: {
    name: 'dimensiwd-line',
    className: 'text-success-2',
    isLeaf: true,
  },
  [TableType.DIMENSION]: {
    name: 'dimensionwd-line',
    className: 'text-success-1',
    isLeaf: true,
  },
  [TableType.ODS]: {
    name: 'article-line',
    className: 'text-warning-1',
    isLeaf: true,
  },
  [TableType.DWD]: {
    name: 'view_list-fill',
    className: 'text-warning-1',
    isLeaf: true,
  },
  [TableType.DWS]: {
    name: 'description-line',
    className: 'transform translate-180 text-purple-1',
    isLeaf: true,
  },
  [TableType.ADS]: {
    name: 'application-line',
    className: 'text-primary-2',
    isLeaf: true,
  },
  businessCategory: {
    name: 'grid_view-line',
    className: 'text-gray-12',
    isLeaf: false,
  },
  dataDomain: {
    name: 'earth-fill',
    className: 'text-gray-12',
    isLeaf: false,
  },
  businessProcess: {
    name: 'process-line',
    className: 'text-gray-12',
    isLeaf: false,
  },
  dataMart: {
    name: 'datamart-line',
    className: 'text-gray-12',
    isLeaf: false,
  },
  dataSubject: {
    name: 'subject-line',
    className: 'text-gray-12',
    isLeaf: false,
  },
  group_DWD: null,
  group_ODS: null,
  group_DIM: null,
  group_DIMENSION: null,
  group_DWS: null,
  group_ADS: null,
};
