import { tbTypesArr } from '@/constants/array';
import { isArray, searchInString } from '@/utils';

export function treeStructureToTree<T extends Objects<any>, V>(
  childrenKey: string | null,
  convertor: (node: T, path: string) => TreeDataNodeWithData<T>,
  tree?: T | T[],
  parentPath = '',
): Array<TreeDataNodeWithData<T>> {
  if (!tree) return [];
  if (isArray(tree)) {
    return tree.map((node, index) => {
      const currentPath = parentPath ? `${parentPath}.${index}` : `${index}`;
      const newNode = convertor(node, currentPath);
      if (childrenKey) {
        newNode.children = treeStructureToTree<T, V>(childrenKey, convertor, node[childrenKey], currentPath);
      }
      return newNode;
    });
  } else {
    return treeStructureToTree(childrenKey, convertor, [tree]);
  }
}

export function filterTreeDataNodeById(
  nodes: Array<TreeDataNodeWithData<DIMENSION_MODELING.ModelNode>>,
  keyword: string,
  visitor?: (node: TreeDataNodeWithData<DIMENSION_MODELING.ModelNode>) => void,
): Array<TreeDataNodeWithData<DIMENSION_MODELING.ModelNode>> {
  return nodes.filter(node => {
    const tokens = searchInString(node.title as string, keyword);
    const tokensForEn = searchInString(node.data.code ?? '', keyword);
    const keep = !!tokens?.length || !!tokensForEn?.length;
    if (keep) {
      visitor?.(node);
    } else if (node.children) {
      node.children = filterTreeDataNodeById(node.children, keyword, visitor);
    }
    return keep || node.children?.length;
  });
}

export function isTable({ nodeType }: DIMENSION_MODELING.ModelNode): boolean {
  return nodeType ? tbTypesArr.includes(nodeType) : false;
}

export function isCatalog(nodeType: DIMENSION_MODELING.NodeType): boolean {
  return nodeType ? ['businessCategory', 'dataDomain', 'dataMart'].includes(nodeType) : false;
}
