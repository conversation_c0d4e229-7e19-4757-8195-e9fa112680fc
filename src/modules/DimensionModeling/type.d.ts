declare namespace DIMENSION_MODELING {
  // 分层分类
  type LayerCategory = import('@/constants/enum').LayerCategory;

  // 数据分类
  type DataCategory = 'business-category' | 'data-domain';
  // 数据分类选项
  interface DataCategoryOption {
    label: string;
    value: DataCategory;
    iconName: string;
  }

  type NodeType =
    | 'businessCategory'
    | 'dataMart'
    | 'dataSubject'
    | 'dataDomain'
    | 'businessProcess'
    | 'group_DWD'
    | 'group_ODS'
    | 'group_DIM'
    | 'group_DIMENSION'
    | 'group_DWS'
    | 'group_ADS'
    | import('@/constants/enum').TableType;

  interface ModelNode {
    code?: string;
    id: string;
    parentId: string;
    name: string;
    tableCount: number;
    nodeType: NodeType;
    children: ModelNode[];
    projectAuth?: ProjectAuthModel;
  }

  type TreeNode = import('antd').TreeProps<import('antd').TreeDataNode>['treeData'];
  type ModelNodeToTreeNode = (node: ModelNode) => TreeNode;

  // 时间周期
  interface Period {
    id: string;
    unit: string;
    unitName: string;
    code: string;
    name: string;
    nameEn: string;
    description: string | null;
    createTime: string;
    updateTime: string;
    createUserName: string | null;
    updateUserName: string | null;
  }

  // ODS:贴源表，DIM:维度表，DWD:明细表，DWS:汇总表，ADS:应用表，IDX:指标表
  type TableType = 'ODS' | 'DIM' | 'DWD' | 'DWS' | 'ADS' | 'CUZ' | 'IDX' | 'OBJ';
  interface ModelTable {
    id: string; // 主键id
    tbType: TableType; // 类型，ODS贴源表、DIM维度表、DWD明细表、DWS汇总表、ADS应用表
    layerId: string; // 数仓分层id
    bizId: string; // 业务分类id
    dimId: string; // 维度id
    domId: string; // 数据域id
    procId: string; // 业务过程id
    martId: string; // 数据集市id
    martName: string; // 数据集市id
    subjId: string; // 主题域id
    subjName: string; // 主题域id
    storagePolicy: string; // 存储策略
    ruleId: string; // 规则id，关系字段，检查器表的主键
    tbName: string; // 表名
    tbAlias: string; // 中文名称
    lifecycle: number; // 生命周期，单位天
    description: string; // 备注信息
    periodIds?: string[]; // 时间周期id列表
    adjList?: Adjunct[]; // 修饰词列表
    periodList?: Period[]; // 时间周期实例列表
    columns: ModelTableColumn[]; // 表列的定义
    version?: number;
    dimCount: number;
    indicatorCount: number;
  }

  interface Adjunct {
    id: string;
    code: string;
    name: string;
    nameEn: string;
    description: null | string;
    bizExpr: string;
    layerId: string;
    layerName: null | string;
    bizId: string;
    bizName: null | string;
    domId: string;
    domName: null | string;
    martId: null | string;
    martName: null | string;
    subjId: null | string;
    subjName: null | string;
    catalog: null | string;
    createTime: string;
    updateTime: string;
    createUserName: null | string;
    updateUserName: null | string;
  }

  interface ModelTableColumn {
    colType: string; // 字段类型
    colName: string; // 字段名
    colDisplay?: string; // 显示名
    description?: string; // 描述
    isPrimaryKey: boolean; // 是否主键
    isNotNull: boolean; // 是否为空
    colCatalog?: import('@/constants/enum').FieldDescription; // 字段类别 PROPERTY DIMENSION UNIT
    unitId?: string; // 度量单位
    dicId?: string; // 字段标准id
    enumId?: string; // 数据字典id
    indicatorType?: import('@/constants/enum').IndicatorType; // 关联指标类型 ATOMIC DERIV DIM
    periodId?: string; // 时间周期id
    adjIds?: string[]; // 修饰词id
    atomicId?: string; // 原子指标id
    dervId?: string; // 派生指标id
    dimTbId?: string;
    dimColId?: string;
  }

  interface ModelTableColumnWithId extends ModelTableColumn {
    id?: string;
  }

  interface State {
    layerCategory: LayerCategory;
    dataCategory: DataCategory;
    modelFilter: string; // 模型选择类型
  }

  interface Actions {
    updateState: (newState: Partial<State>) => void;
  }

  interface StateWithActions {
    state: State;
    actions: Actions;
  }

  type InsertTableColumn = (oldColumns: ModelTableColumnWithId[], rowNumber: number) => ModelTableColumnWithId[];

  type RemoveEmptyTableColumn = (oldColumns: ModelTableColumnWithId[]) => ModelTableColumnWithId[];

  type RemoveTableColumn = (oldColumns: ModelTableColumnWithId[], position: number) => ModelTableColumnWithId[];
}
