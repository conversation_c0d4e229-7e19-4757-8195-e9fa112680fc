import React, { useEffect, useRef, useState } from 'react';
import { Button, Divider, FormInstance, message, Space, Spin } from 'antd';
import { useParams, useSearchParams } from 'umi';

import { useKeepAliveTabs } from '@/components';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { useReloadModelTree } from '@/modules/DimensionModeling/hooks';
import type { DimensionDetailEntity } from '@/modules/DimensionModeling/models';
import { usePanelStore } from '@/modules/DimensionModeling/stores/usePanelStore';
import { DimensionApi } from '@/services';

import DimensionDetail from './detail';
import DimensionForm from './form';

type Mode = 'edit' | 'read';

const Dimension: React.FC = () => {
  const { hasRights } = useRightsHook();
  const { id, cloneId } = useParams();
  const [searchParams] = useSearchParams();
  const [data, setData] = useState<Partial<DimensionDetailEntity>>();
  const [mode, setMode] = useState<Mode>(!id || cloneId ? 'edit' : 'read');
  const [fetching, setFetching] = useState(false);
  const [saving, setSaving] = useState(false);
  const { closeCurrentTab, openTab, setPageInfo } = useKeepAliveTabs();
  const panelStore = usePanelStore();
  const reloadModelTree = useReloadModelTree(true);
  const dimensionFormRef = useRef<{ validateFields: FormInstance['validateFields'] }>();
  const confirmDelete = useDeleteConfirm({
    prefix: '/api/v2/data/modeling',
    delUrl: '/dimension',
    relyUrl: '/dimension',
  });

  const onSave = async () => {
    const values = await dimensionFormRef.current?.validateFields();
    setSaving(true);
    const { data } = await DimensionApi[id ? 'update' : 'create']({
      ...values,
      id,
    }).finally(() => setSaving(false));
    reloadModelTree();
    setPageInfo({
      isDirty: false,
    });
    if (id) {
      fetchData();
      setMode('read');
    } else {
      closeCurrentTab(() => {
        openTab(`/data-modeling/dimension-modeling/modeling/dimension/edit/${data.id}`);
      });
    }
  };
  const onCancel = () => {
    setMode('read');
    setPageInfo({
      isDirty: false,
    });
  };

  const fetchData = async () => {
    setFetching(true);
    const { data } = await DimensionApi.getDetail((id ?? cloneId) as string).finally(() => setFetching(false));
    const { name, code } = data;
    setData({
      ...data,
      name: cloneId ? `${name}_clone` : name,
      code: cloneId ? `${code}_clone` : code,
    });
    panelStore.setCurrentCatalog(data.layerCatalog);
    setPageInfo({
      title: data.name,
      icon: 'iconfont icon-dimensionwd-line text-gray-6 text-sm text-success-1',
    });
  };

  const handleDelete = async () => {
    if (!data?.id) return;
    await confirmDelete(data.id, `确定删除维度「${data?.name}」`);
    reloadModelTree();
    message.success('删除成功');
    closeCurrentTab();
  };

  useEffect(() => {
    if (id ?? cloneId) {
      fetchData();
    } else {
      setData({
        layerCatalog: searchParams.get('catalog') as string,
      });
    }
  }, []);

  return (
    <div className='flex flex-col h-full w-full'>
      <div className='h-12 flex items-center px-2'>
        {mode === 'edit' && (
          <Space>
            <Button type='primary' loading={saving} onClick={onSave}>
              保存
            </Button>
            {id && <Button onClick={onCancel}>取消编辑</Button>}
          </Space>
        )}
        {mode === 'read' && (
          <Space>
            <Button
              type='text'
              size='small'
              onClick={() => setMode('edit')}
              disabled={!hasRights('data_model:write', data?.projectAuth)}
            >
              <i className='iconfont icon-rename-line text-sm mr-2'></i>编辑
            </Button>
            <Divider type='vertical' className='mx-0' />
            <Button type='text' size='small' onClick={() => fetchData()}>
              <span className='text-gray-6'>
                <i className='iconfont icon-refresh-line text-sm mr-2'></i>刷新
              </span>
            </Button>
            <Divider type='vertical' className='mx-0' />
            <Button
              type='text'
              size='small'
              onClick={() => handleDelete()}
              disabled={!hasRights('data_model:write', data?.projectAuth)}
            >
              <i className='iconfont icon-bin-line text-sm mr-2'></i>删除
            </Button>
          </Space>
        )}
      </div>
      <Divider className='my-0' />
      {mode === 'edit' && (
        <DimensionForm
          initialValues={data}
          ref={dimensionFormRef}
          onValuesChange={() => {
            setPageInfo({
              isDirty: true,
            });
          }}
        />
      )}
      <div className='flex-1 flex justify-center'>
        {mode === 'read' &&
          data &&
          (fetching ? <Spin className='mt-20' /> : <DimensionDetail data={data as DimensionDetailEntity} />)}
      </div>
    </div>
  );
};

export default Dimension;
