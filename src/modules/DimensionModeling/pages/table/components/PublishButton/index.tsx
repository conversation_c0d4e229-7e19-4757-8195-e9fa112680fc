import React, { useState } from 'react';
import { Button, ButtonProps, Modal } from 'antd';

import { PROJECT } from '@/constants';
import { DataSourceConfigMap } from '@/modules/AddressManagement/DataSource/constants';
import { URL_MAP } from '@/modules/DataDev/utils';
import { ModelTableEntityDetail } from '@/modules/DimensionModeling/models';
import { TableDeployApi } from '@/services';

import { DimensionPublishModal } from './DimensionPublishModal';

interface Props extends ButtonProps {
  entity: ModelTableEntityDetail;
  disabled?: boolean;
}

export const PublishButton: React.FC<Props> = ({ entity, disabled, ...otherProps }) => {
  const [open, setOpen] = useState(false);
  const doPublish = async () => {
    try {
      const result = await TableDeployApi.checkStatus({ tbId: entity?.id });
      if (result.data === true) {
        setOpen(true);
      } else {
        const { platform, id } = result.data;
        const unDeployedUrl = `/data-dev/dev/management/${DataSourceConfigMap[platform].path}/${id}`;
        const modal = Modal.confirm({
          maskClosable: true,
          closable: true,
          title: '当前存在未发布的发布项，不允许再次发布。',
          footer: (
            <div className='text-right'>
              <Button
                type='default'
                onClick={() => {
                  modal.destroy();
                  window.open(PROJECT.baseUrl + unDeployedUrl);
                }}
                className='mr-2'
              >
                查看未发布项
              </Button>
              <Button
                type='primary'
                onClick={() => {
                  modal.destroy();
                  TableDeployApi.redeploy(entity?.id).then(({ data }) => {
                    window.open(PROJECT.baseUrl + URL_MAP[data.platform] + `/${data.id}`);
                  });
                }}
              >
                重新发布
              </Button>
            </div>
          ),
          content: <div className='h-16'></div>,
        });
      }
    } catch (e) {}
  };
  return (
    <>
      <Button type='text' size='small' onClick={() => doPublish()} disabled={!!disabled} {...otherProps}>
        <i className='iconfont icon-near_me-line text-sm mr-2'></i>
        {entity?.isOnline ? '发布' : '销毁'}
      </Button>
      {open && <DimensionPublishModal open={open} oncancel={() => setOpen(false)} tableData={entity} />}
    </>
  );
};
