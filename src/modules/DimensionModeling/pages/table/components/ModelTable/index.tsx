import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Divider, FormInstance, message, Space, Tooltip } from 'antd';
import { Link, useParams, useSearchParams } from 'umi';

import { FieldsManager, Loading, SectionCaptain, useKeepAliveTabs } from '@/components';
import { TableType } from '@/constants';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { nodeTypeToIconInfo } from '@/modules/DimensionModeling/constants';
import { useReloadModelTree } from '@/modules/DimensionModeling/hooks';
import { useDataDevUrlByTableId } from '@/modules/DimensionModeling/hooks/useDataDevUrlByTableId';
import type { ModelTableEntity, ModelTableEntityDetail } from '@/modules/DimensionModeling/models';
import { usePanelStore } from '@/modules/DimensionModeling/stores/usePanelStore';
import { ObjectModelingApi } from '@/modules/ObjectModeling/services';
import { TableApi } from '@/services';

import './index.less';

import { LifeCycleModal } from '../LifeCycleModal';
import { PublishButton } from '../PublishButton';

import { ModelTableDetail } from './ModelTableDetail';
import { ModelTableForm } from './ModelTableForm';
import { useTableColumnHook } from './useTableColumnHook';

type Mode = 'edit' | 'read';

const ModelTable: React.FC = () => {
  const { id, cloneId } = useParams();
  const { hasRights } = useRightsHook();
  const [searchParams] = useSearchParams();
  const [data, setData] = useState<Partial<ModelTableEntityDetail>>();
  const [mode, setMode] = useState<Mode>(!id || cloneId ? 'edit' : 'read');
  const [fetching, setFetching] = useState(false);
  const [dimensionTableList, setDimensionTableList] = useState<DefaultOptionType[]>([]);
  const [saving, setSaving] = useState(false);
  const { closeCurrentTab, openTab, setPageInfo } = useKeepAliveTabs();
  const panelStore = usePanelStore();
  const reloadModelTree = useReloadModelTree(true);
  const dimensionFormRef = useRef<{ validateFields: FormInstance['validateFields'] }>();
  const [openLifeCycle, setOpenLifeCycle] = useState(false);

  const versionInfo = useDataDevUrlByTableId();

  const tbType = useMemo(() => {
    return (searchParams.get('tbType') || data?.tbType) as DIMENSION_MODELING.TableType;
  }, [searchParams, data]);

  const { columns, form: tableFieldForm } = useTableColumnHook({ tbType, mode });
  const { columns: IDXcolumns, form: tableFieldIDXForm } = useTableColumnHook({ tbType, mode });

  const confirmDelete = useDeleteConfirm({
    prefix: '/api/v2/data/modeling',
    delUrl: '/table',
    relyUrl: '/table',
  });

  const confirmOffline = useDeleteConfirm({
    prefix: '/api/v2/data/modeling',
    delUrl: '/table/offline',
    relyUrl: '/table/offline',
    type: 'offline',
    confirmContent: '下线后无法再次上线且无法被其他功能引用，只允许删除',
  });

  const onSave = async () => {
    const values = await dimensionFormRef.current?.validateFields();
    let columns = [];
    if (tbType === 'IDX') {
      const dimCol = tableFieldForm.getFieldValue('dataSource').map(item => {
        return {
          ...item,
          colUsage: 'DIM',
        };
      });
      const idxCol = tableFieldIDXForm.getFieldValue('dataSource').map(item => {
        return {
          ...item,
          colUsage: 'IDX',
        };
      });
      if (values.relateDim) {
        columns = idxCol;
      } else {
        columns = idxCol.concat(dimCol);
      }
    } else {
      columns = tableFieldForm.getFieldValue('dataSource');
    }
    setSaving(true);
    const { data } = await TableApi[id ? 'update' : 'create']({
      ...values,
      columns,
      tbType,
      id,
    }).finally(() => setSaving(false));
    reloadModelTree();
    setPageInfo({
      isDirty: false,
    });
    if (id) {
      fetchData();
      setMode('read');
    } else {
      closeCurrentTab(() => {
        openTab(`/data-modeling/dimension-modeling/modeling/table/edit/${data.id}`);
      });
    }
  };
  const onCancel = () => {
    setMode('read');
    setPageInfo({
      isDirty: false,
    });
  };

  const fetchDimensionTable = async () => {
    try {
      const res = await TableApi.getDimensionTable();
      if (res?.code === '0000') {
        setDimensionTableList(
          res?.data?.map(item => {
            return {
              value: item.tbId,
              label: `${item.tbAlias}（${item.tbName}）`,
            };
          }) ?? [],
        );
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (tbType === 'IDX') {
      fetchDimensionTable();
    }
  }, [tbType]);

  const fetchData = async () => {
    setFetching(true);
    const Api = tbType === 'OBJ' ? ObjectModelingApi : TableApi;
    const { data } = await Api.getDetail((id ?? cloneId) as string).finally(() => setFetching(false));
    if (tbType === 'OBJ') {
      data.layerCatalog = 'WH';
    }
    const { tbName, tbAlias } = data;
    if (tbType === 'IDX' || data.tbType === 'IDX') {
      tableFieldForm.setFieldValue('dataSource', data.columns.filter(item => item.colUsage === 'DIM') ?? []);
      tableFieldIDXForm.setFieldValue('dataSource', data.columns.filter(item => item.colUsage === 'IDX') ?? []);
    } else {
      tableFieldForm.setFieldValue('dataSource', data.columns ?? []);
    }
    setData({
      ...data,
      tbName: cloneId ? `${tbName}_clone` : tbName,
      tbAlias: cloneId ? `${tbAlias}_克隆` : tbAlias,
    });

    panelStore.setCurrentCatalog(data.layerCatalog);
    const { name: iconName, className } = nodeTypeToIconInfo[data.tbType] ?? {};
    setPageInfo({
      title: cloneId ? `${tbAlias}_克隆` : tbAlias,
      icon: `iconfont icon-${iconName} text-sm ${className}`,
    });
  };

  const handleDelete = async () => {
    if (!data?.id) return;
    await confirmDelete(data.id, `确定删除表「${data?.tbAlias}」`);
    reloadModelTree();
    message.success('删除成功');
    closeCurrentTab();
  };

  const handleOffline = async () => {
    if (!data?.id) return;
    await confirmOffline(data.id, `确定下线表「${data?.tbAlias}」`);
    reloadModelTree();
    message.success('下线成功');
    fetchData();
  };

  useEffect(() => {
    if (id ?? cloneId) {
      fetchData();
    } else {
      setData({
        tbType: searchParams.get('tbType') as keyof typeof TableType,
        layerCatalog: searchParams.get('catalog') as string,
      });
    }
  }, []);

  const showLifeCycle = () => {
    setOpenLifeCycle(true);
  };

  return (
    <div className='flex flex-col h-full w-full modal-table'>
      {tbType !== 'OBJ' && (
        <>
          <div className='h-12 flex items-center px-2'>
            {mode === 'edit' && (
              <Space>
                <Button
                  type='primary'
                  loading={saving}
                  onClick={onSave}
                  disabled={!hasRights('data_model:write', id ? data?.projectAuth : undefined)}
                >
                  保存
                </Button>
                {id && <Button onClick={onCancel}>取消编辑</Button>}
              </Space>
            )}
            {mode === 'read' && (
              <div className='flex justify-between items-center w-full'>
                <Space>
                  <Button
                    type='text'
                    size='small'
                    onClick={() => setMode('edit')}
                    disabled={!hasRights('data_model:write', data?.projectAuth)}
                  >
                    <i className='iconfont icon-rename-line text-sm mr-2'></i>编辑
                  </Button>
                  {data && versionInfo?.actionType !== 'DELETE' && (
                    <PublishButton entity={data} disabled={!hasRights('data_model:deploy', data?.projectAuth)} />
                  )}
                  <Divider type='vertical' className='mx-0' />
                  <Button type='text' size='small' onClick={() => fetchData()}>
                    <span className='text-gray-6'>
                      <i className='iconfont icon-refresh-line text-sm mr-2'></i>刷新
                    </span>
                  </Button>
                  {data?.isOnline !== false && versionInfo && (
                    <>
                      <Divider type='vertical' className='mx-0' />
                      <Button
                        type='text'
                        size='small'
                        disabled={!hasRights('data_model:write', data?.projectAuth)}
                        onClick={() => handleOffline()}
                      >
                        <span className={hasRights('data_model:write', data?.projectAuth) ? 'text-gray-6' : ''}>
                          <i className='iconfont icon-offline-line1 text-sm mr-2'></i>下线
                        </span>
                      </Button>
                    </>
                  )}
                  {(!versionInfo || versionInfo?.actionType === 'DELETE') && (
                    <>
                      <Divider type='vertical' className='mx-0' />
                      <Button
                        type='text'
                        size='small'
                        onClick={() => handleDelete()}
                        disabled={!hasRights('data_model:write', data?.projectAuth)}
                      >
                        <i className='iconfont icon-bin-line text-sm mr-2'></i>删除
                      </Button>
                    </>
                  )}
                  <Divider type='vertical' className='mx-0' />
                  <Button type='text' size='small' onClick={showLifeCycle}>
                    <span className='text-gray-6'>
                      <i className='iconfont icon-database-clock-outline text-sm mr-2'></i>生命周期策略
                    </span>
                  </Button>
                </Space>

                <Space>
                  <Link type='text' to={`/version/compare/modeling/${data?.id}`}>
                    <span className='text-gray-6 text-sm'>
                      <i className='iconfont icon-clone-line text-sm mr-2'></i>版本对比
                    </span>
                  </Link>
                  <Divider type='vertical' className='mx-0' />
                  <span className='text-captain text-gray-13 px-2'>
                    {data?.version === null || !versionInfo ? (
                      <>未发布</>
                    ) : (
                      <>
                        当前版本
                        <Tooltip title='点击后即可查看数据开发对应发布版本' placement='bottomRight'>
                          <Link to={versionInfo.url} target='_blank'>
                            <span className='text-primary underline cursor-pointer'>v{versionInfo.version}</span>
                          </Link>
                        </Tooltip>
                      </>
                    )}
                  </span>
                </Space>
              </div>
            )}
          </div>
          <Divider className='my-0' />
        </>
      )}

      {fetching && <Loading />}

      {!fetching && (
        <div className='flex-1 overflow-auto'>
          {mode === 'edit' && data && (
            <>
              <ModelTableForm
                ref={dimensionFormRef}
                tbType={tbType}
                dimensionTableList={dimensionTableList}
                tableFieldIDXForm={tableFieldIDXForm}
                tableFieldForm={tableFieldForm}
                columns={columns}
                IDXcolumns={IDXcolumns}
                data={data as ModelTableEntity}
                mode={mode}
                initialValues={data}
                onValuesChange={() => {
                  setPageInfo({
                    isDirty: true,
                  });
                }}
              />
            </>
          )}

          {mode === 'read' && data && (
            <ModelTableDetail
              tbType={tbType}
              data={data as ModelTableEntity}
              dimensionTableList={dimensionTableList}
              mode={mode}
              columns={columns}
              IDXcolumns={IDXcolumns}
              tableFieldForm={tableFieldForm}
              tableFieldIDXForm={tableFieldIDXForm}
            />
          )}

          {tbType !== 'IDX' && <SectionCaptain title='字段管理' className='mb-1 ml-4 mt-4' />}

          <div className='mt-2'>
            {tbType && tbType !== 'IDX' && (
              <FieldsManager
                columns={columns}
                initialValues={data?.columns ?? []}
                form={tableFieldForm}
                tableType={tbType}
                mode={mode}
                name={data?.tbAlias}
              />
            )}
          </div>
        </div>
      )}

      {
        openLifeCycle && <LifeCycleModal
          id={id}
          open={openLifeCycle}
          onCancel={() => setOpenLifeCycle(false)}
        ></LifeCycleModal>
      }
    </div>
  );
};

export default React.memo(ModelTable);
