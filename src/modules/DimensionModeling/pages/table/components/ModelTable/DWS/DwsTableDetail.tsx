import React from 'react';
import { Descriptions, Tag } from 'antd';

import { SectionCaptain } from '@/components';
import { ModelTableEntityDetail } from '@/modules/DimensionModeling/models';

import { LifeCycleInfoLabel } from '../../LifeCycleInfoLabel';

interface Props {
  data: ModelTableEntityDetail;
}
const DwsTableDetail: React.FC<Props> = ({ data }) => {
  const { layerName, bizName, domName, tbName, tbAlias, description, adjList, periodList, ruleName, lifecycle } = data;
  return (
    <div className='px-4 py-2'>
      <SectionCaptain title='基本信息' className='mb-3'></SectionCaptain>

      <Descriptions column={2} labelStyle={{ width: 88, textAlign: 'right', display: 'inline-block' }}>
        <Descriptions.Item label='数仓分层'>{layerName}</Descriptions.Item>
        {bizName && <Descriptions.Item label='业务分类'>{bizName ?? '-'}</Descriptions.Item>}
        {domName && <Descriptions.Item label='数据域'>{domName ?? '-'}</Descriptions.Item>}

        <Descriptions.Item label='修饰词' span={1}>
          {adjList?.map(x => (
            <Tag key={x.id}>{x.name}</Tag>
          ))}
        </Descriptions.Item>
        <Descriptions.Item label='时间周期' span={2}>
          {periodList?.map(x => (
            <Tag key={x.id}>{x.name}</Tag>
          ))}
        </Descriptions.Item>
        <Descriptions.Item label='表名规则' span={2}>
          {ruleName ?? '-'}
        </Descriptions.Item>
        <Descriptions.Item label='表名' span={2}>
          {tbName ?? '-'}
        </Descriptions.Item>
        <Descriptions.Item label='表中文名' span={2}>
          {tbAlias ?? '-'}
        </Descriptions.Item>
        <Descriptions.Item label={<LifeCycleInfoLabel id={data?.id} />} span={2}>
          {lifecycle ? `${lifecycle}天` : '-'}
        </Descriptions.Item>
        <Descriptions.Item label='模型标签' span={2}>
          {data?.tags?.map(item => (
            <Tag key={item}>{item}</Tag>
          ))}
        </Descriptions.Item>
        <Descriptions.Item label='描述' span={2}>
          {description ?? '-'}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default DwsTableDetail;
