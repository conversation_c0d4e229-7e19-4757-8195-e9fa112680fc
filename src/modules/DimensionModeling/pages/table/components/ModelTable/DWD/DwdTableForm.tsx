import { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';
import { Col, Form, Input, InputNumber, Row, Space } from 'antd';
import { useParams } from 'umi';

import { BusinessCategorySelect, BusinessProcessSelect, SectionCaptain } from '@/components';
import {
  DimensionSelector,
  LayerSelector,
  ModelRuleOfLayerSelector,
  StoragePolicySelector,
  TableNameInput,
} from '@/modules/DimensionModeling/components';
import { useInputNameRuleHook } from '@/modules/DimensionModeling/hooks/useInputNameRuleHook';
import { ModelTableEntityDetail } from '@/modules/DimensionModeling/models';

import { LifeCycleInfoLabel } from '../../LifeCycleInfoLabel';
import { TagForm } from '../../TagForm';

interface Props {
  initialValues?: Partial<ModelTableEntityDetail>;
  onValuesChange?: FormProps['onValuesChange'];
}

const DwdTableForm = (props: Props, ref) => {
  const [form] = Form.useForm();
  const bizId = Form.useWatch('bizId', form);
  const layerId = Form.useWatch('layerId', form);
  const { id } = useParams();
  const layerCatalog = props.initialValues?.layerCatalog;
  const { codes, updateCode, rule, setRule } = useInputNameRuleHook();

  const initialValues = useMemo(() => {
    const { domId, procId } = props.initialValues ?? {};
    // 消除控制台warning，Input输入框的value应该初始化为''或undefined，不能为null
    return {
      ...props.initialValues,
      domAndProc: domId && procId ? [domId, procId] : [],
    };
  }, [props.initialValues]);

  const rules = {
    layerId: [{ required: true, message: '请选择' }],
    bizId: [{ required: true, message: '请选择' }],
    procId: [{ required: true, message: '请选择' }],
    domAndProc: [{ required: true, message: '请选择' }],
    tbName: [{ required: true, message: '请输入' }],
    tbAlias: [{ required: true, message: '请输入' }],
    description: [],
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        validateFields: () => {
          return form.validateFields();
        },
      };
    },
    [],
  );

  const onGetDefaultRule = (defaultRule: LayerRule) => {
    const ruleId = form.getFieldValue('ruleId');
    if (!id && !ruleId) {
      setRule(defaultRule);
      form.setFieldValue('ruleId', defaultRule.id);
    }
  };

  useEffect(() => {
    const { subjId, martId } = props.initialValues ?? {};
    // 集市和主题是同一个选框，选择的是集市或主题
    form.setFieldsValue({
      martOrSubj: martId ?? subjId,
    });
  }, []);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  return (
    <div className='mx-4 my-2' ref={ref}>
      <SectionCaptain title='基本信息' className='mb-3' />
      <Form
        form={form}
        labelCol={{ flex: '88px' }}
        wrapperCol={{ span: 24 }}
        initialValues={initialValues}
        onValuesChange={props.onValuesChange}
      >
        <Row gutter={[16, 0]}>
          <Col span={12}>
            <Form.Item label='数仓分层' name='layerId' rules={rules.layerId}>
              <LayerSelector tbType='DWD' catalog={layerCatalog} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='业务分类' name='bizId' rules={rules.bizId}>
              <BusinessCategorySelect
                noTips
                onChange={() => {
                  form.setFieldsValue({
                    domAndProc: undefined,
                    domId: undefined,
                    procId: undefined,
                  });
                }}
                onChangeCode={updateCode}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='业务过程' name='domAndProc' rules={rules.procId}>
              <BusinessProcessSelect
                bizId={bizId}
                onChange={value => {
                  if (!value) {
                    form.setFieldValue('domId', undefined);
                    form.setFieldValue('procId', undefined);
                    return;
                  }
                  const [domId, procId] = value;
                  form.setFieldValue('domId', domId);
                  form.setFieldValue('procId', procId);
                }}
                onChangeCode={updateCode}
              />
            </Form.Item>
            <Form.Item name='domId' hidden>
              <Input />
            </Form.Item>
            <Form.Item name='procId' hidden>
              <Input />
            </Form.Item>
          </Col>

          <Col span={layerCatalog === 'APP' ? 24 : 12}>
            <Form.Item label='存储策略' name='storagePolicy'>
              <StoragePolicySelector onChangeCode={updateCode} />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item label='表名规则' name='ruleId'>
          <ModelRuleOfLayerSelector layerId={layerId} onGetDefaultRule={onGetDefaultRule} onChangeRule={setRule} />
        </Form.Item>
        <Form.Item label='表名' name='tbName' rules={rules.tbName}>
          <TableNameInput codes={codes} form={form} rule={rule} />
        </Form.Item>
        <Form.Item label='表中文名' name='tbAlias' rules={rules.tbAlias}>
          <Input placeholder='请输入' />
        </Form.Item>
        <Form.Item label={<LifeCycleInfoLabel id={id} />}>
          <Space className='flex items-center'>
            <Form.Item name='lifecycle' noStyle>
              <InputNumber className='w-32' />
            </Form.Item>
            <label>天</label>
          </Space>
        </Form.Item>
        <TagForm />
        <Form.Item label='描述' name='description' rules={rules.description}>
          <Input.TextArea placeholder='请输入' />
        </Form.Item>
      </Form>
    </div>
  );
};

export default forwardRef(DwdTableForm);
