import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import {
  Checkbox,
  Col,
  Form,
  FormInstance,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Space,
  Tabs,
  TabsProps,
} from 'antd';
import { cloneDeep } from 'lodash';
import { useParams } from 'umi';

import { BusinessCategorySelect, DataDomainSelect, FieldsManager, SectionCaptain } from '@/components';
import { FieldDescription } from '@/constants';
import {
  LayerSelector,
  ModelRuleOfLayerSelector,
  TableNameInput,
  TimePeriodSelector,
} from '@/modules/DimensionModeling/components';
import { useInputNameRuleHook } from '@/modules/DimensionModeling/hooks/useInputNameRuleHook';
import { ModelTableColumn, ModelTableEntityDetail, StorageModeTypes } from '@/modules/DimensionModeling/models';
import { TableApi } from '@/services';
import { uuid } from '@/utils';

import { LifeCycleInfoLabel } from '../../LifeCycleInfoLabel';
import { TagForm } from '../../TagForm';

interface Props {
  initialValues?: Partial<ModelTableEntityDetail>;
  onValuesChange?: FormProps['onValuesChange'];
  data: ModelTableEntityDetail;
  dimensionTableList?: DefaultOptionType[];
  tbType: DIMENSION_MODELING.TableType;
  columns?: TableProps<ModelTableColumn>['columns'];
  IDXcolumns?: TableProps<ModelTableColumn>['columns'];
  tableFieldForm: FormInstance;
  tableFieldIDXForm: FormInstance;
  mode: 'edit' | 'read';
}

const dimColumn = [
  {
    id: uuid(),
    colType: 'BIGINT',
    colCatalog: FieldDescription.DIMENSION,
    colName: '__series_id',
    colDisplay: '指标唯一id',
    description: '指标唯一id',
    isPrimaryKey: true,
    isNotNull: true,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    colAction: 'default',
    dimColId: undefined,
  },
  {
    id: uuid(),
    colType: 'BIGINT',
    colCatalog: FieldDescription.DIMENSION,
    colName: '__mgmt_id',
    colDisplay: '指标管理id',
    description: '指标管理id',
    isPrimaryKey: false,
    isNotNull: true,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    colAction: 'default',
    dimColId: undefined,
  },
  {
    id: uuid(),
    colType: 'STRING',
    colCatalog: FieldDescription.DIMENSION,
    colName: 'labels',
    colDisplay: '指标标签和标签值',
    description: '指标标签和标签值',
    isPrimaryKey: false,
    isNotNull: true,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    colAction: 'default',
    dimColId: undefined,
  },
  {
    id: uuid(),
    colType: 'STRING',
    colCatalog: FieldDescription.DIMENSION,
    colName: '__name__',
    colDisplay: '指标名称',
    description: '指标名称',
    isPrimaryKey: false,
    isNotNull: true,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    colAction: 'default',
    dimColId: undefined,
  },
];

const idxColumn = [
  {
    id: uuid(),
    colType: 'BIGINT',
    colCatalog: FieldDescription.DIMENSION,
    colName: '__series_id',
    colDisplay: '指标唯一id',
    description: '指标唯一id',
    isPrimaryKey: true,
    isNotNull: true,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    colAction: 'default',
    dimColId: undefined,
  },
  {
    id: uuid(),
    colType: 'DATETIME',
    colCatalog: FieldDescription.DIMENSION,
    colName: 'timestamp',
    colDisplay: '指标时间',
    description: '指标时间',
    isPrimaryKey: false,
    isNotNull: true,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    colAction: 'default',
    dimColId: undefined,
  },
  {
    id: uuid(),
    colType: 'DOUBLE',
    colCatalog: FieldDescription.DIMENSION,
    colName: 'value',
    colDisplay: '指标值',
    description: '指标值',
    isPrimaryKey: false,
    isNotNull: true,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    colAction: 'default',
    dimColId: undefined,
  },
];

const IDXTableForm = (props: Props, ref) => {
  const { data, tbType, columns, tableFieldForm, IDXcolumns, tableFieldIDXForm, mode } = props;
  const [form] = Form.useForm();
  const bizId = Form.useWatch('bizId', form);
  const layerId = Form.useWatch('layerId', form);
  const relateDim = Form.useWatch('relateDim', form);
  const storageMode = Form.useWatch('storageMode', form);
  const { id } = useParams();
  const layerCatalog = props.initialValues?.layerCatalog;
  const { codes, updateCode, rule, setRule } = useInputNameRuleHook();
  const firstChooseStorageMode = useRef(true);

  const items: TabsProps['items'] = [
    {
      key: 'dim_field',
      label: '维度表字段',
      children: (
        <div className={`${relateDim ? 'mt-2' : ''}`}>
          <FieldsManager
            columns={columns}
            initialValues={data?.columns?.filter(item => item.colUsage === 'DIM') ?? []}
            standardFields={dimColumn}
            form={tableFieldForm}
            tableType={tbType}
            mode={relateDim ? 'read' : mode}
          />
        </div>
      ),
    },
    {
      key: 'value_field',
      label: '值表字段',
      children: (
        <FieldsManager
          columns={IDXcolumns}
          initialValues={data?.columns?.filter(item => item.colUsage === 'IDX') ?? []}
          standardFields={storageMode === 'MULTI' ? idxColumn.filter(item => item.colName !== 'value') : idxColumn}
          form={tableFieldIDXForm}
          tableType={tbType}
          mode={mode}
        />
      ),
    },
  ];

  const initialValues = useMemo(() => {
    const { domId, procId } = props.initialValues ?? {};
    // 消除控制台warning，Input输入框的value应该初始化为''或undefined，不能为null
    return {
      ...props.initialValues,
      domAndProc: domId && procId ? [domId, procId] : [],
    };
  }, [props.initialValues]);

  const rules = {
    layerId: [{ required: true, message: '请选择' }],
    bizId: [{ required: true, message: '请选择' }],
    procId: [{ required: true, message: '请选择' }],
    domAndProc: [{ required: true, message: '请选择' }],
    periodIds: [{ required: true, message: '请选择' }],
    tbName: [{ required: true, message: '请输入' }],
    domId: [{ required: true, message: '请选择' }],
    relateDim: [{ required: true, message: '请选择' }],
    storageMode: [{ required: true, message: '请选择' }],
    tbAlias: [{ required: true, message: '请输入' }],
    dimTbName: [{ required: true, message: '请输入' }],
    idxTbName: [{ required: true, message: '请输入' }],
    description: [],
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        validateFields: () => {
          return form.validateFields();
        },
      };
    },
    [],
  );

  useEffect(() => {
    if (storageMode) {
      const idxArr = props.tableFieldIDXForm?.getFieldValue('dataSource') ?? [];
      const customIdsArr = idxArr.filter(item => item.colAction !== 'default');
      const dimArr = props.tableFieldForm?.getFieldValue('dataSource') ?? [];
      const customDimArr = dimArr.filter(item => item.colAction !== 'default');

      if (storageMode === 'MULTI') {
        props.tableFieldIDXForm?.setFieldValue(
          'dataSource',
          idxColumn.filter(item => item.colName !== 'value').concat(customIdsArr),
        );
      } else {
        props.tableFieldIDXForm?.setFieldValue('dataSource', idxColumn.concat(customIdsArr));
      }

      if (firstChooseStorageMode.current) {
        props.tableFieldForm?.setFieldValue('dataSource', dimColumn.concat(customDimArr));
      }
      firstChooseStorageMode.current = false;
    }
  }, [storageMode]);

  const onGetDefaultRule = (defaultRule: LayerRule) => {
    const ruleId = form.getFieldValue('ruleId');
    if (!id && !ruleId) {
      setRule(defaultRule);
      form.setFieldValue('ruleId', defaultRule.id);
    }
  };

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  const handleDimTbNameChange = async (value: string) => {
    try {
      const res = await TableApi.getDetail(value);
      if (res?.code === '0000') {
        const dimArr = props.tableFieldForm?.getFieldValue('dataSource') ?? [];
        const columns =
          res?.data?.columns
            ?.filter(item => item.colUsage == 'DIM')
            ?.map(item => {
              item.colAction = 'default';
              return item;
            }) ?? [];
        dimArr.forEach(column => {
          if (columns.every(item => item.colName !== column.colName)) {
            columns.push(column);
          }
        });
        props.tableFieldForm?.setFieldValue('dataSource', columns);
      }
    } catch (error) {}
  };

  return (
    <div className='mx-4 my-2' ref={ref}>
      <SectionCaptain title='基本信息' className='mb-3' />
      <Form
        form={form}
        labelCol={{ flex: '120px' }}
        wrapperCol={{ span: 24 }}
        initialValues={initialValues}
        onValuesChange={props.onValuesChange}
      >
        <Row gutter={[16, 0]}>
          <Col span={12}>
            <Form.Item label='数仓分层' name='layerId' rules={rules.layerId}>
              <LayerSelector tbType='DWS' catalog={layerCatalog!} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='业务分类' name='bizId' rules={rules.bizId}>
              <BusinessCategorySelect
                noTips
                onChange={() => {
                  form.setFieldsValue({
                    domAndProc: undefined,
                    domId: undefined,
                    procId: undefined,
                  });
                }}
                onChangeCode={updateCode}
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label='数据域' name='domId' rules={rules.domId}>
              <DataDomainSelect bizId={bizId} onChangeCode={updateCode} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='储存模型' name='storageMode' rules={rules.storageMode}>
              <Radio.Group>
                {Object.entries(StorageModeTypes)
                  .map(([value, label]) => ({ value, label }))
                  .map(item => (
                    <Radio value={item.value} key={item.value}>
                      {item.label}
                    </Radio>
                  ))}
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label='时间周期' name='periodIds' rules={rules.periodIds}>
              <TimePeriodSelector onChangeCode={updateCode} />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item label='表名规则' name='ruleId'>
          <ModelRuleOfLayerSelector layerId={layerId} onGetDefaultRule={onGetDefaultRule} onChangeRule={setRule} />
        </Form.Item>
        <Form.Item label='表名' name='tbName' rules={rules.tbName}>
          <TableNameInput
            codes={codes}
            form={form}
            rule={rule}
            onChange={e => {
              if (e?.target?.value) {
                form.setFieldsValue({
                  idxTbName: `${e.target.value}_value`,
                  dimTbName: `${e.target.value}_series`,
                });
              }
            }}
          />
        </Form.Item>

        <Row gutter={24}>
          <Col span={24}>
            <Form.Item label='是否关联维度表' name='relateDim' valuePropName='checked' initialValue={false}>
              <Checkbox
                onChange={({ target: { checked } }) => {
                  if (!checked) {
                    tableFieldForm.setFieldValue('dataSource', cloneDeep(dimColumn));
                    form.setFieldValue('dimTbId', null);
                  }
                }}
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label='维度表' name={relateDim ? 'dimTbId' : 'dimTbName'} rules={rules.dimTbName}>
              {relateDim ? (
                <Select
                  options={props?.dimensionTableList?.filter(item => item.value !== id) ?? []}
                  allowClear
                  showSearch
                  optionFilterProp='label'
                  onChange={handleDimTbNameChange}
                />
              ) : (
                <Input />
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='值表' name='idxTbName' rules={rules.idxTbName}>
              <Input />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label='表中文名' name='tbAlias' rules={rules.tbAlias}>
          <Input placeholder='请输入' />
        </Form.Item>
        <Form.Item label={<LifeCycleInfoLabel id={id} />}>
          <Space className='flex items-center'>
            <Form.Item name='lifecycle' noStyle>
              <InputNumber className='w-32' />
            </Form.Item>
            <label>天</label>
          </Space>
        </Form.Item>
        <TagForm />
        <Form.Item label='描述' name='description' rules={rules.description}>
          <Input.TextArea placeholder='请输入' />
        </Form.Item>
      </Form>

      <SectionCaptain title='字段管理' className='mb-1 ml-4 mt-4' />

      <Tabs items={items} />
    </div>
  );
};

export default forwardRef(IDXTableForm);
