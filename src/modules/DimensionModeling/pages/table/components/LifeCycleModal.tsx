import { useEffect, useMemo, useState } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Button, Modal, Popover, Tooltip } from 'antd';

import { CustomTable, SectionCaptain } from '@/components';
import { BACKUP_TYPE_MAP } from '@/modules/LifeCycle/constants';
import { LifeCycleApi } from '@/modules/LifeCycle/services';

export const LifeCycleModal = ({ id, open, onCancel }) => {
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    hideOnSinglePage: true,
  });
  

  const handleTableChange = ({ pageSize, current }) => {
    setPagination({
      ...pagination,
      pageSize,
      current,
    });
  };

  const getColumns = (columns: any[] = []) => {
    return [
      {
        title: '策略名称',
        dataIndex: 'name',
        width: 200,
        render: (text, record) => <Tooltip title={text}>
          <div>
            <i className={`${!record.isEnabled ? 'text-gray-5' : 'text-warning'} text-[16px] mr-1 
            iconfont ${record.sourcePlatform === 'CLICKHOUSE' ? 'icon-ck' : 'icon-hive-fill'}`}
            />
            <span className={`${!record.isEnabled && 'text-gray-5'}`}>{text}</span>
          </div>
        </Tooltip>,
      },
      {
        title: '策略优先级',
        dataIndex: 'priority',
        width: 100,
        render: value => <div className='w-[120px]'>{ value }</div>,
      },
      {
        title: '备份模式',
        dataIndex: 'backupMode',
        align: 'center',
        render: (value, record) => <div className='text-[13px]'>
          <div>{BACKUP_TYPE_MAP[value]}</div>
          <div>（{
            value === 'NEAR_REALTIME' && `T-${record?.backupConfig?.backupStartDays ?? ''}开始，`
          }保留时长{record?.retentionDays}天）</div>
        </div>,
      },
      ...columns,
      {
        title: '操作',
        dataIndex: 'action',
        width: 80,
        render: (_, record) => {
          return <Button type='link' size='small' onClick={() => gotoView(record)}>查看</Button>;
        },
      },
    ];
  };

  const matchedColumns = getColumns([
    {
      title: '生效状态',
      dataIndex: 'reason',
      render(val) {
        return val ? <Popover content={val}>
          <>
            <span className='text-danger'>未生效</span>
            <ExclamationCircleOutlined className='ml-2 text-warning' />
          </>
        </Popover> : <span className='text-success'>已生效</span>;
      },
    },
  ]);

  const unMatchedColumns = getColumns([
    {
      title: '匹配失败原因',
      dataIndex: 'reason',
    },
  ]);

  const fetchData = async () => {
    const { data } = await LifeCycleApi.getTableCycleInfo(id);
    setList(data);    
  };

  const matchedList = useMemo(() => {
    return list?.filter(item => item.isCurrent);
  }, [list]);
  
  const unMatchedList = useMemo(() => {
    return list?.filter(item => !item.isCurrent);
  }, [list]);

  useEffect(() => {
    fetchData();
  }, [id]);

  const gotoView = record => {
    window.open(`/life-cycle/strategy/list/?name=${record.name}`);    
  };

  return <Modal open={open} width={960} footer={null} onCancel={onCancel} title="生命周期策略"
    styles={{ body: { padding: '4px 24px 24px' } }}
  >
    <SectionCaptain title='当前策略'/>
    <div className='overflow-hidden mt-2 flex flex-col' style={{ maxHeight: '400px' }}>
      <CustomTable
        className='h-full'
        size='small'
        rowKey='id'
        columns={matchedColumns}
        dataSource={matchedList}
        pagination={false}
        scroll={{ x: '900px', y: 300 }}
      />
    </div>
    <SectionCaptain title='匹配失败策略'/>
    <div className='overflow-hidden mt-2 flex flex-col' style={{ maxHeight: '400px' }}>
      <CustomTable
        className='h-full'
        size='small'
        rowKey='id'
        columns={unMatchedColumns}
        dataSource={unMatchedList}
        pagination={pagination}
        onChange={handleTableChange}
        scroll={{ x: '900px', y: 300 }}
      />
    </div>
  </Modal>;
};
