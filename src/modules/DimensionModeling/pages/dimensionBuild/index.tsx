import { useEffect, useRef, useState } from 'react';
import { Button, Space, Tag, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { history } from 'umi';

import { CustomTable, KeepAliveToTab, useCustomTableHook, useKeepAliveTabs } from '@/components';
import { TableType } from '@/constants';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { RELATION_OBJ_TYPE_NAME } from '@/modules/Dependencies/constants';
import { getTableTypeRoutePath } from '@/modules/DimensionModeling/constants';
import { useReloadModelTree } from '@/modules/DimensionModeling/hooks';
import { ModelTableEntityDetail } from '@/modules/DimensionModeling/models';
import { useLayoutStore } from '@/modules/DimensionModeling/stores/useLayoutStore';
import { usePanelStore } from '@/modules/DimensionModeling/stores/usePanelStore';
import { isTable } from '@/modules/DimensionModeling/utils';
import { TableApi } from '@/services';

import { SearchForm } from './components';

type ModelNode = DIMENSION_MODELING.ModelNode;

export const _DimensionBuild = () => {
  const { closeTab } = useKeepAliveTabs();
  const { hasRights } = useRightsHook();
  const reloadModelTree = useReloadModelTree(true);
  const nodeId = useLayoutStore(state => state.nodeId);
  const nodeType = useLayoutStore(state => state.nodeType);
  const debouncedRef = useRef<any>();
  const { currentCatalog } = usePanelStore();
  const confirmDelete = useDeleteConfirm({
    prefix: '',
    delUrl: '',
  });
  const {
    pagination,
    queryParams,
    selectedRowKeys,
    filter,
    setTotal,
    setFilter,
    setSelectedRows,
    setSelectedRowKeys,
    handleTableChange,
  } = useCustomTableHook({
    pageSize: 20,
    cacheId: 'dimensionBuild',
    filter: {
      dimension: false,
    },
  });

  const [list, setList] = useState<ModelTableEntityDetail[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ColumnsType<ModelTableEntityDetail> = [
    {
      title: '中文名称（表名中文）',
      dataIndex: 'name',
      render: (value, record) => (
        <Button type='link' size='small' className='max-w-full' onClick={() => onClick({ data: record })}>
          <Tooltip title={value ?? record?.tbAlias}>
            <div className='w-full overflow-hidden whitespace-nowrap text-ellipsis'>{value ?? record?.tbAlias}</div>
          </Tooltip>
        </Button>
      ),
    },
    {
      title: '英文缩写（表名）',
      dataIndex: 'code',
      render: (value, record) => <span>{value ?? record?.tbName}</span>,
    },
    {
      title: '标签',
      dataIndex: 'tags',
      render: value => (
        <span>
          {value?.map(item => (
            <Tag key={item}>{item}</Tag>
          ))}
        </span>
      ),
    },
    {
      title: '数仓分层',
      dataIndex: 'layerName',
    },
    {
      title: '集市/主题',
      dataIndex: 'martName',
      render: (_, record) => <span>{`${record?.martName ?? '-'}/${record?.subjName ?? '-'}`}</span>,
    },
    {
      title: '表名',
      dataIndex: 'tbName',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type='link'
            size='small'
            onClick={() => handleClone(record)}
            disabled={!hasRights('data_model:write')}
          >
            克隆
          </Button>
          <Button
            disabled={!hasRights('data_model:write', record?.projectAuth)}
            type='link'
            size='small'
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const onClick = node => {
    const modelNode = node?.data as ModelNode;
    modelNode.nodeType = modelNode.tbType;

    if (!isTable(modelNode)) {
      return;
    }
    const { nodeType, id } = modelNode;
    const path = getTableTypeRoutePath(nodeType as keyof typeof TableType, 'edit');
    history.push(`${path}/${id}${nodeType === 'OBJ' ? '?tbType=OBJ' : ''}`, {
      title: modelNode.name,
      catalog: currentCatalog,
      icon: node?.icon?.props?.className,
    });
  };

  const handleDelete = modelData => {
    const { id, tbType, name, tbAlias, tbName } = modelData;
    const isDimension = tbType === 'DIMENSION';
    confirmDelete(id, `确定删除${RELATION_OBJ_TYPE_NAME[tbType] ?? '模型'}「${tbAlias ?? tbName ?? name}」`, {
      delUrl: isDimension ? '/api/v2/data/modeling/dimension' : '/api/v2/data/modeling/table',
      relyUrl: isDimension ? '/api/v2/data/modeling/dimension' : '/api/v2/data/modeling/table',
    }).then(() => {
      // 删除后，关闭右侧对应页签
      const path = getTableTypeRoutePath(tbType, 'edit');
      closeTab(`${path}/${id}`);
      // 刷新左侧树
      reloadModelTree();
    });
  };

  const handleClone = modelData => {
    const { id, tbType, name, tbAlias, tbName } = modelData;
    const path = getTableTypeRoutePath(tbType, 'clone');
    history.push(`${path}/${id}`, {
      title: `${tbAlias ?? tbName ?? name}_copy`,
    });
  };

  useEffect(() => {
    if (nodeType && nodeId) {
      switch (nodeType) {
      case 'businessCategory':
        setFilter({
          ...filter,
          martId: undefined,
          domId: undefined,
          bizId: nodeId,
        });
        break;
      case 'dataDomain':
        setFilter({
          ...filter,
          martId: undefined,
          bizId: undefined,
          domId: nodeId,
        });
        break;
      case 'dataMart':
        setFilter({
          ...filter,
          domId: undefined,
          bizId: undefined,
          martId: nodeId,
        });
        break;
      default:
        break;
      }
    }
  }, [nodeId, nodeType]);

  const fetchData = async () => {
    setLoading(true);
    const res = await TableApi.getTableList(queryParams).finally(() => {
      setLoading(false);
    });
    if (res?.code === '0000') {
      setTotal(res?.total ?? 0);
      setList(res?.data ?? []);
    }
  };

  useEffect(() => {
    if (debouncedRef.current) clearTimeout(debouncedRef.current);
    debouncedRef.current = setTimeout(() => {
      fetchData();
    }, 300);
  }, [queryParams]);

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      <SearchForm filter={filter} setFilter={setFilter} fetchData={fetchData} selectedRowKeys={selectedRowKeys} />

      <div className='flex-1 overflow-hidden'>
        <CustomTable
          dataSource={list}
          loading={loading}
          columns={columns}
          onChange={handleTableChange}
          pagination={pagination}
          scroll={{ x: 1400 }}
          rowSelection={{
            selectedRowKeys,
            onChange: (rowKeys, rows) => {
              setSelectedRows(rows);
              setSelectedRowKeys(rowKeys as string[]);
            },
          }}
        />
      </div>
    </div>
  );
};

export const DimensionBuild = () => {
  return (
    <KeepAliveToTab>
      <_DimensionBuild />
    </KeepAliveToTab>
  );
};
