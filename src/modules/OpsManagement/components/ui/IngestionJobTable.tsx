import { ForwardedRef, forwardRef, useEffect, useImperativeHandle } from 'react';
import { Button, Tag } from 'antd';
import cs from 'classnames';
import { Link, useNavigate } from 'umi';

import { CustomTable } from '@/components/business/CustomTable';
import { SortType } from '@/constants';
import { usePagination, useRightsHook } from '@/hooks';
import { DATA_INGESTION_URL_PREFIX } from '@/modules/DataIngestion/constants';
import { ingestionTypeToLowerCase } from '@/modules/DataIngestion/utils';
import { useIngestionJobList, useStartIngestionJob, useStopIngestionJob } from '@/modules/OpsManagement/hooks';

import { IngestionJobStatusEnum, IngestionTypeEnum } from '../../constants';
import { getStatus } from '../../utils';

import { CellStatus } from './CellStatus';

const getIngestionNavigateUrl = ({ ingestionType, ingestionCategory, id }) => {
  const basePath = `${DATA_INGESTION_URL_PREFIX}/${ingestionCategory}/${ingestionTypeToLowerCase(ingestionType)}`;
  const distPath = id ? `${basePath}/${id}` : basePath;
  return distPath;
};

export interface JobRef {
  refreshList: () => void;
}

interface Props {
  filter?: OPS_MANAGEMENT.INGESTION_JOB.Form;
}

const JobTable = ({ filter = {} }: Props, ref: ForwardedRef<JobRef>) => {
  const { hasRights } = useRightsHook();
  const permissionCode = 'collection_job:operate';

  const navigation = useNavigate();
  const pagination = usePagination({
    current: 1,
    pageSize: 50,
  });
  // const { updateGlobalState } = useJobDetailInfo()

  useEffect(() => {
    pagination.onChange(1, 50);
  }, [filter]);

  const { ingestionJobList, isLoading, total, refetch } = useIngestionJobList({
    filter,
    sort: { updateTime: SortType.descend },
    page: pagination.current! - 1,
    size: pagination.pageSize,
  });

  useImperativeHandle(
    ref,
    () => {
      return {
        refreshList: refetch,
      };
    },
    [filter],
  );

  const startIngestionJob = useStartIngestionJob();
  const stopIngestionJob = useStopIngestionJob();

  // const totalPage = Math.ceil(total / pagination.pageSize!);

  const INGENSTION_COLUMN = [
    {
      title: '任务名称',
      dataIndex: 'name',
      width: 300,
      render(name, record) {
        const { ingestionId, ingestionType } = record;
        const url = getIngestionNavigateUrl({
          ingestionType,
          id: ingestionId,
          ingestionCategory: 'online',
        });

        return (
          <Link to={url} state={{ title: name }}>
            {name}
          </Link>
        );
      },
    },
    {
      title: '业务流程',
      dataIndex: 'businessFlowName',
      width: 250,
    },
    {
      title: '类型',
      width: 130,
      dataIndex: 'ingestionType',
      render: (value: OPS_MANAGEMENT.INGESTIONS_TYPE) => {
        return <span>{IngestionTypeEnum[value]}</span>;
      },
    },
    {
      title: '采集网关',
      dataIndex: 'cellName',
      width: 200,
      render: (value: string) => {
        return <Tag>{value}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 130,
      render: (value: OPS_MANAGEMENT.IngestionJobStatus, record: OPS_MANAGEMENT.INGESTION_JOB.Item) => {
        const { taskStatusStatistics } = record;
        const filedStatus = JSON.parse(taskStatusStatistics);
        return (
          <CellStatus status={value} failed={Object.values(filedStatus || {})?.[0]}>
            {IngestionJobStatusEnum[value]}
          </CellStatus>
        );
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'opt',
      fixed: 'right',
      width: 200,
      render: (_value: unknown, record: OPS_MANAGEMENT.INGESTION_JOB.Item) => {
        const handleStartClick = () => {
          if (record.status === 'StartSucceeded') {
            stopIngestionJob(record.id).then(res => {
              res && refetch();
            });
          } else if (record.status === 'Starting' || record.status === 'Stopping') {
            return false;
          } else {
            startIngestionJob(record.id).then(res => {
              res && refetch();
            });
          }
        };
        const handleDetail = () => {
          navigation(`/ops/ingestion/cell-agent/detail/${record.id}`);
          // updateGlobalState({
          //   ...record,
          //   title: record.name,
          //   status: getIngestionStatus[record.status],
          //   statusTitle: IngestionJobStatusEnum[record.status]
          // })
        };
        const handleJobPM = () => {
          navigation(`/ops/ingestion/cell-agent/job-performance-monitor/${record.id}`);
        };

        return (
          <div className='flex gap-x-2'>
            <Button
              type='link'
              size='small'
              disabled={!hasRights(permissionCode, record?.projectAuth)}
              className={cs({
                'cursor-not-allowed': record.status === 'Starting' || record.status === 'Stopping',
              })}
              onClick={handleStartClick}
            >
              {getStatus(record.status)}
            </Button>
            <Button type='link' size='small' onClick={handleDetail}>
              任务详情
            </Button>
            <Button type='link' size='small' onClick={handleJobPM}>
              性能监控
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <CustomTable
      loading={isLoading}
      rowKey='id'
      cacheId={'IngestionJobTable'}
      pagination={{
        ...pagination,
        total,
      }}
      scroll={{ x: 1200 }}
      resizable
      dataSource={ingestionJobList as any[]}
      columns={INGENSTION_COLUMN}
    />
  );
};

export const IngestionJobTable = forwardRef(JobTable);
