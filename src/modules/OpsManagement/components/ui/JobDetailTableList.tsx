import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import cs from 'classnames';
import moment from 'moment';
import { useNavigate } from 'umi';

import { CustomTable } from '@/components';
import { byteConvert } from '@/components/visual/helper';
import { SortType } from '@/constants';
import { usePagination } from '@/hooks';
import { AGENT_LABEL_VALUE, connectionStatus, IngestionTaskStatusEnum } from '@/modules/OpsManagement/constants';
import { useAgentInfo, useIngestionJobAgentList } from '@/modules/OpsManagement/hooks';
import { getDataSoure } from '@/modules/OpsManagement/utils';

import { AgentInfoModal } from './AgentInfoModal';
import { CellStatus } from './CellStatus';
import { ViewConfigModal } from './ViewConfigModal';
import { ViewLogModal } from './ViewLogModal';

interface Props {
  filter?: OPS_MANAGEMENT.INGESTION_JOB_TASK.Form;
}

export interface TaskRef {
  refetch: () => void;
}

const DetailTableList = ({ filter = {} }: Props, ref: ForwardedRef<TaskRef>) => {
  const [showModal, setShowModal] = useState({
    agentModal: false,
    configModal: false,
    logModal: false,
  });
  const [currentAgent, setCurrentAgent] = useState<OPS_MANAGEMENT.INGESTION_JOB_TASK.AgentInfo>();
  const navigation = useNavigate();

  const { current, pageSize, onChange, onShowSizeChange } = usePagination({
    current: 1,
    pageSize: 50,
  });

  const { agentInfo } = useAgentInfo({
    cellId: currentAgent?.cellId,
    agentId: currentAgent?.agentId,
  });

  const { ingestionJobTaskList, isLoading, total, refetch } = useIngestionJobAgentList({
    filter,
    sort: { updateTime: SortType.descend },
    page: (current ?? 1) - 1,
    size: pageSize,
  });

  useImperativeHandle(ref, () => ({ refetch }), [ref]);

  useEffect(() => {
    onChange(1, 50);
  }, [filter]);

  const handleClose = () => {
    setShowModal({
      agentModal: false,
      configModal: false,
      logModal: false,
    });
  };
  const dataToLabelValue = (data: any) => {
    const newData = { ...data, ...data?.profile };
    const labevaue = AGENT_LABEL_VALUE.map(item => {
      if (item.value === 'connection') {
        const value = newData[item.value] as 0 | 1 | 2;
        return {
          ...item,
          value: (
            <span
              className={cs({
                'text-primary': value === 0,
                'text-danger': value === 2,
                'text-success': value === 1,
              })}
            >
              {connectionStatus[value]}
            </span>
          ),
        };
      }
      if (item.value === 'status') {
        const value: OPS_MANAGEMENT.IngestionJobTaskStatus = newData[item.value];
        return {
          ...item,
          value: <span className='text-primary'>{IngestionTaskStatusEnum[value]}</span>,
        };
      }
      if (item.value === 'lastHbTime') {
        const value = newData[item.value];
        return {
          ...item,
          value: `${moment(value).format('YYYY-MM-DD HH:mm:ss')}`,
        };
      }
      if (item.value === 'cpu') {
        return { ...item, value: `${newData[item.value]}core` };
      }
      if (item.value === 'memory') {
        return { ...item, value: `${newData[item.value]}MB` };
      }
      if (item.value === 'bandwidth') {
        return {
          ...item,
          value: `单进程网络 ${byteConvert(Number(newData[item.value]))?.display}/s`,
        };
      }
      const value = newData[item.value];
      return { ...item, value };
    });
    return labevaue;
  };

  const totalPage = Math.ceil(total / pageSize!);

  const INGENSTION_COLUMN = [
    {
      title: '代理主机',
      dataIndex: 'hostname',
      width: 150,
    },
    {
      title: '代理IP',
      dataIndex: 'ip',
      width: 150,
    },
    {
      title: '操作系统',
      dataIndex: 'os',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (value: OPS_MANAGEMENT.IngestionJobTaskStatus) => {
        return (
          <CellStatus warningTips={false} status={value}>
            {IngestionTaskStatusEnum[value]}
          </CellStatus>
        );
      },
    },
    {
      title: '采集器状态',
      dataIdex: 'connection',
      width: 100,
      render: (rec: any) => {
        const value = rec?.connection;
        const mapStatus = new Map([
          [0, '初始化'],
          [1, '健康'],
          [2, '离线'],
        ]);
        return (
          <span
            className={cs({
              'text-success': value === 1,
              'text-primary': value === 0,
              'text-gray-5': value === 2,
            })}
          >
            {mapStatus.get(value)}
          </span>
        );
      },
    },
    {
      title: '最后修改时间',
      dataIndex: 'updateTime',
      width: 200,
      render: (value: number) => {
        return <span>{moment(value).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
    {
      title: '操作',
      dataIndex: 'opt',
      fixed: 'right',
      width: 300,
      render: (_value: unknown, record: OPS_MANAGEMENT.INGESTION_JOB_TASK.AgentInfo) => {
        const handleAgentInfo = () => {
          setShowModal(pre => ({
            ...pre,
            agentModal: true,
          }));
        };
        const handleViewConfig = () => {
          setShowModal(pre => ({
            ...pre,
            configModal: true,
          }));
        };
        const handlePM = () => {
          const { agentId, cellId, process } = record;
          navigation(`/ops/ingestion/cell-agent/task-performance-monitor/${cellId}/${agentId}/${process}`);
        };
        const handleViewLog = () => {
          setShowModal(pre => ({
            ...pre,
            logModal: true,
          }));
        };

        const handleClick = () => {
          setCurrentAgent(record);
        };

        return (
          <div className='flex gap-5 text-primary cursor-pointer' onClick={handleClick}>
            <span onClick={handleAgentInfo}>代理信息</span>
            <span onClick={handleViewConfig}>查看配置</span>
            <span onClick={handlePM}>性能监控</span>
            <span onClick={handleViewLog}>查看日志</span>
          </div>
        );
      },
    },
  ];

  return (
    <div className='flex-1 overflow-hidden'>
      <CustomTable
        loading={isLoading}
        rowKey='ip'
        scroll={{ x: 1000 }}
        pagination={{
          current,
          pageSize,
          total,
          onChange,
          onShowSizeChange,
        }}
        dataSource={getDataSoure(ingestionJobTaskList)}
        columns={INGENSTION_COLUMN}
      />
      {showModal.agentModal && (
        <AgentInfoModal open={showModal.agentModal} onClose={handleClose} data={dataToLabelValue(agentInfo)} />
      )}
      {showModal.logModal && <ViewLogModal open={showModal.logModal} onClose={handleClose} config={currentAgent} />}
      {showModal.configModal && (
        <ViewConfigModal open={showModal.configModal} onClose={handleClose} congfigData={currentAgent} />
      )}
    </div>
  );
};

export const JobDetailTableList = forwardRef(DetailTableList);
