import { useEffect, useState } from 'react';
import { Button, Form } from 'antd';
import { useSearchParams } from 'umi';

import { useLocationState } from '@/hooks';

import {
  BusinessFlowSelect,
  CellSelect,
  IngestionJobName,
  IngestionJobStatusSelect,
  IngestionJobTypeSelect,
} from '../form';

interface Props {
  onSearch: (values: OPS_MANAGEMENT.INGESTION_JOB.Form) => void;
  onReFetch?: () => void;
}
const statusParams = {
  RUNNING: ['StartSucceeded'],
  STOPPED: ['StopSucceeded'],
  ABNORMAL: ['StartFailed', 'StartException', 'StopFailed', 'StopException'],
};

export const IngestionJobSearchForm = ({ onSearch, onReFetch }: Props) => {
  const [form] = Form.useForm();
  const [searchParams] = useSearchParams();
  const statusQuery = searchParams.get('status');
  const { taskName } = useLocationState<{ taskName: string }>();
  const [name, setName] = useState<string>(taskName);
  const businessFlowId = Form.useWatch('businessFlowId', form);
  const ingestionType = Form.useWatch('ingestionType', form);
  const cellId = Form.useWatch('cellId', form);
  const statusList = Form.useWatch('statusList', form);

  useEffect(() => {
    if (statusQuery) {
      form.setFieldValue('statusList', statusParams[statusQuery]);
    }
  }, [statusQuery]);

  useEffect(() => {
    onSearch({
      name,
      businessFlowId,
      ingestionType,
      cellId,
      statusList,
    });
  }, [name, businessFlowId, ingestionType, cellId, statusList]);

  const handleClick = () => {
    onReFetch?.();
  };

  return (
    <Form form={form} className='px-[12px] py-[12px] overflow-hidden'>
      <div className='flex items-center gap-5 flex-wrap'>
        <IngestionJobName onSearch={setName} />
        <BusinessFlowSelect />
        <IngestionJobTypeSelect />
        <CellSelect />
        <IngestionJobStatusSelect />
        <Button type='text' onClick={handleClick} className='px-3'>
          <span className='flex gap-[2px]'>
            <i className='iconfont icon-refresh-line'></i>
          </span>
        </Button>
      </div>
    </Form>
  );
};
