import { Modal, Tabs, TabsProps, Tag } from 'antd';

import {
  COLLECT_TYPE_MAP,
  COLLECTOFFSET_MAP,
  CONFIG_LIST_FIELDS,
  CONFIG_LIST_MAP,
  FIELDDELIMITER_MAP,
  FILTER_ARCHIVE,
  FILTER_CONTENT,
  FILTER_FILE,
  INGESTION_TYPE_CONFIG,
  JDBC_CUSTOM_CONFIG_MAP,
  JDBC_DATA_SOURCE_CONFIG_MAP,
  TRACKING_COLUMN_TYPE_MAP,
} from '@/modules/OpsManagement/constants';
import { useTaskConfigInfo } from '@/modules/OpsManagement/hooks';
import { uuid } from '@/utils';

import { CollectionConfig } from './CollectionConfig';
import { TextWithNumberedLines } from './TextWithNumberedLines';

interface Props {
  open: boolean;
  onClose: () => void;
  congfigData?: OPS_MANAGEMENT.INGESTION_JOB_TASK.AgentInfo;
}

export const ViewConfigModal = ({ open, onClose, congfigData }: Props) => {
  const { configInfo, collectionConfig } = useTaskConfigInfo(congfigData!);
  const dataMapLabelValue = (data: any, LV: any) => {
    if (!data) return [];
    return LV.map((item: any) => {
      if (item.value === 'tagList') {
        const tags = data[item?.value]?.map((itx: string) => {
          return <Tag key={uuid()}>{itx}</Tag>;
        });
        return {
          ...item,
          value: tags,
        };
      }
      if (item.value === 'collectOffSet') {
        const key: 'earliest' | 'latest' = data[item.value];
        return {
          ...item,
          value: COLLECTOFFSET_MAP[key],
        };
      }
      if (item.value === 'fieldDelimiter') {
        const key: 'package' | 'linebase' | 'multiline' = data[item.value];
        return {
          ...item,
          value: FIELDDELIMITER_MAP[key],
        };
      }
      if (item.value === 'syncMode') {
        const key = data[item.value];
        return {
          ...item,
          value: COLLECT_TYPE_MAP[key],
        };
      }
      if (item.value === 'trackingColumnType') {
        const key = data[item.value];
        return {
          ...item,
          value: TRACKING_COLUMN_TYPE_MAP[key],
        };
      }
      const value = data[item.value];
      return { ...item, value };
    });
  };

  const generateConfigListFields = data => {
    const ingestionType = data?.ingestionType ?? 'FILE';
    if (ingestionType !== 'JDBC') {
      return CONFIG_LIST_FIELDS[ingestionType];
    }
    if (data?.setting?.input?.type === 'CUSTOM') {
      return JDBC_CUSTOM_CONFIG_MAP.map(item => item.value).concat(CONFIG_LIST_FIELDS[ingestionType]);
    }
    return JDBC_DATA_SOURCE_CONFIG_MAP.map(item => item.value).concat(CONFIG_LIST_FIELDS[ingestionType]);
  };

  const generateConfigMap = data => {
    const ingestionType = data?.ingestionType ?? 'FILE';
    if (ingestionType !== 'JDBC') {
      return CONFIG_LIST_MAP[ingestionType];
    }
    if (data?.setting?.input?.type === 'CUSTOM') {
      return JDBC_CUSTOM_CONFIG_MAP.concat(CONFIG_LIST_MAP[ingestionType]);
    }
    return JDBC_DATA_SOURCE_CONFIG_MAP.concat(CONFIG_LIST_MAP[ingestionType]);
  };

  const filterCollectionConfig = (data?: OPS_MANAGEMENT.INGESTION_JOB_TASK.INPUT_CONFIG) => {
    const inputConfig: any = data?.setting.input;
    const ingestionType = data?.ingestionType ?? 'FILE';
    const configList = generateConfigListFields(data).reduce((pre, cur) => {
      pre[cur] = data?.setting.input[cur];
      return pre;
    }, {});
    const fliterFile = {
      blacklist: data?.setting.input?.blacklist,
      whitelist: data?.setting.input?.whitelist,
    };
    const innerFliterFile = {
      innerBlackList: data?.setting.input?.innerBlackList,
      innerWhiteList: data?.setting.input?.innerWhiteList,
    };
    const fliterContent = {
      exclude: data?.setting.input?.exclude,
      include: data?.setting.input?.include,
    };
    const unitMap = (value: string) => {
      return value === 'day' ? '天' : '小时';
    };
    const booleanMap = (value: unknown) => {
      if (typeof value === 'boolean') {
        return value ? '打开' : '关闭';
      }
      return value;
    };
    return {
      type: ingestionType,
      list: dataMapLabelValue(configList, generateConfigMap(data)),
      isShowFile: inputConfig?.useWhitelist || inputConfig?.useBlacklist,
      isShowContent: inputConfig?.useExclude || inputConfig?.useInclude,
      filterFile: dataMapLabelValue(fliterFile, FILTER_FILE),
      innerFilterFile: dataMapLabelValue(innerFliterFile, FILTER_ARCHIVE),
      filterContent: dataMapLabelValue(fliterContent, FILTER_CONTENT),
      advanceConfig: INGESTION_TYPE_CONFIG[ingestionType].map((item: string) => {
        if (item === 'ignoreOlderThan') {
          return {
            config: `${inputConfig?.[item]?.num}${unitMap(inputConfig?.[item]?.unit)}`,
          };
        }
        return { config: booleanMap(inputConfig?.[item]) };
      }),
    };
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '配置文件',
      children: (
        <div className='w-full h-[416px] bg-[#282c34] overflow-scroll mt-4'>
          <TextWithNumberedLines text={configInfo ?? ''} />
        </div>
      ),
    },
    {
      key: '2',
      label: '采集配置',
      children: <CollectionConfig configInfo={filterCollectionConfig(collectionConfig) ?? []} />,
    },
  ];
  return (
    <Modal
      open={open}
      className='w-3/4'
      styles={{ body: { padding: '20px 25px' } }}
      title='查看配置'
      onCancel={onClose}
      footer={null}
    >
      <Tabs items={items} defaultActiveKey='1' />
    </Modal>
  );
};
