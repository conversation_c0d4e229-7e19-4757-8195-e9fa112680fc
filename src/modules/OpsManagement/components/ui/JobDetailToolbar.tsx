import { Button, Tag } from 'antd';
import { useNavigate, useParams } from 'umi';

import { Icon } from '@/components';
import { useRightsHook } from '@/hooks';
import { IngestionTypeEnum } from '@/modules/OpsManagement/constants';
import { useIngestionJobById, useStartIngestionJob, useStopIngestionJob } from '@/modules/OpsManagement/hooks';
import { ingestionJobPerformancePathname } from '@/routes/ops-management';

interface Props {
  refetch: () => void;
}

export const JobDetailToolbar = ({ refetch }: Props) => {
  const { hasRights } = useRightsHook();
  const permissionCode = 'collection_job:operate';

  const { id } = useParams();
  const navigation = useNavigate();
  const { job, refetch: refectchJob } = useIngestionJobById(id ?? '');
  const stopIngestionJob = useStopIngestionJob();
  const startIngestionJob = useStartIngestionJob();

  const handleStopJob = () => {
    if (job?.status === 'StopSucceeded') {
      id &&
        startIngestionJob(id).then(res => {
          res && refetch();
        });
    } else {
      id &&
        stopIngestionJob(id).then(res => {
          if (res) {
            refetch();
          }
        });
    }
  };

  const handleClick = () => {
    navigation(`${ingestionJobPerformancePathname}/${id}`);
  };

  const getJobStatus = (status?: string) => {
    return status === 'StopSucceeded';
  };

  const handleRefetch = () => {
    refectchJob();
    refetch();
  };

  return (
    <div className='h-[48px] bg-white flex justify-between  items-center px-[30px]'>
      <div className='flex gap-[70px] text-sm'>
        <span>
          业务流程：
          <span className='text-black'>{job?.businessFlowName}</span>
        </span>
        <span>
          类型：
          <span className='text-black'>{IngestionTypeEnum[job?.ingestionType as OPS_MANAGEMENT.INGESTIONS_TYPE]}</span>
        </span>
        <span>
          <span>采集网关：</span>
          <Tag>{job?.cellName}</Tag>
        </span>
      </div>
      <div className='flex gap-2'>
        <Button
          type='primary'
          danger={!getJobStatus(job?.status)}
          color={getJobStatus(job?.status) ? 'primary' : ''}
          disabled={!hasRights(permissionCode)}
          onClick={handleStopJob}
        >
          <span className='flex items-center gap-2'>
            <Icon className='text-xs' name={getJobStatus(job?.status) ? 'arrow_right-fill' : 'stop-fill1'} />
            <span>{getJobStatus(job?.status) ? '启动' : '停止'}</span>
          </span>
        </Button>
        <Button icon={<Icon name='monitor-line' className='mr-2' />} type='primary' onClick={handleClick}>
          性能监控
        </Button>
        <Button onClick={handleRefetch} className='px-3 '>
          <span className='flex gap-[2px]'>
            刷新
            <i className='iconfont icon-refresh-line'></i>
          </span>
        </Button>
      </div>
    </div>
  );
};
