import { useEffect, useState } from 'react';
import { Form } from 'antd';
import { useParams } from 'umi';

import { JobDetailNameIP } from '../form/JobDetailNameIP';
import { JobDetailStatus } from '../form/JobDetailStatus';

interface Props {
  onSearch: (values: OPS_MANAGEMENT.INGESTION_JOB_TASK.Form) => void;
}

export const JobDetailSearchForm = ({ onSearch }: Props) => {
  const [form] = Form.useForm();
  const [hostnameOrIp, setHostnameOrIp] = useState<string>();
  const { id: ingestionJobId } = useParams();
  const taskStatus = Form.useWatch('taskStatus', form);

  useEffect(() => {
    onSearch({
      hostnameOrIp,
      ingestionJobId,
      taskStatus,
    });
  }, [hostnameOrIp, ingestionJobId, taskStatus]);
  return (
    <Form form={form}>
      <div className='h-[56px] px-3 flex gap-[50px] items-center'>
        <JobDetailNameIP onSearch={setHostnameOrIp} />
        <JobDetailStatus />
      </div>
    </Form>
  );
};
