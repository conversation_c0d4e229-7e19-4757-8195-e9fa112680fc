import { LoadingOutlined, RedoOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Checkbox, Form, Input, Modal, Spin } from 'antd';

import { cellAgentService } from '@/modules/DataIngestion/services';

import { TextWithNumberedLines } from './TextWithNumberedLines';

interface Props {
  open: boolean;
  onClose: () => void;
  config?: OPS_MANAGEMENT.INGESTION_JOB_TASK.AgentInfo;
}

const { getAgentProcessLog } = cellAgentService;

export const ViewLogModal = ({ open, onClose, config }: Props) => {
  const [form] = Form.useForm();
  const lines = Form.useWatch('lines', form);
  const tail = Form.useWatch('tail', form);
  const tabLine = Form.useWatch('tabLine', form);
  const {
    data: linesData,
    refetch,
    isLoading,
  } = useQuery(
    [config?.cellId, config?.agentId, config?.process],
    () =>
      getAgentProcessLog({
        cellId: config?.cellId,
        agentId: config?.agentId,
        process: config?.process,
        lines: lines ?? 1000,
        tail: tail ?? true,
      }),
    {
      enabled: !!config?.cellId && !!config?.agentId && !!config?.process,
    },
  );
  const handleReFetch = () => {
    refetch();
  };
  return (
    <Modal
      open={open}
      className='w-3/4'
      styles={{ body: { padding: '20px 25px' } }}
      title='查看日志'
      onCancel={onClose}
      footer={null}
    >
      <div className='flex flex-col gap-4'>
        <Form form={form} initialValues={{ tail: true, tabLine: true, lines: 1000 }}>
          <div className='flex justify-between'>
            <div className='flex gap-2'>
              <Form.Item name='tail' valuePropName='checked' className='m-0'>
                <Checkbox>最新日志</Checkbox>
              </Form.Item>
              <Form.Item name='tabLine' valuePropName='checked' className='m-0'>
                <Checkbox>换行显示</Checkbox>
              </Form.Item>
            </div>
            <div className='flex gap-2 items-center'>
              <div>
                <Form.Item name='lines' label='行数' className='m-0'>
                  <Input className='text-gray-5' />
                </Form.Item>
              </div>
              <div>
                {isLoading ? (
                  <LoadingOutlined />
                ) : (
                  <RedoOutlined rotate={270} className='text-primary cursor-pointer' onClick={handleReFetch} />
                )}
              </div>
            </div>
          </div>
        </Form>

        <div className='h-[416px] bg-[#282c34] overflow-scroll '>
          {isLoading ? <Spin /> : <TextWithNumberedLines text={linesData} isTabLine={tabLine} />}
        </div>
      </div>
    </Modal>
  );
};
