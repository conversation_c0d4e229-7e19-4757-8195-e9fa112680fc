import { AdvancedConfig } from '@/modules/DataIngestion/components';
// eslint-disable-next-line max-len
import { AdvancedConfig as ArchiveConfig } from '@/modules/DataIngestion/pages/online/Archive/components/AdvancedConfig';
import { AdvancedConfig as AConfig } from '@/modules/DataIngestion/pages/online/TcpUdp/components/AdvancedConfig';

import { AngentLabelValue, LabelValueList } from './LabelValueList';
import { MainTitle } from './MainTitle';

interface Props {
  configInfo: {
    type: OPS_MANAGEMENT.INGESTIONS_TYPE;
    isShowFile: boolean;
    isShowContent: boolean;
    list: AngentLabelValue[];
    filterFile: AngentLabelValue[];
    innerFilterFile: AngentLabelValue[];
    filterContent: AngentLabelValue[];
    advanceConfig: Array<{ config: unknown }>;
  };
}

export const CollectionConfig = ({ configInfo }: Props) => {
  return (
    <div className='mt-4'>
      {configInfo?.list.map((item, index) => {
        return (
          <LabelValueList
            key={item?.label}
            label={item.label}
            value={item.value}
            isLast={configInfo?.list?.length - 1 === index}
          />
        );
      })}
      {['FILE', 'ARCHIVE'].includes(configInfo.type) && configInfo.isShowFile && (
        <div>
          <MainTitle title='过滤文件' />
          {configInfo?.filterFile.map((item, index) => {
            return (
              <LabelValueList
                key={item?.label}
                label={item.label}
                value={item.value}
                isLast={configInfo?.filterFile?.length - 1 === index}
              />
            );
          })}
        </div>
      )}
      {['ARCHIVE'].includes(configInfo.type) && configInfo.isShowFile && (
        <div>
          <MainTitle title='过滤子文件' />
          {configInfo?.innerFilterFile.map((item, index) => {
            return (
              <LabelValueList
                key={item?.label}
                label={item.label}
                value={item.value}
                isLast={configInfo?.innerFilterFile?.length - 1 === index}
              />
            );
          })}
        </div>
      )}
      {['FILE', 'SYSLOG', 'ARCHIVE'].includes(configInfo.type) && configInfo.isShowContent && (
        <div>
          <MainTitle title='过滤内容' />
          {configInfo?.filterContent.map((item, index) => {
            return (
              <LabelValueList
                key={item?.label}
                label={item.label}
                value={item.value}
                isLast={configInfo?.filterContent?.length - 1 === index}
              />
            );
          })}
        </div>
      )}
      <div>
        {!['KAFKA', 'JDBC', 'LOGSTASH'].includes(configInfo.type) && <MainTitle title='采集高级配置' />}
        {['FILE'].includes(configInfo.type) && (
          <AdvancedConfig noCollapse readonly configData={configInfo.advanceConfig} />
        )}
        {['ARCHIVE'].includes(configInfo.type) && (
          <ArchiveConfig noCollapse readonly configData={configInfo.advanceConfig} />
        )}
        {['SYSLOG', 'TCP_UDP'].includes(configInfo.type) && (
          <AConfig readonly noCollapse configData={configInfo.advanceConfig} />
        )}
      </div>
    </div>
  );
};
