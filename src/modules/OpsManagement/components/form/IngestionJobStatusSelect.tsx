/**
 * @module 采集任务运维-过滤表单-任务状态
 */
import { Form, Select } from 'antd';

import { INGESTION_JOB_STATUS_OPTIONS } from '@/modules/OpsManagement/constants';

export const IngestionJobStatusSelect = () => {
  return (
    <Form.Item name='statusList' label='状态' className='m-0 flex-1'>
      <Select showSearch allowClear mode='multiple' placeholder='请选择' className='status-select'>
        {INGESTION_JOB_STATUS_OPTIONS.map(({ label, value }) => {
          return (
            <Select.Option key={value} value={value}>
              <span className={`status-ck-status ${value} align-middle leading-[22px]`}>{label}</span>
            </Select.Option>
          );
        })}
      </Select>
    </Form.Item>
  );
};
