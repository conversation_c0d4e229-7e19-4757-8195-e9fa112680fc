/**
 * @module 采集任务运维-过滤表单-业务流程
 */
import { Form } from 'antd';

import { Select } from '@/components';
import { BusinessFlowApi } from '@/services';

export const BusinessFlowSelect = () => {
  const fetcher = async () => {
    const { data = [] } = await BusinessFlowApi.getAll();
    return data;
  };

  return (
    <Form.Item name='businessFlowId' label='业务流程' className='m-0 flex-1'>
      <Select<DATA_DEV.BusinessFlowEntity>
        showSearch
        queryKey={[BusinessFlowApi.getAllUrl]}
        queryFunc={fetcher}
        placeholder='请选择'
        toOption={({ name, id }) => {
          return {
            label: name,
            value: id,
          };
        }}
      />
    </Form.Item>
  );
};
