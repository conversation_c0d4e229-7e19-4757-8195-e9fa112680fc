import { useEffect } from 'react';
import { Form } from 'antd';

import { SearchInput } from '@/components';
import { useLocationState } from '@/hooks';

interface Props {
  onSearch: (value: string) => void;
  placeholder?: string;
}

export const IngestionJobName = ({ onSearch }: Props) => {
  const { taskName } = useLocationState<{ taskName: string }>();

  useEffect(() => {
    onSearch(taskName);
  }, []);

  return (
    <Form.Item name='name' className='m-0 flex-1' initialValue={taskName}>
      <SearchInput onSearch={onSearch} placeholder='请输入任务名称' />
    </Form.Item>
  );
};
