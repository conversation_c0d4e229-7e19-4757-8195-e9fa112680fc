import {
  CONFIG_FIELDS_ARCHIVE,
  CONFIG_FIELDS_FILE,
  CONFIG_FIELDS_JDBC,
  CONFIG_FIELDS_KAFKA,
  CONFIG_FIELDS_LOGSTASH,
  CONFIG_FIELDS_SYSLOG,
  CONFIG_FIELDS_TCP_UDP,
} from '@/modules/DataIngestion/constants';

import { COLLECT_CONFIG_MAP } from './options';

export const JOB_DETAILS_MAP = {
  businessFlowName: '业务流程',
  cellName: '采集网关',
  name: '任务名称',
  publishVersion: '版本',
};

export const JOB_TASK_DETAILS_MAP = {
  hostname: '代理主机',
  serverIp: '代理IP',
  'profile.cpu': '代理CPU',
  'profile.memory': '内存总量',
  osAlias: '操作系统',
};

export const INGESTION_TYPE_CONFIG = {
  FILE: CONFIG_FIELDS_FILE,
  ARCHIVE: CONFIG_FIELDS_ARCHIVE,
  TCP_UDP: CONFIG_FIELDS_TCP_UDP,
  SYSLOG: CONFIG_FIELDS_SYSLOG,
  KAFKA: CONFIG_FIELDS_KAFKA,
  JDBC: CONFIG_FIELDS_JDBC,
  LOGSTASH: CONFIG_FIELDS_LOGSTASH,
};

export const FIELDDELIMITER_MAP = {
  package: '按包分割',
  linebase: '按行分割',
  multiline: '多行合并',
};

export const COLLECTOFFSET_MAP = {
  earliest: '全量',
  latest: '首次最新',
};

export const CONFIG_LIST_MAP = COLLECT_CONFIG_MAP;

export const CONFIG_LIST_FIELDS = Object.keys(COLLECT_CONFIG_MAP).reduce((res, cur) => {
  res[cur] = COLLECT_CONFIG_MAP[cur].map(item => item.value);
  return res;
}, {});

export const CK_STATUS_MAP = {
  UNSTART: '未启动',
  STARTING: '启动中',
  START_FAILED: '启动失败',
  STOPPING: '停止中',
  STOP_FAILED: '停止失败',
  STOPPED: '已停止',
  RUNNING: '运行中',
  RUNNING_FAILED: '运行失败',
};

export const COLLECT_TYPE_MAP = {
  whole: '全量模式',
  incremental: '增量模式',
};

export const TRACKING_COLUMN_TYPE_MAP = {
  timestamp: '时间戳',
  numeric: '数值类型',
};
