export const INGESTION_TYPE_OPTIONS = [
  {
    label: '文件和目录',
    value: 'FILE',
  },
  {
    label: '归档文件',
    value: 'ARCHIVE',
  },
  {
    label: 'TCP/UDP',
    value: 'TCP_UDP',
  },

  {
    label: 'SYSLOG',
    value: 'SYSLOG',
  },
  {
    label: 'KAFKA',
    value: 'KAFKA',
  },
  {
    label: '数据库',
    value: 'JDBC',
  },
  {
    label: '自定义',
    value: 'LOGSTASH',
  },
];

export const INGESTION_JOB_STATUS_OPTIONS = [
  {
    label: '启动中',
    value: 'Starting',
  },
  {
    label: '启动成功',
    value: 'StartSucceeded',
  },
  {
    label: '启动失败',
    value: 'StartFailed',
  },
  {
    label: '启动异常',
    value: 'StartException',
  },
  {
    label: '停止中',
    value: 'Stopping',
  },
  {
    label: '停止成功',
    value: 'StopSucceeded',
  },
  {
    label: '停止失败',
    value: 'StopFailed',
  },
  {
    label: '停止异常',
    value: 'StopException',
  },
];
export const JOB_DETAIL_STATUS_OPTIONS = [
  {
    label: '初始化',
    value: 'Init',
  },
  {
    label: '启动中',
    value: 'Starting',
  },
  {
    label: '启动成功',
    value: 'StartSucceeded',
  },
  {
    label: '启动失败',
    value: 'StartFailed',
  },
  {
    label: '停止中',
    value: 'Stopping',
  },
  {
    label: '停止成功',
    value: 'StopSucceeded',
  },
  {
    label: '停止失败',
    value: 'StopFailed',
  },
  {
    label: '删除中',
    value: 'Deleting',
  },
  {
    label: '删除成功',
    value: 'DeleteSucceeded',
  },
  {
    label: '删除失败',
    value: 'DeleteFailed',
  },
  {
    label: '配置不匹配',
    value: 'ConfigMismatch',
  },
  {
    label: '状态不匹配',
    value: 'StateMismatch',
  },
];

export enum IngestionTypeEnum {
  FILE = '文件目录同步',
  ARCHIVE = '归档文件同步',
  TCP_UDP = 'TCP/UDP同步',
  SYSLOG = 'SYSLOG同步',
  KAFKA = 'KAFKA同步',
  JDBC = '数据库同步',
  LOGSTASH = '自定义同步',
}

export enum IngestionJobStatusEnum {
  Starting = '启动中',
  StartSucceeded = '启动成功',
  StartFailed = '启动失败',
  StartException = '启动异常',
  Stopping = '停止中',
  StopSucceeded = '停止成功',
  StopFailed = '停止失败',
  StopException = '停止异常',
  Deleting = '删除中',
  DeleteSucceeded = '删除成功',
  DeleteFailed = '删除失败',
  DeleteException = '删除异常',
}
export const getIngestionStatus = {
  Starting: 'loading',
  StartSucceeded: 'success',
  StartFailed: 'error',
  StartException: 'warning',
  Stopping: 'loading',
  StopSucceeded: 'delete',
  StopFailed: 'error',
  StopException: 'warning',
  Deleting: 'loading',
  DeleteSucceeded: 'delete',
  DeleteFailed: 'error',
  DeleteException: 'warning',
};

export const TASK_STATUS_MAP = {
  Init: 'loading',
  Starting: 'loading',
  StartSucceeded: 'success',
  StartFailed: 'error',
  Stopping: 'loading',
  StopSucceeded: 'delete',
  StopFailed: 'error',
  Deleting: 'loading',
  DeleteSucceeded: 'delete',
  DeleteFailed: 'error',
  ConfigMismatch: 'warning',
  StateMismatch: 'warning',
};

export enum IngestionTaskStatusEnum {
  Init = '初始化',
  Starting = '启动中',
  StartSucceeded = '运行中',
  StartFailed = '启动失败',
  Stopping = '停止中',
  StopSucceeded = '停止成功',
  StopFailed = '停止失败',
  Deleting = '删除中',
  DeleteSucceeded = '删除成功',
  DeleteFailed = '删除失败',
  ConfigMismatch = '配置不匹配',
  StateMismatch = '状态不匹配',
}

export const AGENT_LABEL_VALUE = [
  { label: 'id', value: 'agentId' },
  { label: '版本', value: 'version' },
  { label: '主机', value: 'hostname' },
  { label: 'IP', value: 'ip' },
  { label: '操作系统', value: 'osAlias' },
  { label: '操作系统版本', value: 'os' },
  { label: 'MAC地址', value: 'mac' },
  { label: '采集器状态', value: 'connection' },
  { label: '采值状态', value: 'status' },
  { label: '上次心跳时间', value: 'lastHbTime' },
  { label: 'CPU', value: 'cpu' },
  { label: '内存', value: 'memory' },
  { label: '带宽限额', value: 'bandwidth' },
];

export const COLLECT_CONFIG_MAP = {
  FILE: [
    { label: '路径', value: 'path' },
    { label: '数据标签', value: 'tagList' },
    { label: '字符集', value: 'charset' },
    { label: '多行合并', value: 'multiline' },
  ],
  ARCHIVE: [
    { label: '路径', value: 'path' },
    { label: '数据标签', value: 'tagList' },
    { label: '字符集', value: 'charset' },
    { label: '多行合并', value: 'multiline' },
  ],
  TCP_UDP: [
    { label: '监听端口', value: 'port' },
    { label: '数据标签', value: 'tagList' },
    { label: '协议', value: 'protocol' },
    { label: '字符集', value: 'charset' },
    { label: '分割方式', value: 'fieldDelimiter' },
    { label: '多行合并', value: 'multiline' },
  ],
  SYSLOG: [
    { label: '监听端口', value: 'port' },
    { label: '协议', value: 'protocol' },
    { label: '字符集', value: 'charset' },
    { label: '编码规范', value: 'standard' },
  ],
  KAFKA: [
    { label: '数据标签', value: 'tagList' },
    { label: '数据源', value: 'dsName' },
    { label: '主题', value: 'topicName' },
    { label: '缓存策略', value: 'collectOffSet' },
  ],
  JDBC: [
    { label: '采集方式', value: 'syncMode' },
    { label: '同步关键字类型', value: 'trackingColumnType' },
    { label: '同步关键字', value: 'trackingColumn' },
    { label: 'SQL语句', value: 'sql' },
    { label: '采集周期', value: 'cron' },
    { label: '数据标签', value: 'tagList' },
  ],
  LOGSTASH: [
    { label: '输入配置', value: 'input' },
    { label: '过滤配置', value: 'filter' },
    { label: '输出配置', value: 'output' },
  ],
};

export const JDBC_DATA_SOURCE_CONFIG_MAP = [
  { label: '数据源', value: 'datasourceName' },
  { label: '数据库名', value: 'database' },
];

export const JDBC_CUSTOM_CONFIG_MAP = [
  { label: 'JDBC URL', value: 'jdbcUrl' },
  { label: 'JDBC Driver', value: 'jdbcDriver' },
  { label: 'JDBC JAR', value: 'jdbcJar' },
];

export const FILTER_FILE = [
  { label: '文件黑名单', value: 'blacklist' },
  { label: '文件白名单', value: 'whitelist' },
];
export const FILTER_ARCHIVE = [
  { label: '子文件黑名单', value: 'innerBlackList' },
  { label: '子文件白名单', value: 'innerWhiteList' },
];
export const FILTER_CONTENT = [
  { label: '仅包含内容', value: 'include' },
  { label: '排除内容', value: 'exclude' },
];

export const INPUT_CONFIG = [
  { label: 'kafka模型', value: 'kafkaTdName' },
  { label: '消费策略', value: 'strategy' },
  { label: '消费组ID', value: 'consumeGroup' },
];
export const OUTPUT_CONFIG = [
  { label: '写入模式', value: 'writeMode' },
  { label: 'Clickhouse模型', value: 'ckTdName' },
  { label: '自动字段映射', value: 'enableAutoSchema' },
  { label: '动态字段适应', value: 'enableDynamicSchema' },
  { label: '文件黑名单', value: 'blacklist' },
  { label: '文件白名单', value: 'whitelist' },
  { label: '最大字段数', value: 'maxDims' },
  { label: 'Sharding key', value: 'shardingKey' },
  { label: '指标维度表', value: 'dimTdName' },
  { label: '指标值表', value: 'idxTdName' },
];
export const RUNNING_CONFIG = [
  { label: '存储集群', value: 'storageClusterName' },
  { label: '批写入最大延迟', value: 'flushInterval' },
  { label: '最大批次大小', value: 'batchBufferSize' },
];

export const SHARDINGKEY = [{ label: 'Sharding key', value: 'shardingKey' }];

export const ADVANCE_CONFIG = [{ label: 'KV', value: 'runningSettingMap' }];

export const connectionStatus = {
  0: '初始化',
  1: '健康',
  2: '离线',
};

export const CKTORAGE_TASK_STATUS_OPTIONS: Array<LabelValue<OPS_MANAGEMENT.STORAGE.CK.Status>> = [
  {
    label: '未启动',
    value: 'UNSTART',
  },
  {
    label: '启动中',
    value: 'STARTING',
  },
  {
    label: '启动失败',
    value: 'START_FAILED',
  },
  {
    label: '停止中',
    value: 'STOPPING',
  },
  {
    label: '停止失败',
    value: 'STOP_FAILED',
  },
  {
    label: '已停止',
    value: 'STOPPED',
  },
  {
    label: '运行中',
    value: 'RUNNING',
  },
  {
    label: '运行失败',
    value: 'RUNNING_FAILED',
  },
];

export const SCHEMA_COLUMN = [
  {
    title: '模型字段',
    dataIndex: 'tableName',
  },
  {
    title: '模型字段类型',
    dataIndex: 'tableNameType',
  },
  {
    title: 'kafka字段类型',
    dataIndex: 'kafkaNameType',
  },
  {
    title: 'kafka字段',
    dataIndex: 'kafkaName',
  },
];
