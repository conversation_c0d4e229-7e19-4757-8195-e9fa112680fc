import { JOB_DETAILS_MAP, JOB_TASK_DETAILS_MAP } from './constants';

export const getStatus = (status: string) => {
  const startStatus = [
    'StartFailed',
    'StartException',
    'StopSucceeded',
    'StopFailed',
    'StopException',
    'Deleting',
    'DeleteSucceeded',
    'DeleteFailed',
    'DeleteException',
  ];
  if (startStatus.includes(status)) {
    return '启动';
  }
  if (status === 'Starting') {
    return '启动中';
  }
  if (status === 'Stopping') {
    return '停止中';
  }
  if (status === 'StartSucceeded') {
    return '停止';
  }
};

export const getDataSoure = (data: any) => {
  const arr: any[] = [];
  data.map((item: any) => {
    arr.push({
      ingestionId: item.ingestionId,
      cellId: item.cellId,
      ...item.agent,
      ...item.task,
    });
  });
  return arr;
};

export const getJobDetailsForPerformance = (job: OPS_MANAGEMENT.INGESTION_JOB.Item) => {
  return Object.entries(JOB_DETAILS_MAP).reduce((acc: LabelValueOptions, [key, label]) => {
    acc.push({
      label,
      value: job[key as keyof typeof job],
    });
    return acc;
  }, []);
};

export const getTaskDetailsForPerformance = (task: OPS_MANAGEMENT.INGESTION_JOB_TASK.AgentInfo) => {
  return Object.entries(JOB_TASK_DETAILS_MAP).reduce((acc: LabelValueOptions, [key, label]) => {
    const pathes = key.split('.');
    acc.push({
      label,
      value: pathes.length === 1 ? task[pathes[0]] : task[pathes[0]][pathes[1]],
    });
    return acc;
  }, []);
};

export function getBucket(minMilliseconds: number, maxMilliseconds: number) {
  const seconds = Math.floor((maxMilliseconds - minMilliseconds) / 1000);
  return Math.max(Math.floor(seconds / 250), 1);
}
