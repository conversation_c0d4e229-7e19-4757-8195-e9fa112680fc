import { useMutation } from '@tanstack/react-query';

import { useQuery, useQueryAllList } from '@/hooks';
import { ingestionService, QueryAllJobs, QueryAllJobTasks } from '@/modules/DataIngestion/services';

export function useIngestionJobList(props: QueryParams<QueryAllJobs>) {
  const { list, ...result } = useQueryAllList({
    ...props,
    fetcher: ingestionService.queryAllIngestionJobs,
  });
  return { ...result, ingestionJobList: list };
}

export function useIngestionJobAgentList(props: QueryParams<QueryAllJobTasks>) {
  const { list, ...result } = useQueryAllList({
    ...props,
    fetcher: ingestionService.getJobTaskList,
  });
  return { ...result, ingestionJobTaskList: list };
}

/**
 * @returns typeof useStartIngestionJob
 */
export function useStartIngestionJob() {
  const startIngestionJob = useMutation((id: string) => {
    return ingestionService.startIngestionJob(id);
  });

  return (id: string) => {
    return startIngestionJob.mutateAsync(id);
  };
}

/**
 * @returns typeof useStopIngestionJob
 */
export function useStopIngestionJob() {
  const stopIngestionJob = useMutation((id: string) => {
    return ingestionService.stopIngestionJob(id);
  });

  return (id: string) => {
    return stopIngestionJob.mutateAsync(id);
  };
}

/**
 * @returns typeof useIngestionJobById
 */
export function useIngestionJobById(id: string) {
  const { data, isLoading, refetch } = useQuery({
    queryKey: [ingestionService.getIngestionJobByIdUrl(id)],
    queryFunc: () => ingestionService.getIngestionJobById(id),
    queryOptions: {
      enabled: !!id,
    },
  });

  return { job: data, isLoading, refetch };
}
