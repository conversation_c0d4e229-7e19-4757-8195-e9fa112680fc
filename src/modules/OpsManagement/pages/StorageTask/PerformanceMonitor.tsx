import { useMemo } from 'react';
import { useParams } from 'umi';

import { CKPerformanceMonitor } from './CK';

export const StoragePerformanceMonitor = () => {
  const { type } = useParams();

  // const getPerformanceMonitor = cond([
  //   [equals('ck'), always(CKPerformanceMonitor)]
  // ]);
  // TODO: 之前写法只判断了一个ck的条件，如果 !== ck不做任何判断？
  const getPerformanceMonitor = (type: string) => {
    switch (type) {
    case 'ck':
      return CKPerformanceMonitor;
    default:
      return CKPerformanceMonitor;
    }
  };

  const Component = useMemo(() => getPerformanceMonitor(type as string), [type]);

  return <Component />;
};
