import React, { useEffect, useState } from 'react';
import { LineOptions } from '@antv/g2plot';
import { Empty } from 'antd';
import { useParams } from 'umi';

import { ChartWrapper } from '@/components/visual/chart/ChartWrapper';
import { LineChart } from '@/components/visual/chart/LineChart';
import { StorageCkApi } from '@/services';

interface Props {
  range: string[];
  isRealTime: boolean;
  width: number;
}

export const CpuUsageChart: React.FC<Props> = props => {
  const { range, isRealTime, width } = props;

  const metricName = 'cpu';
  const { id } = useParams();
  const [data, setData] = useState([]);

  const fetchData = async () => {
    if (!range) return;
    if (width <= 1) return;
    const [beginTime, endTime] = range;
    const { data } = await StorageCkApi.getMetric({
      id: id as string,
      beginTime,
      endTime,
      metricName,
      width,
    });
    let val = [];
    data.forEach(({ task, appId, values, ...rest }) => {
      val = val.concat(
        values.map(([time, y]) => {
          return {
            x: time * 1000,
            y: Number(y),
            category: JSON.stringify(rest).replace(/"/gi, ' '),
          };
        }),
      );
    });

    setData(val);
  };

  useEffect(() => {
    fetchData();
  }, [range, width]);

  const options: Partial<LineOptions> = {
    isStack: false,
    seriesField: 'category',
    yAxis: {
      label: {
        formatter: val => {
          return `${Number(val)?.toFixed(2)}%`;
        },
      },
      tickMethod(scale: ScaleConfig) {
        let { min, max } = scale;
        if (max < 1) {
          max = 1;
        }
        const gap = ((max - min) * 100) / 500;
        const ticks: number[] = [];
        for (let i = 0; i < 6; i++) {
          ticks.push(min + gap * i);
        }
        return ticks;
      },
    },
    tooltip: {
      formatter: data => {
        return {
          name: data.category,
          value: `${Number(data.y)?.toFixed(2)}%`,
        };
      },
    },
  };

  return (
    <ChartWrapper
      displayName={
        <span className='text-gray-5'>
          实例<b className='text-gray-6'>CPU</b>使用率
        </span>
      }
    >
      {data.length === 0 ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : isRealTime ? (
        <LineChart key='1' options={options} data={data} range={range} isRealTime={isRealTime} />
      ) : (
        <LineChart key='2' options={options} data={data} range={range} />
      )}
    </ChartWrapper>
  );
};
