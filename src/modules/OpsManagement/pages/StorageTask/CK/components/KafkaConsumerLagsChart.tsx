import React, { useEffect, useState } from 'react';
import { LineOptions } from '@antv/g2plot';
import { Empty } from 'antd';
import { useParams } from 'umi';

import { ChartWrapper } from '@/components/visual/chart/ChartWrapper';
import { LineChart } from '@/components/visual/chart/LineChart';
import { StorageCkApi } from '@/services';
import { formatNumber } from '@/utils';

interface Props {
  range: string[];
  isRealTime: boolean;
  width: number;
}

export const KafkaConsumerLagsChart: React.FC<Props> = props => {
  const { range, isRealTime, width } = props;

  const metricName = 'consumer-lags';
  const { id } = useParams();
  const [data, setData] = useState([]);

  const fetchData = async () => {
    if (!range) return;
    if (width <= 1) return;
    const [beginTime, endTime] = range;
    const { data } = await StorageCkApi.getMetric({
      id: id as string,
      beginTime,
      endTime,
      metricName,
      width,
    });
    let val = [];
    data.forEach(({ appId, task, values, ...rest }) => {
      val = val.concat(
        values.map(([time, y]) => {
          return {
            x: time * 1000,
            y: Number(y),
            category: JSON.stringify(rest).replace(/"/gi, ' '),
          };
        }),
      );
    });

    setData(val);
  };

  useEffect(() => {
    fetchData();
  }, [range, width]);

  const options: Partial<LineOptions> = {
    isStack: false,
    seriesField: 'category',
    yAxis: {
      label: {
        formatter: val => {
          const obj = formatNumber(Number(val));
          return `${Number(obj?.value).toFixed(2)}${obj?.symbol}个`;
        },
      },
      tickMethod(scale: ScaleConfig) {
        let { min, max } = scale;
        if (max === 0) {
          max = 10;
        } else {
          const obj = formatNumber(Number(max * 1.2));
          max = Math.ceil(obj.value) * obj.unit;
        }
        const gap = ((max - min) * 100) / 500;
        const ticks: number[] = [];
        for (let i = 0; i < 6; i++) {
          ticks.push(min + gap * i);
        }
        return ticks;
      },
    },
    tooltip: {
      formatter: data => {
        const obj = formatNumber(Number(data.y));
        return {
          name: data.category,
          value: `${Number(obj?.value)?.toFixed(2)}${obj?.symbol}个`,
        };
      },
    },
  };

  return (
    <ChartWrapper
      displayName={
        <span className='text-gray-5'>
          <b className='text-gray-6'>Kafka</b>消费延迟
        </span>
      }
    >
      {data.length === 0 ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : isRealTime ? (
        <LineChart key='1' options={options} data={data} range={range} isRealTime={isRealTime} />
      ) : (
        <LineChart key='2' options={options} data={data} range={range} />
      )}
    </ChartWrapper>
  );
};
