import { useEffect, useState } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Modal, Select, Space, Spin } from 'antd';
import { Link, useSearchParams } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { StorageClusterSelect } from '@/components/business/form/select/StorageClusterSelect';
import { useRightsHook } from '@/hooks';
import { ingestionService } from '@/modules/DataIngestion/services';
import { CKStorageTaskDetailModal } from '@/modules/OpsManagement/components';
import { CKSTORAGE_TASK_COLUMNS, CKTORAGE_TASK_STATUS_OPTIONS } from '@/modules/OpsManagement/constants';

import './index.less';

export { CKPerformanceMonitor } from './PerformanceMonitor';

export const CK = () => {
  const { hasRights } = useRightsHook();
  const permissionCode = 'storage_job:operate';

  const [isLoading, setIsLoading] = useState(false);
  const [list, setList] = useState([]);
  const [ckId, setCkId] = useState();
  const [searchParams] = useSearchParams();
  const name = searchParams.get('name') || undefined;
  const status = searchParams.get('status');
  const {
    pagination,
    setTotal,
    queryParams,
    selectedRowKeys,
    selectedRows,
    filter,
    setFilter,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    sort: {
      updateTime: 'DESC',
    },
    filter: {
      name,
      ...status && {
        statusList: status === 'FAILED' ? ['START_FAILED', 'STOP_FAILED', 'RUNNING_FAILED'] : [status],
      },
    },
    cacheId: name || status ? undefined : 'ckTaskList',
  });

  const action: (typeof CKSTORAGE_TASK_COLUMNS)[number] = {
    title: '操作',
    fixed: 'right',
    width: 200,
    render: (_, record) => {
      const status = record.storageCkStatus;
      const isStart =
        status === 'STOPPED' || status === 'UNSTART' || status === 'START_FAILED' || status === 'RUNNING_FAILED';
      return (
        <div className='flex items-center gap-x-2'>
          {isStart && (
            <Button
              type='link'
              size='small'
              disabled={!hasRights(permissionCode, record.projectAuth)}
              onClick={() => handleStart([record.storageCkId])}
            >
              {record.starting ? <Spin size='small' className='mr-2' /> : '启动'}
            </Button>
          )}
          {!isStart && (
            <Button
              type='link'
              size='small'
              disabled={!hasRights(permissionCode, record.projectAuth)}
              onClick={() => handleStop([record.storageCkId])}
            >
              {record.stopping ? <Spin size='small' className='mr-2' /> : '停止'}
            </Button>
          )}
          <Button type='link' size='small' onClick={() => setCkId(record.storageCkId)}>
            任务详情
          </Button>
          <Link to={`/ops/realtime/storage/ck/performance-monitor/${record.storageCkId}?name=${record.storageCkName}`}>
            性能监控
          </Link>
        </div>
      );
    },
  };

  const fetchData = async () => {
    setIsLoading(true);
    const { data, total } = await ingestionService
      .queryCkStorageTaskList({
        ...queryParams,
      })
      .finally(() => setIsLoading(false));
    setList(data);
    setTotal(total ?? 0);
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  const updateStatus = async (action: 'start' | 'stop', selectedRowKeys: string[]) => {
    setList(list => {
      return list.map(item => {
        if (selectedRowKeys.includes(item.storageCkId)) {
          if (action === 'start') {
            item.starting = true;
          } else {
            item.stopping = true;
          }
          return item;
        }
        return item;
      });
    });
    const fun = action === 'start' ? 'startupCkStorageById' : 'stopCkStorageById';
    const results = await Promise.allSettled(selectedRowKeys.map(x => ingestionService[fun](x))).finally(() => {
      setList(list => {
        return list.map(item => {
          if (selectedRowKeys.includes(item.storageCkId)) {
            if (action === 'start') {
              item.starting = false;
            } else {
              item.stopping = false;
            }
            return item;
          }
          return item;
        });
      });
    });
    if (results.length === 1) {
      if (results[0].status === 'fulfilled') {
        message.success('操作成功，请稍候查看结果');
      } else {
        message.error(results[0].reason.msg);
      }
    } else {
      const msg = results.some(x => x.status === 'rejected') ? message.warning : message.success;
      msg(
        `操作成功，成功${results.filter(x => x.status === 'fulfilled').length}个，失败${
          results.filter(x => x.status === 'rejected').length
        }个，请稍候查看结果`,
      );
    }
    fetchData();
  };

  const handleStart = items => {
    Modal.confirm({
      title: '是否确认启动？',
      onOk: () => {
        updateStatus('start', items);
      },
    });
  };

  const handleStop = items => {
    Modal.confirm({
      title: '是否确认停止？',
      onOk: () => {
        updateStatus('stop', items);
      },
    });
  };

  const batchActions = (key: 'start' | 'stop') => {
    switch (key) {
    case 'start':
      handleStart(selectedRowKeys);
      break;
    case 'stop': {
      handleStop(selectedRowKeys);
      break;
    }
    }
  };

  const getDataRights = () => {
    if (selectedRows.some(item => !hasRights(permissionCode, item.projectAuth))) {
      return true;
    }
    return false;
  };

  return (
    <>
      <div className='flex flex-col h-full'>
        <div className='px-3 pt-3 pb-3 flex justify-between'>
          <Space>
            <Dropdown
              menu={{
                items: [
                  {
                    label: '启动',
                    key: 'start',
                    disabled: selectedRowKeys.length === 0 || getDataRights(),
                  },
                  {
                    label: '停止',
                    key: 'stop',
                    disabled: selectedRowKeys.length === 0 || getDataRights(),
                  },
                ],
                onClick: ({ key }) => {
                  batchActions(key);
                },
              }}
            >
              <Button>
                批量操作 <DownOutlined />
              </Button>
            </Dropdown>
            <SearchInput
              defaultValue={filter.name}
              onSearch={v =>
                setFilter({
                  ...filter,
                  name: v,
                })
              }
              placeholder='请输入任务名称'
            />
            <Select
              placeholder='请选择任务状态'
              showSearch
              allowClear
              mode="multiple"
              optionFilterProp='label'
              defaultValue={filter.statusList}
              onChange={v => setFilter({ ...filter, statusList: v })}
              className='w-52 status-select'
            >
              {CKTORAGE_TASK_STATUS_OPTIONS.map(({ label, value }) => (
                <>
                  <Select.Option key={value} value={value}>
                    <span className={`storage-ck-status ${value} align-middle leading-[22px]`}>{label}</span>
                  </Select.Option>
                </>
              ))}
            </Select>
            <StorageClusterSelect
              className='w-52'
              placeholder='请选择存储集群'
              defaultValue={filter.storageClusterId}
              onChange={v => setFilter({ ...filter, storageClusterId: v })}
            />
          </Space>
          <Button type='text' onClick={() => fetchData()}>
            <i className='iconfont icon-refresh-line' />
          </Button>
        </div>
        <div className='flex-1 overflow-hidden'>
          <CustomTable
            loading={isLoading}
            scroll={{ x: 1200 }}
            pagination={pagination}
            rowKey='storageCkId'
            cacheId='ckTaskList'
            resizable
            onChange={handleTableChange}
            onRowSelectionChange={onRowSelectionChange}
            dataSource={list}
            columns={[...CKSTORAGE_TASK_COLUMNS, action]}
          />
        </div>
      </div>
      {ckId && <CKStorageTaskDetailModal footer={null} id={ckId} open={!!ckId} onCancel={() => setCkId(undefined)} />}
    </>
  );
};
