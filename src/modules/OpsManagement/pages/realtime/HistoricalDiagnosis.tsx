import { useEffect, useState } from 'react';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import { Link, useParams, useSearchParams } from 'umi';

import { CustomTable, useCustomTableHook } from '@/components';
import { useGlobalHook } from '@/hooks';
import { PipelineApi } from '@/modules/PipelineDevOps';

import './HistoricalDiagnosis.less';

import { HISTORICAL_DIAGNOSIS_COLUMN, HISTORICAL_DIAGNOSIS_SUB_COLUMN } from '../../constants';
import { PipelineHistoryApi } from '../../services/PipelineHistoryApi';

export const HistoricalDiagnosis = () => {
  const { id } = useParams();
  const [data, setData] = useState([]);
  const [clusterId, setClusterId] = useState();
  const [loading, setLoading] = useState(false);
  const defaultTimeRange = [dayjs().subtract(7, 'day'), dayjs()];
  const [searchParams] = useSearchParams();
  const { filter, setFilter, queryParams } = useCustomTableHook({
    cacheId: 'historicalDiagnosis',
  });
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  useEffect(() => {
    if (!searchParams.get('useCache')) {
      setFilter({
        ...filter,
        startTime: dayjs().subtract(7, 'day').valueOf(),
        endTime: dayjs().valueOf(),
      });
    }
    setPageInfo({
      backUrl: '/ops/realtime/pipeline/devOps',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    if (!id) return;
    PipelineApi.getDetail(id)
      .then(({ data }) => {
        const { pipelineAlias, clusterId } = data;
        setClusterId(clusterId);
        setPageInfo({
          title: (
            <span className='items-center flex'>
              <span className='mr-5'>诊断：{pipelineAlias}</span>
            </span>
          ),
        });
      })
      .catch(() => {});
  }, [id]);

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  const fetchData = async () => {
    setLoading(true);
    const { data } = await PipelineHistoryApi.getList(id, {
      startTime: filter.startTime,
      endTime: filter.endTime,
    }).finally(() => setLoading(false));
    setData(
      data.map(item => {
        item.jobAttempts = item.jobAttempts.map(attempt => ({
          ...attempt,
          applicationId: item.applicationId,
          trackUrl: item.trackUrl,
          prometheusUrl: item.prometheusUrl,
        }));
        return {
          ...item,
          state: item.application.state,
        };
      }) as any,
    );
    setExpandedRowKeys(data.map(item => item.applicationId));
  };

  const { setPageInfo, resetPageInfo } = useGlobalHook();
  useEffect(() => {
    return () => {
      resetPageInfo();
    };
  }, []);

  const handleDateChange = val => {
    setFilter({
      ...filter,
      startTime: dayjs(val[0]).valueOf(),
      endTime: dayjs(val[1]).valueOf(),
    });
  };

  const columns = [...HISTORICAL_DIAGNOSIS_COLUMN];

  const expandedRowRender = record => {
    const columns = [
      ...HISTORICAL_DIAGNOSIS_SUB_COLUMN,
      {
        title: '操作',
        fixed: 'right',
        width: 130,
        render(record) {
          const { appAttemptId, jobId, trackUrl, prometheusUrl, startTime, endTime, applicationId } = record;
          const query = encodeURIComponent(
            JSON.stringify({
              jobId,
              trackUrl,
              prometheusUrl,
              startTime,
              endTime,
              applicationId,
              clusterId,
              pipelineId: id,
            }),
          );
          return (
            <Link to={`/ops/realtime/pipeline/historical-running-instances/${appAttemptId}?query=${query}`}>
              查看运行实例
            </Link>
          );
        },
      },
    ];
    return (
      <CustomTable
        className='inside-table-container'
        columns={columns}
        dataSource={record.jobAttempts}
        pagination={false}
      />
    );
  };

  const onExpandedRowsChange = expandedRows => {
    setExpandedRowKeys(expandedRows);
  };

  return (
    <div className='h-full bg-white flex flex-col overflow-hidden'>
      <div className='px-4 py-3 flex items-center'>
        <span className='text-[14px] opacity-[0.85]'>时间范围：</span>
        <DatePicker.RangePicker
          defaultValue={defaultTimeRange}
          showTime
          value={[dayjs(filter.startTime), dayjs(filter.endTime)]}
          allowClear={false}
          className='w-[350px]'
          onChange={handleDateChange}
        />
      </div>
      <CustomTable
        columns={columns}
        dataSource={data}
        pagination={false}
        scroll={{ x: 1200 }}
        loading={loading}
        expandable={{ expandedRowRender, expandedRowKeys, onExpandedRowsChange }}
        className='flex-1 overflow-hidden'
        rowKey='applicationId'
      />
    </div>
  );
};
