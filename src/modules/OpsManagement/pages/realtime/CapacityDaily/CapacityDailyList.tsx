import { useEffect, useState } from 'react';
import { But<PERSON>, Col, DatePicker, Progress, Row, Space, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { Link } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import BatchActions from '@/components/business/ui/BatchActions';
import { formatLargeNumber } from '@/components/visual/helper';
import { useGlobalHook } from '@/hooks';
import { PipelineCapacityDailyApi } from '@/modules/OpsManagement/services/PipelineCapacityDailyApi';

export const CapacityDailyList = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const {
    queryParams,
    pagination,
    filter,
    sort,
    selectedRowKeys,
    handleTableChange,
    setTotal,
    setFilter,
    onRowSelectionChange,
  } = useCustomTableHook({
    cacheId: 'capacity-daily-list',
    filter: {
      statisticalDay: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    },
    sort: {
      updateTime: 'DESC',
    },
  });
  const [list, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ColumnsType<any> = [
    {
      title: '作业名称',
      dataIndex: 'pipelineAlias',
      width: 200,
      render: (pipelineAlias, record) => {
        return <Link to={`/ops/realtime/pipeline/devOps?pipelineId=${record.pipelineId}`}>{pipelineAlias}</Link>;
      },
    },
    {
      title: '统计日期',
      width: 150,
      dataIndex: 'statisticalDay', // 条
      sorter: true,
    },
    {
      title: '单并行度CPU最高使用率',
      dataIndex: 'maxCpuRateQuotaRatio',
      width: 160,
      sorter: true,
      render: maxCpuRateQuotaRatio => {
        return (
          <span className='flex'>
            <Progress
              style={{ width: 100 }}
              percent={maxCpuRateQuotaRatio}
              status={maxCpuRateQuotaRatio > 100 ? 'exception' : 'success'}
              size='small'
              showInfo={false}
            ></Progress>
            {maxCpuRateQuotaRatio}%
          </span>
        );
      },
    },
    {
      title: '单并行度CPU平均使用率',
      dataIndex: 'avgCpuRateQuotaRatio',
      width: 160,
      sorter: true,
      render: avgCpuRateQuotaRatio => {
        return (
          <span className='flex'>
            <Progress
              style={{ width: 100 }}
              percent={avgCpuRateQuotaRatio}
              status={avgCpuRateQuotaRatio > 100 ? 'exception' : 'success'}
              size='small'
              showInfo={false}
            ></Progress>
            {avgCpuRateQuotaRatio}%
          </span>
        );
      },
    },
    {
      title: '24小时处理量',
      dataIndex: 'recordCount', // 条
      width: 120,
      sorter: true,
      render(recordCount) {
        const count = formatLargeNumber(recordCount);
        return <Tooltip title={recordCount}>
          <span>{ count }</span>
        </Tooltip>;
      },
    },
    {
      title: '重启次数',
      dataIndex: 'restartCount',
      width: 100,
      sorter: true,
    },
    {
      title: '最大FullGC/m',
      dataIndex: 'maxFullGcCount', // 次/分
      width: 120,
      sorter: true,
    },
    {
      title: '并行度/单个TM的SLOT',
      dataIndex: 'parallelism',
      width: 120,
      render: (parallelism, record) => {
        return `${parallelism}/${record.slot}`;
      },
      sorter: true,
    },
    {
      title: '操作',
      fixed: 'right',
      width: 80,
      align: 'center',
      render: (_, record) => {
        const { id, pipelineAlias, statisticalDay } = record;
        return (
          <Space>
            <Link
              to={`/ops/realtime/pipeline/capacity-daily-detail/${id}?title=${pipelineAlias}&date=${statisticalDay}`}
            >
              查看
            </Link>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    setPageInfo({
      title: '容量日报',
      description: '洞察作业的资源容量和处理容量，为扩缩容提供依据。',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  const fetchData = async () => {
    setLoading(true);
    const res = await PipelineCapacityDailyApi.getList(queryParams).finally(() => {
      setLoading(false);
    });
    if (res?.code === '0000') {
      setList(res?.data ?? []);
      setTotal(res?.total ?? 0);
    }
  };

  return (
    <div className='h-full w-full bg-white overflow-hidden flex flex-col'>
      <div className='px-4 py-3 flex justify-between'>
        <Row gutter={[8, 8]} className='flex-1'>
          <Col>
            <BatchActions type='pipelineCapacityDaily' ids={selectedRowKeys} condition={{ filter, sort }} />
          </Col>
          <Col>
            <SearchInput
              placeholder='请输入关键词'
              className='w-[224px]'
              defaultValue={filter.search}
              onSearch={value => {
                setFilter({
                  ...filter,
                  search: value,
                });
              }}
              allowClear
            />
          </Col>
          <Col>
            <DatePicker
              defaultValue={dayjs(filter.statisticalDay)}
              disabledDate={current => {
                return current && current >= dayjs().startOf('day');
              }}
              onChange={val => {
                setFilter({
                  ...filter,
                  statisticalDay: val?.format('YYYY-MM-DD') ?? undefined,
                });
              }}
            />
          </Col>
        </Row>
        <Button type='text' onClick={() => fetchData()}>
          <i className='iconfont icon-refresh-line'></i>
        </Button>
      </div>

      <div className='flex-1 overflow-hidden'>
        <CustomTable
          dataSource={list}
          loading={loading}
          columns={columns}
          onChange={handleTableChange}
          cacheId='capacity-daily-list'
          resizable
          onRowSelectionChange={onRowSelectionChange}
          pagination={pagination}
          scroll={{ x: 1400 }}
        />
      </div>
    </div>
  );
};
