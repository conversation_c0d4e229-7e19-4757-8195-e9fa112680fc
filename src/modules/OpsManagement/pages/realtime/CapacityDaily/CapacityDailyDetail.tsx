import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Col, Row } from 'antd';
import { pick } from 'lodash';
import { useParams, useSearchParams } from 'umi';

import { useGlobalHook } from '@/hooks';
import { PipelineCapacityDailyApi } from '@/modules/OpsManagement/services/PipelineCapacityDailyApi';
import {
  ConsumeBackLogChart,
  CpuUsageChart,
  FullGcRateChart,
  KafkaWriteAndConsumeRateChart,
} from '@/modules/PipelineDevOps';

export const CapacityDailyDetail = () => {
  const [searchParams] = useSearchParams();
  const { id } = useParams();
  const { setPageInfo } = useGlobalHook();
  const [metrics, setMetrics] = useState<any>();
  const container = useRef();

  const dateStr = searchParams.get('date');
  const isRealTime = false;
  const rangeTime = [`${dateStr} 00:00:00`, `${dateStr} 23:59:59`];

  const fetchData = async width => {
    const { data } = await PipelineCapacityDailyApi.getMetrics({
      id: id as string,
      width,
    });
    setMetrics(data);
  };

  useLayoutEffect(() => {
    const width = (container.current?.offsetWidth ?? 1200) / 2;
    fetchData(width);
  }, []);

  useEffect(() => {
    setPageInfo({
      title: `${searchParams.get('title')}（${searchParams.get('date')}）`,
    });
  }, []);

  return (
    <div className='h-full w-full bg-white overflow-y-auto p-4' ref={container}>
      <Row gutter={[12, 12]} className='mb-3'>
        <Col span={12}>
          <div className='h-[332px]'>
            <CpuUsageChart {...metrics?.cpuUsage} range={rangeTime} isRealTime={isRealTime} />
          </div>
        </Col>
        <Col span={12}>
          <div className='h-[332px]'>
            <KafkaWriteAndConsumeRateChart
              values={pick(metrics ?? {}, ['kafkaInputRate', 'kafkaConsumeRate', 'flinkKafkaOffsetRate'])}
              range={rangeTime}
              isRealTime={isRealTime}
            />
          </div>
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={12}>
          <div className='h-[332px]'>
            <FullGcRateChart values={metrics?.fullGcRate?.values ?? []} range={rangeTime} isRealTime={isRealTime} />
          </div>
        </Col>
        <Col span={12}>
          <div className='h-[332px]'>
            <ConsumeBackLogChart values={pick(metrics ?? {}, ['kafkaLag'])} range={rangeTime} isRealTime={isRealTime} />
          </div>
        </Col>
      </Row>
    </div>
  );
};
