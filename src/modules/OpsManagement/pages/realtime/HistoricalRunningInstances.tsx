import { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import { useParams, useSearchParams } from 'umi';

import { useGlobalHook } from '@/hooks';

import { PipelineHistoryApi } from '../../services/PipelineHistoryApi';

import { RunningLog } from './components/RunningLog';
import { TaskMetricChart } from './components/TaskMetricChart';

interface QueryParams {
  jobId: string;
  trackUrl: string;
  prometheusUrl: string;
  startTime: number;
  endTime: number;
  applicationId: string;
  clusterId: string;
  pipelineId: string;
}

export const HistoricalRunningInstances = () => {
  const [searchParams] = useSearchParams();
  const { id } = useParams();
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [tmIdList, setTmIdList] = useState<string[]>([]);
  const [attempt, setAttempt] = useState();
  const pageQuery: QueryParams = JSON.parse(decodeURIComponent(searchParams.get('query') as string));

  useEffect(() => {
    setPageInfo({
      title: (
        <span className='items-center flex'>
          <span className='mr-5'>运行实例：{id}</span>
        </span>
      ),
      backUrl: `/ops/realtime/pipeline/historical-diagnosis/${pageQuery.pipelineId}?useCache=1`,
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  const [containers, setContainers] = useState<YarnSessionLogItem[]>([]);

  useEffect(() => {
    if (!pageQuery.applicationId) return;
    const { jobId, trackUrl, prometheusUrl, startTime, endTime, applicationId, pipelineId } = pageQuery;
    PipelineHistoryApi.getAttemptDetail({
      applicationId,
      pipelineId,
      attemptId: id,
      jobId,
      trackUrl,
      prometheusUrl,
      startMillis: startTime,
      endMillis: endTime,
    }).then(({ data }) => {
      const { amContainer, tmContainers, tmIds, attemptCount, startTime } = data;
      setTmIdList(tmIds || []);
      setContainers([amContainer].concat(tmContainers));
      setAttempt({
        attemptCount,
        startTime,
      });
    });
  }, []);

  const items = [
    {
      label: '任务指标',
      key: 'task-indicator',
      children: (
        <TaskMetricChart
          prometheusUrl={pageQuery.prometheusUrl}
          jobId={pageQuery.jobId}
          tmIdList={tmIdList}
          startTime={pageQuery.startTime}
          endTime={pageQuery.endTime}
        />
      ),
    },
    {
      label: '运行日志',
      key: 'running-log',
      children: (
        <RunningLog
          pipelineData={{ yarnAppId: pageQuery.applicationId, clusterId: pageQuery.clusterId }}
          containers={containers}
          attempt={attempt}
        />
      ),
    },
  ];
  return (
    <div className='h-full bg-white online-task-diagnose'>
      <Tabs items={items} className='h-full' tabBarStyle={{ paddingLeft: '16px' }} />
    </div>
  );
};
