import React, { useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON>, <PERSON>lapse, Divider, Row } from 'antd';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import ResizeObserver from 'resize-observer-polyfill';

import { Range, TimeRangeSelector } from '@/components/visual/tool/TimeRangerSelector';
import { getRealRangeTime } from '@/components/visual/tool/TimeRangerSelector/helper';
import {
  ConsumeBackLog<PERSON>hart,
  CpuUsageChart,
  DirectMemoryChart,
  FlinkMetricApi,
  FullGcRateChart,
  HeapAvgChart,
  KafkaWriteAndConsumeRateChart,
  MetaSpaceChart,
  NettyMemoryChart,
  NonHeapChart,
  SinkRateChart,
  SourceRateChart,
  TmHeapChart,
  YgcChart,
} from '@/modules/PipelineDevOps';

import './TaskMetricChart.less';

interface Props {
  tmIdList: string[];
  prometheusUrl: string;
  jobId: string;
  startTime: number;
  endTime: number;
}

export const TaskMetricChart: React.FC<Props> = props => {
  const { tmIdList, prometheusUrl, jobId, startTime, endTime } = props;
  const [range, setRange] = useState<Range>([
    dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
    dayjs(endTime || Date.now()).format('YYYY-MM-DD HH:mm:ss'),
  ]);
  const [tmIds, setTmIds] = useState<string[]>([]);
  const [jobMetrics, setJobMetrics] = useState<any>();
  const [tmMetrics, setTmMetrics] = useState<Array<{ tmId: string; metrics: any }>>([]);
  const [width, setWidth] = useState(500);
  const containerRef = useRef();
  const observerWidth = useRef(false);
  const [activeKey, setActiveKey] = useState<string[]>([]);
  const rangeTime = useMemo<[string, string]>(() => getRealRangeTime(range), [range]);

  useEffect(() => {
    const defaultTmId = tmIdList?.length ? [tmIdList[0]] : [];
    setTmIds(defaultTmId);
    setActiveKey(defaultTmId);
  }, [tmIdList]);

  useEffect(() => {
    if (tmIds.length === 0) {
      setTmMetrics([]);
      return;
    }
    fetchTmRangeMetrics();
  }, [tmIds]);

  useEffect(() => {
    if (!scroll) return;
    const observer = new ResizeObserver(entries => {
      const { width } = entries[0].contentRect;
      if (observerWidth.current) return;
      setWidth(width / 3);
      observerWidth.current = true;
    });
    observer.observe(containerRef?.current as unknown as Element);
    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (!observerWidth.current) return;
    fetchData();
  }, [range, width]);

  const fetchData = () => {
    fetchJobLevelMetrics();
    fetchTmRangeMetrics();
  };

  const fetchJobLevelMetrics = () => {
    if (!jobId) return;
    const [beginTime, endTime] = rangeTime;
    return FlinkMetricApi.getJobLevelMetrics({ prometheusUrl, jobId, beginTime, endTime, width }).then(({ data }) => {
      setJobMetrics(data);
    });
  };

  const fetchTmRangeMetrics = () => {
    const [beginTime, endTime] = rangeTime;
    Promise.allSettled(
      tmIds.map(tmId => {
        return FlinkMetricApi.getTmRangeMetrics({
          prometheusUrl,
          tmIds: tmId,
          beginTime,
          endTime,
          width,
        });
      }),
    ).then(res => {
      const tmMetrics = tmIds?.map((tmId, index) => {
        return {
          tmId,
          metrics: res?.[index]?.value?.data,
        };
      });
      setTmMetrics(tmMetrics);
    });
  };

  const onChangeRange = (range: Range) => {
    setRange(range);
  };

  const onClickTmId = (tmId: string) => {
    const index = tmIds.findIndex(x => x === tmId);
    if (index !== -1) {
      tmIds.splice(index, 1);
    } else {
      tmIds.push(tmId);
    }
    setTmIds([...tmIds]);
    setActiveKey([...tmIds]);
  };

  return (
    <div className='task-indicator' ref={containerRef}>
      <div className='px-3 py-2 flex'>
        <TimeRangeSelector className='mr-3' range={range} onChange={onChangeRange} />
      </div>
      <div className='px-3'>
        <Row gutter={[12, 12]}>
          <Col span={16}>
            <Row gutter={[12, 12]}>
              <Col span={10}>
                <div className='h-64'>
                  <CpuUsageChart {...jobMetrics?.cpuUsage} range={rangeTime} />
                </div>
              </Col>
              <Col span={14}>
                <div className='h-64'>
                  <KafkaWriteAndConsumeRateChart
                    values={pick(jobMetrics ?? {}, ['kafkaInputRate', 'kafkaConsumeRate', 'flinkKafkaOffsetRate'])}
                    range={rangeTime}
                  />
                </div>
              </Col>
            </Row>
            <Row gutter={[12, 12]} className='mt-3'>
              <Col span={10}>
                <div className='h-64'>
                  <HeapAvgChart
                    values={pick(jobMetrics ?? {}, ['heapMax', 'heapUsed', 'heapCommitted'])}
                    range={rangeTime}
                  />
                </div>
              </Col>
              <Col span={14}>
                <div className='h-64'>
                  <ConsumeBackLogChart values={pick(jobMetrics ?? {}, ['kafkaLag'])} range={rangeTime} />
                </div>
              </Col>
            </Row>
          </Col>
          <Col span='8' className='flex flex-col justify-between right-metric-chart'>
            <div className='h-32'>
              <YgcChart values={jobMetrics?.youngGcRate?.values ?? []} range={rangeTime} />
            </div>
            <div className='h-32'>
              <FullGcRateChart values={jobMetrics?.fullGcRate?.values ?? []} range={rangeTime} />
            </div>
            <div className='h-32'>
              <SinkRateChart values={jobMetrics?.sinkRate?.values ?? []} range={rangeTime} />
            </div>
            <div className='h-32'>
              <SourceRateChart values={jobMetrics?.sourceRate?.values ?? []} range={rangeTime} />
            </div>
          </Col>
        </Row>
      </div>
      <Divider className='my-3' />
      <div className='text-gray-6 font-medium py-1 px-3'>进程级别监控</div>
      <div className='px-3 flex overflow-x-auto'>
        {tmIdList.map(item => (
          <div
            className={`card px-3 py-1 mb-2 mr-3 cursor-pointer ${tmIds.includes(item) ? 'selected' : ''}`}
            key={item}
            onClick={() => onClickTmId(item)}
          >
            <div className='card-header'>TaskManager Id: {item}</div>
          </div>
        ))}
      </div>
      <Collapse
        size='small'
        className='custom-small'
        activeKey={activeKey}
        onChange={setActiveKey}
        items={tmMetrics.map(({ tmId, metrics }) => {
          return {
            key: tmId,
            label: `TaskManager Id: ${tmId}`,
            children: (
              <Row gutter={[12, 12]}>
                <Col span='16'>
                  <Row gutter={[12, 12]}>
                    <Col span='10'>
                      <div className='h-[296px]'>
                        <TmHeapChart
                          values={pick(metrics ?? {}, ['heapMax', 'heapUsed', 'heapCommitted'])}
                          range={rangeTime}
                        />
                      </div>
                    </Col>
                    <Col span='14' className='flex flex-col justify-between'>
                      <div className='h-[142px]'>
                        <NonHeapChart
                          values={pick(metrics ?? {}, ['nonHeapMax', 'nonHeapUsed', 'nonHeapCommitted'])}
                          range={rangeTime}
                        />
                      </div>
                      <div className='h-[142px]'>
                        <MetaSpaceChart
                          values={pick(metrics ?? {}, ['metaspaceMax', 'metaspaceUsed', 'metaspaceCommitted'])}
                          range={rangeTime}
                        />
                      </div>
                    </Col>
                  </Row>
                </Col>
                <Col span='8' className='flex flex-col justify-between'>
                  <div className='h-[142px]'>
                    <DirectMemoryChart values={pick(metrics ?? {}, ['directUsed', 'directTotal'])} range={rangeTime} />
                  </div>
                  <div className='h-[142px]'>
                    <NettyMemoryChart values={pick(metrics ?? {}, ['networkUsed', 'networkTotal'])} range={rangeTime} />
                  </div>
                </Col>
              </Row>
            ),
          };
        })}
      />
    </div>
  );
};
