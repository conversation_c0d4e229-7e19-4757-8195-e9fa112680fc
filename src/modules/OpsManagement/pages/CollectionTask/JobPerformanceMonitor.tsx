import { useEffect } from 'react';
import { useParams } from 'umi';

import { Loading } from '@/components';
import { useGlobalHook } from '@/hooks';
import { useIngestionJobById } from '@/modules/OpsManagement/hooks';
import Page404 from '@/pages/404';

import { PerformanceMonitor } from './shared';

export const JobPerformanceMonitor = () => {
  const { id = '' } = useParams();
  const { job, isLoading } = useIngestionJobById(id);
  const { setPageInfo, resetPageInfo } = useGlobalHook();

  const { id: taskId, cellId } = job ?? {};

  useEffect(() => {
    setPageInfo({ title: '性能监控', description: '' });
    return () => {
      resetPageInfo();
    };
  }, []);

  if (isLoading) {
    return <Loading />;
  }

  if (!cellId || !taskId) {
    return <Page404 />;
  }

  return <PerformanceMonitor job={job} fetchParams={{ cellId, taskId }} />;
};
