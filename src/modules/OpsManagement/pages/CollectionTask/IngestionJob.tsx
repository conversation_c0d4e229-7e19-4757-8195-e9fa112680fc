import { useRef, useState } from 'react';

import { useLocationState } from '@/hooks';
import { IngestionJobSearchForm, IngestionJobTable, JobRef } from '@/modules/OpsManagement/components';

export const IngestionJob = () => {
  const { taskName } = useLocationState<{ taskName: string }>();
  const [filter, setFilter] = useState<OPS_MANAGEMENT.INGESTION_JOB.Form>({
    name: taskName,
  });
  const jobRef = useRef<JobRef>(null);

  const handleRefetch = () => {
    jobRef.current?.refreshList();
  };
  return (
    <div className='bg-white h-full flex flex-col '>
      <IngestionJobSearchForm onSearch={setFilter} onReFetch={handleRefetch} />
      <div className='flex-1 overflow-hidden'>
        <IngestionJobTable filter={filter} ref={jobRef} />
      </div>
    </div>
  );
};
