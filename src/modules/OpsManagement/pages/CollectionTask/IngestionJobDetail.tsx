import { useEffect, useRef, useState } from 'react';
import { useParams } from 'umi';

import { useGlobalHook } from '@/hooks';
import {
  CellStatus,
  JobDetailSearchForm,
  JobDetailTableList,
  JobDetailToolbar,
  Status,
  TaskRef,
} from '@/modules/OpsManagement/components';
import { IngestionJobStatusEnum } from '@/modules/OpsManagement/constants';
import { useIngestionJobById } from '@/modules/OpsManagement/hooks';
import { ingestionJobPathname } from '@/routes/ops-management';

export const IngestionJobDetail = () => {
  const { id } = useParams();

  const detailRef = useRef<TaskRef>(null);

  const [filter, setFilter] = useState<OPS_MANAGEMENT.INGESTION_JOB_TASK.Form>({
    ingestionJobId: id,
  });
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { job } = useIngestionJobById(id ?? '');

  useEffect(() => {
    setPageInfo({
      title: (
        <span className='inline-flex items-center gap-3 text-base font-medium text-header'>
          {job?.name ?? ''}
          <CellStatus className='h-6 text-xs' status={job?.status ?? ('StartException' as Status)} warningTips={false}>
            {IngestionJobStatusEnum[job?.status as OPS_MANAGEMENT.IngestionJobStatus]}
          </CellStatus>
        </span>
      ),
      backUrl: ingestionJobPathname,
    });
    return () => {
      resetPageInfo();
    };
  }, [job?.name, job?.status]);

  const handleStop = () => {
    detailRef.current?.refetch();
  };

  return (
    <div className='h-full flex flex-col gap-[5px]'>
      <div>
        <JobDetailToolbar refetch={handleStop} />
      </div>
      <div className='flex-1 flex  flex-col bg-white overflow-hidden'>
        <JobDetailSearchForm onSearch={setFilter} />
        <JobDetailTableList filter={filter} ref={detailRef} />
      </div>
    </div>
  );
};
