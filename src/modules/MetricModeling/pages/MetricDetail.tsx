import { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import { useParams } from 'umi';

import { KeepAliveToTab, SectionCaptain, useKeepAliveTabs } from '@/components';
import { useRightsHook } from '@/hooks';

import { MetricBaseInfo, MetricBindSourceList, MetricItemModal } from '../components';
import { MetricItemDetail } from '../models';
import { MetricItemApi } from '../service';

const Page = () => {
  const { id } = useParams();
  const { setPageInfo } = useKeepAliveTabs();
  const { hasRights } = useRightsHook();

  const [detail, setDetail] = useState<MetricItemDetail>();
  const [open, setOpen] = useState(false);

  const fetchDetail = async () => {
    try {
      const { data } = await MetricItemApi.detail(id!);
      setDetail(data);
      setPageInfo({ title: data?.name });
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    if (id) {
      fetchDetail();
    }
  }, [id]);

  return (
    <div className='h-full w-full flex flex-col'>
      <div className='flex-1 overflow-auto'>
        <div className='flex justify-between items-center pr-4'>
          <SectionCaptain title='基本信息' className='my-2 px-4' />
          <Button disabled={!hasRights('metric_item:write')} onClick={() => setOpen(true)}>
            编辑
          </Button>
        </div>
        <MetricBaseInfo detail={detail} />
        <SectionCaptain title='指标来源' className='my-2 px-4' />
        <MetricBindSourceList detail={detail} />
      </div>
      {open && (
        <MetricItemModal
          type='edit'
          open={open}
          metricItem={detail}
          onCancel={() => setOpen(false)}
          callback={fetchDetail}
        />
      )}
    </div>
  );
};

export const MetricDetail = () => {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
};
