import { useEffect, useState } from 'react';
import { Button, Col, message, Row, Tag } from 'antd';
import { Link } from 'umi';

import { CustomTable, KeepAliveToTab, SearchInput, TagSelectForSearch, useCustomTableHook } from '@/components';
import BatchActions from '@/components/business/ui/BatchActions';
import { useDeleteConfirm, useRightsHook } from '@/hooks';

import { MetricItemModal } from '../components';
import { MetricSetSearchSelect } from '../components/MetricSetSearchSelect';
import { sampleFrequencyUnitOptions } from '../constants';
import { MetricItemListItem } from '../models/MetricItem';
import { MetricItemApi } from '../service';
import { useMetricModelingStore } from '../store/useMetricModelingStore';

const Page = () => {
  const {
    queryParams,
    pagination,
    filter,
    selectedRows,
    selectedRowKeys,
    sort,
    setSelectedRows,
    setSelectedRowKeys,
    onRowSelectionChange,
    handleTableChange,
    setFilter,
    setPagination,
  } = useCustomTableHook({
    pageSize: 100,
    sort: {
      updateTime: 'DESC',
    },
  });

  const {
    timestamp,
    treeFilter,
    actions: { setTimestamp, fetchTreeData, setTreeFilter },
  } = useMetricModelingStore();

  const deleteConfirm = useDeleteConfirm({
    delUrl: 'metric/item',
    prefix: '/api/v2/data-modeling/',
  });
  const { hasRights } = useRightsHook();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<MetricItemListItem[]>([]);
  const [open, setOpen] = useState(false);
  const [metricItem, setMetricItem] = useState<MetricItemListItem>();

  const columns: Array<ColumnType<MetricItemListItem>> = [
    {
      title: '指标中文名称',
      dataIndex: 'name',
      fixed: 'left',
      render: (name, record) => <Link to={`/metric/modeling/detail/${record?.id}`}>{name}</Link>,
    },
    {
      title: '英文名称',
      dataIndex: 'nameEn',
      fixed: 'left',
    },
    {
      title: '指标分类',
      ellipsis: true,
      dataIndex: 'category',
    },
    {
      title: '指标标签',
      dataIndex: 'itemTagList',
      render(itemTagList) {
        return (
          <div>
            {itemTagList.map(x => (
              <Tag key={x}>{x}</Tag>
            ))}
          </div>
        );
      },
    },
    {
      title: '对象模型',
      ellipsis: true,
      dataIndex: 'objName',
    },
    {
      title: '指标集',
      dataIndex: 'msetName',
    },
    {
      title: '度量单位',
      dataIndex: 'unitName',
      width: 80,
    },
    {
      title: '采样频率',
      dataIndex: 'sampleFrequency',
      width: 80,
      render: (_, record) =>
        `${record.sampleFrequency}(${
          sampleFrequencyUnitOptions.find(x => x.value === record.sampleFrequencyUnit)?.label
        })`,
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <>
          <Button
            type='link'
            className='px-0 mr-2'
            onClick={() => handleEdit(record)}
            disabled={!hasRights('metric_item:write')}
          >
            编辑
          </Button>
          <Button
            type='link'
            className='px-0'
            onClick={() => handleDelete(record)}
            disabled={!hasRights('metric_item:write')}
          >
            删除
          </Button>
        </>
      ),
    },
  ];

  const handleEdit = (record: MetricItemListItem) => {
    setMetricItem(record);
    setOpen(true);
  };

  const handleDelete = async (record: MetricItemListItem) => {
    const { code } = await deleteConfirm(record.id, `确认删除指标来源「${record.name}」？`);
    if (code === '0000') {
      message.success('删除成功');
      setTimestamp(new Date().valueOf());
      fetchTreeData(true);
      setSelectedRows([]);
      setSelectedRowKeys([]);
    }
  };

  const handleBatchDelete = async () => {
    await deleteConfirm(selectedRows ?? [], `确认删除勾选对象(共计${selectedRows?.length})个吗？`, {
      delUrl: '/metric/item/bulk',
      title: '确定删除',
      prefix: '/api/v2/data-modeling',
    });
    fetchData();
    fetchTreeData(true);
    setSelectedRows([]);
    setSelectedRowKeys([]);
  };

  useEffect(() => {
    setTimeout(() => {
      setFilter({ ...filter, ...treeFilter });
    }, 100);
  }, [treeFilter]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data, total } = await MetricItemApi.queryItem(queryParams);
      setLoading(false);
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams, timestamp]);

  return (
    <div className='h-full w-full flex flex-col'>
      <div className='p-3'>
        <Row gutter={[8, 8]}>
          <Col>
            <BatchActions
              type='objectIndicatorGroup'
              ids={selectedRowKeys}
              condition={{ filter, sort }}
              disabled={!hasRights('metric_item:write')}
              onDeleteItems={handleBatchDelete}
            />
          </Col>
          <Col className='flex items-center' span={6}>
            <SearchInput
              placeholder='请输入指标名称'
              value={filter?.name}
              onSearch={val => setFilter({ ...filter, itemName: val })}
            />
          </Col>
          <Col className='flex items-center' span={6}>
            <MetricSetSearchSelect
              placeholder='请选择分类或指标集'
              className='flex-1'
              value={{ treeId: filter.treeId, treeType: filter.treeType }}
              onChange={value => {
                setTreeFilter(value);
              }}
            />
          </Col>
          <Col className='flex items-center' span={6}>
            <TagSelectForSearch
              placeholder='请选择标签'
              className='flex-1'
              tagType='ITEM'
              defaultValue={filter.tagIdList}
              onChange={value => {
                setFilter({
                  ...filter,
                  tagIdList: value,
                });
              }}
            />
          </Col>
        </Row>
      </div>
      <div className='flex-1 overflow-hidden'>
        <CustomTable
          rowKey='id'
          scroll={{ x: '2000px' }}
          cacheId='metricSearch'
          dataSource={list}
          loading={loading}
          columns={columns}
          pagination={pagination}
          onChange={handleTableChange}
          rowSelection={{ selectedRowKeys }}
          onRowSelectionChange={onRowSelectionChange}
        />
      </div>
      {open && (
        <MetricItemModal
          type='edit'
          open={open}
          metricItem={metricItem}
          onCancel={() => setOpen(false)}
          callback={fetchData}
        />
      )}
    </div>
  );
};

export const MetricSearch = () => {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
};
