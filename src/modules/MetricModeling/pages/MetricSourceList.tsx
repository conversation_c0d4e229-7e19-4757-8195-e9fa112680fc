import { useEffect, useState } from 'react';
import { Button, message } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components';
import { useDeleteConfirm, useGlobalHook, useRightsHook } from '@/hooks';

import { AddMetricSourceModal } from '../components';
import { MetricSourceListItem, MetricSourceTypeEnum } from '../models';
import { MetricSourceApi } from '../service';

export const MetricSourceList = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { hasRights } = useRightsHook();
  const [isDetail, setDetail] = useState<boolean>(false);
  const { pagination, setPagination, queryParams } = useCustomTableHook({
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    pageSize: 20,
    cacheId: 'metricSource',
  });
  const deleteConfirm = useDeleteConfirm({
    delUrl: 'metric-source',
    prefix: '/api/v2/data-modeling/',
  });

  const [list, setList] = useState<MetricSourceListItem[]>([]);
  const [open, setOpen] = useState(false);
  const [editItem, setEditItem] = useState<MetricSourceListItem>();

  const columns: Array<ColumnType<MetricSourceListItem>> = [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      render: (type: keyof typeof MetricSourceTypeEnum) => {
        return MetricSourceTypeEnum[type];
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '操作',
      dataIndex: 'operator',
      width: 220,
      render: (_: any, record) => (
        <>
          <Button className='mr-4 p-0' type='link' size='small' onClick={() => handleDetail(record)}>
            查看
          </Button>
          <Button
            className='mr-4 p-0'
            type='link'
            size='small'
            disabled={!hasRights('metric_source:write')}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            className='p-0'
            type='link'
            size='small'
            disabled={!hasRights('metric_source:write')}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </>
      ),
    },
  ];

  const handleCreate = () => {
    setOpen(true);
  };

  const handleEdit = (record: MetricSourceListItem) => {
    setOpen(true);
    setEditItem(record);
  };

  const handleDetail = (record: MetricSourceListItem) => {
    setOpen(true);
    setDetail(true);
    setEditItem(record);
  };

  const handleDelete = async (record: MetricSourceListItem) => {
    const { code } = await deleteConfirm(record.id, `确认删除指标来源「${record.name}」？`);
    if (code === '0000') {
      message.success('删除成功');
      fetchData();
    }
  };

  const fetchData = async () => {
    try {
      const { data, total } = await MetricSourceApi.query(queryParams);
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    setPageInfo({
      title: '指标来源',
      description:
        '指标的数据来源非常多样化，我们可以获取指标数据来自于不同监控系统，包括但不限于Zabbix和蓝鲸等监控系统。',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  return (
    <div className='h-full bg-white flex flex-col'>
      <div className='p-3'>
        <Button type='primary' disabled={!hasRights('metric_source:write')} onClick={handleCreate}>
          创建
        </Button>
      </div>
      <CustomTable className='flex-1' scroll={true} pagination={pagination} columns={columns} dataSource={list} />
      {open && (
        <AddMetricSourceModal
          open={open}
          isDetail={isDetail}
          id={editItem?.id as string}
          onCancel={() => {
            setOpen(false);
            setDetail(false);
            setEditItem(undefined);
          }}
          callback={() => {
            setOpen(false);
            setDetail(false);
            setEditItem(undefined);
            fetchData();
          }}
        />
      )}
    </div>
  );
};
