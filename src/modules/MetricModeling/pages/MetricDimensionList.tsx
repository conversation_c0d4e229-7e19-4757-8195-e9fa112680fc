import { useEffect, useState } from 'react';
import { Button, message, Radio, Space, Tag } from 'antd';
import { Link } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import BatchActions from '@/components/business/ui/BatchActions';
import { useDeleteConfirm, useGlobalHook, useRightsHook } from '@/hooks';

import { MetricDimensionEditModal } from '../components/MetricDimensionLabel/MetricDimensionEditModal';
import { MetricDimension } from '../models';
import { MetricDimensionApi } from '../service';

export const MetricDimensionList = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { hasRights } = useRightsHook();
  const {
    pagination,
    setPagination,
    queryParams,
    handleTableChange,
    filter,
    sort,
    selectedRowKeys,
    setSelectedRowKeys,
    setFilter,
    onRowSelectionChange,
  } = useCustomTableHook({
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    pageSize: 20,
    cacheId: 'metricDimension',
  });
  const deleteConfirm = useDeleteConfirm({
    delUrl: 'metric/dimension',
    prefix: '/api/v2/data-modeling/',
  });

  const [list, setList] = useState<MetricDimension[]>([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editItem, setEditItem] = useState<MetricDimension>();

  const columns: Array<ColumnType<MetricDimension>> = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (name, record) => {
        return (
          <>
            {record.action === 'default' && <Tag>内置</Tag>}
            <Link to={`/metric/dimension/detail/${record.id}`}>{name}</Link>
          </>
        );
      },
    },
    {
      title: '维度键',
      dataIndex: 'code',
    },
    {
      title: '关键维度',
      dataIndex: 'isKeyDimension',
      width: 150,
      render: isKeyDimension => {
        return isKeyDimension ? '是' : '否';
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'operator',
      fixed: 'right',
      width: 150,
      render: (_: any, record) => (
        <>
          <Button
            className='mr-4 p-0'
            type='link'
            size='small'
            disabled={!hasRights('metric_source:write')}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            className='p-0'
            type='link'
            size='small'
            disabled={!hasRights('metric_source:write')}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </>
      ),
    },
  ];

  const handleCreate = () => {
    setOpen(true);
  };

  const handleEdit = (record: MetricDimension) => {
    setOpen(true);
    setEditItem(record);
  };

  const handleDelete = async (record: MetricDimension) => {
    const { code } = await deleteConfirm(record.id, `确认删除维度标签「${record.name}」？`);
    if (code === '0000') {
      message.success('删除成功');
      fetchData();
      setSelectedRowKeys([]);
    }
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data, total } = await MetricDimensionApi.getList(queryParams).finally(() => setLoading(false));
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    setPageInfo({
      title: '维度标签',
      description: '定义指标模型中维度标签的命名标准。',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  return (
    <div className='h-full bg-white flex flex-col'>
      <div className='p-3 flex justify-between items-center'>
        <Space>
          <BatchActions type='dimLabel' ids={selectedRowKeys} condition={{ filter, sort }} />
          <Button type='primary' disabled={!hasRights('metric_source:write')} onClick={handleCreate}>
            创建
          </Button>
          <SearchInput
            placeholder='请输入名称'
            defaultValue={filter.name}
            onSearch={name => {
              setFilter({
                ...filter,
                name,
              });
            }}
          />
          <label className='text-sm'>关键维度</label>
          <Radio.Group
            defaultValue={filter.isKeyDimension}
            onChange={e => setFilter({ ...filter, isKeyDimension: e.target.value })}
          >
            <Radio>全部</Radio>
            <Radio value={true}>是</Radio>
            <Radio value={false}>否</Radio>
          </Radio.Group>
        </Space>
        <i className='iconfont icon-refresh-line cursor-pointer pull-right' onClick={() => fetchData()}></i>
      </div>
      <CustomTable
        loading={loading}
        className='flex-1'
        scroll={true}
        pagination={pagination}
        columns={columns}
        dataSource={list}
        rowSelection={{ selectedRowKeys }}
        onChange={handleTableChange}
        onRowSelectionChange={onRowSelectionChange}
      />
      {open && (
        <MetricDimensionEditModal
          open={open}
          editItem={editItem}
          onCancel={() => {
            setOpen(false);
            setEditItem(undefined);
          }}
          onCallback={() => {
            setOpen(false);
            setEditItem(undefined);
            fetchData();
          }}
        />
      )}
    </div>
  );
};
