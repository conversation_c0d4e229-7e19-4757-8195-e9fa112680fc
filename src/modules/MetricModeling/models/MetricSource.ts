import { MetricDimension } from './MetricDimension';

export enum MetricSourceTypeEnum {
  ZABBIX = 'ZABBIX',
  OTHER = '其他',
}

export interface MetricSourceParams {
  id: string;
  name: string;
  type: keyof typeof MetricSourceTypeEnum;
  config: string; // 动态表单JSON
  description: string;
}

export interface MetricSourceListItem {
  id: string;
  name: string;
  type: string;
  config: any;
  description: string;
  createTime: string;
  updateTime: string;
  createUserName: string;
  updateUserName: string;
}

export interface DimensionRuleItem {
  id: string;
  metricDimensionId?: string; // 指标维度Id
  extractionMethod?: string; // 提取方式,JSON提取:JSON_PATH, 正则提取:REGEX, 解构提取:DISSECT
  jsonPath?: string;
  regex?: string; // 正则
  dissectPattern?: string; // 解构提取表达式
  testJson?: string;
  metricDimensionResp?: MetricDimension;
}

export interface MetricBindSourceParams {
  id: string;
  metricId: string; // 指标id
  metricSourceId: string; // 指标数据源id
  bindKeyword: string; // 匹配的关键字
  dimensionRule: DimensionRuleItem[];
}

export interface MetricBindSourceListItem {
  id: string;
  metricId: string;
  metricSourceId: string;
  bindKeyword: string;
  dimensionRule: DimensionRuleItem[];
  createTime: string;
  createUserName: string;
  updateTime: string;
  updateUserName: string;
  metricSourceResp: MetricSourceListItem;
}
