import { create } from 'zustand';

import { MetricItemTreeNode } from '../models';
import { MetricItemApi } from '../service';
import { setNodeInfo } from '../utils';

interface State {
  treeFilter: {
    treeId?: string;
    treeType?: string;
  };
  searchKey?: string;
  treeData: MetricItemTreeNode[];
  fetching: boolean;
  timestamp: number; // 树结构创建指标项后，保证列表刷新的监听变量
  actions: {
    setTimestamp: (timestamp: number) => void;
    setSearchKey: (searchKey: string) => void;
    fetchTreeData: (refresh?: boolean) => Promise<void>;
    setTreeFilter: (data: { treeId?: string; treeType?: string }) => void;
  };
}

const formatTreeData = (treeData: MetricItemTreeNode[]) => {
  return treeData.map(node => {
    setNodeInfo(node);
    node.children = formatTreeData(node.children);
    return node;
  });
};

export const useMetricModelingStore = create<State>((set, get) => ({
  treeFilter: {
    treeId: undefined,
    treeType: undefined,
  },
  searchKey: '',
  treeData: [],
  fetching: false,
  timestamp: 0,
  actions: {
    setTreeFilter: (data: { treeId: string; treeType: string }) => {
      set({
        treeFilter: {
          ...data,
        },
      });
    },
    setTimestamp: (timestamp: number) => set({ timestamp }),
    setSearchKey: (searchKey: string) => {
      const {
        actions: { fetchTreeData },
      } = get();
      set({ searchKey });
      fetchTreeData(true);
    },
    fetchTreeData: async (refresh?: boolean) => {
      const { treeData, searchKey } = get();
      if (refresh || treeData.length === 0) {
        set({
          fetching: true,
        });
        const { data } = await MetricItemApi.getItemTree({ setName: searchKey }).finally(() => {
          set({
            fetching: false,
          });
        });
        set({
          treeData: formatTreeData(data??[]),
        });
      }
    },
  },
}));
