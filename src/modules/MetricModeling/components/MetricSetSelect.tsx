import { useMemo } from 'react';
import { TreeSelectProps } from 'antd';
import { cloneDeep } from 'lodash';

import { TreeSelect } from '@/components/form/TreeSelect';

import { MetricItemTreeNode } from '../models';
import { useMetricModelingStore } from '../store/useMetricModelingStore';

interface Props extends TreeSelectProps {
  disableId?: string;
  value?: string;
  onChange?: (value?: string) => void;
}
export const MetricSetSelect = (props: Props) => {
  const { disableId, ...otherProps } = props;

  const formatTreeData = (treeData: MetricItemTreeNode[]) => {
    return treeData.map(node => {
      node.name = `${node.name}(${node.count})`;
      if (node.type === 'CATEGORY') {
        node.children = formatTreeData(node.children);
        node.disabled = true;
      } else if (node.type === 'OBJECT') {
        node.children = formatTreeData(node.children);
        node.disabled = true;
      } else if (node.type === 'METRIC_SET') {
        node.disabled = false;
        node.isLeaf = true;
        node.children = [];
      }
      return node;
    });
  };

  const treeData = useMetricModelingStore(state => state.treeData);

  const computedTreeData = useMemo(() => {
    return formatTreeData(cloneDeep(treeData));
  }, [treeData]);

  return (
    <TreeSelect
      className='custom-directory-tree hidden-leaf-switcher bg-transparent'
      treeData={computedTreeData}
      fieldNames={{ label: 'name', value: 'id' }}
      treeIcon
      allowClear
      {...otherProps}
    />
  );
};
