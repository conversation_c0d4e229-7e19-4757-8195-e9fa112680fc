import { useEffect, useState } from 'react';
import { Button, message, Modal, Tooltip } from 'antd';
import { cloneDeep } from 'lodash';

import { CustomTable } from '@/components';
import { useRightsHook } from '@/hooks';

import { MetricBindSourceCreate } from '../components';
import { ExtractionMethodEnum } from '../constants';
import { DimensionRuleItem, MetricBindSourceListItem, MetricDimension, MetricItemDetail } from '../models';
import { MetricSourceApi } from '../service';

interface Props {
  detail?: MetricItemDetail;
}

export const MetricBindSourceList = ({ detail }: Props) => {
  const { hasRights } = useRightsHook();
  const [list, setList] = useState<MetricBindSourceListItem[]>([]);
  const [open, setOpen] = useState(false);
  const [editDetail, setEditDetail] = useState<any>();

  const columns: Array<ColumnType<MetricBindSourceListItem>> = [
    {
      title: '指标来源',
      dataIndex: 'metricSourceId',
      width: 200,
      render: (_, record) => `${record.metricSourceResp?.name}`,
    },
    {
      title: '指标匹配',
      dataIndex: 'bindKeyword',
      width: 200,
    },
    {
      title: '维度提取',
      dataIndex: 'dimensionRule',
      ellipsis: true,
      width: 300,
      render: dimensionRule => {
        return (
          <div className='w-[300px] overflow-hidden text-ellipsis'>
            <Tooltip
              placement='topLeft'
              title={dimensionRule.map(
                (x: DimensionRuleItem) =>
                  `${x?.metricDimensionResp?.name ?? '-'}：${ExtractionMethodEnum[x.extractionMethod!]} `,
              )}
            >
              {dimensionRule.map(
                (x: DimensionRuleItem) =>
                  `${x?.metricDimensionResp?.name ?? '-'}：${ExtractionMethodEnum[x.extractionMethod!]} `,
              )}
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 120,
      render: (_, record) => (
        <>
          <Button
            className='mr-4'
            type='link'
            size='small'
            onClick={() => handleEdit(record)}
            disabled={!hasRights('metric_item:source')}
          >
            编辑
          </Button>
          <Button
            className='mr-4'
            type='link'
            size='small'
            disabled={!hasRights('metric_item:source')}
            onClick={() => handleDelete(record)}
          >
            移除
          </Button>
        </>
      ),
    },
  ];

  const handleEdit = (record: MetricBindSourceListItem) => {
    const dimTagList = detail?.dimTagList ?? [];
    const dimensionRule = record?.dimensionRule ?? [];

    const cloneRecord = cloneDeep(record) as MetricBindSourceListItem & MetricDimension;

    cloneRecord.dimensionRule = dimTagList.map(item => {
      const matchItem = dimensionRule.find(x => x.metricDimensionResp?.id === item.id);
      return {
        ...item,
        ...matchItem,
      };
    });

    setEditDetail(cloneRecord);
    setOpen(true);
  };

  const handleDelete = (record: MetricBindSourceListItem) => {
    Modal.confirm({
      title: `确定移除关联指标源「${record.metricSourceResp?.name}」？`,
      onOk: async () => {
        try {
          await MetricSourceApi.deleteBind(record.id);
          message.success('移除成功');
          fetchBindList();
        } catch (e: any) {
          message.error(e.msg);
        }
      },
    });
  };

  const fetchBindList = async () => {
    if (!detail) return;
    try {
      const { data } = await MetricSourceApi.getBindList(detail.id);
      setList(data);
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchBindList();
  }, [detail]);

  return (
    <div className='pb-10'>
      <div className='mb-2 px-4'>
        <Button type='primary' disabled={!hasRights('metric_item:source')} onClick={() => setOpen(true)}>
          关联指标来源
        </Button>
      </div>
      <CustomTable className='flex-1' columns={columns} dataSource={list} pagination={false} />
      {open && (
        <MetricBindSourceCreate
          open={open}
          detail={detail}
          editDetail={editDetail}
          onCancel={() => {
            setOpen(false);
            setEditDetail(undefined);
          }}
          callback={fetchBindList}
        />
      )}
    </div>
  );
};
