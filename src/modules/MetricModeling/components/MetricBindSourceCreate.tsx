import { useEffect, useState } from 'react';
import { Form, Input, message, Modal, ModalProps } from 'antd';
import { useParams } from 'umi';

import { MetricSourceSelect } from '@/components/business/form/select/MetricSourceSelect';

import { DimensionRuleItem, MetricBindSourceListItem, MetricItemDetail } from '../models';
import { MetricSourceApi } from '../service';

import { DimensionConfigAndTest } from './DimensionConfigAndTest';

interface Props extends ModalProps {
  detail?: MetricItemDetail;
  editDetail?: MetricBindSourceListItem;
  callback?: () => void;
}

export const MetricBindSourceCreate = ({ detail, editDetail, callback, ...otherProps }: Props) => {
  const { id } = useParams();

  const [dimensionRuleList, setDimensionRuleList] = useState<DimensionRuleItem[]>([]);
  const [form] = Form.useForm();

  const onOk = async e => {
    const values = await form.validateFields();

    const needMsg = checkDimensionRuleList(dimensionRuleList);
    if (needMsg) return message.warning('请补充关键维度提取规则');

    try {
      await MetricSourceApi[editDetail ? 'updateBind' : 'createBind']({
        metricId: id as string,
        ...values,
        dimensionRule: dimensionRuleList,
      });
      message.success(editDetail ? '修改成功' : '关联成功');
      otherProps.onCancel?.(e);
      callback?.();
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  const checkDimensionRuleList = dimensionRuleList => {
    let needMsg = false;

    for (let i = 0; i < dimensionRuleList.length; i++) {
      const item = dimensionRuleList[i];

      if (!item.isKeyDimension) return false;
      if (!item.extractionMethod) return true;

      switch (item.extractionMethod) {
      case 'JSON_PATH':
        needMsg = !item.jsonPath;
        break;
      case 'REGEX':
        needMsg = !item.jsonPath || !item.regex;
        break;
      case 'DISSECT':
        needMsg = !item.jsonPath || !item.dissectPattern;
        break;
      default:
        needMsg = true;
        break;
      }
    }
    return needMsg;
  };

  useEffect(() => {
    if (editDetail) return;
    const newList = detail?.dimTagList?.map(x => {
      return {
        ...x,
        metricDimensionId: x.id,
        extractionMethod: 'JSON_PATH',
        jsonPath: x.isKeyDimension ? `$.tags.${x.code}` : `$.dimensions.${x.code}`,
      };
    });
    setDimensionRuleList(newList ?? []);
  }, [detail]);

  useEffect(() => {
    if (!editDetail) return;
    form.setFieldsValue(editDetail);
    setDimensionRuleList(
      editDetail?.dimensionRule?.map(x => ({
        ...x,
        metricDimensionId: x.metricDimensionId ?? x.id,
      })) ?? [],
    );
  }, [editDetail]);

  return (
    <Modal
      title='关联指标来源'
      width={800}
      {...otherProps}
      onOk={onOk}
      styles={{ body: { height: 568, overflow: 'auto' } }}
    >
      <div className='flex items-center mb-2'>
        <i className='iconfont icon-error-fill text-[#FFB232]' />
        <p className='text-[#999] ml-1'>
          为指标 <span className='text-[rgba(0,0,0,0.85)]'>{detail?.name}</span> 添加指标来源
        </p>
      </div>
      <Form form={form} labelCol={{ style: { width: '100px' } }}>
        <Form.Item name='id' hidden>
          <Input />
        </Form.Item>
        <Form.Item label='源指标来源' name='metricSourceId' rules={[{ required: true }]}>
          <MetricSourceSelect />
        </Form.Item>
        <Form.Item label='源指标匹配' name='bindKeyword' rules={[{ required: true }]}>
          <Input placeholder='请输入' />
        </Form.Item>
      </Form>
      <p className='pl-8 mb-2 text-[rgba(0,0,0,0.65)]'>
        维度提取：
        <span className='text-[#999]'>
          为标准化指标数据，设置维度标签提取的规则，关键维度必需设置，非关键维度可以不设置
        </span>
      </p>
      {detail && <DimensionConfigAndTest value={dimensionRuleList} onChange={setDimensionRuleList} />}
    </Modal>
  );
};
