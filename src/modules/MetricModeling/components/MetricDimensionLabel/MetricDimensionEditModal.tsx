import { useEffect } from 'react';
import { Form, message, Modal, ModalProps } from 'antd';

import { MetricDimension } from '../../models';
import { MetricDimensionApi } from '../../service';

import { LabelCreateForm } from './LabelSelectModal';

interface Props extends ModalProps {
  editItem?: MetricDimension;
  onCallback?: () => void;
}
export const MetricDimensionEditModal = (props: Props) => {
  const { editItem, onCallback, ...otherProps } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (editItem) {
      form.setFieldsValue(editItem);
    }
  }, [editItem]);

  const handleOk = async () => {
    const values = await form.validateFields();
    await MetricDimensionApi[editItem ? 'update' : 'create'](values);
    message.success(editItem ? '更新成功' : '创建成功');
    onCallback?.();
  };
  return (
    <Modal title={`${editItem ? '编辑维度标签' : '创建维度标签'}`} {...otherProps} onOk={handleOk}>
      <LabelCreateForm form={form} />
    </Modal>
  );
};
