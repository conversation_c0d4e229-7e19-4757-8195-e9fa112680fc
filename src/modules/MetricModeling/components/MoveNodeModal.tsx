import React, { useEffect } from 'react';
import { Form, Input, message, Modal, ModalProps } from 'antd';

import { ObjectCategoryApi } from '@/modules/ObjectModeling/services';

import { SelectParentTree } from './SelectParentTree';

interface Props extends ModalProps {
  moveItem: object;
  onCallback?: () => void;
}
export const MoveNodeModal: React.FC<Props> = props => {
  const { moveItem, onCallback, ...otherProps } = props;
  const [form] = Form.useForm();

  const onOk = async e => {
    const values = await form.validateFields();
    await ObjectCategoryApi.move(values);
    onCallback?.();
    otherProps.onCancel?.(e);
    message.success('移动成功');
  };

  useEffect(() => {
    const { id, parentId, type } = moveItem;
    console.log(moveItem);
    form.setFieldsValue({
      id,
      parentId,
      type,
    });
  }, [moveItem]);

  return (
    <Modal title='移动' width={760} styles={{ body: { padding: '10px 20px' } }} {...otherProps} onOk={onOk}>
      <Form form={form} labelCol={{ span: 24 }}>
        <Form.Item label='分类id' name='id' hidden>
          <Input placeholder='请输入' />
        </Form.Item>
        <Form.Item label='类型' name='type' hidden>
          <Input placeholder='请输入' />
        </Form.Item>
        <Form.Item label='请选择父级分类' name='parentId'>
          <SelectParentTree disableId={moveItem.id} isRootDisabled={moveItem.type === 'OBJECT'} height={300} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
