import { useEffect, useMemo, useState } from 'react';
import { Form, Input, message, Modal, ModalProps } from 'antd';
import { cloneDeep } from 'lodash';

import { TreeSelect } from '@/components/form/TreeSelect';

import { MetricItemTreeNode, ModalType } from '../models';
import { MetricSetApi } from '../service';

interface MetricSetModalProps extends ModalProps {
  treeData: MetricItemTreeNode[];
  type?: ModalType;
  objectModeling?: MetricItemTreeNode;
  metricSet?: MetricItemTreeNode;
  categoryId?: string;
  onCallback?: () => void;
}

export const MetricSetModal = ({
  treeData,
  type,
  objectModeling,
  metricSet,
  onCallback,
  categoryId,
  ...otherProps
}: MetricSetModalProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [belongType, setBelongType] = useState('');

  const formatTreeData = (treeData: any[]) => {
    const cloneTreeData = cloneDeep(treeData.filter(item => item.type !== 'METRIC_SET'));
    return cloneTreeData.map(node => {
      if (node.type === 'CATEGORY' && node.categoryType === 'custom') {
        node.disabled = false;
      }else if (node.type === 'OBJECT') {
        node.isLeaf = true;
        node.disabled = false;
        node.children = [];
      } else {
        node.disabled = true;
      }
      node.children = formatTreeData(node.children);
      return node;
    });
  };

  const title = useMemo(() => {
    switch (type) {
    case 'create':
      return '创建指标集';
    case 'edit':
      return '编辑指标集';
    }
  }, [type]);

  const onOk = async e => {
    const values = await form.validateFields();
    if (belongType === 'CATEGORY') {
      values.categoryId = values.belongId;
    } else {
      values.objId = values.belongId;
    }
    delete values.belongId;
    if (type === 'create') {
      delete values.id;
      setLoading(true);
      await MetricSetApi.createSet(values).finally(() => {
        setLoading(false);
      });
      message.success('创建成功');
      onCallback?.();
      otherProps.onCancel?.(e);
      setLoading(false);
    } else if (type === 'edit') {
      await MetricSetApi.updateSet(values).finally(() => {
        setLoading(false);
      });
      message.success('编辑成功');
      onCallback?.();
      otherProps.onCancel?.(e);
      setLoading(false);
    }
  };

  const getDetail = async (id: string) => {
    const { data } = await MetricSetApi.getDetail(id);
    form.setFieldsValue(data);
    form.setFieldValue('belongId', data.objId || data.categoryId);
    setBelongType(data.objId ? 'OBJECT' : 'CATEGORY');
  };

  const handleTreeSelect = (_, e) => {
    setBelongType(e.type);
  };

  useEffect(() => {
    if (objectModeling?.id) { 
      form.setFieldValue('belongId', objectModeling.id);
      setBelongType('OBJECT');
    }
    if (categoryId) {
      setBelongType('CATEGORY');
      form.setFieldValue('belongId', categoryId);
    }
    if (metricSet) {
      getDetail(metricSet?.id);
    }
  }, []);

  return (
    <Modal width={760} title={title} confirmLoading={loading} {...otherProps} onOk={onOk}>
      <Form form={form} labelCol={{ span: 4 }}>
        <Form.Item label='id' name='id' hidden>
          <Input />
        </Form.Item>
        <Form.Item label='指标集名称' name='name' rules={[{ required: true, message: '请输入指标集名称' }]}>
          <Input placeholder='请输入' allowClear />
        </Form.Item>
        <Form.Item label='对象模型/分类' name='belongId' rules={[{ required: true, message: '请选择对象模型或指标分类' }]}>
          <TreeSelect
            placeholder='请选择'
            treeNodeFilterProp='title'
            fieldNames={{ label: 'name', value: 'id', children: 'children' }}
            treeIcon
            allowClear
            treeData={formatTreeData(treeData)}
            filterTreeNode
            onSelect={handleTreeSelect}
          />
        </Form.Item>
        <Form.Item label='描述' name='description'>
          <Input.TextArea allowClear placeholder='请输入' autoSize={{ minRows: 3, maxRows: 8 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
