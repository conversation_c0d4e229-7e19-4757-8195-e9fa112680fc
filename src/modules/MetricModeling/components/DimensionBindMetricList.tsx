import { useEffect, useState } from 'react';
import { message, Tag } from 'antd';
import { Link } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';

import { sampleFrequencyUnitOptions } from '../constants';
import { MetricItemListItem } from '../models';
import { MetricItemApi } from '../service';

interface Props {
  dimensionId: string;
}

export const DimensionBindMetricList = ({ dimensionId }: Props) => {
  const [list, setList] = useState<MetricItemListItem[]>([]);
  const [loading, setLoading] = useState(false);

  const { pagination, setPagination, queryParams, handleTableChange, filter, setFilter } = useCustomTableHook({
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    filter: {
      dimensionId,
    },
    pageSize: 20,
  });

  const columns: Array<ColumnType<MetricItemListItem>> = [
    {
      title: '指标中文名称',
      dataIndex: 'name',
      fixed: 'left',
      render: (name, record) => (
        <Link to={`/metric/modeling/detail/${record?.id}`} target='_blank'>
          {name}
        </Link>
      ),
    },
    {
      title: '英文名称',
      dataIndex: 'nameEn',
    },
    {
      title: '指标分类',
      ellipsis: true,
      dataIndex: 'category',
    },
    {
      title: '维度标签',
      dataIndex: 'dimTagList',
      render(dimTagList) {
        return (
          <div>
            {dimTagList.map(x => (
              <Tag key={x.id}>{x.name}</Tag>
            ))}
          </div>
        );
      },
    },
    {
      title: '对象模型',
      ellipsis: true,
      dataIndex: 'objName',
    },
    {
      title: '指标集',
      dataIndex: 'msetName',
    },
    {
      title: '度量单位',
      dataIndex: 'unitName',
      width: 80,
    },
    {
      title: '采样频率',
      dataIndex: 'sampleFrequency',
      width: 80,
      render: (_, record) =>
        `${record.sampleFrequency}(${
          sampleFrequencyUnitOptions.find(x => x.value === record.sampleFrequencyUnit)?.label
        })`,
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
  ];

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data, total } = await MetricItemApi.queryItem(queryParams).finally(() => setLoading(false));
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
    } catch (e: any) {
      message.error(e.msg);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <>
      <div className='pb-2 px-3'>
        <SearchInput
          className='w-52'
          placeholder='请输入指标项名称'
          defaultValue={filter.itemName}
          onSearch={itemName => setFilter({ ...filter, itemName })}
        />
      </div>
      <CustomTable
        loading={loading}
        className='flex-1'
        columns={columns}
        dataSource={list}
        pagination={pagination}
        onChange={handleTableChange}
      />
    </>
  );
};
