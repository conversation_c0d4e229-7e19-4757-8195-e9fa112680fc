import { create } from 'zustand';

import { ObjectModelingTreeNode } from '../model';
import { ObjectModelingApi } from '../services/ObjectModelingApi';

type ModelingType = 'object' | 'relation';

interface State {
  searchKey?: string;
  currentModeling: ModelingType;
  treeData: ObjectModelingTreeNode[];
  categoryId: string;
  fetching: boolean;
  changeCurrentModeling: (name: ModelingType) => void;
  fetchTreeData: (refresh?: boolean, searchKey?: string) => Promise<void>;
  setSearchKey: (searchKey?: string) => void;
  setCategoryId: (categoryId?: string) => void;
}

const formatTreeData = (treeData: ObjectModelingTreeNode[]) => {
  return treeData.map(node => {
    if (node.type === 'CATEGORY') {
      node.isLeaf = false;
      if (node.parentId === '0') {
        node.icon = <i className='iconfont icon-layers-line text-gray-6'></i>;
      } else {
        node.icon = <i className='iconfont icon-classone-line text-gray-6'></i>;
      }
    } else {
      node.isLeaf = true;
      node.icon = <i className='iconfont icon-application-line text-primary text-sm'></i>;
    }
    node.children = formatTreeData(node.children);
    return node;
  });
};

export const useObjectModelingStore = create<State>((set, get) => ({
  searchKey: '',
  currentModeling: 'object',
  treeData: [],
  categoryId: '',
  fetching: false,
  changeCurrentModeling: (name: ModelingType) => {
    set({
      currentModeling: name,
    });
  },
  setSearchKey: (str?: string) => {
    set({
      searchKey: str,
    });
    const { fetchTreeData } = get();
    fetchTreeData(true, str);
  },
  setCategoryId: (str?: string) => {
    set({
      categoryId: str,
    });
  },
  fetchTreeData: async (refresh?: boolean, newSearchKey?: string) => {
    const { treeData, searchKey } = get();
    if (refresh || treeData.length === 0) {
      set({
        fetching: true,
      });
      const { data } = await ObjectModelingApi.getTree({ objectName: newSearchKey ?? searchKey }).finally(() => {
        set({
          fetching: false,
        });
      });
      set({
        treeData: formatTreeData(data??[]),
      });
    }
  },
}));
