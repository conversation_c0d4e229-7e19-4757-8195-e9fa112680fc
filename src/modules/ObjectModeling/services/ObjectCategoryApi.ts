import Request from '@/request';

import { ObjectModelingCategory } from '../model';

const url = '/api/v2/data-modeling/object-category';
export const ObjectCategoryApi = {
  /**
   * 模型树分组列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建模型树分组
   */
  async create(data: Omit<ObjectModelingCategory, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新模型树分组
   * @param data
   * @returns
   */
  async update(data: ObjectModelingCategory) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除模型树分组
   * @param id 模型树分组id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 移动模型树分组
   * @param data
   * @returns
   */
  async move(data: { id: string; parentId?: string; type: string }) {
    const { id, ...rest } = data;
    return await Request.put(`${url}/${id}/move`, { data: rest });
  },
  /**
   * 获取模型树分组详情
   * @param id 模型树分组id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部模型树分组
   * @returns
   */
  async getAll(): Promise<Response<ObjectModelingCategory[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * 获取模型树分组树
   * @returns
   */
  async getTree(
    data?: {
      objectName?: string;
      categoryType?: string;
    },
    options?,
  ) {
    return await Request.post(`${url}/tree`, {
      data: {
        ...data,
      },
      ...options,
    });
  },
};
