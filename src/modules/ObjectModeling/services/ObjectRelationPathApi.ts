import Request from '@/request';

import { RelationPathEntity } from '../model';

const url = '/api/v2/data-modeling/object-relation-path';
export const ObjectRelationPathApi = {
  /**
   * 对象关系路径列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建对象关系路径
   */
  async create(data: Omit<RelationPathEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新对象关系路径
   * @param data
   * @returns
   */
  async update(data: RelationPathEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除对象关系路径
   * @param id 对象关系路径id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取对象关系路径详情
   * @param id 对象关系路径id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部对象关系路径
   * @returns
   */
  async getAll(): Promise<Response<RelationPathEntity[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * 启用对象关系路径
   * @param pathId 对象关系路径id
   * @returns
   */
  async enable(pathId: string) {
    return await Request.put(`${url}/${pathId}/enable`);
  },
  /**
   * 禁用对象关系路径
   * @param pathId 对象关系路径id
   * @returns
   */
  async disable(pathId: string) {
    return await Request.put(`${url}/${pathId}/disable`);
  },
};
