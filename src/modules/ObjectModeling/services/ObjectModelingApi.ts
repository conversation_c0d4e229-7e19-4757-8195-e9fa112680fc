import Request from '@/request';

import { ObjectModelingEntity, ObjectModelingTreeNode, RelationObject } from '../model';

const url = '/api/v2/data-modeling/object-table';
const urlRelatin = '/api/v2/data-modeling/object-relation';
export const ObjectModelingApi = {
  /**
   * 对象模型列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建对象模型
   */
  async create(data: Omit<ObjectModelingEntity, 'id'>) {
    return await Request.post(`${url}`, { data });
  },
  /**
   * 更新对象模型
   * @param data
   * @returns
   */
  async update(data: ObjectModelingEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除对象模型
   * @param id 对象模型id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 移动对象模型
   * @param data
   * @returns
   */
  async move(categoryId: string, parentId?: string) {
    return await Request.put(`${url}/${categoryId}/move`, { parentId });
  },
  /**
   * 获取对象模型详情
   * @param id 对象模型id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部对象模型
   * @returns
   */
  async getAll(): Promise<Response<ObjectModelingEntity[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * 获取对象模型树
   * @returns
   */
  async getTree(data?: { objectName?: string }): Promise<Response<ObjectModelingTreeNode[]>> {
    return await Request.post(`${url}/tree`, { data });
  },
  /**
   * 获取关联对象约束
   * @returns
   */
  async getRelations(id: string): Promise<Response<RelationObject[]>> {
    return await Request.get(`${url}/${id}/relations`);
  },
  /**
   * 批量添加关联约束
   * @returns
   */
  async addRelations(data: { relationTypeId: string; items: RelationObject[] }) {
    return await Request.post(`${urlRelatin}/batch-add`, { data });
  },
  /**
   * 编辑关联约束
   * @returns
   */
  async updateRelations(data: RelationObject) {
    return await Request.put(`${urlRelatin}/${data.id}`, { data });
  },
  /**
   * 删除关联约束
   * @returns
   */
  async deleteRelation(id) {
    return await Request.delete(`${urlRelatin}/${id}`);
  },
  async getGplot() {
    return await Request.post(`${urlRelatin}/gplot`, { data: {} });
  },
};
