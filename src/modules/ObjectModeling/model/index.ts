export interface ObjectModelingTreeNode {
  id: string;
  name: string;
  type: 'CATEGORY' | 'OBJECT';
  parentId: string;
  count: number;
  children: ObjectModelingTreeNode[];
  isLeaf?: boolean;
  deployStatus?: DATA_DEV.DEPLOY_STATUS;
  projectAuth?: ProjectAuthModel;
}

export enum ObjectStateIconEnum {
  'DRAFT' = 'icon-draft-line',
  'OFFLINE' = 'icon-offline-line1',
  'ONLINE' = 'icon-success-line',
}

export enum ObjectStatusEnum {
  'ONLINE' = '已上线',
  'OFFLINE' = '已下线',
  'DRAFT' = '草稿',
}

export interface ObjectModelingCategory {
  id: string;
  name: string;
  parentId?: string;
}

export interface ObjectModelingColumn {
  colNo?: number;
  colType: string; // todo 字段类型
  colName: string;
  colDisplay: string;
  description: string;
  isPrimaryKey: boolean;
  isNotNull: boolean;
  unitId?: string;
  dicId?: string;
  enumId?: string;
  isKeyField?: boolean;
  isIdentificationField?: boolean;
  colAction: 'default' | null;
}

export interface ObjectModelingEntity {
  id: string;
  name: string;
  nameEn: string;
  categoryId: string;
  categoryName: string;
  typeCode: string;
  layerId: string;
  layerName?: string;
  bizId: string;
  bizName?: string;
  procId: string;
  procName?: string;
  domId: string;
  domName?: string;
  domAndProc?: string[];
  columns: ObjectModelingColumn[];
  description: string;
  tbName: string;
  tbType?: string;
  createTime?: string;
  updateTime?: string;
  version?: string | number;
  deployStatus: DATA_DEV.DEPLOY_STATUS;
  tags: string[];
  status: keyof typeof ObjectStatusEnum;
  metrics?: any[]; // todo 要分接口查
  relations?: RelationObject[]; // todo 要分接口查
  isOnline?: boolean;
}

export interface metricSet {
  metricId: string;
  metricName: string;
  metricItems: any[];
  description: string;
}

export interface RelationObject {
  id: string;
  sourceObjId: string;
  sourceObjName?: string;
  targetObjId: string;
  targetObjName?: string;
  relationTypeId: string;
  relationTypeName?: string;
}

export interface ObjectRelationEntity {
  id: string;
  code: string;
  name: string;
  nameEn: string;
  description?: string;
  relations: RelationObject[];
  count: string | number;
  status: boolean;
}

export interface QueryPathRuleItem {
  nodeId: string;
  nodeType: 'TAG' | 'EDGE';
  parentNodeId?: string;
}

// 节点
export interface QueryPathRuleTag extends QueryPathRuleItem {
  nodeType: 'TAG';
  tags: Array<{ ObjectType: 'OBJECT' | 'CATEGORY'; objectId: string }>;
}

// 边
export interface QueryPathRuleEdge extends QueryPathRuleItem {
  nodeType: 'EDGE';
  relations: Array<{ relationId: string; relationName: string }>;
  sourceId: string;
  targetId: string;
}

export interface RelationPathEntity {
  id: string;
  queryName: string;
  queryType: string;
  remark: string;
  queryPathRule: Array<QueryPathRuleItem | QueryPathRuleEdge>;
}
