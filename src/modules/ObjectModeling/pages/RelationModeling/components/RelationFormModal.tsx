import { useEffect, useMemo, useState } from 'react';
import { Form, Input, message, Modal, ModalProps } from 'antd';

import { ObjectRelationEntity } from '@/modules/ObjectModeling/model';
import { ObjectRelationApi } from '@/modules/ObjectModeling/services';

interface Props extends ModalProps {
  detail?: ObjectRelationEntity | null;
  title: string;
  handleCancel: () => void;
  fetchData: () => void;
}

const formItemLayout = {
  labelCol: { style: { width: '92px' } },
  wrapperCol: { span: 20 },
};

export const RelationFormModal = (props: Props) => {
  const { open, handleCancel, detail, title, fetchData } = props;
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const isEdit = useMemo(() => {
    return title?.includes('编辑');
  }, [title]);

  useEffect(() => {
    if (detail) {
      form.setFieldsValue(detail);
    }
  }, [detail]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (!isEdit) {
        delete values.id;
      }
      setConfirmLoading(true);
      const res = await ObjectRelationApi[isEdit ? 'update' : 'create'](values).finally(() => {
        setConfirmLoading(false);
      });
      if (res?.code === '0000') {
        message.success(res?.msg ?? '成功');
        fetchData();
        handleCancel();
      }
    } catch (error) {
      setConfirmLoading(false);
      message.error(error?.msg ?? '失败');
    }
  };

  return (
    <Modal
      open={open}
      title={title}
      onCancel={handleCancel}
      width={760}
      confirmLoading={confirmLoading}
      onOk={handleOk}
    >
      <Form form={form} {...formItemLayout}>
        <Form.Item name='id' hidden>
          <Input />
        </Form.Item>
        <Form.Item name='name' label='关系中文名' rules={[{ required: true }]}>
          <Input placeholder='请输入' allowClear />
        </Form.Item>
        <Form.Item
          name='nameEn'
          label='关系英文名'
          rules={[
            { required: true },
            {
              pattern: /^[A-za-z][0-9a-zA-Z_]{0,}[0-9a-zA-Z]{1,}$/,
              message: '以字母开头，支持与数字或下划线组合；注意不能以下划线结尾',
            },
          ]}
        >
          <Input placeholder='以字母开头，支持与数字或下划线组合；注意不能以下划线结尾' allowClear />
        </Form.Item>
        <Form.Item
          name='code'
          label='关系标识'
          rules={[
            { required: true },
            {
              pattern: /^[A-za-z][0-9a-zA-Z_]{0,}[0-9a-zA-Z]{1,}$/,
              message: '以字母开头，支持与数字或下划线组合；注意不能以下划线结尾',
            },
          ]}
        >
          <Input placeholder='请输入' allowClear disabled={isEdit} />
        </Form.Item>
        <Form.Item name='description' label='描述'>
          <Input.TextArea placeholder='请输入' allowClear />
        </Form.Item>
      </Form>
    </Modal>
  );
};
