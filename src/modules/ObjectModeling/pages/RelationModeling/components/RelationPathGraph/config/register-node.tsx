import { Graph } from '@antv/x6';

import '@antv/x6-react-shape';

import { TagsNode } from '../components/TagsNode';

export const registerNode = ({ objectModelingTreeData, graph }) => {
  Graph.registerNode(
    'tag-node',
    {
      inherit: 'react-shape',
      width: 200,
      height: 32,
      component: <TagsNode objectModelingTreeData={objectModelingTreeData} graph={graph} />,
    },
    true,
  );
};
