import { Descriptions, Tag } from 'antd';

import { ObjectModelingEntity } from '@/modules/ObjectModeling/model';

interface Props {
  data: ObjectModelingEntity;
}

export const ObjectModelingDetail = ({ data }: Props) => {
  const {
    layerName,
    bizName,
    categoryName,
    nameEn,
    typeCode,
    procName,
    tbName,
    name,
    description,
    tags,
    createTime,
    domName,
    updateTime,
  } = data;

  return (
    <>
      <Descriptions column={2} labelStyle={{ width: 150, textAlign: 'right', display: 'inline-block' }}>
        <Descriptions.Item label='对象分类'>{categoryName ?? '-'}</Descriptions.Item>
        <Descriptions.Item label='数仓分层'>{layerName ?? '-'}</Descriptions.Item>
        <Descriptions.Item label='对象中文名'>{name ?? '-'}</Descriptions.Item>
        <Descriptions.Item label='业务分类'>{bizName ?? '-'}</Descriptions.Item>
        <Descriptions.Item label='对象英文名'>{nameEn ?? '-'}</Descriptions.Item>
        <Descriptions.Item label='业务过程'>
          {domName} / {procName}
        </Descriptions.Item>
        <Descriptions.Item label='类型标识' span={2}>
          {typeCode ?? '-'}
        </Descriptions.Item>
        <Descriptions.Item label='表名' span={2}>
          {tbName ?? '-'}
        </Descriptions.Item>
        <Descriptions.Item label='模型标签' span={2}>
          {tags?.map(item => (
            <Tag key={item}>{item}</Tag>
          ))}
        </Descriptions.Item>
        <Descriptions.Item label='创建时间'>{createTime}</Descriptions.Item>
        <Descriptions.Item label='修改时间'>{updateTime}</Descriptions.Item>

        <Descriptions.Item label='描述' span={2}>
          {description ?? '-'}
        </Descriptions.Item>
      </Descriptions>
    </>
  );
};

export default ObjectModelingDetail;