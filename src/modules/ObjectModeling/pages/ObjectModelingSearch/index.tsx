import { useEffect, useRef, useState } from 'react';
import { Button, Col, message, Row, Space, Tag } from 'antd';
import { ColumnType } from 'antd/es/table';
import { cloneDeep } from 'lodash';
import { history, useAliveController } from 'umi';

import { CustomTable, KeepAliveToTab, SearchInput, useCustomTableHook , useKeepAliveTabs } from '@/components';
import BatchActions from '@/components/business/ui/BatchActions';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { ObjectModelingEntity } from '@/modules/ObjectModeling/model';
import { ObjectModelingApi } from '@/modules/ObjectModeling/services';
import { useObjectModelingStore } from '@/modules/ObjectModeling/store/useObjectModelingStore';

import { ObjectCategorySelect } from '../../components/ObjectCategorySelect';

const Page = () => {
  const { hasRights } = useRightsHook();
  const [data, setData] = useState<ObjectModelingEntity[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const debouncedRef = useRef<any>();
  const categoryId = useObjectModelingStore(state => state.categoryId);
  const confirmDelete = useDeleteConfirm({
    prefix: '/api/v2/data-modeling',
    delUrl: '/object-table',
  });
  const fetchTreeData = useObjectModelingStore(state => state.fetchTreeData);
  const { currentCacheNodes } = useKeepAliveTabs();
  const { dropScope } = useAliveController();
  const {
    queryParams,
    pagination,
    handleTableChange,
    filter,
    sort,
    setFilter,
    setTotal,
    onRowSelectionChange,
    setSelectedRowKeys,
    setSelectedRows,
    selectedRowKeys,
    selectedRows,
  } = useCustomTableHook({
    cacheId: 'ObjectModelingSearch',
    sort: {
      updateTime: 'DESC',
    },
  });

  useEffect(() => {
    const copyData = cloneDeep(filter);
    copyData.categoryId = categoryId ?? undefined;
    setFilter(copyData);
  }, [categoryId]);

  const fetchData = async () => {
    setLoading(true);
    const res = await ObjectModelingApi.getList({
      ...queryParams,
      filter: {
        ...filter,
        categoryId: filter.categoryId,
      },
    }).finally(() => {
      setLoading(false);
    });
    if (res?.code === '0000') {
      setData(res?.data ?? []);
      setTotal(res?.total ?? 0);
    }
  };

  useEffect(() => {
    if (debouncedRef.current) clearTimeout(debouncedRef.current);
    debouncedRef.current = setTimeout(() => {
      fetchData();
    }, 300);
  }, [queryParams]);

  const onDelete = async (id: string, name: string) => {
    try {
      await confirmDelete(id ?? '', `确定删除对象模型「${name}」吗`);
      fetchTreeData(true);
      fetchData();
      message.success('删除成功！');
      setSelectedRowKeys([]);
      setSelectedRows([]);
    } catch (error) {
      message.error(error.msg ?? '删除失败');
    }
  };

  const handleBatchDelete = async () => {
    await confirmDelete(selectedRows ?? [], `确认删除勾选对象(共计${selectedRows?.length})个吗？`, {
      delUrl: '/object-table/bulk',
      prefix: '/api/v2/data-modeling',
    });
    fetchData();
    fetchTreeData(true);
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const getCurrentActiveTabUrl = id => {
    return currentCacheNodes?.find(item => item.id.includes(id));
  };

  const gotoDetail = record => {
    const activeTab = getCurrentActiveTabUrl(record.id);
    if (activeTab && activeTab.id !== `/object/modeling/detail/${record?.id}`) {
      dropScope(activeTab.name).catch(() => {});
    }
    history.push(`/object/modeling/detail/${record?.id}`);
  };

  const gotoEdit = record => {
    const activeTab = getCurrentActiveTabUrl(record.id);
    if (activeTab && activeTab.id !== `/object/modeling/edit/${record?.id}`) {
      dropScope(activeTab.name).catch(() => {});
    }
    history.push(`/object/modeling/edit/${record?.id}`);
  };

  const columns: Array<ColumnType<any>> = [
    {
      title: '对象中文名称',
      dataIndex: 'name',
      render: (value, record) => <Button type='link' size='small' onClick={() => gotoDetail(record)}>{value}</Button>,
    },
    {
      title: '英文名称',
      dataIndex: 'nameEn',
    },
    {
      title: '对象分类',
      ellipsis: true,
      dataIndex: 'categoryPath',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      render: value => (
        <span>
          {value?.map(item => (
            <Tag key={item}>{item}</Tag>
          ))}
        </span>
      ),
    },
    {
      title: '类型标识',
      dataIndex: 'typeCode',
    },
    {
      title: '表名',
      dataIndex: 'name',
    },
    {
      title: '描述',
      dataIndex: 'tbName',
    },
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type='link'
            size='small'
            className='p-0 border-none bg-white bg-opacity-0'
            onClick={() => gotoEdit(record)}
            disabled={!hasRights('object_model:write')}
          >
            编辑
          </Button>
          <Button
            type='link'
            onClick={() => onDelete(record?.id, record?.name)}
            disabled={!hasRights('object_model:write')}
            className='px-0'
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className='h-full w-full overflow-hidden flex flex-col'>
      <div className='p-3 flex'>
        <Row gutter={[8, 8]} className='flex-1'>
          <Col>
            <BatchActions
              type='object'
              ids={selectedRowKeys}
              condition={{ filter, sort }}
              disabled={!hasRights('object_model:write')}
              onDeleteItems={handleBatchDelete}
            />
          </Col>
          <Col className='flex items-center' span={8}>
            <div className='shrink-0'>模型名称：</div>
            <SearchInput
              placeholder='请输入'
              onSearch={value => {
                const copyData = cloneDeep(filter);
                copyData.name = value ?? '';
                setFilter(copyData);
              }}
            />
          </Col>
          <Col className='flex items-center' span={8}>
            <div className='shrink-0'>对象分类：</div>
            <ObjectCategorySelect
              placeholder='请选择'
              className='flex-1'
              value={filter?.categoryId}
              onChange={value => {
                const copyData = cloneDeep(filter);
                copyData.categoryId = value ?? '';
                setFilter(copyData);
              }}
            />
          </Col>
        </Row>
        <Button type='text' className='shrink-0' onClick={() => fetchData()}>
          <i className='iconfont icon-refresh-line'></i>
        </Button>
      </div>

      <div className='flex-1 overflow-hidden'>
        <CustomTable
          dataSource={data}
          loading={loading}
          scroll={{ x: '1400px' }}
          columns={columns}
          rowSelection={{ selectedRowKeys }}
          pagination={pagination}
          onRowSelectionChange={onRowSelectionChange}
          onChange={handleTableChange}
        />
      </div>
    </div>
  );
};

export const ObjectModelingSearch = () => {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
};
