import { useEffect, useMemo, useState } from 'react';
import hljs from 'highlight.js';
import { marked } from 'marked';

import './MarkdownViewer.less';
import 'github-markdown-css';

import 'highlight.js/styles/default.css';

interface Props {
  display: string;
  data: string;
  jobName: string;
  options?: Record<string, any>;
}
export const MarkdownViewer: React.FC<Props> = ({ data, display, jobName, options }) => {
  const [markdownHtml, setMarkdownHtml] = useState<string | undefined>();
  const [currentHash, setCurrentHash] = useState<string>();
  const defaultOption = {
    // renderer: new marked.Renderer(),
    highlight(data) {
      return hljs.highlightAuto(data).value;
    },
    pedantic: false,
    gfm: true,
    tables: true,
    breaks: false,
    sanitize: false,
    smartLists: true,
    smartypants: false,
    xhtml: false,
  };

  const headings = useMemo(() => {
    const tokens = marked.lexer(data);
    return tokens
      .filter((item: marked.Token) => item.type === 'heading')
      .map((item: marked.Token) => {
        return {
          text: item.text,
          depth: item.depth,
        };
      });
  }, [data]);

  useEffect(() => {
    let i = 0;
    const renderer = {
      heading(text: string, level: number) {
        i++;
        return `
          <h${level}>
            <a name="key${i}" class="anchor" href="#key${i}">
              <span class="header-link"></span>
            </a>
            ${text}
          </h${level}>`;
      },
      link(href: string, title: string, text: string) {
        return `<a href="${href}" target="_blank">${text}</a>`;
      },
    };

    marked.use({ renderer });

    setMarkdownHtml(marked.parse(data, defaultOption));

    setCurrentHash(location?.hash);
  }, []);

  return (
    <div className='markdown-viewer'>
      <div className='markdown-viewer-inner'>
        <div className='text-center'>
          <div className='title'>{display}</div>
          <div className='subTitle'>{jobName}</div>
        </div>
        <div className='flex items-start'>
          <div className='content markdown-body flex-1 mr-2'>
            <div
              dangerouslySetInnerHTML={{
                __html: markdownHtml ?? '',
              }}
            ></div>
          </div>
          <div className='content-nav'>
            <ul>
              {headings.map((heading, index) => (
                <li key={index} className={currentHash === `#key${index + 1}` ? 'selected' : ''}>
                  <a
                    onClick={() => setCurrentHash(`#key${index + 1}`)}
                    href={`#key${index + 1}`}
                    style={{ paddingLeft: `${(heading.depth - 1) * 20}px` }}
                  >
                    {heading.text}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
