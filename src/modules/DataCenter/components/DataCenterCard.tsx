import { useEffect, useState } from 'react';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Avatar, Button, Divider, Popover } from 'antd';

import BigLayerIcon from '@/assets/images/data-center.svg';
import { DataCenterApi } from '@/services';

interface Props {
  item: DATA_CENTER.DataCenterEntity;
  onEdit: (item: DATA_CENTER.DataCenterEntity) => void;
  onDelete: (item: DATA_CENTER.DataCenterEntity) => void;
}

const PopoverContent = ({ id, type }: { id: string; type: DATA_CENTER.ReferenceType }) => {
  const [list, setList] = useState<any[]>([]);
  useEffect(() => {
    DataCenterApi.getReference({
      dataCenterId: id,
      type,
    }).then(({ data }) => {
      setList(data ?? []);
    });
  }, [id, type]);
  return <div>{list.map(x => x.name).join('、') || '无'}</div>;
};

export const DataCenterCard: React.FC<Props> = ({ item, onEdit, onDelete }) => {
  return (
    <section className='border border-solid border-gray-1 rounded-sm flex flex-col h-full'>
      <div className='flex-1 flex pt-4 pl-4 pr-4 pb-2 relative'>
        <Avatar size={48} shape='square' src={BigLayerIcon} />
        <div className='flex-1 ml-2 flex flex-col'>
          <div className='text-header font-medium text-base mb-1 leading-6 break-all line-clamp-1' title={item.name}>
            {item.name}
          </div>
          <div className='text-gray-5 text-xs mb-2 flex-1 line-clamp-2 break-all' title={item.description}>
            {item.description ?? '暂无描述'}
          </div>
          <div className='text-xs flex justify-between text-center my-3 px-2 cursor-pointer'>
            {item.groupCountResultList.map((group, index) => (
              <Popover
                title={group.typeDesc}
                content={<PopoverContent id={item.id} type={group.type}></PopoverContent>}
                placement='bottom'
                key={index}
                overlayInnerStyle={{ maxWidth: 200, wordBreak: 'break-all' }}
              >
                <div className='flex flex-col'>
                  <span className='text-gray-6 mb-2'>{group.typeDesc}</span>
                  <span className='text-gray-7'>{group.number ?? 0}</span>
                </div>
              </Popover>
            ))}
          </div>
          <div className='text-xs text-gray'>
            <label>最后更新时间：</label>
            <span>{item.updateTime}</span>
          </div>
        </div>
      </div>
      <div className='bg-neutral-50 flex items-center border-0 border-t border-solid border-gray-1'>
        <Button className='flex-1 bg-transparent border-0 text-base text-black h-10' onClick={() => onEdit(item)}>
          <EditOutlined />
          编辑
        </Button>
        <Divider type='vertical' />
        <Button className='flex-1 bg-transparent border-0 text-base text-black h-10' onClick={() => onDelete(item)}>
          <DeleteOutlined />
          删除
        </Button>
      </div>
    </section>
  );
};
