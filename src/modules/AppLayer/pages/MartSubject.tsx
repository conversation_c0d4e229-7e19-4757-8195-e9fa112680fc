import { useEffect, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Allotment, LayoutPriority } from 'allotment';
import { Button, message, TableProps, Tooltip } from 'antd';
import { history, useParams } from 'umi';

import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { Detail, Icon } from '@/components/ui';
import { DataSubjectConfig } from '@/constants/deleteUrl';
import { useGlobalHook, useRightsHook } from '@/hooks';
import useDeleteConfirm from '@/hooks/useDeleteConfirm';
import dataMartService from '@/modules/AppLayer/services/data-subject';
import Empty from '@/modules/DataStandard/components/Empty';

import { useDsAction, useDsCurrentNode } from '../stores/data-subject';
import { getDetailFields } from '../utils/detail';
import { addNode, deleteNode, transformTreeData, updateNode } from '../utils/tree';

import { DataSubjectFormModal, DataSubjectTree } from './components';

const { queryDataSubjectTree, queryDataSubject, queryDataSubjectTreeChild } = dataMartService;
const fields = getDetailFields('Subject');

export const MartSubject = () => {
  const { id } = useParams();
  const { hasRights } = useRightsHook();
  const [modalTitle, setModalTitle] = useState('');
  const [treeData, setTreeData] = useState<DSTreeNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<DSTreeNode | null>();
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { pagination, setPagination, handleTableChange } = useCustomTableHook({
    pageSize: 4,
  });
  const queryClient = useQueryClient();
  const currentNode = useDsCurrentNode();
  const { data: treeResult } = useQuery(['getSubjectTree'], queryDataSubjectTree);
  const { data: treeChildResult } = useQuery(
    ['getSubjectTreeChild', selectedNode?.id],
    () => queryDataSubjectTreeChild(selectedNode?.id as string),
    {
      enabled: !!selectedNode?.id,
      onSuccess: data => {
        setPagination({ ...pagination, total: data?.length });
      },
    },
  );

  const { data: detailResult } = useQuery(
    ['getSubjectDetail', selectedNode?.id],
    () => queryDataSubject(id ?? (selectedNode?.id as string)),
    {
      enabled: !!id ?? !!selectedNode?.id,
      onSuccess(data) {
        setSelectedNode(data?.data as DSTreeNode);
      },
    },
  );

  const { toggleModal, setActionType, setCurrentNode } = useDsAction();
  const deleteConfirm = useDeleteConfirm(DataSubjectConfig);

  useEffect(() => {
    setPageInfo({
      title: '主题域',
      description:
        '将数据集市按照分析视角进行切分，比如在电商行业。通常分为会员、交易、商品等。运维领域通常分为应用，基础设施，网络等。',
    });
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    // console.log('treeResult>>>change', treeResult);
    if (treeResult?.code == '0000') {
      init();
    }
  }, [treeResult]);

  const init = () => {
    try {
      setTreeData(
        transformTreeData(treeResult.data, (d: DSTreeNode) => ({
          ...d,
          icon: <i className='iconfont icon-folder-line' />,
        })) as DSTreeNode[],
      );
    } catch (e) {
      console.error(e);
    }
  };
  const onEdit = (data: DSTreeNode) => {
    setActionType('edit');
    setModalTitle('编辑主题域');
    toggleModal();
    setCurrentNode(data);
  };

  const handleClick = () => {
    toggleModal();
    setModalTitle('新增一级主题域');
    setActionType('create');
    setCurrentNode(null);
  };

  const getTreeRoot = (type: 'add' | 'update' | 'delete', node: DSTreeNode) => {
    let result;

    if (type === 'add') {
      result = addNode(node, treeData, String(node.parentId));
    } else if (type === 'update') {
      result = updateNode(node, treeData, String(node.parentId));
    } else {
      result = deleteNode(node, treeData);
    }
    queryClient.invalidateQueries(['getSubjectTreeChild', selectedNode?.id]);

    setTreeData(
      transformTreeData(result, (d: DSTreeNode) => ({
        ...d,
        icon: <i className='iconfont icon-folder-line' />,
      })) as DSTreeNode[],
    );
    // setSelectedNode(getNodeById(selectedNode?.id, result))
  };

  const handleTreeSelect = async (node: DSTreeNode) => {
    setCurrentNode(node);
    setSelectedNode(node);
    history.push(`/data-modeling/warehouse-plan/app-layer/mart-subject/${node.id}`);
    queryClient.invalidateQueries(['getSubjectTreeChild', selectedNode?.id]);
  };
  const getMenuType = (type: string) => {
    switch (type) {
    case 'cteateChildDataMart':
      setModalTitle('创建子主题域');
      return 'createChild';
    case 'editDataMart':
      setModalTitle('编辑主题域');
      return 'edit';
    case 'deleteDataMart':
      return 'delete';
    default:
      return null;
    }
  };
  const handleMenuSelect = async (type: string, record: DSTreeNode) => {
    const actionType = getMenuType(type);
    setActionType(actionType);
    setCurrentNode(record);
    if (actionType === 'delete') {
      const config = DataSubjectConfig;

      const res = await deleteConfirm(record?.id, `${config.title}「${record?.name}」吗?`, config);

      if (res.code === '0000') {
        message.success('删除成功');
        getTreeRoot('delete', res?.data);
        if (selectedNode?.id === record.id) {
          setSelectedNode(null);
          history.push('/data-modeling/warehouse-plan/app-layer/mart-subject');
        } else {
          queryClient.invalidateQueries(['getSubjectDetail']);
        }
      }
    } else {
      toggleModal();
    }
  };
  const handleCrateChild = () => {
    toggleModal();
    setModalTitle('新增子主题域');
    setActionType('createChild');
    setCurrentNode(detailResult?.data ?? {});
  };
  const renderCreateButton = (text: string) => {
    return (
      <div className='px-4 pt-0 pb-3'>
        <Button block={false} type='primary' disabled={!hasRights('data_subject:write')} onClick={handleCrateChild}>
          {text}
        </Button>
      </div>
    );
  };
  const handleTableEdit = async (record: any) => {
    await Promise.resolve().then(() => {
      setCurrentNode(record);
    });
    setActionType('edit');
    setModalTitle('编辑主题域');
    toggleModal();
  };
  const hadleTableDelete = async (record: any) => {
    const config = DataSubjectConfig;
    const { data, code } = await deleteConfirm(record?.id, `${config.title}「${record?.name}」吗?`, config);
    if (code === '0000') {
      message.success('删除成功');
      getTreeRoot('delete', data);
    }
  };
  const dataMartChildCol: TableProps<any>['columns'] = [
    {
      key: 'name',
      dataIndex: 'name',
      title: '主题域名称',
    },
    {
      key: 'code',
      dataIndex: 'code',
      title: '英文缩写',
    },
    {
      key: 'createTime',
      dataIndex: 'createTime',
      title: '创建时间',
    },
    {
      key: 'description',
      align: 'center',

      dataIndex: 'description',
      title: '备注',
    },
    {
      key: 'operate',
      dataIndex: 'operate',
      title: '操作',
      width: '120px',

      render: (value, record, index) => {
        return (
          <div>
            <Button
              type='link'
              size='small'
              className='mr-4 p-0'
              disabled={!hasRights('data_subject:write', record.projectAuth)}
              onClick={() => handleTableEdit(record)}
            >
              编辑
            </Button>
            <Button
              type='link'
              size='small'
              className='p-0'
              disabled={!hasRights('data_subject:write', record.projectAuth)}
              onClick={() => hadleTableDelete(record)}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];
  return (
    <>
      <div className='flex overflow-hidden  h-full rounded-[2px] bg-white-4'>
        <Allotment proportionalLayout={false}>
          <Allotment.Pane preferredSize='320px' minSize={120}>
            <div className='w-full h-full bg-neutral-50 flex flex-col'>
              <div className='h-[48px] p-[8px] flex justify-between items-center'>
                <div className='text-gray-6 pl-2'>主题域</div>
                <Tooltip placement='bottom' title='创建一级主题域'>
                  {hasRights('data_subject:write') && (
                    <Button
                      type='ghost'
                      disabled={!hasRights('data_subject:write')}
                      icon={<Icon name='add_box-line leading-6' size={24} className='text-gray-12' />}
                      onClick={handleClick}
                    ></Button>
                  )}
                </Tooltip>
              </div>
              <DataSubjectTree
                treeData={treeData}
                onTreeSelect={handleTreeSelect}
                onMenuSelect={handleMenuSelect}
                selectedNode={selectedNode}
              />
            </div>
          </Allotment.Pane>
          <Allotment.Pane priority={LayoutPriority.High}>
            {!selectedNode ? (
              <div className='flex w-full h-full'>
                <Empty />
              </div>
            ) : (
              <div className='flex-grow h-full bg-white overflow-scroll '>
                <Detail
                  fields={fields}
                  disabled={!hasRights('data_subject:write', detailResult?.data?.projectAuth)}
                  className='py-0'
                  detail={detailResult?.data ?? null}
                  onEdit={() => onEdit(detailResult?.data)}
                  loading={!detailResult?.data}
                ></Detail>
                {renderCreateButton('创建子主题域')}
                <CustomTable
                  dataSource={treeChildResult?.data ?? []}
                  columns={dataMartChildCol}
                  pagination={pagination}
                  onChange={handleTableChange}
                  scroll={{ x: '100%', y: 'auto' }}
                ></CustomTable>
              </div>
            )}
          </Allotment.Pane>
        </Allotment>

        <DataSubjectFormModal onSubmit={getTreeRoot} title={modalTitle} />
      </div>
    </>
  );
};
