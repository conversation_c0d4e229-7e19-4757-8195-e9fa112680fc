import { useEffect, useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Form, Input, message, Modal, Radio } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useForm } from 'antd/lib/form/Form';

import { BusinessCategorySelect } from '@/components';

import dataMartService from '../../services/data-mart';
import { useActionType, useDmAction, useDmCurrentNode, useDmModal } from '../../stores/data-mart';

interface Props {
  title: string;
  onSubmit: (type: 'add' | 'update' | 'delete', node: DMTreeNode & DataMart) => void;
}
const DataMartMap = {
  COM: { value: 'COM', label: '公共集市' },
  BIZ: { value: 'BIZ', label: '业务集市' },
  APP: { value: 'APP', label: '数据应用集市' },
};
export const DataMartFormModal = ({ title, onSubmit }: Props) => {
  // const [value, setValue] = useState<DataMartFormData>()
  const [radioOptions, setRadioOptions] = useState([
    { value: 'COM', label: '公共集市' },
    { value: 'BIZ', label: '业务集市' },
    { value: 'APP', label: '数据应用集市' },
  ]);
  const showModal = useDmModal();
  const actionType = useActionType();
  const { toggleModal, setActionType, setCurrentNode } = useDmAction();
  const currentNode = useDmCurrentNode() as DataMart;
  const { createDataMart, updateDataMart } = dataMartService;
  const queryClient = useQueryClient();
  const postMutation = useMutation((data: Partial<DataMartFormData>) => createDataMart(data), {
    onSuccess: () => {
      queryClient.invalidateQueries(['getMartTree']);
    },
  });

  const putMutation = useMutation((data: Partial<DataMartFormData>) => updateDataMart(currentNode.id, data), {
    onSuccess: () => {
      queryClient.invalidateQueries(['getMartTree']);
      queryClient.invalidateQueries(['getMartDetail']);
    },
  });

  const [form] = useForm();

  useEffect(() => {
    if (actionType === 'edit') {
      const options = (currentNode?.martType && DataMartMap[currentNode?.martType]) ?? {
        value: 'None',
        label: '无',
      };
      setRadioOptions([options]);
      form.setFieldsValue(currentNode);
    }
    if (actionType === 'createChild') {
      const options = (currentNode?.martType && DataMartMap[currentNode?.martType]) ?? {
        value: 'None',
        label: '无',
      };
      setRadioOptions([options]);
      form.setFieldsValue({
        name: '',
        code: '',
        martType: currentNode?.martType,
        bizId: currentNode?.bizId,
        description: '',
      });
    }
    if (actionType === 'create') {
      setRadioOptions([
        { value: 'COM', label: '公共集市' },
        { value: 'BIZ', label: '业务集市' },
        { value: 'APP', label: '数据应用集市' },
      ]);
      form.resetFields();
    }
  }, [currentNode, actionType]);

  const onCancel = () => {
    toggleModal();
  };

  const onOk = async () => {
    const values = await form.validateFields();

    if (actionType === 'create' || actionType === 'createChild') {
      const { data, code } = await postMutation.mutateAsync({
        ...values,
        martType: actionType === 'createChild' ? currentNode?.martType : values?.martType,
        bizId: actionType === 'createChild' ? currentNode?.bizId : values?.bizId,
        parentId: currentNode?.id ?? null,
      });
      if (code === '0000') {
        message.success('创建成功');
        onSubmit('add', data);
      }
    }
    if (actionType === 'edit') {
      const { data, code } = await putMutation.mutateAsync({
        ...values,

        parentId: currentNode?.parentId ?? null,
      });
      if (currentNode?.id === data.id) {
        setCurrentNode(data);
      }
      if (code === '0000') {
        message.success('更新成功');
        onSubmit('update', data);
      }
    }

    toggleModal();
    setActionType(null);
  };

  return (
    <Modal title={title} onCancel={onCancel} open={showModal} onOk={onOk} width='760px' okText='确定' cancelText='取消'>
      <Form
        form={form}
        labelAlign='right'
        labelCol={{ style: { width: '152px' } }}
        wrapperCol={{ style: { width: '378px' } }}
      >
        <Form.Item
          name='code'
          label='英文缩写'
          rules={[
            { required: true, message: '请输入英文缩写' },
            {
              pattern: /^[a-z][a-z_0-9]*$/,
              message: '小写字母开头，支持小写字母、数字和下划线的组合',
            },
          ]}
        >
          <Input disabled={actionType === 'edit'} placeholder={'请输入'} />
        </Form.Item>
        <Form.Item
          name='name'
          label='集市名称'
          rules={[
            { required: true, message: '请输入集市名称' },
            {
              // eslint-disable-next-line no-useless-escape
              pattern: /^[\u4e00-\u9fa5a-zA-Z0-9][(（\u4e00-\u9fa5_a-zA-Z0-9)）&\\]*$/,
              message: '中文、字母或数字开头，支持下划线、&及括号的组合',
            },
          ]}
        >
          <Input placeholder={'请输入'} />
        </Form.Item>
        <Form.Item name='martType' label='集市类型' rules={[{ required: true }]}>
          <Radio.Group
            options={radioOptions}
            disabled={actionType === 'edit' || actionType === 'createChild'}
          ></Radio.Group>
        </Form.Item>
        {actionType !== 'createChild' && (
          <Form.Item
            name='bizId'
            label='所属业务分类'
            rules={[{ required: true, message: '请选择业务分类' }]}
            style={actionType === 'edit' ? { display: 'none' } : {}}
          >
            <BusinessCategorySelect value='' noTips={true} onChange={() => {}} />
          </Form.Item>
        )}
        <Form.Item name='description' label='描述' rules={[{ required: false }]}>
          <TextArea placeholder={'请输入'}></TextArea>
        </Form.Item>
      </Form>
    </Modal>
  );
};
