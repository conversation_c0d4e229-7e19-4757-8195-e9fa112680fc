import { useEffect, useMemo } from 'react';
import { Spin, Tree } from 'antd';
import { history } from 'umi';

import { basePathname } from '@/routes/data-indicator';

import useTreePanel from './useTreePanel';

const getNewExpandKeys = (tree: TreeItem[]): string[] => {
  let keys: string[] = [];
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      keys.push(node.locationId);
      keys = keys.concat(getNewExpandKeys(node.children));
    }
  }
  return keys;
};

interface Props {
  store: any;
}
const TreeEntity = ({ store }: Props) => {
  useEffect(() => {
    store?.fetchData();
  }, [store.name]);

  const { treeFilter } = useTreePanel(store, store.disabledKey);

  const onExpand = (newExpandedKeys: React.Key[]) => {
    store.setExpandedKeys(newExpandedKeys);
    store.setAutoExpandParent(false);
  };

  const gotoDetail = (item: TreeItem) => {
    history.push(`${basePathname}/${store.name}/detail/${item.id}`, {
      title: item.name,
    });
  };

  const onselect = (keys, { node, selectedNodes }) => {
    if (node.nodeType.endsWith('Leaf')) {
      gotoDetail(node);
    }
  };

  const filterTreeData = useMemo(() => {
    const { filter, treeData, leafNodeType } = store;
    return treeFilter({
      filter,
      treeData,
      leafNodeType,
    });
  }, [store.filter, store.treeData, store.leafNodeType]);

  useEffect(() => {
    if (!filterTreeData || !store.filter.searchKey) return;
    store.setAutoExpandParent(true);
    const newExpandedKeys = getNewExpandKeys(filterTreeData);
    store.setExpandedKeys(newExpandedKeys);
  }, [store.filter.searchKey, filterTreeData]);

  return store.loading ? (
    <div className='flex justify-center mt-16'>
      <Spin />
    </div>
  ) : (
    <Tree.DirectoryTree
      onExpand={onExpand}
      className='custom-directory-tree hidden-leaf-switcher'
      expandedKeys={store.expandedKeys}
      treeData={filterTreeData}
      fieldNames={store.fieldNames}
      onSelect={onselect}
      autoExpandParent={store.autoExpandParent}
      showIcon
    />
  );
};

export default TreeEntity;
