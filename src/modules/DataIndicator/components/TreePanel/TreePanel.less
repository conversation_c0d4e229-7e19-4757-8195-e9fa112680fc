.tree-panel {
  height: 100%;
  background-color: #fafafa;
  box-shadow: 1px 0 4px 0 rgb(0 0 0 / 20%);
  z-index: 100;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    margin: 12px 0;

    .tree-panel-name {
      font-size: 16px;
      color: rgb(0 0 0 / 65%);
      font-weight: 500;
    }

    .iconfont {
      font-size: 24px;
      cursor: pointer;
      color: #525252;
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .goto-list-btn {
    position: absolute;
    z-index: 100;
    right: 10px;
    top: 18px;
    font-size: 12px !important;
    height: 22px !important;
    line-height: 20px;
    border-radius: 11px !important;
    background: #e6f7ff !important;
    border-color: #91d5ff !important;
    letter-spacing: 0;
    padding: 0 12px !important;

    a {
      letter-spacing: 0;
    }

    .iconfont {
      font-size: 5px;
      margin-left: 2px;
    }
  }

  &-filter {
    padding: 0 12px;
  }

  &-tree {
    position: relative;

    .ant-tree {
      background-color: transparent;
    }
  }
}
