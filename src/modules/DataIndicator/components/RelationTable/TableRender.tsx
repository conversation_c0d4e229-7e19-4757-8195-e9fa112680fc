import { useEffect, useState } from 'react';
import { Link } from 'umi';

import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { TableApi } from '@/services';

const TableRender = ({ filter, onClose }: { filter: Record<string, any>; onClose: () => void }) => {
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const { pagination, setPagination, queryParams, handleTableChange } = useCustomTableHook({
    pageSize: 20,
    filter,
  });
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
    },
    {
      title: '表名/表中文名',
      key: 'name',
      render: ({ tbName, tbAlias }) => {
        return (
          <span>
            {tbName} / {tbAlias}
          </span>
        );
      },
    },
    {
      title: '字段名称/字段显示名',
      key: 'colName',
      render: ({ colName, colDisplay }) => {
        return (
          <span>
            {colName} / {colDisplay}
          </span>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 130,
      render(record) {
        return (
          <>
            <Link
              onClick={onClose}
              to={`/data-modeling/dimension-modeling/modeling/table/edit/${record.id}`}
              className='mr-4'
            >
              查看详情
            </Link>
          </>
        );
      },
    },
  ];

  const fetchData = () => {
    setLoading(true);
    TableApi.getRelationTable(queryParams)
      .then(({ data, total }) => {
        const { page, size } = queryParams;
        setList(
          data?.map((item, index) => {
            // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
            item.index = page * size + index + 1;
            return item;
          }),
        );
        if (!pagination.total) {
          setPagination({
            ...pagination,
            total,
          });
        }
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='h-full overflow-hidden pt-4'>
      <CustomTable
        dataSource={list}
        columns={columns}
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        rowKey='index'
        size='small'
        scroll={true}
        className='bordered'
      />
    </div>
  );
};

export default TableRender;
