import { useEffect, useState } from 'react';
import { Button, message, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { history } from 'umi';

import { KeepAliveToTab } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { useTimePeriodStore } from '@/modules/DataIndicator/store';
import { basePathname } from '@/routes/data-indicator';
import { TimePeriodApi } from '@/services';

import SearchForm from './SearchForm';
import { fieldsLabel } from './utils';

const Page = () => {
  const { hasRights } = useRightsHook();
  const treeStore = useTimePeriodStore();
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const {
    setFilter,
    selectedRowKeys,
    selectedRows,
    filter,
    sort,
    pagination,
    setPagination,
    queryParams,
    setSelectedRows,
    setSelectedRowKeys,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    pageSize: 20,
    sort: {
      updateTime: 'DESC',
    },
  });
  const deleteConfirm = useDeleteConfirm(treeStore.deleteConfig);

  const onDeleteItem = async (item: DATA_INDICATOR.TimePeriodEntity) => {
    const res = await deleteConfirm(item.id, `确认删除「${item.name}」`);
    if (res.code === '0000') {
      message.success('删除成功');
      fetchData();
      // 刷新左侧树
      treeStore.fetchData();
      clearSelectedRows();
    }
  };

  const clearSelectedRows = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const onEditItem = (id: string, name: string) => {
    history.push(`${basePathname}/timePeriod/edit/${id}`, {
      title: '编辑' + name,
    });
  };

  const goDetail = (id: string) => {
    history.push(`${basePathname}/timePeriod/detail/${id}`);
  };

  const columns: ColumnsType<DATA_INDICATOR.TimePeriodEntity> = [
    {
      title: fieldsLabel.code,
      dataIndex: 'code',
      key: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: `${fieldsLabel.nameEn}/${fieldsLabel.name}`,
      key: 'nameEn',
      render({ nameEn, name }) {
        return (
          <>
            {nameEn} / {name}
          </>
        );
      },
      ellipsis: true,
    },
    {
      title: fieldsLabel.unit,
      dataIndex: 'unitName',
      key: 'unitName',
      align: 'center',
      width: 120,
    },
    {
      title: fieldsLabel.updateUserName,
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: fieldsLabel.updateTime,
      dataIndex: 'updateTime',
      key: 'updateTime',
      ellipsis: true,
      width: 160,
      align: 'center',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 130,
      render(item: DATA_INDICATOR.TimePeriodEntity) {
        const { name, id } = item;
        return (
          <Space>
            <Button type='link' size='small' className='p-0' onClick={() => goDetail(id)}>
              查看
            </Button>
            <Button
              type='link'
              size='small'
              className='p-0'
              disabled={!hasRights('time_period:write', item?.projectAuth)}
              onClick={() => onEditItem(id, name)}
            >
              编辑
            </Button>
            <Button
              type='link'
              size='small'
              className='p-0'
              disabled={!hasRights('time_period:write', item?.projectAuth)}
              onClick={async () => onDeleteItem(item)}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  const fetchData = () => {
    setLoading(true);
    TimePeriodApi.getList(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='flex flex-col h-full overflow-auto'>
      <SearchForm
        setFilter={setFilter}
        ids={selectedRowKeys}
        selectedRows={selectedRows}
        condition={{ filter, sort }}
        onSubmit={fetchData}
        onDelete={clearSelectedRows}
      />
      <CustomTable
        dataSource={list}
        columns={columns}
        rowSelection={{ selectedRowKeys }}
        onRowSelectionChange={onRowSelectionChange}
        pagination={pagination}
        loading={loading}
        scroll={true}
        onChange={handleTableChange}
      ></CustomTable>
    </div>
  );
};

export function TimePeriodListPage() {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
}
