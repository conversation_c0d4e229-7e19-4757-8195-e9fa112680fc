import { But<PERSON>, Col, Form, Row } from 'antd';
import { history } from 'umi';

import { EnumSelect, SearchInput } from '@/components';
import BatchActions from '@/components/business/ui/BatchActions';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { basePathname } from '@/routes/data-indicator';

import { useTimePeriodStore } from '../../store';

import { fieldsLabel } from './utils';

interface Props {
  setFilter: (filter: Record<string, any>) => void;
  ids: string[];
  condition: Pick<QueryParams, 'filter' | 'sort'>;
  onSubmit: () => void;
  selectedRows: DATA_INDICATOR.TimePeriodEntity[];
  onDelete: () => void;
}
const SearchForm = ({ setFilter, ids, selectedRows, condition, onSubmit, onDelete }: Props) => {
  const [form] = Form.useForm();
  const { hasRights } = useRightsHook();

  const deleteConfirm = useDeleteConfirm({
    delUrl: '/time-period/bulk',
  });

  const fetchData = useTimePeriodStore(state => state.fetchData);

  const updateFilter = () => {
    const fieldsValue = form.getFieldsValue();
    const filter: Record<string, any> = {};
    Object.entries(fieldsValue).forEach(([key, value]: [string, any]) => {
      if (value) {
        filter[key] = value;
      }
    });
    setFilter(filter);
  };

  const handleDelete = async () => {
    await deleteConfirm(selectedRows ?? [], `确认删除勾选对象(共计${selectedRows?.length})个吗？`);
    fetchData();
    onSubmit();
    onDelete?.();
  };

  return (
    <Form className='pt-2 pr-4 pb-2 pl-4' form={form}>
      <Row gutter={[8, 8]}>
        <Col>
          <Button
            type='primary'
            loading={false}
            disabled={!hasRights('time_period:write')}
            onClick={() => {
              history.push(`${basePathname}/timePeriod/create`);
            }}
          >
            创建
          </Button>
        </Col>
        <Col>
          <BatchActions
            type='unit'
            ids={ids}
            disabled={!hasRights('time_period:write')}
            condition={condition}
            onDeleteItems={handleDelete}
          />
        </Col>
        <Col>
          <Form.Item className='mb-0' name='searchKeyword'>
            <SearchInput placeholder='请输入搜索关键字' allowClear onSearch={() => updateFilter()} />
          </Form.Item>
        </Col>
        <Col style={{ marginLeft: '28px' }}>
          <Form.Item label={fieldsLabel.unit} className='mb-0' name='unit'>
            <EnumSelect code='unit' onChange={() => updateFilter()} style={{ width: '200px' }} />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default SearchForm;
