import { useEffect, useState } from 'react';
import { Button, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { history } from 'umi';

import { KeepAliveToTab } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { useDerivativeIndicatorStore } from '@/modules/DataIndicator/store';
import { basePathname } from '@/routes/data-indicator';
import { DerivativeIndicatorApi } from '@/services';

import SearchForm from './SearchForm';
import { fieldsLabel } from './utils';

const Page = () => {
  const treeStore = useDerivativeIndicatorStore();
  const { hasRights } = useRightsHook();
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const {
    setFilter,
    selectedRowKeys,
    selectedRows,
    filter,
    sort,
    pagination,
    setPagination,
    queryParams,
    setSelectedRowKeys,
    setSelectedRows,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    pageSize: 20,
    sort: {
      updateTime: 'DESC',
    },
  });
  const deleteConfirm = useDeleteConfirm(treeStore.deleteConfig);

  const onDeleteItem = async (item: DATA_INDICATOR.TimePeriodEntity) => {
    await deleteConfirm(item.id, `确认删除「${item.name}」`);
    fetchData();
    // 刷新左侧树
    treeStore.fetchData();
    clearSelectedRows();
  };

  const clearSelectedRows = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const onEditItem = (id: string, name: string) => {
    history.push(`${basePathname}/derivativeIndicator/edit/${id}`, {
      title: '编辑' + name,
    });
  };

  const onDetailItem = (id: string) => {
    history.push(`${basePathname}/derivativeIndicator/detail/${id}`);
  };

  const onCloneItem = (id: string) => {
    history.push(`${basePathname}/derivativeIndicator/clone/${id}`);
  };

  const columns: ColumnsType<DATA_INDICATOR.DerivativeIndicatorEntity> = [
    {
      title: fieldsLabel.code,
      dataIndex: 'code',
      key: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: `${fieldsLabel.nameEn}/${fieldsLabel.name}`,
      key: 'nameEn',
      render({ nameEn, name }) {
        return (
          <>
            {nameEn ?? '-'} / {name}
          </>
        );
      },
      ellipsis: true,
      width: 300,
    },
    {
      title: fieldsLabel.procId,
      dataIndex: 'procName',
      key: 'procName',
      ellipsis: true,
      width: 200,
    },
    {
      title: fieldsLabel.domId,
      dataIndex: 'domName',
      key: 'domName',
      ellipsis: true,
      width: 200,
    },
    {
      title: fieldsLabel.martAndSubj,
      key: 'martName',
      ellipsis: true,
      width: 200,
      render({ subjName, martName }) {
        return `${martName || ''}/${subjName || ''}`;
      },
    },
    {
      title: fieldsLabel.updateUserName,
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      align: 'center',
      ellipsis: true,
      width: 200,
    },
    {
      title: fieldsLabel.updateTime,
      dataIndex: 'updateTime',
      key: 'updateTime',
      ellipsis: true,
      width: 160,
      align: 'center',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 180,
      render(item) {
        return (
          <Space>
            <Button type='link' size='small' className='p-0' onClick={() => onDetailItem(item.id)}>
              查看
            </Button>
            <Button
              type='link'
              size='small'
              className='p-0'
              disabled={!hasRights('derivative_indicator:write', item?.projectAuth)}
              onClick={() => onEditItem(item.id, item.name)}
            >
              编辑
            </Button>
            <Button
              type='link'
              size='small'
              className='p-0'
              disabled={!hasRights('derivative_indicator:write')}
              onClick={() => onCloneItem(item.id)}
            >
              克隆
            </Button>
            <Button
              type='link'
              size='small'
              className='p-0'
              disabled={!hasRights('derivative_indicator:write', item?.projectAuth)}
              onClick={async () => onDeleteItem(item)}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  const fetchData = () => {
    setLoading(true);
    DerivativeIndicatorApi.getList(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='flex flex-col h-full'>
      <SearchForm
        setFilter={setFilter}
        ids={selectedRowKeys}
        selectedRows={selectedRows}
        condition={{ filter, sort }}
        onSubmit={fetchData}
        onDelete={clearSelectedRows}
      />
      <CustomTable
        dataSource={list}
        columns={columns}
        rowSelection={{ selectedRowKeys }}
        onRowSelectionChange={onRowSelectionChange}
        pagination={pagination}
        loading={loading}
        scroll={{ x: 1000 }}
        onChange={handleTableChange}
      ></CustomTable>
    </div>
  );
};

export function DerivativeListPage() {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
}
