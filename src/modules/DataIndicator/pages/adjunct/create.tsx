import React, { useEffect, useState } from 'react';
import { Button, Divider, Form, Input } from 'antd';
import { useParams } from 'umi';

import {
  BusinessCategorySelect,
  DataDomainSelect,
  KeepAliveToTab,
  MartAndSubjectSelect,
  useKeepAliveTabs,
  WarehouseLayerSelect,
} from '@/components';
import RelationTable from '@/modules/DataIndicator/components/RelationTable';
import { useAdjunctStore } from '@/modules/DataIndicator/store';
import { basePathname } from '@/routes/data-indicator';
import { AdjunctApi, WarehouseLayerApi } from '@/services';

import { fieldsLabel } from './utils';

const rules = {
  martOrSubj: [{ required: true, message: '请选择' + fieldsLabel.martAndSubj }],
  layerId: [{ required: true, message: '请选择' + fieldsLabel.layerId }],
  bizId: [{ required: true, message: '请选择' + fieldsLabel.bizId }],
  domId: [{ required: true, message: '请选择' + fieldsLabel.domId }],
  martId: [{ required: true, message: '请选择' + fieldsLabel.martId }],
  code: [
    { required: true, message: '请输入' + fieldsLabel.code },
    {
      pattern: /^[a-z][0-9a-zA-Z_]{1,}$/,
      message: '小写字母开头，支持小写字母、数字和下划线组合',
    },
  ],
  nameEn: [
    {
      pattern: /^[a-z0-9][a-zA-Z0-9_]{1,}$/,
      message: '英文字母或数字开头，支持英文字母、数字和下划线的组合',
    },
  ],
  name: [{ required: true, message: '请输入' + fieldsLabel.name }],
  bizExpr: [{ required: true, message: '请输入' + fieldsLabel.bizExpr }],
};

const formLayout = {
  labelCol: { flex: '110px' },
  wrapperCol: { span: 18 },
};

const Page: React.FC = () => {
  const store = useAdjunctStore();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { closeCurrentTab, openTab, setPageInfo } = useKeepAliveTabs();
  const [bizId, setBizId] = useState<string | undefined>(undefined);
  const [layer, setLayer] = useState({});
  const [openRelationTable, setOpenRelationTable] = useState(false);
  const [dirty, setDirty] = useState(false);

  // 保存表单
  const saveForm = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      let result: { data: { id: any } } | null = null;
      await AdjunctApi[id ? 'update' : 'create']({
        ...values,
        id,
      })
        .then(data => {
          result = data;
          store.fetchData();
        })
        .finally(() => {
          setLoading(false);
        });

      closeCurrentTab(() => {
        openTab(`${basePathname}/adjunct/detail/${result?.data.id}`, {
          refresh: true,
        });
      });
    } catch (e) {
      console.log(e);
    }
  };

  const onChangeBusinessCategory = (bizId: string) => {
    setBizId(bizId);
    form.setFieldValue('domId', undefined);
  };

  const onChangeLayer = (layerId: string, layer: WAREHOUSE_LAYER.WarehouseLayerEntity | {}) => {
    setLayer(layer);
  };

  // 集市和主题二选一
  const onChangeMartAndSubj = (value: string, obj: { martId?: string; subjId?: string }) => {
    Object.entries(obj).forEach(([key, value]) => {
      form.setFieldValue(key, value || null);
    });
  };

  useEffect(() => {
    setPageInfo({ isDirty: dirty });
  }, [dirty]);

  useEffect(() => {
    if (id) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      AdjunctApi.getDetail(id).then(({ data }) => {
        setBizId(data.bizId);
        form.setFieldsValue({
          ...data,
          martOrSubj: data.subjId ?? data.martId,
        });

        WarehouseLayerApi.getDetail(data.layerId)
          .then(({ data }) => {
            setLayer(data);
          })
          .catch(() => {});
      });
    }
  }, [id]);

  return (
    <div className='page-create'>
      {id && (
        <RelationTable open={openRelationTable} onClose={() => setOpenRelationTable(false)} params={{ adjId: id }} />
      )}
      <div className='flex justify-between pl-4 pr-4 pt-2 pb-2'>
        <div className='flex-1'>
          <Button type='primary' className='mr-2' onClick={saveForm} loading={loading} disabled={!dirty}>
            保存
          </Button>
        </div>
        {id && (
          <Button type='text' onClick={() => setOpenRelationTable(true)}>
            <i className='iconfont icon-link-line text-sm mr-2'></i>关联表
          </Button>
        )}
      </div>
      <Divider style={{ margin: 0 }} />
      <div className='pl-4 pr-4 flex-1'>
        <div className='custom-header mb-2 mt-2'>基本信息</div>
        <Form {...formLayout} form={form} onValuesChange={() => setDirty(true)}>
          <Form.Item label={fieldsLabel.layerId} rules={rules.layerId} name='layerId' wrapperCol={{ span: 12 }}>
            <WarehouseLayerSelect onChange={onChangeLayer} />
          </Form.Item>
          {layer.catalog === 'WH' && (
            <>
              <Form.Item label={fieldsLabel.bizId} name='bizId' wrapperCol={{ span: 12 }} rules={rules.bizId}>
                <BusinessCategorySelect value={bizId} onChange={bizId => onChangeBusinessCategory(bizId)} />
              </Form.Item>
              {bizId && (
                <Form.Item label={fieldsLabel.domId} rules={rules.domId} name='domId' wrapperCol={{ span: 12 }}>
                  <DataDomainSelect bizId={bizId} />
                </Form.Item>
              )}
            </>
          )}
          {layer.catalog === 'APP' && (
            <>
              <Form.Item label={fieldsLabel.martId} hidden name='martId' wrapperCol={{ span: 12 }}>
                <Input />
              </Form.Item>
              <Form.Item label={fieldsLabel.subjId} hidden name='subjId' wrapperCol={{ span: 12 }}>
                <Input />
              </Form.Item>
              <Form.Item
                label={fieldsLabel.martAndSubj}
                name='martOrSubj'
                rules={rules.martOrSubj}
                wrapperCol={{ span: 12 }}
              >
                <MartAndSubjectSelect onChange={onChangeMartAndSubj} />
              </Form.Item>
            </>
          )}
          <Form.Item label={fieldsLabel.code} rules={rules.code} name='code' wrapperCol={{ span: 12 }}>
            <Input disabled={!!id} placeholder='小写字母开头，支持小写字母、数字和下划线组合' />
          </Form.Item>
          <Form.Item label={fieldsLabel.nameEn} rules={rules.nameEn} name='nameEn' wrapperCol={{ span: 12 }}>
            <Input placeholder='英文字母或数字开头，支持英文字母、数字和下划线的组合' />
          </Form.Item>
          <Form.Item label={fieldsLabel.name} rules={rules.name} name='name' wrapperCol={{ span: 12 }}>
            <Input placeholder='请输入' />
          </Form.Item>
          <Form.Item label={fieldsLabel.bizExpr} rules={rules.bizExpr} name='bizExpr'>
            <Input.TextArea rows={3} placeholder='请输入' />
          </Form.Item>
          <Form.Item label={fieldsLabel.description} name='description'>
            <Input.TextArea rows={3} placeholder='请输入' />
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export function AdjunctCreatePage() {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
}
