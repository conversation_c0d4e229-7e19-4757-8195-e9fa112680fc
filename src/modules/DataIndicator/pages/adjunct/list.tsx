import { useEffect, useState } from 'react';
import { Button, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { history } from 'umi';

import { KeepAliveToTab } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { useAdjunctStore } from '@/modules/DataIndicator/store';
import { basePathname } from '@/routes/data-indicator';
import { AdjunctApi } from '@/services';

import SearchForm from './SearchForm';
import { fieldsLabel } from './utils';

const Page = () => {
  const { hasRights } = useRightsHook();
  const treeStore = useAdjunctStore();
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const {
    setFilter,
    selectedRowKeys,
    selectedRows,
    filter,
    sort,
    pagination,
    setPagination,
    queryParams,
    setSelectedRows,
    setSelectedRowKeys,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    pageSize: 20,
    sort: {
      updateTime: 'DESC',
    },
  });
  const deleteConfirm = useDeleteConfirm(treeStore.deleteConfig);

  const onDeleteItem = async (item: DATA_INDICATOR.AdjunctEntity) => {
    await deleteConfirm(item.id, `确认删除「${item.name}」`);
    fetchData();
    // 刷新左侧树
    treeStore.fetchData();
    clearSelectedRows();
  };

  const clearSelectedRows = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const onEditItem = (id: string, name: string) => {
    history.push(`${basePathname}/adjunct/edit/${id}`, {
      title: '编辑' + name,
    });
  };

  const goDetail = (id: string) => {
    history.push(`${basePathname}/adjunct/detail/${id}`);
  };

  const columns: ColumnsType<DATA_INDICATOR.AdjunctEntity> = [
    {
      title: fieldsLabel.code,
      dataIndex: 'code',
      key: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: `${fieldsLabel.nameEn}/${fieldsLabel.name}`,
      key: 'nameEn',
      render({ nameEn, name }) {
        return (
          <>
            {nameEn} / {name}
          </>
        );
      },
      ellipsis: true,
    },
    {
      title: fieldsLabel.domId,
      dataIndex: 'domName',
      key: 'domName',
      align: 'center',
      ellipsis: true,
      render(domName) {
        return domName || '-';
      },
    },
    {
      title: fieldsLabel.martAndSubj,
      key: 'subjId',
      align: 'center',
      ellipsis: true,
      render({ martName, subjName }) {
        return martName || subjName || '-';
      },
    },
    {
      title: fieldsLabel.updateUserName,
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: fieldsLabel.updateTime,
      dataIndex: 'updateTime',
      key: 'updateTime',
      ellipsis: true,
      width: 160,
      align: 'center',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 130,
      render(item) {
        const { name, id } = item;
        return (
          <Space>
            <Button className='p-0' type='link' size='small' onClick={() => goDetail(id)}>
              查看
            </Button>
            <Button
              className='p-0'
              type='link'
              size='small'
              disabled={!hasRights('data_adjunct:write', item?.projectAuth)}
              onClick={() => onEditItem(id, name)}
            >
              编辑
            </Button>
            <Button
              className='p-0'
              type='link'
              size='small'
              disabled={!hasRights('data_adjunct:write', item?.projectAuth)}
              onClick={async () => onDeleteItem(item)}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  const fetchData = () => {
    setLoading(true);
    AdjunctApi.getList(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='flex flex-col h-full'>
      <SearchForm
        setFilter={setFilter}
        ids={selectedRowKeys}
        selectedRows={selectedRows}
        condition={{ filter, sort }}
        onSubmit={fetchData}
        onDelete={clearSelectedRows}
      />
      <CustomTable
        dataSource={list}
        columns={columns}
        rowSelection={{ selectedRowKeys }}
        onRowSelectionChange={onRowSelectionChange}
        pagination={pagination}
        loading={loading}
        scroll={true}
        onChange={handleTableChange}
      ></CustomTable>
    </div>
  );
};

export function AdjunctListPage() {
  return (
    <KeepAliveToTab>
      <Page />
    </KeepAliveToTab>
  );
}
