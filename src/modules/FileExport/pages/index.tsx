import { useState } from 'react';
import { Button, Result } from 'antd';
import { useLocation, useParams } from 'umi';

import { ExcelImportApi } from '@/services';

import './index.less';

interface State {
  desc: string;
  type: 'unit' | 'derivative' | 'atomic' | 'adjunct' | 'table';
}

export const FileExport = () => {
  const { id } = useParams();

  const { desc, type } = (useLocation().state ?? {}) as State;
  const [stateResult, setStateResult] = useState<{
    status?: string;
    title: string;
    icon?: any;
  }>({
    icon: null,
    title: `${desc} ${id}.xlsx 文件已生成`,
  });
  const onExport = async () => {
    await ExcelImportApi.export({
      type,
      ids: [id],
    });
    setStateResult({
      status: 'success', // 	success | error | info | warning
      title: `${desc} ${id}.xlsx 文件已生成`,
    });
  };
  return (
    <div className='bg-white-4 flex flex-col flex-1 component-1676962528036'>
      <div className='flex items-center mb-2 bg-white'>
        <div className='px-4 py-3 text-dark-85 text-title-3'>{desc}导出状态详情</div>
      </div>
      <div className='bg-white flex-1 flex flex-col items-center pt-36'>
        <Result
          {...stateResult}
          className={stateResult.icon === null ? 'iconContainer' : ''}
          extra={[
            <Button type='primary' key='console' onClick={onExport}>
              下载文件
            </Button>,
          ]}
        />
      </div>
    </div>
  );
};
