import { create } from 'zustand';

interface State {
  statusList: EnumCodeItemModel[];
  actionList: EnumCodeItemModel[];
  actions: {
    setStatusList: (data: EnumCodeItemModel[]) => void;
    setActionList: (data: EnumCodeItemModel[]) => void;
  };
}

const useStore = create<State>((set, get) => ({
  statusList: [],
  actionList: [],
  actions: {
    setStatusList(list) {
      set({
        statusList: list,
      });
    },
    setActionList(list) {
      set({
        actionList: list,
      });
    },
  },
}));

export const useActionList = () => useStore(state => state.actionList);
export const useStatusList = () => useStore(state => state.statusList);
export const useActions = () => useStore(state => state.actions);
