import React, { useState } from 'react';
import { message, Modal } from 'antd';
import { history } from 'umi';

import { basePathname } from '@/routes/cluster-opts-management';

import { CLUSTER_OPTS_TYPES } from '../constant';

interface Props {
  visible: boolean;
  onClose: (visible: boolean) => void;
}
export const ChooseOpsType: React.FC<Props> = ({ visible, onClose }) => {
  const [optsType, setOptsType] = useState<string>();

  const onOk = () => {
    if (!optsType) {
      message.warning('请选择框架类型');
      return;
    }
    history.push(`${basePathname}/create?optsType=${optsType}`);
  };

  return (
    <Modal
      open={visible}
      title='选择框架类型'
      closable={true}
      onCancel={() => onClose(false)}
      width={562}
      okText='下一步'
      onOk={() => onOk()}
    >
      <div className='flex items-center justify-center'>
        <ul className='select-icon-list mt-1'>
          {CLUSTER_OPTS_TYPES.map(item => (
            <li
              key={item.value}
              onClick={() => setOptsType(item.value)}
              className={item.value === optsType ? 'selected' : ''}
            >
              <div className='select-icon-list-icon'>
                <i className={`iconfont ${item.icon}`}></i>
              </div>
              <div className='select-icon-list-label'>{item.label}</div>
            </li>
          ))}
        </ul>
      </div>
    </Modal>
  );
};
