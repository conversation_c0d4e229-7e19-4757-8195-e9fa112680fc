import React from 'react';
import { Select, SelectProps } from 'antd';

import { CLUSTER_OPTS_TYPES } from '../constant';

interface Props extends SelectProps {}
export const ClusterOptsTypeSelect: React.FC<Props> = ({ ...otherProps }) => {
  return (
    <Select {...otherProps}>
      {CLUSTER_OPTS_TYPES?.map((option, index) => (
        <Select.Option key={index} value={option.value}>
          <span className='flex' style={{ alignItems: 'center' }}>
            <i className={`iconfont ${option.icon} text-primary-1`}></i>
            <span className='ml-2'>{option.label}</span>
          </span>
        </Select.Option>
      ))}
    </Select>
  );
};
