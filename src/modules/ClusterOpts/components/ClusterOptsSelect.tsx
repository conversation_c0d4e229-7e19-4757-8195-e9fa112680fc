import React, { useEffect, useState } from 'react';
import { Select, SelectProps } from 'antd';

import { ClusterOptsApi } from '../services/ClusterOptsApi';

interface Props extends SelectProps {
  optsType?: OptsType;
}
export const ClusterOptsSelect: React.FC<Props> = ({ optsType, ...otherProps }) => {
  const [options, setOptions] = useState([]);
  const fieldNames = {
    label: 'optsName',
    value: 'id',
  };

  useEffect(() => {
    ClusterOptsApi.getAll(optsType).then(({ data }) => {
      setOptions(data);
    });
  }, []);

  return <Select options={options} fieldNames={fieldNames} placeholder='请选择' {...otherProps}></Select>;
};
