import Request from '@/request';

const url = '/api/v2/ingestion/opts';

export const ClusterOptsApi = {
  /**
   * 集群框架框架列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 创建集群框架
   */
  async create(data: Omit<ClusterEntity, 'id'>, options?) {
    return await Request.post(url, { data, ...options });
  },
  /**
   * 更新集群框架
   * @param data
   * @returns
   */
  async update(data: ClusterEntity, options?) {
    return await Request.put(`${url}/${data.id}`, { data, ...options });
  },
  /**
   * 删除集群框架
   * @param id 集群框架id
   * @returns
   */
  async delete(id: string, options?) {
    return await Request.delete(`${url}/${id}`, { ...options });
  },
  /**
   * 获取集群框架详情
   * @param id 集群框架id
   * @returns
   */
  async getDetail(id: string, options?) {
    return await Request.get(`${url}/${id}`, { ...options });
  },

  getAllUrl(optsType?: OptsType) {
    if (optsType) {
      return `${url}/list/${optsType}`;
    }
    return `${url}/list`;
  },

  /**
   * 获取全部集群框架
   */
  async getAll(optsType?: OptsType, options?) {
    if (optsType) {
      return await Request.get(`${url}/list/${optsType}`, { ...options });
    }
    return await Request.get(`${url}/list`, { ...options });
  },
  /**
   * 获取集群框架列表及配置项
   */
  async getOptsListHasOptions(optsType: OptsType, scope: 'pipeline' | 'marayarn' = 'pipeline') {
    return await Request.get(`${url}/list/${optsType}/options`, {
      params: { scope },
    });
  },
  /**
   * 获取集群框架配置项 根据框架类型
   */
  async getOptsOptionsByType(optsType: OptsType) {
    return await Request.get(`${url}-type/${optsType}/options`);
  },
  /**
   * 获取集群框架配置项 根据框架id
   */
  async getOptsOptionsById(id: string) {
    return await Request.get(`${url}/${id}/options`);
  },
};
