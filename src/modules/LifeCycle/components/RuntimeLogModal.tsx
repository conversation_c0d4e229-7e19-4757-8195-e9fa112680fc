import { useEffect, useRef, useState } from 'react';
import { FullscreenExitOutlined, FullscreenOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Collapse, Modal, ModalProps, Spin, Tooltip } from 'antd';
import { debounce } from 'lodash';

import { LOG_STATUS_STYLE_MAP, TASK_EXECUTE_STATUS_MAP } from '../constants';
import { LifeCycleApi } from '../services';

interface Props extends ModalProps {
  taskId: string;
}
export const LogModal = (props: Props) => {
  const { taskId, ...otherProps } = props;
  const [logs, setLogs] = useState<any[]>([]);
  const [viewLogs, setViewLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [childTaskList, setChildTaskList] = useState([]);
  const [activeTabKeys, setActiveTabKeys] = useState([]);
  const hasExpand = useRef(false);

  const fetchLogs = async () => {
    setLoading(true);
    const { data } = await LifeCycleApi.getTaskLog(taskId).finally(() => setLoading(false));   
    setLogs(data.reduce((acc, cur) => {
      if (acc?.at(-1)?.taskId === cur.taskId) {
        acc.at(-1).data.push(cur.log);
      } else {
        if (!cur.taskId) {
          acc.push({
            taskId: Math.random().toFixed(6),
            taskName: '调度日志',
            executorAddress: cur.executorAddress,
            data: [cur.log],
            noStatus: true,
            id: cur.id,
            loading: false,
          });
        } else {
          acc.push({
            taskId: cur.taskId,
            taskName: cur.taskName,
            executorAddress: cur.executorAddress,
            data: [cur.log],
            id: cur.id,
            loading: false,
          });
        }
      }
      return acc;
    }, []));
  };

  const getStatusList = async () => {
    const { data } = await LifeCycleApi.getTaskDetail(taskId);
    setChildTaskList(data);
  };

  useEffect(() => {
    fetchLogs();
    getStatusList();
  }, [taskId]);

  useEffect(() => {
    if (!logs?.length && !childTaskList?.length) return;
    if (logs?.length) {
      setViewLogs([...logs]);
    }
    let hasExceptSuccess = false;
    const idList = [];
    const exceptSuccessIdList = [];
    const newLogs = logs.map(item => {
      const curTask = childTaskList.find(task => task.id == item.taskId);
      item.status = curTask?.status;
      if (item.status !== 'SUCCESS') {
        hasExceptSuccess = true;
        exceptSuccessIdList.push(item.id);
      }
      idList.push(item.id);
      return item;
    });

    setViewLogs(newLogs);
    if (!hasExpand.current) {
      if (!hasExceptSuccess) {
        setActiveTabKeys([...idList]);
      } else {
        setActiveTabKeys([...exceptSuccessIdList]);
      }
    }
  }, [childTaskList, logs]);


  const handleRefresh = async () => {
    fetchLogs();
    getStatusList();
    hasExpand.current = true;
  };

  return (
    <Modal
      title={
        <div className='flex items-center'>
          查看日志
          <Button
            size='small'
            type='link'
            onClick={() => setFullscreen(flag => !flag)}
            icon={
              fullscreen ? (
                <Tooltip title='退出全屏'>
                  <FullscreenExitOutlined />
                </Tooltip>
              ) : (
                <Tooltip title='全屏'>
                  <FullscreenOutlined />
                </Tooltip>
              )
            }
          />
          <Button
            size='small'
            type='link'
            onClick={() => handleRefresh()}
            icon={<Tooltip title='刷新'>
              <ReloadOutlined />
            </Tooltip>}
            className='ml-2'
          />
        </div>
      }
      width={1000}
      className={fullscreen ? 'full-screen' : ''}
      styles={{
        body: { overflowY: 'auto', flex: 1, paddingTop: '12px' },
        content: { display: 'flex', flexDirection: 'column', overflow: 'hidden', height: '100vh' },
      }}
      footer={null}
      {...otherProps}
    >
      <Spin spinning={loading}>
        <Collapse activeKey={activeTabKeys} onChange={setActiveTabKeys} items={
          viewLogs?.map(item => {
            const iconItem = TASK_EXECUTE_STATUS_MAP?.[item.status];

            return {
              key: item.id,
              label: <span>
                <i
                  className={`iconfont icon-${iconItem?.icon} mr-2`}
                  style={{ color: `var(${iconItem?.iconColor})` }}
                ></i>
                <span>{item.taskName}{ item.executorAddress ? `（${item.executorAddress}）` : '' }</span>  
              </span>,
              children: <Spin spinning={item.loading}><div className=''>
                {
                  item?.data?.map((log, index) =>
                    <pre key={index} className='whitespace-pre-wrap text-gray-6 font-mono'>{log}</pre>)
                }
              </div></Spin>,
            };
          })
        }
        >
        </Collapse>
      </Spin>
    </Modal>
  );
};
