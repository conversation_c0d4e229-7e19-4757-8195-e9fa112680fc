import { Col, Row } from 'antd';
import dayjs from 'dayjs';

import { ColumnChart } from '@/components/visual/chart/ColumnChart';
import { byteConvert } from '@/components/visual/helper';

import { CompareInfo } from './CompareInfo';
import style from './DataChart.less';

const chartOptions = {
  xField: 'x',
  yField: 'y',
  seriesField: '',
};

const getChartOptions = (symbol = '') => {
  return {
    ...chartOptions,
    yAxis: {
      label: {
        formatter: val => {
          return  val as string + symbol ;
        },
      },
    },
    xAxis: {
      label: {
        formatter: val => {
          return  dayjs(val).format('MM-DD');
        },
      },
    },
    tooltip: {
      showTitle: false,
      formatter: data => {
        const { y, x } = data;
        return {
          name: x,
          value: `${y}${symbol}`,
        };
      },
    },
  };
};

interface Props{
  hotChart: { chartData: any[], symbol: string, lastData: number };
  coldChart: { chartData: any[], symbol: string, lastData: number };
}

export const DataChart = ({ hotChart, coldChart }: Props) => {
  const { chartData: hotChartData, lastData: hotLastData } = hotChart;
  const { chartData: coldChartData, lastData: coldLastData } = coldChart;
  const hotDataNum = byteConvert(hotLastData);
  const coldDataNum = byteConvert(coldLastData);

  return <div className="chart-container mt-4 flex h-[300px] w-full">
    <div className={`h-full flex-1 ${style.cardItem}`}>
      <div className="flex flex-col h-full">
        <div className="num-item flex">
          <div>
            <div className="title text-[16px] text-gray-6">昨日热数据迁移</div>
            <div className="num text-[24px] text-success-2 mt-1">
              {hotDataNum?.bytes}
              <span className='text-[14px]'>{hotDataNum?.symbol}</span>
            </div>
          </div>
          <div className='ml-5'>
            <CompareInfo label='前日同比' value={[hotChartData?.at(-1)?.y, hotChartData?.at(-2)?.y]}></CompareInfo>
            <CompareInfo label='上周同比' value={[hotChartData?.at(-1)?.y, hotChartData?.at(-8)?.y]}></CompareInfo>
          </div>
        </div>
        <div className='chart flex-1 mt-2 min-h-0'>
          <ColumnChart
            data={hotChart?.chartData || []}
            options={getChartOptions(hotChart?.symbol)}
          />
        </div>
      </div>
    </div>
    <div className={`h-full flex-1 ${style.cardItem} ml-4`}>
      <div className="flex flex-col h-full">
        <div className="num-item flex">
          <div>
            <div className="title text-[16px] text-gray-6">昨日冷数据归档 (含删除)</div>
            <div className="num text-[24px] text-success-2 mt-1">
              {coldDataNum?.bytes}
              <span className='text-[14px]'>{coldDataNum?.symbol}</span>
            </div>
          </div>
          <div className='ml-5'>
            <CompareInfo label='前日同比' value={[coldChartData?.at(-1)?.y, coldChartData?.at(-2)?.y]}></CompareInfo>
            <CompareInfo label='上周同比' value={[coldChartData?.at(-1)?.y, coldChartData?.at(-8)?.y]}></CompareInfo>
          </div>
        </div>
        <div className='chart flex-1 mt-2 min-h-0'>
          <ColumnChart
            data={coldChart?.chartData || []}
            options={getChartOptions(coldChart?.symbol)}
          />
        </div>
      </div>
    </div>
  </div>;
};
