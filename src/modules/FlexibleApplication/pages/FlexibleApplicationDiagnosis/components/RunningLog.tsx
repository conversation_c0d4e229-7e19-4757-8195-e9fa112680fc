import { useEffect, useState } from 'react';

import { RunningLog as RunningConsoleLog } from '@/components';
import { useDiagnosisStore } from '@/modules/FlexibleApplication/store';
import { FlexibleApplicationApi } from '@/services';

export const RunningLog = () => {
  const { id, yarnApplicationId } = useDiagnosisStore();
  const [containers, setContainers] = useState<YarnSessionLogItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const fetchYarnApplicationLog = () => {
    setLoading(true);
    try {
      FlexibleApplicationApi.yarnApplicationLog(id ?? '', yarnApplicationId ?? '')
        .then(res => {
          const { amContainer, containers } = res?.data;
          setContainers([amContainer].concat(containers));
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id && yarnApplicationId) {
      fetchYarnApplicationLog();
    }
  }, [id, yarnApplicationId]);

  return (
    <RunningConsoleLog
      applicationId={yarnApplicationId ?? ''}
      containers={containers ?? []}
      onDownloadFile={url => {
        FlexibleApplicationApi.downloadLog(id ?? '', url ?? '');
      }}
      loading={loading}
      clusterId={id}
      logType='FlexibleApplication'
    />
  );
};
