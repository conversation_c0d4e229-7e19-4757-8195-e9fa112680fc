import { useEffect, useMemo, useState } from 'react';
import { Button, Popconfirm, Radio, Space, Spin, Tooltip } from 'antd';

import { CustomTable, SectionCaptain, useCustomTableHook } from '@/components';
import { Containers, FlexibleApplicationStatus } from '@/modules/FlexibleApplication/constants';
import { useDiagnosisStore } from '@/modules/FlexibleApplication/store';
import { getTime } from '@/modules/FlexibleApplication/utils';

import { ResourceAllocation } from './ResourceAllocation';

export const DiagnosisDetail = ({ taskStatus }: { taskStatus: FlexibleApplicationStatus }) => {
  const { id, fetchDetail, loading, diagnosisDetail, stopDiagnosis, resetPageInfo, setPageInfo } = useDiagnosisStore();
  const [groupValue, setGroupValue] = useState<'RUNNING' | 'FINISHED'>('RUNNING');
  const { selectedRowKeys, setSelectedRowKeys, pagination, handleTableChange } = useCustomTableHook({});

  useEffect(() => {
    if (id) fetchDetail();
  }, [id]);

  useEffect(() => {
    return () => {
      setPageInfo({
        diagnosisDetail: {},
      });
    };
  }, []);

  const customTableData: Containers[] = useMemo(() => {
    return groupValue === 'RUNNING' ? diagnosisDetail?.containers ?? [] : diagnosisDetail?.completedContainers ?? [];
  }, [groupValue, diagnosisDetail]);
  const columns = [
    {
      title: '实例名称',
      dataIndex: 'id',
    },
    {
      title: '节点',
      dataIndex: 'nodeHttpAddress',
    },
    {
      title: '核数配额',
      dataIndex: 'vcore',
      width: 80,
    },
    {
      title: '内存',
      dataIndex: 'memory',
      render: (text, record) => <span>{text}MiB</span>,
    },
    {
      title: '操作',
      dataIndex: 'name',
      render: (_, record) => (
        <Space>
          {groupValue === 'RUNNING' && (
            <Popconfirm title='确定停止？' onConfirm={() => stopDiagnosis(record.id)}>
              <Button type='link' style={{ padding: 0 }}>
                停止
              </Button>
            </Popconfirm>
          )}

          <a href={record.logUrl} target='_blank' rel='noreferrer'>
            查看日志
          </a>
        </Space>
      ),
    },
  ];
  return (
    <div className='diagnosis-detail'>
      <Spin spinning={loading} wrapperClassName='diagnosis-spin'>
        <div className='basic-information'>
          <SectionCaptain title='基本信息' className='mt-[9px]' titleClassName='section-captain' />
          <div className='row mt-2'>
            <div className='col'>
              <span className='title'>启动时间：</span>
              <span className='remark'>
                {diagnosisDetail?.startTime ? getTime(new Date(diagnosisDetail?.startTime)) : ''}
              </span>
            </div>
            <div className='col'>
              <span className='title'>执行命令：</span>
              <Tooltip title={diagnosisDetail?.arguments?.commandLine} placement='topLeft'>
                <span className='remark'>{diagnosisDetail?.arguments?.commandLine}</span>
              </Tooltip>
            </div>
            <div className='col'>
              <span className='title'>队列：</span>
              <span className='remark'>{diagnosisDetail?.arguments?.queue}</span>
            </div>
          </div>
          <div className='row mt-2'>
            <div className='col'>
              <span className='title'>AM日志连接：</span>
              {diagnosisDetail?.logUrl && (
                <a href={diagnosisDetail?.logUrl} target='_blank' rel='noreferrer'>
                  查看
                </a>
              )}
            </div>
          </div>
          <div className='row mt-2 h-[auto]'>
            <div className='col w-[100%] h-[auto]' style={{ alignItems: 'flex-start' }}>
              <span className='title'>资源分配：</span>
              <span className='remark'>
                <ResourceAllocation
                  containers={diagnosisDetail?.containers ?? []}
                  numTotalExecutors={diagnosisDetail?.numTotalExecutors ?? 0}
                />
              </span>
            </div>
          </div>
          <SectionCaptain title='实例信息' className='mt-6' titleClassName='section-captain' />
          <div className='mt-2 operate'>
            <Radio.Group
              options={[
                { label: '运行中实例', value: 'RUNNING' },
                { label: '已完成实例', value: 'FINISHED' },
              ]}
              optionType='button'
              value={groupValue}
              onChange={({ target: { value } }) => {
                setGroupValue(value);
              }}
            />
            {groupValue === 'RUNNING' && (
              <Button
                className='mr-[-8px]'
                onClick={() =>
                  stopDiagnosis(selectedRowKeys, () => {
                    if (selectedRowKeys.length) {
                      setSelectedRowKeys([]);
                    }
                  })
                }
                disabled={!selectedRowKeys.length}
              >
                批量停止
              </Button>
            )}
          </div>
        </div>
        <div style={{ height: 'calc(100% - 234px)' }} className='mt-2'>
          <CustomTable
            dataSource={customTableData}
            columns={columns}
            onChange={data => {
              handleTableChange(data);
            }}
            pagination={pagination}
            rowSelection={
              groupValue === 'RUNNING'
                ? {
                  type: 'checkbox',
                  selectedRowKeys,
                  onChange: (rowKeys: string[]) => {
                    setSelectedRowKeys(rowKeys);
                  },
                }
                : undefined
            }
            rowKey='id'
            scroll={true}
          />
        </div>
      </Spin>
    </div>
  );
};
