import { useEffect, useRef, useState } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Button, Popconfirm, Popover, Space, Tooltip } from 'antd';
import { history } from 'umi';

import { CustomTable } from '@/components';
import { useRightsHook } from '@/hooks';
import { FlexibleApplicationColumnType } from '@/modules/FlexibleApplication/constants';
import { useFlexibleApplicationStore } from '@/modules/FlexibleApplication/store';
import { FlexibleApplicationApi } from '@/services';
import { handleCheckResultResult } from '@/utils/handleCheckResourceResult';

import { StatusComponent } from '../../../components/StatusComponent';
import { ApplicationStatus, ApplicationType } from '../../../constants/enum';

import { Diagnosis } from './DiagnosisDropdown';
import { ExpansionShrink } from './ExpansionShrink';

export const Content = ({ pagination, queryParams, handleTableChange, setTotal }) => {
  const { hasRights } = useRightsHook();
  const {
    loading,
    list,
    total,
    fetchData,
    selectedRowKeys,
    setSelectedRowKeys,
    onChangeStatus,
    startApplication,
    stopApplication,
    setListItemValue,
    deleteApplication,
  } = useFlexibleApplicationStore();
  const debouncedRef = useRef<any>();
  const [temp, setTemp] = useState<FlexibleApplicationColumnType | null>(null);
  const [capacityOpen, setCapacityOpen] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  useEffect(() => {
    setTotal(total);
  }, [total]);
  const startStatus = [
    'WAITING_START',
    'UNSTART',
    'STARTING',
    'START_FAILED',
    'RUNNING_FAILED',
    'EXCEPTION',
    'STOPPED',
  ];
  const columns: Array<ColumnType<FlexibleApplicationColumnType>> = [
    {
      title: '作业名称',
      dataIndex: 'name',
      width: 235,
      render: (text, record) => {
        return (
          <div className='assignment-name'>
            <Tooltip title={text} placement='topLeft'>
              <Button
                type='link'
                size='small'
                disabled={!hasRights('application:write', record.projectAuth)}
                onClick={() => {
                  history.push(`/flexible-application/detail/${record.id}`);
                }}
                className='assignment-name-title'
              >
                {text}
              </Button>
            </Tooltip>
            {record.isPublished == 0 && (
              <Popover
                content={
                  <div className='assignment-name-tooltip'>
                    <ExclamationCircleOutlined className='icon cr-[8px]' />
                    <div>
                      作业内容存在更新，您可
                      <Button
                        type='link'
                        className='deploy'
                        disabled={!hasRights('application:operate', record.projectAuth)}
                        onClick={() => startApplication(record.id, queryParams)}
                      >
                        一键启动
                      </Button>
                      任务
                    </div>
                  </div>
                }
                placement='right'
                overlayClassName='assignment-tooltip'
              >
                <i className={'iconfont icon-messagepoint-line ml-[5px]'} style={{ color: '#FFB232' }}></i>
              </Popover>
            )}
          </div>
        );
      },
    },
    {
      title: '作业类型',
      dataIndex: 'appType',
      width: 100,
      render: text => ApplicationType[text],
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 120,
      render: (text, record) => (
        <StatusComponent
          id={record.id}
          pipelineStatus={text}
          enumTaskStatus={ApplicationStatus}
          fetchStatuseApi={FlexibleApplicationApi.checkStatus}
          onChangeStatus={status => onChangeStatus(status, record.id)}
          keyAlias='status'
        />
      ),
    },
    {
      title: '集群资源配额',
      dataIndex: 'name',
      render: (_, record) => {
        const cpu = Number(record.executorCpu);
        const hiz = (Number(record.executorMemory) / 1024).toFixed(0);
        const instance = Number(record.instanceCount);
        const labels = [record.clusterName, record.yarnQueue].filter(Boolean);

        const mainInfo = `${cpu * instance}核${Number(hiz) * instance}G`;
        const descInfo = `(${cpu}核/${hiz}G x ${instance}实例)`;

        return (
          <Tooltip
            placement='topLeft'
            title={
              <div className='flex flex-col'>
                <div>
                  <span>运行集群：</span>
                  <span>{`${labels}`}</span>
                </div>
                <div>
                  <span>资源：</span>
                  <span>{`${mainInfo}${descInfo}`}</span>
                </div>
              </div>
            }
          >
            <div className='flex flex-col'>
              <div>
                <span>运行集群：</span>
                <span className='text-gray-5'>{`${labels}`}</span>
              </div>
              <div>
                <span>资源：</span>
                <span className='text-gray-5'>{`${mainInfo}${descInfo}`}</span>
              </div>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '最后修改时间',
      width: 150,
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      dataIndex: 'name',
      width: 270,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type='link'
            disabled={
              record.status === 'STARTING' ||
              record.status === 'STOPPING' ||
              !hasRights('application:operate', record.projectAuth)
            }
            style={{ padding: 0 }}
            onClick={
              startStatus.includes(record.status)
                ? () => startApplication(record.id, queryParams)
                : () => stopApplication(record.id, queryParams)
            }
          >
            {startStatus.includes(record.status) ? '启动' : '停止'}
          </Button>

          <Button
            type='link'
            style={{ padding: 0 }}
            disabled={
              !['STOP_FAILED', 'RUNNING'].includes(record.status) ||
              !hasRights('application:operate', record.projectAuth)
            }
            onClick={() => {
              setTemp(record);
              setCapacityOpen(true);
            }}
          >
            扩缩容
          </Button>
          <Diagnosis record={record} />
          <Button
            type='link'
            style={{ padding: 0 }}
            disabled={!hasRights('application:write')}
            onClick={() => history.push(`/flexible-application/clone/${record.id}?name=${record.name}`)}
          >
            克隆
          </Button>
          <Button
            type='link'
            style={{ padding: 0 }}
            disabled={!hasRights('application:write', record.projectAuth)}
            onClick={() => history.push(`/flexible-application/edit/${record.id}?name=${record.name}`)}
          >
            编辑
          </Button>
          <Popconfirm
            title='确定删除？'
            disabled={
              !['UNSTART', 'START_FAILED', 'STOPPED', 'RUNNING_FAILED', 'EXCEPTION'].includes(record.status) ||
              !hasRights('application:write', record.projectAuth)
            }
            onConfirm={() => deleteApplication(record.id, queryParams)}
            placement='topLeft'
          >
            <Button
              type='link'
              style={{ padding: 0 }}
              disabled={
                !['UNSTART', 'START_FAILED', 'STOPPED', 'RUNNING_FAILED', 'EXCEPTION'].includes(record.status) ||
                !hasRights('application:write', record.projectAuth)
              }
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (debouncedRef.current) clearTimeout(debouncedRef.current);
    debouncedRef.current = setTimeout(() => {
      fetchData(queryParams);
    }, 300);
  }, [queryParams]);

  const CancelHandle = () => {
    setCapacityOpen(false);
    setTemp(null);
  };

  const confirmHandle = (count, forceStart: boolean = false) => {
    try {
      if (count === temp?.instanceCount) return CancelHandle();
      setConfirmLoading(true);
      FlexibleApplicationApi.applicationScale({
        id: temp?.id ?? '',
        count,
        forceStart,
      })
        .then(({ code, data: { instanceCount } }) => {
          if (code === '0000') {
            setListItemValue('instanceCount', instanceCount, temp?.id ?? '');
            CancelHandle();
          }
        })
        .catch(result => {
          handleCheckResultResult(result, () => confirmHandle(count, true));
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    } catch (error) {
      setConfirmLoading(false);
    }
  };

  return (
    <div className='content'>
      <CustomTable
        dataSource={list}
        columns={columns}
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 1200 }}
        className={'flex-1'}
        rowKey='id'
        rowSelection={{
          selectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys, selectedRows);
          },
        }}
      ></CustomTable>
      {capacityOpen && (
        <ExpansionShrink
          open={capacityOpen}
          count={temp?.instanceCount ?? 1}
          onCancel={CancelHandle}
          confirmHandle={confirmHandle}
          confirmLoading={confirmLoading}
        />
      )}
    </div>
  );
};
