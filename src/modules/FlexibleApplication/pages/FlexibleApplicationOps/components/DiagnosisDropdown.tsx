import { useState } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps } from 'antd';
import { history } from 'umi';

import { FlexibleApplicationColumnType } from '@/modules/FlexibleApplication/constants';

interface Props {
  record: FlexibleApplicationColumnType;
}
type TYPE = 'DETAIL' | 'WORK' | 'OPERATE';

export const Diagnosis = (props: Props) => {
  const { record } = props;
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const handleClick = (type: TYPE) => {
    history.push({
      pathname: `/flexible-application/${record.id}/diagnosis`,
      // eslint-disable-next-line max-len
      search: `?type=${type}&id=${record.id}&name=${record.name}&trackUrl=${record.trackUrl}&taskStatus=${record.status}&yarnApplicationId=${record.yarnApplicationId}&clusterId=${record.clusterId}`,
    });
  };
  const items: MenuProps['items'] = [
    {
      key: 'detail',
      label: (
        <div
          onClick={() => {
            handleClick('DETAIL');
          }}
        >
          实例详情
        </div>
      ),
    },
    {
      key: 'workLog',
      label: (
        <div
          onClick={() => {
            handleClick('WORK');
          }}
        >
          运行日志
        </div>
      ),
    },
    {
      key: 'operateLog',
      label: <div onClick={() => handleClick('OPERATE')}>操作日志</div>,
    },
    {
      key: 'track',
      label: (
        <div
          onClick={() => {
            window.open(record.trackUrl);
          }}
        >
          追踪
        </div>
      ),
    },
  ];
  return (
    <Dropdown menu={{ items }} open={dropdownOpen} onOpenChange={() => setDropdownOpen(!dropdownOpen)}>
      <Button type='link' style={{ padding: 0 }} onClick={() => handleClick('DETAIL')}>
        <span>诊断</span>
        {dropdownOpen ? <UpOutlined style={{ fontSize: 12 }} /> : <DownOutlined style={{ fontSize: 12 }} />}
      </Button>
    </Dropdown>
  );
};
