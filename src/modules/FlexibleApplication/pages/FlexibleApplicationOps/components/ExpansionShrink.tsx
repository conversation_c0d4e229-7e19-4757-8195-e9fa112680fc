import { useEffect } from 'react';
import { Form, InputNumber, Modal, ModalProps } from 'antd';

interface Props extends ModalProps {
  open: boolean;
  onCancel: () => void;
  confirmLoading?: boolean;
  count: number;
  confirmHandle: (count: number) => void;
}

export const ExpansionShrink = (props: Props) => {
  const { open, onCancel, confirmLoading, count, confirmHandle } = props;
  const [form] = Form.useForm();
  useEffect(() => {
    if (count) {
      form.setFieldValue('count', Number(count));
    }
  }, [count]);
  const onOk = async () => {
    const values = await form.validateFields();
    confirmHandle(values.count);
  };
  return (
    <Modal title='扩缩容' open={open} onCancel={onCancel} width='464px' onOk={onOk} confirmLoading={confirmLoading}>
      <Form form={form}>
        <Form.Item name='count' label='扩缩容'>
          <InputNumber min={1} max={1024} style={{ width: '120px' }} precision={0} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
