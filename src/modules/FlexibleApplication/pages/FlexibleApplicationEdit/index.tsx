import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Form, Input, InputNumber, message } from 'antd';
import { history, useParams, useSearchParams } from 'umi';

import { SectionCaptain } from '@/components';
import { DynamicForm } from '@/components/business/ui/DynamicForm';
import {
  AvailableAddOptionsSelect,
} from '@/components/business/ui/RunningTimeConfig/components/AvailableAddOptionsSelect';
import { useGlobalHook, useRightsHook } from '@/hooks';
import { ClusterSelect } from '@/modules/Cluster/components/ClusterSelect';
import { ClusterOptsMasterSelect } from '@/modules/ClusterOpts/components/ClusterOptsMasterSelect';
import { ClusterOptsApi } from '@/modules/ClusterOpts/services/ClusterOptsApi';
import { FlexibleApplicationApi } from '@/services';
import { handleCheckResultResult } from '@/utils/handleCheckResourceResult';

import './index.less';

const formItemLayout = {
  labelCol: { style: { width: '136px' } },
  wrapperCol: { span: 22 },
};

interface ClusterSelectOption extends DefaultOptionType {
  marayarnOptsId: string;
}

export const FlexibleApplicationEdit = () => {
  const [searchParams] = useSearchParams();
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { hasRights } = useRightsHook();
  const [optsList, setOptsList] = useState<PipelineParameterModel[]>();
  const [detail, setDetail] = useState<PipelineParameterModel>();
  const [form] = Form.useForm();
  const [selectedOptsList, setSelectedOptsList] = useState<Array<PipelineParameterModel & { value?: any }>>([]);
  const [opts, setOpts] = useState({});
  const dynamicFormRef = useRef<any>();
  const [applicationId, setApplicationId] = useState<string>('');

  const name = searchParams.get('name') ?? '';
  const [list, setList] = useState<ClusterOptsEntity[]>([]);
  const { id, cloneId } = useParams();
  useEffect(() => {
    setPageInfo({
      title: id ? `编辑：${name ?? ''}` : '创建弹性作业',
      description: '',
    });
    if (id) {
      setApplicationId(id ?? '');
    } else if (cloneId) {
      setApplicationId(cloneId ?? '');
    }

    return () => {
      resetPageInfo();
    };
  }, []);

  const availableAddOptions = useMemo(() => {
    return optsList?.filter(x => !selectedOptsList?.find(item => item?.name === x.name)) ?? [];
  }, [optsList, selectedOptsList]);

  useEffect(() => {
    ClusterOptsApi.getOptsListHasOptions('marayarn', 'marayarn').then(({ data }) => {
      setList(data);
    });
  }, []);

  useEffect(() => {
    if (applicationId && list.length) fetchDetail();
  }, [applicationId, list]);

  const fetchDetail = () => {
    try {
      FlexibleApplicationApi.applicationDetail(applicationId).then(res => {
        const { data, code } = res;
        if (code === '0000') {
          form.setFieldsValue(data ?? {});
          if (cloneId) {
            form.setFieldValue('name', data?.name ? `${data.name}_克隆` : '');
          }
          const parameters = list?.find(item => item.id === data.optsId)?.parameters;
          setOpts(data?.settingMap ?? {});
          setOptsList(parameters);
          const OptsList: PipelineParameterModel[] = [];
          for (const k in data.settingMap) {
            const temp = parameters?.find(item => item.name === k);
            temp && OptsList.push(temp);
          }
          setSelectedOptsList(OptsList);
          setDetail(data);
        }
      });
    } catch (error) {}
  };

  // 当更新框架时，返回当前框架对应的配置项列表
  const onChangeOptsMaster = (newOptsId: string, optsMaster: ClusterOptsEntity) => {
    form.setFieldValue('optsId', newOptsId);
    form.setFieldValue('addConfig', undefined);
    if (optsMaster) {
      setOpts({});
      setOptsList(optsMaster.parameters);
      setSelectedOptsList([]);
    }
  };

  // 当选中配置项时，添加配置项到自选列表
  const addOptions = (record?: PipelineParameterModel) => {
    if (record && selectedOptsList.every(item => item.name !== record.name)) {
      setSelectedOptsList(selectedOptsList.concat([record]));
      let defaultVal;
      try {
        defaultVal = JSON.parse(record.defaultValue);
      } catch (error) {
        defaultVal = record.defaultValue ?? null;
      }
      if (defaultVal == '' && record.type?.includes('LIST')) {
        defaultVal = [];
      }
      const newOpts = {
        ...opts,
        [record.name]: defaultVal,
      };
      setOpts(newOpts);
    }
  };

  const onChangeOpts = (newOpts: Record<string, any>) => {
    setOpts({
      ...newOpts,
    });
  };

  // 从自选列表中删除配置项
  const deleteOpts = (record: PipelineParameterModel) => {
    setSelectedOptsList([...selectedOptsList.filter(x => x.name !== record.name)]);
    const newOpts = {
      ...opts,
    };
    delete newOpts[record.name];
    setOpts({
      ...newOpts,
    });
  };

  const save = async (type: 'SAVE' | 'DEPLOY', forceStart: boolean = false) => {
    const values = await form.validateFields();
    const settingMap = await dynamicFormRef.current?.validateFields();
    values.settingMap = settingMap;
    if (id) {
      values.id = applicationId;
    }
    if (values.addConfig) {
      delete values.addConfig;
    }
    const saveApi = id ? FlexibleApplicationApi.update : FlexibleApplicationApi.create;
    const startApi = id ? FlexibleApplicationApi.updateStart : FlexibleApplicationApi.createStart;
    const Api = type === 'SAVE' ? saveApi : startApi;
    Api({
      ...values,
      forceStart,
    })
      .then(res => {
        if (res?.code === '0000') {
          message.success(res?.msg ?? '成功');
          history.go(-1);
        }
      })
      .catch(res => {
        handleCheckResultResult(res, () => save(type, true));
      });
  };
  return (
    <div className='edit'>
      <div className='form'>
        <Form form={form} {...formItemLayout} initialValues={{ instanceCount: 1 }}>
          <div className='px-4'>
            <SectionCaptain title='基本信息' className='mt-2' />
            <Form.Item
              className='mt-2 '
              label='作业名称'
              name='name'
              rules={[{ required: true, message: '请输入作业名称' }]}
            >
              <Input className='w-[420px]' placeholder='请输入' />
            </Form.Item>
            <Form.Item
              className='mt-2 '
              label='作业执行命令'
              name='command'
              rules={[{ required: true, message: '请输入作业执行命令' }]}
            >
              <Input.TextArea className='w-[420px]' style={{ minHeight: 88 }} placeholder='请输入' />
            </Form.Item>
            <Form.Item className='mt-2 ' label='描述' name='description'>
              <Input.TextArea className='w-[420px]' style={{ minHeight: 88 }} placeholder='请输入' />
            </Form.Item>
            <SectionCaptain title='运行配置信息' className='mt-4' />
            <Form.Item
              className='mt-2 '
              label='运行集群'
              name='clusterId'
              rules={[{ required: true, message: '请输入作业执行命令' }]}
            >
              <ClusterSelect
                clusterType='yarn'
                className='w-[420px]'
                onChange={(value, opt: ClusterSelectOption) => {
                  const findMarayarnOpts = list.find(item => item.id === opt.marayarnOptsId);
                  if (findMarayarnOpts) {
                    form.setFieldsValue({ optsId: opt.marayarnOptsId, addConfig: undefined });
                    setOptsList(findMarayarnOpts?.parameters ?? []);
                  } else {
                    form.setFieldsValue({ optsId: undefined, addConfig: undefined });
                    setOptsList([]);
                  }

                  setSelectedOptsList([]);
                  setOpts({});
                }}
              />
            </Form.Item>
            <Form.Item
              className='mt-2 '
              label='运行框架'
              name='optsId'
              rules={[{ required: true, message: '请输入作业执行命令' }]}
            >
              {/* <ClusterOptsSelect className='w-[420px]' optsType='marayarn' /> */}
              {/* <div className='w-[420px]'> */}
              <ClusterOptsMasterSelect
                className='w-[420px]'
                optsType={'marayarn'}
                scope='marayarn'
                onChange={onChangeOptsMaster}
              ></ClusterOptsMasterSelect>
              {/* </div> */}
            </Form.Item>
            <div style={{ position: 'relative' }}>
              <Form.Item className='mt-2' label='执行器实例数' name='instanceCount'>
                <InputNumber precision={0} step={1} placeholder='请输入' className='w-[120px]' min={1} />
              </Form.Item>
              <span className='description ml-[9px]'>设置弹性作业的并发实例数</span>
            </div>
            <Form.Item label='添加配置' name='addConfig'>
              <AvailableAddOptionsSelect
                className='w-[420px]'
                key='availableOptions'
                onChange={addOptions}
                options={availableAddOptions}
              />
            </Form.Item>
            {selectedOptsList.length > 0 && (
              <div
                className='py-1 px-8 bg-neutral-50 pl-[0px] ml-[-16px] mt-[-4px]'
                style={{ width: 'calc(100% + 32px)' }}
              >
                <div style={{ width: 635 }}>
                  <DynamicForm
                    ref={dynamicFormRef}
                    parameters={selectedOptsList}
                    initialValues={opts}
                    onChange={onChangeOpts}
                    deletable={true}
                    labelHelpMode={true}
                    layout='horizontal'
                    onDelete={deleteOpts}
                    {...formItemLayout}
                    wrapperCol={{ span: 24 }}
                    className='pl-4'
                  />
                </div>
              </div>
            )}
          </div>
        </Form>
      </div>
      <div className='footer'>
        <Button
          type='primary'
          disabled={!hasRights('application:write', id ? detail?.projectAuth : undefined)}
          onClick={async () => save('DEPLOY')}
        >
          发布
        </Button>
        <Button
          type='primary'
          className='ml-2'
          disabled={!hasRights('application:write', id ? detail?.projectAuth : undefined)}
          onClick={async () => save('SAVE')}
        >
          保存
        </Button>
        <Button className='ml-2' onClick={() => history.go(-1)}>
          取消
        </Button>
      </div>
    </div>
  );
};
