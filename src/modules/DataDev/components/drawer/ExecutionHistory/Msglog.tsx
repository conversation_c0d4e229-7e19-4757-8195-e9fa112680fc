import { useEffect, useState } from 'react';
import { Button, Typography } from 'antd';

import './index.less';

const { Paragraph, Text } = Typography;
interface Props {
  value: string;
}
export const Msglog = (props: Props) => {
  const { value } = props;
  const [showAllmsg, setAllmsgShow] = useState<boolean>(false);
  const [showBtn, setBtnShow] = useState<boolean>(false);
  const [logContentHeight, setLogContentHeight] = useState<number>(66);
  useEffect(() => {
    const containerHeight = document.getElementById('logContent');
    const inspectLogHeight = () => {
      if (containerHeight && containerHeight?.scrollHeight > 66) {
        setLogContentHeight(containerHeight.scrollHeight + 22);
        setBtnShow(true);
      }
    };
    inspectLogHeight();
  }, []);
  return (
    <div className='flexMsglog mt-[12px]'>
      <div className='title'>日志内容：</div>
      <div className='msglog-oneRow'>
        <pre id='logContent' style={{ height: showAllmsg ? logContentHeight : 66 }}>
          {value}
          {showAllmsg && <span className='scope'></span>}
        </pre>
      </div>
      {value && showBtn && (
        <Button
          type='link'
          className='btn'
          onClick={() => setAllmsgShow(!showAllmsg)}
          style={{ top: showAllmsg ? logContentHeight - 22 : 44 }}
        >
          {showAllmsg ? '收起' : '查看全部'}
        </Button>
      )}
    </div>
  );
};
