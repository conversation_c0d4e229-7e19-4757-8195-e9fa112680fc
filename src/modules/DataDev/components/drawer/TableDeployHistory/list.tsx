import { useEffect, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { But<PERSON>, Drawer } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { TableDeployHistoryApi } from '@/services';

interface Props {
  deployId: string;
  open: boolean;
  onClose?: () => void;
}

export const TableDeployHistoryList = (props: Props) => {
  const { open, deployId, onClose } = props;
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const { pagination, setPagination, queryParams, handleTableChange, onRowSelectionChange } = useCustomTableHook({
    pageSize: 20,
    filter: {
      tableDeployId: deployId,
    },
  });
  const columns: Array<ColumnType<WH_LAYER.BusinessProcessEntity>> = [
    {
      title: '发布类型',
      dataIndex: 'actionType',
      key: 'actionType',
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 130,
      render(record) {
        return (
          <>
            <a className='mr-4'>查看详情</a>
          </>
        );
      },
    },
  ];

  const fetchData = () => {
    setLoading(true);
    TableDeployHistoryApi.getList(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <Drawer
      open={open}
      onClose={onClose}
      closable={false}
      width='712px'
      title='模型发布历史记录'
      extra={
        <div className='flex  '>
          <Button type='text' icon={<CloseOutlined />} onClick={onClose} />
        </div>
      }
    >
      <CustomTable
        dataSource={list}
        columns={columns}
        onRowSelectionChange={onRowSelectionChange}
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        scroll={true}
        style={{ border: '1px solid #f0f0f0' }}
      ></CustomTable>
    </Drawer>
  );
};
