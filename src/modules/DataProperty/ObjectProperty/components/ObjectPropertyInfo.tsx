import { Collapse, Descriptions } from 'antd';
import { sortBy } from 'lodash-es';

import './ObjectPropertyInfo.less';

import { ColumnField, InstanceInfo } from '../../models/ObjectProperty';

interface Props {
  detail: InstanceInfo;
  fields: ColumnField[];
}

export const ObjectPropertyInfo = (props: Props) => {
  const { detail, fields } = props;

  const baseItems = [
    {
      key: '1',
      label: '对象Id',
      children: detail.objectId,
    },
    {
      key: '2',
      label: '对象名称',
      children: detail.objectName,
    },
    {
      key: '3',
      label: '对象状态',
      children: detail.objectStatus,
    },
    {
      key: '4',
      label: '对象分类',
      children: detail.objectType,
    },
  ];

  const otherItems = sortBy(
    fields.filter(item => !['objectName', 'objectStatus', 'objectId', 'objectType'].includes(item.colName)),
    item => item.isIdentificationField ? 0 : 1,
  ).map(item => {
    return {
      key: item.colName,
      label: item.colDisplay || item.colName,
      children: detail[item.colName],
      labelStyle: item.isIdentificationField ? { fontWeight: 'bold', color: 'var(--color-gray-6)' } : {},
    };
  });
  
  const collapseItems = [
    {
      key: '1',
      label: '基础属性',
      children: <Descriptions
        className='pl-3 pt-3 property-desc' labelStyle={{ width: '100px' }} column={2} items={baseItems}
      />,
    },
    {
      key: '2',
      label: '其他属性',
      children: <Descriptions
        className='pl-3 property-desc' labelStyle={{ width: '100px' }} column={2} items={otherItems}
      />,
    },
  ];

  return (<div className='overflow-auto h-full bg-[#fff]'>
    <Collapse className='property-collapse' items={collapseItems} defaultActiveKey={['1', '2']} />
  </div>);
};
