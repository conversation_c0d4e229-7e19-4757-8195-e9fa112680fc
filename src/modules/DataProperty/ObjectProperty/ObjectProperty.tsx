import { useEffect, useMemo, useState } from 'react';
import { ArrowDownOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Form, Input, Popover, Radio } from 'antd';

import { SearchInput, Select } from '@/components';
import { findPath } from '@/utils/treeHelper';

import './ObjectProperty.less';

import { objectPropertyApi } from '../service/objectProperty';

import { useObjectModelingStore } from './store/useObjectModelingStore';
import { InstanceTable } from './InstanceTable';

export const ObjectProperty = () => {
  const objectId = useObjectModelingStore(state => state.objectId);
  const treeData = useObjectModelingStore(state => state.treeData);
  const [searchKey, setSearchKey] = useState('');
  const [selectedSearchFields, setSelectedSearchFields] = useState([]);
  const [showMoreSearch, setShowMoreSearch] = useState(false);
  const [filterParams, setFilterParams] = useState({
    logicalOperator: 'and',
  });
  const [fields, setFields] = useState(null);
  const [showTable, setShowTable] = useState(false);
  const selectedSearchFieldsOptions = useMemo(() => {
    if(!fields) return [];
    return fields.filter(item => {
      return selectedSearchFields.includes(item.colName);
    });
  }, [fields, selectedSearchFields]);

  const filedCheckOptions = (fields || []).map(item => ({
    ...item,
    value: item.colName,
    label: item.colDisplay || item.colName,
  }));
  const [form] = Form.useForm();
  const path = useMemo(() => {
    if (!treeData?.length || !objectId) return;
    const pathNodes = findPath(treeData, item => item.id === objectId);
    if (pathNodes?.length) {
      return pathNodes.map(item => item.name).join(' / ');
    }
    return '';
  }, [treeData, objectId]);

  useEffect(() => {
    if(!objectId) return;
    setShowTable(false);
    getFields();
    setSearchKey('');
    setSelectedSearchFields([]);
    setShowMoreSearch(false);
    setFilterParams({
      logicalOperator: 'and',
    });
    localStorage.removeItem(objectId);
  }, [objectId]);

  const getFields = async () => {
    const { data } = await objectPropertyApi.getColumnByObjId(objectId);
    setFields(data || []);
    setShowTable(true);
  };

  const handleClearSearch = () => {
    form.resetFields();
    handleSearch();
  };

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = Object.keys(values).map(key => ({
      key,
      value: values[key].value,
      operator: values[key].type,
    })).filter(item => item.value);
    setFilterParams({
      ...filterParams,
      search: searchKey,
      conditionList: params,
    });
  };

  return (<div className='w-full flex flex-col h-full'>
    <div className='header-name w-full bb-1 px-2 py-2 text-gray-8 text-sm'>{path}</div>
    <div className='px-2 py-2'>
      <div className='flex items-center'>
        <SearchInput
          className='w-[250px]'
          placeholder='名称模糊搜索'
          value={searchKey}
          onChange={e => setSearchKey(e.target.value)}
          onSearch={val => setFilterParams({ ...filterParams, search: val })}
        />
        <div
          className='flex items-center text-primary ml-5 cursor-pointer'
          onClick={() => setShowMoreSearch(isShow => !isShow)}
        >
          <span className='mr-1'>高级搜索</span>
          <ArrowDownOutlined />
        </div>
      </div>
      {
        showMoreSearch && <div>

          <Form
            form={form}
            labelCol={{ style: { width: '90px' } }}
            className='mt-2 field-search-form'
            labelAlign='left'
            key={objectId}
            layout='inline'
            labelWrap
          >
            {
              selectedSearchFieldsOptions.map(item =>
                <Form.Item key={item.colName} label={item.colDisplay || item.colName} className='mb-2'>
                  <div className='flex items-center'>
                    <Form.Item name={[item.colName, 'type']} initialValue={'like'} className='mr-0'>
                      <Select
                        defaultValue={'like'}
                        options={[
                          { value: 'like', label: '模糊' },
                          { value: 'eq', label: '精确' },
                        ]}
                      ></Select>
                    </Form.Item>
                    <Form.Item name={[item.colName, 'value']} className='ml-[-1px]'>
                      <Input className='w-[200px]' allowClear></Input>
                    </Form.Item>
                  </div>
                </Form.Item>,
              )
            }
          </Form>
          
          <div className='flex items-center mt-2 justify-between'>
            <Popover
              placement='bottom'
              trigger="click"
              content={
                <Checkbox.Group
                  className='field-select-popover'
                  options={filedCheckOptions}
                  value={selectedSearchFields}
                  onChange={setSelectedSearchFields}
                />
              }
              title=""
            >
              <Button icon={<PlusOutlined />}>添加字段</Button>
            </Popover>
            <div className='flex items-center'>
              <Radio.Group
                onChange={e => setFilterParams({ ...filterParams, logicalOperator: e.target.value })}
                value={filterParams.logicalOperator}
              >
                <Radio value={'and'}>全部满足</Radio>
                <Radio value={'or'}>任意满足</Radio>
              </Radio.Group>
              <Button type='primary' onClick={handleSearch}>搜索</Button>
              <Button className='ml-2 mr-5' onClick={handleClearSearch}>重置</Button>
            </div>
          </div>
        </div>
      }
    </div>
    <div className='flex-1 overflow-hidden'>
      {showTable && fields && <InstanceTable fields={fields} filterParams={filterParams}></InstanceTable>}
    </div>
  </div>);
};
