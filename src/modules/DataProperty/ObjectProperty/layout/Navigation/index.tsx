// import { Radio } from 'antd';
// import { shallow } from 'zustand/shallow';
import { useState } from 'react';

import { SearchInput } from '@/components';

import { useObjectModelingStore } from '../../store/useObjectModelingStore';

import { ObjectModelingTree } from './ObjectModelingTree';

export const Navigation = () => {
  const searchKey = useObjectModelingStore(state => state.searchKey);
  const setSearchKey = useObjectModelingStore(state => state.setSearchKey);
  const [query, setQuery] = useState(searchKey);

  return (
    <div
      className='h-full flex flex-col bg-white-1 overflow-hidden'
      style={{
        borderRight: '1px solid var(--color-white-4)',
      }}
    >
      <div className='px-3 my-2'>
        <SearchInput
          placeholder='关键字搜索'
          value={query}
          onChange={e => setQuery(e.target.value)}
          onSearch={setSearchKey}
        />
      </div>
      <ObjectModelingTree />
    </div>
  );
};
