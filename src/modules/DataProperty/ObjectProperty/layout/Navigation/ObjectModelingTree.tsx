import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Tree, TreeProps } from 'antd';
import { useSearchParams } from 'umi';

import { DeployStatus } from '@/modules/DimensionModeling/components/ui/DeployStatus';
import { ObjectModelingTreeNode } from '@/modules/ObjectModeling';
import { getParentsValues } from '@/utils';
import { findNodeWithDeep } from '@/utils/treeHelper';

import { useObjectModelingStore } from '../../store/useObjectModelingStore';

interface Props extends TreeProps {}
export const ObjectModelingTree: React.FC<Props> = props => {
  const { ...otherProps } = props;
  const [height, setHeight] = useState();
  const containerRef = useRef();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const treeData = useObjectModelingStore(state => state.treeData);
  const setObjectId = useObjectModelingStore(state => state.setObjectId);
  const fetchTreeData = useObjectModelingStore(state => state.fetchTreeData);
  const objectId = useObjectModelingStore(state => state.objectId);
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');

  useEffect(() => {
    if (!treeData?.length) return;

    if (id) {
      const newExpandedKeys = getParentsValues(treeData, {
        parentKey: 'id',
        childrenKey: 'children',
        value: id,
        currentKey: 'id',
      });
      setExpandedKeys(Array.from(new Set(expandedKeys.concat(newExpandedKeys))));
      setSelectedKeys([id]);
      setObjectId(id);
    } else {
      const defaultObject = findNodeWithDeep(treeData, item => item.type === 'OBJECT' && item.count);
      if (defaultObject) {
        const newExpandedKeys = getParentsValues(treeData, {
          parentKey: 'id',
          childrenKey: 'children',
          value: defaultObject.id,
          currentKey: 'id',
        });
        const { id } = defaultObject;
        setExpandedKeys(Array.from(new Set(expandedKeys.concat(newExpandedKeys))));
        setSelectedKeys([id]);
        setObjectId(id);
      }
    }
  }, [id, treeData]);

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
  };

  useLayoutEffect(() => {
    setHeight(containerRef.current?.clientHeight);
  }, [treeData]);

  useEffect(() => {
    fetchTreeData();
  }, []);

  const handleClickName = (node: ObjectModelingTreeNode) => {
    setSelectedKeys([node.id]);
    if (node.type === 'OBJECT') {
      setObjectId(node.id);
      localStorage.removeItem(objectId);
    }
  };

  const titleRender = (node: ObjectModelingTreeNode) => {
    return (
      <div className='flex items-center' onClick={() => handleClickName(node)}>
        <span className='flex-1'>
          {node.name}（{node.count}）
        </span>
        <DeployStatus status={node?.deployStatus as DATA_DEV.DEPLOY_STATUS} />
      </div>
    );
  };

  return (
    <>
      <Tree.DirectoryTree
        className='custom-directory-tree overflow-auto'
        treeData={treeData}
        fieldNames={{ title: 'name', key: 'id' }}
        titleRender={titleRender}
        selectedKeys={selectedKeys}
        expandedKeys={expandedKeys}
        onExpand={onExpand}
        height={height}
        {...otherProps}
      />
    </>
  );
};
