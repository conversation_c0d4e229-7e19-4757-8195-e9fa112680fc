import { useEffect, useState } from 'react';
import { DeploymentUnitOutlined, TableOutlined } from '@ant-design/icons';
import { Drawer, Radio, Tabs } from 'antd';

import { ColumnField, InstanceInfo } from '../models/ObjectProperty';
import { objectPropertyApi } from '../service/objectProperty';

import { ObjectPropertyInfo } from './components/ObjectPropertyInfo';
import { ObjectRelation } from './components/ObjectRelation';

interface Props {
  selectedInstance: Record<string, any>;
  open: boolean;
  onClose: () => void;
}

export const InstanceDetailDrawer = (props: Props) => {
  const { selectedInstance, open, onClose } = props;
  const [activeKey, setActiveKey] = useState('property');
  const [viewType, setViewType] = useState<'table' | 'topology'>('table');
  const [detail, setDetail] = useState<InstanceInfo>({});
  const [fields, setFields] = useState<ColumnField[]>([]);
  const [curInstance, setCurInstance] = useState(selectedInstance);

  useEffect(() => {
    if(!curInstance?.vid) return;
    getDetail();
  }, [curInstance]);

  const getDetail = async () => {
    const { data } = await objectPropertyApi.getInstanceDetail({
      vid: curInstance.vid,
      tag: curInstance.tag,
    });
    if (data) {
      setDetail(data.data || {});
      setFields(data.columnList || []);
    }
  };

  const handleChangeInstance = item => {
    setCurInstance(item);
  };

  return (<Drawer
    title={detail?.objectName}
    styles={{ body: { padding: '0' } }}
    onClose={onClose}
    open={open}
    width='60%'
  >
    <Tabs
      activeKey={activeKey}
      onChange={key => setActiveKey(key)}
      className='flex-1 overflow-auto h-full'
      tabBarStyle={{ padding: '0 16px' }}
      destroyInactiveTabPane
      tabBarExtraContent={
        activeKey !== 'property' ? <Radio.Group onChange={e => setViewType(e.target.value)} value={viewType}>
          <Radio value={'table'}><TableOutlined className='text-[20px]' /></Radio>
          <Radio value={'topology'}><DeploymentUnitOutlined className='text-[20px]' /></Radio>
        </Radio.Group>
          : null}
      items={[
        {
          label: '对象属性',
          key: 'property',
          children: <ObjectPropertyInfo detail={detail} fields={fields} />,
        },
        {
          label: '对象关系',
          key: 'relation',
          children: <ObjectRelation
            viewType={viewType}
            id={curInstance?.vid}
            objectName={detail?.objectName}
            onChangeInstance={handleChangeInstance}
          />,
        },
      ]}
    />
  </Drawer>);
};
