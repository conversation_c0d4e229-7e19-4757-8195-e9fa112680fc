/**
 * 改造antd原始的Switch组件，使之支持设置checked 和 unchecked 状态的值
 */
import { useEffect } from 'react';
import { Switch as SwitchAntd, SwitchProps } from 'antd';

interface Props extends SwitchProps {
  value?: any;
  checkedValue: any;
  unCheckedValue: any;
}

export const SwitchCustom = (props: Props) => {
  const { value, onChange, checkedValue = true, unCheckedValue = false, ...restProps } = props;

  useEffect(() => {
    if (value === undefined) {
      onChange?.(restProps.defaultChecked ? checkedValue : unCheckedValue, undefined);
    }
  }, []);

  return (
    <SwitchAntd
      {...restProps}
      checked={value === checkedValue}
      onChange={(checked, e) => {
        if (checked) {
          onChange?.(checkedValue, e);
        } else {
          onChange?.(unCheckedValue, e);
        }
      }}
    />
  );
};
