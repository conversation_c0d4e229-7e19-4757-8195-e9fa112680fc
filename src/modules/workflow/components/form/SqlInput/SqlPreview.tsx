import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, DatePicker } from 'antd';
import dayjs from 'dayjs';

import { CodeMirror, useCustomTableHook } from '@/components';
import { JsonDataPreview } from '@/components/business/ui/JsonDataPreview';
import { TaskDefinitionApi } from '@/modules/workflow/services/TaskDefinitionApi';

export const SqlPreview = ({ value, sqlParams }) => {
  const [sql, setSql] = useState(value);
  const [result, setResult] = useState();
  const [loading, setLoading] = useState(false);
  const [scheduleTime, setScheduleTime] = useState(dayjs());

  const { pagination, handleTableChange, queryParams, filter, setTotal } = useCustomTableHook({
    pageSize: 5,
    filter: {
      ...sqlParams,
    },
  });

  const handleExecSql = async () => {
    setLoading(true);
    const { data } = await TaskDefinitionApi.execSql({
      page: (queryParams.page ?? 0) + 1,
      size: queryParams.size ?? 5,
      filter: {
        ...filter,
        taskParams: {
          ...filter?.taskParams,
          sql,
        },
        scheduleTime: scheduleTime?.format('YYYY-MM-DD HH:mm:ss'),
      },
    }).finally(() => {
      setLoading(false);
    });
    setResult(
      data ?? {
        pageNum: 1,
        pageSize: 5,
        totalNum: 0,
        rows: [],
      },
    );
    setTotal(data.totalNum);
  };

  // todo 一开始不应该执行
  useEffect(() => {
    handleExecSql();
  }, [queryParams]);

  return (
    <div>
      <CodeMirror lang='sql' value={value} onChange={setSql} />
      <div className='flex items-center justify-between'>
        <Alert showIcon type='warning' message='执行上述SQL，请确保仅包含select语句。支持全局参数自动替换。' />
        <div className='my-3'>
          <label>
            模拟执行时间：
            <DatePicker showTime format='YYYY-MM-DD HH:mm:ss' value={scheduleTime} onChange={setScheduleTime} />
          </label>
          <Button type='primary' className='ml-2' loading={loading} onClick={handleExecSql}>
            执行SQL
          </Button>
        </div>
      </div>

      <JsonDataPreview
        data={
          result ?? {
            pageNum: 1,
            pageSize: 5,
            totalNum: 0,
            rows: [],
          }
        }
        pagination={pagination}
        onChange={handleTableChange}
        className='bordered'
        style={{ borderTop: 'none' }}
        loading={loading}
      />
    </div>
  );
};
