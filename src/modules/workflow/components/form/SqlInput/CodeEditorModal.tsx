import { useEffect, useMemo, useState } from 'react';
import { color } from '@uiw/codemirror-extensions-color';
import { LanguageName, loadLanguage } from '@uiw/codemirror-extensions-langs';
import ReactCodeMirror from '@uiw/react-codemirror';
import { Modal, ModalProps, Tabs } from 'antd';

import { ProcessDefinition, TaskDefinitionItem } from '@/modules/workflow/models';
import { TaskDefinitionApi } from '@/modules/workflow/services/TaskDefinitionApi';

import './CodeEditorModal.less';

import { SqlPreview } from './SqlPreview';

export type LangType = 'SQL' | 'JSON' | 'SPL' | 'JAVASCRIPT' | 'AVIATOR_SCRIPT' | 'XML';

interface Props extends ModalProps {
  open: boolean;
  value: string;
  onChange?: (value: string) => void;
  lang?: LangType | LanguageName;
  title?: string;
  readOnly?: boolean;
  sqlParams: ProcessDefinition['globalParams'] & TaskDefinitionItem;
}
export const CodeEditorModal = (props: Props) => {
  const { open, value, onChange, title, readOnly = false, sqlParams, ...otherProps } = props;
  const [code, setCode] = useState(value);
  const [isChanged, setIsChanged] = useState(false);
  const [previewSql, setPreviewSql] = useState();

  const dirty = useMemo(() => {
    return value !== code;
  }, [value, code]);

  const onChangeTab = async (tabKey: string) => {
    if ((tabKey === 'preview' && isChanged) || !previewSql) {
      const { data } = await TaskDefinitionApi.getPreviewSql({
        type: sqlParams.taskParams.type as string,
        sql: code,
        segmentSeparator: sqlParams.taskParams.segmentSeparator,
      });
      setPreviewSql(data);
      setIsChanged(false);
    }
  };

  const onOk = e => {
    // todo 校验代码格式
    onChange?.(code);
    otherProps.onCancel?.(e);
  };

  // 当value更新时，同步更新code
  useEffect(() => {
    setCode(value);
  }, [value]);

  // todo 多语言代码编辑器
  return (
    <Modal
      title={ title }
      okButtonProps={{
        disabled: !dirty,
      }}
      width={1000}
      open={open}
      onOk={onOk}
      className='body-no-padding code-editor-modal'
      {...otherProps}
    >
      <Tabs
        items={[
          {
            label: 'SQL语句',
            key: 'sql',
            children: (
              <ReactCodeMirror
                value={code}
                onChange={code => {
                  setCode(code);
                  setIsChanged(true);
                }}
                extensions={[color, loadLanguage('sql')!].filter(x => x)}
                className='h-96 overflow-auto'
                readOnly={readOnly}
              />
            ),
          },
          {
            label: 'SQL预览',
            key: 'preview',
            children: previewSql && <SqlPreview value={previewSql} sqlParams={sqlParams} />,
          },
        ]}
        className='p-4 h-full'
        onChange={onChangeTab}
      />
    </Modal>
  );
};
