import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';

export const PredefineSql = props => {
  return (
    <Form.List {...props}>
      {(fields, { add, remove }) => (
        <>
          {fields.map(field => (
            <div key={field.key} className='mb-2 w-full flex'>
              <Form.Item noStyle className='flex-1' name={[field.name]}>
                <Input placeholder='请输入非查询SQL语句' />
              </Form.Item>
              <MinusCircleOutlined
                className='ml-2 text-gray text-[18px] cursor-pointer'
                onClick={() => remove(field.name)}
              />
            </div>
          ))}
          <Form.Item>
            <Button type='dashed' onClick={() => add()} style={{ width: '100%' }} icon={<PlusOutlined />}>
              添加
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
};
