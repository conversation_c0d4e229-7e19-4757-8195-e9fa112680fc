import { useMemo } from 'react';
import type { Graph, Node } from '@antv/x6';
import { Col, Form, FormInstance, Row } from 'antd';
import { isEqual } from 'lodash';

import { ConditionNodeSelect } from '../form/ConditionNodeSelect';
import { DependenceCondition } from '../form/DependenceCondition';

const Conditions = ({ form, graph, selectedNode }: { form: FormInstance; graph: Graph; selectedNode: Node }) => {
  const conditionTasks = useMemo<DefaultOptionType[]>(() => {
    // 获取所有后置节点
    const nextNodes = graph
      .getConnectedEdges(selectedNode, {
        outgoing: true,
      })
      .map(edge => edge.getTargetCell());
    return (
      nextNodes
        ?.filter(node => node)
        ?.map(node => {
          const { code, name } = node!.getData();
          return {
            label: name,
            value: code,
            key: code,
          };
        }) ?? []
    );
  }, [graph, selectedNode]);

  const preTasks = useMemo(() => {
    // 获取所有前置节点
    const preNodes = graph
      .getConnectedEdges(selectedNode, {
        incoming: true,
      })
      .map(edge => edge.getSourceCell());

    return (
      preNodes
        ?.filter(node => node)
        ?.map(node => {
          const { code, name } = node!.getData();
          return {
            label: name,
            value: code,
            key: code,
          };
        }) ?? []
    );
  }, [graph, selectedNode]);

  const rules = {
    successNode: [
      ({ getFieldValue }) => ({
        validator: (_, value) => {
          if (!value) {
            return Promise.reject(new Error('必填项'));
          }
          const failedNode = getFieldValue(['taskParams', 'conditionResult', 'failedNode']);
          if (isEqual(value, failedNode)) {
            return Promise.reject(new Error('成功状态和失败状态不能相同'));
          }
          return Promise.resolve();
        },
      }),
    ],
    failedNode: [
      ({ getFieldValue }) => ({
        validator: (_, value) => {
          if (!value) {
            return Promise.reject(new Error('必填项'));
          }
          const successNode = getFieldValue(['taskParams', 'conditionResult', 'successNode']);
          if (isEqual(value, successNode)) {
            return Promise.reject(new Error('失败状态和成功状态不能相同'));
          }
          return Promise.resolve();
        },
      }),
    ],
  };

  return (
    <>
      <Row gutter={[8, 8]}>
        <Col span={12}>
          <Form.Item label='成功状态' name={['taskParams', 'conditionResult', 'successNode']} rules={rules.successNode}>
            <ConditionNodeSelect options={conditionTasks} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label='失败状态' name={['taskParams', 'conditionResult', 'failedNode']} rules={rules.failedNode}>
            <ConditionNodeSelect options={conditionTasks} />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label='添加前置检查条件' name={['taskParams', 'dependence']}>
        <DependenceCondition options={preTasks ?? []} />
      </Form.Item>
    </>
  );
};

export default Conditions;
