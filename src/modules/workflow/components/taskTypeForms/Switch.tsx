import { useMemo } from 'react';
import type { Graph, Node } from '@antv/x6';
import { Form, FormInstance, Select } from 'antd';

import { SwitchDependTaskList } from '../form/SwitchDependTaskList';

const Switch = ({ form, graph, selectedNode }: { form: FormInstance; graph: Graph; selectedNode: Node }) => {
  const conditionTasks = useMemo<DefaultOptionType[]>(() => {
    // 获取所有后置节点
    const nextNodes = graph
      .getConnectedEdges(selectedNode, {
        outgoing: true,
      })
      .map(edge => edge.getTargetCell());
    return (
      nextNodes
        ?.filter(node => node)
        ?.map(node => {
          const { code, name } = node!.getData();
          return {
            label: name,
            value: code,
            key: code,
          };
        }) ?? []
    );
  }, [graph, selectedNode]);
  return (
    <>
      <Form.Item label='条件分支流转'>
        <SwitchDependTaskList name={['taskParams', 'switchResult', 'dependTaskList']} options={conditionTasks} />
      </Form.Item>
      <Form.Item label='默认流转分支' name={['taskParams', 'switchResult', 'nextNode']}>
        <Select placeholder='请选择' options={conditionTasks} />
      </Form.Item>
    </>
  );
};

export default Switch;
