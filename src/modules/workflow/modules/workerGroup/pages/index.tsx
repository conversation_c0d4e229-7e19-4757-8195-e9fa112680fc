import { useEffect, useRef, useState } from 'react';
import { Button, Col, message, Row, Space, Tag, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useSearchParams } from 'umi';

import { CustomTable, SearchInput, useCustomTableHook } from '@/components';
import { useDeleteConfirm, useGlobalHook, useRightsHook } from '@/hooks';
import { WorkerGroupMangementEntry } from '@/modules/workflow/models';
import { WorkerGroupApi } from '@/modules/workflow/services/WorkerGroupApi';

import { WorkerGroupForm } from '../components/WorkerGroupForm';

const MASTER_STATUS = {
  NOT_CONFIG: {
    label: '未配置',
    className: 'bg-gray',
  },
  NORMAL: {
    label: '健康',
    className: 'bg-success',
  },
  ABNORMAL: {
    label: '离线',
    className: 'bg-warning',
  },
};

export const WorkerGroup = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const { hasRights } = useRightsHook();
  const [searchParams] = useSearchParams();
  const name = searchParams.get('name');
  const { queryParams, pagination, handleTableChange, setTotal, setFilter } = useCustomTableHook({
    cacheId: name ? undefined : 'workerGroupManagement',
    sort: {
      updateTime: 'DESC',
    },
    ...name && { filter: { name } },
  });
  const [list, setList] = useState<WorkerGroupMangementEntry[]>([]);
  const [editItem, setEditItem] = useState<WorkerGroupMangementEntry | undefined>();
  const [loading, setLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [status, setStatus] = useState<'create' | 'edit' | 'view'>('create');
  const debouncedRef = useRef<any>(null);
  const [masterStatus, setMasterStatus] = useState<'NOT_CONFIG' | 'NORMAL' | 'ABNORMAL'>('');
  const [masterStatusNodes, setMasterStatusNodes] = useState([]);

  const confirmDelete = useDeleteConfirm({
    prefix: '/api/v2/workflow',
    delUrl: '/worker-group',
  });

  const onCancel = () => {
    setOpen(false);
    setStatus('create');
    setEditItem(undefined);
  };

  const columns: ColumnsType<WorkerGroupMangementEntry> = [
    {
      title: '执行节点名称',
      dataIndex: 'name',
    },
    {
      title: 'Worker地址',
      width: 300,
      dataIndex: 'addrList',
      render: text => (
        <Row gutter={[8, 8]}>
          {text?.map(item => (
            <Col key={item}>
              <Tag key={item} color='green'>
                {item}
              </Tag>
            </Col>
          ))}
        </Row>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '创建时间',
      width: 150,
      dataIndex: 'createTime',
    },
    {
      title: '创建人',
      width: 140,
      dataIndex: 'createUserName',
    },
    {
      title: '更新时间',
      width: 150,
      dataIndex: 'createTime',
    },
    {
      title: '更新人',
      width: 140,
      dataIndex: 'updateUserName',
    },
    {
      title: '操作',
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button type='link' size='small' className='p-0' onClick={() => onDetail(record)}>
            查看
          </Button>
          {!record.systemDefault && (
            <>
              <Button
                type='link'
                size='small'
                className='p-0'
                onClick={() => onEdit(record)}
                disabled={!hasRights('ds_workgroup:write', record?.projectAuth)}
              >
                编辑
              </Button>
              <Button
                type='link'
                size='small'
                className='p-0'
                onClick={() => onDelete(record?.id, record?.name)}
                disabled={!hasRights('ds_workgroup:write', record?.projectAuth)}
              >
                删除
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  const onDelete = async (id: string, name: string) => {
    try {
      await confirmDelete(id ?? '', `确定删除执行节点「${name}」吗`);
      fetchData();
      message.success('删除成功！');
    } catch (error: any) {
      message.error(error.msg ?? '删除失败');
    }
  };

  const onDetail = (record: WorkerGroupMangementEntry) => {
    setEditItem(record);
    setStatus('view');
    setOpen(true);
  };

  const onEdit = (record: WorkerGroupMangementEntry) => {
    setEditItem(record);
    setStatus('edit');
    setOpen(true);
  };

  const onCreate = () => {
    setStatus('create');
    setOpen(true);
  };

  useEffect(() => {
    setPageInfo({
      title: '执行节点',
      description: true,
    });
    getMasterStatus();
    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    if (debouncedRef.current) clearTimeout(debouncedRef.current);
    debouncedRef.current = setTimeout(() => {
      fetchData();
    }, 300);
  }, [queryParams]);

  const fetchData = async () => {
    setLoading(true);
    const res = await WorkerGroupApi.getList(queryParams).finally(() => {
      setLoading(false);
    });
    if (res?.code === '0000') {
      setList(res?.data ?? []);
      setTotal(res?.total ?? 0);
    }
  };

  const getMasterStatus = async () => {
    const { data } = await WorkerGroupApi.getMasterStatus();
    setMasterStatus(data.status);
    if (data.status === 'NORMAL' && data.nodes?.length) {
      setMasterStatusNodes(data.nodes);
    }
  };

  const handleRefresh = () => {
    fetchData();
    getMasterStatus();
  };

  return (
    <div className='h-full w-full bg-white overflow-hidden flex flex-col'>
      <div className='px-4 py-3 flex justify-between'>
        <Row gutter={[8, 8]} className='flex-1'>
          <Col>
            <Button type='primary' onClick={onCreate} disabled={!hasRights('ds_workgroup:write')}>
              创建执行节点
            </Button>
          </Col>
          <Col>
            <SearchInput
              placeholder='请输入关键词'
              className='w-[224px]'
              defaultValue={queryParams?.filter?.name}
              onSearch={value => {
                const temp = queryParams.filter;
                if (temp) {
                  temp.name = value;
                  setFilter(temp);
                }
              }}
              allowClear
            />
          </Col>
        </Row>
        <div className='flex items-center'>
          <span className='flex items-center mr-2'>
            调度器状态：
            <span className={`w-[8px] h-[8px] rounded-full ${MASTER_STATUS?.[masterStatus]?.className}`}></span>
            {
              masterStatus === 'NORMAL' ?
                <Tooltip title={
                  <div>
                    <div>节点列表：</div>
                    {
                      masterStatusNodes.map((item, index) =>  <div key={index}>{item}</div>)
                    }
                  </div>
                }
                >
                  <span className='ml-1.5'>{MASTER_STATUS?.[masterStatus]?.label}</span>
                </Tooltip>
                :<span className='ml-1.5'>{MASTER_STATUS?.[masterStatus]?.label}</span>
            }
            
          </span>
          <Button type='text' onClick={handleRefresh}>
            <i className='iconfont icon-refresh-line'></i>
          </Button>
        </div>
      </div>

      <div className='flex-1 overflow-hidden'>
        <CustomTable
          dataSource={list}
          loading={loading}
          columns={columns}
          onChange={handleTableChange}
          pagination={pagination}
          scroll={{ x: 1400 }}
        />
      </div>

      {open && (
        <WorkerGroupForm
          status={status}
          open={open}
          onCallback={() => fetchData()}
          onCancel={onCancel}
          record={editItem}
        />
      )}
    </div>
  );
};
