import { useRef, useState } from 'react';
import { useActivate, useUnactivate } from 'react-activation';
import { Graph, Model, Node } from '@antv/x6';
import { Dnd } from '@antv/x6/lib/addon';

import { ProcessDefinition, TaskDefinitionItem, TaskLocation, TaskRelationItem } from '@/modules/workflow/models';

import { getDnd } from '../config/config-dnd';
import { initGraph } from '../config/x6-config';

/**
 * 获取不同数据量下的动画档位
 *
 * @param dataNum 数据量
 */
export const getAnimationGears = function (dataNum) {
  const maxNum = 100;
  if (dataNum === 0) return 0;
  if (dataNum <= Math.ceil(maxNum * 0.33)) return 1;
  if (dataNum <= Math.ceil(maxNum * 0.66)) return 2;
  return 3;
};

type GraphToPipelineData = ({
  graph,
  pipelineData,
}: {
  graph: Graph;
  pipelineData: ProcessDefinition;
}) => ProcessDefinition;

/**
 * @description 根据画布内容生成pipelineData
 * @params args: { clearDebugTag: boolean } 是否保留jobOpts中enableDebug标记。只有在用户调试时需要保持，其他清空下清除。
 */
export const graphToPipelineData: GraphToPipelineData = ({ graph, pipelineData }) => {
  const { cells } = graph?.toJSON();
  // todo 类型定义
  const taskDefinitionList: TaskDefinitionItem[] = [];
  const taskRelationList: TaskRelationItem[] = [];
  const locations: TaskLocation[] = [];

  for (let i = 0, len = cells.length; i < len; i += 1) {
    const { shape, data, position } = cells[i];
    // 算子
    if (shape === 'task-node') {
      delete data.docUrl;
      delete data.icon;
      delete data.label;
      taskDefinitionList.push({
        ...data,
      });
      locations.push({
        taskCode: data.code,
        ...position,
      });
    }
  }

  for (let i = 0, len = cells.length; i < len; i += 1) {
    const { shape, data } = cells[i];
    if (shape === 'task-edge') {
      if (data) {
        taskRelationList.push({
          ...data,
        });
      } else {
        const { source, target } = cells[i];
        const preTaskCode = cells.find(cell => cell.id === source.cell)?.data.code;
        const postTaskCode = cells.find(cell => cell.id === target.cell)?.data.code;
        const preTaskVersion = taskDefinitionList.find(x => x.code === preTaskCode)?.version ?? 0;
        const postTaskVersion = taskDefinitionList.find(x => x.code === postTaskCode)?.version ?? 0;
        taskRelationList.push({
          preTaskCode,
          preTaskVersion,
          postTaskCode,
          postTaskVersion,
        });
      }
    }
  }

  console.log('---------------', cells, {
    ...pipelineData,
    taskDefinitionList,
    taskRelationList,
    locations,
  });

  return {
    ...pipelineData,
    taskDefinitionList,
    taskRelationList,
    locations,
  };
};

export const useGraphHook = () => {
  const [graph, setGraph] = useState<Graph>();
  const [dnd, setDnd] = useState<Dnd>();
  const [zoom, setZoom] = useState(1);
  const [selectedNode, setSelectedNode] = useState<Node | undefined>();
  const [selectedItems, setSelectedItems] = useState<Node[]>();
  const isActive = useRef<boolean>(true);

  useActivate(() => {
    isActive.current = true;
  });

  useUnactivate(() => {
    isActive.current = false;
  });

  const initEvents = (graph: Graph) => {
    // 不需要操作pipelineData的事件
    // 连线选中时，高亮显示
    graph.on('edge:selected', ({ edge }) => {
      if (!isActive.current) return;
      if (graph.isFrozen()) return;
      graph.disableHistory();
      edge.attr({
        line: {
          stroke: 'rgb(39, 181, 186)',
        },
      });
      graph.enableHistory();
    });

    // 连线取消选中时，恢复原来颜色
    graph.on('edge:unselected', ({ edge }) => {
      if (!isActive.current) return;
      if (graph.isFrozen()) return;
      graph.disableHistory();
      edge.attr({
        line: {
          stroke: '#bbb',
        },
      });
      graph.enableHistory();
    });

    // 进入调试
    graph.on('debug:enter', () => {});

    // 退出调试
    graph.on('debug:outer', () => {});

    // 节点点击事件
    graph.on('node:click', ({ e, node }) => {
      if (e.target.nodeName === 'circle') {
        setSelectedNode(undefined);
        return;
      }
      if (!isActive.current) return;
      // 当选中的算子个数只有一个时，显示算子参数配置面板，否则隐藏面板
      if (selectedNode?.id === node.id) {
        setSelectedNode(undefined);
      } else {
        setSelectedNode(node);
      }
    });

    // 连线变化时，关闭算子配置面板
    graph.on('edge:changed', () => {
      setSelectedNode(undefined);
    });

    // 添加连线时, 重置edge source
    graph.on('edge:connected', ({ isNew, edge }) => {
      if (!isActive.current) return;
      if (graph.isFrozen()) return;
      graph.disableHistory();
      if (isNew) {
        const sourceNode = edge.getSourceNode() as Node;
        edge.setSource(sourceNode);
      }
      graph.enableHistory();
    });

    // 选中的元素集合发生变化时记录选中元素
    graph.on('selection:changed', ({ selected }: { selected: Node[] }) => {
      if (!isActive.current) return;
      setSelectedItems(selected);
    });

    // 当点击空白区域，隐藏算子参数面板
    graph.on('blank:click', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 撤销 隐藏算子参数配置
    graph.history.on('undo', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 重做 隐藏算子参数配置
    graph.history.on('redo', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 响应键盘删除事件，删除选中项，并且清空选中节点
    graph.bindKey('delete', () => {
      if (!isActive.current) return;
      const selectedItems = graph.getSelectedCells();
      if (selectedItems.length > 0) {
        graph.removeCells(selectedItems);
        setSelectedNode(undefined);
      }
    });

    // 自定义事件 清空选中算子，关闭参数配置面板
    graph.on('reset:selectedNode', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 自定义事件 开始调试，关闭左侧参数配置面板
    graph.on('debug:enter', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 当删除节点时，关闭被删除节点的配置层
    graph.on('node:beforeDelete', ({ nodes }) => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });
  };

  const graphRender = (graphModel: Model.FromJSONData) => {
    if (!graph) return;
    setTimeout(() => {
      graph.fromJSON(graphModel);
      graph.centerContent({
        useCellGeometry: true,
      });
    }, 200);
  };

  const graphInit = (container: HTMLDivElement) => {
    const graph = initGraph(container);

    initEvents(graph);
    setDnd(getDnd(graph));

    setGraph(graph);
  };

  return {
    graph,
    dnd,
    selectedNode,
    zoom,
    selectedItems,
    setZoom,
    setGraph,
    graphInit,
    graphRender,
    setSelectedNode,
  };
};
