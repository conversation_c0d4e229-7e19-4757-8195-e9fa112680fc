import { useMemo, useState } from 'react';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { Graph } from '@antv/x6';
import { Button, Divider, message } from 'antd';
import { useParams } from 'umi';

import { TagSelectInToolbar } from '@/components';
import { CodeEditorModal } from '@/components/business/ui/CodeEditor/CodeEditorModal';
import { useRightsHook } from '@/hooks';
import { ProcessDefinition } from '@/modules/workflow/models';
import { ProcessDefinitionApi } from '@/modules/workflow/services/ProcessDefinitionApi';

import { VersionManagement } from '../../VersionManagement';

import { graphToPipelineData } from './PipelineGraph/hooks/useGraphHook';
import { ProcessDefinitionTest } from './ProcessDefinitionTest';
import { RuntimeConfigModal } from './RuntimeConfigModal';
import { ScheduleConfigModal } from './ScheduleConfigModal';

interface Props {
  fullScreen?: boolean;
  pipelineData: ProcessDefinition;
  disabled: boolean;
  getGraph: () => Graph | undefined;
  onChangePipelineData?: (pipelineData: ProcessDefinition) => void;
  onSave: (pipelineData: ProcessDefinition, silent?: boolean) => Promise<any>;
  onPublish: (pipelineData: ProcessDefinition) => Promise<any>;
  onChangeFullScreen?: (fullScreen: boolean) => void;
  setDirty: (dirty: boolean) => void;
}

export const PipelineToolbar = (props: Props) => {
  const { onChangePipelineData, onSave, onPublish, pipelineData, disabled, getGraph, setDirty } = props;
  const { hasRoleRights, hasDataRights } = useRightsHook();
  const [jsonModalVisible, setJsonModalVisible] = useState(false);
  const [scheduleVisible, setScheduleVisible] = useState<boolean>(false);
  const [runtimeConfigVisible, setRuntimeConfigVisible] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testSaving, setTestSaving] = useState(false);
  const [publishing, setPublishing] = useState(false);
  const { id } = useParams();
  const [startTest, setStartTest] = useState(false);

  const pipelineStr = useMemo(() => {
    if (!pipelineData) return '';
    return JSON.stringify(pipelineData, null, 2);
  }, [pipelineData]);

  const onViewJSON = async () => {
    const graph = getGraph();
    if (!graph) return;
    graph.trigger('reset:selectedNode');
    onChange();
    setJsonModalVisible(true);
  };

  const getLatestPipelineData = (changedValue = {}) => {
    const graph = getGraph();
    if (!graph) return;
    return graphToPipelineData({
      graph,
      pipelineData: {
        ...pipelineData,
        ...changedValue,
      },
    });
  };

  const onChange = (changedValue = {}) => {
    const newPipelineData = getLatestPipelineData(changedValue);
    if (!newPipelineData) return;
    onChangePipelineData?.(newPipelineData);
  };

  const handleChangeCode = code => {
    try {
      const changedValue = JSON.parse(code);
      onChange(changedValue);
    } catch (e) {
      message.error('JSON格式不合法');
    }
  };

  const handleSave = async () => {
    const newPipelineData = getLatestPipelineData();
    if (!newPipelineData) return;
    setSaving(true);
    // 保存
    onSave(newPipelineData)
      .then(() => {
        setDirty(false);
      })
      .finally(() => {
        setSaving(false);
      });
  };

  const handlePublish = async () => {
    const newPipelineData = getLatestPipelineData();
    if (!newPipelineData) return;

    onChangePipelineData?.(newPipelineData);
    setPublishing(true);
    // 发布
    onPublish(newPipelineData)
      .then(() => {
        setDirty(false);
      })
      .finally(() => {
        setPublishing(false);
      });
  };

  const toggleFullScreen = () => {
    if (props.fullScreen) {
      props.onChangeFullScreen?.(false);
    } else {
      props.onChangeFullScreen?.(true);
    }
  };

  const onChangeSchedule = schedule => {
    onChange?.({
      schedule,
    });
  };

  const handleChangeRuntimeConfig = (data: ProcessDefinition) => {
    onChangePipelineData?.(data);
  };

  const processDefinitionRunTest = () => {
    const newPipelineData = getLatestPipelineData();
    if (!newPipelineData) return;
    setTestSaving(true);
    // 保存
    onSave(newPipelineData, true)
      .then(() => {
        setStartTest(true);
        setDirty(false);
      })
      .finally(() => {
        setTestSaving(false);
      });
  };

  // 下线
  const handleOffline = async () => {
    if (!pipelineData.id) return;
    await ProcessDefinitionApi.offline(pipelineData.id);
    message.success('下线成功');
    onChange({
      releaseState: 'OFFLINE',
    });
  };

  return (
    <>
      <TagSelectInToolbar
        tagType='OFFLINE_WORKFLOW'
        value={pipelineData.tags ?? []}
        onChange={tags => {
          onChange?.({ tags });
        }}
      />
      <Button type='text' className='px-2' onClick={onViewJSON}>
        <i className='iconfont icon-codemode-line text-sm mr-2'></i>JSON
      </Button>
      <Button
        type='text'
        className='px-2'
        onClick={() => {
          onChange();
          setScheduleVisible(true);
        }}
      >
        <i className='iconfont icon-timer-line text-sm mr-2'></i>定时配置
      </Button>
      <Button
        type='text'
        className='px-2'
        onClick={() => {
          onChange();
          setRuntimeConfigVisible(true);
        }}
      >
        <i className='iconfont icon-timebox-line text-sm mr-2'></i>运行配置
      </Button>
      <Divider type='vertical' className='mx-1' />
      {id && (
        <Button
          type='text'
          className='px-2'
          onClick={processDefinitionRunTest}
          disabled={disabled}
          loading={testSaving}
        >
          <i className='iconfont icon-run-line text-sm mr-2'></i>试跑
        </Button>
      )}
      {pipelineData.releaseState === 'ONLINE' && !pipelineData.processInstanceResp && (
        <Button
          type='text'
          className='px-2'
          onClick={() => handleOffline()}
          loading={testSaving}
          disabled={
            !hasRoleRights('data_develop:deploy') ||
            (pipelineData?.projectAuth && !hasDataRights('workflow:operate', pipelineData?.projectAuth))
          }
        >
          <i className='iconfont icon-arrow_down-line text-xs mr-2'></i>下线
        </Button>
      )}
      {!disabled && (
        <>
          <Button type='text' className='px-2' onClick={handleSave} loading={saving} disabled={disabled}>
            <i className='iconfont icon-save-line text-sm mr-2'></i>保存
          </Button>
          <Divider type='vertical' className='mx-1' />
          <Button
            className='mr-1'
            type='primary'
            onClick={handlePublish}
            loading={publishing}
            disabled={
              !hasRoleRights('data_develop:deploy') ||
              (pipelineData?.projectAuth && !hasDataRights('workflow:operate', pipelineData?.projectAuth))
            }
          >
            <i className='iconfont icon-near_me-fill text-xs mr-2'></i>发布
          </Button>
        </>
      )}

      <Button type='text' className='px-2 mr-1' onClick={() => toggleFullScreen()}>
        {props.fullScreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
      </Button>

      {pipelineData.version > 0 && <VersionManagement pipelineData={pipelineData} onRollback={onChangePipelineData} />}

      {jsonModalVisible && (
        <CodeEditorModal
          title={`${pipelineData?.name}详细信息`}
          open={jsonModalVisible}
          lang='JSON'
          value={pipelineStr}
          readOnly={disabled}
          onCancel={() => {
            setJsonModalVisible(false);
          }}
          onChange={handleChangeCode}
        />
      )}

      {scheduleVisible && (
        <ScheduleConfigModal
          schedule={pipelineData.schedule}
          disabled={disabled}
          open={scheduleVisible}
          onCancel={() => setScheduleVisible(false)}
          onChange={onChangeSchedule}
        />
      )}

      {runtimeConfigVisible && (
        <RuntimeConfigModal
          value={pipelineData}
          disabled={disabled}
          open={runtimeConfigVisible}
          onCancel={() => setRuntimeConfigVisible(false)}
          onChange={handleChangeRuntimeConfig}
        />
      )}

      {startTest && (
        <ProcessDefinitionTest open={startTest} onCancel={() => setStartTest(false)} processDefinition={pipelineData} />
      )}
    </>
  );
};
