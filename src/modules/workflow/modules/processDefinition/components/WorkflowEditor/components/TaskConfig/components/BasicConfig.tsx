import { useEffect } from 'react';
import { Form, Input, Radio } from 'antd';
import { FormProps } from 'antd/lib/form/Form';

import { TaskDefinitionFlagOptions } from '@/modules/workflow/models';

import './BasicConfig.less';

interface Props extends FormProps {
  values: any;
  onChange?: (values: any) => void;
}
export const BasicConfig = ({ values, onChange, ...otherProps }: Props) => {
  const [form] = Form.useForm();

  const onValuesChange = (changedValues, values) => {
    onChange?.(values);
  };

  useEffect(() => {
    form.setFieldsValue(values);
  }, [values]);

  return (
    <div>
      <Form form={form} layout='vertical' onValuesChange={onValuesChange} {...otherProps}>
        <Form.Item label='节点名称' name='name' rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item label='节点描述' name='description'>
          <Input.TextArea rows={10} />
        </Form.Item>
        <Form.Item label='运行标志' name='flag'>
          <Radio.Group options={TaskDefinitionFlagOptions}></Radio.Group>
        </Form.Item>
      </Form>
    </div>
  );
};
