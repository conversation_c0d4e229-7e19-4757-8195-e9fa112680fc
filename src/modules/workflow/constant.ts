export enum TaskDefinitionFlag {
  YES = '正常',
  NO = '禁止执行',
}

export enum ProcessInstancePriority {
  HIGHEST = '超高',
  HIGH = '高',
  MEDIUM = '中',
  LOW = '底',
  LOWEST = '超低',
}

export enum ProcessInstanceStateTypeEnum {
  SUBMITTED_SUCCESS = '提交成功',
  RUNNING_EXECUTION = '运行中',
  READY_PAUSE = '准备暂停',
  PAUSE = '已暂停',
  READY_STOP = '准备停止',
  STOP = '已停止',
  FAILURE = '失败',
  SUCCESS = '成功',
  DELAY_EXECUTION = '延迟执行',
  SERIAL_WAIT = '串行等待',
  READY_BLOCK = '准备阻塞',
  BLOCK = '阻塞中',
}

export enum ProcessInstanceCommandTypeEnum {
  START_PROCESS = '试跑',
  COMPLEMENT_DATA = '补数',
  SCHEDULER = '调度执行',
  REPEAT_RUNNING = '重跑',
  START_FAILURE_TASK_PROCESS = '重跑失败任务',
  STOP = '停止',
  RECOVER_TOLERANCE_FAULT_PROCESS = '容错恢复',
  START_CURRENT_TASK_PROCESS = '执行当前任务',
  RECOVER_SUSPENDED_PROCESS = '恢复挂起的进程',
  PAUSE = '暂停',
  RECOVER_WAITING_THREAD = '恢复等待线程',
  RECOVER_SERIAL_WAIT = '恢复串行等待',
}

export enum TaskInstanceStateEnum {
  SUBMITTED_SUCCESS = '提交成功',
  RUNNING_EXECUTION = '运行中',
  PAUSE = '暂停',
  STOP = '停止',
  FAILURE = '失败',
  SUCCESS = '成功',
  NEED_FAULT_TOLERANCE = '需要容错',
  KILL = '杀死',
  DELAY_EXECUTION = '延迟执行',
  FORCED_SUCCESS = '强制成功',
  DISPATCH = '调度中',
}

export const TaskInstanceStateMap = {
  SUBMITTED_SUCCESS: {
    label: '等待执行',
    icon: 'iconfont icon-load-fill text-warning',
  },
  DELAY_EXECUTION: {
    label: '等待执行',
    icon: 'iconfont icon-load-fill text-warning',
  },

  RUNNING_EXECUTION: {
    label: '正在执行',
    icon: 'iconfont icon-timer-line text-primary',
  },
  DISPATCH: {
    label: '正在执行',
    icon: 'iconfont icon-timer-line text-primary',
  },

  SUCCESS: {
    label: '成功',
    icon: 'iconfont icon-check-circle-line text-success',
  },
  FORCED_SUCCESS: {
    label: '成功',
    icon: 'iconfont icon-check-circle-line text-success',
  },

  FAILURE: {
    label: '失败',
    icon: 'iconfont icon-error-line text-danger',
  },
  NEED_FAULT_TOLERANCE: {
    label: '失败',
    icon: 'iconfont icon-error-line text-danger',
  },

  PAUSE: {
    label: '停止',
    icon: 'iconfont icon-block-line text-gray',
  },
  STOP: {
    label: '停止',
    icon: 'iconfont icon-block-line text-gray',
  },
  KILL: {
    label: '停止',
    icon: 'iconfont icon-block-line text-gray',
  },
};

export enum ReleaseState {
  ONLINE = '已上线',
  OFFLINE = '下线',
}

export enum TaskParamDirect {
  IN = '输入',
  OUT = '输出',
}

export enum FailureStrategyEnum {
  CONTINUE = '继续',
  END = '结束',
}

export enum ExecutionTypeEnum {
  PARALLEL = '并行',
  SERIAL_WAIT = '串行',
}

export const CYCLE_LIST = [
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'week',
    label: '周',
  },
  {
    value: 'day',
    label: '天',
  },
  {
    value: 'hour',
    label: '小时',
  },
];

export const DATE_LIST = {
  hour: [
    {
      value: 'currentHour',
      label: '当前小时',
    },
    {
      value: 'last1Hour',
      label: '前1小时',
    },
    {
      value: 'last2Hours',
      label: '前2小时',
    },
    {
      value: 'last3Hours',
      label: '前3小时',
    },
    {
      value: 'last24Hours',
      label: '前24小时',
    },
  ],
  day: [
    {
      value: 'today',
      label: '今天',
    },
    {
      value: 'last1Days',
      label: '昨天',
    },
    {
      value: 'last2Days',
      label: '前两天',
    },
    {
      value: 'last3Days',
      label: '前三天',
    },
    {
      value: 'last7Days',
      label: '前七天',
    },
  ],
  week: [
    {
      value: 'thisWeek',
      label: '本周',
    },
    {
      value: 'lastWeek',
      label: '上周',
    },
    {
      value: 'lastMonday',
      label: '上周一',
    },
    {
      value: 'lastTuesday',
      label: '上周二',
    },
    {
      value: 'lastWednesday',
      label: '上周三',
    },
    {
      value: 'lastThursday',
      label: '上周四',
    },
    {
      value: 'lastFriday',
      label: '上周五',
    },
    {
      value: 'lastSaturday',
      label: '上周六',
    },
    {
      value: 'lastSunday',
      label: '上周日',
    },
  ],
  month: [
    {
      value: 'thisMonth',
      label: '本月',
    },
    {
      value: 'thisMonthBegin',
      label: '本月初',
    },
    {
      value: 'lastMonth',
      label: '上月',
    },
    {
      value: 'lastMonthBegin',
      label: '上月初',
    },
    {
      value: 'lastMonthEnd',
      label: '上月末',
    },
  ],
};
