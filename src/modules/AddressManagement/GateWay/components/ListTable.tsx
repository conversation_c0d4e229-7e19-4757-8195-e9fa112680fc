import { Button, Tag } from 'antd';

import { Status } from '@/components';
import { CustomTable, CustomTableProps } from '@/components/business/CustomTable';
import { useSearchValue } from '@/stores/toolbar';

import { getCurrentMode } from '../constants/options';

interface ListTableProps<Record> extends Omit<CustomTableProps, 'columns'> {
  children?: ReactNode;
  dataSource: Record[];
  onEdit: GATEWAY.onEdit;
  onRouterEdit: GATEWAY.onRouterEdit;
  onDetail: GATEWAY.onDetail;
  onDelete: GATEWAY.onDelete;
  onCheck: GATEWAY.onConnect;
}

export function ListTable<T extends GATEWAY.ListItem>({
  children,
  dataSource,
  onDelete,
  onDetail,
  onEdit,
  onRouterEdit,
  onCheck,
  ...tableProps
}: ListTableProps<T>) {
  const { page, setPage, size, total } = useSearchValue();
  const columns: Array<ColumnType<T>> = [
    {
      title: '网关名',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => <Button type="link" size='small' onClick={handleDetail(record)}>{text}</Button>,
    },
    {
      title: '网关类型',
      dataIndex: 'type',
      render: text => <span>{text === 'ROUTER' ? '接入网关' : '采集网关'}</span>,
    },
    {
      title: '接入模式',
      dataIndex: 'connectMode',
      key: 'connectMode',
      renderText: value => {
        const mode = getCurrentMode(value);
        return mode?.label ?? '';
      },
    },
    {
      title: '所属中心',
      dataIndex: 'dataCenterList',
      render: dataCenterList => {
        return (
          <div className='flex-wrap flex'>
            {dataCenterList?.map(d => (
              <Tag className='mb-1 mr-1' key={d?.id}>
                {d?.name}
              </Tag>
            ))}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (status, record) => {
        return (
          <Status
            detail={record.connectDetail as GATEWAY.ConnectDetail}
            status={status}
            onCheck={handleCheck(record)}
          />
        );
      },
    },
    {
      title: '最近修改时间',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      dataIndex: 'operator',
      width: 170,
      render: (_: any, record) => (
        <>
          {record.type !== 'ROUTER' && (
            <>
              {record.status !== 'ABNORMAL' ? (
                <a className='mr-[6px]' rel='noreferrer' target='_blank' href={record.cellManagementUrl}>
                  采集器管理
                </a>
              ) : (
                <span className='mr-[6px] text-gray-5 cursor-not-allowed'>采集器管理</span>
              )}
            </>
          )}
          <a className='mr-[6px]' onClick={handleEdit(record)}>
            编辑
          </a>
          <a onClick={handleDelete(record)}>删除</a>
        </>
      ),
    },
  ];

  const handleCheck = (record: T) => async () => {
    return onCheck(record);
  };

  const handleEdit = (record: T) => () => {
    onEdit(record);
  };

  const handleDetail = (record: T) => () => {
    onDetail(record);
  };

  const handleDelete = (record: T) => () => {
    onDelete(record);
  };

  const handlePageChange = (page: number, pageSize: number) => {
    setPage(page - 1, pageSize);
  };

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      {children}
      <CustomTable
        dataSource={dataSource}
        pagination={{
          className: 'pagination-total-left pr-2',
          total,
          pageSize: size,
          current: page + 1,
          showSizeChanger: true,
          onChange: handlePageChange,
        }}
        columns={columns}
        scroll={{ x: undefined }}
        {...tableProps}
      ></CustomTable>
    </div>
  );
}
