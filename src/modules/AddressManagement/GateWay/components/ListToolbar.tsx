// 列表页操作栏
import { Button } from 'antd';

interface ListToolbarProps {
  onCreate: GATEWAY.onCreate;
  onRouterCreate: GATEWAY.onRouterCreate;
}

export const ListToolbar: React.FC<ListToolbarProps> = ({ onCreate, onRouterCreate }) => {
  return (
    <div className='flex items-center px-4 py-3'>
      <Button type='primary' onClick={onCreate}>
        注册采集网关
      </Button>
      <Button type='primary' className='ml-2' onClick={onRouterCreate}>
        注册接入网关
      </Button>
    </div>
  );
};
