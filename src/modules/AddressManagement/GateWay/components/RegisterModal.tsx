import { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, ModalProps, Select } from 'antd';

import { LinkTest } from '@/components';
import { DataCenterSelect } from '@/components/business/form/select/DataCenterSelect';
import EditList from '@/components/form/EditList';
import { nameRules, requiredRule } from '@/constants/rules';
import { DataSourceApi, GateWayApi, RegisterCenterApi } from '@/services';

import ModeRadio from './forms/ModeRadio';

const FormItem = Form.Item;

const FormCol = {
  wrapperCol: {
    span: 14,
  },
  labelCol: {
    span: 6,
  },
};

interface RegisterModalProps extends Omit<ModalProps, 'onOk'> {
  initialValue?: GATEWAY.Entity;
  onOk: (value: GATEWAY.Entity) => Promise<any>;
  isDetail?: boolean;
}

const isDisabled = (value: Omit<GATEWAY.Entity, 'address'> & { address: Array<{ value: string }> }) => {
  const { connectMode, address, registerCenterId } = value;

  if (connectMode === 'MANUAL') {
    if (!address) return true;
    return (address ?? [])?.filter(d => d && d?.value).length === 0;
  } else if (connectMode === 'NACOS_3RD') {
    return !registerCenterId;
  }
};

export const RegisterModal: React.FC<RegisterModalProps> = ({
  open,
  initialValue,
  onCancel,
  onOk,
  isDetail,
  ...modalProps
}) => {
  const [form] = Form.useForm();
  const mode = Form.useWatch('connectMode', form);
  const [loading, setLoading] = useState(false);
  const [dsOptions, setDSOptions] = useState<DefaultOptionType[]>([]);
  const [nacosOptions, setNacosOptions] = useState<DefaultOptionType[]>([]);
  const [statusDetail, setStatusDetail] = useState<GATEWAY.CheckRes>({
    ...(initialValue?.connectDetail as GATEWAY.ConnectDetail),
    status: initialValue?.status ?? 'ABNORMAL',
  });

  const isEdit = !!initialValue;

  useEffect(() => {
    if (isEdit) {
      const address = initialValue.address ? initialValue.address.split(',').map(d => ({ value: d })) : [{ value: '' }];
      form.setFieldsValue({
        ...initialValue,
        groupName: initialValue.groupName ?? 'DEFAULT_GROUP',
        address,
      });
      setStatusDetail({
        ...(initialValue?.connectDetail as GATEWAY.ConnectDetail),
        status: initialValue?.status ?? 'ABNORMAL',
      });
    } else {
      form.resetFields();
    }
  }, [initialValue]);

  useEffect(() => {
    DataSourceApi.getAll({
      filter: {
        platform: 'KAFKA',
      },
    }).then(({ data }) => {
      setDSOptions(data.map(d => ({ label: d.name, value: d.id })));
    });
    RegisterCenterApi.getAll({
      filter: {
        type: 'NACOS',
      },
    }).then(({ data }) => {
      setNacosOptions(data.map(d => ({ label: d.name, value: d.id })));
    });
  }, []);

  const handleCheck: GATEWAY.onCheck = async () => {
    try {
      const { address, registerCenterId, groupName } = await form.getFieldsValue();
      let paramMap = {};

      if (mode === 'MANUAL') {
        paramMap = {
          address: address
            .filter((d: any) => d)
            .map((d: any) => d.value)
            .join(','),
        };
      } else if (mode === 'NACOS_3RD') {
        paramMap = {
          registerCenterId,
          groupName,
        };
      }

      const { data, code } = await GateWayApi.check({
        connectMode: mode,
        platform: 'CELL',
        paramMap,
      });

      if (code === '0000') {
        setStatusDetail(data);
      } else {
        setStatusDetail({
          status: data.status,
        });
      }

      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  };

  const handleCancel = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    form.resetFields();
    setLoading(false);
    setStatusDetail({
      status: 'ABNORMAL',
    });
    onCancel?.(e);
  };

  const handleOk = async (e: any) => {
    try {
      const value: GATEWAY.Entity & { address: Array<Record<string, any>> } = await form.validateFields();
      setLoading(true);
      const res = await onOk({
        ...value,
        status: statusDetail.status,
        connectDetail: statusDetail,
        address: value.address
          ?.filter(d => d)
          .map(d => d.value)
          .join(','),
      });
      handleCancel(e);
      return res;
    } catch (error) {
      setLoading(false);
      if (typeof error === 'string') {
        message.error(error);
      } else if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('保存失败');
      }
      return Promise.reject(error);
    }
  };

  const renderNacos3rd = () => {
    return (
      <div className='bg-[rgba(216,216,216,0.15)] py-1 mb-1'>
        <FormItem
          label='Nacos注册中心'
          name='registerCenterId'
          rules={[requiredRule('请选择Nacos注册中心')]}
          className='mb-2'
        >
          <Select placeholder='请选择' options={nacosOptions} disabled={isDetail} />
        </FormItem>
        <FormItem
          label='分组'
          name='groupName'
          initialValue='DEFAULT_GROUP'
          rules={[requiredRule('请输入分组名')]}
          className='mb-2'
        >
          <Input placeholder='请输入' disabled={isDetail} />
        </FormItem>
        <FormItem label=' ' colon={false} shouldUpdate className='mb-0'>
          {() => <LinkTest onCheck={handleCheck} disabled={isDisabled(form.getFieldsValue()) || isDetail} />}
        </FormItem>
      </div>
    );
  };
  const renderManual = () => {
    return (
      <div className='bg-[rgba(216,216,216,0.15)] py-1 mb-1'>
        <FormItem label='网关地址' required className='mb-0'>
          <EditList
            name='address'
            isDetail={isDetail}
            columns={[
              {
                name: 'value',
                placeholder: '请输入',
                rules: [requiredRule('请输入网关地址')],
              },
            ]}
          />
        </FormItem>
        <FormItem label=' ' colon={false} shouldUpdate className='mb-1'>
          {() => <LinkTest onCheck={handleCheck} disabled={isDisabled(form.getFieldsValue()) || isDetail} />}
        </FormItem>
      </div>
    );
  };

  const renderChildren = () => {
    return mode === 'NACOS_3RD' ? renderNacos3rd() : mode === 'MANUAL' ? renderManual() : null;
  };

  const renderFooter = () => {
    return (
      <div className='flex justify-end items-center'>
        {mode === 'NACOS_AUTO' && <LinkTest onCheck={handleCheck} disabled={isDetail} />}
        <Button onClick={handleCancel} className='mx-2'>
          取消
        </Button>
        <Button loading={loading} type='primary' onClick={handleOk} disabled={isDetail}>
          确定
        </Button>
      </div>
    );
  };

  return (
    <Modal
      width={760}
      title={`${isEdit ? '编辑' : '注册'}采集网关`}
      styles={{ body: { padding: '24px 0' } }}
      open={open}
      onCancel={handleCancel}
      confirmLoading={loading}
      {...modalProps}
      footer={renderFooter()}
      destroyOnClose
    >
      <Form form={form} {...FormCol}>
        <FormItem
          name='connectMode'
          className='mb-2'
          label='接入模式'
          initialValue='NACOS_AUTO'
          rules={[{ required: true, message: '请选择接入模式' }]}
          wrapperCol={{ span: 16 }}
        >
          <ModeRadio className='mt-[6px]' disabled={isDetail} />
        </FormItem>

        {renderChildren()}

        <FormItem
          label='采集网关名称'
          name='name'
          rules={[...nameRules, requiredRule('请输入采集网关名称')]}
          className='mb-2'
        >
          <Input placeholder='请输入' disabled={isDetail} />
        </FormItem>
        <FormItem label='所属中心' name='dataCenterIds' className='mb-2' wrapperCol={{ span: 16 }}>
          <DataCenterSelect disabled={isDetail} />
        </FormItem>
        <FormItem name='dsId' label='关联kafka数据源' className='mb-2' rules={[requiredRule('请选择kafka数据源')]}>
          <Select allowClear options={dsOptions} disabled={isDetail} placeholder='选择Router组件配置的kafka集群地址' />
        </FormItem>
        <FormItem label='描述' name='description' wrapperCol={{ span: 16 }}>
          <Input.TextArea placeholder='请输入' autoSize={{ minRows: 3 }} disabled={isDetail} />
        </FormItem>
      </Form>
    </Modal>
  );
};
