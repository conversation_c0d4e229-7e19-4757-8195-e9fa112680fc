import { useEffect, useState } from 'react';
import { Button, Descriptions, message } from 'antd';
import { useParams } from 'umi';

import { DataCenterDetail } from '@/components/business/ui/DataCenterDetail';
import { useGlobalHook } from '@/hooks';
import { DataSourceApi, GateWayApi, RegisterCenterApi } from '@/services';
import { jsonStringify } from '@/utils';

import { RegisterModal } from '../components/RegisterModal';
import { RouterRegisterModal } from '../components/RouterRegisterModal ';
import { modeOptions } from '../constants/options';

const labelStyle = { width: 125, textAlign: 'right', display: 'inline-block' };

export const Detail = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const [detail, setDetail] = useState<GATEWAY.Entity>({});
  const [routerOpen, setRouterOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const { id } = useParams();
  const [dsOptions, setDSOptions] = useState<DefaultOptionType[]>([]);
  const [nacosOptions, setNacosOptions] = useState<DefaultOptionType[]>([]);
  const [editDetail, setEditDetail] = useState<GATEWAY.Entity>();

  useEffect(() => {
    if(!id) return;
    getDetail();
    getDS();
    getCenter();
    return () => {
      resetPageInfo();
    };
  }, [id]);

  const getDetail = async () => {
    const { data } = await GateWayApi.getDetail(id);
    setDetail({
      ...data,
      dataCenterIds: (data?.dataCenterList ?? []).map(d => d.id),
    });
    setPageInfo({ title: `查看：${data.name}` });
  };

  const getDS = async () => {
    const { data } = await DataSourceApi.getAll({
      filter: {
        platform: 'KAFKA',
      },
    });
    setDSOptions(data.map(d => ({ label: d.name, value: d.id })));
  };

  const getCenter = async () => {
    const { data } = await RegisterCenterApi.getAll({
      filter: {
        type: 'NACOS',
      },
    });
    setNacosOptions(data.map(d => ({ label: d.name, value: d.id })));
  };

  const items = detail?.type === 'CELL' ? [
    {
      label: '接入模式',
      children: modeOptions?.find(item => item.value === detail?.connectMode)?.label,
    },
    ...detail?.connectMode === 'NACOS_3RD' ? [
      {
        label: 'Nacos注册中心',
        children: nacosOptions?.find(item => item.value === detail?.registerCenterId)?.label,
      },
      {
        label: '分组',
        children: detail?.groupName,
      },
    ] : detail?.connectMode === 'MANUAL' ? [
      {
        label: '网关地址',
        children: detail?.address,
      },
    ] : [],
    {
      label: '采集网关名称',
      children: detail?.name,
    },
    {
      label: '所属中心',
      children: <DataCenterDetail value={detail?.dataCenterIds}></DataCenterDetail>,
    },
    {
      label: '关联kafka数据源',
      children: dsOptions?.find(item => item.value === detail?.dsId)?.label,
    },
    {
      label: '描述',
      children: detail?.description,
    },
  ] : [
    {
      label: '网关地址',
      children: detail?.address,
    },
    {
      label: '采集网关名称',
      children: detail?.name,
    },
    {
      label: '所属中心',
      children: <DataCenterDetail value={detail?.dataCenterIds}></DataCenterDetail>,
    },
    {
      label: '关联kafka数据源',
      children: dsOptions?.find(item => item.value === detail?.dsId)?.label,
    },
    {
      label: '是否是SSL信道',
      children: detail?.isSsl ? '是' : '否',
    },
    {
      label: '描述',
      children: detail?.description,
    },
  ];

  const handleSave = async (value: GATEWAY.Entity, type: 'CELL' | 'ROUTER') => {
    await GateWayApi.update({
      ...detail,
      ...value,
      type,
      connectDetail: jsonStringify({
        ...(detail.connectDetail as GATEWAY.ConnectDetail),
        ...(value.connectDetail as GATEWAY.ConnectDetail),
      }) as GATEWAY.ConnectDetail,
    });
    message.success('保存成功');
    getDetail();
  };

  return <div className='flex flex-col h-full'>
    <Descriptions labelStyle={labelStyle} column={1} items={items} className='flex-1 pt-[20px] pl-[16px]' />
    <div className='flex items-center gap-2 px-4 py-2 border-0 border-t border-[#f0f0f0] border-solid'>
      <Button type='default' onClick={() => {
        setEditDetail({ ...detail });
        if (detail?.type === 'CELL') {
          setOpen(true);
        } else {
          setRouterOpen(true);
        }
      }}
      >
          编辑
      </Button>
    </div>
    <RegisterModal
      open={open}
      onCancel={() => setOpen(false)}
      onOk={value => handleSave(value, 'CELL')}
      initialValue={editDetail}
    />

    <RouterRegisterModal
      open={routerOpen}
      onCancel={() => setRouterOpen(false)}
      onOk={value => handleSave(value, 'ROUTER')}
      initialValue={editDetail}
    />
  </div>;
};
