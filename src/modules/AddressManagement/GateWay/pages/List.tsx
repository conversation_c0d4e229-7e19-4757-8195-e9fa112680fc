import { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import { history,useSearchParams  } from 'umi';

import { GateWayDelConfig } from '@/constants/deleteUrl';
import { useDeleteConfirm } from '@/hooks';
import { GateWayApi } from '@/services';
import { useSearchValue } from '@/stores/toolbar';
import { useGlobalStore } from '@/stores/useGlobalStore';
import { jsonStringify } from '@/utils';

import { ListTable, ListToolbar } from '../components';
import { RegisterModal } from '../components/RegisterModal';
import { RouterRegisterModal } from '../components/RouterRegisterModal ';

export const List = () => {
  const [list, setList] = useState<GATEWAY.ListItem[]>([]);
  const [editRecord, setEditRecord] = useState<GATEWAY.Entity>();
  const [open, setOpen] = useState(false);
  const [routerOpen, setRouterOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isDetail, setDetail] = useState(false);
  const { setPageInfo, resetPageInfo } = useGlobalStore();
  const delConfirm = useDeleteConfirm(GateWayDelConfig);
  const [searchParams] = useSearchParams();
  const { page, size, reset, setTotal, setName, total = 0, setPage } = useSearchValue();
  const id = searchParams.get('id');

  useEffect(() => {
    setName('gatway');
    setPageInfo({
      title: '采集网关管理',
      description:
        '采集网关是一个管理采集代理的组件，负责下发采集任务配置、上送采集数据到指定的帖源层kafka，同时负责采集代理的部署、升级、监控。平台注册采集网关后，即可具备基于代理的数据采集能力。',
    });

    return () => {
      resetPageInfo();
      reset();
    };
  }, []);

  useEffect(() => {
    fetchData();
  }, [page, size, id]);

  const fetchData = async () => {
    setLoading(true);
    const { data, total } = await GateWayApi.getList({
      page,
      size,
      filter: id ? { id } : {},
      sort: {
        updateTime: 'DESC',
      },
    });
    // if (id) {
    //   setList(data.filter(d => d.id === id));
    //   setTotal(1);
    // } else {
    setList(data);
    setTotal(total as number);
    // }
    setLoading(false);
  };

  const handleCheck: GATEWAY.onConnect = async record => {
    try {
      const { data } = await GateWayApi.connect(record.id);

      const newList = list.map(d => {
        if (d.id === record.id) {
          return {
            ...d,
            status: data.status,
            connectDetail: data,
          };
        }
        return d;
      });

      setList(newList);
    } catch (err) {
      return Promise.reject(err);
    }
  };

  const handleSave = async (value: GATEWAY.Entity, type: 'CELL' | 'ROUTER') => {
    value.type = type;
    try {
      if (editRecord) {
        const { code, msg } = await GateWayApi.update({
          ...editRecord,
          ...value,
          connectDetail: jsonStringify({
            ...(editRecord.connectDetail as GATEWAY.ConnectDetail),
            ...(value.connectDetail as GATEWAY.ConnectDetail),
          }) as GATEWAY.ConnectDetail,
        });

        if (code !== '0000') {
          throw new Error(msg);
        } else {
          message.success('保存成功');
        }
      } else {
        const { code, msg } = await GateWayApi.create({
          ...value,
          connectDetail: jsonStringify(value.connectDetail) as GATEWAY.ConnectDetail,
        });

        if (code !== '0000') {
          throw new Error(msg);
        } else {
          message.success('保存成功');
        }
      }
      await fetchData();
    } catch (err) {
      return Promise.reject(err);
    }
  };

  const handleDelete: GATEWAY.onDelete = record => {
    const skip = total % size === 1 && page === Math.floor(total / size);
    delConfirm(record.id, `${GateWayDelConfig.title}「${record?.name}」吗?`).then(() => {
      if (skip) {
        setPage(page - 1);
      } else {
        fetchData();
      }
      fetchData();
    });
  };

  const openEdit: GATEWAY.onEdit = async record => {
    const { data } = await GateWayApi.getDetail(record.id);
    setEditRecord({
      ...data,
      dataCenterIds: (data?.dataCenterList ?? []).map(d => d.id),
    });
    if (record.type === 'ROUTER') {
      setRouterOpen(true);
    } else {
      setOpen(true);
    }
  };

  const openRouterEdit: GATEWAY.onEdit = async record => {
    const { data } = await GateWayApi.getDetail(record.id);
    setEditRecord({
      ...data,
      dataCenterIds: (data?.dataCenterList ?? []).map(d => d.id),
    });
    setRouterOpen(true);
  };

  const openDetail: GATEWAY.onDetail = async record => {
    history.push(`/platform-management/address/gateway/detail/${record.id}`);
  };

  const clear = () => {
    setOpen(false);
    setDetail(false);
    setRouterOpen(false);
    setEditRecord(undefined);
  };

  return (
    <div className='h-full'>
      <ListTable
        dataSource={list}
        onCheck={handleCheck}
        onDelete={handleDelete}
        onEdit={openEdit}
        onRouterEdit={openRouterEdit}
        onDetail={openDetail}
        loading={loading}
      >
        <div className='flex justify-between'>
          <ListToolbar onCreate={() => setOpen(true)} onRouterCreate={() => setRouterOpen(true)} />
          <div className='flex items-center mr-3'>
            <Button type='text' onClick={fetchData}>
              <i className='iconfont icon-refresh-line' />
            </Button>
          </div>
        </div>
      </ListTable>

      <RegisterModal
        open={open}
        isDetail={isDetail}
        onCancel={clear}
        onOk={value => handleSave(value, 'CELL')}
        initialValue={editRecord}
      />

      <RouterRegisterModal
        open={routerOpen}
        isDetail={isDetail}
        onCancel={clear}
        onOk={value => handleSave(value, 'ROUTER')}
        initialValue={editRecord}
      />
    </div>
  );
};
