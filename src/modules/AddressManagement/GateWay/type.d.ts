declare namespace GATEWAY {
  type Status = 'NORMAL' | 'ABNORMAL' | 'PART_NORMAL';
  type StatusText = '正常' | '异常' | '部分异常';
  type ConnectMode = 'NACOS_AUTO' | 'NACOS_3RD' | 'MANUAL';
  type ConnectModeText = '注册中心' | '外部注册中心' | '手工录入';

  type onCreate = () => void;
  type onRouterCreate = () => void;
  type onSearch = (params: SearchParams) => void;
  type onEdit<Record = Entity> = (record: Record) => any;
  type onRouterEdit<Record = Entity> = (record: Record) => any;
  type onDetail<Record = Entity> = (record: Record) => any;
  type onManage<Record = ListItem> = (record: Record) => any;
  type onDelete<Record = ListItem> = (record: Record) => any;
  type onPageChange = (page: number, pageSize: number) => any;
  type onCheck = () => Promise<CheckRes>;
  type onConnect<Record = ListItem> = (record: Record, index?: number) => Promise<any>;

  type CheckParams = Pick<Entity, 'connectMode' | 'paramMap' | 'platform'>;

  interface ModeOption {
    value: ConnectMode;
    label: ConnectModeText;
    desc: string;
  }

  interface SearchParams {
    page?: number;
    pageSize?: number;
  }

  interface DataCenterItem {
    id: string;
    name: string;
  }

  interface ListItem extends CommonRecord {
    code: string;
    status: Status;
    connectMode: ConnectMode;
    connectDetail: ConnectDetail | string;
    lastConnectTime: string;
    description: string;
    registerCenterId: string;
    dsId: string;
    dataCenterList: DataCenterItem[];
    paramMap: Record<string, any>;
    address: string;
    cellManagementUrl: string;
    groupName?: string;
    type: 'CELL' | 'ROUTER';
    isSsl?: boolean;
  }

  interface Entity extends ListItem {
    dataCenterIds: string[];
    platform?: string;
  }

  interface ConnectDetail {
    abnormalUrlMap?: Record<string, string>;
    normalUrls?: string[];
  }

  interface CheckRes extends ConnectDetail {
    status: Status;
    errMsg?: string;
  }
}
