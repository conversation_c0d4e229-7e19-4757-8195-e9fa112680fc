import { forwardRef } from 'react';
import { Descriptions } from 'antd';

import { BaseDetail } from '../BaseDetail';
import { MapDetail } from '../MapDetail';

const DorisDetail = ({ detail, ...reset }) => {
  const items = [
    ...BaseDetail(detail),
    {
      label: 'FE IP地址',
      children: detail?.paramMap?.feAddress,
    },
    {
      label: 'http端口',
      children: detail?.paramMap?.httpPort,
    },
    {
      label: '查询端口',
      children: detail?.paramMap?.queryPort,
    },
    {
      label: '用户名',
      children: detail?.paramMap?.username,
    },
    {
      label: '密码',
      children: '******',
    },
    {
      label: 'jdbc连接参数',
      children: <MapDetail value={detail?.paramMap?.jdbcProp}></MapDetail>,
    },
    {
      label: '描述',
      children: detail?.description,
    },
  ];

  return <Descriptions
    column={1}
    items={items}
    {...reset}
  />;
};

export default forwardRef(DorisDetail);
