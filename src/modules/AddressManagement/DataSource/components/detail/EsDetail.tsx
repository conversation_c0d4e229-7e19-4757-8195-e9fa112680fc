import { forwardRef } from 'react';
import { Descriptions } from 'antd';

import { BaseDetail } from '../BaseDetail';

const EsDetail = ({ detail, ...reset }) => {
  const items = [
    ...BaseDetail(detail),
    {
      label: 'host地址',
      children: detail?.paramMap?.address,
    },
    {
      label: '认证方式',
      children: detail?.paramMap?.authType === 'HTTP_BASIC' ? 'HTTP基础认证' : '不认证',
    },
    ...detail?.paramMap?.authType === 'HTTP_BASIC' ? [
      {
        label: '用户名',
        children: detail?.paramMap?.username,
      },
      {
        label: '密码',
        children: detail?.paramMap?.password,
      },
    ] : [],
    {
      label: '描述',
      children: detail?.description,
    },
    {
      label: '可用节点',
      children:(detail?.paramMap?.nodes || []).join(','),
    },
  ];

  return <Descriptions
    column={1}
    items={items}
    {...reset}
  />;
};

export default forwardRef(EsDetail);
