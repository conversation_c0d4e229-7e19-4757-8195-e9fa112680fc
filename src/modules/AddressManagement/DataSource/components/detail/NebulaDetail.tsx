import { forwardRef } from 'react';
import { Descriptions } from 'antd';

import { BaseDetail } from '../BaseDetail';

const NebulaDetail = ({ detail, ...reset }) => {
  const graphNodes = detail?.paramMap?.graphNodes;
  const metaNodes = detail?.paramMap?.metaNodes;
  const storageNodes = detail?.paramMap?.storageNodes;

  const items = [
    ...BaseDetail(detail),
    {
      label: 'Graph服务地址',
      children: detail?.paramMap?.address,
    },
    {
      label: 'Meta服务地址',
      children: detail?.paramMap?.metaAddress,
    },
    {
      label: '用户名',
      children: detail?.paramMap?.username,
    },
    {
      label: '密码',
      children: '******',
    },
    {
      label: '描述',
      children: detail?.description,
    },
    {
      label: '可用节点',
      children:<div className='node-list'>
        {
          graphNodes?.length
            ? <div>
          graph节点：{graphNodes.join(',')}
            </div>
            : null
        }
        {
          metaNodes?.length
            ? <div>
          meta节点：{metaNodes.join(',')}
            </div>
            : null
        }
        {
          storageNodes?.length
            ? <div>
          storage节点：{storageNodes.join(',')}
            </div>
            : null
        }
        <div></div>
      </div>,
    },
  ];

  return <Descriptions
    column={1}
    items={items}
    {...reset}
  />;
};

export default forwardRef(NebulaDetail);
