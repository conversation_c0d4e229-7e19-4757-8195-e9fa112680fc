import { forwardRef } from 'react';
import { Descriptions } from 'antd';

import { BaseDetail } from '../BaseDetail';

const SqlServerDetail = ({ detail, ...reset }) => {
  const items = [
    ...BaseDetail(detail),
    {
      label: 'jdbc地址',
      children: detail?.paramMap?.address,
    },
    {
      label: '用户名',
      children: detail?.paramMap?.username,
    },
    {
      label: '密码',
      children: '******',
    },
    {
      label: '描述',
      children: detail?.description,
    },
  ];

  return <Descriptions
    column={1}
    items={items}
    {...reset}
  />;
};

export default forwardRef(SqlServerDetail);
