import { forwardRef, useEffect, useState } from 'react';
import { Descriptions } from 'antd';

import { ClusterApi } from '@/modules/Cluster';

import { BaseDetail } from '../BaseDetail';

const HiveDetail = ({ detail, ...reset }) => {
  const [options, setOptions] = useState([]);

  const getCluster = async () => {
    const { data } = await ClusterApi.getAll({ clusterType: 'yarn', supportOpts: 'flink' });
    setOptions(data);
  };

  useEffect(() => {
    getCluster();
  }, []);

  const items = [
    ...BaseDetail(detail),
    {
      label: '关联Hadoop集群',
      children: options?.find(item => item.id == detail?.paramMap?.clusterId)?.clusterName,
    },
    {
      label: 'jdbc地址',
      children: detail?.paramMap?.hiveJdbcUrl,
    },
    {
      label: 'jdbc驱动',
      children: detail?.paramMap?.driver,
    },
    {
      label: '用户名',
      children: detail?.paramMap?.hiveJdbcUser,
    },
    {
      label: '密码',
      children: '******',
    },
    {
      label: '描述',
      children: detail?.description,
    },
  ];

  return <Descriptions
    column={1}
    items={items}
    {...reset}
  />;
};

export default forwardRef(HiveDetail);
