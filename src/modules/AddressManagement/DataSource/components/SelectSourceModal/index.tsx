import { useState } from 'react';
import { Modal, ModalProps } from 'antd';
import { history } from 'umi';

import { basePathname } from '@/routes/platform-management';

import { DataSourceType, DataSourceTypeOptions } from '../../constants/options';

export const SelectSourceModal: React.FC<ModalProps> = ({ onCancel, ...props }) => {
  const [type, setType] = useState<DataSourceType>();

  const handleNext = () => {
    if (type) {
      const url = `${basePathname}/address/data-source/create?platform=${type}`;
      history.push(url);
    }
  };

  const handleSelect = (value: DataSourceType) => () => [setType(value)];

  const clear = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    setType(undefined);
    onCancel?.(e);
  };

  return (
    <Modal
      title='选择数据源'
      okText='下一步'
      onOk={handleNext}
      width={850}
      onCancel={clear}
      {...props}
      okButtonProps={{
        disabled: !type,
      }}
    >
      <div className='flex items-center justify-center'>
        <ul className='select-icon-list mt-1'>
          {DataSourceTypeOptions.map(op => (
            <li key={op.value} onClick={handleSelect(op.value)} className={`${op.value === type ? 'selected' : ''}`}>
              <div className='select-icon-list-icon'>
                <i className={`iconfont ${op.icon}`}></i>
              </div>
              <span className='select-icon-list-label w-[90px] inline-block whitespace-nowrap'>{op.label}</span>
            </li>
          ))}
        </ul>
      </div>
    </Modal>
  );
};
