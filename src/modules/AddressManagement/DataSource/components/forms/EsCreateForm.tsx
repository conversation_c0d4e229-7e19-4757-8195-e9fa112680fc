import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Form, Input, Radio } from 'antd';

import { LinkTest } from '@/components';
import { DataCenterSelect } from '@/components/business/form/select/DataCenterSelect';
import { DataSourceApi } from '@/services';

import { DataSourceModel } from '../../models';
import { AvailableNodes } from '../AvailableNodes';

const rules = {
  code: [
    {
      pattern: /^[0-9a-zA-Z_]+$/,
      message: '支持英文字母、数字和下划线',
    },
    { required: true, message: '请输入数据源标识' },
  ],
  name: [{ required: true, message: '请输入数据源名称' }],
  address: [{ required: true, message: '请输入jdbc地址' }],
  username: [{ required: true, message: '请输入用户名' }],
  password: [{ required: true, message: '请输入密码' }],
};

const defaultData: Partial<DataSourceModel> = {
  connectMode: 'MANUAL',
  status: 'ABNORMAL',
  paramMap: {
    authType: 'NONE',
  },
};

const isDisabled = (paramMap: any) => {
  if (!paramMap?.address) return true;
  return false;
};

const EsCreateForm = ({ form, detail, disabled, ...rest }, ref) => {
  const authType = Form.useWatch(['paramMap', 'authType'], form);
  useEffect(() => {
    form.setFieldsValue({
      ...defaultData,
      ...detail,
      dataCenterIds: detail?.dataCenterList?.map(d => d.id),
    });
  }, [detail]);

  const testConnection = async () => {
    try {
      const { paramMap, connectMode, platform } = await form.getFieldsValue();
      const { data, code } = await DataSourceApi.check({
        platform,
        connectMode,
        paramMap,
      });

      if (code === '0000') {
        form.setFieldsValue({
          status: data.status,
          connectDetail: JSON.stringify(data),
        });
      } else {
        form.setFieldsValue({
          status: data.status,
        });
      }

      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  };

  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      return await form.validateFields();
    },
  }));

  return (
    <Form form={form} disabled={disabled} {...rest}>
      <Form.Item label='数据源名称' name='name' rules={rules.name}>
        <Input placeholder='数据源的显示名字' />
      </Form.Item>
      <Form.Item label='数据源标识' name='code' rules={rules.code}>
        <Input placeholder='数据源的唯一标识，支持英文字母、数字和下划线' />
      </Form.Item>
      <Form.Item label='所属中心' name='dataCenterIds'>
        <DataCenterSelect disabled={disabled} />
      </Form.Item>

      <Form.Item label='数据源类型' name='platform' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='接入模式' name='connectMode' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='状态' name='status' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='连接详情' name='connectDetail' hidden>
        <Input />
      </Form.Item>

      <Form.Item label='host地址' required rules={rules.address} className='pt-1' name={['paramMap', 'address']}>
        <Input placeholder='多个地址以逗号隔开' allowClear />
      </Form.Item>
      <Form.Item label='认证方式' name={['paramMap', 'authType']}>
        <Radio.Group>
          <Radio value='NONE'>不认证</Radio>
          <Radio value='HTTP_BASIC'>HTTP基础认证</Radio>
        </Radio.Group>
      </Form.Item>
      {authType === 'HTTP_BASIC' && (
        <div>
          <Form.Item label='用户名' name={['paramMap', 'username']} wrapperCol={{ span: 6 }}>
            <Input autoComplete='new-password' placeholder='认证用户名' allowClear />
          </Form.Item>
          <Form.Item label='密码' name={['paramMap', 'password']} wrapperCol={{ span: 6 }}>
            <Input.Password autoComplete='new-password' placeholder='认证密码' type='password' allowClear />
          </Form.Item>
        </div>
      )}
      <Form.Item wrapperCol={{ offset: 3, span: 22 }} className='pb-1' shouldUpdate>
        {() => <LinkTest disabled={isDisabled(form.getFieldValue('paramMap')) || disabled} onCheck={testConnection} />}
      </Form.Item>

      <Form.Item label='描述' name='description' wrapperCol={{ span: 14 }}>
        <Input.TextArea rows={4} placeholder='请输入' />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 3 }} name={['paramMap', 'nodes']}>
        {<AvailableNodes form={form} />}
      </Form.Item>
    </Form>
  );
};

export default forwardRef(EsCreateForm);
