import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Form, Input } from 'antd';

import { LinkTest } from '@/components';
import { DataCenterSelect } from '@/components/business/form/select/DataCenterSelect';
import { DataSourceApi } from '@/services';

import { DataSourceModel } from '../../models';
import { NebulaAvailableNodes } from '../NebulaAvailableNodes';

const rules = {
  code: [
    {
      pattern: /^[0-9a-zA-Z_]+$/,
      message: '支持英文字母、数字和下划线',
    },
    { required: true, message: '请输入数据源标识' },
  ],
  name: [{ required: true, message: '请输入数据源名称' }],
  address: [{ required: true, message: '请输入Graph服务地址' }],
  metaAddress: [{ required: true, message: '请输入meta服务地址' }],
  username: [{ required: true, message: '请输入用户名' }],
  password: [{ required: true, message: '请输入密码' }],
};

const defaultData: Partial<DataSourceModel> = {
  connectMode: 'MANUAL',
  status: 'ABNORMAL',
  paramMap: {},
};

const isDisabled = (paramMap: any) => {
  if (!paramMap?.username || !paramMap?.address || !paramMap?.password) return true;

  return false;
};

const NebulaCreateForm = ({ form, detail, disabled, ...rest }, ref) => {
  useEffect(() => {
    form.setFieldsValue({
      ...defaultData,
      ...detail,
      dataCenterIds: detail?.dataCenterList?.map(d => d.id),
    });
  }, [detail]);

  const testConnection = async () => {
    try {
      const { paramMap, connectMode, platform } = await form.getFieldsValue();
      const { data, code } = await DataSourceApi.check({
        platform,
        connectMode,
        paramMap,
      });

      if (code === '0000') {
        form.setFieldsValue({
          status: data.status,
          connectDetail: JSON.stringify(data),
        });
      } else {
        form.setFieldsValue({
          status: data.status,
        });
      }

      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  };

  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      return await form.validateFields();
    },
  }));

  return (
    <Form form={form} disabled={disabled} {...rest}>
      <Form.Item label='数据源名称' name='name' rules={rules.name}>
        <Input placeholder='数据源的显示名字' />
      </Form.Item>
      <Form.Item label='数据源标识' name='code' rules={rules.code}>
        <Input placeholder='数据源的唯一标识，支持英文字母、数字和下划线' />
      </Form.Item>
      <Form.Item label='所属中心' name='dataCenterIds'>
        <DataCenterSelect disabled={disabled} />
      </Form.Item>

      <Form.Item name={['paramMap', 'address']} label='Graph服务地址' rules={rules.address}>
        <Input allowClear placeholder='请输入' autoComplete='new-password' />
      </Form.Item>
      <Form.Item name={['paramMap', 'metaAddress']} label='Meta服务地址' rules={rules.metaAddress}>
        <Input allowClear placeholder='请输入' autoComplete='new-password' />
      </Form.Item>
      <Form.Item name={['paramMap', 'username']} label='用户名' rules={rules.username}>
        <Input allowClear placeholder='请输入' autoComplete='new-password' />
      </Form.Item>
      <Form.Item name={['paramMap', 'password']} label='密码' rules={rules.password}>
        <Input.Password allowClear placeholder='请输入' autoComplete='new-password' />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 3 }} shouldUpdate>
        {() => <LinkTest onCheck={testConnection} disabled={isDisabled(form.getFieldValue('paramMap')) || disabled} />}
      </Form.Item>

      <Form.Item label='数据源类型' name='platform' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='接入模式' name='connectMode' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='状态' name='status' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='连接详情' name='connectDetail' hidden>
        <Input />
      </Form.Item>

      <Form.Item label='描述' name='description' wrapperCol={{ span: 14 }}>
        <Input.TextArea rows={4} placeholder='请输入' />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 3 }}>
        {<NebulaAvailableNodes form={form} />}
      </Form.Item>
      <Form.Item name={['paramMap', 'graphNodes']} hidden>
        <Input />
      </Form.Item>
      <Form.Item name={['paramMap', 'metaNodes']} hidden>
        <Input />
      </Form.Item>
      <Form.Item name={['paramMap', 'storageNodes']} hidden>
        <Input />
      </Form.Item>
    </Form>
  );
};

export default forwardRef(NebulaCreateForm);
