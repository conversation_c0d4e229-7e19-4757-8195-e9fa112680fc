import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Form, Input } from 'antd';

import { LinkTest } from '@/components';
import { DataCenterSelect } from '@/components/business/form/select/DataCenterSelect';
import { ClusterSelect } from '@/modules/Cluster';
import { DataSourceApi } from '@/services';

import { DataSourceModel } from '../../models';

const rules = {
  code: [
    {
      pattern: /^[0-9a-zA-Z_]+$/,
      message: '支持英文字母、数字和下划线',
    },
    { required: true, message: '请输入数据源标识' },
  ],
  name: [{ required: true, message: '请输入数据源名称' }],
  hiveJdbcUrl: [{ required: true, message: '请输入jdbc地址' }],
  username: [{ required: true, message: '请输入用户名' }],
  password: [{ required: true, message: '请输入密码' }],
  clusterId: [{ required: true, message: '请选择关联Hadoop集群' }],
};

const defaultData: Partial<DataSourceModel> = {
  connectMode: 'MANUAL',
  status: 'ABNORMAL',
  paramMap: {},
};

const isDisabled = (paramMap: any) => {
  if (!paramMap?.hiveJdbcUrl) return true;

  return false;
};

const HiveCreateForm = ({ form, detail, disabled, ...rest }, ref) => {
  useEffect(() => {
    form.setFieldsValue({
      ...defaultData,
      ...detail,
      dataCenterIds: detail?.dataCenterList?.map(d => d.id),
      paramMap: {
        ...(detail.paramMap || {}),
        ...detail?.paramMap?.clusterId && {
          clusterId: `${detail.paramMap.clusterId}`,
        },
      },
    });
  }, [detail]);

  const testConnection = async () => {
    try {
      const { paramMap, connectMode, platform } = await form.getFieldsValue();
      const { data, code } = await DataSourceApi.check({
        platform,
        connectMode,
        paramMap,
      });

      if (code === '0000') {
        form.setFieldsValue({
          status: data.status,
          connectDetail: JSON.stringify(data),
        });
      } else {
        form.setFieldsValue({
          status: data.status,
        });
      }

      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  };

  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      return await form.validateFields();
    },
  }));

  return (
    <Form form={form} disabled={disabled} {...rest}>
      <Form.Item label='数据源名称' name='name' rules={rules.name}>
        <Input placeholder='数据源的显示名字' />
      </Form.Item>
      <Form.Item label='数据源标识' name='code' rules={rules.code}>
        <Input placeholder='数据源的唯一标识，支持英文字母、数字和下划线' />
      </Form.Item>
      <Form.Item label='所属中心' name='dataCenterIds'>
        <DataCenterSelect disabled={disabled} />
      </Form.Item>

      <Form.Item required label='关联Hadoop集群' name={['paramMap', 'clusterId']} rules={rules.clusterId}>
        <ClusterSelect clusterType='yarn' supportOpts='flink' />
      </Form.Item>
      <Form.Item
        label='jdbc地址'
        name={['paramMap', 'hiveJdbcUrl']}
        rules={rules.hiveJdbcUrl}
        help='kerberos认证时示例：******************************************=<hive server principal>'
      >
        <Input placeholder='************************' />
      </Form.Item>
      <Form.Item label='jdbc驱动' name={['paramMap', 'driver']}>
        <Input placeholder='org.apache.hive.jdbc.HiveDriver' />
      </Form.Item>
      <Form.Item label='用户名' name={['paramMap', 'hiveJdbcUser']} wrapperCol={{ span: 5 }}>
        <Input autoComplete='off' placeholder='请输入' />
      </Form.Item>
      <Form.Item label='密码' name={['paramMap', 'hiveJdbcPassword']} wrapperCol={{ span: 5 }}>
        <Input.Password autoComplete='new-password' placeholder='请输入' />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 3 }} shouldUpdate>
        {() => <LinkTest onCheck={testConnection} disabled={isDisabled(form.getFieldValue('paramMap')) || disabled} />}
      </Form.Item>

      <Form.Item label='数据源类型' name='platform' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='接入模式' name='connectMode' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='状态' name='status' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='连接详情' name='connectDetail' hidden>
        <Input />
      </Form.Item>

      <Form.Item label='描述' name='description' wrapperCol={{ span: 14 }}>
        <Input.TextArea rows={4} placeholder='请输入' />
      </Form.Item>
    </Form>
  );
};

export default forwardRef(HiveCreateForm);
