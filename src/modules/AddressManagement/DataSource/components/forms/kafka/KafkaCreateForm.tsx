import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Form, FormInstance, Input, Switch } from 'antd';
import { omit } from 'lodash';

import { LinkTest } from '@/components';
import { DataCenterSelect } from '@/components/business/form/select/DataCenterSelect';
import { DataSourceApi } from '@/services';

import { DataSourceModel } from '../../../models';
import { AvailableNodes } from '../../AvailableNodes';
import { MapSetting } from '../../MapSetting';

import { defaultSetting } from './components/setConfig';
import { TableSetting } from './components';

const rules = {
  brokerAddress: [{ required: true, message: 'ip:port ,多个地址以逗号隔开' }],
  code: [
    {
      pattern: /^[0-9a-zA-Z_]+$/,
      message: '支持英文字母、数字和下划线',
    },
    { required: true, message: '请输入数据源标识' },
  ],
  name: [{ required: true, message: '请输入数据源名称' }],
};

const defaultData: Partial<DataSourceModel> = {
  platform: 'KAFKA',
  connectMode: 'MANUAL',
  status: 'ABNORMAL',
  paramMap: {
    securityProtocol: 'PLAINTEXT',
  },
};

const isDisabled = (paramMap: any) => {
  if (!paramMap?.brokerAddress) return true;

  return false;
};

const KafkaCreateForm = ({ form, detail, disabled, ...rest }, ref) => {
  const isAuthentication = Form.useWatch(['paramMap', 'isAuthentication'], form);

  useEffect(() => {
    if (!detail.id) {
      form.setFieldsValue({
        ...defaultData,
        dataCenterIds: [],
      });
    } else {
      detail.paramMap.setting = omit(detail.paramMap, ['nodes', 'brokerAddress', 'properties']);
      detail.paramMap.isAuthentication = ['SSL', 'SASL_PLAINTEXT', 'SASL_SSL']
        .includes(detail.paramMap.securityProtocol);
      
      form.setFieldsValue({
        ...defaultData,
        ...detail,
        dataCenterIds: detail?.dataCenterList?.map(d => d.id),
      });
    }
  }, [detail]);

  const testConnection = async () => {
    try {
      const { paramMap, connectMode, platform } = await form.getFieldsValue();
      const { setting, ...rest } = paramMap;

      const { data, code } = await DataSourceApi.check({
        platform,
        connectMode,
        paramMap: {
          ...rest,
          ...setting,
        },
      });

      if (code === '0000') {
        form.setFieldsValue({
          status: data.status,
          connectDetail: JSON.stringify(data),
        });
      } else {
        form.setFieldsValue({
          status: data.status,
        });
      }

      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  };

  const getFieldValues = () => {
    const values = (form as FormInstance).getFieldsValue();
    const {
      paramMap: { setting, ...rest },
    } = values;

    return {
      ...values,
      paramMap: {
        ...rest,
        ...setting,
      },
    };
  };

  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      const values = await form.validateFields();
      const {
        paramMap: { setting, ...rest },
      } = values;

      return {
        ...values,
        paramMap: {
          ...rest,
          ...setting,
        },
      };
    },
  }));

  return (
    <Form form={form} disabled={disabled} {...rest}>
      <Form.Item label='数据源名称' name='name' required rules={rules.name}>
        <Input placeholder='数据源的显示名字' allowClear />
      </Form.Item>
      <Form.Item label='数据源标识' name='code' required rules={rules.code}>
        <Input placeholder='数据源的唯一标识，支持英文字母、数字和下划线' allowClear />
      </Form.Item>
      <Form.Item label='所属中心' name='dataCenterIds' wrapperCol={{ span: 20 }}>
        <DataCenterSelect disabled={disabled} />
      </Form.Item>

      <Form.Item
        label='Broker地址'
        name={['paramMap', 'brokerAddress']}
        required
        rules={rules.brokerAddress}
        className='pt-1'
      >
        <Input placeholder='ip:port ,多个地址以逗号隔开' allowClear />
      </Form.Item>
      <Form.Item label='是否认证' name={['paramMap', 'isAuthentication']} valuePropName='checked'>
        <Switch
          checkedChildren='开'
          unCheckedChildren='关'
          onChange={isAuthentication => {
            if (isAuthentication) {
              form.setFieldValue(['paramMap', 'setting'], defaultSetting);
            } else {
              form.setFieldValue(['paramMap', 'securityProtocol'], 'PLAINTEXT');
            }
            form.setFieldsValue({
              status: 'ABNORMAL',
            });
          }}
        />
      </Form.Item>

      {isAuthentication ? (
        <Form.Item label='配置' name={['paramMap', 'setting']} wrapperCol={{ span: 20 }}>
          <TableSetting />
        </Form.Item>
      ) : (
        <Form.Item label='安全协议' name={['paramMap', 'securityProtocol']} wrapperCol={{ span: 20 }} hidden>
          <Input />
        </Form.Item>
      )}

      <Form.Item label='高级配置' name={['paramMap', 'properties']} wrapperCol={{ span: 16 }}>
        <MapSetting>
          <div className='fs-12 mb-1 mt-1' style={{ color: 'rgba(0,0,0,0.45)' }}>
            手工配置Kafka生产和消费的必要配置参数，参数详见Kafka官方文档
          </div>
        </MapSetting>
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 3 }} className='pb-1' shouldUpdate>
        {() => <LinkTest disabled={isDisabled(form.getFieldValue('paramMap')) || disabled} onCheck={testConnection} />}
      </Form.Item>

      <Form.Item label='数据源类型' name='platform' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='接入模式' name='connectMode' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='状态' name='status' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='连接详情' name='connectDetail' hidden>
        <Input />
      </Form.Item>

      <Form.Item label='描述' name='description' wrapperCol={{ span: 16 }}>
        <Input.TextArea allowClear placeholder='请输入' autoSize={{ minRows: 3, maxRows: 8 }} />
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 3 }} name={['paramMap', 'nodes']}>
        {<AvailableNodes form={form} getFormValues={getFieldValues} />}
      </Form.Item>
    </Form>
  );
};

export default forwardRef(KafkaCreateForm);
