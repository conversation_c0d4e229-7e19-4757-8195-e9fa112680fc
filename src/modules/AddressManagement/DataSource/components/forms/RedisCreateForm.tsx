import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Form, Input, Radio } from 'antd';

import { LinkTest } from '@/components';
import { DataCenterSelect } from '@/components/business/form/select/DataCenterSelect';
import { DataSourceApi } from '@/services';

import { DataSourceModel } from '../../models';

enum RedisMode {
  single = '单机模式',
  cluster = '集群模式',
  sentinel = '哨兵模式',
}

const RedisModeOptions: Array<Record<'value' | 'label', string>> = Object.entries(RedisMode).map(([value, label]) => ({
  value,
  label,
}));

const rules = {
  code: [
    {
      pattern: /^[0-9a-zA-Z_]+$/,
      message: '支持英文字母、数字和下划线',
    },
    { required: true, message: '请输入数据源标识' },
  ],
  name: [{ required: true, message: '请输入数据源名称' }],
  mode: [{ required: true, message: '请选择模式' }],
  address: [{ required: true, message: 'http://ip:port ,多个地址以逗号隔开' }],
  masterName: [{ required: true, message: '请输入哨兵master名称' }],
  password: [{ required: false, message: '请输入' }],
};

const defaultData: Partial<DataSourceModel> = {
  connectMode: 'MANUAL',
  status: 'ABNORMAL',
  paramMap: {},
};

const isDisabled = (paramMap: any, isSentinel) => {
  if (!paramMap?.mode || !paramMap?.address || (isSentinel && !paramMap?.masterName)) return true;

  return false;
};

const RedisCreateForm = ({ form, detail, disabled, ...rest }, ref) => {
  const mode = Form.useWatch(['paramMap', 'mode'], form);

  useEffect(() => {
    form.setFieldsValue({
      ...defaultData,
      ...detail,
      dataCenterIds: detail?.dataCenterList?.map(d => d.id),
    });
  }, [detail]);

  const testConnection = async () => {
    try {
      const { paramMap, connectMode, platform } = await form.getFieldsValue();
      const { data, code } = await DataSourceApi.check({
        platform,
        connectMode,
        paramMap,
      });

      if (code === '0000') {
        form.setFieldsValue({
          status: data.status,
          connectDetail: JSON.stringify(data),
        });
      } else {
        form.setFieldsValue({
          status: data.status,
        });
      }

      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  };

  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      return await form.validateFields();
    },
  }));

  return (
    <Form form={form} disabled={disabled} {...rest}>
      <Form.Item label='数据源名称' name='name' rules={rules.name}>
        <Input placeholder='数据源的显示名字' />
      </Form.Item>
      <Form.Item label='数据源标识' name='code' rules={rules.code}>
        <Input placeholder='数据源的唯一标识，支持英文字母、数字和下划线' />
      </Form.Item>
      <Form.Item label='所属中心' name='dataCenterIds'>
        <DataCenterSelect disabled={disabled} />
      </Form.Item>

      <Form.Item name={['paramMap', 'mode']} label='模式' rules={rules.mode}>
        <Radio.Group options={RedisModeOptions} />
      </Form.Item>
      <Form.Item
        name={['paramMap', 'address']}
        label={mode === 'sentinel' ? '哨兵连接地址' : 'redis连接地址'}
        rules={rules.address}
      >
        <Input allowClear placeholder='请输入' autoComplete='new-password' />
      </Form.Item>
      <Form.Item name={['paramMap', 'password']} label='密码' rules={rules.password}>
        <Input.Password allowClear placeholder='请输入' autoComplete='new-password' />
      </Form.Item>

      {mode === 'sentinel' && (
        <Form.Item name={['paramMap', 'masterName']} label='哨兵master名称' rules={rules.masterName}>
          <Input allowClear placeholder='请输入' />
        </Form.Item>
      )}
      <Form.Item wrapperCol={{ offset: 3 }} shouldUpdate>
        {() => (
          <LinkTest
            onCheck={testConnection}
            disabled={isDisabled(form.getFieldValue('paramMap'), mode === 'sentinel') || disabled}
          />
        )}
      </Form.Item>

      <Form.Item label='数据源类型' name='platform' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='接入模式' name='connectMode' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='状态' name='status' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='连接详情' name='connectDetail' hidden>
        <Input />
      </Form.Item>

      <Form.Item label='描述' name='description' wrapperCol={{ span: 14 }}>
        <Input.TextArea rows={4} placeholder='请输入' />
      </Form.Item>
    </Form>
  );
};

export default forwardRef(RedisCreateForm);
