import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Form, Input } from 'antd';

import { LinkTest } from '@/components';
import { DataCenterSelect } from '@/components/business/form/select/DataCenterSelect';
import { DataSourceApi } from '@/services';

import { MapSetting } from '../MapSetting';

const rules = {
  code: [
    {
      pattern: /^[0-9a-zA-Z_]+$/,
      message: '支持英文字母、数字和下划线',
    },
    { required: true, message: '请输入数据源标识' },
  ],
  name: [{ required: true, message: '请输入数据源名称' }],
  feAddress: [{ required: true, message: '填写ip，多个ip使用逗号隔开' }],
  username: [{ required: true, message: '请输入用户名' }],
  httpPort: [{ required: true, message: '请输入http端口' }],
  queryPort: [{ required: true, message: '请输入查询端口' }],
};

const defaultData = {
  platform: 'DORIS',
  connectMode: 'MANUAL',
  paramMap: {
    httpPort: 8030,
    queryPort: 9030,
  },
  status: 'ABNORMAL',
};

const DorisCreateForm = ({ form, detail, disabled, ...rest }, ref) => {
  useEffect(() => {
    form.setFieldsValue({
      ...defaultData,
      ...detail,
      dataCenterIds: detail?.dataCenterList?.map(d => d.id),
    });
  }, [detail]);

  const testConnection = async () => {
    try {
      const { paramMap, connectMode, platform } = await form.getFieldsValue();
      const { data } = await DataSourceApi.check({
        platform,
        connectMode,
        paramMap: {
          ...paramMap,
        },
      });
      form.setFieldsValue({
        status: data.status,
      });
      return data;
    } catch (err) {
      return Promise.reject(err);
    }
  };

  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      return await form.validateFields();
    },
  }));

  return (
    <Form form={form} disabled={disabled} ref={ref} {...rest} >
      <Form.Item label='数据源名称' name='name' rules={rules.name}>
        <Input placeholder='数据源的显示名字' />
      </Form.Item>
      <Form.Item label='数据源标识' name='code' rules={rules.code}>
        <Input placeholder='数据源的唯一标识，支持英文字母、数字和下划线' />
      </Form.Item>
      <Form.Item label='所属中心' name='dataCenterIds'>
        <DataCenterSelect disabled={disabled} />
      </Form.Item>

      <Form.Item label='FE IP地址' name={['paramMap', 'feAddress']} rules={rules.feAddress}>
        <Input placeholder='填写ip，多个ip使用逗号隔开' />
      </Form.Item>
      <Form.Item label='http端口' name={['paramMap', 'httpPort']} rules={rules.httpPort}>
        <Input placeholder='请输入' />
      </Form.Item>
      <Form.Item label='查询端口' name={['paramMap', 'queryPort']} rules={rules.queryPort}>
        <Input placeholder='请输入' />
      </Form.Item>
      <Form.Item label='用户名' name={['paramMap', 'username']} rules={rules.username}>
        <Input autoComplete='off' placeholder='请输入' />
      </Form.Item>
      <Form.Item label='密码' name={['paramMap', 'password']}>
        <Input.Password autoComplete='new-password' placeholder='请输入' />
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 3 }} shouldUpdate>
        {() => <LinkTest onCheck={testConnection} />}
      </Form.Item>
      <Form.Item name={['paramMap', 'jdbcProp']} className='fixed-width' label='jdbc连接参数' wrapperCol={{ span: 16 }}>
        <MapSetting />
      </Form.Item>

      <Form.Item label='数据源类型' name='platform' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='状态' name='status' hidden>
        <Input />
      </Form.Item>
      <Form.Item label='接入模式' name='connectMode' hidden>
        <Input />
      </Form.Item>

      <Form.Item label='描述' name='description' wrapperCol={{ span: 14 }}>
        <Input.TextArea rows={4} placeholder='请输入' />
      </Form.Item>
    </Form>
  );
};

export default forwardRef(DorisCreateForm);
