// 列表页操作栏
import { Button, Form, Select } from 'antd';

import { SearchInput } from '@/components';

import { DataSourceTypeOptions } from '../constants/options';

interface ListToolbarProps {
  onCreate: DATA_SOURCE.onCreate;
  onRefresh: () => void;
  filter: any;
  setFilter: (val) => void;
}

export const ListToolbar: React.FC<ListToolbarProps> = ({ filter, setFilter,  onCreate, onRefresh }) => {
  const [form] = Form.useForm();

  return (
    <div className='flex items-center px-4 py-3'>
      <Button type='primary' onClick={onCreate}>
        创建
      </Button>
      <Form name='search' layout='inline' form={form} initialValues={filter}>
        <Form.Item name='name' className='ml-2 w-[240px]'>
          <SearchInput
            onSearch={val => setFilter({ ...filter, name: val })}
            placeholder='搜索数据源名称'
            allowClear
          />
        </Form.Item>
        <Form.Item colon label='类型' name='platform' className='w-[200px]'>
          <Select
            options={DataSourceTypeOptions}
            placeholder='请选择'
            allowClear
            onChange={val => setFilter({ ...filter, platform: val })}
          />
        </Form.Item>
      </Form>
      <Button className='ml-auto' type='text' onClick={onRefresh}>
        <i className='iconfont icon-refresh-line'></i>
      </Button>
    </div>
  );
};
