import { useState } from 'react';
import { Button } from 'antd';

import { DataSourceApi } from '@/services';

interface Props {
  form: any;
  value?: string[];
  onChange?: (val) => void;
  getFormValues?: () => unknown;
}

export const AvailableNodes = (props: Props) => {
  const { value, onChange, form, getFormValues } = props;
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    setLoading(true);
    let values;
    if (getFormValues) {
      values = getFormValues();
    } else {
      values = await form.getFieldsValue();
    }
    const { paramMap, connectMode, platform } = values;
    const { data } = await DataSourceApi.getAllServerNode({
      platform,
      connectMode,
      paramMap,
    }).finally(() => setLoading(false));
    onChange?.(data.nodes);
  };

  return <>
    <Button className='flex items-center mb-1'loading={loading} onClick={handleClick}>
      获取可用节点
    </Button>
    {
      !value?.length
        ? <div className='text-[13px] text-gray-5'>暂无可用节点</div>
        :
        <div className='node-list mt-1'>
          {(value || []).join(',')}
        </div>
    }
  </>;
};
