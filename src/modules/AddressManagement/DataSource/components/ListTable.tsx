import { Button, Tag } from 'antd';

import { Status } from '@/components';
import { CustomTable, CustomTableProps } from '@/components/business/CustomTable';

interface ListTableProps<Record> extends Omit<CustomTableProps, 'columns'> {
  children?: ReactNode;
  dataSource: Record[];
  onEdit: DATA_SOURCE.onEdit;
  onDetail: DATA_SOURCE.onEdit;
  onDelete: DATA_SOURCE.onDelete;
  onCheck: DATA_SOURCE.onCheck;
}

export function ListTable<T extends DATA_SOURCE.DataSourceListItem>({
  children,
  dataSource,
  onDelete,
  onEdit,
  onCheck,
  onDetail,
  ...tableProps
}: ListTableProps<T>) {

  const columns: Array<ColumnType<T>> = [
    {
      title: '数据源名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => <Button type="link" size='small' onClick={handleDetail(record)}>{text}</Button>,
    },
    {
      title: '数据源标识',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '类型',
      dataIndex: 'platform',
      renderText: value => value.toLocaleLowerCase(),
    },
    {
      title: '所属中心',
      dataIndex: 'dataCenterList',
      render: dataCenterList => {
        return (
          <div className='flex-wrap flex'>
            {dataCenterList?.map(d => (
              <Tag className='mb-1 mr-1' key={d?.id}>
                {d?.name}
              </Tag>
            ))}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (status, record) => {
        return (
          <Status
            detail={record.connectDetail as DATA_SOURCE.ConnectDetail | undefined}
            status={status}
            onCheck={handleCheck(record)}
          />
        );
      },
    },
    {
      title: '最近修改时间',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      dataIndex: 'operator',
      width: 110,
      render: (_: any, record) => (
        <>
          <Button size='small' type='link' className='mr-[6px]' onClick={handleEdit(record)}>
            编辑
          </Button>
          <Button size='small' type='link' onClick={handleDelete(record)}>
            删除
          </Button>
        </>
      ),
    },
  ];

  const handleCheck = (record: T) => async () => {
    return onCheck(record);
  };

  const handleEdit = (record: T) => () => {
    onEdit(record);
  };

  const handleDetail = (record: T) => () => {
    onDetail(record);
  };

  const handleDelete = (record: T) => () => {
    onDelete(record);
  };

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      {children}
      <CustomTable
        dataSource={dataSource}
        columns={columns}
        scroll={{ x: undefined }}
        {...tableProps}
      />
    </div>
  );
}
