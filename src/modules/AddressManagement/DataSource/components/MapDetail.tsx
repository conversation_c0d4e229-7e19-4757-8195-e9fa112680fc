import { useEffect, useState } from 'react';

export const MapDetail = ({ value } : {value: Record<string, string>}) => {
  const [arr, setArr] = useState([]);

  useEffect(() => {
    if (value) {
      setArr(
        Object.keys(value)?.length ? Object.entries(value).map(([key, value]) => ({
          key,
          value,
        })) : [],
      );
    }
  }, [value]);

  return (
    <div>
      {arr.map((item, i: number) => {
        return (
          <div key={i} className={'mb-2'}>
            <span>{ item.key }:{ item.value }</span>
          </div>
        );
      })}
    </div>
  );
};
