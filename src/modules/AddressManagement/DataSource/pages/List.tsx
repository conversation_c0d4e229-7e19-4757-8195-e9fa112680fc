import { useEffect, useState } from 'react';
import { message } from 'antd';
import { history, useSearchParams } from 'umi';

import { useCustomTableHook } from '@/components';
import { DataSourceDelConfig } from '@/constants/deleteUrl';
import { useDeleteConfirm } from '@/hooks';
import { basePathname } from '@/routes/platform-management';
import { DataSourceApi } from '@/services';
import { useGlobalStore } from '@/stores/useGlobalStore';

import { ListTable, ListToolbar } from '../components';
import { SelectSourceModal } from '../components/SelectSourceModal';

export const List = () => {
  const [list, setList] = useState<DATA_SOURCE.DataSourceListItem[]>([]);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { setPageInfo, resetPageInfo } = useGlobalStore();
  const delConfirm = useDeleteConfirm(DataSourceDelConfig);
  const [searchParams] = useSearchParams();
  const name = searchParams.get('name');
  const {
    pagination,
    queryParams,
    setPagination,
    handleTableChange,
    setFilter,
    filter,
  } = useCustomTableHook({
    cacheId: !name ? 'dataSourceList' : undefined,
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
    ...name && { filter: { name } },
  });

  useEffect(() => {
    setPageInfo({
      title: '数据源管理',
      description: '关联系统内外部数据源，赋予数据中台访问数据源的能力。',
    });

    return () => {
      resetPageInfo();
    };
  }, []);

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data, total } = await DataSourceApi.getList(
        queryParams,
        {
          headers: {
            'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
            'Jax-Super-Project-Admin': false,
          },
        },
      );
      setList(data);
      setPagination({
        ...pagination,
        total,
      });
      setLoading(false);
    } catch (error: any) {
      message.error(error?.msg);
      setLoading(false);
    }
  };

  const handleCheck: DATA_SOURCE.onCheck = async record => {
    try {
      const { data } = await DataSourceApi.connect(record.id);

      const newList = list.map(d => {
        if (d.id === record.id) {
          return {
            ...d,
            status: data.status,
            connectDetail: data,
          };
        }
        return d;
      });

      setList(newList);
    } catch (err) {
      return Promise.reject(err);
    }
  };

  const handleEdit: DATA_SOURCE.onEdit = record => {
    const url = `${basePathname}/address/data-source/edit/${record.id}`;
    history.push(url);
  };

  const handleDetail: DATA_SOURCE.onEdit = record => {
    const url = `${basePathname}/address/data-source/detail/${record.id}`;
    history.push(url);
  };

  const handleDelete: DATA_SOURCE.onDelete = record => {
    const { page, size } = queryParams;
    const { total } = pagination;
    const skip = total % size === 1 && page === Math.floor(total / size);
    delConfirm(record.id, `${DataSourceDelConfig.title}「${record?.name}」吗?`, {
      options: {
        headers: {
          'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
          'Jax-Super-Project-Admin': false,
        },
      },
    }).then(() => {
      if (skip) {
        setFilter({
          ...filter,
        });
      } else {
        fetchData();
      }
    });
  };

  return (
    <div className='h-full'>
      <ListTable
        dataSource={list}
        onCheck={handleCheck}
        onDelete={handleDelete}
        onEdit={handleEdit}
        onDetail={handleDetail}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
      >
        <ListToolbar filter={filter} setFilter={setFilter} onCreate={() => setOpen(true)} onRefresh={fetchData} />
      </ListTable>

      <SelectSourceModal open={open} onCancel={() => setOpen(false)} />
    </div>
  );
};
