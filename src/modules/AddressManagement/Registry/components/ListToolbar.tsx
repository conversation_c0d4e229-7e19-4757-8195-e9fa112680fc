// 列表页操作栏
import { Button, Form } from 'antd';

import { SearchInput } from '@/components';
import { useSearchValue } from '@/stores/toolbar';

interface ListToolbarProps {
  onCreate: DATA_SOURCE.onCreate;
}

export const ListToolbar: React.FC<ListToolbarProps> = ({ onCreate }) => {
  const [form] = Form.useForm();
  const { setFilter } = useSearchValue();

  const handleSearch = (name: string, val) => {
    const params = form.getFieldsValue();
    if (!val) {
      params[name] = val;
      form.setFieldValue(name, val);
    }

    setFilter(params);
  };

  return (
    <div className='flex items-center px-4 py-3'>
      <Button type='primary' onClick={onCreate}>
        创建
      </Button>
      <Form name='search' layout='inline' form={form}>
        <Form.Item name='name' className='ml-2 w-[240px]'>
          <SearchInput onSearch={val => handleSearch('name', val)} placeholder='搜索中心名称' allowClear />
        </Form.Item>
      </Form>
    </div>
  );
};
