import { useEffect, useState } from 'react';
import { Button, message } from 'antd';
import { omit } from 'lodash-es';
import { history, useSearchParams } from 'umi';

import { RegisterCenterDelConfig } from '@/constants/deleteUrl';
import { useDeleteConfirm } from '@/hooks';
import { RegisterCenterApi } from '@/services';
import { useSearchValue } from '@/stores/toolbar';
import { useGlobalStore } from '@/stores/useGlobalStore';
import { jsonStringify } from '@/utils';

import { ListTable, ListToolbar } from '../components';
import EditModal from '../components/EditModal';

export const List = () => {
  const [list, setList] = useState<REGISTER_CENTER.ListItem[]>([]);
  const [editRecord, setEditRecord] = useState<REGISTER_CENTER.Entity>();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isDetail, setDetail] = useState(false);
  const { setPageInfo, resetPageInfo } = useGlobalStore();
  const delConfirm = useDeleteConfirm(RegisterCenterDelConfig);
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const { page, size, filter, reset, setTotal, setName, total = 0, setPage } = useSearchValue();

  useEffect(() => {
    setName('registerCenter');
    setPageInfo({
      title: '注册中心管理',
      description: ' ',
    });

    return () => {
      resetPageInfo();
      reset();
    };
  }, []);

  useEffect(() => {
    fetchData();
  }, [jsonStringify({ page, size, filter, id })]);

  const fetchData = async () => {
    setLoading(true);
    const { data, total } = await RegisterCenterApi.getList({
      page,
      size,
      filter,
      sort: {
        updateTime: 'DESC',
      },
    });
    if (id) {
      setList(data.filter(d => d.id === id));
      setTotal(1);
    } else {
      setList(data);
      setTotal(total as number);
    }
    setLoading(false);
  };

  const handleCheck: REGISTER_CENTER.onCheck = async record => {
    try {
      const { data } = await RegisterCenterApi.connect(record.id);

      const newList = list.map(d => {
        if (d.id === record.id) {
          return {
            ...d,
            status: data.status,
            connectDetail: data,
          };
        }
        return d;
      });

      setList(newList);
    } catch (err) {
      return Promise.reject(err);
    }
  };

  const handleSave: REGISTER_CENTER.onEdit = async value => {
    try {
      if (editRecord) {
        const { code, msg } = await RegisterCenterApi.update({
          ...editRecord,
          ...value,
          connectDetail: jsonStringify({
            ...editRecord.connectDetail,
            ...value.connectDetail,
          }) as REGISTER_CENTER.ConnectDetail,
        });

        if (code !== '0000') {
          throw new Error(msg);
        } else {
          message.success('保存成功');
        }
      } else {
        const { code, msg } = await RegisterCenterApi.create({
          ...value,
          connectDetail: jsonStringify(value.connectDetail) as REGISTER_CENTER.ConnectDetail,
        });

        if (code !== '0000') {
          throw new Error(msg);
        } else {
          message.success('保存成功');
        }
      }
      await fetchData();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const handleDelete: REGISTER_CENTER.onDelete = record => {
    const skip = total % size === 1 && page === Math.floor(total / size);
    delConfirm(record.id, `${RegisterCenterDelConfig.title}「${record?.name}」吗?`).then(res => {
      if (skip) {
        setPage(page - 1);
      } else {
        if (res.code === '0000') {
          message.success('删除成功');
          fetchData();
        }
      }
    });
  };

  const handlePageChange: REGISTER_CENTER.onPageChange = (page: number, pageSize: number = 10) => {
    fetchData();
  };

  const handleSearch: REGISTER_CENTER.onSearch = params => {
    fetchData();
  };

  const openEdit: REGISTER_CENTER.onEdit = async record => {
    setEditRecord({
      ...record,
      dataCenterIds: record.dataCenterList.map(d => d.id),
    });
    setOpen(true);
  };

  const openDetail: REGISTER_CENTER.onDetail = async record => {
    // eslint-disable-next-line max-len
    const detail = omit(record, ['connectDetail','dataCenterList', 'createUser', 'createTime', 'createUserName', 'updateTime', 'updateUser', 'updateUserName' ]);
    history.push(`/platform-management/address/registry/detail/${record.id}?info=${JSON.stringify({
      ...detail,
      dataCenterIds: record.dataCenterList.map(d => d.id),
    })}`);
  };

  const clear = () => {
    setOpen(false);
    setDetail(false);
    setEditRecord(undefined);
  };

  return (
    <div className='h-full'>
      <ListTable
        dataSource={list}
        onCheck={handleCheck}
        onDelete={handleDelete}
        onEdit={openEdit}
        onDetail={openDetail}
        onPageChange={handlePageChange}
        loading={loading}
      >
        <div className='flex justify-between'>
          <ListToolbar onSearch={handleSearch} onCreate={() => setOpen(true)} />
          <div className='flex items-center mr-3'>
            <Button type='text' onClick={fetchData}>
              <i className='iconfont icon-refresh-line' />
            </Button>
          </div>
        </div>
      </ListTable>

      <EditModal open={open} isDetail={isDetail} onCancel={clear} onOk={handleSave} initialValue={editRecord} />
    </div>
  );
};
