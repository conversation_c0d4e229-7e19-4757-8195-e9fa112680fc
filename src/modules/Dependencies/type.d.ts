declare namespace DEPENDENCIES {
  type ObjType =
    | 'businessCategory'
    | 'dataMart'
    | 'dataSubject'
    | 'dataDomain'
    | 'businessProcess'
    | 'ODS'
    | 'DIM'
    | 'DWD'
    | 'DWS'
    | 'ADS'
    | 'DIMENSION'
    | 'timePeriod'
    | 'adjunct'
    | 'atomicIndicator'
    | 'derivativeIndicator'
    | 'columnDict'
    | 'dictEnum'
    | 'dictGroup'
    | 'nameDict'
    | 'measureUnit'
    | 'checkRule'
    | 'layer'
    | 'derivativeIndicator'
    | 'kafkaDatasource'
    | 'elasticsearchDatasource'
    | 'clickhouseDatasource'
    | 'mysqlDatasource'
    | 'cell'
    | 'registerCenter'
    | 'kafkaDeploy'
    | 'elasticsearchDeploy'
    | 'clickhouseDeploy'
    | 'mysqlDeploy'
    | 'DataServiceClient'
    | 'OBJECT'
    | 'metricSet'
    | 'objectRelationType'
    | 'objectTable'
    | 'nebulaDeploy'
    | 'tDsWorkerGroup'
    | 'tDsProcessDefinition'
    | 'tDsResources'
    | 'metricItem'
    | 'bluekingIntegration'
    | 'bluekingDatasource';

  interface DependencyEntity {
    id: string;
    name: string;
    type: ObjType;
    relatedObjId: string;
    relatedObjName: string;
    relatedObjType: ObjType;
    index: number;
  }
}
