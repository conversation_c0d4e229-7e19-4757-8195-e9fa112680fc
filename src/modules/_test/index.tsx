import { Table, TableColumnType } from 'antd';

import { buildClassDecorator, buildPropertyDecorator } from '@/lib';

import { ChartSlider } from '../OpsManagement/components/form/ChartSlider';

interface IUser {
  username: string
  age: number
}

interface BasePropertyMetaData<T> {
  convertor?: (data?: T) => any
}

const basePropertyDecorators =
  buildPropertyDecorator<BasePropertyMetaData<IUser>>();
const BaseClass = buildClassDecorator((metaKey): ClassDecorator => {
  return (Target: any): any => {
    return class extends Target {
      constructor(data: any) {
        super(data);
        const keys = Object.keys(this);
        keys.forEach(key => {
          const { convertor } = Reflect.getMetadata(
            basePropertyDecorators.metaKey,
            this,
            key,
          );

          this[key] = convertor?.(data) ?? data?.[key];
        });
      }
    };
  };
});
const Base = basePropertyDecorators.getDecorator;

interface PropertyColumnMetaData<T> extends TableColumnType<T> {}
const propertyColumnDecorators =
  buildPropertyDecorator<PropertyColumnMetaData<IUser>>();
const ColumnClass = buildClassDecorator((metaKey): ClassDecorator => {
  let caches: Array<PropertyColumnMetaData<IUser>> = [];

  return (Target: any): any => {
    return class TargetWithColumn extends Target {
      static get columns() {
        if (caches.length) {
          return caches;
        }
        const instance = new TargetWithColumn();
        caches = Object.keys(instance).reduce(
          (acc: Array<PropertyColumnMetaData<IUser>>, key) => {
            const metaData = Reflect.getMetadata(
              propertyColumnDecorators.metaKey,
              instance,
              key,
            );
            if (!metaData) {
              return acc;
            }
            acc.push({ ...metaData, key, dataIndex: key });
            return acc;
          },
          [],
        );
        return caches;
      }
    };
  };
});
const Column = propertyColumnDecorators.getDecorator;

@ColumnClass
@BaseClass
class User {
  constructor(data: any) {
    return this;
  }

  static get columns() {
    return [];
  }

  @Column({
    title: '用户名',
  })
  @Base({
    convertor(data) {
      return data?.username;
    },
  })
    name: string = '';

  @Column({
    title: '年龄',
  })
  @Base({})
    age: number = 0;

  @Base({
    convertor(data) {
      return data?.username;
    },
  })
    key: string = '';
}

const TestPage = () => {
  const userList = [
    {
      username: 'marvin',
      age: 12,
    },
    {
      username: 'kafka',
      age: 99,
    },
  ];
  const users = userList.map(u => new User(u));
  const columns = User.columns;

  // console.log({ columns, users });

  return (
    <div className='bg-white h-full overflow-y-auto'>
      {/* <CustomTableTest /> */}
      <ChartSlider />
      <Table dataSource={users} columns={columns} />
    </div>
  );
};

export default TestPage;
