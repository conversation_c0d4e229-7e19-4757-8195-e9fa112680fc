import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Button, message, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { history } from 'umi';

import { RToolTip } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { useDeleteConfirm, useRightsHook } from '@/hooks';
import { basePathname } from '@/routes/wh-layer';
import { BusinessProcessApi } from '@/services';

import { BusinessProcessEditModal } from '../components/BusinessProcessEditModal';
import { fieldsLabel } from '../utils';

interface Props {
  domain?: WH_LAYER.DataDomainEntity;
  customHeader?: () => ReactNode;
  cacheId?: string;
}
// eslint-disable-next-line react/display-name
export const BusinessProcessList = forwardRef(({ domain, customHeader, cacheId }: Props, ref) => {
  const { hasRights } = useRightsHook();
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editItem, setEditItem] = useState<WH_LAYER.BusinessProcessEntity | undefined>(undefined);
  const tableHook = useCustomTableHook({
    pageSize: 20,
    filter: {
      domId: domain?.id,
    },
    sort: {
      updateTime: 'DESC',
    },
    cacheId,
  });

  const { pagination, setTotal, queryParams, handleTableChange } = tableHook;

  const deleteConfirm = useDeleteConfirm({
    delUrl: '/bus-process',
    title: '确定删除业务过程',
  });

  const gotoDetail = (item: WH_LAYER.BusinessProcessEntity) => {
    history.push({
      pathname: `${basePathname}/business-process/detail/${item.id}`,
    });
  };

  const deleteItem = async (item: WH_LAYER.BusinessProcessEntity) => {
    const res = await deleteConfirm(item.id, `确定删除业务过程「${item.name}」？`);
    if (res?.code === '0000') {
      message.success('删除成功');
      fetchData();
    }
  };
  const columns: ColumnsType<WH_LAYER.BusinessProcessEntity> = [
    {
      title: fieldsLabel.code,
      dataIndex: 'code',
      key: 'code',
      width: 200,
      ellipsis: true,
      render(code, { isBuiltIn }) {
        return (
          <div className='flex'>
            <RToolTip content={code} />
            {isBuiltIn && <Tag className='ml-2'>系统</Tag>}
          </div>
        );
      },
    },
    {
      title: `${fieldsLabel.nameEn}/${fieldsLabel.name}`,
      key: 'nameEn',
      render({ nameEn, name }) {
        return `${nameEn} / ${name}`;
      },
      ellipsis: true,
    },
    {
      title: fieldsLabel.domId,
      dataIndex: 'domName',
      key: 'domName',
      align: 'center',
      ellipsis: true,
      render(domName) {
        return domName || '-';
      },
    },
    {
      title: fieldsLabel.updateUserName,
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: fieldsLabel.updateTime,
      dataIndex: 'updateTime',
      key: 'updateTime',
      ellipsis: true,
      width: 160,
      align: 'center',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      align: 'center',
      width: 150,
      render(item) {
        const { isBuiltIn, projectAuth } = item;
        return (
          <>
            <a className='mr-4' onClick={() => gotoDetail(item)}>
              查看
            </a>
            <Button
              type='link'
              disabled={!hasRights('business_process:write', projectAuth)}
              className={'mr-4 p-0'}
              size='small'
              onClick={() => openEditLayer(item)}
            >
              编辑
            </Button>
            <Button
              type='link'
              disabled={!hasRights('business_process:write', projectAuth)}
              size='small'
              className={`${isBuiltIn && 'invisible'} p-0`}
              onClick={() => deleteItem(item)}
            >
              删除
            </Button>
          </>
        );
      },
    },
  ];

  const onCallback = () => {
    fetchData();
    hideEditLayer();
  };

  const openEditLayer = (item?: WH_LAYER.BusinessProcessEntity) => {
    setEditItem(item);
    setOpen(true);
  };

  const hideEditLayer = () => {
    setOpen(false);
  };

  const fetchData = () => {
    setLoading(true);
    BusinessProcessApi.getList(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setTotal(total);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  // 把实例传给ref，然后父组件可以通过ref获取子组件的变量和方法。
  useImperativeHandle(ref, () => ({
    ...tableHook,
    openEditLayer,
    hideEditLayer,
    fetchData,
  }));

  return (
    <div className='h-full flex flex-col overflow-hidden'>
      {customHeader && customHeader()}
      <CustomTable
        dataSource={list}
        columns={columns}
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: '100%' }}
        className='flex-1 overflow-hidden'
      ></CustomTable>
      {open && (
        <BusinessProcessEditModal
          open={open}
          item={editItem}
          domain={domain}
          callback={onCallback}
          oncancel={hideEditLayer}
        />
      )}
    </div>
  );
});
