import { useState } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps, message } from 'antd';
import { history } from 'umi';

import { FlexibleApplicationApi } from '@/services';

interface Props {
  record: STORAGE_CLUSTER_MANAGEMENT_CK.API.StorageClusterItem;
}
type TYPE = 'DETAIL' | 'WORK' | 'OPERATE';

export const DiagnosisDropdown = (props: Props) => {
  const { record } = props;
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const handleClick = async (type: TYPE) => {
    const res = await FlexibleApplicationApi.getList({
      filter: { id: record.appId },
      page: 0,
      size: 20,
    });
    if (res?.code === '0000') {
      const temp = res?.data?.[0] ?? {};
      history.push({
        pathname: `/flexible-application/${temp.id}/diagnosis`,
        search: `?type=${type}&id=${temp.id}&name=${temp.name ?? ''}&trackUrl=${temp.trackUrl ?? ''}&taskStatus=${
          temp.status
        }&yarnApplicationId=${temp.yarnApplicationId}&clusterId=${temp.clusterId}`,
      });
    } else {
      message.error(res?.msg ?? '查询失败');
    }
  };
  const items: MenuProps['items'] = [
    {
      key: 'operateLog',
      label: <div onClick={() => handleClick('OPERATE')}>操作日志</div>,
    },
  ];
  return (
    <Dropdown menu={{ items }} open={dropdownOpen} onOpenChange={() => setDropdownOpen(!dropdownOpen)}>
      <a onClick={() => handleClick('OPERATE')}>
        <span className='mr-1'>诊断</span>
        {dropdownOpen ? <UpOutlined style={{ fontSize: 12 }} /> : <DownOutlined style={{ fontSize: 12 }} />}
      </a>
    </Dropdown>
  );
};
