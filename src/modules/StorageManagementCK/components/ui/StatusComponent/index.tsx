import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';

import { TaskStatus } from '@/constants';
import { ingestionService } from '@/modules/DataIngestion/services';
import { FlexibleApplicationStatus } from '@/modules/FlexibleApplication/constants';
import Request from '@/request';

import './index.less';

interface Props {
  id: string; // 任务id
  pipelineStatus: FlexibleApplicationStatus; // 任务状态
  noCheck?: boolean;
  onChangeStatus?: (status: FlexibleApplicationStatus) => void;
  enumTaskStatus?: Object | undefined;
  keyAlias?: string; //
}

export const StatusComponent: React.FC<Props> = props => {
  const { pipelineStatus, id, noCheck, enumTaskStatus, keyAlias = 'pipelineStatus' } = props;
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState(pipelineStatus);

  const fetchPipelineStatus = async (id: string) => {
    setLoading(true);
    await Request.get(ingestionService.refreshCkStorageClusterUrl(id), {
      headers: {
        'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
        'Jax-Super-Project-Admin': false,
      },
    })
      .then(({ data }) => {
        setStatus(data[keyAlias]);
        props?.onChangeStatus?.(data[keyAlias]);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    setStatus(pipelineStatus);
  }, [pipelineStatus]);

  return loading ? (
    <span className='text-primary'>
      <Spin size='small' className='mr-2' />
      状态检查中
    </span>
  ) : (
    <>
      <span className={`storage-ck-status  ${status}`}>
        {enumTaskStatus ? enumTaskStatus[status] : TaskStatus[status]}
      </span>
      {!noCheck && (
        <i
          onClick={() => {
            fetchPipelineStatus(id);
          }}
          className='iconfont icon-cheak text-primary ml-2 text-sm cursor-pointer'
        ></i>
      )}
    </>
  );
};
