import { useEffect, useState } from 'react';
import { Button, message, Modal, Space } from 'antd';
import { useNavigate } from 'umi';

import { CustomTable, useCustomTableHook } from '@/components';
import { useGlobalHook } from '@/hooks';
import { ingestionService } from '@/modules/DataIngestion/services';
import { CK_STATUS_MAP } from '@/modules/OpsManagement/constants';
import { CK_STORAGE_CLUSTER_COLUMN } from '@/modules/StorageManagementCK/constants';
import { isStartCKStorage } from '@/modules/StorageManagementCK/utils';
import { detailCKPathName, editCKPathName } from '@/routes/storage-management-ck';
import { handleCheckResultResult } from '@/utils/handleCheckResourceResult';

import '@/modules/OpsManagement/pages/StorageTask/CK/index.less';

import { CKStorageSearchForm } from '../components';
import { DiagnosisDropdown } from '../components/ui/DiagnosisDropdown';
import { StatusComponent } from '../components/ui/StatusComponent';

const title = '存储集群管理(CK)';
const description = `存储集群(CK)能够高效利用计算集群资源，以最合理的方式将数据从Kafka写入Clickhouse。
一个存储集群绑定一套Kafka集群和一套Clickhouse集群，通过注册中心向存储集群同步任务配置。`;

const StartButton = (props: {
  record: STORAGE_CLUSTER_MANAGEMENT_CK.API.StorageClusterItem;
  fetchData: () => void;
}) => {
  const { record, fetchData } = props;
  const [loading, setLoading] = useState(false);
  const id = record.id;
  const status = record.status;
  const isStart = isStartCKStorage(status);

  const handleStart = (forceStart: boolean = false) => {
    setLoading(true);
    ingestionService
      .startCkStorageCluster(id, forceStart, {
        headers: {
          'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
          'Jax-Super-Project-Admin': false,
        },
      })
      .catch(result => {
        handleCheckResultResult(result, () => handleStart(true));
      })
      .finally(() => {
        setLoading(false);
        fetchData();
      });
  };

  return (
    <Button
      type='link'
      size='small'
      disabled={!isStart || status === 'STARTING'}
      className='p-0' loading={loading}
      onClick={() => handleStart()}
    >
      启动
    </Button>
  );
};

export const CKStorageList = () => {
  const { setPageInfo, resetPageInfo } = useGlobalHook();
  const navigate = useNavigate();
  const [list, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const { pagination, queryParams, filter, setFilter, setTotal, handleTableChange } = useCustomTableHook({
    sort: {
      updateTime: 'DESC',
    },
    cacheId: 'ckStorageList',
  });

  const handleEdit = (record: STORAGE_CLUSTER_MANAGEMENT_CK.API.StorageClusterItem) => {
    navigate(`${editCKPathName}/${record.id}`);
  };

  const handleDetail = (record: STORAGE_CLUSTER_MANAGEMENT_CK.API.StorageClusterItem) => {
    navigate(`${detailCKPathName}/${record.id}`);
  };

  const handleDelete = (id: string, name: string) => {
    return () => {
      Modal.confirm({
        title: `确认删除${name}?`,
        width: 424,
        onOk: async () => {
          await ingestionService.deleteCkStorageCluster(id, {
            headers: {
              'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
              'Jax-Super-Project-Admin': false,
            },
          });
          message.success(`删除${name}成功`);
          fetchData();
        },
      });
    };
  };

  const handleStop = (id: string, name: string) => {
    return () => {
      Modal.confirm({
        title: `确认停止${name}?`,
        width: 424,
        onOk: async () => {
          await ingestionService.stopCkStorageCluster(id, {
            headers: {
              'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
              'Jax-Super-Project-Admin': false,
            },
          });
          message.success(`停止${name}成功`);
          fetchData();
        },
      });
    };
  };

  const columns = [
    ...CK_STORAGE_CLUSTER_COLUMN,
    {
      title: '状态',
      dataIndex: 'status',
      render: (value: OPS_MANAGEMENT.STORAGE.CK.Status, record) => {
        return (
          <StatusComponent
            id={record.id}
            pipelineStatus={value}
            enumTaskStatus={CK_STATUS_MAP}
            keyAlias='status'
            onChangeStatus={state => {
              setList(list => {
                return list.map(item => {
                  if (item.id === record.id) {
                    item.status = state;
                  }
                  return {
                    ...item,
                  };
                });
              });
            }}
          />
        );
      },
    },
    {
      title: '集群资源配额',
      dataIndex: 'sourceConfig',
      render: (_, record) => {
        const cpu = Number(record.cpuNum);
        const hiz = (Number(record.memory) / 1024).toFixed(0);
        const instance = Number(record.instanceNum);
        const labels = [record.clusterName, record.runningCluster, record.resourceQueue].filter(Boolean);

        const mainInfo = `${cpu * instance}核${+hiz * instance}G`;
        const descInfo = `(${cpu}核/${hiz}G x ${instance}实例)`;

        return (
          <div className='flex flex-col'>
            <div>
              <span>运行集群：</span>
              <span className='text-gray-5'>{`${labels}`}</span>
            </div>
            <div>
              <span>资源：</span>
              <span className='text-gray-5'>{`${mainInfo}${descInfo}`}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        const id = record.id;
        const status = record.status;
        const isStart = isStartCKStorage(status);

        return (
          <Space>
            <DiagnosisDropdown record={record} />
            <StartButton record={record} fetchData={fetchData} />
            {!isStart && status !== 'STOPPING' && <a onClick={handleStop(id, record.name)}>停止</a>}
            {isStart && <a onClick={() => handleEdit(record)}>编辑</a>}
            {isStart && <a onClick={handleDelete(id, record.name)}>删除</a>}
            {status == 'RUNNING' && <a onClick={() => handleDetail(record)}>查看</a>}
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    setPageInfo({ title, description });
    return () => {
      resetPageInfo();
    };
  }, []);

  const fetchData = () => {
    setLoading(true);
    ingestionService
      .queryCKStorageClusterList(queryParams, {
        headers: {
          'Jax-Super-Project-Manager-Id': localStorage.getItem('__CURRENT_PROJECT__'),
          'Jax-Super-Project-Admin': false,
        },
      })
      .then(({ data, total }) => {
        setList(data ?? []);
        setTotal(total);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  return (
    <div className='w-full h-full bg-white flex flex-col'>
      <CKStorageSearchForm filter={filter} onSearch={setFilter} onReFetch={fetchData} />
      <div className='flex-1 overflow-y-hidden'>
        <CustomTable
          loading={loading}
          rowKey='id'
          scroll={true}
          pagination={pagination}
          dataSource={list}
          columns={columns}
          onChange={handleTableChange}
        />
      </div>
    </div>
  );
};
