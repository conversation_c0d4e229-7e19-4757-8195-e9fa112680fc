declare namespace STORAGE_CLUSTER_MANAGEMENT_CK {
  namespace API {
    interface StorageClusterItem {
      clusterId: string;
      id: string;
      name: string;
      appId: string;
      appName: string;
      appStatus: OPS_MANAGEMENT.STORAGE.CK.Status;
      kafkaDsId: string;
      ckDsId: string;
      ckMaxConnection: number;
      registerCenterId: string;
      nacosGroupName: string;
      nacosDataId: string;
      runningCluster: string;
      clusterName: string;
      resourceQueue: string;
      instanceNum: number;
      cpuNum: number;
      memory: number;
      settingMap: unknown;
      status: OPS_MANAGEMENT.STORAGE.CK.Status;
      isPublished: number;
      lastStartTime: string;
      errMsg: string;
      script: string;
      createTime: string;
      updateTime: string;
      createUserName: null;
      updateUserName: null;
      enableTaskRebalanced?: boolean;
    }

    interface CreateCKStorageClusterParams {
      name: string;
      kafkaDsId: number;
      ckDsId: number;
      ckMaxConnection: number;
      registerCenterId: number;
      nacosGroupName: string;
      runningCluster: string;
      resourceQueue: string;
      instanceNum: number;
      cpuNum: number;
      memory: number;
      settingMap: Array<Record<string, Record<string, string>>>;
      forceStart?: boolean;
    }

    interface DataSourceItem {
      id: string;
      code: string;
      name: string;
      platform: string;
      status: string;
      connectMode: string;
      connectDetail: string;
      lastConnectTime: string;
      description: string;
      registerCenterId: string;
      setting: string;
      paramMap: Record<string, any>;
      dataCenterList: DATA_INGESTION.CELL.DataCenterListItem[];
      cellRespList: GATEWAY.ListItem[];
      createTime: string;
      updateTime: string;
      createUserName: string;
      updateUserName: string;
    }
  }

  namespace CREATE_EDIT_STORAGE_CLUSTER {
    type Form = API.CreateCKStorageClusterParams & {
      nacosDataId?: string;
    };

    interface ClusterList {
      // 运行集群
      id: string;
      clusterName: string;
      clusterType: string;
      clusterDescription: string;
      defaultFlinkCluster: boolean;
      defaultSparkCluster: boolean;
      defaultMarayarnCluster: boolean;
      flinkOptsId: string;
      sparkOptsId: string;
      marayarnOptsId: string;
      setting: {};
      createTime: string;
      updateTime: string;
      createUserName: string;
      updateUserName: string;
      resourcePool: {};
    }
  }

  namespace STORAGE_CLUSTER_LIST {
    interface Form {
      name?: string;
      kafkaDsId?: string;
      ckDsId?: string;
      status?: OPS_MANAGEMENT.STORAGE.CK.Status;
    }
  }

  interface Store {
    currentNode?: API.StorageClusterItem;
    setCurrentNode: (node: API.StorageClusterItem) => void;
  }
}
