import { Link } from 'umi';

export const CK_STORAGE_CLUSTER_COLUMN: Array<TableColumn<STORAGE_CLUSTER_MANAGEMENT_CK.API.StorageClusterItem>> = [
  {
    title: '集群名',
    dataIndex: 'name',
    render: (value, record) => {
      return <Link to={`/platform-management/cluster/storage-management-ck/detail/${record.id}`}>
        {value}
      </Link>;
    },
  },
  {
    title: 'Kafka数据源',
    dataIndex: 'kafkaDsName',
  },
  {
    title: 'Clickhouse数据源',
    dataIndex: 'ckDsName',
  },
  {
    title: '配置中心',
    dataIndex: 'nacosName',
  },
];
