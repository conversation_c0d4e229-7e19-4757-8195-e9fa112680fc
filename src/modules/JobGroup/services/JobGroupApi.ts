import Request from '@/request';

import { JobGroupEntity } from '../models/JobGroupModel';

const url = '/api/v2/ingestion/job-group';
export const JobGroupApi = {
  /**
   * 算子分组列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 创建算子分组
   */
  async create(data: Omit<JobGroupEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新算子分组
   * @param data
   * @returns
   */
  async update(data: JobGroupEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除算子分组
   * @param id 算子分组id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取算子分组详情
   * @param id 算子分组id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部算子分组
   * @returns
   */
  async getAll(): Promise<Response<JobGroupEntity[]>> {
    return await Request.get(`${url}/list`);
  },
};
