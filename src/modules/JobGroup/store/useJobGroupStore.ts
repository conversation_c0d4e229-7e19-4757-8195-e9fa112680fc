import { create } from 'zustand';

import { JobGroupEntity } from '../models/JobGroupModel';
import { JobGroupApi } from '../services/JobGroupApi';

interface JobStoreState {
  loading: boolean;
  list: JobGroupEntity[] | null;
  fetchData: (refresh?: boolean) => Promise<void>;
}
export const useJobGroupStore = create<JobStoreState>((set, get) => ({
  list: null,
  loading: false,
  async fetchData(refresh?: boolean) {
    const { list } = get();
    if (!refresh && list !== null) return;
    set({
      loading: true,
    });
    const { data } = await JobGroupApi.getAll().finally(() => set({ loading: false }));
    set({
      list: data,
    });
  },
}));
