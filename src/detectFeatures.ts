/* eslint-disable max-len */

/* eslint-disable @typescript-eslint/naming-convention */
import { format } from 'date-fns';
import platform from 'platform';

declare let __PROJECT_NAME__: string;
declare let __PROJECT_DISPLAY_NAME__: string;
declare let __PROJECT_ENVIRONMENT__: 'dev' | 'prod';
declare let __PROJECT_VERSION__: string;
declare let __PROJECT_COMMITS_SINCE_RELEASE__: number;
declare let __PROJECT_COMPILE_TIME__: number;
declare let __PROJECT_COMMIT_SHA__: string;

void (function detectEnv() {
  console.group('版本号');
  console.info(
    `${__PROJECT_DISPLAY_NAME__} (${__PROJECT_NAME__})，运行在 ${__PROJECT_ENVIRONMENT__} 环境，编译时间：${format(
      Number(__PROJECT_COMPILE_TIME__),
      'yyyy-MM-dd\'T\'HH:mm:ssXXXXX',
    )}`,
  );
  console.info(
    `发布版本：${__PROJECT_VERSION__}；最后提交：${__PROJECT_COMMIT_SHA__}${
      __PROJECT_COMMITS_SINCE_RELEASE__ ? `，超前发布版本 ${__PROJECT_COMMITS_SINCE_RELEASE__} 个提交` : ''
    }`,
  );
  console.info(`运行在：${platform.description}`);
  console.groupEnd();
})();

void (function detectOS() {
  let OSName = 'Unknown';
  if (navigator.userAgent.includes('Win')) {
    OSName = 'Windows';
  } else if (navigator.userAgent.includes('Mac')) {
    OSName = 'macOS';
  } else if (navigator.userAgent.includes('X11')) {
    OSName = 'UNIX';
  } else if (navigator.userAgent.includes('Linux')) {
    OSName = 'Linux';
  }

  document.documentElement.classList.add('os-' + OSName.toLowerCase());
})();

void (function detectLayout() {
  document.documentElement.classList.add(`platform-${platform.layout.toLowerCase()}`);
})();

void (function detectWebpSupport() {
  const canvas = document.createElement('canvas');
  canvas.width = canvas.height = 1;
  if (canvas.toDataURL('image/webp').indexOf('image/webp') === 5) {
    document.documentElement.classList.add('webp');
  } else {
    document.documentElement.classList.add('no-webp');
  }
})();
