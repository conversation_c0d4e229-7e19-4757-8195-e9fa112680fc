import { useMemo } from 'react';
import { QueryFunction, QueryKey, UseQueryOptions } from '@tanstack/react-query';

import { useQuery } from '@/hooks/api/useQuery';

interface Params<
  TQueryFnData extends any[] = [],
  TError = unknown,
  TData extends any[] = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
> {
  queryKey: TQueryKey;
  queryFunc: QueryFunction<TQueryFnData, TQueryKey>;
  queryOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>;
  dataToOptionConvertor: (data: TData) => SelectOptions;
}

type QueryOptions<TQueryFnData, TError, TData, TQueryKey extends QueryKey = QueryKey> = Omit<
  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
  'initialData'
> & {
  initialData?: () => undefined;
};

export const useSelect = <
  TQueryFnData extends any[] = [],
  TError = unknown,
  TData extends any[] = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
    params: Params<TQueryFnData, TError, TData, TQueryKey>,
  ) => {
  const { queryKey, queryOptions, queryFunc, dataToOptionConvertor } = params;
  const { data = [], isLoading } = useQuery<TQueryFnData, TError, TData, TQueryKey>({
    queryKey,
    queryFunc,
    queryOptions,
  });

  const options = useMemo(() => dataToOptionConvertor(data as TData), [data, dataToOptionConvertor]);

  return {
    options: options ?? [],
    isLoading,
  };
};

export default useSelect;
