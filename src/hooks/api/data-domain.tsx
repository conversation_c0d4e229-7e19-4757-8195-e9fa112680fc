import { Icon } from '@/components';
import { treeStructureToTreeSelect } from '@/modules/DimensionModeling/utils';
import { DataDomainApi } from '@/services';

import { QueryOptions, useQuery } from './useQuery';

export function useDataDomains(opt?: QueryOptions<DataDomain[]>) {
  const { data = [], isLoading } = useQuery<DataDomain[]>({
    queryKey: [DataDomainApi.getAllDomainUrl],
    queryFunc: () => DataDomainApi.getAllDomain().then(({ data }) => data),
    queryOptions: opt,
  });

  return { dataDomains: data, isLoading };
}

export function useDataDomainsTreeSelectTreeData(opt?: QueryOptions<DataDomain[]>) {
  const { dataDomains, isLoading } = useDataDomains(opt);

  const treeData =
    treeStructureToTreeSelect(
      null,
      ({ name, id, nameEn }) => ({
        title: (
          <span title={name} className='w-full overflow-hidden truncate flex items-center py-1'>
            <Icon name='earth-fill' size={14} className='mr-1' /> {name}
          </span>
        ),
        nameEn: nameEn || '',
        value: id,
      }),
      dataDomains,
    ) ?? [];

  return { treeData, isLoading };
}
