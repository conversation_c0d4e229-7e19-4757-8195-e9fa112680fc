import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Column, ColumnOptions } from '@antv/g2plot';
import { merge } from 'lodash';

interface Props {
  data: any;
  range: string[];
  options?: Partial<ColumnOptions>;
  isRealTime?: Boolean;
  onClick?: (value) => void;
}

export const ColumnChart: React.FC<Props> = props => {
  const { data, range, options, isRealTime, onClick } = props;

  const container = useRef<any>();
  const [chart, setChart] = useState<Column>();

  const config: ColumnOptions = useMemo(() => {
    return merge(
      {
        data,
        isGroup: true,
        xField: 'xAxis',
        yField: 'yAxis',
        seriesField: 'seriesField',
        color: ['rgba(255,77,79,0.85)', 'rgba(250,173,20,0.85)', 'rgba(9,109,217,0.85)'],
        dodgePadding: 2, // 分组柱状图 组内柱子间的间距 (像素级别)
        legend: {
          position: 'bottom-left',
        },
        tooltip: {
          title: (x, record) => record.title || x,
        },
      },
      options,
    );
  }, [isRealTime, range, options]);

  const init = () => {
    const column = new Column(container.current, config);
    column.render();
    column.on('element:click', ({ data: { data } }) => {
      onClick?.(data);
    });
    setChart(column);
  };

  useEffect(() => {
    if (!isRealTime) {
      chart?.update(config);
    }
  }, [config]);

  useEffect(() => {
    chart?.changeData(data);
  }, [data]);

  useEffect(() => {
    init();
    return () => {
      chart?.destroy();
    };
  }, []);

  return <div className='w-full h-full' ref={container}></div>;
};
