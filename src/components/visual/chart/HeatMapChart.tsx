import React, { useEffect, useMemo, useRef, useState } from 'react';
import colorUtil from '@antv/color-util';
import { Heatmap, HeatmapOptions } from '@antv/g2plot';
import { merge } from 'lodash';
import moment from 'moment';

import { defaultTickMethod, timeTickMethod } from '@/modules/OpsManagement/pages/StorageTask/CK/helper';
import { getTimeFormatFromRange, getXAxisTimeFormatter } from '@/utils';

interface Props {
  data: any;
  range: string[];
  options: Partial<HeatmapOptions>;
  isRealTime?: Boolean;
}

export const HeatMapChart: React.FC<Props> = props => {
  const { data, range, options, isRealTime } = props;

  const container = useRef();
  const [chart, setChart] = useState<Heatmap>();

  const maxValue = useMemo(() => {
    return Math.max(...data.map(x => x.value));
  }, [data]);

  const getColor = (value: number) => {
    const colors = ['#BAE7FF', '#1890FF', '#0050B3'];
    return colorUtil.toRGB(colorUtil.gradient(colors)((value * 100) / maxValue / 100));
  };

  const config: HeatmapOptions = useMemo(() => {
    const [beginTime, endTime] = range;
    const minX = moment(beginTime).valueOf();
    const maxX = moment(endTime).valueOf();
    return merge(
      {
        data,
        color: data => {
          if (data.value === 0) {
            return '#fff';
          } else {
            return getColor(data.value);
          }
        },
        autoFit: true,
        padding: 'auto',
        xField: 'x',
        yField: 'y',
        legend: {
          position: 'bottom',
        },
        xAxis: {
          tickCount: 5,
          type: 'cat',
          label: {
            autoRotate: true,
            autoHide: true,
            formatter: (value: string) => {
              return moment(value).format(getTimeFormatFromRange(minX, maxX));
            },
          },
        },
        tooltip: {
          showMarkers: true,
          enterable: true,
        },
      },
      options,
    );
  }, [isRealTime, range, options]);

  const init = () => {
    const heatMap = new Heatmap(container.current!, config);
    heatMap.render();
    setChart(heatMap);
  };

  useEffect(() => {
    if (!isRealTime) {
      chart?.update(config);
    }
  }, [config]);

  useEffect(() => {
    chart?.changeData(data);
  }, [data]);

  useEffect(() => {
    init();
    return () => {
      chart?.destroy();
    };
  }, []);

  return <div className='w-full h-full' ref={container}></div>;
};
