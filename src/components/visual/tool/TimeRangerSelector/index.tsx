import React, { useEffect, useMemo, useState } from 'react';
import { CalendarOutlined, DownOutlined } from '@ant-design/icons';
import { Button, DatePicker, Dropdown, DropdownProps, Form, Input, MenuProps, message, theme } from 'antd';
import { ItemType } from 'antd/es/menu/hooks/useItems';
import { Dayjs } from 'dayjs';
import { isEqual } from 'lodash';

import { getRealTime } from './helper';

const { useToken } = theme;

interface Props extends DropdownProps {
  range?: Range;
  onChange?: (range: Range) => void;
}

const OPTIONS = [
  {
    label: '最近 5 分钟',
    key: 'latest 5 minute',
    value: ['now-5m', 'now'],
  },
  {
    label: '最近 15 分钟',
    key: 'latest 15 minute',
    value: ['now-15m', 'now'],
  },
  {
    label: '最近 30 分钟',
    key: 'latest 30 minute',
    value: ['now-30m', 'now'],
  },
  {
    label: '最近 1 小时',
    key: 'latest 1 hour',
    value: ['now-1h', 'now'],
  },
  {
    label: '最近 3 小时',
    key: 'latest 3 hour',
    value: ['now-3h', 'now'],
  },
  {
    label: '最近 6 小时',
    key: 'latest 6 hour',
    value: ['now-6h', 'now'],
  },
  {
    label: '最近 12 小时',
    key: 'latest 12 hour',
    value: ['now-12h', 'now'],
  },
  {
    label: '最近 24 小时',
    key: 'latest 24 hour',
    value: ['now-24h', 'now'],
  },
  {
    label: '最近 7 天',
    key: 'latest 7 day',
    value: ['now-7d', 'now'],
  },
  {
    label: '最近 30 天',
    key: 'latest 30 day',
    value: ['now-30d', 'now'],
  },
  {
    label: '最近 60 天',
    key: 'latest 60 day',
    value: ['now-60d', 'now'],
  },
  {
    label: '最近 6 个月',
    key: 'latest 6 month',
    value: ['now-6M', 'now'],
  },
  {
    label: '最近 1 年',
    key: 'latest 1 year',
    value: ['now-1y', 'now'],
  },
  {
    label: '最近 2 年',
    key: 'latest 2 year',
    value: ['now-2y', 'now'],
  },
  {
    label: '最近 5 年',
    key: 'latest 5 year',
    value: ['now-5y', 'now'],
  },
];

interface ValidateStatus {
  status: Parameters<typeof Form.Item>[0]['validateStatus'];
  error?: string;
}

export type Range = [string, string];

export const TimeRangeSelector: React.FC<Props> = props => {
  const { onChange, ...otherProps } = props;
  const [open, setOpen] = useState(false);
  const { token } = useToken();
  const [range, setRange] = useState<Range>(props.range ?? ['now-5m', 'now']);
  const [beginStatus, setBeginStatus] = useState<ValidateStatus>({
    status: undefined,
    error: '',
  });
  const [endStatus, setEndStatus] = useState<ValidateStatus>({
    status: undefined,
    error: '',
  });

  const items: MenuProps['items'] = useMemo<MenuProps['items']>(() => {
    return OPTIONS.map(item => {
      const newItem: ItemType = {
        ...item,
      };
      if (isEqual(item.value, range)) {
        newItem.label = <span className='text-primary'>{item.label}</span>;
      }
      return newItem;
    });
  }, [range]);

  // 显示时间标题
  const rangeDesc = useMemo(() => {
    const item = OPTIONS.find(item => isEqual(item.value, range));
    if (item) {
      return item.label;
    } else {
      return `${range[0]} 至 ${range[1]}`;
    }
  }, [range]);

  useEffect(() => {
    if (isEqual(props.range, range)) return;
    if (!props.range) return;
    setRange(props.range);
  }, [props.range]);

  // 点击快捷时间区间菜单事件
  const onClickItem: MenuProps['onClick'] = ({ key }) => {
    const value = OPTIONS.find(item => item.key === key)?.value as Range;
    setRange(value);
    onChange?.(value);
    setOpen(false);
  };

  // 自定义 选择时间区间
  const onChangeDate = (value: [Dayjs, Dayjs]) => {
    if (!value) return;
    const start = value[0].set('hour', 0).set('minute', 0).set('second', 0).format('YYYY-MM-DD HH:mm:ss');
    const end = value[1].set('hour', 23).set('minute', 59).set('second', 59).format('YYYY-MM-DD HH:mm:ss');
    setRange([start, end]);
  };

  const contentStyle = {
    backgroundColor: token.colorBgElevated,
    borderRadius: token.borderRadiusLG,
    boxShadow: token.boxShadowSecondary,
    width: 428,
  };

  const menuStyle = {
    boxShadow: 'none',
  };

  const validateRange = (range: Range, type?: 'begin' | 'end') => {
    const [start, end] = range;
    try {
      const beginTime = getRealTime(start);
      const endTime = getRealTime(end);
      if (beginTime.isAfter(endTime)) {
        if (type === 'begin') {
          setBeginStatus({
            status: 'error',
            error: '开始时间需 早于 结束时间',
          });
        } else if (type === 'end') {
          setEndStatus({
            status: 'error',
            error: '结束时间需 晚于 开始时间',
          });
        }
        return false;
      } else {
        if (type === 'begin') {
          setBeginStatus({
            status: 'success',
          });
        } else if (type === 'end') {
          setEndStatus({
            status: 'success',
          });
        }
      }
      return true;
    } catch (e) {
      if (e.message) {
        if (type === 'begin') {
          setBeginStatus({
            status: 'error',
            error: e.message,
          });
        } else if (type === 'end') {
          setEndStatus({
            status: 'error',
            error: e.message,
          });
        }
      }
      return false;
    }
  };

  const handleClick = () => {
    setOpen(false);
  };

  useEffect(() => {
    window.addEventListener('click', handleClick);
    return () => {
      window.removeEventListener('click', handleClick);
    };
  }, []);

  // 时间区间弹层
  const dropdownRender = menu => (
    <div style={contentStyle} className='flex justify-stretch' onClick={e => e.stopPropagation()}>
      <div className='border-l-0 border-t-0 border-b-0 border-r border-solid border-gray-1 h-96 overflow-y-auto'>
        {React.cloneElement(menu as React.ReactElement, { style: menuStyle })}
      </div>
      <div className='p-3 px-3 flex-1 relative'>
        <div className='mb-5'>自定义时间范围</div>
        <Form layout='vertical'>
          <Form.Item label='开始时间' validateStatus={beginStatus.status} help={beginStatus.error}>
            <Input
              value={range[0]}
              onChange={e => {
                const newRange: Range = [...range];
                newRange[0] = e.target.value;
                validateRange(newRange, 'begin');
                setRange(newRange);
              }}
              suffix={
                <div className='relative cursor-pointer'>
                  <CalendarOutlined className='cursor-pointer' />
                  <DatePicker.RangePicker
                    onChange={onChangeDate}
                    className='w-0 absolute -top-[5px] right-0 opacity-0'
                    onClick={e => {
                      e.stopPropagation();
                    }}
                  />
                </div>
              }
            />
          </Form.Item>
          <Form.Item label='结束时间' validateStatus={endStatus.status} help={endStatus.error}>
            <Input
              value={range[1]}
              onChange={e => {
                const newRange: Range = [...range];
                newRange[1] = e.target.value;
                validateRange(newRange, 'end');
                setRange(newRange);
              }}
              suffix={
                <div className='relative cursor-pointer'>
                  <CalendarOutlined className='cursor-pointer' />
                  <DatePicker.RangePicker
                    onChange={onChangeDate}
                    className='w-0 absolute -top-[5px] right-0 opacity-0'
                    onClick={e => {
                      e.stopPropagation();
                    }}
                  />
                </div>
              }
            />
          </Form.Item>
        </Form>

        <div className='absolute bottom-3 right-3'>
          <Button
            type='primary'
            className='mt-4'
            onClick={() => {
              if (validateRange(range)) {
                onChange?.(range);
                setOpen(false);
              } else {
                message.warning('请选择有效时间区间');
              }
            }}
          >
            确定
          </Button>
        </div>
      </div>
    </div>
  );
  return (
    <Dropdown open={open} menu={{ items, onClick: onClickItem }} dropdownRender={dropdownRender} {...otherProps}>
      <div
        className='flex border-1 items-center h-8 px-3 cursor-pointer'
        onClick={e => {
          e.stopPropagation();
          setOpen(true);
        }}
      >
        <i className='iconfont icon-timer-line text-sm mr-2'></i>
        <div>{rangeDesc}</div>
        <DownOutlined className='text-xs ml-2 text-gray-5' />
      </div>
    </Dropdown>
  );
};
