import moment from 'moment';

/**
 * Calculate tick step.
 * Implementation from d3-array (ticks.js)
 * https://github.com/d3/d3-array/blob/master/src/ticks.js
 * @param start Start value
 * @param stop End value
 * @param count Ticks count
 */
export function tickStep(start: number, stop: number, count: number): number {
  const e10 = Math.sqrt(50);
  const e5 = Math.sqrt(10);
  const e2 = Math.sqrt(2);

  const step0 = Math.abs(stop - start) / Math.max(0, count);
  let step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10));
  const error = step0 / step1;

  if (error >= e10) {
    step1 *= 10;
  } else if (error >= e5) {
    step1 *= 5;
  } else if (error >= e2) {
    step1 *= 2;
  }

  return stop < start ? -step1 : step1;
}

export function getScaledDecimals(decimals: number, tickSize: number) {
  return decimals - Math.floor(Math.log(tickSize) / Math.LN10);
}

/**
 * Calculate tick size based on min and max values, number of ticks and precision.
 * Implementation from Flot.
 * @param min           Axis minimum
 * @param max           Axis maximum
 * @param noTicks       Number of ticks
 * @param tickDecimals  Tick decimal precision
 */
export function getFlotTickSize(min: number, max: number, noTicks: number, tickDecimals = 2) {
  const delta = (max - min) / noTicks;
  let dec = -Math.floor(Math.log(delta) / Math.LN10);
  const maxDec = tickDecimals;

  const magn = Math.pow(10, -dec);
  const norm = delta / magn; // norm is between 1.0 and 10.0
  let size;

  if (norm < 1.5) {
    size = 1;
  } else if (norm < 3) {
    size = 2;
    // special case for 2.5, requires an extra decimal
    if (norm > 2.25 && (maxDec == null || dec + 1 <= maxDec)) {
      size = 2.5;
      ++dec;
    }
  } else if (norm < 7.5) {
    size = 5;
  } else {
    size = 10;
  }

  size *= magn;

  return size;
}

/**
 * Calculate axis range (min and max).
 * Implementation from Flot.
 */
export function getFlotRange(panelMin: any, panelMax: any, datamin: number, datamax: number) {
  const autoscaleMargin = 0.02;

  let min = +(panelMin != null ? panelMin : datamin);
  let max = +(panelMax != null ? panelMax : datamax);
  const delta = max - min;

  if (delta === 0.0) {
    // Grafana fix: wide Y min and max using increased wideFactor
    // when all series values are the same
    const wideFactor = 0.25;
    const widen = Math.abs(max === 0 ? 1 : max * wideFactor);

    if (panelMin === null) {
      min -= widen;
    }
    // always widen max if we couldn't widen min to ensure we
    // don't fall into min == max which doesn't work
    if (panelMax == null || panelMin != null) {
      max += widen;
    }
  } else {
    // consider autoscaling
    const margin = autoscaleMargin;
    if (margin != null) {
      if (panelMin == null) {
        min -= delta * margin;
        // make sure we don't go below zero if all values
        // are positive
        if (min < 0 && datamin != null && datamin >= 0) {
          min = 0;
        }
      }
      if (panelMax == null) {
        max += delta * margin;
        if (max > 0 && datamax != null && datamax <= 0) {
          max = 0;
        }
      }
    }
  }
  return { min, max };
}

/**
 * Calculate tick decimals.
 * Implementation from Flot.
 */
export function getFlotTickDecimals(datamin: number, datamax: number, axis: { min: any; max: any }, height: number) {
  const { min, max } = getFlotRange(axis.min, axis.max, datamin, datamax);
  const noTicks = 0.3 * Math.sqrt(height);
  const delta = (max - min) / noTicks;
  const dec = -Math.floor(Math.log(delta) / Math.LN10);

  const magn = Math.pow(10, -dec);
  // norm is between 1.0 and 10.0
  const norm = delta / magn;
  let size;

  if (norm < 1.5) {
    size = 1;
  } else if (norm < 3) {
    size = 2;
    // special case for 2.5, requires an extra decimal
    if (norm > 2.25) {
      size = 2.5;
    }
  } else if (norm < 7.5) {
    size = 5;
  } else {
    size = 10;
  }
  size *= magn;

  const tickDecimals = Math.max(0, -Math.floor(Math.log(delta) / Math.LN10) + 1);
  // grafana addition
  const scaledDecimals = tickDecimals - Math.floor(Math.log(size) / Math.LN10);
  return { tickDecimals, scaledDecimals };
}

/**
 * Format timestamp similar to Grafana graph panel.
 * @param ticks Number of ticks
 * @param min Time from (in milliseconds)
 * @param max Time to (in milliseconds)
 */
export function grafanaTimeFormat(ticks: number, min: number, max: number) {
  if (min && max && ticks) {
    const range = max - min;
    const secPerTick = range / ticks / 1000;
    const oneDay = 86400000;
    const oneYear = 31536000000;

    if (secPerTick <= 45) {
      return 'HH:mm:ss';
    }
    if (secPerTick <= 7200 || range <= oneDay) {
      return 'HH:mm';
    }
    if (secPerTick <= 80000) {
      return 'HH:mm';
    }
    if (secPerTick <= 2419200 || range <= oneYear) {
      return 'MM/DD';
    }
    return 'YYYY-MM';
  }

  return 'HH:mm';
}

/**
 * Logarithm of value for arbitrary base.
 */
export function logp(value: number, base: number) {
  return Math.log(value) / Math.log(base);
}

/**
 * Get decimal precision of number stored as a string ("3.14" => 2)
 */
export function getStringPrecision(num: string): number {
  if (isNaN(num as unknown as number)) {
    return 0;
  }

  const dotIndex = num.indexOf('.');
  if (dotIndex === -1) {
    return 0;
  } else {
    return num.length - dotIndex - 1;
  }
}

/**
 * Get decimal precision of number (3.14 => 2)
 */
export function getPrecision(num: number): number {
  const str = num.toString();
  return getStringPrecision(str);
}

export function byteConvert(bytes, decimal = 2) {
  if (isNaN(bytes)) {
    return null;
  }

  // 在这里定义了常用的字节，字节、千字节、兆字节、吉字节、太字节、拍字节、艾字节、Z字节、Y字节
  const symbols = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  let exp = Math.floor(Math.log(Math.abs(bytes)) / Math.log(2));

  if (exp < 1) {
    exp = 0;
  }

  const i = Math.floor(exp / 10);

  bytes /= Math.pow(2, 10 * i);

  if (bytes.toString().length > bytes.toFixed(decimal).toString().length) {
    bytes = bytes.toFixed(decimal);
  }

  return {
    bytes,
    symbol: symbols[i],
    unit: Math.pow(2, 10 * i),
    display: bytes ? `${bytes}${symbols[i]}` : '',
  };
}
const toUtc = (input?, formatInput?) => {
  return moment.utc(input, formatInput);
};
type DecimalCount = number | null | undefined;
interface FormattedValue {
  text: string;
  prefix?: string;
  suffix?: string;
}
export function toClock(size: number, decimals?: DecimalCount): FormattedValue {
  if (size === null) {
    return { text: '' };
  }

  // < 1 second
  if (size < 1000) {
    return {
      text: toUtc(size).format('SSS \\m\\s'),
    };
  }

  // < 1 minute
  if (size < 60000) {
    let format = 'ss\\s:SSS\\m\\s';
    if (decimals === 0) {
      format = 'ss\\s';
    }
    return { text: toUtc(size).format(format) };
  }

  // < 1 hour
  if (size < 3600000) {
    let format = 'mm\\m:ss\\s:SSS\\m\\s';
    if (decimals === 0) {
      format = 'mm\\m';
    } else if (decimals === 1) {
      format = 'mm\\m:ss\\s';
    }
    return { text: toUtc(size).format(format) };
  }

  let format = 'mm\\m:ss\\s:SSS\\m\\s';

  // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
  const hours = `${('0' + Math.floor(moment.duration(size, 'milliseconds').asHours())).slice(-2)}h`;

  if (decimals === 0) {
    format = '';
  } else if (decimals === 1) {
    format = 'mm\\m';
  } else if (decimals === 2) {
    format = 'mm\\m:ss\\s';
  }

  const text = format ? `${hours}:${toUtc(size).format(format)}` : hours;
  return { text };
}
export enum Interval {
  Year = 'year',
  Month = 'month',
  Week = 'week',
  Day = 'day',
  Hour = 'hour',
  Minute = 'minute',
  Second = 'second',
  Millisecond = 'millisecond',
}
const UNITS = [
  Interval.Year,
  Interval.Month,
  Interval.Week,
  Interval.Day,
  Interval.Hour,
  Interval.Minute,
  Interval.Second,
  Interval.Millisecond,
];

export const INTERVALS_IN_SECONDS = {
  [Interval.Year]: 365 * 24 * 3600,
  [Interval.Month]: 30 * 24 * 3600,
  [Interval.Week]: 7 * 24 * 3600,
  [Interval.Day]: 24 * 3600,
  [Interval.Hour]: 3600,
  [Interval.Minute]: 60,
  [Interval.Second]: 1,
  [Interval.Millisecond]: 0.001,
};

export function toDuration(size: number, decimals: DecimalCount, timeScale: Interval): FormattedValue {
  if (size === null) {
    return { text: '' };
  }

  if (size === 0) {
    return { text: '0', suffix: ' ' + timeScale + 's' };
  }

  if (size < 0) {
    const v = toDuration(-size, decimals, timeScale);
    if (!v.suffix) {
      v.suffix = '';
    }
    v.suffix += ' ago';
    return v;
  }

  // convert $size to milliseconds
  // intervals_in_seconds uses seconds (duh), convert them to milliseconds here to minimize floating point errors
  size *= INTERVALS_IN_SECONDS[timeScale] * 1000;

  const strings: string[] = [];

  // after first value >= 1 print only $decimals more
  let decrementDecimals = false;
  let decimalsCount = 0;

  if (decimals !== null && decimals !== undefined) {
    decimalsCount = decimals;
  }

  for (let i = 0; i < UNITS.length && decimalsCount >= 0; i++) {
    const interval = INTERVALS_IN_SECONDS[UNITS[i]] * 1000;
    const value = size / interval;
    if (value >= 1 || decrementDecimals) {
      decrementDecimals = true;
      const floor = Math.floor(value);
      const unit = UNITS[i] + (floor !== 1 ? 's' : '');
      strings.push(`${floor} ${unit}`);
      size = size % interval;
      decimalsCount--;
    }
  }

  return { text: strings.join(', ') };
}

export function fixHalfMonthDay(val) {
  const time = moment(val);
  const days = time.daysInMonth();
  const month = time.date(1).hour(0).minute(0).second(0);

  const currMonth = month.valueOf();
  const halfMonth = month.add(Math.floor(days / 2), 'd').valueOf();
  const nextMonth = time.date(1).hour(0).minute(0).second(0).add(1, 'M').valueOf();

  const array = [currMonth, halfMonth, nextMonth];
  let near: number | null = null;

  for (const item of array) {
    if (near == null || Math.abs(item - val) < Math.abs(near - val)) {
      near = item;
    }
  }
  return near;
}

export function fixMonthDay(val) {
  const time = moment(val);
  const month = time.date(1).hour(0).minute(0).second(0);

  const currMonth = month.valueOf();
  const nextMonth = month.add(1, 'M').valueOf();

  const array = [currMonth, nextMonth];
  let near: number | null = null;

  for (const item of array) {
    if (near == null || Math.abs(item - val) < Math.abs(near - val)) {
      near = item;
    }
  }
  return near;
}

export const gapArray: Array<{ size: number; fixValue?: (val: any) => any }> = [
  { size: 100 }, // 毫秒
  { size: 200 },
  { size: 500 },
  { size: 1000 }, // 秒
  { size: 2 * 1000 },
  { size: 5 * 1000 },
  { size: 10 * 1000 },
  { size: 20 * 1000 },
  { size: 30 * 1000 },
  { size: 60 * 1000 }, // 分
  { size: 2 * 60 * 1000 },
  { size: 5 * 60 * 1000 },
  { size: 10 * 60 * 1000 },
  { size: 15 * 60 * 1000 },
  { size: 20 * 60 * 1000 },
  { size: 30 * 60 * 1000 },
  { size: 3600 * 1000 }, // 时
  { size: 2 * 3600 * 1000 },
  { size: 3 * 3600 * 1000 },
  { size: 4 * 3600 * 1000 },
  { size: 8 * 3600 * 1000 },
  { size: 12 * 3600 * 1000 },
  { size: 24 * 3600 * 1000 }, // 天
  { size: 2 * 24 * 3600 * 1000 },
  { size: 5 * 24 * 3600 * 1000 },
  { size: 7 * 24 * 3600 * 1000 }, // 周
  { size: 10 * 24 * 3600 * 1000 },
  { size: 15 * 24 * 3600 * 1000, fixValue: fixHalfMonthDay },
  { size: 30 * 24 * 3600 * 1000, fixValue: fixMonthDay }, // 月
  { size: 2 * 30 * 24 * 3600 * 1000, fixValue: fixMonthDay },
  { size: 4 * 30 * 24 * 3600 * 1000, fixValue: fixMonthDay },
  { size: 8 * 30 * 24 * 3600 * 1000, fixValue: fixMonthDay },
  { size: 12 * 30 * 24 * 3600 * 1000, fixValue: fixMonthDay }, // 年
];

export function getTimeTickSize(min, max, tickCount): { size: number; fixValue?: (val: number) => number } {
  let gap: null | { size: number; fixValue? } = null;
  const avg = (max - min) / tickCount;

  for (let index = 0; index < gapArray.length - 1; index++) {
    if (avg >= gapArray[index].size && avg <= gapArray[index + 1].size) {
      if (Math.abs(gapArray[index].size - avg) < Math.abs(gapArray[index + 1].size - avg)) {
        gap = gapArray[index];
      } else {
        gap = gapArray[index + 1];
      }
    }
  }

  if (gap == null) {
    gap = gapArray[gapArray.length - 1];
  }
  return gap;
}

export function roundInterval(interval: number) {
  switch (true) {
  // 0.015s
  case interval < 15:
    return 10; // 0.01s
    // 0.035s
  case interval < 35:
    return 20; // 0.02s
    // 0.075s
  case interval < 75:
    return 50; // 0.05s
    // 0.15s
  case interval < 150:
    return 100; // 0.1s
    // 0.35s
  case interval < 350:
    return 200; // 0.2s
    // 0.75s
  case interval < 750:
    return 500; // 0.5s
    // 1.5s
  case interval < 1500:
    return 1000; // 1s
    // 3.5s
  case interval < 3500:
    return 2000; // 2s
    // 7.5s
  case interval < 7500:
    return 5000; // 5s
    // 12.5s
  case interval < 12500:
    return 10000; // 10s
    // 17.5s
  case interval < 17500:
    return 15000; // 15s
    // 25s
  case interval < 25000:
    return 20000; // 20s
    // 45s
  case interval < 45000:
    return 30000; // 30s
    // 1.5m
  case interval < 90000:
    return 60000; // 1m
    // 3.5m
  case interval < 210000:
    return 120000; // 2m
    // 7.5m
  case interval < 450000:
    return 300000; // 5m
    // 12.5m
  case interval < 750000:
    return 600000; // 10m
    // 12.5m
  case interval < 1050000:
    return 900000; // 15m
    // 25m
  case interval < 1500000:
    return 1200000; // 20m
    // 45m
  case interval < 2700000:
    return 1800000; // 30m
    // 1.5h
  case interval < 5400000:
    return 3600000; // 1h
    // 2.5h
  case interval < 9000000:
    return 7200000; // 2h
    // 4.5h
  case interval < 16200000:
    return 10800000; // 3h
    // 9h
  case interval < 32400000:
    return 21600000; // 6h
    // 1d
  case interval < 86400000:
    return 43200000; // 12h
    // 1w
  case interval < 604800000:
    return 86400000; // 1d
    // 3w
  case interval < 1814400000:
    return 604800000; // 1w
    // 6w
  case interval < 3628800000:
    return 2592000000; // 30d
  default:
    return 31536000000; // 1y
  }
}

export function getTimeTickSize2(min, max, tickCount): { size: number; fixValue?: (val: any) => any } {
  const avg = (max - min) / tickCount;
  const size = roundInterval(avg);
  const fixValue = size >= 15 * 24 * 3600 * 1000 ? fixMonthDay : undefined;
  return {
    size,
    fixValue,
  };
}

export function toClockSimple(size: number): FormattedValue {
  if (size === null) {
    return { text: '' };
  }

  // < 1 second
  if (size < 1000) {
    return {
      text: toUtc(size).format('SSS毫秒'),
    };
  }

  // < 1 minute
  if (size < 60000) {
    const format = 's秒';
    return { text: toUtc(size).format(format) };
  }

  // < 1 hour
  if (size < 3600000) {
    const format = 'm分钟';
    return { text: toUtc(size).format(format) };
  }

  // < 24 hour
  if (size < 86400000) {
    const hours = `${Math.floor(moment.duration(size, 'milliseconds').asHours())}小时`;

    return { text: hours };
  }

  // < 365 day
  if (size < 31536000000) {
    const days = `${Math.floor(moment.duration(size, 'milliseconds').asDays())}天`;

    return { text: days };
  }
  const years = `${Math.floor(moment.duration(size, 'milliseconds').asYears())}年`;
  return { text: years };
}

export const formatLargeNumber = (n: number, decimalPlaces = 1) => {
  if (n >= 1e12) {
    return (n / 1e12).toFixed(decimalPlaces) + '万亿';
  }
  if (n >= 1e8) {
    return (n / 1e8).toFixed(decimalPlaces) + '亿';
  }
  if (n >= 1e4) {
    return (n / 1e4).toFixed(decimalPlaces) + '万';
  }
  if (n >= 1e3) {
    return (n / 1e3).toFixed(decimalPlaces) + '千';
  }
  return n;
};

export const numberUnitFormat = (n: number, decimalPlaces = 2) => {
  let num = n;
  let symbol = '';
  if (n >= 1e12) {
    num = (n / 1e12).toFixed(decimalPlaces);
    symbol = '万亿';
  }else if (n >= 1e8) {
    num = (n / 1e8).toFixed(decimalPlaces);
    symbol = '亿';
  }else if (n >= 1e4) {
    num = (n / 1e4).toFixed(decimalPlaces);
    symbol = '万';
  }else if (n >= 1e3) {
    num = (n / 1e3).toFixed(decimalPlaces);
    symbol = '千';
  }

  return {
    num,
    symbol,
    display: `${num}${symbol}`,
  };
};
