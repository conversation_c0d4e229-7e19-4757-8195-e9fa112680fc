/**
 * @module 展开更多
 */
import { PropsWithChildren, useMemo } from 'react';
import { RightOutlined } from '@ant-design/icons';
import { Collapse, CollapseProps } from 'antd';
import cs from 'classnames';

import { isString, uuid } from '@/utils';

import './style.less';

type Props = PropsWithChildren &
  CollapseProps & {
    label: ReactNode;
  };

export const CollapseMore = ({ label, children, className, ...props }: Props) => {
  const key = useMemo(() => uuid(), []);
  const realLabel = isString(label) ? <span className='text-primary'>{label}</span> : label;

  return (
    <Collapse
      ghost
      className={cs('more-collapse', className)}
      expandIcon={({ isActive }) => {
        return (
          <RightOutlined
            className={cs('text-primary transition-all', {
              'transform rotate-90': isActive,
            })}
          />
        );
      }}
      {...props}
    >
      <Collapse.Panel forceRender header={realLabel} key={key}>
        {children}
      </Collapse.Panel>
    </Collapse>
  );
};
