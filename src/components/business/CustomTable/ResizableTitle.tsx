import React from 'react';
import { Resizable } from 'react-resizable';

import { FitArrangeColumns } from './CustomTable';

interface Props {
  width: number | undefined;
  onResize: (info: FitArrangeColumns) => void;
  children: ReactNode;
  style: React.CSSProperties;
}

export const ResizableTitle = (props: Props) => {
  const { onResize, width, ...restProps } = props;
  if (!width) {
    return <th {...restProps} />;
  }
  return (
    <Resizable
      width={Number(width)}
      height={0}
      handle={
        <span
          className='react-resizable-handle'
          onClick={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
        />
      }
      onResizeStart={() => {
        document.body.onselectstart = () => false;
      }}
      onResize={onResize}
      onResizeStop={() => {
        document.body.onselectstart = () => true;
      }}
      draggableOpts={{
        enableUserSelectHack: false,
      }}
    >
      <th
        {...restProps}
        style={{
          overflow: 'visible',
          ...restProps.style,
        }}
      >
        <div
          style={{
            width: '100%',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
          }}
        >
          {restProps.children}
        </div>
      </th>
    </Resizable>
  );
};
