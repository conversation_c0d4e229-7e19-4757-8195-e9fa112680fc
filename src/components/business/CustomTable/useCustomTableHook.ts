import { useEffect, useMemo, useState } from 'react';
import { cloneDeep, isEqual } from 'lodash';

import { useCustomTableStore } from './useCustomTableStore';

interface Props {
  pageSize?: number;
  filter?: Record<string, any>;
  sort?: Record<string, SortTypes>;
  cacheId?: string;
}
export const useCustomTableHook = ({ pageSize, filter: initFilter, sort: initSort, cacheId: initCacheId }: Props) => {
  const [cacheId] = useState(initCacheId || `${(Math.random() * 10000).toFixed(0)}`);
  const store = useCustomTableStore();

  const initialData = {
    filter: {
      ...initFilter,
    },
    sort: {
      ...initSort,
    },
    pageSize: pageSize ?? 20,
    current: 1,
  };

  const data = useMemo(() => {
    return (
      store.cacheList.find(x => x.cacheId === cacheId)?.data ?? {
        state: initialData,
        total: 0,
      }
    );
  }, [store.cacheList, cacheId]);

  const [total, setTotal] = useState(data.total ?? 0);

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const pagination = useMemo(() => {
    const { current, pageSize } = data.state;
    return {
      current,
      total,
      pageSize,
    };
  }, [JSON.stringify({ current: data.state.current, total, pageSize: data.state.pageSize })]);

  const sort = useMemo(() => {
    return data.state.sort;
  }, [data.state.sort]);

  const filter = useMemo(() => {
    return data.state.filter;
  }, [data.state.filter]);

  const setPagination = ({ total }) => {
    setTotal(total);
  };

  const setSort = sort => {
    store.updateCache({
      cacheId,
      data: {
        state: {
          ...data.state,
          ...sort,
        },
        total: 0,
      },
    });
  };

  const setFilter = filter => {
    const cloneState = cloneDeep(data.state);
    cloneState.filter = {
      ...filter,
    };
    cloneState.current = 1;
    store.updateCache({
      cacheId,
      data: {
        state: cloneState,
        total: 0,
      },
    });
  };

  const queryParams: QueryParams = useMemo(() => {
    const { pageSize, current, filter, sort } = data.state;
    return {
      size: pageSize,
      page: (current ?? 1) - 1,
      filter,
      sort,
    };
  }, [
    JSON.stringify({
      size: data.state.pageSize,
      page: (data.state.current ?? 1) - 1,
      filter: data.state.filter,
      sort: data.state.sort,
    }),
  ]);

  const handleTableChange = ({ pageSize, current, filter: newFilter, sort }: TableQueryParams) => {
    let newSort = {
      ...initSort,
    };
    if (!isEqual(sort, {})) {
      newSort = sort!;
    }

    store.updateCache({
      cacheId,
      data: {
        total: 0,
        state: {
          ...data.state,
          pageSize,
          current,
          filter: {
            ...filter,
            ...newFilter,
          },
          sort: {
            ...newSort,
          },
        },
      },
    });
  };

  const onRowSelectionChange = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  };

  useEffect(() => {
    store.updateCache({
      cacheId,
      data: { state: data.state, total },
    });
  }, [data.state, total]);

  useEffect(() => {
    // store.getCache(cacheId, {
    //   state: initialData,
    //   total: 0
    // });
    return () => {
      if (!initCacheId) {
        store.deleteCache(cacheId);
      }
    };
  }, []);

  return {
    pagination,
    setPagination,
    setTotal,
    filter,
    setFilter,
    sort,
    setSort,
    selectedRowKeys,
    selectedRows,
    setSelectedRows,
    setSelectedRowKeys,
    handleTableChange,
    onRowSelectionChange,
    queryParams,
  };
};
