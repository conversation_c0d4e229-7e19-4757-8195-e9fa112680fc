import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import Table, { ColumnType, TableProps } from 'antd/es/table';
import type { FilterValue, SorterResult, TablePaginationConfig, TableRowSelection } from 'antd/es/table/interface';
import { isEqual } from 'lodash';
import ResizeObserver from 'resize-observer-polyfill';

import { getColumns } from '@/utils/table';

import './CustomTable.less';

import { FitArrangeModal } from './FitArrangeModal';
import { ResizableTitle } from './ResizableTitle';

export interface FitArrangeColumns extends ColumnType<any> {
  visible?: boolean; // 值为false时默认不显示列
  disabled?: boolean; // 不可修改显示影藏状态
}

enum SortTypes {
  ascend = 'ASC',
  descend = 'DESC',
}

export interface CacheColumn {
  title: string;
  // dataIndex: string;
  width?: number;
  fixed?: 'left' | 'right';
  visible?: boolean;
  disabled?: boolean;
}

export interface CustomTableProps extends Omit<TableProps<any>, 'onChange' | 'scroll'> {
  columns: FitArrangeColumns[];
  dataSource: any[];
  pagination?: false | TablePaginationConfig;
  loading?: boolean;
  onRowSelectionChange?: TableRowSelection<any>['onChange'];
  onChange?: (data: TableQueryParams) => void;
  className?: string;
  scroll?: boolean | TableProps<any>['scroll'];
  resizable?: boolean; // 是否可拖动表头
  cacheId?: string; // 控制是否缓存字段配置
}
export const CustomTable: React.FC<CustomTableProps> = props => {
  const {
    columns: initialColumns,
    dataSource,
    loading,
    pagination,
    onRowSelectionChange = null,
    onChange,
    scroll = false,
    className,
    rowSelection,
    resizable = false,
    cacheId = '',
    style,
    ...otherProps
  } = props;
  const [columns, setColumns] = useState(initialColumns);

  const getCacheColumn = columns => {
    return columns.map(column => {
      const { dataIndex, width, fixed, visible, disabled, title } = column;
      const obj: CacheColumn = {
        title: title ?? dataIndex,
        visible: visible ?? true,
      };
      if (width !== undefined) {
        obj.width = width;
      }
      if (fixed !== undefined) {
        obj.fixed = fixed;
      }
      if (disabled !== undefined) {
        obj.disabled = disabled;
      }
      return obj;
    });
  };

  const initialCacheColumns = useMemo(() => {
    return getCacheColumn(initialColumns);
  }, [initialColumns]);

  const currentCacheColumns = useMemo(() => {
    return getCacheColumn(columns);
  }, [columns]);

  const initialColumnsMap = useMemo(() => {
    const newMap = new Map();
    initialColumns.forEach(column => {
      newMap.set(column.title, column);
    });
    return newMap;
  }, [initialColumns]);

  const generateColumnsByCache = (cacheColumns: CacheColumn[]) => {
    return cacheColumns
      .filter(column => column.visible)
      .map(column => {
        const { fixed } = column;
        return {
          ...initialColumnsMap.get(column.title),
          fixed,
          width: column.width,
          onHeaderCell: col => ({
            width: col.width ?? 200,
            onResize: handleResize(column),
          }),
        };
      });
  };

  useEffect(() => {
    if (!cacheId) return;
    try {
      const cacheStr = localStorage.getItem(cacheId);
      if (cacheStr) {
        const cache: { initial: CacheColumn[]; current: CacheColumn[] } = JSON.parse(cacheStr);
        if (cache) {
          if (isEqual(cache.initial, initialCacheColumns)) {
            setColumns(generateColumnsByCache(cache.current));
          } else {
            localStorage.setItem(
              cacheId,
              JSON.stringify({
                initial: initialCacheColumns,
                current: currentCacheColumns,
              }),
            );
          }
        }
      } else {
        localStorage.setItem(
          cacheId,
          JSON.stringify({
            initial: initialCacheColumns,
            current: currentCacheColumns,
          }),
        );
      }
    } catch (e) {}
  }, [initialColumns]);

  // 表格scroll高度
  const [height, setHeight] = useState(500);
  const tableRef = useRef<React.Ref<Element> | undefined>(undefined);
  const [arrangeModalOpen, setArrangeModalOpen] = useState<boolean>(false);

  const customRowSelection = useMemo(() => {
    if (onRowSelectionChange) {
      return {
        preserveSelectedRowKeys: true,
        onChange: onRowSelectionChange,
        ...rowSelection,
      };
    }
    if (rowSelection) {
      return {
        preserveSelectedRowKeys: true,
        ...rowSelection,
      };
    }
    return undefined;
  }, [rowSelection, onRowSelectionChange]);

  // 监听table变更
  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<any>,
  ) => {
    if (!onChange) return;
    const sort: Record<string, SortTypes> = {};
    if (sorter.field) {
      sort[sorter.field] = SortTypes[sorter.order] ?? undefined;
    }
    const { pageSize, current } = pagination;
    onChange({
      sort,
      filter: {
        ...filters,
      },
      current: current ?? 1,
      pageSize: pageSize ?? 10,
    });
  };

  // 监听列表的高度，设置表scroll的高度
  useLayoutEffect(() => {
    if (!scroll) return;
    const observer = new ResizeObserver(entries => {
      const { height } = entries[0].contentRect;
      let short = 0;
      short += otherProps.showHeader !== false ? 39 : 0;
      if (pagination) {
        short += 56;
      }
      setHeight(height - short);
    });
    observer.observe(tableRef?.current as Element);
    return () => {
      observer.disconnect();
    };
  }, []);

  const handleResize =
    info =>
      (_, { size }) => {
        setColumns(columns => {
          return columns.map(column => {
            if (column.title === info.title) {
              return {
                ...column,
                width: size.width >75 ? size.width : 75,
              };
            }
            return {
              ...column,
            };
          });
        });
      };

  return (
    <div
      ref={tableRef}
      className={`custom-table h-full ${scroll ? 'overflow-hidden' : ''} ${className ?? ''}`}
      style={style}
    >
      <div className='relative'>
        {cacheId && otherProps?.showHeader !== false && (
          <div className='custom-table-icon' onClick={() => setArrangeModalOpen(true)}>
            <i className='iconfont icon-settings-line' />
          </div>
        )}
        <Table
          className='flex-1 bg-white'
          size='small'
          loading={loading}
          columns={cacheId ? getColumns(columns) : getColumns(initialColumns)}
          dataSource={dataSource}
          rowSelection={customRowSelection}
          scroll={
            !dataSource?.length ? undefined : scroll
              ? {
                x: 'auto',
                y: height,
                ...(typeof scroll === 'boolean' ? {} : scroll),
              }
              : {
                x: 'auto',
              }
          }
          pagination={
            pagination === false
              ? false
              : {
                showTotal: total => <span className='text-gray'>共 {total} 条记录</span>,
                showSizeChanger: true,
                // showQuickJumper: true,
                className: 'pagination-total-left',
                ...pagination,
              }
          }
          rowKey='id'
          onChange={onChange ? handleTableChange : undefined}
          components={{
            header: {
              cell: resizable ? ResizableTitle : undefined,
            },
          }}
          {...otherProps}
        />
      </div>
      {arrangeModalOpen && (
        <FitArrangeModal
          open={arrangeModalOpen}
          onCancel={() => setArrangeModalOpen(false)}
          cacheId={cacheId}
          onChange={cacheColumns => {
            setColumns(generateColumnsByCache(cacheColumns));
          }}
        />
      )}
    </div>
  );
};
