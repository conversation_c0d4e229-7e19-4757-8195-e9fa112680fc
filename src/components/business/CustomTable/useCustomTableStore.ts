import { create } from 'zustand';

interface CacheItem {
  cacheId: string;
  data: {
    state: {
      pageSize: number;
      // total: number;
      current: number;
      filter: Record<string, any>;
      sort: Record<string, SortTypes>;
    };
    total: number;
  };
}

interface State {
  cacheList: CacheItem[];
  initCache: (cacheId: string, initialData: CacheItem['data']) => void;
  deleteCache: (cacheId: string) => void;
  updateCache: (cache: CacheItem) => void;
  getCache: (cacheId: string, initialData: CacheItem['data']) => CacheItem;
}
const defaultState = {
  cacheList: [],
};
export const useCustomTableStore = create<State>((set, get) => ({
  ...defaultState,
  initCache(cacheId: string, initialData: CacheItem['data']) {
    const { cacheList } = get();
    const existCache = cacheList.find(cache => cache.cacheId === cacheId);
    if (existCache) {
      existCache.data = {
        ...initialData,
      };
    } else {
      const cache = {
        cacheId,
        data: initialData,
      };
      cacheList.push(cache);
    }
    set({
      cacheList: [...cacheList],
    });
  },
  addCache(data: CacheItem) {
    const { cacheList } = get();
    cacheList.push(data);
    set({ cacheList });
  },
  deleteCache(cacheId: string) {
    const { cacheList } = get();
    set({
      cacheList: cacheList.filter(cache => cache.cacheId !== cacheId),
    });
  },
  updateCache(cache: CacheItem) {
    const { cacheId, data } = cache;
    const { cacheList } = get();
    const existCache = cacheList.find(cache => cache.cacheId === cacheId);
    if (existCache) {
      existCache.data = {
        ...data,
      };
    } else {
      cacheList.push(cache);
    }
    set({
      cacheList: [...cacheList],
    });
  },
  getCache(cacheId: string, initialData) {
    const { cacheList } = get();
    const existCache = cacheList.find(cache => cache.cacheId === cacheId);
    if (existCache) {
      return existCache;
    } else {
      const cache = {
        cacheId,
        data: initialData,
      };
      cacheList.push(cache);
      set({
        cacheList: [...cacheList],
      });
      return cache;
    }
  },
}));
