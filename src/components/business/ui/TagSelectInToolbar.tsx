import { TagOutlined } from '@ant-design/icons';
import { useMutation } from '@tanstack/react-query';
import { Button, Popover } from 'antd';

import { TagSelect } from '@/components/form';
import { tagListService } from '@/modules/DataIngestion/services';

interface Props {
  tagType: 'TASK' | 'OFFLINE_WORKFLOW';
  value?: string[];
  onChange?: (value: string[]) => void;
}
export function TagSelectInToolbar(props: Props) {
  const { tagType, ...rest } = props;
  const { mutate: fetchData, data: tagOptions=[] } = useMutation({
    mutationFn: async () => {
      if (tagOptions.length > 0) return tagOptions;
      const { data } = await tagListService.queryByTypeUrl({ tagType });
      return data;
    },
  });

  function handleClick() {
    fetchData();
  }

  return <Popover
    overlayInnerStyle={{ width: 400 }}
    content={<div>
      <TagSelect
        data={tagOptions}
        valueKey='name'
        labelKey='name'
        {...rest}
      />
    </div>}
    placement='bottom'
    trigger='click'
  >
    <Button type='text' onClick={handleClick}>
      <TagOutlined />
      <span className='ml-2'></span> 标签
    </Button>
  </Popover>;
}
