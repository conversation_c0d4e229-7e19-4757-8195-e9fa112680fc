import { useEffect, useMemo, useRef, useState } from 'react';
import { type SheetComponent } from '@antv/s2-react';
import type { Node } from '@antv/x6';
import { Parser } from '@json2csv/plainjs';
import { Tabs, Tooltip } from 'antd';
import xlsx from 'json-as-xlsx';
import { omit, uniq } from 'lodash';

import './DebugObserver.less';

import { DebugObserverTable } from './DebugObserverTable';

function download(url, name) {
  const a = document.createElement('a');
  a.href = url;
  a.download = name;
  a.click();
}

interface TabData {
  index: number;
  key: string;
  jobId: string;
  label: string;
  slot: number;
  children: ReactNode;
}

interface Props {
  selectedNode?: Node;
  nodes: Node[];
  mapObserveData: Record<string, any[]>;
  clearData: () => void;
}

export const DebugObserver = ({ selectedNode, mapObserveData, nodes, clearData }: Props) => {
  const [activeKey, setActiveKey] = useState<string>();
  const refs = useRef<Record<string, typeof SheetComponent>>({});

  const jobIdAndSlotList = useMemo(() => {
    const list: any = [];
    nodes.forEach(node => {
      const { outTypes, jobDisplay } = node.data;
      const jobId = node.id;
      outTypes?.forEach((_, index) => {
        const key = `${jobId}-${index}`;
        list.push({
          key,
          slot: index,
          jobId,
          jobDisplay,
        });
      });
    });
    return list;
  }, [nodes]);

  const formatListData = observeData => {
    let columns: string[] = [];
    const rows = observeData;
    // 计算出columns and rows
    rows?.forEach((row, index) => {
      if (row) {
        const rowColumns = Object.keys(row);
        if (index === 0) {
          columns = rowColumns;
        } else {
          columns = uniq([...columns.filter(x => x!== '操作'), ...rowColumns.filter(x => x!== '操作')]);
        }
      }
    });

    columns = columns.sort();

    columns.push('操作');

    return {
      rows: rows ?? [],
      columns,
    };
  };

  const tabs: TabData[] = useMemo(() => {
    return jobIdAndSlotList
      .filter(x => mapObserveData?.[x.key]?.length > 0)
      .map(x => {
        const { jobDisplay, slot, key } = x;
        const observeData = mapObserveData[key];
        const exportFileName = `${jobDisplay}${slot}`;
        const listData = formatListData(observeData);
        return {
          ...x,
          label: `${exportFileName}(${observeData.length})`,
          children: (
            <DebugObserverTable
              listData={listData}
              ref={ref => {
                refs.current[key] = ref;
              }}
            />
          ),
        };
      });
  }, [jobIdAndSlotList, mapObserveData]);

  useEffect(() => {
    if (!selectedNode) {
      return;
    }

    const tab = tabs.find(tab => {
      return tab.jobId === selectedNode.id;
    });
    if (tab && tab.key !== activeKey) {
      setActiveKey(tab.key);
    }
  }, [selectedNode]);

  const getCurrentTab = () => {
    let currentTab = activeKey;
    if (!activeKey) {
      if (tabs.length == 0) return;
      currentTab = tabs[0].key;
    }
    return currentTab;
  };

  function exportJsonData() {
    const currentTab = getCurrentTab();
    if (currentTab) {
      const item = jobIdAndSlotList.find(x => x.key === currentTab);
      const exportFileName = `${item.jobDisplay}${item.slot}`;
      const observeData = mapObserveData[currentTab].map(x => {
        return omit(x, ['操作']);
      });
      const blob = new Blob([JSON.stringify(observeData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      download(url, `${exportFileName}.json`);
      URL.revokeObjectURL(url);
    }
  }
  function exportCsvData() {
    const currentTab = getCurrentTab();
    if (currentTab) {
      const parser = new Parser({
        withBOM: true,
      });

      const item = jobIdAndSlotList.find(x => x.key === currentTab);
      const exportFileName = `${item.jobDisplay}${item.slot}`;
      const observeData = mapObserveData[currentTab].map(x => {
        return omit(x, ['操作']);
      });

      const csv = parser.parse(observeData);
      const blob = new Blob([csv]);
      const url = URL.createObjectURL(blob);
      download(url, `${exportFileName}.csv`);
      URL.revokeObjectURL(url);
    }
  }
  function exportExcelData() {
    const currentTab = getCurrentTab();
    if (currentTab) {
      const item = jobIdAndSlotList.find(x => x.key === currentTab);
      const exportFileName = `${item.jobDisplay}${item.slot}`;
      const listData = formatListData(mapObserveData[currentTab]);

      xlsx([{
        sheet: 'sheet1',
        content: listData.rows,
        columns: listData.columns.filter(x => {
          return !['操作'].includes(x);
        }).map(x => {
          return {
            label: x,
            value: x,
          };
        }),
      }], {
        fileName: exportFileName,
      });
    }
  }

  function exportSearchResult() {
    const currentTab = getCurrentTab();
    if (!currentTab) return;
    // console.log(refs.current[currentTab].dataSet.fields.columns);
    const parser = new Parser({
      withBOM: true,
    });

    const item = jobIdAndSlotList.find(x => x.key === currentTab);
    const exportFileName = `${item.jobDisplay}${item.slot}`;
    const observeData =refs.current[currentTab]?.dataSet.getDisplayDataSet()?.map(x => {
      return omit(x, ['操作']);
    });

    const csv = parser.parse(observeData);
    const blob = new Blob([csv]);
    const url = URL.createObjectURL(blob);
    download(url, `${exportFileName}.csv`);
    URL.revokeObjectURL(url);
  }

  return (
    <div className='flex w-full h-full overflow-hidden'>
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        items={tabs}
        destroyInactiveTabPane={false}
        className='debug-observer flex-1 overflow-hidden h-full'
      ></Tabs>
      <div className='w-7 py-1 flex flex-col text-center bg-white'>
        <Tooltip title='清空' placement='left'>
          <i onClick={() => clearData()} className='iconfont icon-bin-line cursor-pointer text-gray mt-2'></i>
        </Tooltip>
        <Tooltip title='导出json' placement='left'>
          <i
            onClick={() => exportJsonData()}
            className='iconfont icon-json-download cursor-pointer text-gray mt-2'
          ></i>
        </Tooltip>
        <Tooltip title='导出csv' placement='left'>
          <i
            onClick={() => exportCsvData()}
            className='iconfont icon-csv-download cursor-pointer text-gray mt-2 text-[20px]'
          ></i>
        </Tooltip>
        <Tooltip title='导出excel' placement='left'>
          <i
            onClick={() => exportExcelData()}
            className='iconfont icon-excel-download cursor-pointer text-gray mt-2 text-[20px]'
          ></i>
        </Tooltip>
        <Tooltip title='导出搜索结果' placement='left'>
          <i
            onClick={() => exportSearchResult()}
            className='iconfont icon-save_alt-line cursor-pointer text-gray mt-2 text-sm'
          ></i>
        </Tooltip>
      </div>
    </div>
  );
};
