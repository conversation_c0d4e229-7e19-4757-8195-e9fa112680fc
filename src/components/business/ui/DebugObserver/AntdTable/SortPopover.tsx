import React, { memo, useEffect, useMemo, useState } from 'react';
import { DataFrame } from '@antv/ava';
import { Checkbox, Form, Input, Modal, ModalProps, Radio } from 'antd';
import { get } from 'lodash';

/**
 *
 * @param fieldName
 * @param filterParams
 * @returns
 */
export const getCurrentFilterParams = (fieldName, filterParams) => {
  const filtered = get(
    (filterParams || []).filter(param => param.filterKey === fieldName),
    '[0].filteredValues',
    [],
  );

  return filtered;
};

const convertToObject = values => {
  const initData = {};

  values.forEach(val => {
    initData[val] = true;
  });

  return initData;
};

const getCurrentSearchKeyword = (fieldName, filterParams) => {
  return get(
    (filterParams || []).filter(param => param.filterKey === fieldName),
    '[0].searchKeyword',
    undefined,
  );
};

interface Props extends ModalProps {
  dataSource: any[];
  fieldName: string;
  dataConfig: any;
  updateDataConfig: (config: any) => void;
}

const _SortPopover: React.FC<Props> = ({ dataSource, fieldName, dataConfig, updateDataConfig, ...otherProps }) => {
  const [form] = Form.useForm();
  const [dfInfo, setDfInfo] = useState();

  const { sortParams, filterParams } = dataConfig;
  const [sort, setSort] = React.useState(sortParams.sortFieldId === fieldName ? sortParams.sortMethod : 'NONE');
  const [filtered, setFiltered] = React.useState(convertToObject(getCurrentFilterParams(fieldName, filterParams)));
  const [changed, setChanged] = React.useState({
    sort: false,
    filter: false,
  });
  const [searchKeyword, setSearchKeyword] = React.useState(getCurrentSearchKeyword(fieldName, filterParams));

  const options = useMemo(() => {
    if (dfInfo && dfInfo[0]) {
      const { distinct } = dfInfo[0];
      if (distinct > 0 && distinct <= 20) {
        return Object.keys(dfInfo[0].valueMap);
      }
    }
    return null;
  }, [dfInfo]);

  useEffect(() => {
    if (!options) return;
    if (Object.keys(filtered)?.length) return;

    setFiltered(() => {
      const initData = {};
      options.forEach(val => {
        initData[val] = true;
      });
      return initData;
    });
  }, [options]);

  const onKeywordChange = keyword => {
    // 关键词变化时将不在关键词内的值过滤
    setSearchKeyword(keyword);
    setChanged(old => ({ ...old, filter: true }));
  };

  const onOk = e => {
    if (changed.sort) {
      updateDataConfig({
        ...dataConfig,
        sortParams: {
          sortFieldId: fieldName,
          sortMethod: sort,
        },
      });
    }
    if (changed.filter) {
      updateDataConfig(({ filterParams, ...rest }) => {
        let filteredValues = Object.entries(filtered)
          .map(([fieldValue, isFiltered]) => {
            if (isFiltered) return fieldValue;
          })
          .filter(Boolean);
        if (filteredValues?.length === options?.length) {
          filteredValues = [];
        }

        const index = filterParams.findIndex(x => x.filterKey === fieldName);
        if (index !== -1) {
          if (!searchKeyword && filteredValues.length === 0) {
            filterParams.splice(index, 1);
          } else {
            if (searchKeyword) {
              filterParams[index].searchKeyword = searchKeyword;
            } else {
              filterParams[index].filteredValues = filteredValues;
            }
          }
        } else {
          filterParams.push({
            filterKey: fieldName,
            filteredValues,
            searchKeyword,
          });
        }
        return {
          ...rest,
          filterParams,
        };
      });
    }
    setChanged({ filter: false, sort: false });
    otherProps.onCancel?.(e);
  };

  useEffect(() => {
    try {
      const df = new DataFrame(
        dataSource.map(x => {
          return { [fieldName]: x[fieldName] };
        }),
      );
      setDfInfo(df.info());
    } catch (e) {
      console.log(e);
    }
  }, []);

  return (
    <Modal
      title='列设置'
      className='antv-s2-data-preview-demo-modal'
      onCancel={e => {
        otherProps.onCancel?.(e);
        form.resetFields();
      }}
      onOk={onOk}
      {...otherProps}
    >
      <Form style={{ padding: '1em' }} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item label='数据排序: ' className='sort-item'>
          <Radio.Group
            onChange={e => {
              setSort(e.target.value);
              setChanged(val => ({ ...val, sort: true }));
            }}
            value={sort}
          >
            <Radio value={'NONE'}>无</Radio>
            <Radio value={'ASC'}>升序</Radio>
            <Radio value={'DESC'}>降序</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label='数值筛选: ' className='filter-item'>
          <div>
            {!options && (
              <Input.Search
                value={searchKeyword}
                onChange={e => onKeywordChange(e.target.value)}
                placeholder='请输入搜索关键词'
                className='mb-2'
              />
            )}
            {options && (
              <div className='flex flex-col mt-2'>
                <Checkbox
                  className='ml-0'
                  checked={options.every(fieldValue => filtered[fieldValue])}
                  indeterminate={
                    options.some(fieldValue => filtered[fieldValue]) &&
                    !options.every(fieldValue => filtered[fieldValue])
                  }
                  onChange={e => {
                    const {
                      target: { checked },
                    } = e;
                    setChanged(val => ({ ...val, filter: true }));

                    if (checked) {
                      setFiltered(() => {
                        const newValue = {};
                        options.forEach(fieldValue => {
                          newValue[fieldValue] = true;
                        });
                        return newValue;
                      });
                    } else {
                      // 将全部过滤
                      setFiltered(() => {
                        const newValue = {};
                        options.forEach(fieldValue => {
                          newValue[fieldValue] = false;
                        });

                        return newValue;
                      });
                    }
                  }}
                >
                  {'全选'}
                </Checkbox>

                {options?.map((item, index) => {
                  // 如果值是对象，不支持过滤
                  if (typeof item === 'object' || item === undefined) return;
                  return (
                    <Checkbox
                      className='ml-0'
                      checked={filtered[item]}
                      key={index}
                      onChange={e => {
                        setChanged(old => ({ ...old, filter: true }));
                        setFiltered(old => ({
                          ...old,
                          [item]: e.target.checked,
                        }));
                      }}
                    >
                      <span className='line-clamp-1 w-80' title={item}>
                        {item}
                      </span>
                    </Checkbox>
                  );
                })}
              </div>
            )}
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export const SortPopover = memo(_SortPopover);
