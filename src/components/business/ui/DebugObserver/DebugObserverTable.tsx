/* eslint-disable max-len */
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { copyToClipboard, S2CellType } from '@antv/s2';
import { SheetComponent, SheetComponentsProps } from '@antv/s2-react';
import { message } from 'antd';
import { produce } from 'immer';
import { cloneDeep, omit } from 'lodash';

import './DebugObserverTable.less';

import { CustomTableColCell } from './AntvS2Table/CustomTableColCell';
import { SortPopover } from './AntvS2Table/SortPopover';

import '@antv/s2-react/dist/style.min.css';

export const filterIcon =
  '<svg t="1633848048963" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="85936" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><defs><style type="text/css"></style></defs><path d="M0 0h1024L724.676923 488.369231V1024l-425.353846-141.784615v-393.846154L0 0z m196.923077 102.4l204.8 354.461538v362.338462l228.430769 63.015385V456.861538l212.676923-354.461538H196.923077z" opacity=".4" p-id="85937"></path></svg>';
export const sortUp =
  '<svg t="1634734477742" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2208" width="200" height="200"><path d="M569.508769 653.352619l151.594419 0 0 108.819221-151.594419 0L569.508769 653.352619zM569.508769 65.693452l385.479045 0 0 108.828814L569.508569 174.522266 569.508769 65.693452 569.508769 65.693452zM569.508769 261.583239l307.513506 0 0 108.819021L569.508769 370.402259 569.508769 261.583239 569.508769 261.583239zM569.508769 457.463032l229.552363 0 0 108.821019-229.552363 0C569.508769 566.284051 569.508769 457.463032 569.508769 457.463032zM569.508769 849.232612l73.62868 0 0 108.826815-73.62868 0L569.508769 849.232612zM354.693414 427.846912l0 530.212516L203.94622 958.059428 203.94622 427.846912 62.754748 427.846912 279.308125 65.187795 495.87849 427.846912 354.693414 427.846912z" p-id="2209"></path></svg>';
export const sortDown =
  '<svg t="1634734501800" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2875" width="200" height="200"><path d="M279.15323 958.059228l217.110799-363.160177-141.539436 0L354.724593 63.957829l-151.123938 0 0 530.943021L62.057421 594.900849 279.15323 958.059228 279.15323 958.059228zM570.078783 64.464885l386.443791 0 0 108.976114L570.078583 173.440999 570.078783 64.464885 570.078783 64.464885zM570.078783 369.594007 878.364965 369.594007l0-108.974515L570.078783 260.619492 570.078783 369.594007zM570.078783 565.747016l230.128573 0 0-108.976114L570.078783 456.770901 570.078783 565.747016 570.078783 565.747016zM570.078783 761.904621l151.972163 0L722.050945 652.930305l-151.972163 0L570.078783 761.904621zM570.078783 958.059228l73.813355 0 0-108.974315-73.813355 0L570.078783 958.059228z" p-id="2876"></path></svg>';

const _DebugObserverTable = ({ listData }, ref) => {
  const [dataCfg, setDataCfg] = useState<SheetComponentsProps['dataCfg']>({
    fields: {
      columns: listData.columns,
    },
    data: listData.rows,
    sortParams: [],
    filterParams: [],
  });

  const s2Ref = useRef(null);
  const [colModalVisible, setColModalVisible] = useState(false);
  const [interactedCol, setInteractedCol] = useState('');

  const s2Options = {
    frozenRowCount: 0, // 冻结行的数量，从顶部开始计数
    frozenTrailingRowCount: 0, // 冻结行数量，从底部开始计数
    frozenColCount: 0, // 冻结列的数量，从左侧开始计数
    frozenTrailingColCount: 1, // 冻结列的数量，从右侧开始计数
    showDefaultHeaderActionIcon: false,
    showSeriesNumber: true,
    interaction: {
      enableCopy: true,
      copyWithHeader: true,
      selectedCellsSpotlight: true,
      brushSelection: {
        data: true, // 默认开启
        row: true,
        col: true,
      },
    },
    conditions: {
      text: [
        // 行头
        {
          field: '操作',
          mapping() {
            return {
              // fill 是文本字段标记下唯一必须的字段，用于指定文本颜色
              fill: '#096dd9',
            };
          },
        },
      ],
    },
    style: {
      colCfg: {
        width: col => {
          return col.field === '操作' ? 40 : 'auto';
        },
      },
    },
    colCell: (item, spreadsheet, headerConfig) => {
      if (item.colIndex !== 0) {
        return new CustomTableColCell(
          item,
          spreadsheet,
          headerConfig,
          onIconClick,
        );
      }
    },
    customSVGIcons: [
      {
        name: 'Filter',
        svg: filterIcon,
      },
      {
        name: 'SortUp',
        svg: sortUp,
      },
      {
        name: 'SortDown',
        svg: sortDown,
      },
    ],
    tooltip: {
      content: (cell: S2CellType) => {
        if (!cell) return;
        const meta = cell.getMeta();
        const { valueField, data } = meta;
        let cellValue = data?.[valueField];
        if (!cellValue) return;
        if (typeof cellValue === 'object') {
          cellValue = JSON.stringify(cellValue, null, 2);
        }
        return <pre className='p-2 text-white font-mono'>
          {cellValue}
        </pre>;
      },
      row: {
        content: (cell: S2CellType) => {
          if (!cell) return;
          const meta = cell.getMeta();
          const ds = meta.spreadsheet.dataSet.getDisplayDataSet();
          const { rowIndex } = meta;
          const data = cloneDeep(ds[rowIndex]);
          delete data['操作'];
          return <pre className='p-2 text-white font-mono'>
            {JSON.stringify(data ?? {}, null, 2)}
          </pre>;
        },
      },
    },
  };

  useEffect(() => {
    setDataCfg(cfg => ({
      ...cfg,
      fields: {
        columns: listData.columns,
      },
      data: listData.rows,
    }));
    s2Ref.current?.render?.(true);
  }, [listData?.rows?.at(-1)]);

  const onDataCellClick = ({ viewMeta }) => {
    try {
      let data = viewMeta.data[viewMeta.valueField];
      if (viewMeta.valueField === '操作') {
        data = omit(viewMeta.spreadsheet.dataSet.getDisplayDataSet()[viewMeta.rowIndex], ['操作']);
      }

      // 首先拿到单元格当前信息
      copyToClipboard(JSON.stringify(data))
        .then(() => {
          message.success('复制成功');
        })
        .catch(() => {
          message.error('复制失败');
        });
    } catch(e) {
      console.log(e);
    };
  };

  const onRowCellClick = ({ viewMeta }) => {
    // 首先拿到单元格当前信息
    const data = omit(viewMeta.spreadsheet.dataSet.getDisplayDataSet()[viewMeta.rowIndex], ['操作']);

    copyToClipboard(JSON.stringify(data))
      .then(() => {
        message.success('复制成功');
      })
      .catch(() => {
        message.error('复制失败');
      });
  };

  const onIconClick = ({ meta }) => {
    setInteractedCol(meta.value);
    setColModalVisible(!colModalVisible);
  };

  const themeCfg = {
    name: 'gray',
  };

  useImperativeHandle(ref, () => s2Ref.current);

  return (
    <>
      <SheetComponent
        ref={s2Ref}
        sheetType={'table'}
        adaptive={{
          width: true,
          height: true,
        }}
        dataCfg={dataCfg}
        options={s2Options}
        themeCfg={themeCfg}
        onDataCellClick={onDataCellClick}
        onRowCellClick={onRowCellClick}
      />
      { colModalVisible && <SortPopover
        open={colModalVisible}
        onCancel={() => setColModalVisible(false)}
        fieldName={interactedCol}
        spreadsheet={s2Ref.current!}
        updateFilterParams={filter => {
          setDataCfg(produce(cfg => {
            const filterParams = cfg.filterParams ?? [];
            const index = filterParams?.findIndex(x => x.filterKey === filter.filterKey);
            if (index !== -1) {
              filterParams[index] = filter;
            } else {
              filterParams.push(filter);
            }
            return cfg;
          }));
        }}
        updateSortParams={sorter => {
          setDataCfg(produce(cfg => {
            const sortParams = cfg.sortParams ?? [];
            const index = sortParams?.findIndex(x => x.sortFieldId === sorter.sortFieldId);
            if (index !== -1) {
              sortParams[index] = sorter;
            } else {
              sortParams.push(sorter);
            }
            return cfg;
          }));
        }}
      />
      }
    </>
  );
};

export const DebugObserverTable = forwardRef(_DebugObserverTable);
