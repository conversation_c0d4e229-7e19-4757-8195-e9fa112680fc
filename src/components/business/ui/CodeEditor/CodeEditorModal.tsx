import { useEffect, useMemo, useState } from 'react';
import { color } from '@uiw/codemirror-extensions-color';
import { LanguageName, loadLanguage } from '@uiw/codemirror-extensions-langs';
import ReactCodeMirror from '@uiw/react-codemirror';
import { Modal, ModalProps } from 'antd';

import { AviatorSample } from './AviatorSample';
import { AviatorTest } from './AviatorTest';

export type LangType = 'SQL' | 'JSON' | 'SPL' | 'JAVASCRIPT' | 'AVIATOR_SCRIPT' | 'XML';

interface Props extends ModalProps {
  open: boolean;
  value: string;
  onChange?: (value: string) => void;
  lang?: LangType | LanguageName;
  title?: string;
  readOnly?: boolean;
}
export const CodeEditorModal = (props: Props) => {
  const { open, value, onChange, title, readOnly = false, ...otherProps } = props;
  const [code, setCode] = useState(value);
  const [topLevel, setTopLevel] = useState<boolean>(false);

  const isAviator = useMemo(() => {
    return props.lang === 'AVIATOR_SCRIPT';
  }, [props.lang]);

  const lang = useMemo<LanguageName>(() => {
    switch (props.lang) {
    case 'JSON':
      return 'json';
    case 'JAVASCRIPT':
      return 'javascript';
    case 'AVIATOR_SCRIPT':
      return 'python';
    case 'XML':
      return 'xml';
    case 'SQL':
      return 'sql';
    case 'SPL':
      return 'textile';
    default:
      return props.lang;
    }
  }, [props.lang]);

  const dirty = useMemo(() => {
    return value !== code;
  }, [value, code]);

  const onOk = e => {
    // todo 校验代码格式
    onChange?.(code);
    otherProps.onCancel?.(e);
  };

  // 当value更新时，同步更新code
  useEffect(() => {
    setCode(value);
  }, [value]);

  // todo 多语言代码编辑器
  return (
    <Modal
      title={title ?? '代码编辑器'}
      okButtonProps={{
        disabled: !dirty,
      }}
      width={1000}
      open={open}
      onOk={onOk}
      className='body-no-padding'
      {...otherProps}
    >
      <div className='flex justify-start'>
        <div style={{ height: '500px', overflow: 'hidden' }} className='flex flex-1 flex-col'>
          {isAviator && <AviatorSample code={code} topLevel={topLevel} setCode={setCode} setTopLevel={setTopLevel} />}
          <div className='flex-1 overflow-auto'>
            <ReactCodeMirror
              value={code}
              onChange={setCode}
              extensions={[color, loadLanguage(lang ?? 'json')!].filter(x => x)}
              className='h-full overflow-auto'
              readOnly={readOnly}
            />
          </div>
        </div>
        {isAviator && <AviatorTest script={code} topLevel={topLevel} />}
      </div>
    </Modal>
  );
};
