import React, { useMemo, useState } from 'react';
import { MinusOutlined } from '@ant-design/icons';
import Split from '@uiw/react-split';
import { Button, Tabs, TabsProps } from 'antd';
import { cloneDeep } from 'lodash';

import { AviatorApi } from '@/services';

import './AviatorTest.less';

import { CodeMirror } from './CodeMirror';

const getRandomId = function () {
  return Math.floor(Math.random() * 10000).toString();
};

export interface AviatorSample {
  label: string;
  index: string;
  jsonStr: string;
  result: string;
}

interface Props {
  script: string;
  samples?: any[];
  topLevel?: boolean;
  topField?: string;
}
export const AviatorTest: React.FC<Props> = ({ script, samples, topField, topLevel }) => {
  const [expand, setExpand] = useState(false);
  const [loading, setLoading] = useState(false);
  const [scriptList, setScriptList] = useState(
    samples ?? [
      {
        label: '样本',
        index: '0',
        jsonStr: '',
        result: '',
      },
    ],
  );

  const [activeKey, setActiveKey] = useState('0');

  const updateScriptItem = (item, value) => {
    item.jsonStr = value;
  };

  const onEdit = (targetKey: React.MouseEvent | React.KeyboardEvent | string, action: 'add' | 'remove') => {
    if (action === 'add') {
      const randomId = getRandomId();
      setScriptList(
        scriptList.concat([
          {
            label: '样本',
            index: randomId,
            jsonStr: '',
            result: '',
          },
        ]),
      );
      setActiveKey(randomId);
    } else {
      const targetIndex = scriptList.findIndex(x => x.index == targetKey);
      const newScriptList = scriptList.filter(x => x.index != targetKey);
      setScriptList(newScriptList);
      if (newScriptList[targetIndex]) {
        setActiveKey(newScriptList[targetIndex].index);
      } else {
        if (newScriptList.length > 0) {
          setActiveKey(newScriptList[newScriptList.length - 1].index);
        }
      }
    }
  };

  const testScript = async () => {
    // 组织样本
    const list = scriptList.map(item => {
      const { index, jsonStr } = item;
      return {
        index,
        jsonStr,
        script,
        topField,
        topLevel,
      };
    });
    setLoading(true);
    const { data } = await AviatorApi.run(list).finally(() => {
      setLoading(false);
    });
    const cloneList = cloneDeep(scriptList);
    data.forEach(item => {
      const { index, result, errorMsg } = item;
      const findItem = cloneList.find(x => x.index == index);
      if (findItem) {
        if (result === null) {
          findItem.result = errorMsg;
        } else {
          findItem.result = typeof result === 'object' ? JSON.stringify(result, null, 2) : String(result);
        }
      }
    });
    setScriptList(cloneList);
  };

  const items: TabsProps['items'] = useMemo(() => {
    return scriptList.map((item, index) => {
      return {
        children: (
          <CodeMirror
            lang='json'
            value={item.jsonStr}
            onChange={val => {
              updateScriptItem(item, val);
            }}
          ></CodeMirror>
        ),
        label: `样本${index + 1}`,
        key: item.index.toString(),
        closable: true,
      };
    });
  }, [scriptList]);

  const resultItems: TabsProps['items'] = useMemo(() => {
    return scriptList.map((item, index) => {
      return {
        children: <CodeMirror lang='json' value={item.result ?? ''} readOnly={true}></CodeMirror>,
        label: `结果${index + 1}`,
        key: item.index.toString(),
        closable: true,
      };
    });
  }, [scriptList]);

  return (
    <div className='aviator-test'>
      {expand && (
        <div style={{ width: '500px' }} className='flex flex-col h-[500px]'>
          <div className='flex justify-between aviator-test-header'>
            <div>
              <i className='iconfont icon-codemode-line mr-1'></i>脚本测试
            </div>
            <MinusOutlined
              className='cursor-pointer'
              onClick={() => {
                setExpand(false);
              }}
            />
          </div>
          <Split mode='vertical' className='flex-1 flex flex-col overflow-hidden'>
            <Tabs
              style={{ height: '50%' }}
              size='small'
              activeKey={activeKey}
              onChange={setActiveKey}
              type='editable-card'
              items={items}
              onEdit={onEdit}
            />
            <Tabs
              style={{ height: '50%' }}
              size='small'
              activeKey={activeKey}
              onChange={setActiveKey}
              items={resultItems}
              type='card'
            />
          </Split>
          <Button type='primary' loading={loading} onClick={() => testScript()}>
            预览结果
          </Button>
        </div>
      )}
      {!expand && (
        <div
          className={`aviator-test-fixed-btn ${expand ? 'activated' : ''}`}
          onClick={() => {
            setExpand(true);
          }}
        >
          <i className='iconfont icon-codemode-line'></i>脚本测试
        </div>
      )}
    </div>
  );
};
