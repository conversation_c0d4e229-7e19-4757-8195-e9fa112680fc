.keep-alive-tabs {
  & > .ant-tabs-nav .ant-tabs-tab {
    border-width: 0 1px;
  }

  &.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab {
    border-radius: 0;
    border-width: 0 1px 0 0;

    & + .ant-tabs-tab {
      margin-left: 0;
    }
  }

  .ant-tabs-nav {
    margin-bottom: 0 !important;
  }

  .ant-tabs-nav-list {
    &:first-child {
      margin-left: -1px;
    }
  }

  > .ant-tabs-content-holder {
    display: none;
  }
}
