import { useEffect, useState } from 'react';
import { Button, Modal } from 'antd';

import { CustomTable, useCustomTableHook } from '@/components';
import { useRightsHook } from '@/hooks';
import { ProcessApi } from '@/services';

interface Props {
  filter: Record<string, any>;
  currentVersion?: number;
  projectAuth?: ProjectAuthModel;
  onRollback: (data: any) => void;
}

export const VersionManagement = ({ filter: baseFilter, projectAuth, currentVersion, onRollback }: Props) => {
  const tableHook = useCustomTableHook({
    pageSize: 20,
    sort: {
      updateTime: 'DESC' as SortTypes,
    },
  });

  const isCurrentVersion = (record: any) => record.version === currentVersion;
  const { hasRights } = useRightsHook();

  const { pagination, queryParams, filter, setPagination, handleTableChange } = tableHook;

  const [open, setOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [list, setList] = useState<any[]>([]);

  const columns: Array<ColumnType<any>> = [
    {
      title: '版本',
      dataIndex: 'version',
      width: '200px',
      render: (version: number, record: any) => `${version}${isCurrentVersion(record) ? '（当前版本）' : ''}`,
    },
    {
      title: '提交人',
      width: '200px',
      dataIndex: 'createUserName',
    },
    {
      title: '发布时间',
      width: '220px',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      width: '100px',
      dataIndex: 'operation',
      render: (_: any, record) => (
        <Button
          type='link'
          size='small'
          disabled={!hasRights('data_develop:write', projectAuth) || isCurrentVersion(record)}
          onClick={() => versionBack(record)}
        >
          回滚
        </Button>
      ),
    },
  ];

  const onCancel = () => {
    setSelectedRowKeys([]);
    setOpen(false);
  };

  const versionBack = (record: any) => {
    Modal.confirm({
      title: '确认回滚',
      content: `确认回滚到版本${record.version}？`,
      onOk: () => {
        onRollback({
          ...record,
        });
        onCancel();
      },
    });
  };

  const fetchData = async () => {
    try {
      const { data, total } = await ProcessApi.getHistory({
        ...queryParams,
        filter: {
          ...baseFilter,
          ...filter,
        },
      });
      setList(data);
      setPagination({ ...pagination, total });
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchData();
  }, [queryParams, currentVersion]);

  return (
    <>
      {currentVersion && (
        <>
          <span className='text-sm text-gray'>当前版本号：</span>
          <Button size='small' type='link' onClick={() => setOpen(true)}>
            V{currentVersion}
          </Button>
        </>
      )}

      <Modal
        title='版本管理'
        width={760}
        open={open}
        onCancel={onCancel}
        footer={null}
        styles={{ body: { height: 500, overflow: 'hidden' } }}
      >
        <CustomTable
          className='bordered'
          scroll={true}
          columns={columns}
          pagination={pagination}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            columnTitle: ' ', // 去掉全选
            onChange: (rowKeys: string[]) => {
              if (rowKeys.length > 2) return;
              setSelectedRowKeys(rowKeys);
            },
          }}
          dataSource={list}
          onChange={handleTableChange}
        />
      </Modal>
    </>
  );
};
