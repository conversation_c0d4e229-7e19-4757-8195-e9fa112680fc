import { Col, Row, Tag } from 'antd';

interface Props {
  list: any[];
  label?: string;
  handleClick: (item: any) => void;
  keyAlias?: string;
  childAlias?: string;
  className?: string;
}

export const QuickInsertLabel = (props: Props) => {
  const {
    list,
    keyAlias = 'name',
    childAlias = 'message',
    handleClick,
    label = '点击标签快捷插入语句',
    ...otherProps
  } = props;

  return (
    <>
      {!!list?.length && (
        <Row gutter={[8, 8]} {...otherProps}>
          <Col>{label}:</Col>
          <>
            {list?.map(item => (
              <Col key={item[keyAlias]} onClick={() => handleClick(item)}>
                <Tag className='mr-0 cursor-pointer'>{item[childAlias]}</Tag>
              </Col>
            ))}
          </>
        </Row>
      )}
    </>
  );
};
