import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Tooltip } from 'antd';
import PubSub from 'pubsub-js';
import ResizeObserver from 'resize-observer-polyfill';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';

import { DEBUG_OUTER, DEBUG_START, RECEIVE_DEBUG_MESSAGE } from '@/constants/eventTypes';

import './DebugLog.less';

import { SearchPanel } from './SearchPanel';

import 'xterm/css/xterm.css';

const whiteTheme = {
  foreground: '#3e3e3e',
  background: '#f4f4f4',
  cursor: '#3f3f3f',

  black: '#3e3e3e',
  brightBlack: '#666666',

  red: '#970b16',
  brightRed: '#de0000',

  green: '#07962a',
  brightGreen: '#87d5a2',

  yellow: '#f8eec7',
  brightYellow: '#f1d007',

  blue: '#003e8a',
  brightBlue: '#2e6cba',

  magenta: '#e94691',
  brightMagenta: '#ffa29f',

  cyan: '#89d1ec',
  brightCyan: '#1cfafe',

  white: '#ffffff',
  brightWhite: '#ffffff',
  selectionBackground: '#ccc',
};
const darkTheme = {
  foreground: '#eff0eb',
  background: '#282a36',
  selection: '#40a9ff33',
  black: '#282a36',
  brightBlack: '#686868',
  red: '#ff4747',
  brightRed: '#ff4747',
  green: '#5af78e',
  brightGreen: '#5af78e',
  yellow: '#ffff01',
  brightYellow: '#ffff01',
  blue: '#57c7ff',
  brightBlue: '#57c7ff',
  magenta: '#ff6ac1',
  brightMagenta: '#ff6ac1',
  cyan: '#9aedfe',
  brightCyan: '#9aedfe',
  white: '#f1f1f0',
  brightWhite: '#eff0eb',
};

export interface DebugLogRef {
  writeln: (str: string) => void;
  writeError: (str: string) => void;
  clear: () => void;
}

const _DebugLog = (props, ref: ForwardedRef<DebugLogRef>) => {
  const { channelId } = props;
  // todo 可否支持换行、不换行切换
  const [theme, setTheme] = useState<'white' | 'dark'>('dark');
  const [terminal, setTerminal] = useState<Terminal>();
  const [fitAddon, setFitAddon] = useState<FitAddon>();
  const container = useRef<HTMLElement>();
  const toggleTheme = () => {
    setTheme(val => {
      if (val === 'white') {
        return 'dark';
      } else {
        return 'white';
      }
    });
  };

  useEffect(() => {
    if (!terminal) return;
    if (theme === 'white') {
      terminal.options = {
        fontFamily: 'Consolas, "Courier New", monospace',
        theme: whiteTheme,
        cursorBlink: false,
        allowProposedApi: true,
      };
    } else {
      terminal.options = {
        fontFamily: 'Consolas, "Courier New", monospace',
        theme: darkTheme,
        cursorBlink: false,
        allowProposedApi: true,
      };
    }
  }, [theme]);

  const clearLog = () => {
    terminal?.clear();
  };

  const writeln = useCallback(
    (str: string) => {
      terminal?.writeln(
        str
          .replace(/(\d{4}-\d{1,2}-\d{1,2} \d{2}:\d{2}:\d{2}\.\d{1,3})/, '\x1b[34m$1\x1b[0m')
          .replace(/( INFO )/, '\x1b[34m$1\x1b[0m')
          .replace(/( DEBUG )/, '\x1b[34m$1\x1b[0m')
          .replace(/( WARN )/, '\x1b[93m$1\x1b[0m')
          .replace(/( ERROR )/, '\x1b[91m$1\x1b[0m'),
      );
    },
    [terminal],
  );

  const writeError = useCallback(
    (str: string) => {
      terminal?.writeln(`\x1b[91m${str}\x1b[0m`);
    },
    [terminal],
  );

  useImperativeHandle(
    ref,
    () => {
      return {
        writeln,
        writeError,
        clear: clearLog,
      };
    },
    [terminal],
  );

  useEffect(() => {
    const terminal = new Terminal({
      fontFamily: 'Consolas, "Courier New", monospace',
      theme: theme === 'white' ? whiteTheme : darkTheme,
      cursorBlink: false,
      allowProposedApi: true,
    });

    const fitAddon = new FitAddon();
    fitAddon.fit();

    terminal.loadAddon(fitAddon);
    terminal.open(container.current!);

    setFitAddon(fitAddon);
    setTerminal(terminal);
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver(() => {
      fitAddon?.fit();
    });

    if (container?.current) {
      observer.observe(container?.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [fitAddon]);

  const HandleMessage = (wsData: WebSocketData) => {
    const { code, message } = wsData;

    if (code === '1004') {
      // 普通日志
      writeln?.(message??'');
    } else {
      // 出错日志
      writeError?.(message??'');
    }
  };

  const pubListener = (msg, data: { channelId: string; wsData: WebSocketData }) => {
    if (data.channelId === channelId) {
      HandleMessage(data.wsData);
    }
  };

  const clearAll = (msg, data: { channelId: string }) => {
    if (data.channelId === channelId) {
      clearLog?.();
    }
  };


  useEffect(() => {
    PubSub.subscribe(RECEIVE_DEBUG_MESSAGE, pubListener);

    PubSub.subscribe(DEBUG_START, clearAll);

    PubSub.subscribe(DEBUG_OUTER, clearAll);

    return () => {
      PubSub.unsubscribe(pubListener);
      PubSub.unsubscribe(clearAll);
    };
  }, [terminal]);

  return (
    <div className='relative h-full'>
      <div className='flex w-full h-full'>
        <div className='h-full flex-1 debug-log' ref={element => (container.current = element)}></div>
        <div className='w-6 py-1 flex flex-col text-center bg-white'>
          <Tooltip title='切换主题' placement='left'>
            <i
              onClick={() => toggleTheme()}
              className={`iconfont icon-theme-line mt-2 cursor-pointer ${
                theme === 'white' ? 'text-gray' : 'text-dark'
              }`}
            ></i>
          </Tooltip>
          <Tooltip title='清空日志' placement='left'>
            <i onClick={() => clearLog()} className='iconfont icon-bin-line cursor-pointer text-gray mt-2'></i>
          </Tooltip>
        </div>
      </div>
      <SearchPanel terminal={terminal} theme={theme} />
    </div>
  );
};

export const DebugLog = forwardRef(_DebugLog);
