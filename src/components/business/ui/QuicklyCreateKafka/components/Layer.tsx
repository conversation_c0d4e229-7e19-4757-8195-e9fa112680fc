import { ForwardedRef, forwardRef, useEffect, useMemo, useState } from 'react';
import { RefSelectProps, Select, SelectProps, Tag } from 'antd';
import cs from 'classnames';

import { Loading } from '@/components';
import { LayerCategory, TableType } from '@/constants/enum';
import { LAYER_OPTIONS } from '@/modules/DimensionModeling/constants';
import Request from '@/request';

interface Props extends SelectProps {
  tbType: TableType;
}

const Selector = ({ tbType, ...props }: Props, ref: ForwardedRef<RefSelectProps>) => {
  const type = LayerCategory.SOURCE;
  const [layerList, setlayerList] = useState([]);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    fetchLayer();
  }, []);

  const fetchLayer = async () => {
    try {
      setLoading(true);
      const res = await Request.get('/api/v2/data/modeling/warehouse-layer/list', {
        params: {
          tbType,
          catalog: type?.toUpperCase(),
        },
      });
      if (res?.code === '0000') {
        setlayerList(res?.data ?? []);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const options: SelectOptions = useMemo(() => {
    const layers: Layer[] = layerList ?? [];
    return layers.map(layer => {
      const option = LAYER_OPTIONS.find(({ value }) => value === layer.catalog);
      const baseClassName = 'text-title-5 border border-solid';
      const isCommon = layer.catalog === LayerCategory.COMMON;
      const isApp = layer.catalog === LayerCategory.APPLICATION;
      const isDS = layer.catalog === LayerCategory.SOURCE;
      const className = cs(baseClassName, {
        'border-success-3 bg-success-4 text-success-5': isCommon,
        'border-danger-1 bg-danger-2 text-danger-3': isApp,
        'border-primary-4 bg-white-7 text-primary-5': isDS,
      });
      return {
        label: (
          <div>
            <Tag className={className}>{option?.label}</Tag>
            <span>{layer.name}</span>
          </div>
        ),
        value: layer.id,
      };
    });
  }, [layerList]);

  if (isLoading) {
    return <Loading />;
  }

  return <Select {...props} ref={ref} showSearch placeholder='请选择' options={options} />;
};

export const LayerSelector = forwardRef<RefSelectProps, Props>(Selector);
