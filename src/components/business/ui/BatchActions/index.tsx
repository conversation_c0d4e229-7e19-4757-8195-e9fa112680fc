import { useMemo } from 'react';
import { DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Dropdown } from 'antd';

import { ExcelImportApi } from '@/services';

import './index.less';

type Props = ExportParams & {
  disabled?: boolean;
  onDeleteItems?: (ids: string[]) => void;
  extraItems?: MenuProps['items'];
};

const BatchActions = ({ type, ids, condition, disabled, onDeleteItems, extraItems = [] }: Props) => {
  const onClickItem = async ({ key }: { key: string }): Promise<void> => {
    let params: ExportParams = {
      type: '',
    };
    switch (key) {
    case 'export-all':
      params = {
        type,
      };
      await ExcelImportApi.export(params);
      break;
    case 'export-search-result':
      params = {
        type,
        condition,
      };
      await ExcelImportApi.export(params);
      break;
    case 'export-selected-item':
      params = {
        type,
        ids,
      };
      await ExcelImportApi.export(params);
      break;
    case 'delete': {
      if (ids && ids.length > 0) {
        onDeleteItems?.(ids);
      }
      break;
    }
    }
  };

  // 全部菜单项
  const items: MenuProps['items'] = useMemo(() => {
    return [
      { label: '导出全部', key: 'export-all', onClick: onClickItem },
      {
        label: '导出搜索结果',
        key: 'export-search-result',
        onClick: onClickItem,
        disabled: Object.keys(condition?.filter ?? []).length === 0,
      },
      {
        label: '导出已选择',
        key: 'export-selected-item',
        onClick: onClickItem,
        disabled: ids?.length == 0,
      },
      ...(typeof onDeleteItems === 'function'
        ? [
          { type: 'divider' },
          {
            label: '批量删除',
            disabled: ids?.length == 0 || disabled,
            key: 'delete',
            onClick: onClickItem,
          },
        ]
        : []),
      ...extraItems,
    ];
  }, [onDeleteItems, condition, ids, extraItems]);

  return (
    <Dropdown menu={{ items }}>
      <div className='batch-actions cursor-pointer inline-flex items-center justify-center'>
        <span className='text-sm'>
          批量操作
          <DownOutlined className='ml-2' />
        </span>
      </div>
    </Dropdown>
  );
};

export default BatchActions;
