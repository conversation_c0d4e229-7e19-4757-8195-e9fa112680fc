import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Form, Table } from 'antd';

import { FieldDescription } from '@/constants';
import { useLocalStorage } from '@/hooks';
import { ModelTableColumn } from '@/modules/DimensionModeling/models';
import { useCommonEnumsStore } from '@/stores/useCommonEnumsStore';
import { uuid } from '@/utils';

import { SaveTemplateBtn } from './FieldTemplate/SaveTemplateBtn';
import { RowContext, RowContextProps } from './RowContext';
import { TableOperationColumn } from './TableOperationColumn';

type TableType = DIMENSION_MODELING.TableType;

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': number;
}

interface Props extends TableProps<ModelTableColumn> {
  mode: 'edit' | 'read';
  tableType: TableType;
  initialValues?: ModelTableColumn[];
  form: FormInstance;
  standardFields?: ModelTableColumn[];
  name?: string;
}

const Row: React.FC<RowProps> = props => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props['data-row-key'] + 1 });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );
  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

const getNewItem = () => {
  return {
    id: uuid(),
    colType: 'STRING',
    colCatalog: FieldDescription.PROPERTY,
    colName: '',
    colDisplay: '',
    description: '',
    isPrimaryKey: false,
    isNotNull: false,
    indicatorType: undefined,
    periodId: undefined,
    adjIds: undefined,
    atomicId: undefined,
    dervId: undefined,
    dimTbId: undefined,
    dimColId: undefined,
  };
};

export const FieldsManagerTable = (props: Props) => {
  const { form, columns, initialValues, mode, tableType, standardFields, name } = props;
  const { measureList, columnList, enumList, fetchMeasure, fetchColumn, fetchEnum } = useCommonEnumsStore();
  const containerRef = useRef();

  const [checkedFields, setCheckedFields] = useLocalStorage<string[]>(`${tableType}_checkedFields`);
  const [fieldFormData, setFieldFormData] = useState([]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
  );

  const onDragEnd = (event: DragEndEvent, move) => {
    const { active, over } = event;
    if (active.id && over?.id && active.id !== over?.id) {
      const list = form.getFieldValue('dataSource');
      if (over?.id !== undefined) {
        const activeItem = list[active.data.current?.sortable?.index];
        if (activeItem.colAction === 'default') return;
        const overItem = list[over.data.current?.sortable?.index];
        if (overItem.colAction === 'default') return;
      }

      move(active.data.current?.sortable?.index, over?.data.current?.sortable.index);
    }
  };

  const filteredColumns = useMemo(() => {
    if (!checkedFields) {
      return columns?.filter(({ dataIndex }) => {
        if (dataIndex === 'key') {
          return mode === 'edit';
        }
        return true;
      });
    }
    return columns?.filter(({ dataIndex }) => {
      if (dataIndex === 'key') {
        return mode === 'edit';
      }
      return checkedFields.includes(dataIndex);
    });
  }, [checkedFields, columns, mode]);

  const DataRef = useRef<any>([]);
  DataRef.current = initialValues;

  useEffect(() => {
    if (!measureList.length) fetchMeasure();
    if (!columnList.length) fetchColumn();
    if (!enumList.length) fetchEnum();
  }, []);

  // 移除空白行
  const handleRemoveEmptyColumn = () => {
    const data = form.getFieldValue('dataSource');
    form.setFieldValue(
      'dataSource',
      data.filter(x => x.colName),
    );
  };
  // 插入行数
  const handleInsertColumn = (rowNum: number | null) => {
    const data = form.getFieldValue('dataSource') ?? [];
    form.setFieldValue('dataSource', [...data, ...new Array(rowNum ?? 1).fill(0).map(_ => getNewItem())]);
  };

  const onFieldsVisibleSave = (checkedFields: string[]) => {
    setCheckedFields(checkedFields);
  };

  const handleOpenSave = () => {
    setFieldFormData((form.getFieldValue('dataSource') || []).filter(item => item.colAction !== 'default'));
  };

  return (
    <>
      <div className='flex items-center flex-row-reverse'>
        <SaveTemplateBtn className='pb-[12px] mr-4 ml-2' columns={fieldFormData} name={name} onOpen={handleOpenSave} />
        {mode === 'edit' && (
          <div className='flex-1'>
            <TableOperationColumn
              tableType={tableType}
              onInsert={handleInsertColumn}
              onRemove={handleRemoveEmptyColumn}
              onFieldsVisibleSave={onFieldsVisibleSave}
              form={form}
              columns={columns}
              standardFields={standardFields}
            />
          </div>
        )}
      </div>

      <div className='overflow-x-scroll'>
        <Form initialValues={{ dataSource: initialValues }} form={form}>
          <Form.List name='dataSource'>
            {(fields: any, { remove, move }) => {
              return (
                <>
                  <div
                    style={{
                      borderLeft: '1px solid #f0f0f0',
                      borderRight: '1px solid #f0f0f0',
                    }}
                  >
                    <DndContext
                      sensors={sensors}
                      modifiers={[restrictToVerticalAxis]}
                      onDragEnd={e => onDragEnd(e, move)}
                    >
                      <SortableContext
                        items={fields.map(i => (i.key as number) + 1)}
                        disabled={mode === 'read'}
                        strategy={verticalListSortingStrategy}
                      >
                        <Table
                          ref={containerRef}
                          className='custom-table'
                          size='small'
                          dataSource={fields}
                          components={{
                            body: {
                              row: Row,
                            },
                          }}
                          rowKey='key'
                          columns={[
                            ...filteredColumns,
                            ...(mode === 'edit'
                              ? [
                                {
                                  title: '操作',
                                  dataIndex: 'operate',
                                  align: 'center',
                                  fixed: 'right',
                                  render: (_, { name }, index) => {
                                    const { colAction } = form.getFieldValue('dataSource')[index];
                                    return (
                                      <Button
                                        type='link'
                                        disabled={colAction === 'default'}
                                        onClick={() => remove(name)}
                                      >
                                          删除
                                      </Button>
                                    );
                                  },
                                },
                              ]
                              : []),
                          ]}
                          scroll={{ x: 'max-content' }}
                          pagination={false}
                        />
                      </SortableContext>
                    </DndContext>
                  </div>
                </>
              );
            }}
          </Form.List>
        </Form>
      </div>
    </>
  );
};
