import React, { useEffect, useMemo, useRef } from 'react';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Form, Table } from 'antd';

import { ModelTableColumn } from '@/modules/DimensionModeling/models';
import { useCommonEnumsStore } from '@/stores/useCommonEnumsStore';

import { RowContext, RowContextProps } from '../RowContext';

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': number;
}

interface Props extends TableProps<ModelTableColumn> {
  initialValues?: ModelTableColumn[];
  form: FormInstance;
}

const Row: React.FC<RowProps> = props => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props['data-row-key'] + 1 });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );
  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

export const FieldListTable = (props: Props) => {
  const { form, columns, initialValues } = props;
  const tableRef = useRef(null);
  const { measureList, columnList, enumList, fetchMeasure, fetchColumn, fetchEnum } = useCommonEnumsStore();
  const renderColumns = useMemo(() => {
    return columns?.map(item => {
      const width = item.dataIndex === 'isPrimaryKey' || item.dataIndex === 'isNotNull' ? 70 : 150;
      return {
        ...item,
        ...(item.title && { width }),
        ellipsis: true,
      };
    });
  }, [columns]);

  useEffect(() => {
    if (!measureList.length) fetchMeasure();
    if (!columnList.length) fetchColumn();
    if (!enumList.length) fetchEnum();
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
  );

  const onDragEnd = (event: DragEndEvent, move) => {
    const { active, over } = event;
    if (active.id && over?.id && active.id !== over?.id) {
      const list = form.getFieldValue('dataSource');
      if (over?.id !== undefined) {
        const activeItem = list[active.data.current?.sortable?.index];
        if (activeItem.colAction === 'default') return;
        const overItem = list[over.data.current?.sortable?.index];
        if (overItem.colAction === 'default') return;
      }

      move(active.data.current?.sortable?.index, over?.data.current?.sortable.index);
    }
  };

  return (
    <div className=''>
      <Form initialValues={{ dataSource: initialValues }} form={form}>
        <Form.List name='dataSource'>
          {(fields: any, { remove, move }) => {
            return (
              <div
                style={{
                  borderLeft: '1px solid #f0f0f0',
                  borderRight: '1px solid #f0f0f0',
                }}
              >
                <DndContext sensors={sensors} modifiers={[restrictToVerticalAxis]} onDragEnd={e => onDragEnd(e, move)}>
                  <SortableContext
                    items={fields.map(i => (i.key as number) + 1)}
                    strategy={verticalListSortingStrategy}
                  >
                    <Table
                      ref={tableRef}
                      className='custom-table'
                      size='small'
                      dataSource={fields}
                      rowKey='key'
                      components={{
                        body: {
                          row: Row,
                        },
                      }}
                      columns={[
                        ...renderColumns,
                        {
                          title: '操作',
                          dataIndex: 'operate',
                          align: 'center',
                          fixed: 'right',
                          width: 110,
                          render: (_, { name }, index) => {
                            const { colAction } = form.getFieldValue('dataSource')[index];
                            return (
                              <Button type='link' disabled={colAction === 'default'} onClick={() => remove(name)}>
                                删除
                              </Button>
                            );
                          },
                        },
                      ]}
                      scroll={{ x: fields?.length ? 'max-content' : 'auto', y: 300 }}
                      pagination={false}
                    />
                  </SortableContext>
                </DndContext>
              </div>
            );
          }}
        </Form.List>
      </Form>
    </div>
  );
};
