import { useMemo, useState } from 'react';
import { Button, message } from 'antd';

import { useRightsHook } from '@/hooks';
import { FieldTemplateApi } from '@/modules/DataStandard';

import { generateFieldItem } from '../../constant';
import type { FieldTemplateItem } from '../../models';

import FieldTemplateModal from './FieldTemplateModal';

interface Props {
  name?: string;
  columns: FieldTemplateItem;
  onOpen: () => void;
}

export const SaveTemplateBtn = (props: Props) => {
  const { name, columns, onOpen, ...restProps } = props;
  const [open, setOpen] = useState(false);
  const { hasRights } = useRightsHook();
  const templateItem = useMemo(() => {
    return {
      ...generateFieldItem(),
      name,
      columns,
    };
  }, [name, columns]);

  const handleOpen = () => {
    setOpen(true);
    onOpen();
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSave = async data => {
    await FieldTemplateApi.createTemplateItem(data);
    message.success('保存成功');
    handleClose();
  };

  return (
    <div {...restProps}>
      <Button type='primary' onClick={handleOpen} disabled={!hasRights('column_template:write')}>
        另存为模板
      </Button>
      <FieldTemplateModal
        title={'另存为模板'}
        open={open}
        onClose={handleClose}
        onSave={handleSave}
        templateItem={templateItem}
      />
    </div>
  );
};
