import { useEffect, useState } from 'react';
import { Button, Checkbox, Form, Input, message, Modal } from 'antd';
import { pick } from 'lodash';

import { SectionCaptain } from '@/components';
import { requiredRule } from '@/constants/rules';
import { useTableColumnHook } from '@/modules/DimensionModeling/pages/table/components/ModelTable/useTableColumnHook';
import { CommonApi } from '@/services';

import { generateFieldItem } from '../../constant';
import { FieldTemplateItem } from '../../models';

import { FieldListTable } from './FieldListTable';
import { FieldOperation } from './FieldOperation';
import { SelectFieldColumnModal } from './SelectFieldColumnModal';

const FormItem = Form.Item;

interface Props extends FormModalProps {
  templateItem?: FieldTemplateItem;
  disabled?: boolean;
}

const FieldTemplateModal: React.FC<Props> = props => {
  const { open, onClose, onSave, disabled, templateItem, title } = props;
  const [loading, setLoading] = useState(false);
  const [baseForm] = Form.useForm();
  const { columns, form: tableFieldForm } = useTableColumnHook({ tbType: 'TEMPLATE', mode: 'edit' });
  const [platformOptions, setPlatformOptions] = useState();
  const [openSelectColumn, setOpenSelectColumn] = useState(false);

  useEffect(() => {
    if (!templateItem) {
      baseForm.resetFields();
      tableFieldForm.setFieldValue('dataSource', []);
    } else {
      baseForm.setFieldsValue(pick(templateItem, ['name', 'description', 'platform']));
      tableFieldForm.setFieldValue('dataSource', templateItem.columns);
    }
  }, [templateItem]);

  useEffect(() => {
    getPlatformOptions();
  }, []);

  const getPlatformOptions = async () => {
    const { data } = await CommonApi.getEnumByCode2('ColumnTemplatePlatformEnum');

    setPlatformOptions(
      data.map(item => ({
        label: item.message,
        value: item.code,
      })),
    );
  };

  const handleOk = async () => {
    const baseValue = await baseForm.validateFields();
    const fieldData = tableFieldForm.getFieldValue('dataSource');
    if (!fieldData?.length) {
      message.error('请至少插入一条字段');
      return;
    }
    if (fieldData.some(item => !item.colName)) {
      message.error('字段名称不能为空');
      return;
    }

    setLoading(true);
    onSave({
      ...baseValue,
      columns: fieldData,
    })
      .then(() => {
        handleClose();
      })
      .catch((err: any) => {
        message.error(err?.msg);
        setLoading(false);
      });
  };

  const handleClose = () => {
    setLoading(false);
    baseForm.resetFields();
    onClose();
  };

  // 移除空白行
  const handleRemoveEmptyColumn = () => {
    const data = tableFieldForm.getFieldValue('dataSource') || [];
    tableFieldForm.setFieldValue(
      'dataSource',
      data.filter(x => x.colName),
    );
  };

  // 插入行数
  const handleInsertColumn = (rowNum: number | null) => {
    const data = tableFieldForm.getFieldValue('dataSource') ?? [];
    tableFieldForm.setFieldValue('dataSource', [
      ...data,
      ...new Array(rowNum ?? 1).fill(0).map(_ => generateFieldItem()),
    ]);
  };

  const handleSelectColumns = val => {
    if (!val?.length) return;

    const data = tableFieldForm.getFieldValue('dataSource') ?? [];
    tableFieldForm.setFieldValue('dataSource', [
      ...data,
      ...val.map(item => ({
        ...generateFieldItem(),
        dicId: item.id,
        colType: item?.colType,
        colName: item?.nameEn,
        colDisplay: item?.name,
        unitId: item?.measureUnitId,
        enumId: item?.enumId,
      })),
    ]);
  };

  const handleCloseSelectColumn = () => {
    setOpenSelectColumn(false);
  };

  return (
    <Modal
      title={title}
      open={open}
      destroyOnClose
      width={1060}
      styles={{
        body: {
          maxHeight: 600,
        },
      }}
      classNames={{
        body: 'flex flex-col overflow-hidden',
      }}
      onCancel={handleClose}
      onOk={handleOk}
      okText='确定'
      cancelText='取消'
      confirmLoading={loading}
    >
      <Form form={baseForm} disabled={disabled} labelCol={{ flex: '88px' }}>
        <FormItem className='mb-2' label='模板名' name='name' rules={[requiredRule('请输入模板名')]}>
          <Input placeholder='请输入' allowClear />
        </FormItem>
        <FormItem className='mb-2' label='模板描述' name='description'>
          <Input.TextArea placeholder='请输入' allowClear />
        </FormItem>
        <FormItem className='mb-2' label='适用数据库' name='platform'>
          <Checkbox.Group options={platformOptions} />
        </FormItem>
      </Form>
      <SectionCaptain title='字段管理' className='mb-1' />
      {!disabled && (
        <div className='flex justify-between items-center h-[56px] pb-[12px] '>
          <FieldOperation onInsert={handleInsertColumn} onRemove={handleRemoveEmptyColumn}>
            <Button type='primary' className='ml-4' onClick={() => setOpenSelectColumn(true)}>
              插入字段标准
            </Button>
          </FieldOperation>
        </div>
      )}
      <FieldListTable columns={columns} form={tableFieldForm} />
      {openSelectColumn && (
        <SelectFieldColumnModal
          open={openSelectColumn}
          onClose={handleCloseSelectColumn}
          onSave={handleSelectColumns}
        />
      )}
    </Modal>
  );
};

export default FieldTemplateModal;
