import React, { useEffect, useState } from 'react';
import { Button, InputNumber, Spin } from 'antd';
import { useLocation, useNavigate } from 'umi';

interface Props {
  fileName: string;
  content: string;
  loading: boolean;
}
export const LogContent: React.FC<Props> = props => {
  const location = useLocation();
  const [bytes, setBytes] = useState<Number | undefined>();
  const navigate = useNavigate();

  const getBytes = () => {
    const regex = /\?start=([-\d]*)/;
    const result = location.search.match(regex);
    if (result?.[1]) {
      const num = Math.abs(Number(result[1]));
      return num === 0 ? undefined : num;
    }
    return undefined;
  };

  useEffect(() => {
    setBytes(getBytes());
  }, []);

  const { fileName, content, loading } = props;
  const handleClick = () => {
    setBytes(undefined);
    navigate({
      ...location,
      search: location.search.replace(/\?start=([-\d]*)/, '?start=0'),
    });
  };
  const handleChangeBytes = (newBytes: number) => {
    setBytes(newBytes);
    navigate({
      ...location,
      search: location.search.replace(/\?start=([-\d]*)/, `?start=${-newBytes ?? 0}`),
    });
  };
  return (
    <div className='h-full w-full flex flex-col overflow-auto bg-white pb-4'>
      <div className='p-4'>
        日志文件：{fileName}
        <span className='ml-10 text-xs'>
          仅展示最近
          <InputNumber
            value={bytes}
            onChange={setBytes}
            onBlur={e => handleChangeBytes(e.target.value)}
            onPressEnter={e => handleChangeBytes(e.target.value)}
            className='w-32 mx-1'
            size='small'
          />
          个字节
          {bytes != undefined && (
            <Button type='link' onClick={handleClick}>
              查看全部日志
            </Button>
          )}
        </span>
      </div>

      {loading ? (
        <div className='flex-1 flex items-center justify-center'>
          <Spin />
        </div>
      ) : (
        <pre className='whitespace-pre-wrap px-4 flex-1 font-mono text-gray-8 leading-4 text-xs'>
          {content || <span className='text-gray-5'>日志文件内容为空</span>}
        </pre>
      )}
    </div>
  );
};
