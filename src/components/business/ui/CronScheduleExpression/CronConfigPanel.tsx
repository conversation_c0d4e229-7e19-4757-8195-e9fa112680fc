import { useEffect, useRef, useState } from 'react';
import { Button, Form, InputNumber, message, Popover, Radio, Select, TimePicker } from 'antd';
import dayjs from 'dayjs';

import { WeekEnum } from '@/constants';

import './index.less';

const format = 'HH:mm';

interface Props {
  title?: string;
  onOK: (jobCron: string) => void;
  onCancel?: () => void;
  onReset?: () => void;
  open?: boolean;
  children?: ReactNode;
  placement?;
  value: string;
  options?: OPERATINGCYCLE[];
}

const formItemLayout = {
  labelCol: { style: { width: '86px' } },
  wrapperCol: { span: 20 },
};
type OPERATINGCYCLE = 'SECOND' | 'MINUTE' | 'HOUR' | 'DAY' | 'WEEK' | 'MONTH';

enum Time_Type {
  SECOND = '秒',
  MINUTE = '分钟',
  HOUR = '小时',
}

const defaultOptions = [
  { label: '秒', value: 'SECOND' },
  { label: '分钟', value: 'MINUTE' },
  { label: '小时', value: 'HOUR' },
  { label: '天', value: 'DAY' },
  { label: '周', value: 'WEEK' },
  { label: '月', value: 'MONTH' },
];

export const CronConfigPanel = (props: Props) => {
  const {
    title = '配置',
    onOK,
    onCancel,
    open,
    children,
    placement = 'bottom',
    onReset,
    value,
    options,
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm();

  const initValue = useRef(options?.length ? options[0] : 'SECOND');
  const [operatingCycle, setOperatingCycle] = useState<OPERATINGCYCLE>(options?.length ? options[0] : 'SECOND');
  const [radioOptions, setRadioOptions] = useState(defaultOptions);

  useEffect(() => {
    if (options?.length) {
      setRadioOptions(defaultOptions.filter(item => options.includes(item.value)));
    }
  }, []);

  useEffect(() => {
    setVisible(!!open);
  }, [open]);

  const init = () => {
    const cron = value ? value.split(' ') : [];
    if (!cron.length) return;
    if (cron.length == 5) {
      cron.unshift('0');
    }

    if (cron.length !== 6) return message.error('cron格式错误');

    const minute = cron[1].length == 1 ? '0' + cron[1] : cron[1];
    const hour = cron[2].length == 1 ? '0' + cron[2] : cron[2];

    if (cron[cron.length - 1] !== '?') {
      setOperatingCycle('WEEK');
      setTimeout(() => {
        form.setFieldsValue({
          timeSpace: dayjs(hour + ':' + minute, format),
          dateSpace: cron[5].split(','),
        });
      }, 100);
    } else if (cron[3] !== '*') {
      setOperatingCycle('MONTH');
      setTimeout(() => {
        form.setFieldsValue({
          timeSpace: dayjs(hour + ':' + minute, format),
          dateSpace: cron[3].split(','),
        });
      }, 100);
    } else if (cron[0].includes('/')) {
      setOperatingCycle('SECOND');
      setTimeout(() => {
        form.setFieldsValue({
          timeInterval: parseInt(cron[0].slice(2)),
        });
      }, 100);
    } else if (cron[1].includes('/')) {
      setOperatingCycle('MINUTE');
      setTimeout(() => {
        form.setFieldsValue({
          timeInterval: parseInt(cron[1].slice(2)),
        });
      }, 100);
    } else if (cron[2].includes('/')) {
      setOperatingCycle('HOUR');
      setTimeout(() => {
        form.setFieldsValue({
          timeInterval: parseInt(cron[2].slice(2)),
        });
      }, 100);
    } else {
      setOperatingCycle('DAY');
      setTimeout(() => {
        form.setFieldsValue({
          timeSpace: dayjs(hour + ':' + minute, format),
        });
      }, 100);
    }
  };

  useEffect(() => {
    if (visible) {
      init();
    } else {
      form.resetFields();
      setOperatingCycle(initValue.current);
    }
  }, [visible, value]);

  // eslint-disable-next-line max-len
  const weekOption: Array<Record<'value' | 'label', string>> = Object.entries(WeekEnum).map(([value, label]) => ({
    value,
    label,
  }));

  const showDate = () => {
    const dataList: Array<Record<'label' | 'value', string>> = [];
    for (let i = 1; i <= 31; i++) {
      dataList.push({ label: i.toString(), value: i.toString() });
    }
    return dataList;
  };

  const handleConfirm = async () => {
    const values = await form.validateFields();
    let cron: string = '';
    if (values.timeSpace) {
      const hour = new Date(values.timeSpace).getHours();
      const minute = new Date(values.timeSpace).getMinutes();

      cron = `0 ${minute} ${hour} `;
      switch (operatingCycle) {
      case 'DAY':
        cron += '* * ?';
        break;
      case 'WEEK':
        cron += `? * ${values.dateSpace.join(',')}`;
        break;
      case 'MONTH':
        cron += `${values.dateSpace.join(',')} * ?`;
        break;
      }
    } else {
      switch (operatingCycle) {
      case 'SECOND':
        cron = `0/${values.timeInterval} * * * * ?`;
        break;
      case 'MINUTE':
        cron = `0 0/${values.timeInterval} * * * ?`;
        break;
      case 'HOUR':
        cron = `0 0 0/${values.timeInterval} * * ?`;
        break;

      default:
        break;
      }
    }
    onOK(cron);
    handleCancel();
  };

  useEffect(() => {
    form.resetFields();
  }, [operatingCycle]);

  const handleCancel = () => {
    setOperatingCycle(initValue.current);
    form.resetFields();
    !!onCancel && onCancel();
    setVisible(false);
  };

  const resetHandle = () => {
    form.resetFields();
    setOperatingCycle(initValue.current);
    !!onReset && onReset();
  };

  return (
    <Popover
      open={visible}
      placement={placement}
      overlayInnerStyle={{ padding: 0 }}
      overlayStyle={{ padding: 0 }}
      overlayClassName='cron_schedule_popover'
      content={
        <div className='cron_schedule_expression'>
          <div className='title px-2 py-3'>{title}</div>
          <div className='form'>
            <Form {...formItemLayout} form={form}>
              <Form.Item name='id' hidden>
                <Select />
              </Form.Item>
              <Form.Item label='运行周期'>
                <Radio.Group
                  value={operatingCycle}
                  onChange={({ target: { value } }) => setOperatingCycle(value)}
                  options={radioOptions}
                />
              </Form.Item>
              <Form.Item label='时间间隔' rules={[{ required: true }]}>
                {
                  <div className='flex flex-wrap'>
                    <span className='mr-1' style={{ lineHeight: '32px' }}>
                      计划每{`${operatingCycle == 'DAY' ? '天' : ''}${operatingCycle == 'MONTH' ? '月' : ''}`}
                    </span>
                    {['SECOND', 'MINUTE', 'HOUR'].includes(operatingCycle) ? (
                      <Form.Item name='timeInterval' rules={[{ required: true, message: '请输入' }]}>
                        <InputNumber
                          min={0}
                          step={1}
                          className='w-[88px]'
                          precision={0}
                          max={operatingCycle === 'HOUR' ? 24 : 60}
                        />
                      </Form.Item>
                    ) : (
                      <>
                        {['WEEK', 'MONTH'].includes(operatingCycle) && (
                          <Form.Item name='dateSpace' rules={[{ required: true, message: '请输入' }]}>
                            <Select
                              className='w-[318px]'
                              mode='multiple'
                              allowClear
                              options={operatingCycle == 'MONTH' ? showDate() : weekOption}
                            />
                          </Form.Item>
                        )}
                        <Form.Item name='timeSpace' initialValue={null} rules={[{ required: true, message: '请输入' }]}>
                          <TimePicker className='w-[134px]' format={format} />
                        </Form.Item>
                      </>
                    )}

                    <span className='ml-1' style={{ lineHeight: '32px' }}>{`${
                      Time_Type[operatingCycle] ?? ''
                    }执行一次`}</span>
                  </div>
                }
              </Form.Item>
            </Form>
          </div>

          <div className='flex justify-between px-2 py-2'>
            <Button type='primary' danger onClick={resetHandle}>
              清空
            </Button>
            <div>
              <Button className='mr-2' onClick={handleCancel}>
                取消
              </Button>
              <Button type='primary' onClick={handleConfirm}>
                确定
              </Button>
            </div>
          </div>
        </div>
      }
    >
      {children || (
        <Button type='primary' onClick={() => setVisible(true)}>
          运行计划设置
        </Button>
      )}
    </Popover>
  );
};
