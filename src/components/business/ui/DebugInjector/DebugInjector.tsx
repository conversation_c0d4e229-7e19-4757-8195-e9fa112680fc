import { useEffect, useMemo, useState } from 'react';
import { Graph } from '@antv/x6';
import ReactCodeMirror from '@uiw/react-codemirror';
import Split from '@uiw/react-split';
import { Button, message } from 'antd';

import './DebugInjector.less';

import { DebugInjectorHistory } from './DebugInjectorHistory';

export const DebugInjector = ({
  sendMessage,
  mockId,
  graph,
}: {
  sendMessage: (data) => void;
  mockId: string;
  graph: Graph;
}) => {
  const [input, setInput] = useState('');
  const [historyList, setHistoryList] = useState([]);

  const dataArr = useMemo(() => {
    return input.split('\n');
  }, [input]);

  const getWsData = () => {
    const wsData = [];
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < dataArr.length; i++) {
      try {
        const obj = JSON.parse(dataArr[i]);
        wsData.push(obj);
      } catch (e) {
        break;
      }
    }
    return wsData;
  };

  const send = () => {
    const wsData = getWsData();
    if (!wsData.length) {
      message.error('请输入合法的JSON数据');
      return;
    }
    wsData.forEach(message => {
      sendMessage({ code: '1002', job_id: mockId, message });
    });
    setHistoryList((historyList: unknown[]) => {
      historyList.push(...wsData);
      if (historyList.length > 100) {
        historyList.shift();
      }
      return historyList;
    });
    message.success('发送成功');
  };

  useEffect(() => {
    graph.on('debug:reset', () => {
      setHistoryList([]);
    });
  }, []);

  return (
    <div className='debug-injector'>
      <Split lineBar className='w-full h-full flex'>
        <div className='h-full relative flex-1 overflow-auto'>
          <ReactCodeMirror className='h-full' value={input} onChange={setInput} />
          <div className='absolute left-10 right-2 bottom-2 flex justify-between items-center'>
            <p className='text-gray text-xs'>输入json字符串，每行一个json对象，按行发送数据</p>
            <Button type='primary' onClick={() => send()}>
              发送
            </Button>
          </div>
        </div>

        <DebugInjectorHistory list={historyList} />
      </Split>
    </div>
  );
};
