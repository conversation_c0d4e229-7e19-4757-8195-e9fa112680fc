import { useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import { Badge, BadgeProps, message, Popconfirm, Tooltip } from 'antd';

import { Icon } from '@/components';

interface StatusProps {
  status: DATA_SOURCE.DataSourceStatus;
  detail?: DATA_SOURCE.ConnectDetail;
  onCheck: () => Promise<any>;
}

const statusMap: Record<
  DATA_SOURCE.DataSourceStatus,
  { status: BadgeProps['status']; text: DATA_SOURCE.DataSourceStatusText }
> = {
  NORMAL: {
    status: 'success',
    text: '正常',
  },
  ABNORMAL: {
    status: 'error',
    text: '异常',
  },
  PART_NORMAL: {
    status: 'warning',
    text: '部分异常',
  },
};

export const Status: React.FC<StatusProps> = ({ status, onCheck, detail }) => {
  const [loading, setLoading] = useState(false);
  const disabled = !detail || JSON.stringify(detail) === '{}';

  const handleCheck = async () => {
    try {
      setLoading(true);
      await onCheck();
      setLoading(false);
    } catch (err: any) {
      message.error(err);
      setLoading(false);
    }
  };

  const renderStatusDetailItem = (
    status: DATA_SOURCE.DataSourceStatus,
    detail?: DATA_SOURCE.ConnectDetail['normalUrls'] | DATA_SOURCE.ConnectDetail['abnormalUrlMap'],
  ) => {
    return (
      <div>
        <Badge
          status={statusMap[status]?.status ?? 'default'}
          text={<span className='text-black font-medium'>{statusMap[status]?.text}</span>}
        />
        <ul className='list-none text-black font-normal'>
          {Array.isArray(detail)
            ? detail.map(url => <li key={url}>{url}</li>)
            : Object.keys(detail ?? {})?.map(url => (
              <li key={url}>
                <span>{url}</span>: <span>{detail?.[url]}</span>
              </li>
            ))}
        </ul>
      </div>
    );
  };

  const renderStatusDetail = () => {
    if (disabled) return null;

    if (detail?.errMsg) {
      return <div>{detail.errMsg}</div>;
    }

    return (
      <div className='max-w-lg break-all'>
        {detail?.normalUrls?.length ? renderStatusDetailItem('NORMAL', detail?.normalUrls) : null}
        {Object.keys(detail?.abnormalUrlMap ?? {})?.length > 0
          ? renderStatusDetailItem('ABNORMAL', detail?.abnormalUrlMap)
          : null}
      </div>
    );
  };

  const renderStatus = () => {
    const tip = (
      <div className='flex items-center'>
        <Badge status={statusMap[status]?.status ?? 'default'} text={statusMap[status]?.text ?? '...'} />
        <Tooltip title='状态检查' placement='right'>
          <a
            className='ml-[18px]'
            onClick={event => {
              event.stopPropagation();
              handleCheck();
            }}
          >
            <Icon name='cheak' size={14} />
          </a>
        </Tooltip>
      </div>
    );
    if (!detail?.errMsg && !detail?.normalUrls?.length && !Object.keys(detail?.abnormalUrlMap ?? {}).length) {
      return tip;
    }

    return (
      <Popconfirm
        disabled={disabled}
        icon={null}
        title={renderStatusDetail()}
        trigger='hover'
        okButtonProps={{ className: 'hidden' }}
        cancelButtonProps={{ className: 'hidden' }}
        placement='topLeft'
      >
        {tip}
      </Popconfirm>
    );
  };

  return (
    <div>
      {loading ? (
        <span className='text-primary'>
          <LoadingOutlined className='mr-1' />
          状态检查中
        </span>
      ) : (
        renderStatus()
      )}
    </div>
  );
};
