import React, { useEffect, useRef, useState, useTransition } from 'react';
import { Graph, Node } from '@antv/x6';
import { InputNumber, Tabs } from 'antd';
import { produce } from 'immer';
import { shallow } from 'zustand/shallow';

import { DebugInjector } from '@/components/business/ui/DebugInjector/DebugInjector';
import { DebugLog } from '@/components/business/ui/DebugLog/DebugLog';
import { DebugObserver } from '@/components/business/ui/DebugObserver/DebugObserver';

import './index.less';

import { useDebugStore } from './useDebugStore';

// eslint-disable-next-line no-shadow
export enum RunningLogType {
  success,
  error,
}

export interface JobDebugRunningLog {
  type: RunningLogType;
  message: string;
  id?: number;
  display?: string;
  typeNumber?: number;
}

export interface ObserveDebugList extends WebSocketData {
  display: string;
  time: number;
}

interface Props {
  sendMessage: (data: { code: string; job_id: string; message: string }) => void;
  mockId: string;
  observeData: any[];
  selectedNode: Node;
  graph: Graph;
  channelId: string;
}
const INTERVAL_TIME = 1000; // 1s刷新一次调试观测
const _PipelineDebugPanel: React.FC<Props> = ({ sendMessage, mockId, selectedNode, graph, channelId }) => {
  const [,startTransition] = useTransition();
  const [activeKey, setActiveKey] = useState<string>();
  const [mapObserveData, setMapObserveData] = useState({});
  const debugPanelRef = useRef();
  const timer = useRef();
  const trackUrl = useDebugStore(state => state.trackUrl[channelId]);
  const getMapObserveData = useDebugStore(state => state.getMapObserveData, shallow);
  const [debugCount, setDebugCount] = useState(Number(localStorage.getItem('pipeline-debug-count') ?? 5000));

  const clearData = () => {
    setMapObserveData({});
  };

  const nodes = graph.getNodes()?.filter(node => node.data.jobOpts?.enableDebug && node.data.outTypes?.length > 0);

  const tabs = [
    {
      key: 'log-panel',
      label: '运行日志',
      children: <DebugLog channelId={ channelId} />,
    },
    {
      key: 'observe-panel',
      label: '调试观测',
      children: (
        <DebugObserver
          nodes={nodes}
          mapObserveData={ mapObserveData }
          selectedNode={selectedNode}
          clearData={clearData}
        />
      ),
    },
    mockId && {
      key: 'inject-panel',
      label: '模拟注入',
      children: <DebugInjector sendMessage={sendMessage} mockId={mockId} graph={graph} />,
    },
  ];

  useEffect(() => {
    if (selectedNode) {
      startTransition(() => {
        setActiveKey('observe-panel');
      });
    }
  }, [selectedNode]);

  function refresh() {
    if (timer.current) {
      clearInterval(timer.current);
    }
    timer.current = setInterval(() => {
      const total = debugCount;
      const newData = getMapObserveData(channelId);
      graph.trigger('running', { runningData: newData || {} });
      if (!newData) {
        return;
      };
      setMapObserveData(oldData => {
        return produce(oldData, oldData => {
          const oldKeys = Object.keys(oldData);
          const newKeys = Object.keys(newData);
          newKeys.forEach(key => {
            if (oldKeys.includes(key)) {
              const newLen = newData[key].length;
              const oldLen = oldData[key].length;
              if (total) {
                if (newLen > total) {
                  oldData[key] = newData[key].slice(newLen - total);
                } else if (newLen == total) {
                  oldData[key] = newData[key];
                } else {
                  const needKeepCount = total - newLen;
                  const start = oldLen > needKeepCount ? oldLen - needKeepCount: 0;
                  oldData[key] = [...oldData[key].slice(start), ...newData[key]];
                }
              } else {
                oldData[key] = [...oldData[key], ...newData[key]];
              }
            } else {
              oldData[key] = newData[key]??[];
            }
          });
        });
      });


    }, INTERVAL_TIME);
  }

  function stopRefresh() {
    if (timer.current) {
      clearInterval(timer.current);
    }
    graph.trigger('running', { runningData: {} });
  }

  useEffect(() => {
    refresh();
    graph.on('debug:start', () => {
      clearData();
      refresh();
    });

    graph.on('debug:stop', () => {
      stopRefresh();
    });

    return () => {
      stopRefresh();
      graph.off('debug:start');
    };
  }, [channelId, graph, debugCount]);

  const handleChangeCount = value => {
    setDebugCount(value);
    localStorage.setItem('pipeline-debug-count', value ?? '');
  };

  return (
    <div className='pipeline-debug-panel' ref={debugPanelRef}>
      <Tabs
        defaultActiveKey='log-panel'
        activeKey={activeKey}
        items={tabs}
        onChange={tabKey => {
          startTransition(() => {
            setActiveKey(tabKey);
          });
        }}
        tabBarExtraContent={
          <>
            <span>保留最后
              <InputNumber
                value={debugCount}
                min={1}
                onChange={handleChangeCount}
                className='mx-2'
                size='small'
              />
              <span>条数据</span>
            </span>
            {trackUrl && (
              <a href={trackUrl} rel='noreferrer' target='_blank' className='mx-2'>
                追踪
              </a>
            )}
          </>
        }
      ></Tabs>
    </div>
  );
};

export const PipelineDebugPanel = React.memo(_PipelineDebugPanel);
