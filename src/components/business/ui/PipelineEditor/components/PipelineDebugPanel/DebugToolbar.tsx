import Draggable from 'react-draggable';
import { EyeInvisibleOutlined, EyeOutlined, HolderOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';

export const DebugToolbar = props => {
  const {
    debugging,
    observeAll,
    disableAllSinkJob,
    outerDebug,
    startDebug,
    stopDebug,
    toggleObserveAll,
    toggleDisableSinkJob,
  } = props;
  return (
    <Draggable bounds='parent'>
      <div className='absolute top-1 z-50 left-1/2 -ml-[78px]'>
        <div className='debug-toolbar flex items-center'>
          {debugging ? (
            <Tooltip title='停止调试' placement='bottom'>
              <Button type='text' onClick={() => stopDebug()}>
                <i className='iconfont icon-stop-fill1 text-sm text-danger'></i>
              </Button>
            </Tooltip>
          ) : (
            <Tooltip title='开始调试' placement='bottom'>
              <Button type='text' onClick={() => startDebug()}>
                <PlayCircleOutlined className='text-success' />
              </Button>
            </Tooltip>
          )}

          <Tooltip title={`${observeAll ? '关闭' : '打开'}调试观测`} placement='bottom'>
            <Button type='text' onClick={() => toggleObserveAll()} disabled={debugging}>
              {observeAll ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            </Button>
          </Tooltip>

          <Tooltip title={`${disableAllSinkJob ? '取消禁用' : '禁用'}输出算子`} placement='bottom'>
            <Button type='text' onClick={() => toggleDisableSinkJob()} disabled={debugging}>
              {disableAllSinkJob ? (
                <i className='iconfont icon-block-line1 text-sm'></i>
              ) : (
                <i className='iconfont icon-circle-line text-sm'></i>
              )}
            </Button>
          </Tooltip>

          <Tooltip title='退出调试' placement='bottom'>
            <Button type='text' onClick={() => outerDebug()}>
              <i className='iconfont icon-computer_power-line text-sm'></i>
            </Button>
          </Tooltip>

          <HolderOutlined className='cursor-move text-sm' />
        </div>
      </div>
    </Draggable>
  );
};
