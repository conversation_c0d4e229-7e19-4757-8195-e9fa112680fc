import { produce } from 'immer';
import { create } from 'zustand';

type ChannelId = string;
interface State {
  observeData: Record<ChannelId, string[]>;
  trackUrl: Record<ChannelId, string>;
  debugCount: number | '' | undefined;
  debugStrategy: 'earliest' | 'latest';
  setTrackUrl: (channelId: string, trackUrl: string) => void;
  setObserveData: (channelId: string, wsData: WebSocketData) => void;
  setDebugCount: (debugCount: State['debugCount']) => void;
  setDebugStrategy: (debugStrategy: State['debugStrategy']) => void;
  getMapObserveData: (channelId: string) => Record<string, any[]> | null;
  clearObserveData: (channelId) => void;
}
const defaultState = {
  observeData: {},
  trackUrl: {},
  debugCount: 50,
  debugStrategy: 'latest' as State['debugStrategy'],
};
// const messageRegex = /"message":(".*")\}$/;
const cacheData: Record<ChannelId, Record<string, any[]>> = {};
export const useDebugStore = create<State>((set, get) => ({
  ...defaultState,
  setDebugStrategy(debugStrategy) {
    return set(produce(state => {
      state.debugStrategy = debugStrategy;
    }));
  },
  setDebugCount(count) {
    return set(produce(state => {
      state.debugCount = count;
    }));
  },
  setTrackUrl(channelId, trackUrl) {
    set(produce(state => {
      state.trackUrl[channelId] = trackUrl;
    }));
  },
  setObserveData(channelId, wsData) {
    const channelData = cacheData[channelId];
    const { job_id: jobId, slot, message } = wsData;
    const key = `${jobId}-${slot}`;
    try {
      const data = JSON.parse(message);
      data['操作'] = '复制';
      if (channelData[key]) {
        channelData[key].push(data);
      } else {
        channelData[key] = [data];
        cacheData[channelId] = channelData;
      }
    } catch (e) {
      console.warn('wsData解析出错：', message );
    }

  },
  clearObserveData(channelId) {
    cacheData[channelId] = {};
  },
  getMapObserveData(channelId) {
    const { clearObserveData } = get();
    const observeData = cacheData[channelId];
    clearObserveData(channelId);

    const hasRows = Object.values(observeData ?? {}).some(x => x.length > 0);
    if (!hasRows) {
      return null;
    }
    return observeData;
  },
}));
