@sourceColor: var(--color-source);
@processColor: var(--color-process);
@sinkColor: var(--color-sink);
@shareColor: var(--color-success);

.job-list-collapse {
  background: none !important;

  .ant-collapse-header {
    padding: 4px 12px !important;
  }

  &-header {
    border-width: 0 0 0 4px;
    border-style: solid;
  }

  &-wrapper {
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }

  &-outer {
    > .ant-collapse-item {
      border-bottom: 1px solid rgb(0 0 0 / 8%) !important;

      > .ant-collapse-header {
        background-color: #fff;
      }
    }

    .ant-collapse-content-box {
      padding: 0 !important;
    }
  }

  &-inner {
    border-top: 1px solid rgb(0 0 0 / 15%) !important;
    border-radius: 0;

    > .ant-collapse-item > .ant-collapse-header {
      background-color: rgb(0 0 0 / 8%);
      position: relative;
      padding-left: 22px !important;
    }
  }

  .source {
    border-color: @sourceColor;

    .job-list-collapse-inner {
      .ant-collapse-header {
        &::before {
          content: '';
          position: absolute;
          display: inline-block;
          width: 4px;
          height: 4px;
          background-color: @sourceColor;
          vertical-align: middle;
          left: 8px;
          top: 50%;
          margin-top: -2px;
        }
      }
    }
  }

  .process {
    border-color: @processColor;

    .job-list-collapse-inner {
      .ant-collapse-header {
        &::before {
          content: '';
          position: absolute;
          display: inline-block;
          width: 4px;
          height: 4px;
          background-color: @processColor;
          vertical-align: middle;
          left: 8px;
          top: 50%;
          margin-top: -2px;
        }
      }
    }
  }

  .sink {
    border-color: @sinkColor;

    .job-list-collapse-inner {
      .ant-collapse-header {
        &::before {
          content: '';
          position: absolute;
          display: inline-block;
          width: 4px;
          height: 4px;
          background-color: @sinkColor;
          vertical-align: middle;
          left: 8px;
          top: 50%;
          margin-top: -2px;
        }
      }
    }
  }

  .share {
    border-color: @shareColor;
  }
}
