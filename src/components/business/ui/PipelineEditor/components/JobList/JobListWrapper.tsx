import { JobListItem } from './JobListItem';

interface Props {
  list: JobModel[];
  draggable: boolean;
  handlerDragStart: (event, job: JobModel) => void;
}
export const JobListWrapper = ({ list, draggable, handlerDragStart }: Props) => {
  return (
    <ul className='job-list'>
      {list.map(item => {
        const key = item.jobName;
        return <JobListItem item={item} key={key} handlerDragStart={handlerDragStart} draggable={ draggable } />;
      })}
    </ul>
  );
};