import { message } from 'antd';
import { create } from 'zustand';

import { JobApi } from '@/modules/ExtensionPackage/services/JobApi';

interface JobStoreState {
  streamingJobList: any[];
  batchJobList: any[];
  fetchData: (jobType: JobType) => void;
  // 完整算子详情
  fetchJobParameters: (jobName: string) => Promise<PipelineParameterModel[]>;
  // 无parameters
  getJobInfo: (jobName: string) => JobModel;
  cacheJobParameters: Record<string, PipelineParameterModel[]>;
}

export const useJobStore = create<JobStoreState>((set, get) => ({
  streamingJobList: [],
  batchJobList: [],
  cacheJobParameters: {},
  async fetchJobParameters(jobName: string) {
    if (!jobName) {
      message.error('算子找不到了');
      return;
    }
    const { cacheJobParameters } = get();
    if (cacheJobParameters[jobName]) {
      return cacheJobParameters[jobName];
    }
    const { data } = await JobApi.getParametersByJobName(jobName);
    cacheJobParameters[jobName] = data;
    set({
      cacheJobParameters,
    });
    return data;
  },
  getJobInfo(jobName: string) {
    const { streamingJobList, batchJobList } = get();
    const findNode = streamingJobList.find(x => x.jobName === jobName) ?? batchJobList.find(x => x.jobName === jobName);
    // 异常处理，如果算子不存在，怎么做
    return (
      findNode ?? {
        jobName: undefined,
        jobType: undefined,
        jobDisplay: undefined,
        jobDescription: undefined,
        jobCategory: undefined,
        inTypes: [],
        outTypes: [],
      }
    );
  },
  fetchData(jobType: 'streaming' | 'batch') {
    const { streamingJobList, batchJobList } = get();
    // 如果流作业算子已经存在，返回
    if (jobType === 'streaming') {
      if (streamingJobList.length !== 0) {
        return;
      }
    }
    // 如果批作业算子已经存在，返回
    if (jobType === 'batch') {
      if (batchJobList.length !== 0) {
        return;
      }
    }
    // 算子不存在，根据算子类型请求算子列表
    JobApi.getAll(jobType).then(({ data }) => {
      if (jobType === 'streaming') {
        // 流作业算子
        set({
          streamingJobList: data,
        });
      } else if (jobType === 'batch') {
        // 批作业算子
        set({
          batchJobList: data,
        });
      }
    });
  },
}));
