import { Popover } from 'antd';
import { Link } from 'umi';

import { JobDefaultIcon, PROJECT } from '@/constants';

interface JobListItemProps {
  item: JobModel;
  draggable: boolean;
  handlerDragStart: (event, job: JobModel) => void;
}

export function JobListItem({ item, draggable, handlerDragStart }: JobListItemProps) {
  const { jobDisplay, jobDescription, docUrl, jobName } = item;
  const iconUrl = item.iconUrl ? `${PROJECT.baseUrl}${item.iconUrl}` : JobDefaultIcon[item.jobRole];
  const Content = () => {
    return (
      <>
        <span className='flex flex-vcenter justify-between items-center font-normal'>
          {jobDisplay}{' '}
          {docUrl && (
            <Link
              target='_blank'
              to={`/job/doc?display=${jobDisplay}&jobName=${jobName}&docUrl=${encodeURIComponent(docUrl)}`}
              type='link'
            >
              算子文档
            </Link>
          )}
        </span>
        <div className='text-gray-5'>{jobDescription}</div>
      </>
    );
  };

  return (
    <Popover
      title={null}
      placement='right'
      content={<Content />}
      overlayStyle={{ width: '300px', padding: '0', wordBreak: 'break-all' }}
    >
      <li
        className='flex pl-5 pr-2 py-1 cursor-move'
        draggable={ draggable }
        onDragStart={event => handlerDragStart(event, item)}
      >
        <div className='flex items-center'>
          <img src={iconUrl} width={16} className='mr-2' />
          <span className='flex-1 line-clamp-1' title={jobDisplay}>
            {jobDisplay}
          </span>
        </div>
      </li>
    </Popover>
  );
}
