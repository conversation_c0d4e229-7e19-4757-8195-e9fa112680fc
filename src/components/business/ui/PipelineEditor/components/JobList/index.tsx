import React, { useEffect, useMemo, useState } from 'react';
import { Collapse, Divider, Input } from 'antd';
import { groupBy } from 'lodash-es';

import { JobRoleType } from '@/constants/JobRoleType';
import { JobGroupList, useJobGroupHook } from '@/modules/JobGroup';

import './index.less';

import { JobListWrapper } from './JobListWrapper';
import { useJobStore } from './useJobStore';

type GroupedJobList = Record<keyof typeof JobRoleType, { count: number; groups: Record<string, JobModel[]> }>;

interface Props {
  type: 'streaming' | 'batch';
  draggable: boolean;
  handlerDragStart: (event, job: JobModel) => void;
}
export const JobList: React.FC<Props> = ({ type, draggable, handlerDragStart }) => {
  const [showV1Job, setShowV1Job] = useState<Boolean>(true);
  const [searchKey, setSearchKey] = useState<string | undefined>();
  const store = useJobStore();
  const jobList = type === 'streaming' ? store.streamingJobList : store.batchJobList;
  const { list: jobGroupList } = useJobGroupHook({ jobType: type });

  const filteredJobGroupList = useMemo(() => {
    const filterText = searchKey?.toLowerCase();
    if (!filterText) return jobGroupList;
    return jobGroupList?.filter(item => {
      return item.groupName.includes(filterText);
    });
  }, [searchKey, jobGroupList]);

  const filteredJobList = useMemo(() => {
    const filterText = searchKey?.toLowerCase();
    let filteredList: JobModel[] = jobList.filter(item => !item.internal);

    if (filterText) {
      filteredList = jobList.filter((item: JobModel) => {
        const { jobName, jobDisplay, jobDescription } = item;
        return (
          jobName?.toLowerCase().includes(filterText) ||
          jobDisplay?.toLowerCase().includes(filterText) ||
          jobDescription?.toLowerCase().includes(filterText)
        );
      });
    }

    if (!showV1Job) {
      filteredList = filteredList.filter(({ apiVersion }) => apiVersion === '2');
    }

    return filteredList;
  }, [jobList, showV1Job, searchKey]);

  const groupedJobList = useMemo<GroupedJobList>(() => {
    const emptyGroupObj = {
      source: {
        count: 0,
        groups: {},
      },
      process: {
        count: 0,
        groups: {},
      },
      sink: {
        count: 0,
        groups: {},
      },
    };
    if (!filteredJobList.length) return emptyGroupObj;
    const groupObj: any = groupBy(filteredJobList, 'jobRole');
    for (const pro in groupObj) {
      const obj: any = {};
      obj.count = groupObj[pro].length;
      obj.groups = groupBy(
        groupObj[pro].map((x: JobModel) => {
          const { jobCategory } = x;
          return {
            ...x,
            category: jobCategory || '其他',
          };
        }),
        'category',
      );
      groupObj[pro] = obj;
    }
    return Object.assign(emptyGroupObj, groupObj);
  }, [filteredJobList]);

  // 监听Ctrl + H按键, 切换是否显示V1版本算子
  const handleKeydown = e => {
    const keyCode = e.keyCode || e.which || e.charCode;
    const ctrlKey = e.ctrlKey || e.metaKey;
    // 监听ctrl + H 组合按键
    if (ctrlKey && keyCode === 72) {
      e.preventDefault();
      setShowV1Job(showV1Job => !showV1Job);
    }
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeydown);
    return () => {
      window.removeEventListener('keydown', handleKeydown);
    };
  }, []);

  useEffect(() => {
    store.fetchData(type);
  }, []);

  return (
    <div className='bg-white-1 flex-1 flex flex-col overflow-hidden'>
      <div className='py-2 px-4'>
        <Input onChange={e => setSearchKey(e.target.value)} placeholder='请输入 关键字 搜索' />
      </div>
      <Divider className='mt-0 mb-0' />
      <div className='flex-1 overflow-y-auto job-list-collapse-wrapper'>
        <Collapse
          bordered={false}
          expandIconPosition='end'
          className='w-full job-list-collapse job-list-collapse-outer'
          items={[
            ...Object.entries(groupedJobList).map(([key, item]) => ({
              label: (
                <span className='bg-white'>
                  {JobRoleType[key as keyof typeof JobRoleType]} <span className='fc-gray-7'>({item.count})</span>
                </span>
              ),
              key,
              className: `job-list-collapse-header ${key}`,
              children: (
                <Collapse
                  bordered={false}
                  expandIconPosition='end'
                  items={Object.entries(item.groups).map(([groupName, list]) => ({
                    key: groupName,
                    label: `${groupName}（${list.length}）`,
                    children: <JobListWrapper
                      list={list}
                      draggable={draggable}
                      handlerDragStart={handlerDragStart}
                    />,
                  }))}
                  className='job-list-collapse job-list-collapse-inner'
                />
              ),
            })),
            {
              label: (
                <span className='bg-white'>
                  组合算子 <span className='fc-gray-7'>({filteredJobGroupList?.length ?? 0})</span>
                </span>
              ),
              className: 'job-list-collapse-header share',
              children: <JobGroupList
                handlerDragStart={handlerDragStart}
                list={filteredJobGroupList}
                draggable={draggable}
              />,
            },
          ]}
        />
      </div>
      <Divider className='mt-0 mb-0' />
    </div>
  );
};
