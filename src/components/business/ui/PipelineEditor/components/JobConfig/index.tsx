import { memo, useEffect, useMemo, useRef, useTransition } from 'react';
import { Graph, Node } from '@antv/x6';
import { useQuery } from '@tanstack/react-query';
import { Spin, Tabs, TabsProps, Tooltip } from 'antd';
import { cloneDeep, isEqual } from 'lodash';
import PubSub from 'pubsub-js';
import { Link } from 'umi';
import { shallow } from 'zustand/shallow';

import { DynamicForm, sortParameterByOptional, validateValues } from '@/components/business/ui/DynamicForm';

import './index.less';

import { useJobStore } from '../JobList/useJobStore';

import { AdvanceConfig } from './components/AdvanceConfig';
import { BasicConfig } from './components/BasicConfig';

const JobDocLink = ({ job }) => {
  const { jobName, docUrl } = job;
  const getJobInfo = useJobStore(state => state.getJobInfo, shallow);
  const jobInfo = getJobInfo(jobName);
  return (
    <Link
      target='_blank'
      className='mr-4'
      to={`/job/doc?display=${jobInfo.jobDisplay}&jobName=${jobName}&docUrl=${encodeURIComponent(docUrl)}`}
      type='link'
    >
      <Tooltip title='配置帮助' placement='bottom'>
        <i className='iconfont icon-help-line' />
      </Tooltip>
    </Link>
  );
};

interface Props {
  selectedNode?: Node;
  graph?: Graph;
  editable?: boolean;
  isStreaming: boolean;
}
function _JobConfig(props: Props) {
  const { selectedNode, graph, editable, isStreaming } = props;
  const job = selectedNode?.data;
  const CHANNEL_ID = useRef(Math.random().toString());
  const fetchJobParameters = useJobStore(state => state.fetchJobParameters, shallow);
  const parameterFormRef = useRef();

  const { data: parameters, isLoading: loading } = useQuery({
    queryKey: [job.jobName, 'jobParameter'],
    queryFn: async () => {
      if (!job) return;
      const data = await fetchJobParameters(job.jobName);
      return data;
    },
  });

  const sortedParameters = useMemo(() => {
    return sortParameterByOptional(parameters ?? []);
  }, [parameters]);

  // 广播错误信息，因对象列表的算子配置信息无法接收错误列表，故采用广播形式传递
  function broadcastErrorList(errorList) {
    PubSub.publish('JOB_ERROR_LIST_UPATE', {
      errorList,
      channelId: CHANNEL_ID.current,
    });
  }

  // 更新算子参数配置信息
  const onParameterConfigChange = (values: JobConfig) => {
    if (values) {
      const errorList = validateValues(values, parameters);

      const updatedData: {
        jobConfig: Record<string, any>,
        errorList?: any[],
      } = {
        jobConfig: values,
      };

      if (!isEqual(errorList, selectedNode?.data?.errorList)) {
        updatedData.errorList = errorList ?? [];
        broadcastErrorList(errorList);
      }

      selectedNode?.updateData(updatedData);
    }
  };

  const onChangeAdvanceConfig = (newAdvanceOpts: Record<string, any>) => {
    const newOpts = job.jobOpts || {};
    Object.keys(newAdvanceOpts).forEach(key => {
      if (!newAdvanceOpts[key]) {
        delete newOpts[key];
      } else {
        newOpts[key] = newAdvanceOpts[key];
      }
    });
    
    selectedNode!.updateData({
      jobOpts: {
        ...newOpts,
      },
    });
  };

  const [_, startTransition] = useTransition();

  useEffect(() => {
    if (!parameters || !selectedNode?.id) return;
    startTransition(() => {
      const errorList = validateValues(job.jobConfig, parameters);
      selectedNode?.updateData({
        errorList,
      });
      broadcastErrorList(errorList);
    });
  }, [selectedNode?.id, parameters]);

  const items: TabsProps['items'] = [
    {
      key: 'parameter-config',
      label: '参数配置',
      children: loading ? (
        <div className='flex justify-center mt-4'>
          <Spin />
        </div>
      ) : (
        parameters && <DynamicForm
          ref={parameterFormRef}
          parameters={sortedParameters}
          initialValues={cloneDeep(job?.jobConfig)}
          onChange={onParameterConfigChange}
          job={job}
          disabled={!editable}
          key={selectedNode?.id}
          performanceOptimization={true}
          selectedNode={selectedNode}
          channelId={CHANNEL_ID.current}
        />
      ),
    },
    {
      key: 'basic-config',
      label: '基础配置',
      children: selectedNode && (
        <BasicConfig graph={graph} selectedNode={selectedNode} disabled={!editable} key={job.id} />
      ),
    },
    ...(isStreaming ? [
      {
        key: 'advance-config',
        label: '高级配置',
        children: selectedNode && (
          <AdvanceConfig
            values={job.jobOpts}
            jobRole={job.jobRole}
            onChange={onChangeAdvanceConfig}
            disabled={!editable}
            key={selectedNode.id}
          />
        ),
      },
    ] : [])
    ,
  ];


  return (
    <div className='job-config flex flex-col h-full pl-2'>
      <Tabs
        className='job-config-tabs'
        items={items}
        defaultActiveKey='parameter-config'
        tabBarExtraContent={job.docUrl ? { right: <JobDocLink job={ job } /> } : null}
      ></Tabs>
    </div>
  );
}

export const JobConfig = memo(_JobConfig);
