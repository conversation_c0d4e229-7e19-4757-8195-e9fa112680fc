import { DagreLayout } from '@antv/layout';

export const dagreLayout: DagreLayout = new DagreLayout({
  type: 'dagre',
  rankdir: 'TB',
  align: 'DL',
  ranksep: 20,
  nodesep: 20,
  controlPoints: true,
  sortByCombo: true,
});

export const jobGroupLayout: DagreLayout = new DagreLayout({
  type: 'dagre',
  rankdir: 'TB',
  align: 'DL',
  ranksep: 20,
  nodesep: 80,
  controlPoints: true,
  sortByCombo: true,
});
