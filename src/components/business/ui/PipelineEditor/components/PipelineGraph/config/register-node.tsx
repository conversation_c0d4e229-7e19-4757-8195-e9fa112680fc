import { Graph } from '@antv/x6';

import '@antv/x6-react-shape';

import { JobNode } from '../components/JobNode';

export const registerNode = (graph: Graph) => {
  Graph.registerNode(
    'job',
    {
      inherit: 'react-shape',
      width: 235,
      height: 28,
      component: <JobNode graph={graph} />,
      ports: {
        groups: {
          in: {
            position: 'top',
            label: {
              position: {
                name: 'top',
              },
            },
            attrs: {
              circle: {
                width: 10,
                height: 10,
                y: -5,
                x: -5,
                r: 4,
                magnet: 'passive',
                stroke: '#52C41A',
                strokeWidth: 1,
                fill: '#fff',
              },
            },
          },
          out: {
            position: 'bottom',
            label: {
              position: {
                name: 'bottom',
              },
            },
            attrs: {
              circle: {
                width: 10,
                height: 10,
                y: -5,
                x: -5,
                r: 4,
                magnet: true,
                stroke: '#096DD9',
                strokeWidth: 1,
                fill: '#fff',
              },
            },
          },
        },
      },
    },
    true,
  );
};
