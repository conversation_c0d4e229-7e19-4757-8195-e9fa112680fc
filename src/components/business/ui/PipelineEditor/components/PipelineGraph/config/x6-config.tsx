import { Graph } from '@antv/x6';
import { isEqual, pick } from 'lodash-es';

import { registerEdge } from './register-edge';
import { registerNode } from './register-node';

export const initGraph = container => {
  const graph: Graph = new Graph({
    container,
    autoResize: true,
    grid: true,
    snapline: true,
    keyboard: {
      enabled: true,
      global: true,
    },
    minimap: {
      enabled: false,
      container,
      width: 180,
      height: 120,
    },
    selecting: {
      enabled: true,
      multiple: true,
      rubberEdge: true,
      rubberNode: true,
      modifiers: 'shift',
      rubberband: true,
      strict: true,
    },
    history: {
      enabled: true,
      ignoreAdd: false,
      ignoreRemove: false,
      ignoreChange: false,
    },
    clipboard: {
      enabled: true,
      useLocalStorage: false,
    },
    panning: {
      enabled: true,
      eventTypes: ['leftMouseDown', 'mouseWheel'],
    },
    mousewheel: {
      enabled: true,
      modifiers: 'ctrl',
      factor: 1.1,
      maxScale: 4,
      minScale: 0.25,
    },
    highlighting: {
      // 当链接桩可以被链接时，在链接桩外围渲染一个 2px 宽的红色矩形框
      magnetAvailable: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#47C769',
          },
        },
      },
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            // fill: '#fff',
            stroke: '#31d0c6',
            strokeWidth: 4,
          },
        },
      },
    },
    scaling: {
      min: 0.25,
      max: 4,
    },
    connecting: {
      snap: true,
      allowBlank: false,
      allowMulti: false,
      allowLoop: false,
      highlight: true,
      router: {
        name: 'metro', // er metro manhattan
        args: {
          startDirections: ['bottom'],
          endDirections: ['top'],
        },
      },
      connector: {
        name: 'rounded', // algo-connector
      },
      connectionPoint: {
        name: 'boundary',
        args: {
          // sticky: true,
          offset: {
            x: 0,
            y: 0,
          },
        },
      },
      anchor: 'center',
      validateMagnet({ magnet }) {
        return magnet.getAttribute('port-group') === 'out';
      },
      // 在移动边的时候判断连接是否有效，如果返回 false，当鼠标放开的时候，不会连接到当前元素，否则会连接到当前元素。
      validateConnection(params) {
        const { sourceCell, sourcePort, targetCell, targetPort, targetMagnet } = params;
        if (!targetMagnet) {
          return false;
        }

        if (targetMagnet.getAttribute('port-group') !== 'in') {
          return false;
        }

        // 前序节点 不能连接 targetCell 是 sourceCell 的 前序节点
        if (
          graph.model.isPredecessor(sourceCell, targetCell, {
            deep: true,
          })
        ) {
          return false;
        }

        // 判断slot类型是否相同，类型不同，不能连线
        const sourcePortType = pick(sourceCell.ports.items.find(prot => prot.id === sourcePort)?.data, [
          'raw',
          'parameterizedTypes',
        ]);
        const targetPortType = pick(targetCell.ports.items.find(prot => prot.id === targetPort)?.data, [
          'raw',
          'parameterizedTypes',
        ]);
        if (!isEqual(sourcePortType, targetPortType)) {
          return false;
        }

        // 该slot已经有输入连线，不能连接
        const edges = graph.model.getIncomingEdges(targetCell);
        if (edges?.find(x => x.target.port === targetPort)) {
          return false;
        }

        return true;
      },
      createEdge() {
        return graph.createEdge({
          shape: 'dag-edge',
        });
      },
    },
  });

  registerEdge();
  registerNode(graph);

  return graph;
};
