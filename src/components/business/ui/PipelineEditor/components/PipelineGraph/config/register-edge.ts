import { Graph, ObjectExt, Path } from '@antv/x6';

import strawIcon from '@/assets/images/straw.svg';
import strawActiveIcon from '@/assets/images/straw-active.svg';

export const registerEdge = () => {
  Graph.registerEdge(
    'dag-edge',
    {
      inherit: 'edge',
      markup: [
        {
          tagName: 'path',
          selector: 'wrap',
          attrs: {
            fill: 'none',
            cursor: 'pointer',
            stroke: 'transparent',
            strokeLinecap: 'round',
          },
        },
        {
          tagName: 'path',
          selector: 'line',
          attrs: {
            fill: 'none',
            pointerEvents: 'none',
          },
        },
        {
          tagName: 'image',
          selector: 'image',
        },
      ],
      attrs: {
        wrap: {
          connection: true,
          strokeWidth: 10,
          strokeLinejoin: 'round',
        },
        line: {
          connection: true,
          stroke: '#bbb',
          strokeWidth: 1,
          strokeLinejoin: 'round',
          targetMarker: {
            tagName: 'path',
            d: 'M 10 -5 0 0 10 5 z',
          },
        },
        image: {
          'xlink:href': strawIcon,
          width: 20,
          height: 20,
          atConnectionRatioIgnoreGradient: 0.5,
          x: -10,
          y: -10,
          event: 'straw:click',
          cursor: 'pointer',
          display: 'none',
        },
      },
      propHooks(metadata) {
        const { data, ...others } = metadata;
        const { enableMock, mockId } = data || {};
        ObjectExt.setByPath(others, 'attrs/image/xlink:href', mockId ? strawActiveIcon : strawIcon);
        ObjectExt.setByPath(others, 'attrs/image/display', enableMock ? 'block' : 'none');
        return others;
      },
    },
    true,
  );

  Graph.registerConnector(
    'algo-connector',
    (s, e) => {
      const offset = 4;
      const deltaY = Math.abs(e.y - s.y);
      const control = Math.floor((deltaY / 3) * 2);

      const v1 = { x: s.x, y: s.y + offset + control };
      const v2 = { x: e.x, y: e.y - offset - control };

      return Path.normalize(
        `M ${s.x} ${s.y}
         L ${s.x} ${s.y + offset}
         C ${v1.x} ${v1.y} ${v2.x} ${v2.y} ${e.x} ${e.y - offset}
         L ${e.x} ${e.y}
        `,
      );
    },
    true,
  );
};
