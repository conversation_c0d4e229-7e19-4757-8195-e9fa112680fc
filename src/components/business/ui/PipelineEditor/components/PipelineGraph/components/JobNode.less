@width: 235px;
@height: 28px;

.job-node {
  width: @width;
  height: 28px;
  background: #fff;
  border: 1px solid var(--color-neutrals-4);
  box-shadow: 0 2px 6px rgb(0 0 0 / 12%);
  padding-right: 8px;

  &-tag {
    width: 24px;
    height: @height;
    line-height: @height;
    text-align: center;
    color: #fff;
    font-size: 12px;
  }

  &:hover {
    box-shadow: 0 0 10px rgb(0 0 0 / 20%);
  }

  &-disabled {
    .job-node-tag,
    img,
    span {
      opacity: 0.4;
    }

    border: 1px solid var(--color-neutrals-2);
  }
}

.x6-node-selected .job-node {
  position: relative;

  &::after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: solid 1px #096dd9;
    box-shadow: 0 2px 4px 0 rgb(185 219 255 / 100%);
  }
}

@keyframes running-line {
  0% {
    stroke-dashoffset: 1000;
  }

  100% {
    stroke-dashoffset: 0;
  }
}

.my-port {
  width: 10px;
  height: 10px;
  border: 1px solid #808080;
  border-radius: 100%;
  background: #fff;
}

.x6-port {
  &:hover {
    text {
      opacity: 1;
    }
  }
}
