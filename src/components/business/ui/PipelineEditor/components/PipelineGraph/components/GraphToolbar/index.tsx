import { forwardRef, useEffect, useMemo, useRef } from 'react';
import { flushSync } from 'react-dom';
import { CopyOutlined, DeleteOutlined, RedoOutlined, UndoOutlined } from '@ant-design/icons';
import { Graph, Node } from '@antv/x6';
import { Button, Divider, message, Popover, Tooltip } from 'antd';
import moment from 'moment';
import { Link } from 'umi';

import { useRightsHook } from '@/hooks';
import { CheckTaskPipelineItem } from '@/services';

import './index.less';

import { SaveAsJobGroupBtn } from '../SaveAsJobGroup/SaveAsJobGroupBtn';

export const generateName = (prefix: string) => {
  return `${prefix}${moment().format('YYYYMMDDHHmmSS')}${Math.floor(Math.random() * 1000)}`;
};

interface RefProps {
  debugMode: boolean;
  debugging: boolean;
}

interface Props {
  graph?: Graph;
  pipelineCheckTask?: CheckTaskPipelineItem[];
  selectedNode?: Node;
  selectedItems?: Node[];
  jobType: 'streaming' | 'batch';
  enterDebug: () => void;
  debugMode: boolean;
  onChangeSelectedNode: (node: Node | undefined) => void;
}
export const _GraphToolbar = ({
  graph,
  pipelineCheckTask,
  selectedItems,
  selectedNode,
  jobType,
  enterDebug,
  debugMode,
  onChangeSelectedNode,
}: Props) => {
  const { hasRoleRights } = useRightsHook();
  const selectedNodeId = useRef<string>();
  const isShowCheckTaskError = useMemo(() => {
    if (!pipelineCheckTask) return false;
    return pipelineCheckTask.filter(x => x.summaryStatus === 'FAILED').length > 0;
  }, [pipelineCheckTask]);

  const deleteNodes = () => {
    if (!graph) return;
    graph.trigger('node:beforeDelete', { nodes: selectedItems });
    // 批量删除操作
    graph.startBatch('deleteNode');
    selectedItems?.forEach(node => {
      // 画布上删除算子及其连线
      graph.removeNode(node.id, { tag: generateName('deleteNode') });
    });
    graph.stopBatch('deleteNode');
  };

  // 复制粘贴 注意，克隆出来的节点的data数据中id相同，不要在其他地方使用
  const copy = (copyNodeId: string) => {
    if (!graph) return;
    const selectedCell = graph.getCellById(copyNodeId);
    console.log(selectedCell);
    if (selectedCell) {
      graph.copy([selectedCell]);
      const pasteCells = graph.paste({
        offset: 50,
      });
      console.log(pasteCells);
      graph.cleanSelection();
      graph.select(pasteCells);
      message.success('粘贴成功');
    }
  };

  const handle = (operation: string) => {
    if (!graph) return;
    if (graph.isFrozen()) {
      return;
    }
    try {
      switch (operation) {
      case 'delete':
        deleteNodes();
        break;
      case 'redo':
        graph.redo();
        break;
      case 'undo':
        graph.undo();
        break;
      case 'copy': {
        selectedNodeId.current = selectedNode!.id;
        flushSync(() => {
          onChangeSelectedNode();
        });
        copy(selectedNodeId.current);  
        break;
      }
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    if (!graph) return;

    // 复制快捷操作 ctrl+c
    // graph.bindKey('ctrl+c', e => {
    //   console.log(e);
    //   const cells = graph.getSelectedCells();
    //   if (cells.length) {
    //     graph.copy(cells);
    //   }
    //   return false;
    // });

    // 粘贴快捷操作 ctrl + v
    // 复制多个节点后，拖拽算子不能一起拖动
    // graph.bindKey('ctrl+v', () => {
    //   if (!graph.isClipboardEmpty()) {
    //     const cells = graph.paste({ offset: 32 });
    //     graph.cleanSelection();
    //     graph.select(cells);
    //   }
    //   return false;
    // });

    // 前进快捷键 cmd + shift + z
    graph.bindKey('ctrl+shift+z', () => {
      graph.redo();
      return false;
    });

    // 后退快捷键 cmd + z
    graph.bindKey('ctrl+z', () => {
      graph.undo();
      return false;
    });
  }, [graph]);

  return (
    <>
      <div className='graph-toolbar text-center justify-between'>
        <div>
          <Tooltip title='撤销 Ctrl + Z' placement='bottom'>
            <Button type='text' disabled={!graph?.history.canUndo()} onClick={() => handle('undo')}>
              <UndoOutlined />
            </Button>
          </Tooltip>

          <Tooltip title='恢复 Ctrl + Shift + Z' placement='bottom'>
            <Button type='text' disabled={!graph?.history.canRedo()} onClick={() => handle('redo')}>
              <RedoOutlined />
            </Button>
          </Tooltip>

          <Divider type='vertical' />

          <Tooltip title='复制粘贴' placement='bottom'>
            <Button type='text' disabled={!selectedNode} onClick={() => handle('copy')}>
              <CopyOutlined />
            </Button>
          </Tooltip>

          {hasRoleRights('data_develop:write') && (
            <SaveAsJobGroupBtn selectedItems={selectedItems!} jobType={jobType} />
          )}

          <Tooltip title='删除 Delete' placement='bottom'>
            <Button type='text' disabled={selectedItems?.length === 0} onClick={() => handle('delete')}>
              <DeleteOutlined />
            </Button>
          </Tooltip>

          <Divider type='vertical' />

          { !debugMode && <Tooltip title='进入调试' placement='bottom'>
            <Button type='text' onClick={() => enterDebug()}>
              <i className='iconfont icon-bug-line text-sm'></i>
            </Button>
          </Tooltip>}
        </div>

        {isShowCheckTaskError && (
          <Popover
            title='检测任务附加失败列表'
            content={<PipelineCheckTaskInfo list={pipelineCheckTask} />}
            placement='bottom'
          >
            <Button type='text' className='float-right'>
              <i className='iconfont icon-a-bianzu2 text-danger'></i>
            </Button>
          </Popover>
        )}
      </div>
    </>
  );
};

const PipelineCheckTaskInfo = ({ list }: { list?: CheckTaskPipelineItem[] }) => {
  return (
    <div>
      {list?.map((job, index) => {
        if (job.summaryStatus === 'SUCCESS') return;
        return (
          <div key={index} className='w-96'>
            {job.slotMountResultList?.map(item => {
              return (
                <div key={item.jobSlot}>
                  {job.jobDisplay} [{item.jobSlot}]:
                  {item.checkTaskList.map(task => (
                    <div key={task.taskId} className='flex item-center justify-between mb-1 mt-1'>
                      <Link to={`/data-quality/realtime/task/detail/${task.taskId}`} target='_blank'>
                        {task.taskName}
                      </Link>
                      <Tooltip title={task.reason}>
                        <i
                          className={`iconfont ${
                            task.status === 'SUCCESS' ? 'icon-a-bianzu4 text-success' : 'icon-a-bianzu2 text-danger'
                          }`}
                        />
                      </Tooltip>
                    </div>
                  ))}
                </div>
              );
            })}
          </div>
        );
      })}
    </div>
  );
};

export const GraphToolbar = forwardRef<RefProps, Props>(_GraphToolbar);
