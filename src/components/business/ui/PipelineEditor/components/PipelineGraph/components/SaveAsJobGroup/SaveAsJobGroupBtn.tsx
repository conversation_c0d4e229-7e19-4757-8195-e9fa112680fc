import { useMemo, useState } from 'react';
import { Cell } from '@antv/x6';
import { Button, ButtonProps, Tooltip } from 'antd';

import { SaveAsJobGroupModal } from '@/modules/JobGroup/components/SaveAsJobGroupModal';

function getJobGroupInfo(cells: Cell[]) {
  const jobs: PipelineConfig['jobs'] = [];
  const edges: PipelineConfig['edges'] = [];

  for (let i = 0, len = cells.length; i < len; i += 1) {
    const { shape, data, id: jobId } = cells[i];
    // 算子
    if (shape === 'job') {
      const { jobName, jobConfig, jobDisplay, jobDescription, jobOpts } = data;
      const item: any = {
        jobId,
        jobName,
        jobConfig,
        jobDisplay,
        jobDescription,
      };
      if (jobOpts) {
        item.jobOpts = jobOpts;
      }
      jobs.push(item);
    } else if (shape === 'dag-edge') {
      const {
        source: { cell: from, port: fromSlot },
        target: { cell: to, port: toSlot },
        data,
      } = cells[i];
      edges.push({
        from,
        fromSlot: Number(fromSlot?.replace('out-', '')),
        to,
        toSlot: Number(toSlot?.replace('in-', '')),
        ...data,
      });
    }
  }

  return {
    jobs,
    edges: edges.filter(edge => {
      return jobs.find(x => x.jobId === edge.from) && jobs.find(x => x.jobId === edge.to);
    }),
  };
}

interface Props extends ButtonProps {
  selectedItems: Cell[];
  jobType: 'streaming' | 'batch';
}
export const SaveAsJobGroupBtn = ({ selectedItems, jobType, ...restProps }: Props) => {
  const [open, setOpen] = useState(false);
  const onSaveAs = () => {
    setOpen(true);
  };

  const disabled = useMemo(() => {
    if (selectedItems?.length === 0) return true;
    if (selectedItems?.length === 1 && selectedItems[0].shape === 'rect') return true;
    return false;
  }, [selectedItems]);
  return (
    <>
      <Tooltip title='另存为 Shift + 鼠标框选' placement='bottom'>
        <Button type='text' className='w-8' disabled={disabled} onClick={() => onSaveAs()} {...restProps}>
          <i className='iconfont icon-icon-shareall'></i>
        </Button>
      </Tooltip>
      {open && (
        <SaveAsJobGroupModal
          open={open}
          onCancel={() => setOpen(false)}
          data={getJobGroupInfo(selectedItems)}
          jobType={jobType}
        />
      )}
    </>
  );
};
