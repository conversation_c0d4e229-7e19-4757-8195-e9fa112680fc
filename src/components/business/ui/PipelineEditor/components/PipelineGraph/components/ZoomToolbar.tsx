import { useEffect, useState } from 'react';
import { CompressOutlined, LayoutOutlined, OneToOneOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { Graph } from '@antv/x6';
import { But<PERSON>, Tooltip } from 'antd';

import './ZoomToolbar.less';

import { dagreLayout } from '../config/layout';

interface Props {
  graph?: Graph;
}
export const ZoomToolbar: React.FC<Props> = ({ graph }) => {
  const [zoom, setZoom] = useState(1);

  // 放大 / 缩小
  const handleZoom = (size: number) => {
    if (!graph) return;
    const _zoom = graph.zoom();
    graph.zoomTo(Number((Number(_zoom) + size).toFixed(2)));
    graph.centerContent();
  };

  const fitScreen = () => {
    if (!graph) return;
    graph.zoomToFit();
  };

  const reLayout = () => {
    if (!graph) return;
    if (graph.isFrozen()) return;
    const { cells } = graph?.toJSON();
    const nodes = cells.filter(cell => cell.shape === 'job');
    const edges = cells.filter(cell => cell.shape === 'dag-edge');
    const layoutModel = dagreLayout.layout({
      nodes,
      edges,
    });
    graph?.fromJSON(layoutModel);
    graph.centerContent();
  };

  useEffect(() => {
    if (!graph) return;
    // 放大
    graph.bindKey('ctrl+=', () => {
      handleZoom(+0.25);
      return false;
    });

    // 缩小
    graph.bindKey('ctrl+-', () => {
      handleZoom(-0.25);
      return false;
    });

    // 恢复原始大小
    graph.bindKey('ctrl+0', () => {
      graph.zoomTo(1);
    });

    // 重置View
    graph.bindKey('ctrl+h', () => {
      // todo handle('resetView')
      return false;
    });

    // 自适应屏幕
    graph.bindKey('ctrl+shift+h', () => {
      graph.fitToContent();
      return false;
    });

    // 画布缩放触发事件
    graph.on('scale', ({ sx }: { sx: number }) => {
      // 计算出画布缩放的比例，用于显示
      setZoom(Math.floor(sx * 100) / 100);
    });
  }, [graph]);

  return (
    <div className='zoom-toolbar flex flex-col text-center'>
      <Tooltip title='放大 Ctrl + ' placement='right'>
        <Button type='text' disabled={zoom >= 4} onClick={() => handleZoom(0.1)}>
          <ZoomInOutlined />
        </Button>
      </Tooltip>

      <Tooltip title='缩小 Ctrl - ' placement='right'>
        <Button type='text' disabled={zoom <= 0.25} onClick={() => handleZoom(-0.1)}>
          <ZoomOutOutlined />
        </Button>
      </Tooltip>

      <Tooltip title='缩放到1:1' placement='right'>
        <Button type='text' disabled={zoom == 1} onClick={() => graph?.zoomTo(1)}>
          <OneToOneOutlined />
        </Button>
      </Tooltip>

      <Tooltip title='重新布局' placement='right'>
        <Button type='text' onClick={() => reLayout()}>
          <LayoutOutlined />
        </Button>
      </Tooltip>
      <Tooltip title='缩放到适配屏幕' placement='right'>
        <Button type='text' onClick={() => fitScreen()}>
          <CompressOutlined />
        </Button>
      </Tooltip>
    </div>
  );
};
