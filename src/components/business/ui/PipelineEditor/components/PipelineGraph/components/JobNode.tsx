import React from 'react';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { Graph } from '@antv/x6';
import { ReactShape } from '@antv/x6-react-shape';
import { Tooltip } from 'antd';

import { JobDefaultIcon, JobRoleTypeStyle, PROJECT } from '@/constants';

import './JobNode.less';

export class JobNode extends React.Component<{
  node?: ReactShape;
  text: string;
  graph: Graph;
}> {
  // todo
  // shouldComponentUpdate() {
  //   const node = this.props.node
  //   if (node) {
  //     if (node.hasChanged('data')) {
  //       return true
  //     }
  //   }

  //   return false
  // }

  toggleNodeDebugMode(e) {
    const { node, graph } = this.props;
    const isFreeze = graph.isFrozen();
    if (isFreeze) return;

    graph.disableHistory();

    const { jobOpts } = node.data;
    node.setData({
      jobOpts: {
        ...jobOpts,
        enableDebug: !jobOpts.enableDebug,
      },
    });

    graph.enableHistory();
  }

  toggleDisableExport(e) {
    const { node, graph } = this.props;
    graph.disableHistory();

    const { jobOpts } = node.data;
    node.setData({
      jobOpts: {
        ...jobOpts,
        isDisableExport: !jobOpts.isDisableExport,
      },
    });

    graph.enableHistory();
  }

  render() {
    const { jobOpts, iconUrl, jobRole, jobDisplay, errorList, summaryStatus, slotMountResultList } = this.props.node
      ?.data as GraphJobData;
    return (
      <div className={`job-node flex items-center ${jobOpts?.isDisableExport ? 'job-node-disabled' : ''}`}>
        <div className='job-node-tag' style={{ background: JobRoleTypeStyle[jobRole]?.bgColor }}>
          {JobRoleTypeStyle[jobRole]?.tag}
        </div>
        <img
          src={iconUrl ? `${PROJECT.baseUrl}${iconUrl}` : JobDefaultIcon[jobRole]}
          width='14'
          height='14'
          className='ml-2 mr-2'
        />
        <span className='flex-1 line-clamp-1 text-sm text-gray-7'>
          {jobDisplay === undefined ? '算子找不到了' : jobDisplay}
        </span>
        {errorList.length > 0 && (
          <Tooltip title='有必填项未填写'>
            <i className='iconfont icon-error-fill text-danger'></i>
          </Tooltip>
        )}
        {jobRole !== 'sink' && typeof jobOpts?.enableDebug !== 'undefined' && (
          <span
            title={jobOpts?.enableDebug ? '观测' : '不观测'}
            className={`p-2 cursor-pointer z-50 ${jobOpts?.enableDebug ? 'fc-primary' : ''}`}
            onClick={event => this.toggleNodeDebugMode(event)}
          >
            {jobOpts?.enableDebug ? <EyeOutlined /> : <EyeInvisibleOutlined />}
          </span>
        )}
        {jobRole === 'sink' && jobOpts?.isDisableExport && (
          <i
            title='取消禁用'
            onClick={event => this.toggleDisableExport(event)}
            className={`iconfont icon-block-line1 mr-2 z-50 cursor-pointer text-base ${
              jobOpts?.isDisableExport && 'text-danger'
            }`}
          ></i>
        )}
        {summaryStatus === 'SUCCESS' && (
          <Tooltip title='指标算子附加成功'>
            <i className='iconfont icon-a-bianzu4 text-success' />
          </Tooltip>
        )}
        {summaryStatus === 'FAILED' && (
          <Tooltip title='指标算子附加失败'>
            <i className='iconfont icon-a-bianzu2 text-danger'></i>
          </Tooltip>
        )}
      </div>
    );
  }
}
