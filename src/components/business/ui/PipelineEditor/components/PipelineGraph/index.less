// .x6-graph.x6-graph-pannable {
//   cursor: url('/images/icons/grab.svg') 13 4, -webkit-grab;
// }

// .x6-graph.x6-graph-panning {
//   cursor: url('/images/icons/hand-grabbing.svg') 16 16, -webkit-grabbing;
// }

// .x6-node [magnet='true'] {
//   cursor: url('/images/icons/crosshair.svg') 16 16, crosshair;
// }

.x6-graph .x6-widget-minimap {
  position: absolute;
  bottom: 4px;
  left: 4px;
  background-color: rgb(250 250 250);
  border: 1px solid var(--color-gray-3);
}
