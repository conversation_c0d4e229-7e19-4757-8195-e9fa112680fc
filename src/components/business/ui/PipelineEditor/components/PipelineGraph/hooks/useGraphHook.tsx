import { useRef, useState } from 'react';
import { useActivate, useUnactivate } from 'react-activation';
import { EdgeView, Graph, Model, Node, Vector } from '@antv/x6';
import { Dnd } from '@antv/x6/lib/addon';
import { omit } from 'lodash';

import strawIcon from '@/assets/images/straw.svg';
import strawActiveIcon from '@/assets/images/straw-active.svg';

import { getDnd } from '../config/config-dnd';
import { jobGroupLayout } from '../config/layout';
import { initGraph } from '../config/x6-config';

// import { initX6Event } from '../config/x6-events';

/**
 * 获取不同数据量下的动画档位
 *
 * @param dataNum 数据量
 */
export const getAnimationGears = function (dataNum) {
  const maxNum = 100;
  if (dataNum === 0) return 0;
  if (dataNum <= Math.ceil(maxNum * 0.33)) return 1;
  if (dataNum <= Math.ceil(maxNum * 0.66)) return 2;
  return 3;
};

// graph model -> pipelineData
export const getJsonFromGraph = (graph: Graph) => {
  const { cells } = graph?.toJSON();
  // todo 类型定义
  const nodes: any = [];
  const edges: any = [];
  const pipelineUi: Record<string, any> = {};
  for (let i = 0, len = cells.length; i < len; i += 1) {
    const { shape, data, position, id: jobId } = cells[i];
    // 算子
    if (shape === 'job') {
      const { jobName, jobConfig, jobDisplay, jobDescription, jobUiData, jobOpts } = data;
      const item: any = {
        jobId,
        jobName,
        jobConfig,
        jobDisplay,
        jobDescription,
      };
      if (jobOpts) {
        item.jobOpts = jobOpts;
      }
      nodes.push(item);
      pipelineUi[jobId!] = {
        ...jobUiData,
        ...position,
      };
    } else if (shape === 'dag-edge') {
      const {
        source: { cell: from, port: fromSlot },
        target: { cell: to, port: toSlot },
        data,
      } = cells[i];
      const fromSlotNum = fromSlot?.replace('out-', '');
      const toSlotNum = toSlot?.replace('in-', '');
      edges.push({
        from,
        fromSlot: fromSlotNum === 'null' ? null : Number(fromSlotNum),
        to,
        toSlot: toSlotNum === 'null' ? null : Number(toSlotNum),
        ...data,
      });
    }
  }
  return {
    pipelineConfig: {
      jobs: nodes,
      edges,
    },
    pipelineUi,
  };
};

type GraphToPipelineData = ({
  clearDebugTag,
  graph,
  pipelineData,
}: {
  clearDebugTag: boolean;
  graph: Graph;
  pipelineData: ProcessModel;
}) => Promise<ProcessModel>;

/**
 * @description 根据画布内容生成pipelineData
 * @params args: { clearDebugTag: boolean } 是否保留jobOpts中enableDebug标记。只有在用户调试时需要保持，其他清空下清除。
 */
export const graphToPipelineData: GraphToPipelineData = async ({ clearDebugTag, graph, pipelineData }) => {
  const {
    pipelineConfig: { jobs, edges },
    pipelineUi,
  } = getJsonFromGraph(graph);
  // 合并值
  const _pipelineData: ProcessModel = {
    ...pipelineData,
    pipelineConfig: {
      jobs,
      edges,
      opts: pipelineData.pipelineConfig.opts ?? {},
      ...omit(pipelineData.pipelineConfig, ['jobs', 'edges', 'opts']),
    },
    pipelineUi,
  };
  if (clearDebugTag) {
    // 修剪jobOpts中的enableDebug, isDisableExport
    const { jobs, edges } = _pipelineData.pipelineConfig;
    jobs?.forEach(({ jobOpts }) => {
      delete jobOpts?.enableDebug;
      delete jobOpts?.isDisableExport;
    });
    edges?.forEach(edge => {
      delete edge?.enableMock;
      delete edge?.mockId;
      delete edge?.animationGear;
    });
  }

  return _pipelineData;
};

export const useGraphHook = () => {
  const [graph, setGraph] = useState<Graph>();
  const [dnd, setDnd] = useState<Dnd>();
  const [zoom, setZoom] = useState(1);
  const [selectedNode, setSelectedNode] = useState<Node | undefined>();
  const [selectedItems, setSelectedItems] = useState<Node[]>();
  const isActive = useRef<boolean>(true);

  useActivate(() => {
    isActive.current = true;
  });

  useUnactivate(() => {
    isActive.current = false;
  });

  const initEvents = (graph: Graph) => {
    // 不需要操作pipelineData的事件
    graph.on('node:added', ({ node: groupNode }) => {
      if (!isActive.current) return;
      if (graph.isFrozen()) return;

      const { type, nodes, edges } = groupNode.getData();
      if (type === 'addJobGroup') {
        graph.disableHistory();

        graph.cleanSelection();

        nodes.forEach(node => {
          const newId = `job-${Math.floor(Math.random() * 100000)}`;
          edges.forEach(edge => {
            if (edge.source.cell === node.id) {
              edge.source.cell = newId;
            }
            if (edge.target.cell === node.id) {
              edge.target.cell = newId;
            }
          });
          node.id = newId;
        });

        const layoutResult = jobGroupLayout.layout({
          nodes,
          edges,
        });

        // 获取组合节点位置
        const { x: baseX, y: baseY } = groupNode.getPosition();

        // 新增节点加入画布，并添加为组合节点的child
        layoutResult.nodes?.forEach((node: Node) => {
          const childNode = graph.addNode({
            ...node,
            x: baseX + (node.x as number), // 根据组合节点位置，计算child节点位置
            y: baseY + (node.y as number),
          });
          groupNode.addChild(childNode);
        });

        // 计算算子x坐标最大点，计算算子y坐标最大点，用于确定组合节点的大小
        let maxWidth = 0;
        let maxHeight = 0;
        layoutResult.nodes?.forEach(node => {
          if (node.x > maxWidth) {
            maxWidth = node.x;
          }
          if (node.y > maxHeight) {
            maxHeight = node.y;
          }
        });

        graph.addEdges(layoutResult.edges ?? []);

        groupNode.prop({
          size: {
            width: maxWidth + 335,
            height: maxHeight + 68,
          },
        });

        graph.select(groupNode);
        graph.enableHistory();
      }
    });

    // 连线选中时，高亮显示
    graph.on('edge:selected', ({ edge }) => {
      if (!isActive.current) return;
      if (graph.isFrozen()) return;
      graph.disableHistory();
      edge.attr({
        line: {
          stroke: 'rgb(39, 181, 186)',
        },
      });
      graph.enableHistory();
    });

    // 连线取消选中时，恢复原来颜色
    graph.on('edge:unselected', ({ edge }) => {
      if (!isActive.current) return;
      if (graph.isFrozen()) return;
      graph.disableHistory();
      edge.attr({
        line: {
          stroke: '#bbb',
        },
      });
      graph.enableHistory();
    });

    // 调试观测边运动方案
    graph.on('edge:running-1', ({ edge, animationGear }) => {
      if (!isActive.current) return;
      // 方案2
      if (animationGear > 0) {
        let animationTime = 30;
        switch (animationGear) {
        case 1:
          animationTime = 100;
          break;
        case 2:
          animationTime = 50;
          break;
        case 3:
          animationTime = 1;
          break;
        }
        edge?.setAttrs({
          line: {
            stroke: '#1890ff',
            strokeDasharray: 5,
            style: {
              animation: `running-line ${animationTime}s infinite linear`,
            },
          },
        });
      } else {
        edge?.setAttrs({
          line: {
            stroke: '#bbb',
            strokeDasharray: null,
            style: {
              animation: null,
            },
          },
        });
      }
    });

    graph.on('edge:running-2', ({ edge, animationGear }) => {
      if (!isActive.current) return;
      // 方案3
      let animationTime: number = 0;
      switch (animationGear) {
      case 1:
        animationTime = 3;
        break;
      case 2:
        animationTime = 2;
        break;
      case 3:
        animationTime = 1;
        break;
      }
      const view = graph.findViewByCell(edge) as EdgeView;
      const path = view.findOne('path') as SVGPathElement;
      if (path) {
        if (animationTime) {
          const children = Array.from(path.parentNode?.children ?? []);
          const index = children.findIndex(x => x.nodeName === 'circle');
          if (index !== -1) {
            const item = path.parentNode?.children[index];
            path.parentNode?.removeChild(item!);
          }
          const token = Vector.create('circle', { r: 3, fill: '#1890ff' });
          token.animateAlongPath(
            {
              dur: `${animationTime}s`,
              repeatCount: 'indefinite',
              timing: 'easeQuadInOut',
            },
            path,
          );
          token.appendTo(path.parentNode as SVGGElement);
        } else {
          const children = Array.from(path.parentNode?.children ?? []);
          const index = children.findIndex(x => x.nodeName === 'circle');
          if (index !== -1) {
            const item = path.parentNode?.children[index];
            path.parentNode?.removeChild(item!);
          }
        }
      }
    });

    graph.on('running', ({ runningData }) => {
      if (!isActive.current) return;
      const edges = graph.getEdges();
      edges.forEach(edge => {
        const {
          source: { cell: jobId, port },
        } = edge;
        const key = `${jobId}-${port.replace('out-', '')}`;
        const dataNum = runningData?.[key]?.length ?? 0;
        const animationGear = getAnimationGears(dataNum);
        const oldAnimationGear = edge.getData()?.animationGear;
        if (oldAnimationGear !== animationGear) {
          edge.setData(
            {
              animationGear,
            },
            { silent: true },
          );
          graph.trigger('edge:running-2', { edge, animationGear });
        }
      });
    });

    graph.on('signal', ({ cell }) => {
      if (!isActive.current) return;
      if (cell.isEdge()) {
        // 方案1 来一条消息触发一次，数据量小时效果好
        const view = graph.findViewByCell(cell) as EdgeView;
        const token = Vector.create('circle', { r: 3, fill: '#feb662' });
        const animationTime = 1000;
        if (animationTime) {
          setTimeout(() => {
            const stop = view.sendToken(token.node, animationTime);
            setTimeout(stop, animationTime);
          }, 300);
        }
        // 方案1 end
      }
    });

    // 进入调试
    graph.on('debug:enter', () => {
      if (!isActive.current) return;
      graph.disableHistory();
      // 显示注入的吸管
      const edges = graph.getEdges();
      edges.forEach(edge => {
        edge.setData(
          {
            mockId: null,
            enableMock: true,
          },
          { silent: false },
        );
        edge.setAttrs({
          image: {
            display: 'block',
          },
        });
      });
      graph.enableHistory();
    });

    // 退出调试
    graph.on('debug:outer', () => {
      if (!isActive.current) return;
      graph.disableHistory();
      // 隐藏注入的吸管
      const edges = graph.getEdges();
      edges.forEach(edge => {
        edge.setAttrs({
          image: {
            'xlink:href': strawIcon,
            display: 'none',
          },
        });
        edge.setData(
          {
            mockId: null,
            enableMock: null,
          },
          { silent: false },
        );
      });
      graph.trigger('mockId:changed', undefined);
      graph.enableHistory();
    });

    // 点击吸管，改变吸管状态
    // 各个吸管之间只能点亮一个
    graph.on('straw:click', ({ cell }) => {
      if (!isActive.current) return;
      if (graph.isFrozen()) return;
      graph.disableHistory();
      const edges = graph.getEdges();
      edges.forEach(x => {
        if (x.id !== cell.id) {
          x.setData(
            {
              mockId: null,
              // enableMock: null,
            },
            { silent: true },
          );
          x.setAttrs({
            image: {
              'xlink:href': strawIcon,
            },
          });
        }
      });
      const oldData = cell.getData();
      if (oldData?.mockId) {
        cell.setData({
          mockId: null,
          // enableMock: null,
        });
        cell.setAttrs({
          image: {
            'xlink:href': strawIcon,
          },
        });
        graph.trigger('mockId:changed', null);
      } else {
        const newMockId = cell.getData()?.mockId || new Date().getTime();
        cell.setData({
          mockId: newMockId,
          enableMock: true,
        });
        cell.setAttrs({
          image: {
            'xlink:href': strawActiveIcon,
          },
        });

        graph.trigger('mockId:changed', newMockId);
      }
      graph.enableHistory();
    });

    // 节点点击事件
    graph.on('node:click', ({ e, node }) => {
      if (!isActive.current) return;
      const className = e.target?.className;
      if (typeof className !== 'string') return;
      if (className?.includes('iconfont') && !className?.includes('icon-error-fill')) {
        return;
      }
      // 当选中的算子个数只有一个时，显示算子参数配置面板，否则隐藏面板
      if (selectedNode?.id === node.id) {
        setSelectedNode(undefined);
      } else {
        setSelectedNode(node);
      }
    });

    // 选中的元素集合发生变化时记录选中元素
    graph.on('selection:changed', ({ selected }: { selected: Node[] }) => {
      if (!isActive.current) return;
      if (selected.length === 0) {
        setSelectedNode(undefined);
      }
      setSelectedItems(selected);
    });

    // 当点击空白区域，隐藏算子参数面板
    graph.on('blank:click', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 撤销 隐藏算子参数配置
    graph.history.on('undo', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 重做 隐藏算子参数配置
    graph.history.on('redo', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 响应键盘删除事件，删除选中项，并且清空选中节点
    graph.bindKey('delete', () => {
      if (!isActive.current) return;
      const selectedItems = graph.getSelectedCells();
      if (selectedItems.length > 0) {
        graph.removeCells(selectedItems);
        setSelectedNode(undefined);
      }
    });

    // 自定义事件 清空选中算子，关闭参数配置面板
    graph.on('reset:selectedNode', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });

    // 自定义事件 开始调试，关闭左侧参数配置面板
    graph.on('debug:enter', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
      graph.cleanSelection();
    });

    // 当删除节点时，关闭被删除节点的配置层
    graph.on('node:beforeDelete', () => {
      if (!isActive.current) return;
      setSelectedNode(undefined);
    });
  };

  const graphRender = (graphModel: Model.FromJSONData) => {
    if (!graph) return;
    graph.fromJSON(graphModel);
    graph.centerContent({
      useCellGeometry: true,
    });
  };

  const graphInit = (container: HTMLDivElement) => {
    const graph = initGraph(container);

    initEvents(graph);
    setDnd(getDnd(graph));

    setGraph(graph);
  };

  return {
    graph,
    dnd,
    selectedNode,
    zoom,
    selectedItems,
    setZoom,
    setGraph,
    graphInit,
    graphRender,
    getJsonFromGraph,
  };
};
