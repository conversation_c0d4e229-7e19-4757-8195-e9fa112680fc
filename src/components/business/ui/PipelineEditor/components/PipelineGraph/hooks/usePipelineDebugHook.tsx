import { useEffect, useState } from 'react';
import { Graph } from '@antv/x6';
import { message } from 'antd';
import { cloneDeep } from 'lodash-es';
import PubSub from 'pubsub-js';
import { shallow } from 'zustand/shallow';

import { RunningLogType } from '@/constants';
import { DEBUG_ENTER, DEBUG_OUTER, DEBUG_START, DEBUG_STOP, RECEIVE_DEBUG_MESSAGE } from '@/constants/eventTypes';
import { SocketApi } from '@/utils/websocket';

import { useDebugStore } from '../../PipelineDebugPanel/useDebugStore';

import { graphToPipelineData } from './useGraphHook';

export interface JobDebugRunningLog {
  type: RunningLogType;
  message: string;
  id?: number;
  display?: string;
  typeNumber?: number;
}

// 修剪pipelineData, 查看opts中的标记isDisableExport，如果用户禁用输出算子，修剪掉相应的算子及连线
export const trimPipelineData = (pipelineData: ProcessModel) => {
  const {
    pipelineConfig: { jobs, edges },
  } = pipelineData;
  const disableJobs = jobs.filter(job => job.jobOpts?.isDisableExport).map(job => job.jobId);
  const newPipelineData = cloneDeep(pipelineData);
  newPipelineData.pipelineConfig.jobs = jobs.filter(job => !job.jobOpts?.isDisableExport);
  newPipelineData.pipelineConfig.edges = edges
    .filter(edge => !disableJobs.includes(edge.to))
    ?.map(edge => {
      // 转换前后端数据模型不一致地方。后端有mockId enableMock才能为true
      if (!edge.mockId) {
        edge.enableMock = false;
      } else {
        edge.enableMock = true;
      }
      return edge;
    });

  return newPipelineData;
};

/**
 * 获取后台需要的数据，
 *
 * @param pipelineData 需要处理的数据
 * @param isDeletePipelineUi 是否删除 PipelineUi
 */
export const getPipelineDataNoJobViewData = function (pipelineData: ProcessModel, isDeletePipelineUi = false) {
  const newPipelineData = cloneDeep(pipelineData);
  if (isDeletePipelineUi) {
    delete newPipelineData.pipelineUi;
  }
  return newPipelineData;
};

export const usePipelineDebugHook = (graph: Graph, pipelineData: ProcessModel) => {
  const [channelId] = useState((Math.random() * 10000).toFixed(0));
  const [ws, setWs] = useState<SocketApi>();
  const [mockId, setMockId] = useState<string | undefined | null>();
  // 是否是调试模式
  const [debugMode, setDebugMode] = useState(false);
  // 是否开始了调试
  const [debugging, setDebugging] = useState(false);
  // 是否打开全部算子的调试观测，默认打开
  const [observeAll, setObserveAll] = useState(true);
  // 是否禁用所有输出算子
  const [disableAllSinkJob, setDisableAllSinkJob] = useState(true);

  const setTrackUrl = useDebugStore(state => state.setTrackUrl, shallow);
  const setObserveData = useDebugStore(state => state.setObserveData, shallow);
  const clearObserveData = useDebugStore(state => state.clearObserveData, shallow);

  // 进入调试模式
  const enterDebug = () => {
    if (!graph) return;
    // 禁用所有输出算子
    setDisableAllSinkJob(true);
    // 设置状态为调试模式
    setDebugMode(true);
    // 设置算子为调试模式, 显示依赖注入吸管
    graph.trigger('debug:enter');

    setNodeDebug(true);
    // 默认启用所有算子的调试观测
    toggleNodeDebugMode(true);
    disableAllExportJob(true);

    PubSub.publish(DEBUG_ENTER, {
      channelId,
    });
  };

  // 退出调试模式
  const outerDebug = () => {
    if (!graph) return;
    // 设置状态为退出调试模式
    setDebugMode(false);
    // 设置算子为普通模式, 隐藏依赖注入吸管
    graph.trigger('debug:outer');

    stopDebug();

    setNodeDebug(false);
    toggleNodeDebugMode(undefined);
    disableAllExportJob(undefined);

    PubSub.publish(DEBUG_OUTER, {
      channelId,
    });
    clearObserveData(channelId);
  };

  // 开始调试
  const startDebug = async () => {
    if (!graph) return;
    // 冻结画布
    graph.freeze();
    setDebugging(true);

    graph.trigger('debug:start');
    graph.trigger('debug:reset');

    setTimeout(async() => {
      const newPipelineData = await graphToPipelineData({
        graph,
        pipelineData,
        clearDebugTag: false,
      });

      socketStart(newPipelineData);

      PubSub.publish(DEBUG_START, {
        channelId,
      });
      clearObserveData(channelId);
    });
  };

  // 停止调试
  const stopDebug = () => {
    if (!graph) return;
    // 解冻画布
    graph.unfreeze();
    setDebugging(false);
    graph.trigger('debug:stop');
    ws?.close();
    setWs(undefined);
    // 清空图上线条的动画状态
    graph.trigger('running', { runningData: [] });
    PubSub.publish(DEBUG_STOP, {
      channelId,
    });
  };

  // 切换是否启用所有算子的调试观测
  const toggleObserveAll = () => {
    setObserveAll((flag: boolean) => {
      return !flag;
    });
    toggleNodeDebugMode(!observeAll);
  };

  // 切换是否禁用输出算子
  const toggleDisableSinkJob = () => {
    setDisableAllSinkJob((flag: boolean) => {
      return !flag;
    });
    disableAllExportJob(!disableAllSinkJob);
  };
  // 设置算子debug状态
  const setNodeDebug = (isDebugNode?: boolean) => {
    if (!graph) return;
    graph.disableHistory();
    const nodes = graph.getNodes();
    const edges = graph.getEdges();
    // 清除节点上的debug标记
    nodes.forEach(node => {
      const { jobOpts } = node.data;
      const newJobOpts = Object.assign({}, jobOpts);

      if (isDebugNode === undefined) {
        delete newJobOpts.enableDebug;
      } else {
        newJobOpts.enableDebug = isDebugNode;
      }

      node.setData({
        jobOpts: {
          ...newJobOpts,
        },
      });
    });

    // 清除边上的debug标记
    edges.forEach(edge => {
      const data = edge.getData();
      delete data?.enableMock;
      delete data?.mockId;
    });

    graph.enableHistory();
  };

  // 设置算子是否可观测
  const toggleNodeDebugMode = (enableDebug: boolean | undefined) => {
    if (!graph) return;

    const isFreeze = graph.isFrozen();
    if (isFreeze) return;

    graph.disableHistory();
    const nodes = graph.getNodes();
    // 清除节点上的debug标记
    nodes.forEach(node => {
      const { jobOpts } = node.data;
      const newJobOpts = Object.assign({}, jobOpts);
      if (typeof enableDebug === 'boolean') {
        newJobOpts.enableDebug = enableDebug;
      } else {
        delete newJobOpts.enableDebug;
      }
      node.updateData({
        jobOpts: {
          ...newJobOpts,
        },
      });
    });
    graph.enableHistory();
  };

  // 禁用/启用 所有的输出算子  isDisableExport ==== true 禁用 isDisableExport === undefined 启用
  const disableAllExportJob = (isDisableExport?: boolean) => {
    if (!graph) return;
    const isFreeze = graph.isFrozen();
    if (isFreeze) return;
    graph.disableHistory();
    const nodes = graph.getNodes();
    // 过滤输出算子
    const sinkNodes = nodes.filter(node => node.data.jobRole === 'sink');
    sinkNodes.forEach(node => {
      const { jobOpts } = node.data;
      const newJobOpts = Object.assign({}, jobOpts);

      if (isDisableExport === undefined) {
        delete newJobOpts.isDisableExport;
      } else {
        newJobOpts.isDisableExport = isDisableExport;
      }

      node.updateData({
        jobOpts: {
          ...newJobOpts,
        },
      });
    });
    graph.enableHistory();
  };

  // 开始调试
  const socketStart = (pipelineData: ProcessModel) => {
    const newPipelineData = trimPipelineData(pipelineData);
    console.log('todo 测试调试配置是否正确', newPipelineData, '.....');
    const data = {
      code: '1001',
      message: getPipelineDataNoJobViewData(newPipelineData),
    };

    const ws = new SocketApi('/ws/pipeline/ping', data, {
      onMessageCallback: (wsData: WebSocketData) => {
        const { code, message } = wsData;
        switch (code) {
        case '1003':
          setObserveData(channelId, wsData);
          break;
        case '1004':
        case '1009':
          PubSub.publish(RECEIVE_DEBUG_MESSAGE, {
            channelId,
            wsData,
          });
          break;
        case '1005':
          setTrackUrl(channelId, message);
        }
      },
      closeCallback: () => {
        const errorInfo = pipelineData.pipelineType === 'streaming' ? '调试异常,请重新开始!' : '调试结束;';
        message[pipelineData.pipelineType === 'streaming' ? 'error' : 'success'](errorInfo);
        PubSub.publish(RECEIVE_DEBUG_MESSAGE, {
          channelId,
          wsData: {
            code: '9999',
            message: errorInfo,
          },
        });
        stopDebug();
      },
    });
    setWs(ws);
  };

  // 发送消息到服务器
  const sendMessage = (data: { code: string; job_id: string; message: string }) => {
    ws?.send(data);
  };

  useEffect(() => {
    if (!graph) return;
    graph.on('mockId:changed', (mockId: number | undefined | null) => {
      setMockId(mockId);
    });
  }, [graph]);

  useEffect(() => {
    return () => {
      ws?.close();
    };
  }, [ws]);

  return {
    ws,
    mockId,
    channelId,
    debugging,
    debugMode,
    observeAll,
    disableAllSinkJob,
    sendMessage,
    enterDebug,
    outerDebug,
    startDebug,
    stopDebug,
    toggleObserveAll,
    toggleDisableSinkJob,
  };
};
