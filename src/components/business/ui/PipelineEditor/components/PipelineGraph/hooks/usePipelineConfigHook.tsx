import type { Edge, Node } from '@antv/x6';

import { CheckTaskPipelineItem } from '@/services';

import { useJobStore } from '../../JobList/useJobStore';
import { dagreLayout } from '../config/layout';

/**
 * 随机生成新id
 *
 * @param name 节点名称
 */
export const generateNewId = function (name = '') {
  return `${name}${Date.now()}${Math.floor(Math.random() * 100000)}`;
};

/**
 * 获取节点名称
 *
 * @param str 节点名称
 */
export const getNodeName = function (str: string) {
  const idx = str.lastIndexOf('.') + 1;
  if (idx >= 0) return str.slice(idx);
  return str;
};

export const usePipelineConfigHook = (
  pipelineData: Pick<ProcessModel, 'pipelineConfig' | 'pipelineUi'>,
  pipelineCheckTask?: CheckTaskPipelineItem[],
) => {
  const { pipelineConfig, pipelineUi } = pipelineData;
  const { getJobInfo } = useJobStore();
  const getNodeMetadata = ({
    jobId,
    jobName,
    jobDisplay,
    jobDescription,
    jobConfig,
    jobOpts,
  }: {
    jobId?: string;
    jobName: string;
    jobDisplay: string;
    jobDescription: string;
    jobConfig: Record<string, any>;
    jobOpts?: Record<string, any>;
  }) => {
    // 算子基本信息
    const job = getJobInfo(jobName);

    const { jobDisplay: initialDisplay, jobDescription: initialDescription, inTypes, outTypes } = job;
    const newJobId = jobId ?? generateNewId(jobName);

    // pipelineUi数据写入画布的算子jobUiData，用于显示、更新算子基本信息
    const jobUiData = (pipelineUi ?? {})[newJobId] || {};

    // 查找算子的检测任务信息
    const checkTask = pipelineCheckTask?.find(x => {
      return x.jobName === jobName && x.jobDisplay === (jobDisplay ?? initialDisplay);
    });

    return {
      id: newJobId, // 算子id
      shape: 'job', // 算子形状
      data: {
        ...(job.jobName ? job : { jobId, jobName }), // 算子基本信息
        jobDisplay: jobDisplay ?? initialDisplay,
        jobDescription: jobDescription ?? initialDescription,
        jobUiData, // 算子ui数据
        jobConfig: {
          ...jobConfig,
        },
        id: newJobId,
        jobOpts,
        errorList: [],
        summaryStatus: checkTask?.summaryStatus,
        slotMountResultList: checkTask?.slotMountResultList,
      },
      ports: [
        ...(inTypes ?? []).map((x, index) => {
          const { raw, description } = x;
          return {
            group: 'in',
            id: `in-${index}`,
            data: x,
            attrs: {
              text: {
                text: description ?? `${raw.substring(Number(raw.lastIndexOf('.')) + 1)}`,
                opacity: 0,
              },
            },
          };
        }),
        ...(outTypes ?? [])?.map((x, index) => {
          const { raw, description } = x;
          return {
            group: 'out',
            id: `out-${index}`,
            data: x,
            attrs: {
              text: {
                text: description ?? `${raw.substring(Number(raw.lastIndexOf('.')) + 1)}`,
                opacity: 0,
              },
            },
          };
        }),
      ],
    };
  };

  // pipelineConfig.jobs to graph nodes
  const jobs2Nodes = (jobs: PipelineJob[]) => {
    const nodes: Node.Metadata[] = [];
    // 算子 -> 图节点
    jobs.forEach(({ jobId, jobName, jobDisplay, jobDescription, jobConfig, jobOpts }) => {
      const node = getNodeMetadata({ jobId, jobName, jobDisplay, jobDescription, jobConfig, jobOpts });
      if (node) {
        nodes.push(node);
      }
    });
    return nodes;
  };

  // pipelineConfig.edges to graph edges
  const edges2Edges = (_edges: PipelineEdge[]) => {
    const edges: Edge.Metadata[] = [];
    /**
     * pipelineData的连线 -> 图的边
     * 处理这种数据格式：{ from: jobId[1]  , to: jobId[0] }
     * 上一个算子的输出节点 -> 另一个算子的输入节点
     */
    const reg = /\[(.+)\]/;
    _edges.forEach(({ from, fromSlot, to, toSlot, enableMock, mockId }) => {
      const edgeFrom = reg.exec(from);
      const edgeTo = reg.exec(to);
      const source = {
        cell: from,
        port: `out-${fromSlot}`,
      };

      if (edgeFrom) {
        source.cell = from.slice(0, edgeFrom.index);
        source.port = `out-${+edgeFrom[1]}`;
      }

      const target = {
        cell: to,
        port: `in-${toSlot}`,
      };

      if (edgeTo) {
        target.cell = to.slice(0, edgeTo.index);
        target.port = `in-${+edgeTo[1]}`;
      }

      const data: Record<string, any> = {};
      if (enableMock !== null) {
        data.enableMock = enableMock;
      }
      if (mockId !== null) {
        data.mockId = mockId;
      }

      edges.push({
        shape: 'dag-edge',
        source,
        target,
        data: {
          ...data,
        },
      });
    });

    return edges;
  };

  // 转换 pipelineConfig 成图数据
  const getGraphModel = () => {
    const { jobs, edges: _edges, opts } = pipelineConfig;
    const nodes: Node.Metadata[] = jobs2Nodes(jobs);
    const edges: Edge.Metadata[] = edges2Edges(_edges);

    return {
      nodes,
      edges,
      opts,
    };
  };

  const getLayoutGraphModel = () => {
    // 根据pipelineData的pipelineConfig及pipelineUi，生成antX6需要的数据模型
    const graphModel = getGraphModel();
    // 对数据模型进行布局
    const layoutModel = dagreLayout.layout(graphModel);
    const { nodes } = layoutModel;

    // 从pipelineUi中分离出算子的坐标信息，还原算子的position
    nodes.forEach(item => {
      const uiData = (pipelineUi ?? {})[item.id];
      if (uiData) {
        const { x, y } = uiData;
        item.x = x;
        item.y = y;
      }
    });
    return layoutModel;
  };

  return {
    getGraphModel,
    getLayoutGraphModel,
    getNodeMetadata,
    jobs2Nodes,
    edges2Edges,
  };
};
