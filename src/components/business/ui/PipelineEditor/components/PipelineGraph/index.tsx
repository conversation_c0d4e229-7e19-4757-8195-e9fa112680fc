import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { Graph, Node } from '@antv/x6';
import Split from '@uiw/react-split';
import { isEqual } from 'lodash-es';

import { JobGroupEntity } from '@/modules/JobGroup';
import { CheckTaskPipelineItem } from '@/services';

import './index.less';

import { PipelineDebugPanel } from '../PipelineDebugPanel';
import { DebugToolbar } from '../PipelineDebugPanel/DebugToolbar';

import { GraphToolbar } from './components/GraphToolbar';
import { ZoomToolbar } from './components/ZoomToolbar';
import { getJsonFromGraph, useGraphHook } from './hooks/useGraphHook';
import { usePipelineConfigHook } from './hooks/usePipelineConfigHook';
import { usePipelineDebugHook } from './hooks/usePipelineDebugHook';

export interface RefProps {
  graph?: Graph;
  selectedNode?: Node;
  startDragJob: (event: UIEvent, job: JobModel) => void;
  debugging: boolean;
}

interface Props {
  pipelineData: Pick<ProcessModel, 'pipelineUi' | 'pipelineConfig' | 'pipelineType' | 'forceUpdate'>;
  pipelineCheckTask?: CheckTaskPipelineItem[];
  onChangePipelineData: (pipelineData: ProcessModel) => void;
  onChangeSelectedNode: (node: Node | undefined) => void;
  startDragJob?: ({ event, job }: { event: UIEvent; job: JobModel }) => void;
  onEnterDebug?: () => void;
  onOuterDebug?: () => void;
  onStartDebug?: () => void;
  onStopDebug?: () => void;
}
const _PipelineGraph = (props: Props, ref: ForwardedRef<RefProps>) => {
  const { pipelineData, pipelineCheckTask, onChangeSelectedNode, onChangePipelineData } = props;
  const containerRef = useRef();
  const { graph, dnd, selectedNode, selectedItems, graphInit, graphRender } = useGraphHook();
  const { getLayoutGraphModel, getNodeMetadata, jobs2Nodes, edges2Edges } = usePipelineConfigHook(
    pipelineData,
    pipelineCheckTask,
  );

  const pipelineDebugHook = usePipelineDebugHook(graph!, pipelineData as ProcessModel);
  const { mockId,
    channelId,
    debugMode,
    debugging,
    enterDebug,
    sendMessage,
    startDebug,
  } = pipelineDebugHook;


  const handleStartDebug = () => {
    props.onStartDebug?.();
    startDebug();
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        debugging,
        graph,
        startDragJob: (event: UIEvent, job: PipelineJob | JobGroupEntity) => {
          const { jobName, jobConfig } = job as PipelineJob;
          const jobOpts = {};
          if (debugMode) {
            jobOpts.enableDebug = true;
          }

          if (jobName) {
            // 创建新增算子节点 （分享算子拖入画布含有jobConfig）
            const node = graph!.createNode(getNodeMetadata({ jobName, jobConfig, jobOpts }) as Node.Metadata);
            dnd!.start(node, event);
          } else {
            const {
              jobConfig: { edges, jobs },
              groupName,
            } = job as JobGroupEntity;
            const parent = graph!.createNode({
              width: 240,
              height: 160,
              zIndex: 1,
              label: `${groupName}`,
              attrs: {
                label: {
                  refY: 20,
                  fontSize: 12,
                },
                body: {
                  fill: '#fffbe6',
                  stroke: '#ffe7ba',
                },
              },
              data: {
                nodes: jobs2Nodes(jobs.map(job => ({ ...job, jobOpts }))),
                edges: edges2Edges(edges),
                type: 'addJobGroup',
              },
            });
            dnd!.start(parent, event);
          }
        },
      };
    },
    [graph, dnd, debugging, debugMode],
  );

  useEffect(() => {
    graphInit(containerRef.current!);
  }, []);

  useEffect(() => {
    if (!graph) return;
    const graphModel = getLayoutGraphModel();
    graphRender(graphModel);

    graph.on('debug:enter', () => {
      props.onEnterDebug?.();
    });

    graph.on('debug:outer', () => {
      props.onOuterDebug?.();
    });

    graph.on('debug:stop', () => {
      props.onStopDebug?.();
    });
  }, [graph]);

  const getDataEffectGraph = (pipelineData: Pick<ProcessModel, 'pipelineConfig'>) => {
    const {
      pipelineConfig: { edges, jobs },
    } = pipelineData;
    return {
      jobs: jobs.map(({ jobId, jobName, jobDisplay }) => ({ jobId, jobName, jobDisplay })),
      edges: edges.map(({ from, fromSlot, to, toSlot }) => ({
        from,
        fromSlot,
        to,
        toSlot,
      })),
    };
  };

  useEffect(() => {
    onChangeSelectedNode(selectedNode);
  }, [selectedNode]);

  useEffect(() => {
    if (!graph) return;
    // 编辑JSON，直接更改pipelineData后，采用强制更新
    if (pipelineData.forceUpdate) {
      const graphModel = getLayoutGraphModel();
      graphRender(graphModel);
      delete pipelineData.forceUpdate;
      onChangePipelineData(pipelineData as ProcessModel);
      return;
    }

    // 只有更新了jobs及edges后，重绘画布
    const newModel = getJsonFromGraph(graph);
    if (isEqual(getDataEffectGraph(pipelineData), getDataEffectGraph(newModel))) {
      return;
    }
    const graphModel = getLayoutGraphModel();
    graphRender(graphModel);
  }, [pipelineData.pipelineConfig]);

  // 监听Ctrl + H按键, 切换是否显示V1版本算子
  const handleKeydown = e => {
    const keyCode = e.keyCode || e.which || e.charCode;
    // 监听Delete 组合按键
    if (keyCode === 46) {
      e.preventDefault();
    }
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeydown);
    return () => {
      graph?.dispose();
      window.removeEventListener('keydown', handleKeydown);
    };
  }, []);

  return (
    <>
      <Split mode='vertical' lineBar className='h-full w-full flex flex-col'>
        <div className='pipeline-graph w-full flex-1 flex relative'>
          {!debugging && (
            <GraphToolbar
              graph={graph}
              pipelineCheckTask={pipelineCheckTask}
              selectedNode={selectedNode}
              selectedItems={selectedItems ?? []}
              enterDebug={enterDebug}
              jobType={pipelineData.pipelineType === 'streaming' ? 'streaming' : 'batch'}
              debugMode={debugMode}
              onChangeSelectedNode={onChangeSelectedNode}
            />
          )}

          {debugMode && <DebugToolbar
            {...pipelineDebugHook}
            startDebug={handleStartDebug}
          />}

          <ZoomToolbar graph={graph} />
          <div ref={containerRef} className='flex-1 h-full'></div>
        </div>
        {debugMode && (
          <PipelineDebugPanel
            graph={graph!}
            mockId={mockId}
            channelId={channelId}
            sendMessage={sendMessage}
            selectedNode={selectedNode}
          ></PipelineDebugPanel>
        )}
      </Split>
    </>
  );
};

export const PipelineGraph = React.forwardRef<RefProps, Props>(_PipelineGraph);
