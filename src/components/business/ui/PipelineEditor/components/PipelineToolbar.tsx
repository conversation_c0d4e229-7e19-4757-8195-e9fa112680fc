import { useMemo, useState } from 'react';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { Graph } from '@antv/x6';
import { Button, Divider, notification } from 'antd';
import { cloneDeep, pick } from 'lodash-es';
import { useParams } from 'umi';

import { CodeEditorModal } from '@/components/business/ui/CodeEditor/CodeEditorModal';
import { deleteEmptyValue } from '@/components/business/ui/DynamicForm';
import { AdvanceConfigs, RunningTimeConfigModal } from '@/components/business/ui/RunningTimeConfig';
import { useRightsHook } from '@/hooks';

import { ExtPackageDrawer } from '../../GeneralExtensionPackageTree';
import { TagSelectInToolbar } from '../../TagSelectInToolbar';

import { graphToPipelineData } from './PipelineGraph/hooks/useGraphHook';

interface Props {
  fullScreen?: boolean;
  pipelineData: ProcessModel;
  getGraph: () => Promise<Graph | undefined>;
  onChangePipelineData?: (pipelineData: ProcessModel) => void;
  onSave: (pipelineData: ProcessModel) => Promise<any>;
  onPublish: (pipelineData: ProcessModel) => Promise<any>;
  onChangeFullScreen?: (fullScreen: boolean) => void;
  setDirty: (dirty: boolean) => void;
}

// 修剪所有的算子参数配置及作业高级配置
export const trimPipelineData = (pipelineData: ProcessModel) => {
  const {
    pipelineConfig: { jobs, edges, opts, ...rest },
  } = pipelineData;
  const newJobs =
    jobs?.map(job => {
      return {
        ...job,
        jobConfig: deleteEmptyValue(job.jobConfig),
        jobOpts: deleteEmptyValue(job.jobOpts),
      };
    }) ?? [];
  const newOpts = deleteEmptyValue(opts);
  return {
    ...pipelineData,
    pipelineConfig: {
      jobs: newJobs,
      edges,
      opts: newOpts,
      ...rest,
    },
  };
};

export const PipelineToolbar = (props: Props) => {
  const { onChangePipelineData, onSave, onPublish, pipelineData, getGraph, setDirty } = props;
  const { id } = useParams();

  const { hasRights } = useRightsHook();
  const permissionCode = 'data_develop:write';

  const [jsonModalVisible, setJsonModalVisible] = useState(false);
  const [advanceConfigVisible, setAdvanceConfigVisible] = useState<boolean>(false);
  const [extVisible, setExtVisible] = useState(false);
  const [saving, setSaving] = useState(false);
  const [publishing, setPublishing] = useState(false);

  const type = useMemo(() => {
    return ['flink_sql', 'streaming', 'flink_cmd'].includes(pipelineData.pipelineType) ? 'flink' : 'spark';
  }, [pipelineData.pipelineType]);

  const pipelineStr = useMemo(() => {
    if (!pipelineData) return '';
    const cloneData = trimPipelineData(cloneDeep(pipelineData));
    return JSON.stringify(pick(cloneData, ['pipelineConfig', 'description']), null, 2);
  }, [pipelineData]);

  const onViewJSON = async () => {
    const graph = await getGraph();
    if (!graph) return;
    const newPipelineData = await graphToPipelineData({
      clearDebugTag: true,
      graph,
      pipelineData,
    });
    if (!newPipelineData) return;
    // todo 当查看json时，清除选中的算子(关闭算子参数配置面板)
    graph.trigger('reset:selectedNode');
    onChangePipelineData?.(newPipelineData);
    setJsonModalVisible(true);
  };

  const onChange = (code: string) => {
    try {
      const { pipelineConfig, description } = JSON.parse(code);
      onChangePipelineData?.({
        ...pipelineData,
        pipelineConfig,
        description,
        forceUpdate: true,
      });
    } catch (e) {
      console.log(e);
    }
  };

  const handleSave = async () => {
    const graph = await getGraph();
    if (!graph) return;

    const newPipelineData = await graphToPipelineData({
      clearDebugTag: true,
      graph,
      pipelineData,
    });
    if (!newPipelineData) return;

    const { clusterId, optsId, name } = newPipelineData;
    if (!name) {
      notification.warning({
        message: '必填项未填写',
        description: '请填写任务名称',
      });
      return;
    }

    if (!clusterId || !optsId) {
      notification.warning({
        message: '必填项未填写',
        description: '请在高级配置中设置集群和框架',
      });
      return;
    }

    setSaving(true);
    onChangePipelineData?.(newPipelineData);
    // 保存
    onSave(trimPipelineData(newPipelineData))
      .then(() => {
        setDirty(false);
      })
      .finally(() => {
        setSaving(false);
      });
  };

  const handlePublish = async () => {
    const graph = await getGraph();
    if (!graph) return;
    const newPipelineData = await graphToPipelineData({
      clearDebugTag: true,
      graph,
      pipelineData,
    });
    if (!newPipelineData) return;

    const { clusterId, optsId, name } = newPipelineData;
    if (!name) {
      notification.warning({
        message: '必填项未填写',
        description: '请填写任务名称',
      });
      return;
    }
    if (!clusterId || !optsId) {
      notification.warning({
        message: '必填项未填写',
        description: '请在高级配置中设置集群和框架',
      });
      return;
    }

    setPublishing(true);
    onChangePipelineData?.(newPipelineData);
    // 发布
    onPublish(trimPipelineData(newPipelineData))
      .then(() => {
        setDirty(false);
      })
      .finally(() => {
        setPublishing(false);
      });
  };

  const toggleShowAdvanceConfig = () => {
    setAdvanceConfigVisible((visible: boolean) => {
      return !visible;
    });
  };

  const onChangeAdvanceConfig = (changedValue: AdvanceConfigs) => {
    const { opts } = changedValue;
    const newPipelineData = {
      ...props.pipelineData,
    };
    Object.keys(changedValue).forEach(key => {
      switch (key) {
      case 'clusterId':
      case 'optsId':
      case 'yarnSessionId':
        newPipelineData[key] = changedValue[key]!;
        break;
      case 'opts':
        newPipelineData.pipelineConfig.opts = opts;
        break;
      }
    });
    // 更新pipelineData
    onChangePipelineData?.(newPipelineData);
  };

  const toggleFullScreen = () => {
    if (props.fullScreen) {
      props.onChangeFullScreen?.(false);
    } else {
      props.onChangeFullScreen?.(true);
    }
  };

  const onChangeExtJars = (extJars: string[]) => {
    const newPipelineData = {
      ...props.pipelineData,
    };
    if (!newPipelineData.pipelineConfig.opts) {
      newPipelineData.pipelineConfig.opts = {
        extJars: [],
      };
    }
    newPipelineData.pipelineConfig.opts.extJars = extJars;

    // 更新pipelineData
    onChangePipelineData?.(newPipelineData);
  };

  return (
    <>
      <TagSelectInToolbar
        tagType='TASK'
        value={pipelineData.tags ?? []}
        onChange={tags => {
          onChangePipelineData?.({
            ...pipelineData,
            tags,
          });
        }}
      />
      <Button type='text' className='px-2' onClick={onViewJSON}>
        <i className='iconfont icon-codemode-line text-sm mr-2'></i>JSON
      </Button>
      <Divider type='vertical' className='mx-1' />
      <Button type='text' className='px-2' onClick={toggleShowAdvanceConfig}>
        <i className='iconfont icon-settings-line text-sm mr-2'></i>高级设置
      </Button>
      <Divider type='vertical' className='mx-1' />
      <Button type='text' className='px-2' onClick={() => setExtVisible(true)}>
        <i className='iconfont icon-puzzle-line text-sm mr-2'></i>扩展包
      </Button>
      <Divider type='vertical' className='mx-1' />
      <Button
        type='text'
        className='px-2'
        loading={saving}
        disabled={!hasRights(permissionCode, id ? pipelineData?.projectAuth : undefined)}
        onClick={handleSave}
      >
        <i className='iconfont icon-save-line text-sm mr-2'></i>保存
      </Button>
      <Divider type='vertical' className='mx-1' />
      <Button
        className='mr-1'
        type='primary'
        loading={publishing}
        disabled={!hasRights('data_develop:deploy', id ? pipelineData?.projectAuth : undefined)}
        onClick={handlePublish}
      >
        <i className='iconfont icon-near_me-fill text-xs mr-2'></i>发布
      </Button>
      <Button type='text' className='px-2 mr-1' onClick={() => toggleFullScreen()}>
        {props.fullScreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
      </Button>

      {jsonModalVisible && (
        <CodeEditorModal
          title={`${pipelineData?.name}详细信息`}
          open={jsonModalVisible}
          lang='JSON'
          value={pipelineStr}
          onCancel={() => {
            setJsonModalVisible(false);
          }}
          onChange={onChange}
        />
      )}

      {advanceConfigVisible && (
        <RunningTimeConfigModal
          pipelineData={pipelineData}
          type={type}
          visible={advanceConfigVisible}
          onClose={setAdvanceConfigVisible}
          onChange={onChangeAdvanceConfig}
        />
      )}
      {extVisible && (
        <ExtPackageDrawer
          extJars={pipelineData.pipelineConfig.opts?.extJars ?? []}
          open={extVisible}
          onClose={() => setExtVisible(false)}
          onChangeExtJars={onChangeExtJars}
        />
      )}
    </>
  );
};
