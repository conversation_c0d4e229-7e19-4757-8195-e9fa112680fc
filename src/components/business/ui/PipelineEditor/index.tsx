import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { Node } from '@antv/x6';
import Split from '@uiw/react-split';
import { Divider, Modal, Tooltip, Typography } from 'antd';
import jsDiff from 'jsdiff';
import { pick } from 'lodash';

import { useKeepAliveTabs } from '@/components';
import { CheckTaskPipelineItem } from '@/services';

import { VersionManagement } from '../VersionManagement';

import { JobConfig } from './components/JobConfig';
import { JobList } from './components/JobList';
import { useJobStore } from './components/JobList/useJobStore';
import { PipelineGraph, RefProps } from './components/PipelineGraph';
import { graphToPipelineData } from './components/PipelineGraph/hooks/useGraphHook';
import { PipelineToolbar, trimPipelineData } from './components/PipelineToolbar';

interface Props {
  pipelineData: ProcessModel;
  pipelineCheckTask?: CheckTaskPipelineItem[];
  onChange?: (pipelineData: ProcessModel) => void;
  onSave: (pipelineData: ProcessModel) => Promise<void>;
  onPublish: (pipelineData: ProcessModel) => Promise<void>;
}

export const PipelineEditor: React.FC<Props> = ({ pipelineData, pipelineCheckTask, onChange, onSave, onPublish }) => {
  const { beforeClose } = useKeepAliveTabs();
  const pipelineGraphRef = useRef<RefProps>();
  const [collapse, setCollapse] = useState(false);
  const [selectedNode, setSelectedNode] = useState<Node | undefined>();
  const [debugging, setDebugging] = useState(false);
  const [fullScreen, setFullScreen] = useState(false);
  const [dirty, setDirty] = useState(false);

  const jobType = useMemo(() => {
    if (pipelineData.pipelineType === 'streaming') {
      return 'streaming';
    }
    return 'batch';
  }, [pipelineData.pipelineType]);

  const { streamingJobList, batchJobList } = useJobStore();
  const jobList = useMemo(() => {
    if (jobType == 'streaming') {
      return streamingJobList;
    } else {
      return batchJobList;
    }
  }, [jobType, streamingJobList, batchJobList]);

  const handlerDragStart = useCallback((event, job: JobModel) => {
    return pipelineGraphRef?.current?.startDragJob?.(event, job);
  }, []);

  const onChangePipelineData = (pipelineData: ProcessModel) => {
    onChange?.(pipelineData);
    setDirty(true);
  };

  const onChangeName = (name: string) => {
    if (name === pipelineData.name) {
      return;
    }
    onChange?.({
      ...pipelineData,
      name,
    });
    setDirty(true);
  };

  const onChangeSelectedNode = useCallback((node: Node) => {
    setSelectedNode(node);
  }, []);

  const jobConfigComponent = useMemo(
    () => <JobConfig
      selectedNode={selectedNode}
      graph={pipelineGraphRef.current?.graph}
      editable={!debugging}
      isStreaming={ jobType === 'streaming' }
    />,
    [selectedNode?.id, debugging, jobType],
  );

  const onEnterDebug = useCallback(() => {
    setFullScreen(true);
    setCollapse(true);
  }, []);

  const onOuterDebug = useCallback(() => {
    setFullScreen(false);
    setCollapse(false);
  }, []);

  const onStartDebug = useCallback(() => {
    setSelectedNode(undefined);
    pipelineGraphRef.current?.graph?.cleanSelection();
    setDebugging(true);
  }, []);

  const onStopDebug = useCallback(() => {
    setDebugging(false);
  }, []);

  useEffect(() => {
    beforeClose(async () => {
      const graph = pipelineGraphRef.current?.graph;
      if (!graph) return;
      const newPipelineData = await graphToPipelineData({
        clearDebugTag: true,
        graph,
        pipelineData,
      });
      if (dirty || jsDiff(pipelineData, trimPipelineData(newPipelineData))) {
        return new Promise((resolve, reject) => {
          Modal.confirm({
            title: '当前内容未保存，确定离开此界面？',
            content: <div className='text-gray h-10'>离开将丢失已编辑的内容。</div>,
            okText: '离开',
            onOk: () => {
              return resolve();
            },
            onCancel: () => {
              // eslint-disable-next-line prefer-promise-reject-errors
              reject();
            },
          });
        });
      } else {
        return Promise.resolve(true);
      }
    });
  }, [pipelineData, dirty]);

  return (
    <div className={`flex flex-col align-center w-full h-full bg-white ${fullScreen ? 'full-screen' : ''}`}>
      <div className='flex items-center w-full py-2 pl-4'>
        <span className='text-danger'>*</span>
        <Typography.Text
          className='flex-1 font-medium text-base mb-0 mt-[2px]'
          editable={{ onChange: onChangeName }}
          ellipsis={true}
        >
          {pipelineData.name}
        </Typography.Text>
        <div>
          <PipelineToolbar
            fullScreen={fullScreen}
            pipelineData={pipelineData}
            getGraph={async () => {
              setSelectedNode(undefined);
              pipelineGraphRef.current?.graph?.cleanSelection();
              const flag = await new Promise(resolve => {
                flushSync(() => {
                  resolve(true);
                });
              });

              if (flag) {
                return pipelineGraphRef.current?.graph;
              }
            }}
            onChangePipelineData={onChangePipelineData}
            onSave={onSave}
            onPublish={onPublish}
            onChangeFullScreen={setFullScreen}
            setDirty={setDirty}
          ></PipelineToolbar>
          <VersionManagement
            filter={{ processId: pipelineData.id }}
            currentVersion={pipelineData.version}
            projectAuth={pipelineData.projectAuth}
            onRollback={data => {
              onChange?.({
                ...pipelineData,
                ...pick(data, ['pipelineConfig', 'optsId', 'pipelineUi']),
                forceUpdate: true,
              });
            }}
          />
        </div>
      </div>
      <Divider className='m-0' />
      <div className='flex-1 overflow-hidden'>
        {collapse && (
          <header className='absolute left-[1px] box-shadow'>
            <div className={'flex h-[39px] justify-end'}>
              <MenuUnfoldOutlined
                onClick={() => setCollapse(false)}
                className='text-[16px] text-gray-5 z-[60] bg-white'
              />
            </div>
            <Divider className='mt-0 mb-0' />
          </header>
        )}
        <Split className='w-full h-full flex overflow-hidden' lineBar visiable={collapse ? [2, 3] : [1, 2, 3]}>
          <div
            style={{
              width: collapse ? '0%' : '250px',
            }}
            className='overflow-hidden flex flex-col'
          >
            <header>
              <div className={'flex h-[39px] justify-between items-center pl-3'}>
                <span className='text-gray-6 text-sm'>
                  算子列表
                  <Tooltip title='ctrl + H 组合按键可切换V1算子'>
                    <i className='iconfont icon-help-line cursor-pointer ml-2'></i>
                  </Tooltip>
                </span>
                <MenuFoldOutlined onClick={() => setCollapse(true)} className='mr-[21px] text-[16px] text-gray-5' />
              </div>
              <Divider className='mt-0 mb-0' />
            </header>
            {React.useMemo(
              () => (
                <JobList type={jobType} handlerDragStart={handlerDragStart} draggable={!debugging}></JobList>
              ),
              [jobList, debugging],
            )}
          </div>
          <div className='flex-1 overflow-hidden'>
            {jobList.length > 0 && (
              <PipelineGraph
                pipelineData={pipelineData}
                pipelineCheckTask={pipelineCheckTask}
                onChangePipelineData={onChangePipelineData}
                onChangeSelectedNode={onChangeSelectedNode}
                ref={ref => {
                  pipelineGraphRef.current = ref;
                }}
                onEnterDebug={onEnterDebug}
                onOuterDebug={onOuterDebug}
                onStartDebug={onStartDebug}
                onStopDebug={onStopDebug}
              ></PipelineGraph>
            )}
          </div>
          {selectedNode && (
            <div style={{ width: selectedNode ? 428 : '0%' }} className='bg-white z-50'>
              {jobConfigComponent}
            </div>
          )}
        </Split>
      </div>
    </div>
  );
};
