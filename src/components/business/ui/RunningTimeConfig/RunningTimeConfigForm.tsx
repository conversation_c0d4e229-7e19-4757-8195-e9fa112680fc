import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Form } from 'antd';
import { ErrorListProps, FormProps } from 'antd/es/form';
import { isEqual } from 'lodash';

import { FlinkYarnSessionSelect } from '@/components/business/form/select/FlinkYarnSessionSelect';
import { ClusterSelect } from '@/modules/Cluster/components/ClusterSelect';
import { ClusterApi } from '@/modules/Cluster/services/ClusterApi';
import { ClusterOptsMasterSelect } from '@/modules/ClusterOpts/components/ClusterOptsMasterSelect';
import { ClusterOptsApi } from '@/modules/ClusterOpts/services/ClusterOptsApi';

import { DynamicForm } from '../DynamicForm';

import { AvailableAddOptionsSelect } from './components/AvailableAddOptionsSelect';
import { AdvanceConfigs } from './types';

interface Props extends FormProps {
  clusterDisabled?: boolean;
  type: 'flink' | 'spark' | 'marayarn';
  value?: AdvanceConfigs;
  labelHelpMode: boolean;
  onChange?: (values: AdvanceConfigs) => void;
}

export interface RuntimeConfigRefProps {
  validateFields: () => { values: any; errorFields: ErrorListProps[] };
}
const _RunningTimeConfigForm = (props: Props, ref: ForwardedRef<RuntimeConfigRefProps>) => {
  const { onChange, value, type, clusterDisabled, labelHelpMode, className, ...otherProps } = props;
  const isFirstMount = useRef(true);

  const [form] = Form.useForm();
  const dynamicFormRef = useRef();

  const formItemLayout = useMemo(() => {
    if (labelHelpMode) {
      return {
        labelCol: { span: 6 },
        wrapperCol: { span: 20 },
        layout: 'horizontal',
      };
    } else {
      return {
        layout: 'vertical',
      };
    }
  }, [labelHelpMode]);

  const [optsMasterList, setOptsMasterList] = useState<ClusterOptsEntity[]>([]);
  const [defaultOptsId, setDefaultOptsId] = useState();

  // 已经被添加的配置项
  const [selectedOptsList, setSelectedOptsList] = useState<Array<PipelineParameterModel & { value?: any }>>([]);

  // 用户自定义的配置(不在框架预设选项中)
  const [customConfig, setCustomConfig] = useState<Record<string, any>>([]);
  const [opts, setOpts] = useState(value?.opts ?? {});

  const clusterId = Form.useWatch('clusterId', form);
  const optsId = Form.useWatch('optsId', form);

  // 当前选中框架的全部配置选项
  const optsList = useMemo(() => {
    return optsMasterList?.find(x => x.id === (optsId ?? defaultOptsId))?.parameters;
  }, [optsMasterList, defaultOptsId, optsId]);

  const availableAddOptions = useMemo(() => {
    return optsList?.filter(x => !selectedOptsList?.find(item => item?.name === x.name)) ?? [];
  }, [optsList, selectedOptsList]);

  // 切换集群，同时设置集群名称、更新框架名称
  const onChangeCluster = (clusterId: string, cluster: ClusterEntity) => {
    if (cluster) {
      const obj = {
        clusterId,
        optsId: undefined,
      };
      if (optsMasterList.find(x => x.id === cluster.defaultOptsId)) {
        obj.optsId = cluster.defaultOptsId;
      }

      form.setFieldsValue(obj);
      onChange?.({
        ...form.getFieldsValue(),
        optsId: obj.optsId,
      });
    } else {
      form.setFieldsValue({
        clusterId: undefined,
        optsId: undefined,
      });
    }
  };

  // 当更新框架时，返回当前框架对应的配置项列表
  const onChangeOptsMaster = (newOptsId: string, optsMaster: ClusterOptsEntity) => {
    if (optsMaster) {
      setOpts({});
      setSelectedOptsList([]);
      onChange?.({
        ...form.getFieldsValue(),
        opts: {
          ...customConfig,
        },
      });
    }
  };

  const onChangeYarnSession = (yarnSessionId: string) => {
    form.setFieldValue('yarnSessionId', yarnSessionId);
  };

  // 当选中配置项时，添加配置项到自选列表
  const addOptions = (record?: PipelineParameterModel) => {
    if (record) {
      setSelectedOptsList(selectedOptsList.concat([record]));
      let defaultVal;
      try {
        defaultVal = JSON.parse(record.defaultValue);
      } catch (error) {
        defaultVal = record.defaultValue;
      }
      const newOpts = {
        ...opts,
        [record.name]: defaultVal,
      };
      setOpts(newOpts);
      onChange?.({
        ...form.getFieldsValue(),
        opts: {
          ...newOpts,
          ...customConfig,
        },
      });
    }
  };

  // 从自选列表中删除配置项
  const deleteOpts = (record: PipelineParameterModel) => {
    setSelectedOptsList([...selectedOptsList.filter(x => x.name !== record.name)]);
    const newOpts = {
      ...opts,
    };
    delete newOpts[record.name];
    setOpts({
      ...newOpts,
    });
    onChange?.({
      ...form.getFieldsValue(),
      opts: {
        ...newOpts,
        ...customConfig,
      },
    });
  };

  const onChangeOpts = (newOpts: Record<string, any>) => {
    setOpts({
      ...newOpts,
    });
    const data = {
      ...form.getFieldsValue(),
      opts: {
        ...newOpts,
        ...customConfig,
      },
    };
    if (!isEqual(data, value)) {
      onChange?.(data);
    }
  };

  const onValuesChange = () => {
    onChange?.({
      ...form.getFieldsValue(),
      opts: {
        ...opts,
        ...customConfig,
        ...dynamicFormRef.current?.form?.getFieldsValue?.(),
      },
    });
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        validateFields: async () => {
          return Promise.allSettled([form.validateFields(), dynamicFormRef.current?.validateFields?.()]).then(
            result => {
              const values = {
                ...form.getFieldsValue(),
                opts: {
                  ...opts,
                  ...customConfig,
                  ...dynamicFormRef.current?.getFieldsValue?.(),
                },
              };

              if (result.every(item => item.status === 'fulfilled')) {
                return Promise.resolve({
                  errorFields: [],
                  values,
                });
              } else {
                let errorFields: ErrorListProps[] = [];
                result
                  .filter(item => item.status === 'rejected')
                  .forEach(item => {
                    errorFields = errorFields.concat(item.reason.errorFields);
                  });

                // eslint-disable-next-line prefer-promise-reject-errors
                return Promise.reject({
                  errorFields,
                  values,
                });
              }
            },
          );
        },
      };
    },
    [opts, dynamicFormRef],
  );

  const initOpts = (optsId: string, propsOpt?) => {
    const customConfig: Record<string, any> = {};
    ClusterOptsApi.getOptsOptionsById(optsId).then(({ data }) => {
      /**
       * 根据pipelineData及框架配置项列表，分离出用户自定义的参数到customConfig
       * 及 在框架选项列表中的配置项，初始化自选列表
       */
      const keys = Object.keys(propsOpt || opts || {});
      keys.forEach(key => {
        const findItem = data.parameters?.find(item => item.name === key);
        if (!findItem) {
          customConfig[key] = opts[key];
        }
      });

      setSelectedOptsList(data.parameters?.filter(item => keys.includes(item.name)) ?? []);
      setCustomConfig(customConfig);
    });
  };

  // 根据用户设置的框架id，获取框架配置选项
  useEffect(() => {
    if (!isFirstMount.current) return;
    if (value) {
      form.setFieldsValue(value);
      const { opts, optsId } = value;
      if (optsId) {
        initOpts(optsId, opts);
      }
      setOpts(opts);
    }
    isFirstMount.current = false;
  }, [value]);

  const fetchDefaultCluster = async (masterList: ClusterOptsEntity[]) => {
    try {
      const { data } = await ClusterApi.getDefaultCluster(type);
      const { id: clusterId, defaultOptsId } = data;

      const obj: {
        clusterId: string;
        optsId?: string;
      } = {
        clusterId,
      };

      if (masterList.find(x => x.id === defaultOptsId)) {
        obj.optsId = defaultOptsId;
      }

      form.setFieldsValue(obj);

      if (defaultOptsId) {
        setDefaultOptsId(defaultOptsId);
      }

      onValuesChange();
    } catch (e) {}
  };

  const fetchOptsMasterList = async () => {
    return ClusterOptsApi.getOptsListHasOptions(type, 'pipeline').then(({ data }) => {
      setOptsMasterList(data);
      return data;
    });
  };

  // 集群不存在，获取默认集群，及默认集群的默认框架
  useEffect(() => {
    (async () => {
      const { clusterId } = value ?? {};
      // todo 当获取到默认集群的默认框架后，判断框架是否有效
      const masterList = await fetchOptsMasterList();
      if (!clusterId) {
        fetchDefaultCluster(masterList);
      }
    })();
  }, []);

  return (
    <>
      <div className={`running-time-config-form ${className}`}>
        <Form form={form} onValuesChange={onValuesChange} initialValues={value} {...formItemLayout} {...otherProps}>
          <Form.Item label='集群名称' name='clusterId' required>
            <ClusterSelect
              supportOpts={type}
              onChange={onChangeCluster}
              disabled={clusterDisabled ?? false}
            ></ClusterSelect>
          </Form.Item>
          {/* todo yarnSession支持后，高级配置需支持 */}
          {type === 'flink' && (
            <Form.Item label='YarnSession 名称' name='yarnSessionId'>
              <FlinkYarnSessionSelect clusterId={clusterId} onChange={onChangeYarnSession} />
            </Form.Item>
          )}
          <Form.Item label='框架名称' name='optsId' required>
            <ClusterOptsMasterSelect optsType={type} options={ optsMasterList } onChange={onChangeOptsMaster} />
          </Form.Item>
        </Form>
        <Form {...formItemLayout} {...otherProps}>
          <Form.Item label='添加配置'>
            <AvailableAddOptionsSelect key='availableOptions' options={availableAddOptions} onChange={addOptions} />
          </Form.Item>
        </Form>
      </div>
      {selectedOptsList.length > 0 && (
        <div className={`py-2 pr-8 bg-neutral-50 ${className}`}>
          <DynamicForm
            ref={dynamicFormRef}
            parameters={selectedOptsList}
            initialValues={opts}
            onChange={onChangeOpts}
            deletable={true}
            onDelete={deleteOpts}
            labelHelpMode={labelHelpMode}
            {...formItemLayout}
            {...otherProps}
            wrapperCol={{ span: 24 }}
          />
        </div>
      )}
    </>
  );
};

export const RunningTimeConfigForm = forwardRef(_RunningTimeConfigForm);
