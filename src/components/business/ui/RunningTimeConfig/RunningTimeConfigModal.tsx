import React, { useState } from 'react';
import { Drawer } from 'antd';

import { RunningTimeConfigForm } from './RunningTimeConfigForm';
import { AdvanceConfigs } from './types';

interface Props {
  pipelineData: ProcessModel;
  visible: boolean;
  type: 'flink' | 'spark';
  onClose: (visible: boolean) => void;
  onChange: (changedValue: AdvanceConfigs) => void;
}
export const RunningTimeConfigModal: React.FC<Props> = props => {
  const { visible, onClose, onChange, pipelineData, type } = props;
  const { pipelineConfig, optsId, clusterId, yarnSessionId, status } = pipelineData;
  const [initialValues] = useState({
    clusterId,
    optsId,
    yarnSessionId,
    opts: pipelineConfig.opts,
  });

  const DrawerTitle = () => {
    return (
      <div className='relative pl-4'>
        <i
          className='iconfont icon-close-line text-xs text-gray-5 absolute -left-2 top-[5px] cursor-pointer'
          onClick={() => onClose(false)}
        ></i>
        <div className='mb-2'>
          <span className='text-sm font-medium text-gray-7'>高级配置</span>
        </div>
        <div className='text-xs text-tips'>高级高级参数配置，可覆盖当前使用的框架配置。</div>
      </div>
    );
  };

  return (
    <Drawer
      closable={false}
      title={<DrawerTitle />}
      getContainer={false}
      open={visible}
      width={500}
      onClose={() => onClose(false)}
      styles={{ body: { padding: 0 } }}
    >
      <RunningTimeConfigForm
        value={initialValues}
        type={type}
        className='py-4 p-8'
        onChange={onChange}
        labelHelpMode={false}
        clusterDisabled={status !== 'UNSTART'}
      />
    </Drawer>
  );
};
