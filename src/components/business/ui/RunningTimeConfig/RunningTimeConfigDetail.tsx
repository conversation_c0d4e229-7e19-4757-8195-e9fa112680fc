import { useEffect, useState } from 'react';
import { Descriptions } from 'antd';

import { ClusterApi } from '@/modules/Cluster';
import { ClusterOptsApi } from '@/modules/ClusterOpts';
import { FlinkApi } from '@/modules/FlinkYarnSession';

import { DynamicForm } from '../DynamicForm';

import { AdvanceConfigs } from './types';

interface Props {
  type: 'flink' | 'spark' | 'marayarn';
  detail?: AdvanceConfigs;
  labelWidth?: number;
  className?: string;
}

export const RunningTimeConfigDetail = (props: Props) => {
  const { type, detail, labelWidth = 196, className } = props;
  const [clusterOptions, setClusterOptions] = useState([]);
  const [flinkOptions, setFlinkOptions] = useState([]);
  const [optsMasterList, setOptsMasterList] = useState<ClusterOptsEntity[]>([]);
  const [selectedOptsList, setSelectedOptsList] = useState<Array<PipelineParameterModel & { value?: any }>>([]);

  useEffect(() => {
    if (type !== 'flink' || !detail?.clusterId) {
      return;
    }
    FlinkApi.getAllList(detail.clusterId).then(({ data }) => {
      setFlinkOptions(
        data.map(x => ({
          label: `${x.yarnSessionAlias}（${x.yarnSessionName}）`,
          value: x.id,
        })),
      );
    });
  }, [type, detail?.clusterId]);

  useEffect(() => {
    getClusterList();
    fetchOptsMasterList();
  }, []);

  useEffect(() => {
    if (!detail?.optsId) return;

    initOpts(detail?.optsId, detail?.opts);
  }, [detail]);

  const getClusterList = () => {
    ClusterApi.getAll({ supportOpts: type }).then(({ data }) => {
      setClusterOptions(data);
    });
  };

  const fetchOptsMasterList = async () => {
    ClusterOptsApi.getOptsListHasOptions(type, 'pipeline').then(({ data }) => {
      setOptsMasterList(data);
    });
  };

  const initOpts = (optsId: string, propsOpt?) => {
    ClusterOptsApi.getOptsOptionsById(optsId).then(({ data }) => {
      const keys = Object.keys(propsOpt || {});

      setSelectedOptsList(data.parameters?.filter(item => keys.includes(item.name)) ?? []);
    });
  };

  const items = [
    {
      label: '集群名称',
      children: clusterOptions?.find(item => item.id === detail?.clusterId)?.clusterName,
    },
    ...type === 'flink' ? [{
      label: 'YarnSession 名称',
      children: flinkOptions?.find(item => item.value === detail?.yarnSessionId)?.label,
    }] : [],
    {
      label: '框架名称',
      children: optsMasterList?.find(item => item.id === detail?.optsId)?.optsName,
    },
  ];

  return (
    <div className={className}>
      <Descriptions
        labelStyle={{ width: labelWidth, textAlign: 'right', display: 'inline-block' }}
        column={1}
        items={items}
        className='pt-[20px]'
      />
      {selectedOptsList.length > 0 && (
        <div className={'py-2 pr-8 bg-neutral-50'}>
          <DynamicForm
            parameters={selectedOptsList}
            values={detail?.opts}
            layout='horizontal'
            readonly
            labelStyle={{ width: `${labelWidth}px` }}
          />
        </div>
      )}
    </div>
  );
};
