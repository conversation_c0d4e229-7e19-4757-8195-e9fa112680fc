import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { MinusCircleOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import { ErrorListProps, FormInstance } from 'antd/es/form';
import { cloneDeep, debounce } from 'lodash';

import './index.less';

import { DynamicDetail } from './components/DynamicDetail';
import { DynamicFormItem } from './components/DynamicFormItem';
import FormContext from './FormContext';
import { disposeFormModel } from './utils';

interface Props extends FormProps {
  parameters: PipelineParameterModel[];
  initialValues?: any;
  onChange?: (values: any, errors?: ErrorListProps[]) => void;
  deletable?: boolean;
  onDelete?: (item: PipelineParameterModel) => void;
  labelHelpMode?: boolean;
  job?: JobModel;
  readonly?: boolean;
  performanceOptimization?: boolean;
  channelId?: string;
}

export * from './utils';

// function findAllPaths(obj, parentPath = '') {
//   let paths: string[] = [];
//   for (const key in obj) {
//     if (Object.prototype.hasOwnProperty.call(obj, key)) {
//       const newPath = parentPath.length ? `${parentPath}.${key}` : key;
//       if (typeof obj[key] === 'object' && !isNull(obj[key])) {
//         paths = paths.concat(findAllPaths(obj[key], newPath));
//       } else {
//         paths.push(newPath);
//       }
//     }
//   }
//   return paths;
// }

export const _DynamicForm = (
  props: Props,
  ref: ForwardedRef<{
    form: FormInstance;
    setFieldsValue: (value: any) => void;
    getFieldsValue: () => Promise<any>;
    validateFields: Promise<any> | Promise<{ values; errorFields }>;
  }>,
) => {
  const { parameters,
    initialValues,
    onChange,
    onDelete,
    deletable,
    labelHelpMode,
    job,
    readonly,
    performanceOptimization = false,
    channelId,
    ...otherProps
  } = props;
  const cache = useRef(initialValues);
  const isChanged = useRef(false);

  const isInControl = !!onChange;

  if (readonly) {
    return (<DynamicDetail
      parameters={parameters}
      values={initialValues ?? {}}
      layout='vertical'
      job={job}
      {...otherProps}
    />
    );
  }

  const [form] = Form.useForm();

  function setFieldsValue(values: any) {
    form.setFieldsValue(disposeFormModel(parameters, cloneDeep(values) ?? {}, false));
  }

  async function getFieldsValue() {
    const values = await form.validateFields();
    return disposeFormModel(parameters, values, true);
  }

  async function validateFields() {
    try {
      const values = await form.validateFields();
      return Promise.resolve(disposeFormModel(parameters, cloneDeep(values), true));
    } catch (e) {
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject({
        values: disposeFormModel(parameters, cloneDeep(form.getFieldsValue()), true),
        errorFields: e?.errorFields,
      });
    }
  }
  
  useImperativeHandle(
    ref,
    () => {
      return {
        form, // 原始表单，可做表单赋值，直接操作表单
        setFieldsValue,
        getFieldsValue,
        validateFields,
      };
    },
    [parameters],
  );

  const debounceChangedValues = debounce(values => {
    const newValues = disposeFormModel(parameters, cloneDeep(values ?? {}), true);
    onChange?.(newValues);
  }, 200);

  const onValuesChange = async (_, values) => {
    cache.current = values;
    isChanged.current = true;
    if (isInControl) {
      if (performanceOptimization) {
        debounceChangedValues(values);
      } else {
        onChange?.(disposeFormModel(parameters, cloneDeep(cache.current ?? {}), true));
      }
    }
  };

  // 需要性能优化场景时，只在组件销毁时，向上层抛值。
  useEffect(() => {
    return () => {
      if (isChanged.current) {
        onChange?.(disposeFormModel(parameters, cloneDeep(cache.current ?? {}), true));
      }
    };
  }, [parameters]);

  return (
    <FormContext.Provider value={{ form, labelHelpMode: labelHelpMode ?? false, job, channelId }}>
      <Form
        form={form}
        layout='vertical'
        onValuesChange={onValuesChange}
        initialValues={
          disposeFormModel(parameters, cloneDeep(initialValues) ?? {}, false)
        }
        {...otherProps}
      >
        {parameters?.map(parameter => {
          const { requireCondition, availableCondition } = parameter;
          const names = parameters.map(x => x.name).filter(fieldName => {
            let flag = false;
            if (requireCondition) {
              flag = requireCondition.includes(fieldName);
              if (flag) { 
                return flag;
              }
            }
            if (availableCondition) {
              flag = availableCondition.includes(fieldName);
            }
            return flag;
          });

          return <div className='flex items-start' key={parameter.name}>
            <DynamicFormItem
              className='flex-1'
              key={parameter.name}
              parameter={parameter}
              formName={parameter.name}
              relationNames={ names.join(',') }
            />
            {deletable && (
              <MinusCircleOutlined
                className={`
                  iconfont icon-bin-line ml-2 text-danger cursor-pointer
                  ${labelHelpMode ? 'mt-2' : 'mt-[37px]'
              }`}
                onClick={() => onDelete?.(parameter)}
              />
            )}
          </div>;
        })}
      </Form>
    </FormContext.Provider>
  );
};

export const DynamicForm = forwardRef(_DynamicForm);
