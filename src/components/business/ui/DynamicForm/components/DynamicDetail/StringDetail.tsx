import { useContext, useMemo } from 'react';

import { DataSourceSelect } from '@/components/business/form';
import { DataStandardSelect } from '@/components/business/form/DataStandardSelect';
import { MetricSourceSelect } from '@/components/business/form/select/MetricSourceSelect';
import { RegisterCenterSelect } from '@/components/business/form/select/RegisterCenterSelect';
import { SourceModelSelect } from '@/components/business/form/SourceModelSelect';

import FormContext from '../../FormContext';

interface Props {
  parameter: PipelineParameterModel;
  value: any;
}

const KAFKA = [
  'com.eoi.jax.flink.job.source.KafkaByteSourceByModelJob',
  'com.eoi.jax.flink.job.sink.KafkaSinkByModelJob',
];
const ELASTICSEARCH = ['com.eoi.jax.flink.job.sink.ElasticSearchSinkByModelJob'];
const NEBULA = [
  'com.eoi.jax.flink.job.sink.NebulaTagSinkByModelJob',
  'com.eoi.jax.flink.job.sink.NebulaEdgeSinkByModelJob',
];


export const StringDetail = (props: Props) => {
  const { parameter, value } = props;
  const { job } = useContext(FormContext);

  const deployPlatform = useMemo<DATA_SOURCE_TYPE>(() => {
    const jobName = job?.jobName;
    if (!jobName) return undefined;

    if (KAFKA.includes(jobName)) {
      return 'KAFKA';
    }
    if (ELASTICSEARCH.includes(jobName)) {
      return 'ELASTICSEARCH';
    }
    if (NEBULA.includes(jobName)) {
      return 'NEBULA';
    }
    return undefined;
  }, [job?.jobName]);
  const type = useMemo(() => {
    if (parameter.candidates && parameter.candidates.length > 0) {
      return 'select';
    }
    if (parameter.recommendations && parameter.recommendations.length > 0) {
      return 'autoComplete';
    }
    if (['TEXT', null].includes(parameter.inputType)) {
      return 'input';
    }

    if (parameter.inputType === 'PASSWORD') {
      return 'password';
    }

    if (parameter.inputType === 'MODEL_SELECTION') {
      return 'modelSelect';
    }

    if (parameter.inputType === 'JAX_METRIC_SOURCE') {
      return 'JAX_METRIC_SOURCE';
    }

    if (parameter.inputType?.indexOf('JAX_DATASOURCE') === 0) {
      return 'JAX_DATASOURCE';
    }

    if (parameter.inputType === 'JAX_REGISTER_CENTER_NACOS') {
      return 'JAX_REGISTER_CENTER_NACOS';
    }
    if (parameter.inputType === 'JAX_DIC_ENUM') {
      return 'JAX_DIC_ENUM';
    }


    return 'codeInput';
  }, [parameter]);
  switch (type) {
  case 'select':
  case 'autoComplete':
  case 'input':
  case 'password':
  case 'codeInput':
    return (<div className='break-words break-all'> {value} </div>) ;
  
  case 'modelSelect':
    return (
      <SourceModelSelect
        value={value}
        disabled
        platform={deployPlatform}
        title={`选择${parameter.label}`}
        type={job?.jobRole}
      />
    );
  
    // 指标来源
  case 'JAX_METRIC_SOURCE':
    return (
      <MetricSourceSelect value={value} disabled />
    );
  
    // 数据来源
  case 'JAX_DATASOURCE': {
    const platform = parameter.inputType!.replace('JAX_DATASOURCE_', '');
    return (
      <>
        <DataSourceSelect value={value} disabled platformList={[platform]} />
      </>
    );
  }
  
  case 'JAX_REGISTER_CENTER_NACOS':
    return (
      <RegisterCenterSelect type='NACOS' value={value} disabled />
    );
  
  case 'JAX_DIC_ENUM':
    return (
      <DataStandardSelect value={value} disabled></DataStandardSelect>
    );
  }
};
