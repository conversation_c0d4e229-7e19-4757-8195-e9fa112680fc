import React from 'react';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';

interface Props {
  formName: string;
}
export const MapList: React.FC<Props> = ({ formName, ...rest }: Props) => {
  return (
    <Form.List name={formName.split(',')} {...rest}>
      {(fields, { add, remove }, { errors }) => (
        <>
          {fields.map(({ key, name, ...restField }) => (
            <div key={key} className='flex items-start'>
              <Form.Item
                className='mb-2 flex-1'
                {...restField}
                name={[name, 'key']}
                rules={[{ required: true, message: 'key是必填项' }]}
              >
                <Input placeholder='key' />
              </Form.Item>
              <Form.Item className='mb-2 flex-1 pl-2' {...restField} name={[name, 'value']}>
                <Input placeholder='value' />
              </Form.Item>
              <Button
                type='text'
                className='px-2'
                onClick={() => remove(name)}
              >
                <MinusCircleOutlined
                  className='text-lg'
                />
              </Button>
            </div>
          ))}
          <Form.ErrorList errors={errors} />
          <Form.Item>
            <Button type='dashed' onClick={() => add({ key: '', value: '' })} block icon={<PlusOutlined />}>
              添加
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
};
