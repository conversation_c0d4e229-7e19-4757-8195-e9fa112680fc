import React, { useState } from 'react';
import { LanguageName } from '@uiw/codemirror-extensions-langs';
import { Button, Input } from 'antd';

import { CodeEditorModal } from '@/components/business/ui/CodeEditor/CodeEditorModal';

interface Props {
  lang: InputType | LanguageName;
  value: string;
  onChange?: (value: string) => void;
}
export const CodeInput: React.FC = ({ lang, value, onChange }: Props) => {
  const [open, setOpen] = useState(false);
  const openCodeEditor = () => {
    setOpen(true);
  };
  return (
    <div>
      <Input.TextArea
        value={value}
        defaultValue={value}
        onChange={({ target }) => {
          onChange?.(target.value);
        }}
      ></Input.TextArea>
      <Button onClick={openCodeEditor} className='mt-2'>
        代码编辑器
      </Button>
      {open && (
        <CodeEditorModal open={open} onCancel={() => setOpen(false)} value={value} onChange={onChange} lang={lang} />
      )}
    </div>
  );
};
