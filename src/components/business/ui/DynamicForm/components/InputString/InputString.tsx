import React, { useContext, useMemo } from 'react';
import { AutoComplete, Form, Input, Select } from 'antd';

import { DataSourceSelect } from '@/components/business/form';
import { DataStandardSelect } from '@/components/business/form/DataStandardSelect';
import { MetricSourceSelect } from '@/components/business/form/select/MetricSourceSelect';
import { RegisterCenterSelect } from '@/components/business/form/select/RegisterCenterSelect';
import { SourceModelSelect } from '@/components/business/form/SourceModelSelect';

import FormContext from '../../FormContext';

import { CodeInput } from './CodeInput';
import { GtSystemNameSelect } from './GtSystemNameSelect';

const KAFKA = [
  'com.eoi.jax.flink.job.source.KafkaByteSourceByModelJob',
  'com.eoi.jax.flink.job.sink.KafkaSinkByModelJob',
];
const ELASTICSEARCH = ['com.eoi.jax.flink.job.sink.ElasticSearchSinkByModelJob'];
const NEBULA = [
  'com.eoi.jax.flink.job.sink.NebulaTagSinkByModelJob',
  'com.eoi.jax.flink.job.sink.NebulaEdgeSinkByModelJob',
];

interface Props {
  parameter: PipelineParameterModel;
  formName: string;
  isRequired?: boolean;
  onChange: (value: string) => {};
}

export const InputString: React.FC = ({
  parameter,
  formName,
  isRequired,
  ...restParams
}: Props) => {
  const { labelHelpMode, job } = useContext(FormContext);

  const deployPlatform = useMemo<DATA_SOURCE_TYPE>(() => {
    const jobName = job?.jobName;
    if (!jobName) return undefined;

    if (KAFKA.includes(jobName)) {
      return 'KAFKA';
    }
    if (ELASTICSEARCH.includes(jobName)) {
      return 'ELASTICSEARCH';
    }
    if (NEBULA.includes(jobName)) {
      return 'NEBULA';
    }
    return undefined;
  }, [job?.jobName]);

  // ***判断顺序很重要***
  const type = useMemo(() => {
    if (parameter.candidates && parameter.candidates.length > 0) {
      return 'select';
    }
    if (parameter.recommendations && parameter.recommendations.length > 0) {
      return 'autoComplete';
    }
    if (['TEXT', null].includes(parameter.inputType)) {
      return 'input';
    }

    if (parameter.inputType === 'PASSWORD') {
      return 'password';
    }

    if (parameter.inputType === 'MODEL_SELECTION') {
      return 'modelSelect';
    }

    if (parameter.inputType === 'JAX_METRIC_SOURCE') {
      return 'JAX_METRIC_SOURCE';
    }

    if (parameter.inputType?.indexOf('JAX_DATASOURCE') === 0) {
      return 'JAX_DATASOURCE';
    }

    if (parameter.inputType === 'JAX_REGISTER_CENTER_NACOS') {
      return 'JAX_REGISTER_CENTER_NACOS';
    }
    if (parameter.inputType === 'JAX_DIC_ENUM') {
      return 'JAX_DIC_ENUM';
    }
    if (parameter.inputType === 'JAVASCRIPT_EXPRESSION') {
      return 'JAVASCRIPT_EXPRESSION';
    }
    if (parameter.inputType === 'GTJA_SYSTEM_NAME') {
      return 'GTJA_SYSTEM_NAME';
    }


    return 'codeInput';
  }, [parameter]);

  switch (type) {
  case 'select':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <Select placeholder={parameter.placeholder} allowClear showSearch>
          {parameter.candidates.map((item: string) => (
            <Select.Option value={item} key={item}>
              {item}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
    );

  case 'autoComplete':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <AutoComplete
          options={parameter.recommendations!.map((item: string) => {
            return { value: item };
          })}
          allowClear
        />
      </Form.Item>
    );

  case 'input':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <Input placeholder={parameter.placeholder} allowClear />
      </Form.Item>
    );

  case 'password':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <Input.Password placeholder={parameter.placeholder} allowClear />
      </Form.Item>
    );

  case 'modelSelect':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <SourceModelSelect platform={deployPlatform} title={`选择${parameter.label}`} type={job?.jobRole} />
      </Form.Item>
    );

    // 指标来源
  case 'JAX_METRIC_SOURCE':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <MetricSourceSelect />
      </Form.Item>
    );

    // 数据来源
  case 'JAX_DATASOURCE': {
    const platform = parameter.inputType!.replace('JAX_DATASOURCE_', '');
    return (
      <>
        <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
          <DataSourceSelect platformList={[platform]} />
        </Form.Item>
      </>
    );
  }

  case 'JAX_REGISTER_CENTER_NACOS':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <RegisterCenterSelect type='NACOS' />
      </Form.Item>
    );

  case 'codeInput':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <CodeInput lang={parameter.inputType} />
      </Form.Item>
    );

  case 'JAX_DIC_ENUM':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <DataStandardSelect allowClear={!isRequired}></DataStandardSelect>
      </Form.Item>
    );
  case 'JAVASCRIPT_EXPRESSION':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <Input placeholder={parameter.placeholder} allowClear />
      </Form.Item>
    );
  case 'GTJA_SYSTEM_NAME':
    return (
      <Form.Item name={formName.split(',')} {...restParams} help={labelHelpMode && parameter.description}>
        <GtSystemNameSelect placeholder={parameter.placeholder} allowClear />
      </Form.Item>
    );
  }
};
