import React, { memo, useContext, useEffect, useMemo, useState } from 'react';
import { CaretDownOutlined, CaretRightOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Form, InputNumber, Popover, Radio, Select } from 'antd';

import FormContext from '../FormContext';
import { catwith } from '../utils';

import { InputString } from './InputString/InputString';
import { MapList } from './MapList';
import { Object } from './Object';
import { ObjectList } from './ObjectList';
import { StringList } from './StringList';

const { Option } = Select;

interface Props {
  parameter: PipelineParameterModel;
  formName: string;
  fieldModel: any;
  deepFormName: string;
  className: string;
  showLabel?: boolean;
  relationNames: string;
}

const parameterLabelMap = [
  { label: '字段名称', key: 'label' },
  { label: 'name', key: 'name' },
  { label: '字段类型', key: 'type' },
  { label: '默认值', key: 'defaultValue' },
  { label: '是否必填', key: 'optional' },
  { label: '描述', key: 'description' },
];

const _ParameterDesc = ({ parameter }: { parameter: PipelineParameterModel }) => {
  const { optional } = parameter;
  return (
    <div className='mb-5'>
      {parameterLabelMap.map(({ key, label }) => (
        <div key={key}>
          <label className='text-right text-gray-5 inline-block' style={{ width: '70px' }}>
            {label}：
          </label>
          {key === 'optional' ? !optional ? '是' : '否' : <span className='break-words'>{parameter[key]}</span>}
        </div>
      ))}
    </div>
  );
};

const ParameterDesc = memo(_ParameterDesc);

const _ParameterLabel = ({ parameter, isComplexType, spread, setSpread, isRequired, labelHelpMode }) => {
  return (
    <span className='flex items-center'>
      <span
        onClick={() => setSpread(spread => !spread)}
        className={isComplexType ? 'cursor-pointer flex-1 line-clamp-1' : 'flex-1 line-clamp-1'}
      >
        {!spread && isComplexType && <CaretRightOutlined />}
        {spread && isComplexType && <CaretDownOutlined />}
        {isRequired && <span className='text-danger'>*</span>}
        {parameter.label}
      </span>
      {!labelHelpMode && (
        <Popover
          content={() => <ParameterDesc parameter={parameter} />}
          title='详细信息'
          overlayInnerStyle={{ width: '300px' }}
        >
          <QuestionCircleOutlined className='cursor-pointer ml-2 text-gray-7' />
        </Popover>
      )}
    </span>
  );
};

const ParameterLabel = memo(_ParameterLabel);

const _DynamicFormItem: React.FC = ({
  parameter,
  formName,
  deepFormName,
  className,
  showLabel = true,
  relationNames,
  ...restProps
}: Props) => {
  const [spread, setSpread] = useState(true);
  const { form, labelHelpMode } = useContext(FormContext);
  const cloneFormName = (deepFormName || formName).split(',');
  cloneFormName.pop();
  const relations = relationNames ? relationNames.split(',') : [];
  
  const relationNameList = useMemo(() => {
    return relations.map(x => {
      return [...cloneFormName, x];
    });
  }, [relationNames, cloneFormName]);

  const relationModel = {};

  relations.forEach((x,index) => {
    relationModel[x] = Form.useWatch(relationNameList[index], form);
  });
  
  const parameterStr = useMemo(() => {
    return JSON.stringify(parameter);
  }, [parameter]);

  const isAvailable = useMemo(() => {
    const { availableCondition } = parameter;

    if (!availableCondition) {
      return true;
    } else {
      try {
        if (!relationModel) {
          return false;
        }
        const available = catwith(relationModel, `return ${availableCondition};`);
        // eslint-disable-next-line no-eval
        return available;
      } catch (e) {
        // console.warn(fieldModel, availableCondition, e);
        return false;
      }
    }
  }, [parameterStr, relationModel]);

  const isRequired = useMemo(() => {
    if (!isAvailable) return false;
    const { requireCondition, optional } = parameter;

    if (!requireCondition) {
      return !optional;
    } else {
      try {
        if (!relationModel) {
          return false;
        }
        const required = catwith(relationModel, `return ${requireCondition};`);
        // eslint-disable-next-line no-eval
        return required;
      } catch (e) {
        // console.warn(fieldModel, requireCondition, e);
        return false;
      }
    }
  }, [parameterStr, relationModel, isAvailable]);

  const rules = useMemo(() => {
    const { regex, range, type, errorMessage } = parameter;
    const dataTypes = [null, '', undefined];
    return [
      {
        trigger: ['change', 'blur'],
        validator: (rule, value) => {
          if (!isAvailable) return Promise.resolve();
          if (errorMessage) {
            return Promise.resolve();
          }
          if (isRequired) {
            if (type[0] === 'LIST') {
              if (!value || value.length === 0) {
                return Promise.reject(new Error('请填写必填项'));
              } else {
                return Promise.resolve();
              }
            }

            if (type[0] === 'MAP') {
              if (!value || value.length === 0) {
                return Promise.reject(new Error('请填写必填项'));
              } else if (value.length > 1) {
                const keys = value.map((x: { key: string; value: string }) => x?.key);
                const unNullKeys = keys.filter(key => key);
                if (unNullKeys.length === 0) {
                  return Promise.resolve();
                }
                if (Array.from(new Set(keys)).length < keys.length) {
                  return Promise.reject(new Error('key不能重复'));
                }
              } else {
                return Promise.resolve();
              }
            }
          }

          if (isRequired && dataTypes.includes(value)) {
            return Promise.reject(new Error('请填写必填项'));
          }

          try {
            if (regex && !dataTypes.includes(value) && !new RegExp(regex.slice(1, -1)).test(value)) {
              return Promise.reject(new Error(`必须匹配 ${regex} 规则`));
            }
          } catch (e) {
            return Promise.reject(new Error(`请检查正则 ${regex} 是否正确`));
          }

          if (!(range && !dataTypes.includes(value))) return Promise.resolve();

          const rangeArr = range.split(',');
          if (rangeArr[0].length > 1) {
            const num = parseInt(rangeArr[0].slice(1), 10);
            if (rangeArr[0].startsWith('(')) {
              if (value <= num) return Promise.reject(new Error(`请输入大于${num}的值`));
            } else {
              if (value < num) return Promise.reject(new Error(`请输入大于等于${num}的值`));
            }
          }
          if (rangeArr[1].length > 1) {
            const num = parseInt(rangeArr[1].slice(0, -1));
            if (rangeArr[1].startsWith('(')) {
              if (value >= num) return Promise.reject(new Error(`请输入小于${num}的值`));
            } else {
              if (value > num) return Promise.reject(new Error(`请输入小于等于${num}的值`));
            }
          }
          return Promise.resolve();
        },
      },
    ];
  }, [isRequired, isAvailable]);

  const type = parameter?.type[0] ?? '';

  const isNumber = ['FLOAT', 'DOUBLE', 'INT', 'LONG'].includes(type);
  const isComplexType = ['LIST', 'OBJECT', 'MAP'].includes(type);
  const isSelect = parameter.candidates && parameter.candidates.length > 0;

  useEffect(() => {
    if (!isAvailable || !isRequired) return;
    form.validateFields([(deepFormName ?? formName).split(',')], {
      recursive: false,
    });
  }, []);

  return (
    isAvailable && (
      <Form.Item
        label={showLabel ? <ParameterLabel
          parameter={parameter}
          setSpread={setSpread}
          spread={spread}
          isComplexType={isComplexType}
          isRequired={isRequired}
          labelHelpMode={labelHelpMode}
        /> : undefined}
        className={`mb-0 ${className}`}
        help={isComplexType && labelHelpMode ? parameter.description : undefined}
        {...restProps}
      >
        {
          // 数字类型，分类下拉选项和直接输入
          isNumber &&
            (isSelect ? (
              <Form.Item name={formName.split(',')} help={labelHelpMode && parameter.description}>
                <Select allowClear placeholder={parameter.placeholder} showSearch>
                  {parameter.candidates.map((item: number, index: number) => (
                    <Option value={item} key={index}>
                      {item}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            ) : (
              <Form.Item name={formName.split(',')} rules={rules} help={labelHelpMode && parameter.description}>
                <InputNumber className='w-full' changeOnWheel={false} />
              </Form.Item>
            ))
        }
        {
          // boolean类型
          type === 'BOOL' && (
            <Form.Item name={formName.split(',')} rules={rules} help={labelHelpMode && parameter.description}>
              <Radio.Group>
                <Radio value={true}>{parameter.trueLabel || '开启'}</Radio>
                <Radio value={false}>{parameter.falseLabel || '关闭'}</Radio>
              </Radio.Group>
            </Form.Item>
          )
        }
        {
          // 字符串类型
          type === 'STRING' && <InputString
            parameter={parameter}
            isRequired={isRequired}
            formName={formName}
            rules={rules}
          />
        }
        {
          // map类型
          type === 'MAP' && spread && <MapList formName={formName} rules={rules} />
        }
        {
          // 字符串列表
          type === 'LIST' && spread && parameter.listParameter?.type[0] === 'STRING' && (
            <StringList formName={formName} rules={rules} />
          )
        }
        {
          // 对象列表
          type === 'LIST'
          && parameter.listParameter?.type[0] === 'OBJECT'
          && (<div className={`${spread ? '' : 'hidden'}`}>
            <ObjectList
              parameter={parameter}
              formName={formName}
              rules={rules}
              deepFormName={deepFormName ?? formName}
            />
          </div>
          )
        }
        {
          // 对象
          type === 'OBJECT' && (<div className={`${spread ? '' : 'hidden'}`}>
            <Object
              formName={formName}
              parameter={parameter.objectParameters}
              rules={rules}
              deepFormName={deepFormName ?? formName}
            />
          </div>
          )
        }
      </Form.Item>
    )
  );
};

export const DynamicFormItem = memo(_DynamicFormItem);
