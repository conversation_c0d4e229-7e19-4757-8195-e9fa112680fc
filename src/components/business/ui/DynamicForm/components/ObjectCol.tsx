import React, { useMemo } from 'react';
import { Col, Row } from 'antd';

import { sortParameterByOptional } from '../utils';

import { DynamicFormItem } from './DynamicFormItem';

interface Props {
  parameter: PipelineParameterModel[];
  formName: string;
  deepFormName: string[];
  showLabel: boolean;
}

export const ObjectCol: React.FC = ({ parameter, formName, deepFormName, showLabel }: Props) => {
  const sortParameters = useMemo(() => sortParameterByOptional(parameter), [parameter]);
  return (
    <Row gutter={[8, 8]} className='flex-1 object-col'>
      {sortParameters.map(parameter => {
        const { requireCondition, availableCondition } = parameter;
        const names = sortParameters.map(x => x.name).filter(fieldName => {
          let flag = false;
          if (requireCondition) {
            flag = requireCondition.includes(fieldName);
            if (flag) { 
              return flag;
            }
          }
          if (availableCondition) {
            flag = availableCondition.includes(fieldName);
          }
          return flag;
        });
        return <Col key={parameter.name} span={24 / sortParameters.length}>
          <DynamicFormItem
            formName={[formName, parameter.name].join(',')}
            deepFormName={[deepFormName, parameter.name].join(',')}
            parameter={parameter}
            showLabel={showLabel}
            labelCol={{ span: 24 }}
            relationNames={names.join(',')}
          />
        </Col>;
      })}
    </Row>
  );
};
