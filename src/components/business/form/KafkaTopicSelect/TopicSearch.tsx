import React, { useEffect, useState } from 'react';

import { SearchInput } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { DataService } from '@/services';

interface TopicItem {
  tableName: string;
  comment: string;
}

interface Props {
  dsId: string;
  tbName?: string;
  onChange?: (value: string, record: TopicItem) => void;
}
export const TopicSearch: React.FC<Props> = props => {
  const { dsId, tbName, onChange } = props;
  const [list, setList] = useState([]);
  const [searchKey, setSearchKey] = useState();
  const columns = [
    {
      title: '主题名',
      dataIndex: 'tableName',
    },
    {
      title: '备注',
      dataIndex: 'comment',
    },
  ];
  const [loading, setLoading] = useState(false);
  const {
    pagination,
    setPagination,
    queryParams,
    filter,
    setFilter,
    selectedRowKeys,
    setSelectedRowKeys,
    setSelectedRows,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    filter: {
      dsId,
      name: tbName,
    },
  });

  const onRowClick = (record: TopicItem) => {
    const { tableName } = record;
    setSelectedRowKeys([tableName]);
    setSelectedRows([record]);
    onChange?.(tableName, record);
  };

  const fetchData = () => {
    setLoading(true);
    DataService.apiService
      .getTopics(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    if (tbName) {
      setSelectedRowKeys([tbName]);
    } else {
      setSelectedRowKeys([]);
    }
  }, [tbName]);

  return (
    <div className='flex flex-col h-full'>
      <div className='p-4'>
        <SearchInput
          placeholder='关键字搜索'
          defaultValue={filter.name}
          value={searchKey}
          onChange={e => setSearchKey(e.target.value)}
          onSearch={val => setFilter({ ...filter, name: val })}
        />
      </div>

      <div className='flex-1 flex overflow-hidden'>
        <CustomTable
          dataSource={list}
          columns={columns}
          rowSelection={{
            type: 'radio',
            selectedRowKeys,
            onChange: (_, selectedRows) => {
              onRowClick(selectedRows[0]);
            },
          }}
          onRowSelectionChange={onRowSelectionChange}
          pagination={{
            ...pagination,
            showSizeChanger: false,
          }}
          loading={loading}
          onChange={handleTableChange}
          scroll={true}
          className='h-full w-[850px]'
          rowKey='tableName'
          onRow={record => {
            return {
              onClick: () => {
                onRowClick(record);
              },
            };
          }}
        />
      </div>
    </div>
  );
};
