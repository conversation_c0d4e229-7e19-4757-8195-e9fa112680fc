import React, { useEffect, useState } from 'react';
import { Select, SelectProps } from 'antd';

import { ICON_ENTITY_MAP } from '@/constants';
import { BusinessCategoryApi, DataDomainApi } from '@/services';

interface Props extends SelectProps {
  bizId?: string;
  onChangeCode?: (codeMap: { domCode: string }) => void;
}
export const DataDomainSelect: React.FC<Props> = props => {
  const { bizId, onChangeCode, ...otherProps } = props;
  const [options = [], setOptions] = useState<SelectOptions>();

  const handleOnChange = (val, item) => {
    props.onChange?.(val, item);
    onChangeCode?.({
      domCode: item?.code,
    });
  };

  useEffect(() => {
    if (bizId) {
      BusinessCategoryApi.getDataDomainById(bizId)
        .then(({ data }) => {
          setOptions(
            data.map((x: any) => {
              return {
                label: x.domName,
                value: x.domId,
              };
            }),
          );
        })
        .catch(() => {});
    } else {
      DataDomainApi.getAllDomain()
        .then(({ data }) => {
          setOptions(
            data.map(x => {
              return {
                label: x.name,
                value: x.id,
              };
            }),
          );
        })
        .catch(() => {});
    }
  }, []);

  useEffect(() => {
    if (bizId) {
      BusinessCategoryApi.getDataDomainById(bizId)
        .then(({ data }) => {
          setOptions(
            data.map((x: any) => {
              return {
                label: x.domName,
                value: x.domId,
                code: x.domCode,
              };
            }),
          );
        })
        .catch(() => {});
    }
  }, [bizId]);

  return (
    <>
      <Select placeholder='请选择' allowClear {...otherProps} onChange={handleOnChange}>
        {options?.map(option => (
          <Select.Option key={option.value} value={option.value} code={option.code} label={option.label}>
            <span className='flex' style={{ alignItems: 'center' }}>
              <i className={`iconfont ${ICON_ENTITY_MAP.dataDomain}`}></i>
              <span className='ml-2'>{option.label}</span>
            </span>
          </Select.Option>
        ))}
      </Select>
    </>
  );
};

export default DataDomainSelect;
