import { useEffect, useMemo, useState } from 'react';
import { Select, SelectProps, Tag } from 'antd';
import { groupBy } from 'lodash-es';

import { CATALOGS, TableType } from '@/constants';
import { WarehouseLayerApi } from '@/services';

interface Props extends SelectProps {
  tbType?: keyof typeof TableType | 'DIMENSION';
  catalog?: string;
}

export const WarehouseLayerSelect = (props: Props) => {
  const { tbType, catalog, onChange, ...otherProps } = props;
  const [layerList, setLayerList] = useState<WAREHOUSE_LAYER.WarehouseLayerEntity[]>([]);
  const groupLayerOptions = useMemo(() => {
    if (layerList.length === 0) {
      return [];
    }
    const groupData = groupBy(layerList, 'catalog');
    return CATALOGS.filter(x => x.sort > 1)
      .map(item => {
        const options =
          groupData[item.value]?.map(x => {
            return {
              ...x,
              label: x.name,
              value: x.id,
            };
          }) ?? [];
        if (options.length === 0) return undefined;
        return {
          label: item.label,
          code: item.value,
          options,
        };
      })
      .filter(x => x);
  }, [layerList]);

  const getLayer = (layerId: string) => {
    if (layerList.length === 0 || !layerId) {
      return {};
    }
    return layerList.find(x => x.id == layerId);
  };

  const onChangeValue = (layerId: string) => {
    onChange &&
      onChange(
        layerId,
        getLayer(layerId), // todo
      );
  };

  useEffect(() => {
    // 获取数仓分层列表
    WarehouseLayerApi.getList({
      tbType,
      catalog,
    })
      .then(setLayerList)
      .catch(console.error);
  }, [props]);

  return (
    <Select allowClear placeholder='请选择' onChange={onChangeValue} {...otherProps}>
      {groupLayerOptions?.map(({ label, code, options }) => (
        <Select.OptGroup key={code} label={label}>
          {options?.map(option => (
            <Select.Option key={option.id} value={option.id}>
              <span className='flex' style={{ alignItems: 'baseline' }}>
                <Tag color={code === 'WH' ? 'success' : 'magenta'}>{label}</Tag>
                <span>{option.name}</span>
              </span>
            </Select.Option>
          ))}
        </Select.OptGroup>
      ))}
    </Select>
  );
};

export default WarehouseLayerSelect;
