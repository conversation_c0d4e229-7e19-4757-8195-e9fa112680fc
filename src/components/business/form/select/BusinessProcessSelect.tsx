import { useEffect, useState } from 'react';
import { Cascader, CascaderProps } from 'antd';

import { ICON_ENTITY_MAP } from '@/constants';
import { DataDomainApi } from '@/services';

type Props = CascaderProps<CascaderOption> & {
  bizId?: string;
  onChangeCode?: (codeMap: { domCode?: string; procCode?: string }) => void;
};

export const BusinessProcessSelect = (props: Props) => {
  const { bizId, onChangeCode, ...otherProps } = props;
  const [options, setOptions] = useState<TreeItem[]>();

  const fieldNames = {
    // label: 'name',
    // value: 'id',
  };

  const handleChange = (value, items) => {
    props.onChange?.(value, items);
    onChangeCode?.({
      domCode: items?.[0]?.code,
      procCode: items?.[1]?.code,
    });
  };

  const formatTree = (tree: TreeItem[]) => {
    tree.map((item: any) => {
      item.label = (
        <span>
          <i className={`mr-2 iconfont ${ICON_ENTITY_MAP[item.nodeType]}`}></i>
          {item.name}
        </span>
      );
      item.value = item.id;
      item.children && formatTree(item.children);
      return item;
    });
    return tree;
  };

  useEffect(() => {
    DataDomainApi.getDomainAndCategory({ bizId })
      .then(({ data }: any) => setOptions(formatTree(data ?? [])))
      .catch(() => {});
  }, [bizId]);

  return (
    <Cascader
      placeholder='请选择'
      allowClear
      fieldNames={fieldNames}
      options={options}
      {...otherProps}
      onChange={handleChange}
    />
  );
};

export default BusinessProcessSelect;
