import { useEffect, useState } from 'react';
import { Select, SelectProps } from 'antd';

import { StorageClusterApi } from '@/services';

interface Props extends SelectProps {
  options?: DefaultOptionType[];
}

export function StorageClusterSelect({ options, ...otherProps }: Props) {
  const [optionsList, setOptions] = useState<Array<{ label: string; value: string }>>([]);

  useEffect(() => {
    if (options) return;
    StorageClusterApi.getAll()
      .then(({ data }) => {
        setOptions(
          data.map(x => {
            return {
              label: x.name,
              value: x.id,
            };
          }),
        );
      })
      .catch(() => {});
  }, []);

  useEffect(() => {
    if (options) {
      setOptions(
        options.map(x => {
          return {
            label: x.name,
            value: x.id,
          };
        }),
      );
    }
  }, [options]);

  return <Select placeholder='请选择' allowClear options={optionsList} {...otherProps} />;
}

export default StorageClusterSelect;
