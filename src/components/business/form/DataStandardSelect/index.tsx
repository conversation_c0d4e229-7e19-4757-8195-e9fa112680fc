import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { DeleteOutlined, SelectOutlined } from '@ant-design/icons';
import { Button, Input, SelectProps } from 'antd';

import EnumApi from '@/modules/DataStandard/services/enum';

import { SelectModal } from './SelectModal';

interface Props extends SelectProps {
  title?: string;
  allowClear?: boolean;
  disabledDelate?: boolean;
}
export const _DataStandardSelect = (props: Props, ref) => {
  const [open, setOpen] = useState(false);
  const { title, value, allowClear, ...otherProps } = props;
  const [selectedItem, setSelectedItem] = useState<any>();

  const openModal = e => {
    setOpen(true);
    e.preventDefault();
    e.stopPropagation();
  };

  const onCallback = (id: string, item: TableDeployEntity) => {
    props?.onChange?.(id, item);
    setSelectedItem(item);
    setOpen(false);
  };

  useEffect(() => {
    if (value) {
      EnumApi.getItemDetail(value).then(({ data }) => {
        setSelectedItem(data);
      });
    } else {
      setSelectedItem(undefined);
    }
  }, [value]);

  const handleDelete = () => {
    props?.onChange?.(undefined, undefined);
    setSelectedItem(undefined);
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        openModal: () => {
          setOpen(true);
        },
      };
    },
    [],
  );

  return (
    <>
      <Input
        placeholder='请选择'
        value={selectedItem?.name}
        className='no-disable-style'
        {...otherProps}
        disabled
        suffix={
          allowClear ? <>
            <Button
              type='link' className='mr-0' size='small' onClick={openModal} disabled={otherProps.disabled} title='请选择'
            >
              <SelectOutlined />
            </Button>
            {selectedItem
              && <Button
                type='link'
                size='small'
                onClick={handleDelete}
                disabled={otherProps.disabledDelate}
                title='删除'
              >
                <DeleteOutlined className='text-danger' />
              </Button>
            }

          </> :
            <Button type='link' size='small' onClick={openModal} disabled={otherProps.disabled} title='请选择'>
              <SelectOutlined />
            </Button>
        }
      />

      {open && (
        <SelectModal
          open={open}
          onCancel={() => {
            setOpen(false);
          }}
          onCallback={onCallback}
          selectedItem={selectedItem}
          id={selectedItem?.id}
          title={title ?? '请选择数据字典'}
        />
      )}
    </>
  );
};

export const DataStandardSelect = forwardRef(_DataStandardSelect);
