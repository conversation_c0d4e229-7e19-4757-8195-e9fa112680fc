import { useEffect, useRef, useState } from 'react';

import { SearchInput } from '@/components';
import { CustomTable, useCustomTableHook } from '@/components/business/CustomTable';
import { ViewTableDetail } from '@/components/business/ui/ViewTableDetail';
import { CatalogType } from '@/constants';
import { useRightsHook } from '@/hooks';
import { TableDeployApi, WarehouseLayerApi } from '@/services';

import './SourceModal.less';

import { WarehouseLayerCatalog } from './WarehouseLayerCatalog';

interface Props {
  tbId?: string;
  tbName?: string;
  rowKey?: string;
  platform: DATA_SOURCE_TYPE;
  catalog?: keyof typeof CatalogType;
  cellId?: string;
  onChange?: (val: string, record: TableDeployEntity) => void;
  selectedItem?: TableDeployEntity;
  type?: JobModel['jobRole'];
  multiple?: boolean;
  selectedList?: any[];
  otherQueryParams?: Record<string, any>;
}

export const ModalSearch = (props: Props) => {
  // eslint-disable-next-line max-len
  const { platform, catalog, cellId, rowKey = 'tbId', type = 'source', selectedItem, onChange, multiple, selectedList, otherQueryParams = {} } = props;
  const { hasDataRights } = useRightsHook();
  const [layerList, setLayerList] = useState<WAREHOUSE_LAYER.WarehouseLayerEntity[]>([]);
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const searchInputRef = useRef();
  const [searchKey, setSearchKey] = useState();
  const {
    pagination,
    setPagination,
    queryParams,
    filter,
    setFilter,
    selectedRowKeys,
    setSelectedRowKeys,
    setSelectedRows,
    handleTableChange,
    onRowSelectionChange,
  } = useCustomTableHook({
    filter: {
      deployPlatform: platform,
      layerCatalog: catalog,
      cellId,
      searchKeyword: rowKey === 'tbId' ? selectedItem?.tbName : props[rowKey],
      ...otherQueryParams,
    },
  });

  const columns: Array<ColumnType<TableDeployEntity>> = [
    {
      title: '模型名称',
      dataIndex: 'tbAlias',
      key: 'tbAlias',
      render(tbAlias: string) {
        return (
          <span>
            <i className='iconfont icon-charts-line1 text-primary-1 text-base mr-2'></i>
            {tbAlias}
          </span>
        );
      },
    },
    {
      title: '表名',
      dataIndex: 'tbName',
      key: 'tbName',
    },
    {
      title: '读/写',
      dataIndex: 'projectAuth',
      key: 'projectAuth',
      width: 100,
      render(projectAuth: ProjectAuthModel) {
        if (projectAuth) {
          if (!hasDataRights('data_model:produce', projectAuth)) {
            return '读';
          }
        }

        return '读/写';
      },
    },
    {
      title: '操作',
      dataIndex: 'tbId',
      key: 'tbId',
      fixed: 'right',
      width: 80,
      align: 'center',
      render: (tbId, record) => {
        return (
          <ViewTableDetail tbId={tbId} title={record.tbAlias}>
            查看
          </ViewTableDetail>
        );
      },
    },
  ];

  const selectLayer = (newLayerId: string) => {
    if (filter.layerId === newLayerId) {
      setFilter({
        ...filter,
        layerId: undefined,
      });
    } else {
      setFilter({
        ...filter,
        layerId: newLayerId,
      });
    }
  };

  const onRowClick = (record: TableDeployEntity) => {
    const { projectAuth } = record;
    if (type === 'sink' && projectAuth) {
      if (!hasDataRights('data_model:produce', projectAuth)) {
        return;
      }
    }
    setSelectedRowKeys([record[rowKey]]);
    setSelectedRows([record]);
    onChange?.(record[rowKey], record);
  };

  const getRowClassName = (record, index) => {
    const { projectAuth } = record;
    if (projectAuth && type === 'sink') {
      if (!hasDataRights('data_model:produce', projectAuth)) {
        return 'table-row-disabled';
      }
    }

    return '';
  };

  const fetchData = () => {
    setLoading(true);
    TableDeployApi.queryTableDeploy(queryParams)
      .then(({ data, total }) => {
        setList(data);
        setPagination({
          ...pagination,
          total,
        });
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [queryParams]);

  useEffect(() => {
    setFilter({ ...filter, deployPlatform: platform });
  }, [platform]);

  useEffect(() => {
    WarehouseLayerApi.queryLayer({
      deployPlatform: platform,
      layerCatalog: catalog,
      cellId,
      ...otherQueryParams,
    }).then(({ data }) => {
      if (data && data.length > 0) {
        setLayerList(data);
      }
    });
  }, []);

  useEffect(() => {
    if (multiple && selectedList?.length) {
      setSelectedRowKeys(selectedList.map(x => x[rowKey])); 
    }
  }, []);

  useEffect(() => {
    if (multiple) return;
    if (props[rowKey]) {
      setSelectedRowKeys([props[rowKey]]);
      setSelectedRows([...list.filter(x => x[rowKey] === props[rowKey])]);
    } else {
      setSelectedRowKeys([]);
    }
  }, [props[rowKey], list]);

  return (
    <div className='flex flex-col h-full'>
      <div className='p-4'>
        <SearchInput
          placeholder='关键字搜索'
          defaultValue={filter.searchKeyword}
          ref={searchInputRef}
          value={searchKey}
          onChange={e => setSearchKey(e.target.value)}
          onSearch={val => setFilter({ ...filter, searchKeyword: val })}
        />
      </div>

      <div className='flex-1 flex overflow-hidden'>
        <div className='w-[196px] layer-select pt-2 overflow-auto'>
          <ul>
            {layerList.map(item => (
              <li
                key={item.id}
                onClick={() => {
                  selectLayer(item.id);
                }}
                className={`flex items-center w-full ${item.id === filter.layerId ? 'selected' : ''}`}
              >
                <WarehouseLayerCatalog catalog={item.catalog}></WarehouseLayerCatalog>
                <span className='line-clamp-1 flex-1' title={item.name}>
                  {item.name}
                </span>
              </li>
            ))}
          </ul>
        </div>
        <div className='flex-1'>
          <CustomTable
            dataSource={list}
            columns={columns}
            rowSelection={{
              type: multiple ? 'checkbox' : 'radio',
              selectedRowKeys,
              getCheckboxProps: record => {
                const { projectAuth } = record;
                if (projectAuth && type === 'sink') {
                  if (!hasDataRights('data_model:produce', projectAuth)) {
                    return {
                      disabled: true,
                    };
                  }
                }
                return {
                  disabled: false,
                };
              },
              onChange: (selectedRowKeys, selectedRows) => {
                if (multiple) {
                  setSelectedRowKeys(selectedRowKeys);
                  setSelectedRows(selectedRows);
                  onChange?.(selectedRowKeys, selectedRows);
                  return;
                }
                onRowClick(selectedRows[0]);
              },
            }}
            onRowSelectionChange={onRowSelectionChange}
            pagination={{
              ...pagination,
              showSizeChanger: false,
            }}
            loading={loading}
            onChange={handleTableChange}
            scroll={{ x: 700 }}
            className='h-full w-[654px]'
            rowKey={rowKey}
            onRow={record => {
              return {
                onClick: () => {
                  if (multiple) {
                    return;
                  }
                  onRowClick(record);
                },
              };
            }}
            rowClassName={getRowClassName}
          ></CustomTable>
        </div>
      </div>
    </div>
  );
};
