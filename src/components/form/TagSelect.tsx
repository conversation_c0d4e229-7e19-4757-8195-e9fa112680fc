/**
 * @module 基于标签的下拉选择组件
 */
import { PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Select, Tag, TagProps, theme } from 'antd';
import cs from 'classnames';
import { partition } from 'lodash';

type Props<T> = PropsWithChildren<{
  value?: string[];
  onChange?: (value: string[]) => void;
  onTagItemClick?: (value: T) => void;
  title?: string;
  position?: 'left' | 'right';
  addable?: boolean;
  data: T[];
  labelKey: string;
  valueKey: string;
}>;

export function TagSelect<T extends Record<string, any>>(props: Props<T>) {
  const selectRef = useRef<any>();

  const [isAdd, setIsAdd] = useState(false);
  const { token } = theme.useToken();
  const {
    onChange,
    onTagItemClick,
    data,
    valueKey,
    labelKey,
    children,
    title = '添加',
    position = 'right',
    addable = true,
  } = props;

  const toggleAddTag = () => {
    setIsAdd(!isAdd);
  };

  useEffect(() => {
    if (isAdd) selectRef.current.focus({ cursor: 'end' });
  }, [isAdd]);

  const [value, setValue] = useState(props.value ?? []);

  const dataValues = data.map(dataItem => dataItem[valueKey]);

  const extraValueMapper = (valueItem: string) => {
    const isValueInDataList = dataValues.includes(valueItem);
    if (isValueInDataList) return null;
    return { [valueKey]: valueItem, [labelKey]: valueItem };
  };
  const extraValues = value.map(extraValueMapper).filter(Boolean) as typeof data;
  const distData = [...data, ...extraValues];

  const [values] = partition(distData, dataItem => value.includes(dataItem[valueKey]));

  useEffect(() => {
    if (props.value) {
      setValue(props.value);
    }
  }, [props.value]);

  const onClose = (valueToClose: T): TagProps['onClose'] => {
    return () => {
      const filterValue = value.filter(v => v !== valueToClose[valueKey]);
      setValue(filterValue);
      onChange?.(filterValue);
    };
  };

  const renderAddTag = () => {
    if (!addable) return null;
    return (
      <div key='add-key'>
        {!isAdd ? (
          <Tag
            className={cs('border-dashed self-start', {
              'cursor-pointer': true,
            })}
            style={{ background: token.colorBgContainer }}
            onClick={toggleAddTag}
          >
            <PlusOutlined /> {title}
          </Tag>
        ) : (
          <div>
            <Select
              size='small'
              mode='tags'
              className='w-[200px] mr-2'
              ref={selectRef}
              options={data?.map(item => {
                return {
                  label: item[labelKey],
                  value: item[valueKey],
                };
              })}
              value={value}
              onBlur={() => {
                onChange?.(value);
                toggleAddTag();
              }}
              onChange={val => {
                setValue(Array.from(new Set(val)).map(item => item?.trim()));
              }}
            />
          </div>
        )}
      </div>
    );
  };

  const tagsLdom = useMemo(() => {
    return (
      <div className='flex flex-wrap items-center gap-y-1'>
        {[
          ...values.map(value => (
            <Tag
              onClose={onClose(value)}
              closable={addable}
              key={value[valueKey]}
              className='cursor-pointer'
              onClick={() => {
                typeof onTagItemClick === 'function' && onTagItemClick(value);
              }}
            >
              {value[labelKey].slice(0, 20)}
            </Tag>
          )),
          renderAddTag(),
        ]}
      </div>
    );
  }, [values]);

  const tagsRdom = useMemo(() => {
    return (
      <div className='flex flex-wrap items-center gap-y-1'>
        {[
          renderAddTag(),
          ...values.map(value => (
            <Tag
              onClose={onClose(value)}
              closable={addable}
              key={value[valueKey]}
              className='cursor-pointer'
              onClick={() => {
                typeof onTagItemClick === 'function' && onTagItemClick(value);
              }}
            >
              {value[labelKey].slice(0, 20)}
            </Tag>
          )),
        ]}
      </div>
    );
  }, [values]);

  return (
    <div className='flex flex-col'>
      <div className='flex'>
        {position === 'left' && tagsLdom}
        {position === 'right' && tagsRdom}
      </div>
      {children && <div className='mt-2 text-gray-5 text-xs'>{children}</div>}
    </div>
  );
}
