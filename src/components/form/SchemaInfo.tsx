import { Col, Row } from 'antd';
import { NamePath } from 'antd/es/form/interface';
import { has } from 'lodash';

export interface Props<T = Objects<any>> {
  schema: SCHEMA_FORM.Schema;
  values?: T;
}

/**
 * @deprecated — A legacy feature for browser compatibility
 */
export const SchemaInfo = ({ schema, values = {} }: Props) => {
  const getDisplay = (values: Record<string, unknown>, name: NamePath, displayName?: SCHEMA_FORM.DisplayName) => {
    if (typeof displayName === 'function') {
      return displayName(values[name] as string, values);
    }
    return values[displayName ?? name] as string;
  };

  const style = schema.formProps.labelCol?.style;
  const className = 'text-captain text-gray-10 mr-[12px] inline-block text-right';
  const valueClassName = 'text-dark-65 text-captain';

  return (
    <div>
      {schema.items.map(schemaItem => {
        if (has(schemaItem, 'row')) {
          const sItem: SCHEMA_FORM.LayoutItem = schemaItem as SCHEMA_FORM.LayoutItem;
          const { row } = sItem;
          return (
            <Row key={row.key} {...row.props}>
              {row.cols.map(col => {
                const { name, displayName, hidden } = col.item.props;
                if (displayName === null) return null;
                if (hidden === true) return null;
                return (
                  <Col key={col.key} {...col.props} className='mb-[18px]'>
                    <span style={style} className={className}>
                      {col.item.props.label}:
                    </span>
                    <span className={valueClassName}>{getDisplay(values, name, displayName)}</span>
                  </Col>
                );
              })}
            </Row>
          );
        }
        const { name, displayName } = (schemaItem as SCHEMA_FORM.BaseItem).props;
        if (displayName === null) return null;
        return (
          <div key={(schemaItem as SCHEMA_FORM.BaseItem).key} className='mb-[18px]'>
            <span style={style} className={className}>
              {(schemaItem as SCHEMA_FORM.BaseItem).props.label}:
            </span>
            <span className={valueClassName}>{getDisplay(values, name, displayName)}</span>
          </div>
        );
      })}
    </div>
  );
};
