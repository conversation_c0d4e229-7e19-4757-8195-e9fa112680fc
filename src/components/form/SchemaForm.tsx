import { Col, Form, Row } from 'antd';
import { has, omit } from 'lodash';

type BaseItemProps = SCHEMA_FORM.BaseItemProps;
type LayoutItem = SCHEMA_FORM.LayoutItem;
type BaseItem = SCHEMA_FORM.BaseItem;
type Props = SCHEMA_FORM.Props;

export type Schema = SCHEMA_FORM.Schema;

export const SchemaForm = (props: Props) => {
  const { schema, initialValues } = props;

  const omitDisplayName = (props: BaseItemProps): BaseItemProps => {
    return omit(props, ['displayName']);
  };

  const getItemProps = (props: BaseItemProps): BaseItemProps => {
    const { required, label } = props;
    if (required && !props.rules) {
      props.rules = [
        {
          required: true,
          message: `请输入${label as string}`,
        },
      ];
    }
    return omitDisplayName(props);
  };

  return (
    <Form {...schema.formProps} initialValues={initialValues} style={{ padding: 0 }}>
      {schema.items.map(schemaItem => {
        if (has(schemaItem, 'row')) {
          const sItem: LayoutItem = schemaItem as LayoutItem;
          const { row } = sItem;
          return (
            <Row key={row.key} {...row.props}>
              {row.cols.map(col => {
                return (
                  <Col key={col.key} {...col.props}>
                    <Form.Item {...getItemProps(col.item.props)}>{col.item.content}</Form.Item>
                  </Col>
                );
              })}
            </Row>
          );
        }
        return (
          <Form.Item key={(schemaItem as BaseItem).key} {...getItemProps((schemaItem as BaseItem).props)}>
            {(schemaItem as BaseItem).content}
          </Form.Item>
        );
      })}
    </Form>
  );
};
