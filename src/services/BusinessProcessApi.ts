import Request from '@/request';

const url = '/api/v2/data/modeling/bus-process';
export const BusinessProcessApi = {
  url,
  /**
   * 业务过程列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 创建业务过程
   */
  async create(data: Omit<WH_LAYER.BusinessProcessEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新业务过程
   * @param data
   * @returns
   */
  async update(data: WH_LAYER.BusinessProcessEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除业务过程
   * @param id 业务过程id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取业务过程详情
   * @param id 业务过程id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部业务过程
   * @returns
   */
  async getAllBusinessProcess(options?) {
    return await Request.get(`${url}/list`, { ...options });
  },
  /**
   * 业务过程依赖情况
   * @param id 业务过程id
   * @returns
   */
  async getUsage(id: string) {
    return await Request.get(`${url}/bus-process/${id}/usage`);
  },
  /**
   * 获取数据域和业务过程权限树
   */
  async getAuthTree(options?) {
    return await Request.get('/api/v2/data/modeling/domain/bus-process/project-auth', { ...options });
  },
};
