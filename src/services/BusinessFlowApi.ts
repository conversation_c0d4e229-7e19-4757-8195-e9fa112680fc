import Request from '@/request';

const url = '/api/v2/ingestion/business-flow';
const treeuUrl = '/api/v2/ingestion/business-flow-tree';
export const BusinessFlowApi = {
  /**
   * 业务流程列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建业务流程
   */
  async create(data: Omit<DATA_DEV.BusinessFlowEntity, 'id'>) {
    return await Request.post<Response<DATA_DEV.BusinessFlowEntity>>(url, { data });
  },
  /**
   * 更新业务流程
   * @param data
   * @returns
   */
  async update(data: DATA_DEV.BusinessFlowEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除业务流程
   * @param id 业务流程id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取业务流程详情
   * @param id 业务流程id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部业务流程
   * @returns
   */
  get getAllUrl() {
    return `${url}/list`;
  },
  async getAll(options?): Promise<Response<DATA_DEV.BusinessFlowEntity[]>> {
    return await Request.get(this.getAllUrl, { ...options });
  },
  async getBusinessFlowTree(data?: {
    filter: { search: string };
    sort?: {
      name?: 'DESC' | 'ASC';
      type?: 'DESC' | 'ASC';
      updateTime?: 'DESC' | 'ASC';
    };
  }) {
    return await Request.post(`${url}-tree/query`, {
      data,
    });
  },
  /**
   * 根据referenceType referenceId获取关联对象信息
   */
  async getRelationObj(params: { referenceType: string; referenceId: string }) {
    return await Request.get(`${url}-tree/reference/${params.referenceType}/${params.referenceId}`);
  },
  /**
   * 业务流程树移动
   */
  async move(data: { referenceType: string; referenceId: string; businessFlowId: string }) {
    return await Request.put(`${url}-tree/move`, { data });
  },
  /**
   * 获取业务流程授权数据
   * @param params 分页参数
   * @returns
   */
  async getAuthTree({ filter, sort }, options) {
    return await Request.post(`${url}-tree/query-type`, {
      data: {
        filter,
        sort,
      },
      ...options,
    });
  },
  /**
   * 业务流程列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getAllList(data: QueryParams) {
    return await Request.post(`${treeuUrl}/query-list`, { data });
  },
};
