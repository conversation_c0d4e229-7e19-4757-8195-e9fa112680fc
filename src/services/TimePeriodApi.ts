import Request from '@/request';

const url = '/api/v2/data/modeling/time-period';
export const TimePeriodApi = {
  /**
   * 时间周期列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建时间周期
   */
  async create(data: Omit<DATA_INDICATOR.TimePeriodEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新时间周期
   * @param data
   * @returns
   */
  async update(data: DATA_INDICATOR.TimePeriodEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除时间周期
   * @param id 时间周期id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取时间周期详情
   * @param id 时间周期id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部时间周期
   * @returns
   */
  get getAllUrl() {
    return `${url}/list`;
  },
  async getAll(): Promise<Response<DATA_INDICATOR.TimePeriodEntity[]>> {
    return await Request.get(this.getAllUrl);
  },
  /**
   * 获取时间周期树
   * @returns
   */
  async getTree() {
    return await Request.get(`${url}/tree`);
  },
};
