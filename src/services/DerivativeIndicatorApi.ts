import Request from '@/request';

const url = '/api/v2/data/modeling/derivative-indicator';
export const DerivativeIndicatorApi = {
  /**
   * 获取全部派生指标
   */
  async getAll() {
    return await Request.get(`${url}/list`);
  },
  /**
   * 派生指标列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建派生指标
   */
  async create(data: Omit<DATA_INDICATOR.DerivativeIndicatorEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新派生指标
   * @param data
   * @returns
   */
  async update(data: DATA_INDICATOR.DerivativeIndicatorEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除派生指标
   * @param id 派生指标id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取派生指标详情
   * @param id 派生指标id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get<Response<DATA_INDICATOR.DerivativeIndicatorEntity>>(`${url}/${id}`);
  },

  /**
   * 获取派生指标树
   * @returns
   */
  async getTree(
    {
      catalog,
      searchType,
    }: {
      catalog: 'APP' | 'WH';
      searchType: string;
    },
    options?,
  ) {
    return await Request.get(`${url}/tree/${catalog}`, {
      params: {
        searchType,
      },
      ...options,
    });
  },
};
