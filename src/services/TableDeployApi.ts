import Request from '@/request';

const url = '/api/v2/data/modeling/table-deploy';

export type GetModelTableList = QueryParams<{
  cellId?: string;
  layerId?: string;
  searchKeyword?: string;
}>;

export const TableDeployApi = {
  /**
   * 模型发布表 列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 模型发布表创建
   */
  async create(data: Omit<TableDeployEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新模型发布
   * @param data
   * @returns
   */
  async update(data: Pick<TableDeployEntity, 'id' | 'name' | 'description'>) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 更新模型发布表 script settingMap
   * @param data
   * @returns
   */
  async updateSetting(data: Pick<TableDeployEntity, 'id' | 'script' | 'settingMap'>) {
    return await Request.put(`${url}/${data.id}/setting`, { data });
  },
  /**
   * 删除模型发布表
   * @param id 模型发布id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取模型发布表详情
   * @param id 模型发布id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 发布脚本
   * @param id 模型发布id
   * @returns
   */
  async deploy(data: { id: string; script: string; settingMap: Record<string, any> }) {
    return await Request.post<Response<TableDeployEntity & { errMsg: string }>>(`${url}/${data.id}/deploy`, { data });
  },
  /**
   * 重新发布脚本
   * @param id 模型发布id
   * @returns
   */
  async redeploy(tbId: string) {
    return await Request.post<Response<TableDeployEntity & { errMsg: string }>>(`${url}/${tbId}/redeploy`);
  },
  /**
   * 模型发布查询全部
   * @param { platform, businessFlowId }
   * @returns
   */
  async filterAll(data: { platform: string; businessFlowId: string }) {
    return await Request.post(`${url}/all`, { data });
  },
  /**
   * 模型发布表创建前检查是否允许创建(一阶段发布)
   * @param { platform, businessFlowId }
   * @returns
   */
  async checkStatus(data: { tbId: string }) {
    return await Request.post(`${url}/create/check`, { data });
  },
  /**
   * 模型发布表获取允许的发布动作(一阶段发布)
   * @param { platform, businessFlowId }
   * @returns
   */
  async getAllowActions(data: {
    tbId: string;
    tbName: string;
    deployType: DEPLOY_TYPE;
    platform: DATA_SOURCE_TYPE;
    tbType?: 'OBJ';
  }) {
    return await Request.post(`${url}/create/actions`, { data });
  },
  getLastSuccessDeployDetailURL(id: string) {
    return `${url}/${id}/success`;
  },

  getAllTableDeployVersionUrl(id: string) {
    return `${url}/table/${id}`;
  },
  async getAllTableDeployVersion(id: string) {
    const { data } = await Request.get(this.getAllTableDeployVersionUrl(id));
    return data;
  },

  /**
   * 模型发布表获取允许的发布动作(一阶段发布)
   * @param { platform, businessFlowId }
   * @returns
   */
  async getLastSuccessDeployDetail(tbId: string) {
    return await Request.get(`${url}/${tbId}/success`);
  },

  get getModelTableListUrl() {
    return `${url}/search`;
  },
  /**
   * 数据集成模型查询
   * @param { platform, businessFlowId }
   * @returns
   */
  async getModelTableList(data: GetModelTableList) {
    const res = await Request.post<Response<DATA_INGESTION.ModelTableListItem[]>>(`${url}/search`, {
      data,
    });
    return res;
  },

  get getTableDeployLastSuccessListByPlatformUrl() {
    return `${url}/last-success-list`;
  },

  async getTableDeployLastSuccessListByPlatform(platform: string) {
    const res = await Request.get<Response<TableDeployEntity[]>>(this.getTableDeployLastSuccessListByPlatformUrl, {
      params: { platform },
    });
    return res.data;
  },

  async queryTableDeploy(data: QueryParams) {
    return await Request.post<Response<TableDeployEntity[]>>(`${url}/query-by-condition`, { data });
  },

  async createTableDeploy(data) {
    return await Request.post(`${url}/create-deploy`, { data });
  },

  async getIndexTable() {
    return await Request.get(`${url}/last-success-list/idx`);
  },
};
