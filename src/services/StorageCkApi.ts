import Request from '@/request';

const url = '/api/v2/ingestion/storage-ck';
export const StorageCkApi = {
  /**
   * ck存储作业列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建ck存储作业
   */
  async create(data: Omit<DATA_INDICATOR.AdjunctEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新ck存储作业
   * @param data
   * @returns
   */
  async update(data: DATA_INDICATOR.AdjunctEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除ck存储作业
   * @param id ck存储作业id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取ck存储作业详情
   * @param id ck存储作业id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  async getMetric(data: { id: string; metricName: string; beginTime: string; endTime: string; width: number }) {
    const { id, metricName, ...other } = data;
    return await Request.post(`${url}/maintenance/metric/${id}/${metricName}`, {
      data: {
        ...other,
      },
    });
  },
  /**
   * 获取ck存储作业的assigment信息
   * @param id ck存储作业id
   * @returns
   */
  async getAssigment(id: string) {
    return await Request.get(`${url}/${id}/assigment`);
  },
};
