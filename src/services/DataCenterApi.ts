import Request from '@/request';

const url = '/api/v2/ingestion/data/center';
export const DataCenterApi = {
  /**
   * 数据中心列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建数据中心
   */
  async create(data: Omit<DATA_CENTER.DataCenterEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新数据中心
   * @param data
   * @returns
   */
  async update(data: DATA_CENTER.DataCenterEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除数据中心
   * @param id 数据中心id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取数据中心详情
   * @param id 数据中心id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部数据中心
   * @returns
   */
  async getAll(): Promise<Response<DATA_CENTER.DataCenterRecord[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * 获取数据中心关联的数据
   * @returns
   */
  async getReference(data: { dataCenterId: string; type: DATA_CENTER.ReferenceType }): Promise<Response<any[]>> {
    return await Request.post(`${url}/reference`, { data });
  },
};
