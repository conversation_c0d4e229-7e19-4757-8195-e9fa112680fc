import Request from '@/request';

const url = '/api/v2/data/modeling/adjunct';
export const AdjunctApi = {
  /**
   * 修饰词列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建修饰词
   */
  async create(data: Omit<DATA_INDICATOR.AdjunctEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新修饰词
   * @param data
   * @returns
   */
  async update(data: DATA_INDICATOR.AdjunctEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除修饰词
   * @param id 修饰词id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取修饰词详情
   * @param id 修饰词id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部修饰词
   * @returns
   */
  async getAll(): Promise<Response<DATA_INDICATOR.AdjunctEntity[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * 获取修饰词树
   * @returns
   */
  async getTree({ catalog, searchType }: { catalog: 'APP' | 'WH'; searchType: string }) {
    return await Request.get(`${url}/tree`, {
      params: {
        searchType,
        catalog,
      },
    });
  },
};
