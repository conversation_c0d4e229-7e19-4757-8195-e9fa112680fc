import Request from '@/request';

const url = '/api/v2/data/modeling/dimension';
export const DimensionApi = {
  /**
   * 维度列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建维度
   */
  async create(data: Omit<any, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新维度
   * @param data
   * @returns
   */
  async update(data: any) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除维度
   * @param id 维度id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取维度详情
   * @param id 维度id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部维度
   * @returns
   */
  get getAllUrl() {
    return `${url}/list`;
  },
  async getAll(): Promise<Response<any[]>> {
    return await Request.get(this.getAllUrl);
  },
};
