import { LayerCategory } from '@/constants/enum';
import Request from '@/request';

const url = '/api/v2/data/modeling/table';

export const TableApi = {
  /**
   * 获取表详情
   * @param id 表id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取集市-主题 树形式
   * @returns
   */
  get getDataMartUrl() {
    return `${url}/tree/data-mart`;
  },
  async getDataMart() {
    return await Request.get(`${url}/tree/data-mart`);
  },

  getTreeByTypeUrl(type: LayerCategory) {
    return `${url}/tree/${type.toLowerCase()}`;
  },
  /**
   * @param type - 分层类型
   * @returns 表结构树
   */
  getTreeByType(type: LayerCategory, options?) {
    return Request.get(`${url}/tree/${type.toLowerCase()}`, { ...options });
  },

  getTreeByDataDomainUrl(type: LayerCategory) {
    return `${url}/tree/domain/${type.toLowerCase()}`;
  },
  /**
   * @param type -分层类型
   * @returns 按数据域分类的表
   */
  getTreeByDataDomain(type: LayerCategory, options?) {
    return Request.get(`${url}/tree/domain/${type.toLowerCase()}`, { ...options });
  },

  /**
   * @param id - 表id
   * @returns 表使用情况
   */
  getUsageById(id: string) {
    return Request.get(`/${id}/usage`);
  },

  /**
   * 表查询
   * @returns
   */
  async getList(params: { layerCatalog?: string[]; tbType?: string[]; busProcessId?: string; bizId?: string }) {
    return await Request.get(`${url}/list`, { params });
  },
  /**
   * 创建表
   */
  get createUrl() {
    return url;
  },
  async create(data: Omit<DIMENSION_MODELING.ModelTable, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新表
   * @param data
   * @returns
   */
  updateUrl(id: string) {
    return `${url}/${id}`;
  },
  async update(data: DIMENSION_MODELING.ModelTable) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  deleteUrl(id?: string) {
    return id ? `${url}/${id}` : url;
  },
  /**
   * 删除表
   * @param id 表id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  getDetailUrl(id: string) {
    return `${url}/${id}`;
  },
  /**
   * 获取表详情
   * @param id 表id
   * @returns
   */
  async getById(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 根据指标id获取关联表
   * @param data QueryParams
   * @param filter.tbType 表类型
   * @param filter.periodId 时间周期id
   * @param filter.atomicId 原子指标id
   * @param filter.adjId 修饰词id
   * @param filter.dervId 派生指标id
   * @returns
   */
  async getRelationTable(data: QueryParams) {
    return await Request.post(`${url}/related-table`, { data });
  },
  async getDimensionTable(distinct?: boolean) {
    return await Request.get(`${url}-deploy/last-success-list/idx?distinct=${distinct ?? true}`);
  },
  async getRelatedDim(id: string) {
    return await Request.get(`${url}/${id}/related-dim`);
  },
  /**
   * 模型下线
   * @param id 表id
   * @returns
   */
  async offline(id: string) {
    return await Request.get(`${url}/offline/${id}`);
  },
  /**
   * 模型下线时关联数据
   * @param id 表id
   * @returns
   */
  async offlineUsage(id: string) {
    return await Request.get(`${url}/offline/${id}/usage`);
  },
  /**
   * 维度建模列表查询
   * @returns
   */
  async getTableList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
};
