import Request from '@/request';
import { jsonParse } from '@/utils';

const url = '/api/v2/ingestion/datasource';

export const DataSourceApi = {
  /**
   * 数据源列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?): Promise<Response<DATA_SOURCE.DataSourceListItem[]>> {
    try {
      const res: Response<DATA_SOURCE.DataSourceListItem[]> = await Request.post(`${url}/query`, { data, ...options });
      res.data = res.data?.map(d => ({
        ...d,
        connectDetail: jsonParse(d.connectDetail as string),
      }));
      return res;
    } catch (err) {
      return Promise.reject(err);
    }
  },
  /**
   * 创建数据源
   */
  async create(
    data: Omit<DATA_SOURCE.DataSourceEntity, 'id'>,
    options?,
  ): Promise<Response<DATA_SOURCE.DataSourceEntity>> {
    return await Request.post(url, { data, ...options });
  },
  /**
   * 更新数据源
   * @param data
   * @returns
   */
  async update(data: DATA_SOURCE.DataSourceEntity, options?): Promise<Response<DATA_SOURCE.DataSourceEntity>> {
    return await Request.put(`${url}/${data.id}`, { data, ...options });
  },
  /**
   * 删除数据源
   * @param id 数据源id
   * @returns
   */
  async delete(id: string, options) {
    return await Request.delete(`${url}/${id}`, { ...options });
  },
  getDetailUrl(id: string) {
    return `${url}/${id}`;
  },
  /**
   * 获取数据源详情
   * @param id 数据源id
   * @returns
   */
  async getDetail(id: string, options?): Promise<Response<DATA_SOURCE.DataSourceEntity>> {
    try {
      const res: Response<DATA_SOURCE.DataSourceEntity> = await Request.get(`${url}/${id}`, { ...options });
      res.data = {
        ...res.data,
        // connectDetail: jsonParse(res.data.connectDetail as string), // todo 验证有无副作用
      };
      return res;
    } catch (err) {
      return Promise.reject(err);
    }
  },
  /**
   * 获取全部数据源
   * @returns
   */
  async getAll(
    data?: {
      filter?: Record<string, any>;
      sort?: Record<string, SortTypes>;
    },
    options?,
  ): Promise<Response<DATA_SOURCE.DataSourceListItem[]>> {
    return await this.getList(
      {
        page: 0,
        size: 99999999,
        sort: data?.sort ?? {},
        filter: data?.filter ?? {},
      },
      options,
    );
  },
  get checkUrl() {
    return `${url}/connection/check`;
  },
  async check(data: DATA_SOURCE.CheckParams): Promise<Response<DATA_SOURCE.CheckRes>> {
    return await Request.post(`${url}/connection/check`, { data });
  },

  async connect(id: string): Promise<Response<DATA_SOURCE.CheckRes>> {
    return await Request.get(`${url}/${id}/connect`);
  },

  /**
   * 查询所有CK集群
   * @param data
   * @returns
   */
  async getCkcluster(data: clusterModal) {
    return await Request.post('/api/v2/ingestion/ckman/ck/cluster/query', {
      data,
    });
  },
  /**
   * 根据数据类型查询所有数据源
   * @param platform
   * @returns
   */
  async getListByPlatform(platform: string) {
    return await Request.get(`${url}/listByPlatform`, { params: { platform } });
  },
  async getAllServerNode(data: DATA_SOURCE.CheckParams) {
    return await Request.post(`${url}/getAllServerNode`, { data });
  },
};
