import { CreateApplication } from '@/modules/FlexibleApplication/constants';
import Request from '@/request';
import { downloadFile } from '@/utils';

const url = '/api/v2/ingestion/application';
export const FlexibleApplicationApi = {
  /**
   * 弹性作业列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 创建弹性作业
   */
  async create(data: CreateApplication) {
    return await Request.post(`${url}`, { data });
  },
  /**
   * 更新弹性作业
   * @param data
   * @returns
   */
  async update(data: CreateApplication) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除弹性作业
   * @param id 原子指标id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取实例详情
   * @param id 原子指标id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}/instance`);
  },

  /**
   * 刷新状态
   * @param id 作业id
   * @returns
   */
  async checkStatus(id: string) {
    return await Request.get(`${url}/${id}/refresh`);
  },

  /**
   * 启动作业
   * @param id 作业id
   * @returns
   */
  async applicationStart(id: string, forceStart: boolean = false) {
    return await Request.get(`${url}/${id}/start?forceStart=${forceStart}`);
  },

  /**
   * 停止作业
   * @param id 作业id
   * @returns
   */
  async applicationStop(id: string) {
    return await Request.get(`${url}/${id}/stop`);
  },

  /**
   * 扩缩容
   * @param id 作业id
   * @returns
   */
  async applicationScale(data: { id: string, count: number, forceStart?: boolean, killContainerIds?: string[]}) {
    return await Request.post(`${url}/${data.id}/scale`, { data });
  },

  /**
   * 批量启动作业
   * @param ids 作业id
   * @returns
   */
  async applicationBatchStart(ids: string[], forceStart: boolean) {
    return await Request.post(`${url}/batch-start?forceStart=${forceStart}`, {
      data: ids,
    });
  },

  /**
   * 批量停止作业
   * @param ids 作业id
   * @returns
   */
  async applicationBatchStop(ids: string[]) {
    return await Request.post(`${url}/batch-stop`, { data: ids });
  },

  /**
   * 获取运行日志
   * @param id 作业id
   * @param yarnApplicationId 作业yarnApplicationId
   * @returns
   */
  async yarnApplicationLog(id: string, yarnApplicationId: string) {
    return await Request.get(`${url}/${id}/yarn-application/${yarnApplicationId}/log`);
  },

  /**
   * 下载日志
   * @param id 作业id
   * @param url 日志下载地址
   * @returns
   */
  async downloadLog(id: string, fileurl: string) {
    downloadFile(`${url}/${id}/yarn-application-log/download`, { url: fileurl });
  },

  /**
   * 查询运行日志详情
   * @param id 作业id
   * @param url 日志地址
   * @returns
   */
  async getLogDetail(id: string, fileUrl: string) {
    return await Request.get(`${url}/${id}/yarn-application-log?url=${fileUrl}`);
  },

  /**
   * 查询日志详情
   * @param id 作业id
   * @param url 日志下载地址
   * @returns
   */
  async getOperateLog(id: string) {
    return await Request.get(`${url}/${id}/log`);
  },

  /**
   * 查询日志详情
   * @param id 作业id
   * @param url 日志下载地址
   * @returns
   */
  async getConsole(id: string, params: { opId: string; pageIndex: number; pageSize: number }) {
    return await Request.get(`${url}/${id}/console`, { params });
  },

  /**
   * 创建并启动
   * @param id 作业id
   * @param url 日志下载地址
   * @returns
   */
  async createStart(data: CreateApplication) {
    return await Request.post(`${url}/create-start`, { data });
  },

  /**
   * 修改并启动
   * @param data
   * @returns
   */
  async updateStart(data: CreateApplication) {
    return await Request.put(`${url}/${data.id}/update-start`, { data });
  },

  /**
   * 查询作业详情
   * @param id
   * @returns
   */
  async applicationDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
};
