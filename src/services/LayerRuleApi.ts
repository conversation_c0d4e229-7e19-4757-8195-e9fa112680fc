import { RULE_MODE_ENUM, RULE_TYPE_ENUM } from '@/constants';
import Request from '@/request';

const url = '/api/v2/data/modeling/layer-rule';

export const LayerRuleApi = {
  url,

  /**
   * 分层下的规则列表
   * @param params: { layerId: string; ruleMode: 'MODEL' | 'METRIC' }
   * @returns
   */
  get getListUrl() {
    return `${url}/list/query`;
  },
  async getList(params: { layerId: string; ruleMode: keyof typeof RULE_MODE_ENUM }) {
    return await Request.get<Response<WAREHOUSE_LAYER.LayerRuleEntity[]>>(this.getListUrl, { params });
  },
  /**
   * 创建规则
   */
  async create(data: Omit<WAREHOUSE_LAYER.LayerRuleEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新规则
   * @param data
   * @returns
   */
  async update(data: WAREHOUSE_LAYER.LayerRuleEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除规则
   * @param id 规则id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取规则详情
   * @param id 规则id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get<Response<WAREHOUSE_LAYER.LayerRuleEntity>>(`${url}/${id}`);
  },
  /**
   * 启用规则
   * @param id 规则id
   * @returns
   */
  async enable(id: string) {
    return await Request.put(`${url}/${id}/enable`);
  },
  /**
   * 停用规则
   * @param id 规则id
   * @returns
   */
  async disable(id: string) {
    return await Request.put(`${url}/${id}/disable`);
  },
  /**
   * 触发规则
   * @param layerId 分层id
   * @returns
   */
  async run(layerId: string) {
    return await Request.get(`${url}/${layerId}/run`);
  },
  /**
   * 规则使用情况
   * @param id 规则id
   * @returns
   */
  async usage(id: string) {
    return await Request.get(`${url}/${id}/usage`);
  },
  /**
   * 分层的默认规则
   * @param layerId 分层id
   * @returns
   */
  get getDefaultUrl() {
    return `${url}/default`;
  },
  async getDefault(
    layerId: string,
  ): Promise<Response<Record<'tableNameRule' | 'metricNameRule' | 'metricCodeRule', WAREHOUSE_LAYER.LayerRuleEntity>>> {
    return await Request.get(this.getDefaultUrl, {
      params: {
        layerId,
      },
    });
  },

  /**
   * 设置为默认规则
   * @param ruleId 规则id
   * @returns
   */
  async setDefault({
    layerId,
    ruleType,
    ruleId,
  }: {
    layerId: string;
    ruleType: keyof typeof RULE_TYPE_ENUM;
    ruleId: string;
  }) {
    return await Request.put(`${url}/default/${layerId}`, {
      data: {
        ruleType,
        ruleId,
      },
    });
  },
};
