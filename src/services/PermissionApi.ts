import { PermissionEntity } from '@/models';
import Request from '@/request';

const url = '/api/v2/security/permission';
export const PermissionApi = {
  /**
   * 权限列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建权限
   */
  async create(data: Omit<PermissionEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新权限
   * @param data
   * @returns
   */
  async update(data: PermissionEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除权限
   * @param id 权限id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取权限详情
   * @param id 权限id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取权限关联数据
   * @param id 权限id
   * @returns
   */
  async getUsage(id: string) {
    return await Request.get(`${url}/${id}/usage`);
  },
  /**
   * 获取当前登录用户拥有的权限
   * @returns
   */
  async getLoginUserPermission() {
    return await Request.get(`${url}/login-user`);
  },
  /**
   * 获取当前登录用户拥有的权限
   * @returns
   */
  async getAllPermission() {
    return await Request.get(`${url}/front`);
  },
  /**
   * 获取所有前端的权限
   * @returns
   */
  async getCodeTreePermission() {
    return await Request.get(`${url}/code/tree`);
  },
  /**
   * 获取当前用户拥有的权限
   * @returns
   */
  async getUserRights() {
    return await Request.get(`${url}/login-user/code`);
  },
  /**
   * 获取当前用户拥有的权限
   * @returns
   */
  async getAuthProjects() {
    return await Request.get('/api/v2/security/project/list/login-user');
  },
  /**
   * 根据资源code获取 权限列表
   * @returns
   */
  async getResourceAuthList(resourceType: string) {
    return await Request.get(`${url}/resource/code/${resourceType}`);
  },
};
