import Request from '@/request';

const baseUrl = '/api/v2';
const url = '/api/v2/realtime/check-task';
const pipelineUrl = '/api/v2/realtime/check-task-pipeline';

export interface CheckTaskListItem {
  taskId: string;
  taskName: string;
  status: 'SUCCESS' | 'FAILED';
  reason?: string;
}

export interface SlotMountResultListItem {
  checkTaskList: CheckTaskListItem[];
  jobSlot: number;
}

export interface CheckTaskPipelineItem {
  jobDisplay: string;
  jobName: string;
  slotMountResultList: SlotMountResultListItem[];
  summaryStatus: 'SUCCESS' | 'FAILED';
}

export const CheckTaskApi = {
  /**
   * 检测任务列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 检测任务全部数据
   * @returns
   */
  async getAllList() {
    return await Request.get(`${url}/list`);
  },
  /**
   * 创建检测任务
   */
  async create(data: Omit<DATA_INDICATOR.AdjunctEntity, 'id'>) {
    return await Request.post(`${url}`, { data });
  },
  /**
   * 更新检测任务
   * @param data
   * @returns
   */
  async update(data: DATA_INDICATOR.AdjunctEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除检测任务
   * @param id 检测任务id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取检测任务详情
   * @param id 检测任务id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 管线作业列表查询
   * @param params 分页参数
   * @returns
   */
  async getPipeLine(data) {
    return await Request.post(`${baseUrl}/ingestion/pipeline/query`, { data });
  },
  async getPipeLineNoAuth(data) {
    return await Request.post(`${baseUrl}/ingestion/pipeline/query-none-auth`, { data });
  },
  /**
   * 管线作业算子查询
   * @param params 分页参数
   * @returns
   */
  async getAlgList(data) {
    return await Request.post(`${baseUrl}/ingestion/pipeline/same-job`, { data });
  },
  /**
   * 算子输出查询
   * @param params 分页参数
   * @returns
   */
  async getJobSlotList(algName: string) {
    return await Request.get(`${baseUrl}/ingestion/job-name/${algName}`);
  },
  /**
   * 标签列表查询
   * @param params 分页参数
   * @returns
   */
  async getTagList() {
    return await Request.get(`${baseUrl}/ingestion/tag/listByType?tagType=TASK`);
  },
  /**
   * 根据检测作业id查询匹配的管线作业
   * @param params 分页参数
   * @returns
   */
  async getMatchedPipeline(taskId: string) {
    return await Request.get(`${url}/matched-pipeline/${taskId}`);
  },
  /**
   * 根据关系作业id查询挂载的检测任务
   * @param pipelineId
   * @returns
   */
  async getCheckTaskByPipeline(pipelineId: string) {
    return await Request.get<Response<CheckTaskPipelineItem[]>>(`${url}-pipeline/pipeline/${pipelineId}`);
  },
  // 校验是否需要重启
  async previewRestartPipeline(data: DATA_INDICATOR.AdjunctEntity) {
    return Request.post(`${url}/preview-restart-pipeline`, { data });
  },
  // 统计检测任务关联的管线作业
  async statsPipeline(taskId) {
    return Request.get(`${pipelineUrl}/stats/${taskId}`);
  },
  // 查询检测任务关联的管线作业
  async queryRelatedPipeline(data) {
    return Request.post(`${pipelineUrl}/query`, { data });
  },
  async getJobMatchedPipeline(data) {
    return await Request.post(`${url}/job-matched-pipeline`, { data });
  },
  async restartPipeline(id, forceRestart) {
    return await Request.put(`${baseUrl}/ingestion/pipeline/${id}/restart`, { data: { forceRestart } });
  },
  async batchRestartPipeline(ids, forceRestart) {
    return await Request.put(`${baseUrl}/ingestion/pipeline/batch-restart`, { data: { ids, forceRestart } });
  },
};
