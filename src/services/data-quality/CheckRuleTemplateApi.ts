import { RealtimeRuleTemplateEntity } from '@/modules/DataQuality/realtime/RuleTemplate/models';
import Request from '@/request';

const url = '/api/v2/realtime/check-rule-template';
export const CheckRuleTemplateApi = {
  /**
   * 规则模板列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 规则模板全部数据
   * @returns
   */
  async getAllList() {
    return await Request.get(`${url}/list`);
  },
  /**
   * 创建规则模板
   */
  async create(data: Omit<RealtimeRuleTemplateEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新规则模板
   * @param data
   * @returns
   */
  async update(data: RealtimeRuleTemplateEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除规则模板
   * @param id 规则模板id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取规则模板详情
   * @param id 规则模板id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 统计类别对应的模板数量
   * @returns
   */
  async getCountByCategory() {
    return await Request.get(`${url}/count-by-category`);
  },
  /**
   * 获取全部模板
   * @returns
   */
  async getAll() {
    return await Request.get(`${url}/list`);
  },
};
