import Request from '@/request';

const url = '/api/v2/data/modeling/table-deploy-history';
export const TableDeployHistoryApi = {
  /**
   * 模型发布历史 列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 获取模型发布历史详情
   * @param id 模型发布历史id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },

  /**
   * 获取模型发布历史详情
   * @param id 模型发布历史id
   * @returns
   */
  async getVersionsDetail(tdId: string) {
    return await Request.get(`${url}/${tdId}/versions`);
  },
};
