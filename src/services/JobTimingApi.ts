// import { downloadFile } from '@/utils';
import { ActuatorData, CreateJobTiming } from '@/modules/JobTiming/models';
import Request from '@/request';

const url = '/api/v2/xxl-job';
export const JobTimingApi = {
  /**
   * 弹性作业列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    const params = { ...data, ...data.filter };
    delete params.filter;
    return await Request.get(`${url}/job`, { params, ...options });
  },
  /**
   * 创建原子指标
   */
  async create(data: CreateJobTiming) {
    return await Request.post(`${url}/job`, { data });
  },
  /**
   * 更新原子指标
   * @param data
   * @returns
   */
  async update(data: CreateJobTiming) {
    return await Request.put(`${url}/job/${data.id}`, { data });
  },
  /**
   * 删除弹性作业
   * @param id 原子指标id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/job/${id}`);
  },
  /**
   * 获取实例详情
   * @param id 原子指标id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/job/${id}`);
  },

  /**
   * 获取执行器列表
   * @param id 原子指标id
   * @returns
   */
  async getActuatorList(params: QueryParams, options?) {
    return await Request.get(`${url}/group`, { params, ...options });
  },

  /**
   * 启动作业
   * @param id 作业id
   * @returns
   */
  async setJobStart(id: string) {
    return await Request.put(`${url}/job/${id}/start`);
  },

  /**
   * 停止作业
   * @param id 作业id
   * @returns
   */
  async setJobPause(id: string) {
    return await Request.put(`${url}/job/${id}/pause`);
  },

  /**
   * 执行一次
   * @param id 作业id
   * @returns
   */
  async triggerJobTiming(id: string, data: { executorParam: string; addressList: string }) {
    return await Request.put(`${url}/job/${id}/trigger`, { data });
  },

  /**
   * 查询执行器详情
   * @param groupId 执行器id
   * @returns
   */
  async getGroupDetail(groupId: string) {
    return await Request.get(`${url}/group/${groupId}`);
  },

  /**
   * 查询下次触发时间
   * @param cron 运行计划cron表达式
   * @returns
   */
  async checkCron(cron: string) {
    return await Request.get(`${url}/job/todo`, { params: { cron } });
  },

  /**
   * 获取执行器详情
   * @param id 运行计划cron表达式
   * @returns
   */
  async getActuatorDetail(id: string) {
    return await Request.get(`${url}/group/${id}`);
  },

  /**
   * 更新执行器
   * @returns
   */
  async actuatorUpdate(data: ActuatorData) {
    return await Request.put(`${url}/group/${data.id}`, { data });
  },

  /**
   * 创建执行器
   * @returns
   */
  async actuatorCreate(data: ActuatorData) {
    return await Request.post(`${url}/group`, { data });
  },

  /**
   * 删除执行器
   * @param id 执行器id
   * @returns
   */
  async actuatorDelete(id: string) {
    return await Request.delete(`${url}/group/${id}`);
  },

  /**
   * 调度日志列表
   * @returns
   */
  async getDispatchTaskList(params: QueryParams) {
    return await Request.get(`${url}/log`, { params });
  },

  /**
   * 删除日志
   * @param id 执行器id
   * @returns
   */
  async DispatchLogDelete(id: string) {
    return await Request.delete(`${url}/log/${id}`);
  },

  /**
   * 获取日志详情
   * @param logId 日志id
   * @returns
   */
  async DispatchLogCat(logId: string, data) {
    return await Request.put(`${url}/log/${logId}/cat`, { data });
  },

  /**
   * 获取日志详情
   * @param logId 日志id
   * @returns
   */
  async DispatchLogDetail(logId: string) {
    return await Request.get(`${url}/log/${logId}`);
  },
};
