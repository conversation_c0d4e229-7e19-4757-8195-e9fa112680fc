import Request from '@/request';
import { jsonParse } from '@/utils';

const url = '/api/v2/ingestion/cell';
export const GateWayApi = {
  /**
   * 网关列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams): Promise<Response<GATEWAY.ListItem[]>> {
    try {
      const res: Response<GATEWAY.ListItem[]> = await Request.post(`${url}/query`, { data });
      res.data = res.data?.map(d => ({
        ...d,
        connectDetail: jsonParse(d.connectDetail as string),
      }));
      return res;
    } catch (err) {
      return Promise.reject(err);
    }
  },
  /**
   * 创建网关
   */
  async create(data: Omit<GATEWAY.Entity, 'id'>): Promise<Response<GATEWAY.Entity>> {
    return await Request.post(url, { data });
  },
  /**
   * 更新网关
   * @param data
   * @returns
   */
  async update(data: GATEWAY.Entity): Promise<Response<GATEWAY.Entity>> {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除网关
   * @param id 网关id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取网关详情
   * @param id 网关id
   * @returns
   */
  async getDetail(id: string): Promise<Response<GATEWAY.Entity>> {
    try {
      const res: Response<GATEWAY.Entity> = await Request.get(`${url}/${id}`);
      res.data = {
        ...res.data,
        connectDetail: jsonParse(res.data.connectDetail as string),
      };
      return res;
    } catch (err) {
      return Promise.reject(err);
    }
  },
  /**
   * 获取全部网关
   * @returns
   */
  async getAll(data?: {
    filter?: Record<string, any>;
    sort?: Record<string, SortTypes>;
  }): Promise<Response<GATEWAY.ListItem[]>> {
    return await this.getList({
      page: 0,
      size: 99999999,
      sort: data?.sort ?? {},
      filter: data?.filter ?? {},
    });
  },
  async check(data: GATEWAY.CheckParams): Promise<Response<GATEWAY.CheckRes>> {
    return await Request.post(`${url}/connection/check`, { data });
  },
  async connect(id: string): Promise<Response<GATEWAY.CheckRes>> {
    return await Request.get(`${url}/${id}/connect`);
  },
};
