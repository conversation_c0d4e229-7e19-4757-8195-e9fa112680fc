import { ApiEntity, ClientEntity } from '@/modules/DataService/models';
import Request from '@/request';

const url = '/api/v2/data-service/client';
export const ClientService = {
  /**
   * 授权客户端列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams, options?) {
    return await Request.post(`${url}/query`, { data, ...options });
  },
  /**
   * 创建授权客户端
   */
  async create(data: Omit<ClientEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新授权客户端
   * @param data
   * @returns
   */
  async update(data: ClientEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除授权客户端
   * @param id 授权客户端Id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取授权客户端详情
   * @param id 授权客户端id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部授权客户端
   * @returns
   */
  async getAll(): Promise<Response<ClientEntity[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * 生成客户端apiKey
   * @returns
   */
  async createApiKey() {
    return await Request.get(`${url}/create-api-key`);
  },
  /**
   * 生成客户端secretKey
   * @returns
   */
  async createSecretKey() {
    return await Request.get(`${url}/create-secret-key`);
  },
  /**
   * 根据id查询已授权api列表
   * @returns
   */
  async getAuthList(id: string, data: QueryParams): Promise<Response<ApiEntity[]>> {
    return await Request.post(`${url}/${id}/auth-list`, { data });
  },
  /**
   * 根据id查询所有授权api列表
   * @returns
   */
  async getApiList(id: string, data: QueryParams): Promise<Response<ApiEntity[]>> {
    return await Request.post(`${url}/${id}/api-list`, { data });
  },
};
