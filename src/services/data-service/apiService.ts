import { ApiEntity, DsColumnTypeEnum, DsOperationCodeEnum } from '@/modules/DataService/models';
import Request from '@/request';

const url = '/api/v2/data-service/api';
export const ApiService = {
  /**
   * api列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建api
   */
  async create(data: Omit<ApiEntity, 'id'>) {
    return await Request.post(`${url}`, { data });
  },
  /**
   * 更新api
   * @param data
   * @returns
   */
  async update(data: ApiEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除api
   * @param id apiid
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取api详情
   * @param id apiId
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部api
   * @returns
   */
  async getAll(): Promise<Response<ApiEntity[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * api创建并发布
   */
  async createPublish(data): Promise<Response<ApiEntity>> {
    return await Request.post(`${url}/publish`, { data });
  },
  /**
   * api更新并发布
   */
  async publish(data): Promise<Response<ApiEntity>> {
    return await Request.post(`${url}/${data.id}/publish`, { data });
  },
  /**
   * api上线
   */
  async online(data: { apis: Array<{ id: string }> }): Promise<Response<ApiEntity[]>> {
    return await Request.post(`${url}/online`, { data });
  },
  /**
   * api下线
   */
  async offline(data: { apis: Array<{ id: string }> }): Promise<Response<ApiEntity[]>> {
    return await Request.post(`${url}/offline`, { data });
  },
  /**
   * 根据数据源获取数据源下的topic列表
   * @param params 分页参数
   * @returns
   */
  async getTopics(data: QueryParams) {
    return await Request.post(`${url}/query-table`, { data });
  },
  async getApiPath() {
    return await Request.get(`${url}/create-api-path`);
  },
  /**
   * 根据id获取授权api列表
   * @returns
   */
  async getAuthList(apiId: string): Promise<Response<ApiEntity[]>> {
    return await Request.get(`${url}/${apiId}/auth-list`);
  },
  async getRequestParams(data: { apiMode: string; apiSql: string; dataSourceId: string }) {
    return await Request.post(`${url}/analyze/param/request`, { data });
  },

  async getResponseParams(data: { apiMode: string; apiSql: string; dataSourceId: string }) {
    return await Request.post(`${url}/analyze/param/response`, { data });
  },
  /**
   * 数据预览
   * @param data
   * @returns
   */
  async getTablePreviewData(data: {
    dsId: string;
    databaseName: string;
    dataTable: string;
    filters: Array<{
      name: string;
      operation: keyof typeof DsOperationCodeEnum;
      dataType: keyof typeof DsColumnTypeEnum;
      value: string;
    }>;
  }, options?) {
    return await Request.post(`${url}/preview`, { data, ...options });
  },
  /**
   * api测试
   */
  async testApi(
    data: ApiEntity & {
      reqParam: {
        pageNum: number;
        pageSize: number;
        param: Record<string, string>;
      };
    },
  ) {
    return await Request.post(`${url}/test/query`, {
      data,
      getResponse: true,
      responseType: 'text',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json;charset=UTF-8',
      },
    });
  },
  /**
   * 服务数据概览
   */
  async getOverview(data) {
    return await Request.post(`${url}/overview`, { data });
  },
  /**
   * 服务数据概览-失败返回码占比
   */
  async getFailedRetCode(data) {
    return await Request.post(`${url}/overview/failed-ret-code`, { data });
  },
  /**
   * 获取处理类列表
   */
  async getHandlerTree() {
    return await Request.get(`${url}/handler/tree`);
  },
  /**
   * 获取处理类列表
   */
  async getHandlerclassName(className) {
    return await Request.get(`${url}/handler/class-name/${className}`);
  },
  /**
   * api下线
   */
  async publishSingle(id: string): Promise<Response<ApiEntity[]>> {
    return await Request.post(`${url}/${id}/publish/single`);
  },
  /**
   * api下线
   */
  async publishBulk(data: { apis: Array<{ id: string }> }): Promise<Response<ApiEntity[]>> {
    return await Request.post(`${url}/publish/bulk`, { data });
  },
  /**
   * sql预览
   */
  async getSqlPreview(data: {
    dsId?: string;
    database?: string;
    jdbcUrl?: string;
    jdbcDriver?: string;
    jdbcJar?: string;
    userName?: string;
    password?: string;
    sql: string;
  }) {
    return await Request.post(`${url}/sql-preview`, { data });
  },
};
