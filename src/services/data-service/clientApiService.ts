import { ClientApiEntity } from '@/modules/DataService/models';
import Request from '@/request';

const url = '/api/v2/data-service/client-api';
export const ClientApiService = {
  /**
   * 授权客户端列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建授权客户端
   */
  async create(data: Omit<ClientApiEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新授权客户端
   * @param data
   * @returns
   */
  async update(data: { id: string; clientId: string; apiId: string; extraSetting: any }) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除授权客户端
   * @param id 授权客户端Id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取授权客户端详情
   * @param id 授权客户端id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部授权客户端
   * @returns
   */
  async getAll(): Promise<Response<ClientApiEntity[]>> {
    return await Request.get(`${url}/list`);
  },
  /**
   * 授权客户端与api关系批量创建
   */
  async batchAdd(data) {
    return await Request.post(`${url}/batchAdd`, { data });
  },
};
