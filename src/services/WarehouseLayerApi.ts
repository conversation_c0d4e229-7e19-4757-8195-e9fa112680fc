import { CatalogType } from '@/constants';
import { TableType } from '@/constants/enum';
import Request from '@/request';

export interface GetListParams {
  tbType?: keyof typeof TableType;
  catalog?: string;
}

const url = '/api/v2/data/modeling/warehouse-layer';

export const WarehouseLayerApi = {
  get getListUrl() {
    return `${url}/list`;
  },
  /**
   * 数仓分层列表查询
   * @param params 查询参数
   * @returns
   */
  async getList(params?: GetListParams, options?) {
    const res = await Request.get<Response<Layer[]>>(this.getListUrl, {
      params,
      ...options,
    });
    return res.data;
  },
  /**
   * 创建数仓分层
   */
  async create(data: any) {
    return await Request.post(url, { data });
  },
  /**
   * 更新数仓分层
   * @param data
   * @returns
   */
  async update(data: any) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除数仓分层
   * @param id 数仓分层id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取数仓分层详情
   * @param id 数仓分层id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get<Response<WAREHOUSE_LAYER.WarehouseLayerEntity>>(`${url}/${id}`);
  },
  /**
   * 查询数仓分层
   * @param id 网关id
   * @returns
   */
  get queryAllUrl() {
    return `${url}/query`;
  },
  async queryAll(id: string) {
    const { data } = await Request.post<Response<Layer[]>>(this.queryAllUrl, { data: { cellId: id } });
    return data;
  },
  // 根据查询条件查询所有数仓分层
  async queryLayer(params: {
    deployPlatform: DATA_SOURCE_TYPE;
    layerCatalog?: keyof typeof CatalogType;
    cellId?: string;
  }) {
    return await Request.post<Response<[WAREHOUSE_LAYER.WarehouseLayerEntity[]]>>(`${url}/query-by-condition`, {
      data: params,
    });
  },
};
