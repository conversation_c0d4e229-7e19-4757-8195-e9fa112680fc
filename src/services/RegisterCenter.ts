import Request from '@/request';
import { jsonParse } from '@/utils';

const url = '/api/v2/ingestion/register-center';
export const RegisterCenterApi = {
  /**
   * 注册中心列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams): Promise<Response<REGISTER_CENTER.ListItem[]>> {
    try {
      const res: Response<REGISTER_CENTER.ListItem[]> = await Request.post(`${url}/query`, { data });
      res.data = res.data?.map(d => ({
        ...d,
        connectDetail: jsonParse(d.connectDetail),
      }));
      return res;
    } catch (err) {
      return Promise.reject(err);
    }
  },
  /**
   * 创建注册中心
   */
  async create(data: Omit<REGISTER_CENTER.Entity, 'id'>): Promise<Response<REGISTER_CENTER.Entity>> {
    return await Request.post(url, { data });
  },
  /**
   * 更新注册中心
   * @param data
   * @returns
   */
  async update(data: REGISTER_CENTER.Entity): Promise<Response<REGISTER_CENTER.Entity>> {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除注册中心
   * @param id 注册中心id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取注册中心详情
   * @param id 注册中心id
   * @returns
   */
  async getDetail(id: string): Promise<Response<REGISTER_CENTER.Entity>> {
    try {
      const res: Response<REGISTER_CENTER.Entity> = await Request.get(`${url}/${id}`);
      res.data = {
        ...res.data,
        connectDetail: jsonParse(res.data.connectDetail as string),
      };
      return res;
    } catch (err) {
      return Promise.reject(err);
    }
  },
  /**
   * 获取全部注册中心
   * @returns
   */
  async getAll(data?: {
    filter?: Record<string, any>;
    sort?: Record<string, SortTypes>;
  }): Promise<Response<REGISTER_CENTER.ListItem[]>> {
    return await this.getList({
      page: 0,
      size: 99999999,
      sort: data?.sort ?? {},
      filter: data?.filter ?? {},
    });
  },
  async check(data: REGISTER_CENTER.CheckParams): Promise<Response<REGISTER_CENTER.CheckRes>> {
    return await Request.post(`${url}/connection/check`, { data });
  },
  async connect(id: string): Promise<Response<REGISTER_CENTER.CheckRes>> {
    return await Request.get(`${url}/${id}/connect`);
  },
};
