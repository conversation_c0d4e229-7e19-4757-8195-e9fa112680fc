import { GenerateFlinkSqlReqItem } from '@/models/FlinkSqlEntity';
import Request from '@/request';

const url = '/api/v2/ingestion/process';
export const ProcessApi = {
  /**
   * 数据处理列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建数据处理
   */
  async create(data: Omit<ProcessModel, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新数据处理
   * @param data
   * @returns
   */
  async update(data: ProcessModel) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除数据处理
   * @param id 数据处理id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取数据处理详情
   * @param id 数据处理id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部数据处理
   * @returns
   */
  get getAllUrl() {
    return `${url}/list`;
  },
  async getAll() {
    return await Request.get(this.getAllUrl);
  },
  /**
   * 创建并发布
   */
  async createAndPublish(data: Omit<ProcessModel, 'id'>) {
    return await Request.post(`${url}/publish`, { data });
  },
  /**
   * 更新并发布
   */
  async updateAndPublish(data: ProcessModel) {
    return await Request.put(`${url}/${data.id}/publish`, { data });
  },
  /**
   * sql作业根据数据源id或模型id生成CREATE语句
   * * @param reqList 选中的数据源id或模型id
   * @returns
   */
  async generateFlinkSql(reqList: GenerateFlinkSqlReqItem[]) {
    return await Request.post(`${url}/generate-flink-sql`, { data: { reqList } });
  },
  /**
   * 根据条件查询所有数据处理作业
   * @returns
   */
  async getProcessListByCondition(data: {
    search?: string;
    isPublished?: boolean;
    pipelineType?: ProcessModel['processType'];
  }) {
    return await Request.post(`${url}/condition`, { data });
  },
  /**
   * 获取版本发布历史
   * @returns
   */
  async getHistory(data: QueryParams) {
    return await Request.post(`${url}/history/query`, { data });
  },

  async previewMatchedCheckTaskPipeline(data) {
    return await Request.post(`${url}/preview-matched-check-task`, { data });
  },
};
