import Request from '@/request';

const url = '/api/v2/data/modeling/atomic-indicator';
export const AtomicIndicatorApi = {
  /**
   * 原子指标列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建原子指标
   */
  async create(data: Omit<DATA_INDICATOR.AtomicIndicatorEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新原子指标
   * @param data
   * @returns
   */
  async update(data: DATA_INDICATOR.AtomicIndicatorEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除原子指标
   * @param id 原子指标id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取原子指标详情
   * @param id 原子指标id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 获取全部原子指标
   * @returns
   */
  async getAll() {
    return await Request.get(`${url}/list`);
  },
  /**
   * 获取原子指标树
   * @returns
   */
  async getTree({ searchType }, options?) {
    return await Request.get(`${url}/tree/${searchType as string}`, { ...options });
  },
};
