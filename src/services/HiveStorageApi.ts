import { HiveCustomSqlQuickOption, HiveEntity } from '@/models/HiveEntity';
import Request from '@/request';

const url = '/api/v2/ingestion/storage-hive';
export const HiveStorageApi = {
  /**
   * hive存储作业列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建hive存储作业
   */
  async create(data: Omit<HiveEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新hive存储作业
   * @param data
   * @returns
   */
  async update(data: HiveEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除hive存储作业
   * @param id hive存储作业id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取hive存储作业详情
   * @param id hive存储作业id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },
  /**
   * 更新hive存储作业并发布
   * @param data
   * @returns
   */
  async updateAndPublish(data: HiveEntity) {
    return await Request.put(`${url}/${data.id}/publish`, { data });
  },
  /**
   * 创建hive存储作业并发布
   */
  async createAndPublish(data: Omit<HiveEntity, 'id'>) {
    return await Request.post(`${url}/publish`, { data });
  },
  /**
   * 判断是否已修改参数, true表示有修改
   */
  async checkIsModified(data: HiveEntity & { sqlMd5: string }) {
    return await Request.post(`${url}/is-modified`, { data });
  },
  /**
   * 构建script
   */
  async buildScript(data: HiveEntity) {
    return await Request.post(`${url}/build-script`, { data });
  },
  /**
   * 获取所有flink字段处理函数
   */
  async getFieldProcessFn(): Promise<Response<HiveCustomSqlQuickOption[]>> {
    return await Request.get(`${url}/field-process-fn`);
  },
};
