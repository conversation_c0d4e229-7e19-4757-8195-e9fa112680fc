import Request from '@/request';

const url = '/api/v2/data/modeling/domain';
export const DataDomainApi = {
  /**
   * 数据域列表分页查询
   * @param params 分页参数
   * @returns
   */
  async getList(data: QueryParams) {
    return await Request.post(`${url}/query`, { data });
  },
  /**
   * 创建数据域
   */
  async create(data: Omit<DATA_INDICATOR.AdjunctEntity, 'id'>) {
    return await Request.post(url, { data });
  },
  /**
   * 更新数据域
   * @param data
   * @returns
   */
  async update(data: DATA_INDICATOR.AdjunctEntity) {
    return await Request.put(`${url}/${data.id}`, { data });
  },
  /**
   * 删除数据域
   * @param id 数据域id
   * @returns
   */
  async delete(id: string) {
    return await Request.delete(`${url}/${id}`);
  },
  /**
   * 获取数据域详情
   * @param id 数据域id
   * @returns
   */
  async getDetail(id: string) {
    return await Request.get(`${url}/${id}`);
  },

  get getAllDomainUrl() {
    return `${url}/list`;
  },
  /**
   * 获取全部数据域
   * @returns
   */
  getAllDomain(options): Promise<Response<DataDomain[]>> {
    return Request.get(this.getAllDomainUrl, { ...options });
  },

  /**
   * 获取数据域及下属业务过程
   * @returns
   */
  get getDomainAndCategoryUrl() {
    return `${url}/bus-process`;
  },
  getDomainAndCategory(params: { bizId?: string }): Promise<Response<DIMENSION_MODELING.ModelNode[]>> {
    return Request.get(this.getDomainAndCategoryUrl, { params });
  },
};
