export enum ProcessStatus {
  'UNSTART' = '未启动',
  'RUNNING' = '运行中',
  'STOPPED' = '已停止',
  'EXCEPTION' = '异常',
}

export enum TaskStatus {
  WAITING_START = '等待启动',
  STARTING = '启动中',
  START_FAILED = '启动失败',

  WAITING_STOP = '等待停止',
  STOPPING = '停止中',
  STOP_FAILED = '停止失败',
  STOPPED = '已停止',

  RESTARTING = '重启中',
  DELETING = '删除中',
  RUNNING = '运行中',
  FINISHED = '已完成',
  FAILED = '运行失败',
  UNKNOWN = '未知状态',

  WAITING_DELETE = '等待删除',
  DELETE_FAILED = '删除失败',

  DRAFT = '草稿',
}
