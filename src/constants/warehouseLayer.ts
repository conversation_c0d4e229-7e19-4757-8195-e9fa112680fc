export enum CatalogType {
  'DS' = 'DS',
  'WH' = 'WH',
  'APP' = 'APP',
}

export enum RULE_MODE_ENUM {
  'MODEL' = '模型规则',
  'METRIC' = '指标规则',
}

export enum TBTypes {
  'ODS' = 'ODS',
  'DWD' = 'DWD',
  'DIM' = 'DIM',
  'DWS' = 'DWS',
  'DIMENSION' = 'DIMENSION',
  'ADS' = 'ADS',
  'IDX' = 'IDX',
}

export enum CHECK_MODE_ENUM {
  WEAK = '弱规则',
  STRICT = '强规则',
}

export const CHECK_MODE_OPTIONS = Object.entries(CHECK_MODE_ENUM).map(([value, label]) => ({ value, label }));

export const RULE_MODE_OPTIONS = Object.entries(RULE_MODE_ENUM).map(([value, label]) => ({ value, label }));

export const CATALOGS = [
  {
    label: '贴源层',
    sort: 1,
    value: CatalogType.DS,
    children: [{ label: '贴源表', value: TBTypes.ODS }],
  },
  {
    label: '公共层',
    sort: 2,
    value: CatalogType.WH,
    children: [
      { label: '明细表', value: TBTypes.DWD },
      { label: '维度表', value: TBTypes.DIM },
      { label: '汇总表', value: TBTypes.DWS },
      { label: '维度', value: TBTypes.DIMENSION },
    ],
  },
  {
    label: '应用层',
    sort: 3,
    value: CatalogType.APP,
    children: [
      { label: '应用表', value: TBTypes.ADS },
      { label: '维度表', value: TBTypes.DIM },
      { label: '维度', value: TBTypes.DIMENSION },
    ],
  },
];

export const RULE_TYPE = {
  MODEL: [{ label: '表名', value: 'TABLE_NAME' }],
  METRIC: [
    { label: '中文名称', value: 'METRIC_NAME' },
    { label: '英文缩写', value: 'METRIC_CODE' },
  ],
};

export enum RULE_TYPE_ENUM {
  TABLE_NAME = '表名',
  METRIC_NAME = '中文名称',
  METRIC_CODE = '英文缩写',
}

export enum ODS_TABLE_NAME_OPTIONS_ENUM {
  BUSINESS_CATEGORY_CODE = '业务大类英文缩写',
  DATA_DOMAIN_CODE = '数据域英文缩写',
  BUSINESS_PROCESS_CODE = '业务过程英文缩写',
  STORAGE_POLICY_CODE = '存储策略英文缩写',
  DIM_CODE = '维度定义缩写',
  TIME_PERIOD_CODE = '统计周期',
  DATA_MART_CODE = '数据集市英文缩写',
  DATA_SUBJECT_CODE = '主题域英文缩写',
  CUSTOM = '自定义',

  ADJUNCT = '修饰词',
  ATOMIC_INDICATOR = '原子指标',
}

export enum INDICATOR_NAME_OPTIONS_ENUM {
  ADJUNCT = '修饰词',
  ATOMIC_INDICATOR = '原子指标',
  TIME_PERIOD_CODE = '统计周期',
}

export const MODEL_RULES = {
  DS: {
    label: '贴源层',
    ODS: [
      { label: '业务大类英文缩写', value: 'BUSINESS_CATEGORY_CODE' },
      { label: '数据域英文缩写', value: 'DATA_DOMAIN_CODE' },
      { label: '业务过程英文缩写', value: 'BUSINESS_PROCESS_CODE' },
      { label: '存储策略英文缩写', value: 'STORAGE_POLICY_CODE' },
      { label: '自定义', value: 'CUSTOM' },
    ],
  },
  WH: {
    label: '公共层',
    DIM: [
      { label: '业务大类英文缩写', value: 'BUSINESS_CATEGORY_CODE' },
      { label: '数据域英文缩写', value: 'DATA_DOMAIN_CODE' },
      { label: '存储策略英文缩写', value: 'STORAGE_POLICY_CODE' },
      { label: '维度定义缩写', value: 'DIM_CODE' },
      { label: '自定义', value: 'CUSTOM' },
    ],
    DWS: [
      { label: '业务大类英文缩写', value: 'BUSINESS_CATEGORY_CODE' },
      { label: '数据域英文缩写', value: 'DATA_DOMAIN_CODE' },
      { label: '统计周期', value: 'TIME_PERIOD_CODE' },
      { label: '自定义', value: 'CUSTOM' },
    ],
    DWD: [
      { label: '业务大类英文缩写', value: 'BUSINESS_CATEGORY_CODE' },
      { label: '数据域英文缩写', value: 'DATA_DOMAIN_CODE' },
      { label: '业务过程英文缩写', value: 'BUSINESS_PROCESS_CODE' },
      { label: '存储策略英文缩写', value: 'STORAGE_POLICY_CODE' },
      { label: '自定义', value: 'CUSTOM' },
    ],
  },
  APP: {
    label: '应用层',
    DIM: [
      { label: '业务大类英文缩写', value: 'BUSINESS_CATEGORY_CODE' },
      { label: '维度定义缩写', value: 'DIM_CODE' },
      { label: '数据集市英文缩写', value: 'DATA_MART_CODE' },
      { label: '主题域英文缩写', value: 'DATA_SUBJECT_CODE' },
      { label: '自定义', value: 'CUSTOM' },
    ],
    ADS: [
      { label: '业务大类英文缩写', value: 'BUSINESS_CATEGORY_CODE' },
      { label: '数据集市英文缩写', value: 'DATA_MART_CODE' },
      { label: '主题域英文缩写', value: 'DATA_SUBJECT_CODE' },
      { label: '统计周期', value: 'TIME_PERIOD_CODE' },
      { label: '自定义', value: 'CUSTOM' },
    ],
  },
  INDICATOR: {
    lable: '指标',
    list: [
      { label: '修饰词', value: 'ADJUNCT' },
      { label: '原子指标', value: 'ATOMIC_INDICATOR' },
      { label: '统计周期', value: 'TIME_PERIOD_CODE' },
    ],
  },
};
