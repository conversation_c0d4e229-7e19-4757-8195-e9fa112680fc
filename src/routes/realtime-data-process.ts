import { Icon } from './icon';

export const basePathname = '/data-dev/dev/data-process';
export const storagePathname = `${basePathname}/online/storage`;

const route: RouteItem[] = [
  // 实时处理下的创建入口页面
  {
    title: '创建数据处理',
    exact: true,
    path: `${basePathname}/online/index`,
    component: '@/pages/DataProcess/online/DataProcessIndex',
    icon: Icon({ name: 'build-fill', className: 'text-primary' }),
  },
  // 实时处理下创建的管线作业都是流作业
  {
    title: '创建管线作业',
    exact: true,
    path: `${basePathname}/online/pipeline/create/:businessFlowId`,
    component: '@/pages/DataProcess/online/CreateStreamingPipeline',
    icon: Icon({ name: 'pipelinei-line', className: 'text-primary' }),
  },
  {
    title: '编辑管线作业',
    exact: true,
    path: `${basePathname}/online/pipeline/edit/:id`,
    component: '@/pages/DataProcess/online/CreateStreamingPipeline',
    icon: Icon({ name: 'pipelinei-line', className: 'text-primary' }),
  },
  {
    title: '克隆管线作业',
    exact: true,
    path: `${basePathname}/online/pipeline/clone/:cloneId`,
    component: '@/pages/DataProcess/online/CreateStreamingPipeline',
    icon: Icon({ name: 'pipelinei-line', className: 'text-primary' }),
  },
  {
    title: '创建SQL作业',
    exact: true,
    path: `${basePathname}/online/flink-sql/create/:businessFlowId`,
    component: '@/pages/DataProcess/online/FlinkSQL',
    icon: Icon({ name: 'sql-line', className: 'text-primary' }),
  },
  {
    title: '编辑SQL作业',
    exact: true,
    path: `${basePathname}/online/flink-sql/edit/:id`,
    component: '@/pages/DataProcess/online/FlinkSQL',
    icon: Icon({ name: 'sql-line', className: 'text-primary' }),
  },
  {
    title: '克隆SQL作业',
    exact: true,
    path: `${basePathname}/online/flink-sql/clone/:cloneId`,
    component: '@/pages/DataProcess/online/FlinkSQL',
    icon: Icon({ name: 'sql-line', className: 'text-primary' }),
  },
  {
    title: '创建Hive存储作业',
    exact: true,
    path: `${basePathname}/online/hive/create/:businessFlowId`,
    component: '@/pages/DataProcess/online/HiveStorage',
    icon: Icon({ name: 'hive-line', className: 'text-primary' }),
  },
  {
    title: '编辑Hive存储作业',
    exact: true,
    path: `${basePathname}/online/hive/edit/:id`,
    component: '@/pages/DataProcess/online/HiveStorage',
    icon: Icon({ name: 'hive-line', className: 'text-primary' }),
  },
  {
    title: '克隆Hive存储作业',
    exact: true,
    path: `${basePathname}/online/hive/clone/:cloneId`,
    component: '@/pages/DataProcess/online/HiveStorage',
    icon: Icon({ name: 'hive-fill', className: 'text-primary' }),
  },
  {
    title: 'ClickHouse存储作业',
    exact: true,
    path: `${storagePathname}/ck`,
    component: '@/pages/DataProcess/online/Storage',
    icon: Icon({ name: 'ck', className: 'text-primary' }),
  },
  {
    title: 'ClickHouse存储作业',
    exact: true,
    path: `${storagePathname}/ck/:id`,
    component: '@/pages/DataProcess/online/Storage',
    icon: Icon({ name: 'ck', className: 'text-primary' }),
  },
  {
    title: 'ClickHouse存储作业',
    path: `${storagePathname}/clone/ck/:cloneId`,
    component: '@/pages/DataProcess/online/Storage',
    icon: Icon({ name: 'ck', className: 'text-primary' }),
  },
  {
    title: 'ElasticSearch存储作业',
    exact: true,
    path: `${storagePathname}/es`,
    component: '@/pages/DataProcess/online/Storage',
    icon: Icon({ name: 'es', className: 'text-primary' }),
  },
  {
    title: 'ElasticSearch存储作业',
    exact: true,
    path: `${storagePathname}/es/:id`,
    component: '@/pages/DataProcess/online/Storage',
    icon: Icon({ name: 'es', className: 'text-primary' }),
  },
  {
    title: 'ElasticSearch存储作业',
    path: `${storagePathname}/clone/es/:cloneId`,
    component: '@/pages/DataProcess/online/Storage',
    icon: Icon({ name: 'es', className: 'text-primary' }),
  },
  {
    title: '创建Flink自定义作业',
    exact: true,
    path: `${basePathname}/online/flink-custom/create/:businessFlowId`,
    component: '@/pages/DataProcess/online/FlinkCustom',
    icon: Icon({ name: 'flink-line', className: 'text-primary' }),
  },
  {
    title: '编辑Flink自定义作业',
    exact: true,
    path: `${basePathname}/online/flink-custom/edit/:id`,
    component: '@/pages/DataProcess/online/FlinkCustom',
    icon: Icon({ name: 'flink-line', className: 'text-primary' }),
  },
  {
    title: '克隆Flink自定义作业',
    exact: true,
    path: `${basePathname}/online/flink-custom/clone/:cloneId`,
    component: '@/pages/DataProcess/online/FlinkCustom',
    icon: Icon({ name: 'flink-line', className: 'text-primary' }),
  },
];

export default route;
