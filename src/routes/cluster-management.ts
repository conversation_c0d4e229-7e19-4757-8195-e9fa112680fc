import flinkYarnSession from './flink-yarn-session';
import storageManagementCK from './storage-management-ck';

const baseURl = '/platform-management/cluster';

export const basePathname = '/platform-management/cluster/management';

// 集群管理
const route: RouteItem = {
  title: '集群管理',
  exact: true,
  path: baseURl,
  useChildren: true,
  routes: [
    {
      title: '集群管理',
      exact: true,
      path: basePathname,
      useChildren: true,
      routes: [
        {
          title: '集群管理',
          exact: true,
          path: `${basePathname}/list`,
          component: '@/pages/Cluster/list',
        },

        {
          title: '创建集群',
          exact: true,
          path: `${basePathname}/create`,
          component: '@/pages/Cluster/create',
        },
        {
          title: '编辑集群',
          exact: true,
          path: `${basePathname}/edit/:id`,
          component: '@/pages/Cluster/create',
        },
        {
          title: '克隆集群',
          exact: true,
          path: `${basePathname}/clone/:cloneId`,
          component: '@/pages/Cluster/create',
        },
        {
          title: '配置文件',
          exact: true,
          path: `${basePathname}/files/:id`,
          component: '@/pages/Cluster/files',
        },
      ],
    },
    storageManagementCK,
    flinkYarnSession,
  ],
};

export default route;
