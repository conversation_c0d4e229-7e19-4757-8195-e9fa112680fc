export const basePathname: string = '/data-modeling/warehouse-plan/wh-layer';

const routes: RouteItem = {
  title: '公共层',
  exact: true,
  path: basePathname,
  routes: [
    {
      title: '数据域',
      path: `${basePathname}/data-domain/index`,
      component: '@/pages/WhLayer/DataDomain/Index',
    },
    {
      title: '数据域详情',
      path: `${basePathname}/data-domain/detail/:id`,
      component: '@/pages/WhLayer/DataDomain/Detail',
    },
    {
      title: '业务过程',
      path: `${basePathname}/business-process/index`,
      component: '@/pages/WhLayer/BusinessProcess/Index',
    },
    {
      title: '业务过程详情',
      path: `${basePathname}/business-process/detail/:id`,
      component: '@/pages/WhLayer/BusinessProcess/Detail',
    },
  ],
};

export default routes;
