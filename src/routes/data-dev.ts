import DataIngestionRoutes from './data-ingestion';
import DataIngestionOfflineRoutes from './data-ingestion-offline';
import { Icon } from './icon';
import OfflineDataProcessRoutes from './offline-data-process';
import RealtimeDataProcessRoutes from './realtime-data-process';

const route: RouteItem = {
  title: '数据开发',
  exact: true,
  path: '/data-dev',
  component: '@/layouts/Sidebar',
  useChildren: true,
  routes: [
    {
      title: '数据开发',
      exact: true,
      path: '/data-dev/dev',
      component: '@/modules/DataDev/layouts',
      useChildren: true,
      routes: [
        {
          title: '数据开发',
          exact: true,
          path: '/data-dev/dev/management',
          component: '@/pages/DataDev',
        },
        {
          title: 'CK发布',
          exact: true,
          path: '/data-dev/dev/management/ck/:id',
          component: '@/pages/DataDev/models/CK',
          icon: Icon({ name: 'ck', className: 'text-[#23C259]' }),
        },
        {
          title: 'ES发布',
          exact: true,
          path: '/data-dev/dev/management/es/:id',
          component: '@/pages/DataDev/models/ES',
          icon: Icon({ name: 'es', className: 'text-[#597EF7]' }),
        },
        {
          title: 'Kafka发布',
          exact: true,
          path: '/data-dev/dev/management/kafka/:id',
          component: '@/pages/DataDev/models/Kafka',
          icon: Icon({ name: 'kafka', className: 'text-[#096DD9]' }),
        },
        {
          title: 'Mysql发布',
          exact: true,
          path: '/data-dev/dev/management/mysql/:id',
          component: '@/pages/DataDev/models/Mysql',
          icon: Icon({ name: 'mysql', className: 'text-[#FFB232]' }),
        },
        {
          title: 'Hive发布',
          exact: true,
          path: '/data-dev/dev/management/hive/:id',
          component: '@/pages/DataDev/models/Hive',
          icon: Icon({ name: 'hive-line', className: 'text-[#F25605]' }),
        },
        {
          title: 'Nebula发布',
          exact: true,
          path: '/data-dev/dev/management/nebula/:id',
          component: '@/pages/DataDev/models/Nebula',
          icon: Icon({ name: 'nebulagraph-line', className: 'text-[#40A9FF]' }),
        },
        ...RealtimeDataProcessRoutes,
        ...OfflineDataProcessRoutes,
        ...DataIngestionRoutes,
        ...DataIngestionOfflineRoutes,
      ],
    },
  ],
};

export default route;
