import { Icon } from './icon';

const componentPrefix = '@/pages/DataIngestion';
const onlineComponentPrefix = `${componentPrefix}/online`;
export const pathPrefix = '/data-dev/dev/data-ingestion';
const onlinePathPrefix = `${pathPrefix}/online`;

const sharedConf = {
  exact: true,
};

const route: RouteItem[] = [
  {
    ...sharedConf,
    title: '创建数据集成',
    path: `${onlinePathPrefix}/create/:businessFlowId`,
    icon: Icon({ name: 'build-fill', className: 'text-primary' }),
    component: `${onlineComponentPrefix}/Create`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/file`,
    icon: Icon({ name: 'file-line' }),
    component: `${onlineComponentPrefix}/FileSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/file/:id`,
    icon: Icon({ name: 'file-line' }),
    component: `${onlineComponentPrefix}/FileSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/clone/file/:cloneId`,
    icon: Icon({ name: 'file-line' }),
    component: `${onlineComponentPrefix}/FileSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/tcp-udp`,
    icon: Icon({ name: 'tcp-line' }),
    component: `${onlineComponentPrefix}/TcpUdpSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/tcp-udp/:id`,
    icon: Icon({ name: 'tcp-line' }),
    component: `${onlineComponentPrefix}/TcpUdpSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/clone/tcp-udp/:cloneId`,
    icon: Icon({ name: 'tcp-line' }),
    component: `${onlineComponentPrefix}/TcpUdpSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/kafka`,
    icon: Icon({ name: 'kafka' }),
    component: `${onlineComponentPrefix}/KafkaSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/kafka/:id`,
    icon: Icon({ name: 'kafka' }),
    component: `${onlineComponentPrefix}/KafkaSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/clone/kafka/:cloneId`,
    icon: Icon({ name: 'kafka' }),
    component: `${onlineComponentPrefix}/KafkaSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/syslog`,
    icon: Icon({ name: 'log-line' }),
    component: `${onlineComponentPrefix}/SyslogSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/syslog/:id`,
    icon: Icon({ name: 'log-line' }),
    component: `${onlineComponentPrefix}/SyslogSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/clone/syslog/:cloneId`,
    icon: Icon({ name: 'log-line' }),
    component: `${onlineComponentPrefix}/SyslogSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/archive`,
    icon: Icon({ name: 'zip-line' }),
    component: `${onlineComponentPrefix}/ArchiveSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/archive/:id`,
    icon: Icon({ name: 'zip-line' }),
    component: `${onlineComponentPrefix}/ArchiveSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/clone/archive/:cloneId`,
    icon: Icon({ name: 'zip-line' }),
    component: `${onlineComponentPrefix}/ArchiveSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/jdbc`,
    icon: Icon({ name: 'data-line' }),
    component: `${onlineComponentPrefix}/JdbcSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/jdbc/:id`,
    icon: Icon({ name: 'data-line' }),
    component: `${onlineComponentPrefix}/JdbcSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/clone/jdbc/:cloneId`,
    icon: Icon({ name: 'data-line' }),
    component: `${onlineComponentPrefix}/JdbcSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/logstash`,
    icon: Icon({ name: 'logstash', className: 'font-bold' }),
    component: `${onlineComponentPrefix}/LogstashSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/logstash/:id`,
    icon: Icon({ name: 'logstash', className: 'font-bold' }),
    component: `${onlineComponentPrefix}/LogstashSync`,
  },
  {
    ...sharedConf,
    path: `${onlinePathPrefix}/clone/logstash/:cloneId`,
    icon: Icon({ name: 'logstash', className: 'font-bold' }),
    component: `${onlineComponentPrefix}/LogstashSync`,
  },
];

export default route;
