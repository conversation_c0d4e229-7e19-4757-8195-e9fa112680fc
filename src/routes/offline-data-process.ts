import { Icon } from './icon';

export const basePathname = '/data-dev/dev/data-process';
export const storagePathname = `${basePathname}/online/storage`;

const route: RouteItem[] = [
  // 实时处理下的创建入口页面
  {
    title: '创建数据处理',
    exact: true,
    path: `${basePathname}/offline/index`,
    component: '@/pages/DataProcess/offline/index',
    icon: Icon({ name: 'build-fill', className: 'text-warning' }),
  },
  // 离线任务下创建的管线作业都是批作业
  {
    title: '创建管线作业',
    exact: true,
    path: `${basePathname}/batch-process/create`,
    component: '@/pages/DataProcess/offline/batch-process',
    icon: Icon({ name: 'pipelinei-line', className: 'text-warning' }),
  },
  {
    title: '编辑管线作业',
    exact: true,
    path: `${basePathname}/batch-process/edit/:id`,
    component: '@/pages/DataProcess/offline/batch-process',
    icon: Icon({ name: 'pipelinei-line', className: 'text-warning' }),
  },
  {
    title: '克隆管线作业',
    exact: true,
    path: `${basePathname}/batch-process/clone/:cloneId`,
    component: '@/pages/DataProcess/offline/batch-process',
    icon: Icon({ name: 'pipelinei-line', className: 'text-warning' }),
  },
  // 离线工作流
  {
    title: '创建工作流',
    exact: true,
    path: `${basePathname}/bpm-process/create`,
    component: '@/pages/DataProcess/offline/bpm-process',
    icon: Icon({ name: 'out-line', className: 'text-warning' }),
  },
  {
    title: '编辑工作流',
    exact: true,
    path: `${basePathname}/bpm-process/edit/:id`,
    component: '@/pages/DataProcess/offline/bpm-process',
    icon: Icon({ name: 'out-line', className: 'text-warning' }),
  },
  {
    title: '克隆工作流',
    exact: true,
    path: `${basePathname}/bpm-process/clone/:cloneId`,
    component: '@/pages/DataProcess/offline/bpm-process',
    icon: Icon({ name: 'out-line', className: 'text-warning' }),
  },
  {
    title: '工作流运行实例',
    exact: true,
    path: `${basePathname}/bpm-process/test/:instanceId`,
    component: '@/pages/DataProcess/offline/bpm-process-test',
    icon: Icon({ name: 'out-line', className: 'text-warning' }),
  },
  {
    title: '资源包详情',
    exact: true,
    path: `${basePathname}/resource/:id`,
    component: '@/pages/DataProcess/offline/resource',
    icon: Icon({ name: 'zip-line', className: 'text-warning' }),
  },
];

export default route;
