import AlgorithmLabRoutes from './algorithm-lab';
import componentsTest from './components-test';
import dataDev from './data-dev';
import dataIndicator from './data-indicator';
import DataProperty from './data-property';
import DataQualityRoutes from './data-quality';
import dataServiceRoutes from './data-service';
import dataStandard from './data-standard';
import fileexport from './file-export';
import flexibleApplication from './flexible-application';
import { Icon } from './icon';
import importList from './importList';
import jobTiming from './job-timing';
import LifeCycleRoutes from './life-cycle';
import ObjectModelingRoute from './object-modeling';
import opsManagement from './ops-management';
import platformManagement from './platform-management';
import PropertyOverview from './property-overview';
import Rightsmanagement from './rights-management';
import systemConfigRoutes from './system-config';
import TroubleShootingRoutes from './trouble-shooting';
import versionCompare from './version-compare';
import warehouseLayerRoutes from './warehouse-layer';
import whLayerRoutes from './wh-layer';
import WorkerGroupRoutes from './worker-group';

export * from './platform-management';

const routes = [
  ...componentsTest,
  {
    title: '配置中心',
    exact: true,
    path: '/config-center',
    component: '@/pages/ExternalPage',
  },
  {
    title: '',
    exact: true,
    path: '/external-page',
    component: 'ExternalPage',
  },
  {
    title: '',
    exact: true,
    path: '/sidebar',
    component: '@/layouts/SidebarHasBreadcrumb',
    useChildren: true,
    routes: [
      {
        title: '',
        exact: true,
        path: '/sidebar/external-page',
        component: 'ExternalPage',
      },
    ],
  },
  {
    title: '首页',
    exact: true,
    path: '/',
    component: '@/modules/Overview',
  },
  {
    title: '暂无项目',
    exact: true,
    path: '/no-project',
    component: '@/pages/NoProject',
  },
  {
    title: '权限管理',
    exact: true,
    path: '/auth',
    component: '@/modules/Auth/pages/MenuAuth',
  },
  {
    // `/job/doc?display=${display}&jobName=${jobName}&docUrl=${encodeURIComponent(docUrl)}`
    title: '算子文档',
    exact: true,
    path: '/job/doc',
    component: '@/modules/JobDocViewer/',
  },
  {
    title: 'logstash插件文档',
    exact: true,
    path: '/data-dev/logstash-document',
    component: '@/modules/DataIngestion/pages/online/Logstash/PluginDocument',
  },
  {
    title: '数据建模',
    exact: true,
    path: '/data-modeling',
    useChildren: true,
    icon: Icon({ name: 'application-line', size: 16 }),
    routes: [
      {
        title: '依赖关系',
        exact: true,
        path: '/data-modeling/dependencies',
        component: 'Dependencies',
      },
      {
        title: '数仓规划',
        exact: true,
        path: '/data-modeling/warehouse-plan',
        component: '@/layouts/SidebarHasBreadcrumb',
        useChildren: true,
        routes: [
          {
            title: '业务分类',
            exact: true,
            path: '/data-modeling/warehouse-plan/business-category',
            component: 'BusinessCategory',
          },
          {
            title: '业务分类',
            exact: true,
            path: '/data-modeling/warehouse-plan/business-category/:id',
            component: 'BusinessCategory',
          },
          // 数仓分层
          ...warehouseLayerRoutes,
          whLayerRoutes,
          {
            title: '应用层',
            exact: true,
            path: '/data-modeling/warehouse-plan/app-layer',
            routes: [
              {
                title: '数据集市',
                exact: true,
                path: '/data-modeling/warehouse-plan/app-layer/data-mart',
                component: 'AppLayer/DataMart',
              },
              {
                title: '数据集市',
                exact: true,
                path: '/data-modeling/warehouse-plan/app-layer/data-mart/:id',
                component: 'AppLayer/DataMart',
              },
              {
                title: '主题域',
                exact: true,
                path: '/data-modeling/warehouse-plan/app-layer/mart-subject',
                component: 'AppLayer/MartSubject',
              },
              {
                title: '主题域',
                exact: true,
                path: '/data-modeling/warehouse-plan/app-layer/mart-subject/:id',
                component: 'AppLayer/MartSubject',
              },
            ],
          },
        ],
      },
      dataStandard,
      dataIndicator, // 数据指标
    ],
  },
  ...ObjectModelingRoute,
  platformManagement,
  ...flexibleApplication,
  WorkerGroupRoutes,
  jobTiming,
  dataDev,
  ...versionCompare,
  importList,
  fileexport,
  ...opsManagement,
  DataQualityRoutes, // 数据质量管理
  ...dataServiceRoutes,
  systemConfigRoutes, // 系统管理
  Rightsmanagement, // 权限管理
  ...AlgorithmLabRoutes,
  TroubleShootingRoutes,
  DataProperty,
  LifeCycleRoutes,
  PropertyOverview,
  { title: '404 not found', path: '*', component: '404' },
];

export default routes;
