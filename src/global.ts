import { autoFixContext } from 'react-activation';
import dayjs from 'dayjs';
import quarter from 'dayjs/plugin/quarterOfYear';
import timezone from 'dayjs/plugin/timezone';
import updateLocale from 'dayjs/plugin/updateLocale';
import utc from 'dayjs/plugin/utc';

import './detectFeatures';
// import '../config/mock'
import './interceptors';
import 'dayjs/locale/zh-cn';
import 'reflect-metadata';

import { serviceName } from '../package.json';

import { PROJECT } from './constants';

dayjs.extend(quarter);
dayjs.extend(updateLocale);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.updateLocale('zh-cn', {
  weekStart: 0,
  timeZone: 'Asia/Shanghai',
});
autoFixContext(
  [require('react/jsx-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
  [require('react/jsx-dev-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
);

// 重写window.open，解决未拼接统一门户前缀问题
window.open = (function (_open) {
  return function () {
    const url = arguments[0] as string;
    if (url && serviceName && !url.includes(PROJECT.baseUrl) && url.startsWith('/')) {
      console.warn('未拼接统一门户前缀，已自动添加');
      arguments[0] = PROJECT.baseUrl + url;
    }
    return _open.apply(this, arguments);
  };
}(window.open));
