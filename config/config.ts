import getRepoInfo from 'git-cmd-repo-info';
import { defineConfig } from 'umi';

import { displayName, name, serviceName, version } from '../package.json';
import { systemName } from '../public/config/index.js';
import routes from '../src/routes';

const repoInfo = getRepoInfo();

const isDev = process?.env?.NODE_ENV === 'development';
const baseUrl = serviceName ? `/${serviceName}` : '';
const envMap = {
  dev: serviceName ? '192.168.110.175:8080' : '192.168.110.175:9901',
  // dev: '192.168.110.175:9900',
  // dev: '192.168.110.77:9903',
  rest: '192.168.110.175:8080',
};
export default defineConfig({
  publicPath: serviceName ? `/${serviceName}/` : '/',
  base: serviceName ? `/${serviceName}/` : '/',
  extraPostCSSPlugins: [require('tailwindcss'), require('autoprefixer')],
  npmClient: 'npm',
  title: systemName,
  routes,
  // could save 60% time in dev hotload
  devtool: isDev ? 'source-map' : undefined,
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  targets: {
    chrome: 71,
  },
  hash: true,
  plugins: [require.resolve('./plugin/changeFavicon'), 'umi-plugin-keep-alive'],
  esbuildMinifyIIFE: true,
  proxy: {
    // 代理中台API
    [serviceName ? `/${serviceName}/api` : '/api']: {
      target: `http://${envMap.dev}/`,
      changeOrigin: true,
      // pathRewrite: { [`^/${serviceName}`] : '' }
    },
    // 代理中台Socket服务
    [serviceName ? `/${serviceName}/ws` : '//ws']: {
      target: `http://${envMap.dev}/`,
      ws: true,
      // pathRewrite: { [`^/${serviceName}`] : '' }
    },
    // 代理统一门户API
    [serviceName ? `/${serviceName}/rest` : '/rest']: {
      target: `http://${envMap.rest}/`,
      ws: true,
    },
  },
  define: {
    CLIENT_INFO: getRepoInfo(),
    __PROJECT_NAME__: JSON.stringify(name),
    __SERVICE_NAME__: JSON.stringify(serviceName || null),
    __PROJECT_DISPLAY_NAME__: JSON.stringify(displayName),
    __PROJECT_ENVIRONMENT__: JSON.stringify(process.env.NODE_ENV === 'production' ? 'prod' : 'dev'),
    __PROJECT_COMPILE_TIME__: JSON.stringify(Date.now()),
    __PRD_VERSION__: JSON.stringify(version),
    __PROJECT_COMMIT_SHA__: JSON.stringify(repoInfo.sha),
    __PROJECT_VERSION__: JSON.stringify(repoInfo.lastTag),
    __PROJECT_COMMITS_SINCE_RELEASE__: JSON.stringify(repoInfo.commitsSinceLastTag),
  },
  headScripts: [{ src: `${baseUrl}/config/index.js`, deffer: false }],
  clickToComponent: {},
});
