DEST?=dist
ENV?=dev
JAX_SERVICE_NAME?=""
dev:
	yarn run build || npm run build

clear:
	rm -rf ${DEST}/*

build: clear
	yarn run gulp --serviceName ${JAX_SERVICE_NAME} || npm run gulp --serviceName ${JAX_SERVICE_NAME}
	yarn run build || npm run build
	yarn run gulp --serviceName "" || npm run gulp --serviceName ""

lint:
	yarn run lint-staged || npm run lint-staged

install:
	yarn || npm ci --legacy-peer-deps

# 因gulp命令在ci执行中无法找到gulp，故通过yarn run gulp窒息ing