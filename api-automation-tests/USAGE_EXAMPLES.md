# 使用示例

本文档提供了JAX API自动化测试框架的详细使用示例。

## 快速开始

### 1. 一键启动
```bash
# 运行快速启动脚本
python quick_start.py
```

### 2. 手动设置
```bash
# 安装依赖
pip install -r requirements.txt

# 复制配置文件
cp .env.example .env

# 编辑配置文件
vim .env

# 运行框架验证
python run_tests.py --type framework
```

## 配置示例

### .env 配置文件示例
```bash
# API基础配置
BASE_URL=http://localhost:8080
API_VERSION=v2

# 认证配置（根据实际情况配置）
TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# 测试配置
TIMEOUT=30
LOG_LEVEL=INFO
CLEANUP_AFTER_TEST=true
GENERATE_TEST_REPORT=true
```

## 运行测试示例

### 1. 使用运行脚本

```bash
# 运行所有测试
python run_tests.py

# 运行特定模块测试
python run_tests.py --type tag
python run_tests.py --type datacenter

# 并行执行
python run_tests.py --parallel

# 生成报告
python run_tests.py --report

# 详细输出
python run_tests.py --verbose

# 组合使用
python run_tests.py --type tag --parallel --verbose --report
```

### 2. 使用pytest直接运行

```bash
# 运行所有测试
pytest

# 运行特定文件
pytest tests/test_tag_api.py
pytest tests/test_data_center_api.py

# 运行特定测试类
pytest tests/test_tag_api.py::TestTagAPI

# 运行特定测试方法
pytest tests/test_tag_api.py::TestTagAPI::test_create_tag_success

# 使用标记过滤
pytest -m smoke
pytest -m "crud and not slow"

# 并行执行
pytest -n auto

# 详细输出
pytest -v -s

# 生成HTML报告
pytest --html=reports/report.html --self-contained-html

# 生成Allure报告
pytest --alluredir=reports/allure-results
allure generate reports/allure-results -o reports/allure-report --clean
allure open reports/allure-report
```



## 测试用例示例

### 1. 基本CRUD测试

```python
def test_tag_crud_flow(self, test_helper):
    """测试Tag完整CRUD流程"""
    # 1. 创建
    create_data = {"name": "test_tag", "tagType": "AGENT"}
    create_response = tag_service.create_tag(create_data["name"], create_data["tagType"])
    test_helper.assert_success_response(create_response)
    tag_id = tag_service.extract_tag_id(create_response)
    
    # 2. 查询
    get_response = tag_service.get_tag_by_id(tag_id)
    test_helper.assert_success_response(get_response)
    
    # 3. 更新
    update_response = tag_service.update_tag(tag_id, "updated_name")
    test_helper.assert_success_response(update_response)
    
    # 4. 删除
    delete_response = tag_service.delete_tag(tag_id)
    test_helper.assert_success_response(delete_response)
```

### 2. 参数化测试

```python
@pytest.mark.parametrize("tag_type", ["AGENT", "TABLE", "PIPELINE"])
def test_create_tag_with_different_types(self, tag_type, test_helper):
    """测试创建不同类型的标签"""
    response = tag_service.create_tag(f"test_{tag_type.lower()}", tag_type)
    test_helper.assert_success_response(response)
```

### 3. 错误处理测试

```python
@pytest.mark.parametrize("invalid_data", [
    {"name": "", "tagType": "AGENT"},
    {"name": "test", "tagType": "INVALID"},
    {"name": None, "tagType": "AGENT"}
])
def test_create_tag_with_invalid_data(self, invalid_data, test_helper):
    """测试无效数据创建标签"""
    response = tag_service.create_tag(invalid_data["name"], invalid_data["tagType"])
    test_helper.assert_error_response(response)
```

## 自定义测试示例

### 1. 创建新的API服务

```python
# services/custom_service.py
from services.base_service import BaseAPIService

class CustomService(BaseAPIService):
    def __init__(self):
        super().__init__("Custom")
    
    def get_endpoints(self):
        return {
            "create": "ingestion/custom",
            "get": "ingestion/custom/{id}",
            "update": "ingestion/custom/{id}",
            "delete": "ingestion/custom/{id}",
            "list": "ingestion/custom/list",
            "query": "ingestion/custom/query"
        }
```

### 2. 创建测试用例

```python
# tests/test_custom_api.py
import pytest
import allure
from services.custom_service import CustomService

@allure.feature("自定义API")
class TestCustomAPI:
    def test_create_custom_resource(self, test_helper):
        """测试创建自定义资源"""
        service = CustomService()
        data = {"name": "test_custom", "type": "custom"}
        response = service.create(data)
        test_helper.assert_success_response(response)
```

## 报告示例

### 1. HTML报告
运行测试后，在 `reports/report.html` 查看详细的HTML测试报告。

### 2. Allure报告
```bash
# 生成Allure报告
pytest --alluredir=reports/allure-results
allure generate reports/allure-results -o reports/allure-report --clean

# 启动报告服务器
allure open reports/allure-report
```

### 3. 日志文件
- `logs/api_test.log` - 所有日志
- `logs/api_test_error.log` - 错误日志

## 调试示例

### 1. 启用详细日志
```bash
# 设置环境变量
export LOG_LEVEL=DEBUG

# 运行测试
python run_tests.py --verbose
```

### 2. 单独调试失败的测试
```bash
# 运行单个测试并显示详细输出
pytest tests/test_tag_api.py::TestTagAPI::test_create_tag_success -v -s

# 进入调试模式
pytest tests/test_tag_api.py::TestTagAPI::test_create_tag_success --pdb
```

### 3. 查看HTTP请求详情
所有HTTP请求和响应都会记录在日志中，包括：
- 请求URL和方法
- 请求头和请求体
- 响应状态码和响应体
- 请求耗时



## 性能测试示例

### 1. 并发测试
```python
import concurrent.futures
import time

def test_concurrent_requests(self):
    """测试并发请求"""
    def create_tag(index):
        return tag_service.create_tag(f"concurrent_tag_{index}", "AGENT")
    
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(create_tag, i) for i in range(100)]
        results = [future.result() for future in futures]
    
    duration = time.time() - start_time
    success_count = sum(1 for r in results if tag_service.is_success_response(r))
    
    logger.info(f"并发测试完成: {success_count}/100 成功, 耗时: {duration:.2f}s")
```

### 2. 压力测试
```python
@pytest.mark.performance
def test_stress_testing(self):
    """压力测试"""
    for batch in range(10):
        batch_start = time.time()
        for i in range(50):
            response = tag_service.create_tag(f"stress_tag_{batch}_{i}", "AGENT")
            assert tag_service.is_success_response(response)
        
        batch_duration = time.time() - batch_start
        logger.info(f"批次 {batch}: 50个请求耗时 {batch_duration:.2f}s")
```

## 故障排除示例

### 1. 常见错误及解决方案

```bash
# 连接超时
# 解决：检查BASE_URL配置，确认API服务状态
curl http://localhost:8080/api/v2/ingestion/tag/list

# 认证失败
# 解决：检查TOKEN配置
export TOKEN="your_jwt_token"

# 依赖包冲突
# 解决：重新安装依赖
pip install -r requirements.txt --force-reinstall

# 测试数据冲突
# 解决：启用自动清理
export CLEANUP_AFTER_TEST=true
```

### 2. 调试技巧

```python
# 在测试中添加调试信息
def test_debug_example(self):
    response = tag_service.create_tag("debug_tag", "AGENT")
    
    # 打印响应详情
    print(f"Response: {response}")
    
    # 使用断点调试
    import pdb; pdb.set_trace()
    
    # 记录详细日志
    logger.debug(f"Response details: {response}")
```

这些示例涵盖了框架的主要使用场景，可以根据实际需求进行调整和扩展。
