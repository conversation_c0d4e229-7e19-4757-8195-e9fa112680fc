"""
响应模型定义
"""
from typing import Any, Dict, List, Optional, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class BaseResponse(BaseModel, Generic[T]):
    """基础响应模型"""
    code: str = Field(description="业务响应码")
    message: str = Field(description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据")
    timestamp: Optional[int] = Field(default=None, description="时间戳")


class PagedResponse(BaseModel, Generic[T]):
    """分页响应模型 - 当data包含分页信息时使用"""
    total: int = Field(description="总数")
    data: List[T] = Field(description="数据列表")  # 支持list别名
    page: Optional[int] = Field(default=None, description="当前页")
    size: Optional[int] = Field(default=None, description="页大小")

    class Config:
        populate_by_name = True  # 允许使用字段名和别名


class TagModel(BaseModel):
    """Tag模型"""
    id: Optional[int] = Field(default=None, description="ID")
    name: str = Field(description="标签名称")
    tagType: str = Field(description="标签类型")
    createTime: Optional[str] = Field(default=None, description="创建时间")
    updateTime: Optional[str] = Field(default=None, description="更新时间")
    createUser: Optional[int] = Field(default=None, description="创建用户")
    updateUser: Optional[int] = Field(default=None, description="更新用户")


class DataCenterModel(BaseModel):
    """DataCenter模型"""
    id: Optional[int] = Field(default=None, description="ID")
    name: str = Field(description="数据中心名称")
    description: Optional[str] = Field(default=None, description="描述")
    createTime: Optional[str] = Field(default=None, description="创建时间")
    updateTime: Optional[str] = Field(default=None, description="更新时间")
    createUser: Optional[int] = Field(default=None, description="创建用户")
    updateUser: Optional[int] = Field(default=None, description="更新用户")


class ClusterModel(BaseModel):
    """Cluster模型"""
    id: Optional[int] = Field(default=None, description="ID")
    clusterName: str = Field(description="集群名称")
    clusterType: str = Field(description="集群类型")
    clusterDescription: Optional[str] = Field(default=None, description="集群描述")
    defaultFlinkCluster: Optional[bool] = Field(default=False, description="是否默认Flink集群")
    defaultSparkCluster: Optional[bool] = Field(default=False, description="是否默认Spark集群")
    defaultMarayarnCluster: Optional[bool] = Field(default=False, description="是否默认Marayarn集群")
    setting: Optional[Dict[str, Any]] = Field(default=None, description="集群设置")
    createTime: Optional[str] = Field(default=None, description="创建时间")
    updateTime: Optional[str] = Field(default=None, description="更新时间")


class UsageModel(BaseModel):
    """使用情况模型"""
    id: Optional[int] = Field(default=None, description="ID")
    name: str = Field(description="名称")
    type: str = Field(description="类型")
    description: Optional[str] = Field(default=None, description="描述")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    code: str = Field(description="业务错误码")
    message: str = Field(description="错误消息")
    data: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    timestamp: Optional[int] = Field(default=None, description="时间戳")


# 类型别名
TagResponse = BaseResponse[TagModel]
TagListResponse = BaseResponse[List[TagModel]]
TagPagedResponse = BaseResponse[PagedResponse[TagModel]]

DataCenterResponse = BaseResponse[DataCenterModel]
DataCenterListResponse = BaseResponse[List[DataCenterModel]]
DataCenterPagedResponse = BaseResponse[PagedResponse[DataCenterModel]]

ClusterResponse = BaseResponse[ClusterModel]
ClusterListResponse = BaseResponse[List[ClusterModel]]
ClusterPagedResponse = BaseResponse[PagedResponse[ClusterModel]]

UsageListResponse = BaseResponse[List[UsageModel]]
