"""
请求模型定义
"""
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class BaseQueryRequest(BaseModel):
    """基础查询请求模型"""
    page: int = Field(default=1, description="页码")
    size: int = Field(default=10, description="页大小")
    filter: Dict[str, Any] = Field(default_factory=dict, description="过滤条件")
    sort: Dict[str, str] = Field(default_factory=dict, description="排序条件")


class TagCreateRequest(BaseModel):
    """Tag创建请求模型"""
    name: str = Field(description="标签名称")
    tagType: str = Field(description="标签类型")


class TagUpdateRequest(BaseModel):
    """Tag更新请求模型"""
    name: str = Field(description="标签名称")


class TagQueryRequest(BaseQueryRequest):
    """Tag查询请求模型"""
    filter: Dict[str, Any] = Field(
        default_factory=lambda: {
            "name": None,
            "tagType": None,
            "idList": None
        },
        description="过滤条件"
    )
    sort: Dict[str, str] = Field(
        default_factory=lambda: {
            "updateTime": "desc"
        },
        description="排序条件"
    )


class DataCenterCreateRequest(BaseModel):
    """DataCenter创建请求模型"""
    name: str = Field(description="数据中心名称")
    description: Optional[str] = Field(default=None, description="描述")


class DataCenterUpdateRequest(BaseModel):
    """DataCenter更新请求模型"""
    name: str = Field(description="数据中心名称")
    description: Optional[str] = Field(default=None, description="描述")


class DataCenterQueryRequest(BaseQueryRequest):
    """DataCenter查询请求模型"""
    filter: Dict[str, Any] = Field(
        default_factory=lambda: {
            "name": None
        },
        description="过滤条件"
    )
    sort: Dict[str, str] = Field(
        default_factory=lambda: {
            "createTime": "desc",
            "updateTime": "desc"
        },
        description="排序条件"
    )


class DataCenterReferenceRequest(BaseModel):
    """DataCenter关联查询请求模型"""
    dataCenterId: int = Field(description="数据中心ID")
    type: Optional[str] = Field(default=None, description="关联类型")


class ClusterCreateRequest(BaseModel):
    """Cluster创建请求模型"""
    clusterName: str = Field(description="集群名称")
    clusterType: str = Field(description="集群类型")
    clusterDescription: Optional[str] = Field(default=None, description="集群描述")
    defaultFlinkCluster: Optional[bool] = Field(default=False, description="是否默认Flink集群")
    defaultSparkCluster: Optional[bool] = Field(default=False, description="是否默认Spark集群")
    defaultMarayarnCluster: Optional[bool] = Field(default=False, description="是否默认Marayarn集群")
    flinkOptsId: Optional[int] = Field(default=None, description="Flink配置ID")
    sparkOptsId: Optional[int] = Field(default=None, description="Spark配置ID")
    marayarnOptsId: Optional[int] = Field(default=None, description="Marayarn配置ID")
    setting: Optional[Dict[str, Any]] = Field(default=None, description="集群设置")
    dataCenterIds: Optional[List[int]] = Field(default=None, description="数据中心ID列表")


class ClusterUpdateRequest(BaseModel):
    """Cluster更新请求模型"""
    clusterDescription: Optional[str] = Field(default=None, description="集群描述")
    defaultFlinkCluster: Optional[bool] = Field(default=None, description="是否默认Flink集群")
    defaultSparkCluster: Optional[bool] = Field(default=None, description="是否默认Spark集群")
    defaultMarayarnCluster: Optional[bool] = Field(default=None, description="是否默认Marayarn集群")
    flinkOptsId: Optional[int] = Field(default=None, description="Flink配置ID")
    sparkOptsId: Optional[int] = Field(default=None, description="Spark配置ID")
    marayarnOptsId: Optional[int] = Field(default=None, description="Marayarn配置ID")
    setting: Optional[Dict[str, Any]] = Field(default=None, description="集群设置")
    dataCenterIds: Optional[List[int]] = Field(default=None, description="数据中心ID列表")


class ClusterQueryRequest(BaseQueryRequest):
    """Cluster查询请求模型"""
    filter: Dict[str, Any] = Field(
        default_factory=lambda: {
            "clusterName": None,
            "clusterType": None
        },
        description="过滤条件"
    )
    sort: Dict[str, str] = Field(
        default_factory=lambda: {
            "createTime": "desc"
        },
        description="排序条件"
    )
