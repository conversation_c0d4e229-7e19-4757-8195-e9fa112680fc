# JAX API 自动化测试框架

基于Python 3.10的API接口自动化测试框架，专门针对JAX项目的ingestion模块进行测试。

## 功能特性

### 支持的6种标准CRUD接口测试
1. **分页查询** - POST `/api/v2/ingestion/{resource}/query`
2. **List查询** - GET `/api/v2/ingestion/{resource}/list`
3. **根据ID获取详情** - GET `/api/v2/ingestion/{resource}/{id}`
4. **根据ID更新** - PUT `/api/v2/ingestion/{resource}/{id}`
5. **根据ID删除** - DELETE `/api/v2/ingestion/{resource}/{id}`
6. **POST新建** - POST `/api/v2/ingestion/{resource}`

### 测试覆盖的资源
- **Tag管理** - 标签的完整CRUD操作
- **DataCenter管理** - 数据中心的完整CRUD操作
- **Cluster管理** - 集群的完整CRUD操作（可扩展）

### 测试类型
- **功能测试** - 正常业务流程测试
- **参数验证测试** - 无效参数和边界值测试
- **完整CRUD流程测试** - 端到端业务流程测试
- **框架验证测试** - 确保测试框架本身正常工作

## 项目结构

```
api-automation-tests/
├── config/                 # 配置模块
│   ├── __init__.py
│   └── settings.py         # 测试配置
├── models/                 # 数据模型
│   ├── __init__.py
│   ├── request_models.py   # 请求模型
│   └── response_models.py  # 响应模型
├── services/               # API服务层
│   ├── __init__.py
│   ├── base_service.py     # 基础服务类
│   ├── tag_service.py      # Tag服务
│   └── data_center_service.py  # DataCenter服务
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   ├── http_client.py      # HTTP客户端
│   └── test_data_generator.py  # 测试数据生成器
├── tests/                  # 测试用例
│   ├── __init__.py
│   ├── test_tag_api.py     # Tag API测试
│   ├── test_data_center_api.py  # DataCenter API测试
│   └── test_framework_validation.py  # 框架验证测试
├── logs/                   # 日志目录
├── reports/                # 测试报告目录
├── conftest.py            # pytest配置和fixtures
├── pytest.ini            # pytest配置文件
├── run_tests.py           # 测试运行脚本
├── requirements.txt       # 依赖包
├── .env.example          # 环境变量示例
└── README.md             # 项目说明
```

## 安装和配置

### 1. 环境要求
- Python 3.10+
- pip

### 2. 安装依赖
```bash
cd api-automation-tests
pip install -r requirements.txt
```

### 3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，配置API地址等信息
```

### 4. 环境变量说明
```bash
# API基础配置
BASE_URL=http://localhost:8080    # API服务地址
API_VERSION=v2                    # API版本

# 认证配置（如果需要）
TOKEN=                           # JWT Token

# 测试配置
TIMEOUT=30                       # 请求超时时间
LOG_LEVEL=INFO                   # 日志级别
CLEANUP_AFTER_TEST=true          # 测试后清理数据
GENERATE_TEST_REPORT=true        # 生成测试报告
```

## 使用方法

### 1. 使用运行脚本（推荐）

```bash
# 运行所有测试
python run_tests.py

# 运行特定类型的测试
python run_tests.py --type tag          # 只测试Tag API
python run_tests.py --type datacenter   # 只测试DataCenter API

# 并行执行测试
python run_tests.py --parallel

# 生成测试报告
python run_tests.py --report

# 详细输出
python run_tests.py --verbose

# 组合使用
python run_tests.py --type tag --parallel --verbose --report
```

### 2. 直接使用pytest

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_tag_api.py
pytest tests/test_data_center_api.py

# 运行特定测试类
pytest tests/test_tag_api.py::TestTagAPI

# 运行特定测试方法
pytest tests/test_tag_api.py::TestTagAPI::test_create_tag_success

# 使用标记过滤
pytest -m smoke                 # 运行冒烟测试
pytest -m "not slow"           # 排除慢速测试

# 并行执行
pytest -n auto

# 生成HTML报告
pytest --html=reports/report.html --self-contained-html

# 生成Allure报告
pytest --alluredir=reports/allure-results
allure generate reports/allure-results -o reports/allure-report --clean
allure open reports/allure-report
```

## 测试用例说明

### Tag API测试 (`test_tag_api.py`)
- ✅ 创建标签（正常情况和参数验证）
- ✅ 根据ID获取标签详情
- ✅ 更新标签
- ✅ 删除标签
- ✅ 获取标签列表
- ✅ 根据类型获取标签列表
- ✅ 分页查询标签
- ✅ 获取标签使用情况

### DataCenter API测试 (`test_data_center_api.py`)
- ✅ 创建数据中心（正常情况和参数验证）
- ✅ 根据ID获取数据中心详情
- ✅ 更新数据中心
- ✅ 删除数据中心
- ✅ 获取数据中心列表
- ✅ 分页查询数据中心
- ✅ 按名称查询数据中心
- ✅ 获取数据中心使用情况
- ✅ 查询数据中心关联数据

### 框架验证测试 (`test_framework_validation.py`)
- ✅ 日志功能验证
- ✅ 配置加载验证
- ✅ 数据生成器验证
- ✅ HTTP客户端验证
- ✅ API连通性验证
- ✅ 环境变量验证

## 扩展指南

### 添加新的API资源测试

1. **创建服务类**
```python
# services/new_resource_service.py
from services.base_service import BaseAPIService
from config.settings import api_endpoints

class NewResourceService(BaseAPIService):
    def __init__(self):
        super().__init__("NewResource")
    
    def get_endpoints(self) -> Dict[str, str]:
        return {
            "create": "ingestion/new-resource",
            "get": "ingestion/new-resource/{id}",
            "update": "ingestion/new-resource/{id}",
            "delete": "ingestion/new-resource/{id}",
            "list": "ingestion/new-resource/list",
            "query": "ingestion/new-resource/query"
        }
```

2. **添加测试数据生成器**
```python
# utils/test_data_generator.py
@staticmethod
def generate_new_resource_data():
    return {
        "name": f"test_resource_{TestDataGenerator.generate_timestamp_suffix()}",
        "description": "Test resource description"
    }
```

3. **创建测试用例**
```python
# tests/test_new_resource_api.py
@allure.feature("新资源管理")
class TestNewResourceAPI:
    def test_create_new_resource_success(self, test_helper):
        # 测试实现
        pass
```

### 自定义断言和验证

```python
# conftest.py 或 utils/assertions.py
class CustomAssertions:
    @staticmethod
    def assert_resource_structure(data, required_fields):
        """验证资源数据结构"""
        for field in required_fields:
            assert field in data, f"缺少必需字段: {field}"
    
    @staticmethod
    def assert_timestamp_format(timestamp_str):
        """验证时间戳格式"""
        # 实现时间戳格式验证
        pass
```

## 最佳实践

### 1. 测试数据管理
- 使用唯一的测试数据前缀
- 测试后自动清理创建的数据
- 使用fixtures管理测试资源生命周期

### 2. 错误处理
- 对所有API调用进行异常处理
- 记录详细的错误信息
- 区分客户端错误和服务端错误

### 3. 日志记录
- 记录所有API请求和响应
- 使用结构化日志格式
- 区分不同级别的日志信息

### 4. 测试组织
- 按功能模块组织测试用例
- 使用描述性的测试名称
- 添加适当的测试标记

## 故障排除

### 常见问题

1. **连接超时**
   - 检查BASE_URL配置
   - 确认API服务是否启动
   - 调整TIMEOUT设置

2. **认证失败**
   - 检查TOKEN或用户名密码配置
   - 确认认证方式是否正确

3. **测试数据冲突**
   - 启用CLEANUP_AFTER_TEST
   - 使用唯一的测试数据标识

4. **依赖包问题**
   - 确认Python版本为3.10+
   - 重新安装依赖包：`pip install -r requirements.txt --force-reinstall`

### 调试技巧

1. **启用详细日志**
```bash
# 设置环境变量
export LOG_LEVEL=DEBUG
python run_tests.py --verbose
```

2. **单独运行失败的测试**
```bash
pytest tests/test_tag_api.py::TestTagAPI::test_create_tag_success -v -s
```

3. **查看HTTP请求详情**
- 检查logs目录下的日志文件
- 所有HTTP请求和响应都会被记录
