# JAX API 自动化测试框架

基于Python 3.10的API接口自动化测试框架，专门针对JAX项目的ingestion模块进行测试。

## 🎯 功能特性

### 支持的6种标准CRUD接口测试
1. **POST新建** - POST `/api/v2/{resource}`
2. **GET详情** - GET `/api/v2/{resource}/{id}`
3. **PUT更新** - PUT `/api/v2/{resource}/{id}`
4. **DELETE删除** - DELETE `/api/v2/{resource}/{id}`
5. **GET列表** - GET `/api/v2/{resource}/list`
6. **POST分页查询** - POST `/api/v2/{resource}/query`

### 测试覆盖的资源
- **Tag管理** - 标签的完整CRUD操作
- **DataCenter管理** - 数据中心的完整CRUD操作
- **可扩展** - 支持添加更多API资源测试

## 项目结构

```
api-automation-tests/
├── config/                 # 配置模块
│   ├── __init__.py
│   └── settings.py         # 测试配置
├── models/                 # 数据模型
│   ├── __init__.py
│   ├── request_models.py   # 请求模型
│   └── response_models.py  # 响应模型
├── services/               # API服务层
│   ├── __init__.py
│   ├── base_service.py     # 基础服务类
│   ├── tag_service.py      # Tag服务
│   └── data_center_service.py  # DataCenter服务
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   ├── http_client.py      # HTTP客户端
│   └── test_data_generator.py  # 测试数据生成器
├── tests/                  # 测试用例
│   ├── __init__.py
│   ├── test_tag_api.py     # Tag API测试
│   └── test_data_center_api.py  # DataCenter API测试
├── logs/                        # 日志目录（运行时创建）
├── reports/                     # 测试报告目录（运行时创建）
├── conftest.py                  # pytest配置和全局fixtures
├── pytest.ini                  # pytest配置文件
├── run_tests.py                 # 测试运行脚本
├── quick_start.py               # 快速启动脚本
├── requirements.txt             # Python依赖包
├── .env.example                 # 环境变量配置示例
└── README.md                    # 项目说明文档（本文件）
```

## 🚀 快速开始

### 方法一：一键启动（推荐）
```bash
python quick_start.py
```

### 方法二：手动设置
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境
cp .env.example .env
# 编辑.env文件，配置API地址等信息

# 3. 运行测试
python run_tests.py
```

## ⚙️ 配置说明

### 环境要求
- Python 3.10+
- pip

### 环境变量配置
复制`.env.example`为`.env`并根据实际环境修改：

```bash
# API基础配置
BASE_URL=http://localhost:8080    # API服务地址
API_VERSION=v2                    # API版本

# 认证配置（可选）
TOKEN=                           # JWT Token，如果API需要认证请设置

# 测试配置
TIMEOUT=30                       # HTTP请求超时时间（秒）
LOG_LEVEL=INFO                   # 日志级别：DEBUG, INFO, WARNING, ERROR

# 测试数据配置
CLEANUP_AFTER_TEST=true          # 测试完成后是否自动清理创建的数据
GENERATE_TEST_REPORT=true        # 是否生成测试报告
```

## 📖 使用方法

### 支持的测试类型
- `all` - 运行所有测试（默认）
- `tag` - 只运行Tag API测试
- `datacenter` - 只运行DataCenter API测试

### 1. 使用运行脚本（推荐）

```bash
# 运行所有测试
python run_tests.py

# 运行特定类型的测试
python run_tests.py --type tag          # 只测试Tag API
python run_tests.py --type datacenter   # 只测试DataCenter API

# 并行执行测试
python run_tests.py --parallel

# 生成测试报告
python run_tests.py --report

# 详细输出
python run_tests.py --verbose

# 组合使用
python run_tests.py --type tag --parallel --verbose --report
```

### 2. 直接使用pytest

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_tag_api.py
pytest tests/test_data_center_api.py

# 运行特定测试类
pytest tests/test_tag_api.py::TestTagAPI

# 运行特定测试方法
pytest tests/test_tag_api.py::TestTagAPI::test_create_tag

# 使用标记过滤
pytest -m tag                   # 运行Tag API测试
pytest -m datacenter           # 运行DataCenter API测试

# 并行执行
pytest -n auto

# 生成HTML报告
pytest --html=reports/report.html --self-contained-html
```

## 📊 测试报告

### HTML报告
运行测试后，在 `reports/report.html` 查看详细的HTML测试报告。

### 标准输出日志
使用 `python run_tests.py` 运行测试时，测试用例的日志会直接显示在控制台中，方便实时查看测试进度和结果。

### 日志文件
- `logs/api_test.log` - 所有日志
- `logs/api_test_error.log` - 错误日志

## 🎉 总结

这个API自动化测试框架专为JAX项目的ingestion模块设计，具有以下优势：

- ✅ **完整的API测试覆盖** - 支持6种标准CRUD操作
- ✅ **简洁的项目结构** - 易于理解和维护
- ✅ **自动化资源管理** - 无需手动清理测试数据
- ✅ **清晰的测试报告** - HTML格式的详细测试报告
- ✅ **良好的扩展性** - 可以轻松添加新的API测试
- ✅ **完善的错误处理** - 详细的日志和错误信息

框架采用Python 3.10和现代测试工具，提供了高效、可靠的API自动化测试解决方案。

---

**开始使用**: `python quick_start.py`

**运行测试**: `python run_tests.py`

**查看报告**: `reports/report.html`
