"""
pytest配置文件和全局fixtures
"""
import pytest
from typing import Dict, Any, List, Optional
from utils.logger import get_logger
from utils.test_data_generator import test_data_generator
from services.tag_service import tag_service
from services.data_center_service import data_center_service
from config.settings import settings, response_code

logger = get_logger(__name__)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    logger.info("开始设置测试环境")
    logger.info(f"API基础URL: {settings.api_base_url}")
    logger.info(f"测试配置: 超时={settings.TIMEOUT}s")
    yield
    logger.info("测试环境清理完成")


@pytest.fixture(scope="function")
def cleanup_resources():
    """清理测试资源"""
    created_resources = {
        "tags": [],
        "data_centers": []
    }
    
    yield created_resources
    
    # 清理创建的资源
    if settings.CLEANUP_AFTER_TEST:
        logger.info("开始清理测试资源")
        
        # 清理标签
        for tag_id in created_resources["tags"]:
            try:
                tag_service.delete_tag(tag_id)
                logger.info(f"已删除标签 ID: {tag_id}")
            except Exception as e:
                logger.warning(f"删除标签 {tag_id} 失败: {str(e)}")
        
        # 清理数据中心
        for data_center_id in created_resources["data_centers"]:
            try:
                data_center_service.delete_data_center(data_center_id)
                logger.info(f"已删除数据中心 ID: {data_center_id}")
            except Exception as e:
                logger.warning(f"删除数据中心 {data_center_id} 失败: {str(e)}")


@pytest.fixture
def tag_test_data():
    """标签测试数据"""
    return {
        "valid_create_data": test_data_generator.generate_tag_data(),
        "valid_update_data": test_data_generator.generate_tag_update_data(),
        "valid_query_data": test_data_generator.generate_tag_query_data(),
        "invalid_create_data": [
            {},  # 空数据
            {"name": ""},  # 空名称
            {"tagType": "AGENT"},  # 缺少名称
            {"name": "test"},  # 缺少类型
            {"name": "test", "tagType": "INVALID_TYPE"},  # 无效类型
        ],
        "boundary_data": {
            "min_name": "a",
            "max_name": "a" * 100,
            "empty_name": "",
            "null_name": None,
            "long_name": "a" * 256
        }
    }


@pytest.fixture
def data_center_test_data():
    """数据中心测试数据"""
    return {
        "valid_create_data": test_data_generator.generate_data_center_data(),
        "valid_update_data": test_data_generator.generate_data_center_update_data(),
        "valid_query_data": test_data_generator.generate_data_center_query_data(),
        "invalid_create_data": [
            {},  # 空数据
            {"name": ""},  # 空名称
            {"description": "test"},  # 缺少名称
        ],
        "boundary_data": {
            "min_name": "a",
            "max_name": "a" * 100,
            "empty_name": "",
            "null_name": None,
            "long_name": "a" * 256
        }
    }


@pytest.fixture
def create_test_tag(cleanup_resources):
    """创建测试标签的fixture"""
    def _create_tag(name: str = None, tag_type: str = "AGENT"):
        if not name:
            name = test_data_generator.generate_random_string(prefix="test_tag_")
        
        response = tag_service.create_tag(name, tag_type)
        tag_id = tag_service.extract_tag_id(response)
        
        if tag_id:
            cleanup_resources["tags"].append(tag_id)
        
        return response, tag_id
    
    return _create_tag


@pytest.fixture
def create_test_data_center(cleanup_resources):
    """创建测试数据中心的fixture"""
    def _create_data_center(name: str = None, description: str = None):
        if not name:
            name = test_data_generator.generate_random_string(prefix="test_dc_")
        if not description:
            description = f"Test data center: {name}"
        
        response = data_center_service.create_data_center(name, description)
        data_center_id = data_center_service.extract_data_center_id(response)
        
        if data_center_id:
            cleanup_resources["data_centers"].append(data_center_id)
        
        return response, data_center_id
    
    return _create_data_center


class TestHelper:
    """测试辅助类"""
    
    @staticmethod
    def assert_success_response(response: Dict[str, Any], message: str = ""):
        """断言响应成功"""
        assert response.get("code") == response_code.SUCCESS, \
            f"响应失败 {message}: {response.get('message', '未知错误')}"
    
    @staticmethod
    def assert_error_response(response: Dict[str, Any], expected_code: str = None, message: str = ""):
        """断言响应错误"""
        assert response.get("code") != response_code.SUCCESS, \
            f"期望错误响应但得到成功响应 {message}"

        if expected_code:
            assert response.get("code") == expected_code, \
                f"错误码不匹配 {message}: 期望 {expected_code}, 实际 {response.get('code')}"
    
    @staticmethod
    def assert_response_data_not_empty(response: Dict[str, Any], message: str = ""):
        """断言响应数据不为空"""
        data = response.get("data")
        assert data is not None, f"响应数据为空 {message}"
        
        if isinstance(data, list):
            assert len(data) > 0, f"响应数据列表为空 {message}"
        elif isinstance(data, dict):
            assert len(data) > 0, f"响应数据字典为空 {message}"
    
    @staticmethod
    def assert_paged_response(response: Dict[str, Any], expected_page: int = None, expected_size: int = None):
        """断言分页响应格式"""
        TestHelper.assert_success_response(response, "分页查询")
        data = response.get("data")

        # 情况1: data直接是列表
        if isinstance(data, list):
            assert len(data) >= 0, "分页响应数据列表应为有效列表"
            # 总数可能在响应的其他字段中
            total = response.get("total")
            if total is not None:
                assert isinstance(total, int), "total字段应为整数"

        # 情况2: data是包含分页信息的对象
        elif isinstance(data, dict):
            # 查找列表数据字段
            list_data = data.get("list") or data.get("data") or data.get("items")
            assert list_data is not None, "分页响应缺少列表数据字段(list/data/items)"
            assert isinstance(list_data, list), "分页响应列表字段应为列表类型"

            # 检查总数字段
            total = data.get("total")
            if total is not None:
                assert isinstance(total, int), "total字段应为整数"

            # 检查分页参数
            if expected_page and "page" in data:
                assert data.get("page") == expected_page, f"页码不匹配: 期望 {expected_page}, 实际 {data.get('page')}"

            if expected_size and "size" in data:
                assert data.get("size") == expected_size, f"页大小不匹配: 期望 {expected_size}, 实际 {data.get('size')}"

        else:
            assert False, f"分页响应数据格式不正确: {type(data)}"


@pytest.fixture
def test_helper():
    """测试辅助工具fixture"""
    return TestHelper
