"""
基础服务类
"""
from typing import Dict, Any, Optional, List, Union
import requests
from abc import ABC, abstractmethod

from utils.http_client import http_client
from utils.logger import get_logger
from config.settings import response_code, http_status
from models.response_models import BaseResponse

logger = get_logger(__name__)


class BaseAPIService(ABC):
    """基础API服务类"""
    
    def __init__(self, resource_name: str):
        self.resource_name = resource_name
        self.http_client = http_client
    
    @abstractmethod
    def get_endpoints(self) -> Dict[str, str]:
        """获取端点配置"""
        pass
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """处理响应"""
        try:
            response_data = response.json()
            logger.debug(f"响应数据: {response_data}")
            return response_data
        except (ValueError, requests.exceptions.JSONDecodeError) as e:
            logger.error(f"解析响应JSON失败: {str(e)}")
            return {
                "code": response.status_code,
                "message": f"解析响应失败: {response.text}",
                "success": False
            }
    
    def _validate_response(self, response: requests.Response, expected_status: int = http_status.OK) -> bool:
        """验证响应状态"""
        if response.status_code != expected_status:
            logger.error(f"响应状态码不匹配: 期望 {expected_status}, 实际 {response.status_code}")
            return False
        return True
    
    def _format_endpoint(self, endpoint_template: str, **kwargs) -> str:
        """格式化端点"""
        return endpoint_template.format(**kwargs)
    
    # 标准CRUD操作
    
    def create(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建资源"""
        endpoints = self.get_endpoints()
        endpoint = endpoints.get("create")
        if not endpoint:
            raise NotImplementedError(f"{self.resource_name} 创建端点未实现")
        
        logger.info(f"创建 {self.resource_name}: {data}")
        response = self.http_client.post(endpoint, json_data=data)
        return self._handle_response(response)
    
    def get_by_id(self, resource_id: Union[int, str]) -> Dict[str, Any]:
        """根据ID获取资源"""
        endpoints = self.get_endpoints()
        endpoint_template = endpoints.get("get")
        if not endpoint_template:
            raise NotImplementedError(f"{self.resource_name} 获取端点未实现")
        
        endpoint = self._format_endpoint(endpoint_template, id=resource_id)
        logger.info(f"获取 {self.resource_name} ID: {resource_id}")
        response = self.http_client.get(endpoint)
        return self._handle_response(response)
    
    def update(self, resource_id: Union[int, str], data: Dict[str, Any]) -> Dict[str, Any]:
        """更新资源"""
        endpoints = self.get_endpoints()
        endpoint_template = endpoints.get("update")
        if not endpoint_template:
            raise NotImplementedError(f"{self.resource_name} 更新端点未实现")
        
        endpoint = self._format_endpoint(endpoint_template, id=resource_id)
        logger.info(f"更新 {self.resource_name} ID: {resource_id}, 数据: {data}")
        response = self.http_client.put(endpoint, json_data=data)
        return self._handle_response(response)
    
    def delete(self, resource_id: Union[int, str]) -> Dict[str, Any]:
        """删除资源"""
        endpoints = self.get_endpoints()
        endpoint_template = endpoints.get("delete")
        if not endpoint_template:
            raise NotImplementedError(f"{self.resource_name} 删除端点未实现")
        
        endpoint = self._format_endpoint(endpoint_template, id=resource_id)
        logger.info(f"删除 {self.resource_name} ID: {resource_id}")
        response = self.http_client.delete(endpoint)
        return self._handle_response(response)
    
    def list_all(self, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """获取所有资源列表"""
        endpoints = self.get_endpoints()
        endpoint = endpoints.get("list")
        if not endpoint:
            raise NotImplementedError(f"{self.resource_name} 列表端点未实现")
        
        logger.info(f"获取 {self.resource_name} 列表")
        response = self.http_client.get(endpoint, params=params)
        return self._handle_response(response)
    
    def query_paged(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """分页查询资源"""
        endpoints = self.get_endpoints()
        endpoint = endpoints.get("query")
        if not endpoint:
            raise NotImplementedError(f"{self.resource_name} 查询端点未实现")
        
        logger.info(f"分页查询 {self.resource_name}: {query_data}")
        response = self.http_client.post(endpoint, json_data=query_data)
        return self._handle_response(response)
    
    def get_usage(self, resource_id: Union[int, str]) -> Dict[str, Any]:
        """获取资源使用情况"""
        endpoints = self.get_endpoints()
        endpoint_template = endpoints.get("usage")
        if not endpoint_template:
            raise NotImplementedError(f"{self.resource_name} 使用情况端点未实现")
        
        endpoint = self._format_endpoint(endpoint_template, id=resource_id)
        logger.info(f"获取 {self.resource_name} 使用情况 ID: {resource_id}")
        response = self.http_client.get(endpoint)
        return self._handle_response(response)
    
    # 辅助方法
    
    def is_success_response(self, response_data: Dict[str, Any]) -> bool:
        """判断响应是否成功"""
        return response_data.get("code") == response_code.SUCCESS
    
    def get_response_data(self, response_data: Dict[str, Any]) -> Any:
        """获取响应数据"""
        return response_data.get("data")
    
    def get_error_message(self, response_data: Dict[str, Any]) -> str:
        """获取错误消息"""
        return response_data.get("message", "未知错误")
