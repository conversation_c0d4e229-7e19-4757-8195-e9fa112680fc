"""
DataCenter服务类
"""
from typing import Dict, Any, Optional, List
from services.base_service import BaseAPIService
from config.settings import api_endpoints
from utils.logger import get_logger

logger = get_logger(__name__)


class DataCenterService(BaseAPIService):
    """DataCenter API服务类"""
    
    def __init__(self):
        super().__init__("DataCenter")
    
    def get_endpoints(self) -> Dict[str, str]:
        """获取DataCenter相关端点"""
        return {
            "create": api_endpoints.DATA_CENTER_CREATE,
            "get": api_endpoints.DATA_CENTER_GET,
            "update": api_endpoints.DATA_CENTER_UPDATE,
            "delete": api_endpoints.DATA_CENTER_DELETE,
            "list": api_endpoints.DATA_CENTER_LIST,
            "query": api_endpoints.DATA_CENTER_QUERY,
            "usage": api_endpoints.DATA_CENTER_USAGE
        }
    
    def query_reference(self, data_center_id: int, ref_type: Optional[str] = None) -> Dict[str, Any]:
        """查询数据中心关联数据"""
        data = {
            "dataCenterId": data_center_id
        }
        if ref_type:
            data["type"] = ref_type
        
        logger.info(f"查询数据中心关联数据: {data}")
        response = self.http_client.post(api_endpoints.DATA_CENTER_REFERENCE, json_data=data)
        return self._handle_response(response)
    
    def create_data_center(self, name: str, description: Optional[str] = None) -> Dict[str, Any]:
        """创建数据中心"""
        data = {
            "name": name
        }
        if description:
            data["description"] = description
        return self.create(data)
    
    def update_data_center(self, data_center_id: int, name: str, description: Optional[str] = None) -> Dict[str, Any]:
        """更新数据中心"""
        data = {
            "name": name
        }
        if description:
            data["description"] = description
        return self.update(data_center_id, data)
    
    def query_data_centers(
        self,
        name: Optional[str] = None,
        page: int = 1,
        size: int = 10,
        sort_create_time: str = "desc",
        sort_update_time: str = "desc"
    ) -> Dict[str, Any]:
        """查询数据中心"""
        filter_data = {}
        if name:
            filter_data["name"] = name
        
        query_data = {
            "page": page,
            "size": size,
            "filter": filter_data,
            "sort": {
                "createTime": sort_create_time,
                "updateTime": sort_update_time
            }
        }
        return self.query_paged(query_data)
    
    def get_data_center_by_id(self, data_center_id: int) -> Dict[str, Any]:
        """根据ID获取数据中心"""
        return self.get_by_id(data_center_id)
    
    def delete_data_center(self, data_center_id: int) -> Dict[str, Any]:
        """删除数据中心"""
        return self.delete(data_center_id)
    
    def get_all_data_centers(self) -> Dict[str, Any]:
        """获取所有数据中心"""
        return self.list_all()
    
    def get_data_center_usage(self, data_center_id: int) -> Dict[str, Any]:
        """获取数据中心使用情况"""
        return self.get_usage(data_center_id)
    
    # 辅助方法
    
    def extract_data_center_id(self, create_response: Dict[str, Any]) -> Optional[int]:
        """从创建响应中提取数据中心ID"""
        if self.is_success_response(create_response):
            data = self.get_response_data(create_response)
            if data and isinstance(data, dict):
                return data.get("id")
        return None
    
    def extract_data_center_list(self, list_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从列表响应中提取数据中心列表"""
        if self.is_success_response(list_response):
            data = self.get_response_data(list_response)
            if isinstance(data, list):
                return data
        return []
    
    def extract_paged_data_centers(self, query_response: Dict[str, Any]) -> tuple[List[Dict[str, Any]], int]:
        """从分页查询响应中提取数据中心列表和总数"""
        if self.is_success_response(query_response):
            data = self.get_response_data(query_response)

            # 情况1: data直接是列表
            if isinstance(data, list):
                # 总数可能在响应的其他字段中，或者等于列表长度
                total = query_response.get("total", len(data))
                return data, total

            # 情况2: data是包含分页信息的对象
            elif isinstance(data, dict):
                # 尝试不同的字段名来获取列表数据
                data_center_list = data.get("list") or data.get("records") or data.get("items") or []
                total = data.get("total", len(data_center_list))
                return data_center_list, total

        return [], 0
    
    def find_data_center_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称查找数据中心"""
        response = self.query_data_centers(name=name, size=1)
        data_centers, _ = self.extract_paged_data_centers(response)
        return data_centers[0] if data_centers else None
    
    def extract_reference_data(self, reference_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从关联查询响应中提取关联数据"""
        if self.is_success_response(reference_response):
            data = self.get_response_data(reference_response)
            if isinstance(data, list):
                return data
        return []


# 全局DataCenter服务实例
data_center_service = DataCenterService()
