"""
Tag服务类
"""
from typing import Dict, Any, Optional, List
from services.base_service import BaseAPIService
from config.settings import api_endpoints
from utils.logger import get_logger

logger = get_logger(__name__)


class TagService(BaseAPIService):
    """Tag API服务类"""
    
    def __init__(self):
        super().__init__("Tag")
    
    def get_endpoints(self) -> Dict[str, str]:
        """获取Tag相关端点"""
        return {
            "create": api_endpoints.TAG_CREATE,
            "get": api_endpoints.TAG_GET,
            "update": api_endpoints.TAG_UPDATE,
            "delete": api_endpoints.TAG_DELETE,
            "list": api_endpoints.TAG_LIST,
            "query": api_endpoints.TAG_QUERY,
            "usage": api_endpoints.TAG_USAGE
        }
    
    def list_by_type(self, tag_type: str) -> Dict[str, Any]:
        """根据类型获取标签列表"""
        logger.info(f"根据类型获取标签列表: {tag_type}")
        response = self.http_client.get(
            api_endpoints.TAG_LIST_BY_TYPE,
            params={"tagType": tag_type}
        )
        return self._handle_response(response)
    
    def create_tag(self, name: str, tag_type: str) -> Dict[str, Any]:
        """创建标签"""
        data = {
            "name": name,
            "tagType": tag_type
        }
        return self.create(data)
    
    def update_tag(self, tag_id: int, name: str) -> Dict[str, Any]:
        """更新标签"""
        data = {
            "name": name
        }
        return self.update(tag_id, data)
    
    def query_tags(
        self,
        name: Optional[str] = None,
        tag_type: Optional[str] = None,
        id_list: Optional[List[int]] = None,
        page: int = 1,
        size: int = 10,
        sort_field: str = "updateTime",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """查询标签"""
        filter_data = {}
        if name:
            filter_data["name"] = name
        if tag_type:
            filter_data["tagType"] = tag_type
        if id_list:
            filter_data["idList"] = id_list
        
        query_data = {
            "page": page,
            "size": size,
            "filter": filter_data,
            "sort": {
                sort_field: sort_order
            }
        }
        return self.query_paged(query_data)
    
    def get_tag_by_id(self, tag_id: int) -> Dict[str, Any]:
        """根据ID获取标签"""
        return self.get_by_id(tag_id)
    
    def delete_tag(self, tag_id: int) -> Dict[str, Any]:
        """删除标签"""
        return self.delete(tag_id)
    
    def get_all_tags(self) -> Dict[str, Any]:
        """获取所有标签"""
        return self.list_all()
    
    def get_tag_usage(self, tag_id: int) -> Dict[str, Any]:
        """获取标签使用情况"""
        return self.get_usage(tag_id)
    
    # 辅助方法
    
    def extract_tag_id(self, create_response: Dict[str, Any]) -> Optional[int]:
        """从创建响应中提取标签ID"""
        if self.is_success_response(create_response):
            data = self.get_response_data(create_response)
            if data and isinstance(data, dict):
                return data.get("id")
        return None
    
    def extract_tag_list(self, list_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从列表响应中提取标签列表"""
        if self.is_success_response(list_response):
            data = self.get_response_data(list_response)
            if isinstance(data, list):
                return data
        return []
    
    def extract_paged_tags(self, query_response: Dict[str, Any]) -> tuple[List[Dict[str, Any]], int]:
        """从分页查询响应中提取标签列表和总数"""
        if self.is_success_response(query_response):
            data = self.get_response_data(query_response)

            # 情况1: data直接是列表
            if isinstance(data, list):
                # 总数可能在响应的其他字段中，或者等于列表长度
                total = query_response.get("total", len(data))
                return data, total

            # 情况2: data是包含分页信息的对象
            elif isinstance(data, dict):
                # 尝试不同的字段名来获取列表数据
                tag_list = data.get("list") or data.get("records") or data.get("items") or []
                total = data.get("total", len(tag_list))
                return tag_list, total

        return [], 0
    
    def find_tag_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称查找标签"""
        response = self.query_tags(name=name, size=1)
        tags, _ = self.extract_paged_tags(response)
        return tags[0] if tags else None


# 全局Tag服务实例
tag_service = TagService()
