#!/usr/bin/env python3
"""
快速启动脚本
用于快速验证测试框架是否正常工作
"""
import os
import sys
import subprocess
from pathlib import Path


def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    JAX API 自动化测试框架                      ║
    ║                        快速启动脚本                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major != 3 or version.minor < 10:
        print(f"❌ Python版本不符合要求: {version.major}.{version.minor}")
        print("   需要Python 3.10或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖包...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装依赖时出错: {str(e)}")
        return False


def setup_environment():
    """设置环境"""
    print("\n⚙️  设置测试环境...")
    
    # 创建必要目录
    directories = ["logs", "reports", "reports/allure-results"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 创建环境配置文件...")
        try:
            # 复制.env.example到.env
            example_file = Path(".env.example")
            if example_file.exists():
                with open(example_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(env_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print("✅ 环境配置文件已创建")
                print("💡 请编辑 .env 文件配置API地址等信息")
            else:
                print("❌ .env.example文件不存在")
                return False
        except Exception as e:
            print(f"❌ 创建环境配置文件失败: {str(e)}")
            return False
    else:
        print("✅ 环境配置文件已存在")
    
    return True


def run_framework_validation():
    """运行框架验证测试"""
    print("\n🧪 运行框架验证测试...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_framework_validation.py",
            "-v", "--tb=short"
        ], capture_output=True, text=True)
        
        print("测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 框架验证测试通过")
            return True
        else:
            print("❌ 框架验证测试失败")
            return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {str(e)}")
        return False


def run_smoke_tests():
    """运行冒烟测试"""
    print("\n🔥 运行冒烟测试...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "-m", "smoke",
            "-v", "--tb=short"
        ], capture_output=True, text=True)
        
        print("测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 冒烟测试通过")
            return True
        else:
            print("⚠️  冒烟测试失败（可能是API服务未启动）")
            return False
    except Exception as e:
        print(f"❌ 运行冒烟测试时出错: {str(e)}")
        return False


def show_next_steps():
    """显示后续步骤"""
    print("\n📋 后续步骤:")
    print("1. 编辑 .env 文件，配置正确的API地址")
    print("2. 确保API服务正在运行")
    print("3. 运行完整测试:")
    print("   python run_tests.py")
    print("4. 或使用Makefile:")
    print("   make test")
    print("5. 生成测试报告:")
    print("   python run_tests.py --report")
    print("\n📚 更多信息请查看 README.md")


def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 快速启动失败：依赖安装失败")
        sys.exit(1)
    
    # 设置环境
    if not setup_environment():
        print("\n❌ 快速启动失败：环境设置失败")
        sys.exit(1)
    
    # 运行框架验证测试
    framework_ok = run_framework_validation()
    
    # 运行冒烟测试（可选）
    smoke_ok = run_smoke_tests()
    
    # 显示结果
    print("\n" + "="*60)
    print("📊 快速启动结果:")
    print(f"   框架验证: {'✅ 通过' if framework_ok else '❌ 失败'}")
    print(f"   冒烟测试: {'✅ 通过' if smoke_ok else '⚠️  失败'}")
    
    if framework_ok:
        print("\n🎉 测试框架已准备就绪！")
        if not smoke_ok:
            print("💡 冒烟测试失败可能是因为API服务未启动，请检查配置")
        show_next_steps()
    else:
        print("\n❌ 测试框架存在问题，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
