# JAX API自动化测试框架配置文件
# 复制此文件为.env并根据实际环境修改配置

# API基础配置
BASE_URL=http://localhost:8080    # API服务地址
API_VERSION=v2                    # API版本

# 认证配置（可选）
TOKEN=                           # JWT Token，如果API需要认证请设置

# 测试配置
TIMEOUT=30                       # HTTP请求超时时间（秒）
LOG_LEVEL=INFO                   # 日志级别：DEBUG, INFO, WARNING, ERROR

# 测试数据配置
CLEANUP_AFTER_TEST=true          # 测试完成后是否自动清理创建的数据
GENERATE_TEST_REPORT=true        # 是否生成测试报告
