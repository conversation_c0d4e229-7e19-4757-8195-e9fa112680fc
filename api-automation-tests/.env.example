# API Base Configuration
BASE_URL=http://localhost:8080
API_VERSION=v2

# Authentication (if needed)
USERNAME=admin
PASSWORD=admin123
TOKEN=

# Test Configuration
TIMEOUT=30
RETRY_COUNT=3
LOG_LEVEL=INFO

# Database Configuration (if needed for data setup)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=jax_test
DB_USER=root
DB_PASSWORD=password

# Test Data Configuration
CLEANUP_AFTER_TEST=true
GENERATE_TEST_REPORT=true
