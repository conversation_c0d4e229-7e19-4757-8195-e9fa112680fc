#!/usr/bin/env python3
"""
测试运行脚本
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path
from utils.logger import get_logger

logger = get_logger(__name__)


def run_command(command, cwd=None):
    """运行命令"""
    logger.info(f"执行命令: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.stdout:
            logger.info(f"命令输出:\n{result.stdout}")
        
        if result.stderr:
            logger.warning(f"命令错误:\n{result.stderr}")
        
        return result.returncode == 0, result.stdout, result.stderr
    
    except Exception as e:
        logger.error(f"执行命令失败: {str(e)}")
        return False, "", str(e)


def setup_environment():
    """设置测试环境"""
    logger.info("设置测试环境...")
    
    # 创建必要的目录
    directories = ["logs", "reports"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建目录: {directory}")
    
    # 检查环境变量文件
    env_file = Path(".env")
    if not env_file.exists():
        logger.warning(".env文件不存在，请复制.env.example并配置")
        return False
    
    return True


def run_tests(test_type="all", markers=None, parallel=False, verbose=False):
    """运行测试"""
    logger.info(f"开始运行测试: {test_type}")
    
    # 构建pytest命令
    cmd_parts = ["python", "-m", "pytest"]
    
    if test_type == "all":
        cmd_parts.append("tests/")
    else:
        cmd_parts.append(f"tests/test_{test_type}_api.py")
    
    # 添加标记过滤
    if markers:
        cmd_parts.extend(["-m", markers])
    
    # 添加并行执行
    if parallel:
        cmd_parts.extend(["-n", "auto"])
    
    # 添加详细输出和日志显示
    if verbose:
        cmd_parts.extend(["-v", "-s"])  # -s显示print输出和日志
    else:
        cmd_parts.append("-s")  # 总是显示日志输出

    # 执行测试
    command = " ".join(cmd_parts)
    success, stdout, stderr = run_command(command)
    
    if success:
        logger.info("测试执行完成")
    else:
        logger.error("测试执行失败")
    
    return success


def generate_report():
    """生成HTML测试报告"""
    logger.info("检查HTML测试报告...")

    # 检查HTML报告文件
    report_file = Path("reports/report.html")
    if report_file.exists():
        logger.info(f"HTML测试报告已生成: {report_file}")
        logger.info(f"可以在浏览器中打开查看: file://{report_file.absolute()}")
        return True
    else:
        logger.warning("没有找到HTML测试报告文件")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API自动化测试运行器")
    
    parser.add_argument(
        "--type", "-t",
        choices=["all", "tag", "datacenter"],
        default="all",
        help="测试类型"
    )
    
    parser.add_argument(
        "--markers", "-m",
        help="pytest标记过滤器"
    )
    
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="并行执行测试"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--report", "-r",
        action="store_true",
        help="生成测试报告"
    )
    
    parser.add_argument(
        "--setup-only",
        action="store_true",
        help="仅设置环境，不运行测试"
    )
    
    args = parser.parse_args()
    
    # 设置环境
    if not setup_environment():
        logger.error("环境设置失败")
        sys.exit(1)
    
    if args.setup_only:
        logger.info("环境设置完成")
        return
    
    # 运行测试
    success = run_tests(
        test_type=args.type,
        markers=args.markers,
        parallel=args.parallel,
        verbose=args.verbose
    )
    
    # 生成报告
    if args.report:
        generate_report()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
