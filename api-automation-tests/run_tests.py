#!/usr/bin/env python3
"""
测试运行脚本
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path
from utils.logger import get_logger

logger = get_logger(__name__)


def run_command(command, cwd=None):
    """运行命令"""
    logger.info(f"执行命令: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.stdout:
            logger.info(f"命令输出:\n{result.stdout}")
        
        if result.stderr:
            logger.warning(f"命令错误:\n{result.stderr}")
        
        return result.returncode == 0, result.stdout, result.stderr
    
    except Exception as e:
        logger.error(f"执行命令失败: {str(e)}")
        return False, "", str(e)


def setup_environment():
    """设置测试环境"""
    logger.info("设置测试环境...")
    
    # 创建必要的目录
    directories = ["logs", "reports", "reports/allure-results"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建目录: {directory}")
    
    # 检查环境变量文件
    env_file = Path(".env")
    if not env_file.exists():
        logger.warning(".env文件不存在，请复制.env.example并配置")
        return False
    
    return True


def run_tests(test_type="all", markers=None, parallel=False, verbose=False):
    """运行测试"""
    logger.info(f"开始运行测试: {test_type}")
    
    # 构建pytest命令
    cmd_parts = ["python", "-m", "pytest"]
    
    # 添加测试路径
    if test_type == "tag":
        cmd_parts.append("tests/test_tag_api.py")
    elif test_type == "datacenter":
        cmd_parts.append("tests/test_data_center_api.py")
    elif test_type == "all":
        cmd_parts.append("tests/")
    else:
        cmd_parts.append(f"tests/test_{test_type}_api.py")
    
    # 添加标记过滤
    if markers:
        cmd_parts.extend(["-m", markers])
    
    # 添加并行执行
    if parallel:
        cmd_parts.extend(["-n", "auto"])
    
    # 添加详细输出
    if verbose:
        cmd_parts.append("-v")
    
    # 执行测试
    command = " ".join(cmd_parts)
    success, stdout, stderr = run_command(command)
    
    if success:
        logger.info("测试执行完成")
    else:
        logger.error("测试执行失败")
    
    return success


def generate_report():
    """生成测试报告"""
    logger.info("生成Allure报告...")
    
    # 检查allure-results目录
    results_dir = Path("reports/allure-results")
    if not results_dir.exists() or not any(results_dir.iterdir()):
        logger.warning("没有找到测试结果文件，跳过报告生成")
        return False
    
    # 生成Allure报告
    report_dir = Path("reports/allure-report")
    command = f"allure generate {results_dir} -o {report_dir} --clean"
    success, stdout, stderr = run_command(command)
    
    if success:
        logger.info(f"Allure报告已生成: {report_dir}")
        # 尝试打开报告
        try:
            open_command = f"allure open {report_dir}"
            subprocess.Popen(open_command, shell=True)
            logger.info("正在打开Allure报告...")
        except Exception as e:
            logger.warning(f"无法自动打开报告: {str(e)}")
    else:
        logger.error("生成Allure报告失败")
    
    return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API自动化测试运行器")
    
    parser.add_argument(
        "--type", "-t",
        choices=["all", "tag", "datacenter"],
        default="all",
        help="测试类型"
    )
    
    parser.add_argument(
        "--markers", "-m",
        help="pytest标记过滤器"
    )
    
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="并行执行测试"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--report", "-r",
        action="store_true",
        help="生成测试报告"
    )
    
    parser.add_argument(
        "--setup-only",
        action="store_true",
        help="仅设置环境，不运行测试"
    )
    
    args = parser.parse_args()
    
    # 设置环境
    if not setup_environment():
        logger.error("环境设置失败")
        sys.exit(1)
    
    if args.setup_only:
        logger.info("环境设置完成")
        return
    
    # 运行测试
    success = run_tests(
        test_type=args.type,
        markers=args.markers,
        parallel=args.parallel,
        verbose=args.verbose
    )
    
    # 生成报告
    if args.report:
        generate_report()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
