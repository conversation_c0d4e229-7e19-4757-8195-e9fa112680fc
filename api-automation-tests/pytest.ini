[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 标记定义
markers =
    tag: Tag API测试
    datacenter: DataCenter API测试
    integration: 集成测试
    slow: 慢速测试

# 最小版本要求
minversion = 7.0

# 添加选项
addopts =
    -v
    --tb=short
    --maxfail=10
    --durations=10
    --html=reports/report.html
    --self-contained-html
    --alluredir=reports/allure-results
    --clean-alluredir

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning

# 并行测试配置
# 使用 -n auto 启用自动并行
# pytest -n auto

# 超时配置
timeout = 300
