"""
CRUD操作综合测试用例
测试6个标准接口：分页查询、list查询、根据id获取详情、根据id更新、根据id删除、post新建
"""
import pytest
import allure
from typing import Dict, Any, List
from services.tag_service import tag_service
from services.data_center_service import data_center_service
from utils.logger import get_logger
from utils.test_data_generator import test_data_generator

logger = get_logger(__name__)


@allure.feature("CRUD操作综合测试")
class TestCRUDOperations:
    """CRUD操作综合测试类"""
    
    @allure.story("Tag完整CRUD流程")
    @allure.title("测试Tag的完整CRUD操作流程")
    @allure.description("测试Tag的创建、查询、更新、删除完整流程")
    def test_tag_complete_crud_flow(self, test_helper):
        """测试Tag完整CRUD流程"""
        tag_id = None
        
        try:
            # 1. 创建 (POST)
            with allure.step("1. 创建标签 (POST)"):
                create_data = test_data_generator.generate_tag_data()
                create_response = tag_service.create_tag(create_data["name"], create_data["tagType"])
                test_helper.assert_success_response(create_response, "创建标签")
                
                tag_id = tag_service.extract_tag_id(create_response)
                assert tag_id is not None, "创建标签失败，未获取到ID"
                logger.info(f"创建标签成功，ID: {tag_id}")
            
            # 2. 根据ID获取详情 (GET)
            with allure.step("2. 根据ID获取标签详情 (GET)"):
                get_response = tag_service.get_tag_by_id(tag_id)
                test_helper.assert_success_response(get_response, "获取标签详情")
                
                tag_data = get_response.get("data")
                assert tag_data["id"] == tag_id, "获取的标签ID不匹配"
                assert tag_data["name"] == create_data["name"], "获取的标签名称不匹配"
                assert tag_data["tagType"] == create_data["tagType"], "获取的标签类型不匹配"
                logger.info(f"获取标签详情成功: {tag_data}")
            
            # 3. List查询 (GET)
            with allure.step("3. 获取标签列表 (GET)"):
                list_response = tag_service.get_all_tags()
                test_helper.assert_success_response(list_response, "获取标签列表")
                
                tag_list = tag_service.extract_tag_list(list_response)
                # 验证创建的标签在列表中
                found_tag = next((tag for tag in tag_list if tag["id"] == tag_id), None)
                assert found_tag is not None, "创建的标签未在列表中找到"
                logger.info(f"标签列表查询成功，找到创建的标签")
            
            # 4. 分页查询 (POST)
            with allure.step("4. 分页查询标签 (POST)"):
                query_data = test_data_generator.generate_tag_query_data(name=create_data["name"])
                query_response = tag_service.query_paged(query_data)
                test_helper.assert_paged_response(query_response, query_data["page"], query_data["size"])
                
                paged_tags, total = tag_service.extract_paged_tags(query_response)
                # 验证查询结果包含创建的标签
                found_tag = next((tag for tag in paged_tags if tag["id"] == tag_id), None)
                assert found_tag is not None, "分页查询结果中未找到创建的标签"
                logger.info(f"分页查询成功，总数: {total}")
            
            # 5. 根据ID更新 (PUT)
            with allure.step("5. 根据ID更新标签 (PUT)"):
                update_data = test_data_generator.generate_tag_update_data()
                update_response = tag_service.update_tag(tag_id, update_data["name"])
                test_helper.assert_success_response(update_response, "更新标签")
                
                # 验证更新结果
                get_updated_response = tag_service.get_tag_by_id(tag_id)
                updated_tag_data = get_updated_response.get("data")
                assert updated_tag_data["name"] == update_data["name"], "标签名称未正确更新"
                logger.info(f"更新标签成功: {updated_tag_data}")
            
            # 6. 根据ID删除 (DELETE)
            with allure.step("6. 根据ID删除标签 (DELETE)"):
                delete_response = tag_service.delete_tag(tag_id)
                test_helper.assert_success_response(delete_response, "删除标签")
                
                # 验证删除结果
                get_deleted_response = tag_service.get_tag_by_id(tag_id)
                test_helper.assert_error_response(get_deleted_response, message="已删除的标签应无法获取")
                logger.info(f"删除标签成功，ID: {tag_id}")
                tag_id = None  # 标记已删除，避免重复删除
        
        except Exception as e:
            logger.error(f"Tag CRUD流程测试失败: {str(e)}")
            raise
        finally:
            # 清理资源
            if tag_id:
                try:
                    tag_service.delete_tag(tag_id)
                    logger.info(f"清理标签资源: {tag_id}")
                except Exception as e:
                    logger.warning(f"清理标签资源失败: {str(e)}")
    
    @allure.story("DataCenter完整CRUD流程")
    @allure.title("测试DataCenter的完整CRUD操作流程")
    @allure.description("测试DataCenter的创建、查询、更新、删除完整流程")
    def test_data_center_complete_crud_flow(self, test_helper):
        """测试DataCenter完整CRUD流程"""
        data_center_id = None
        
        try:
            # 1. 创建 (POST)
            with allure.step("1. 创建数据中心 (POST)"):
                create_data = test_data_generator.generate_data_center_data()
                create_response = data_center_service.create_data_center(
                    create_data["name"], 
                    create_data["description"]
                )
                test_helper.assert_success_response(create_response, "创建数据中心")
                
                data_center_id = data_center_service.extract_data_center_id(create_response)
                assert data_center_id is not None, "创建数据中心失败，未获取到ID"
                logger.info(f"创建数据中心成功，ID: {data_center_id}")
            
            # 2. 根据ID获取详情 (GET)
            with allure.step("2. 根据ID获取数据中心详情 (GET)"):
                get_response = data_center_service.get_data_center_by_id(data_center_id)
                test_helper.assert_success_response(get_response, "获取数据中心详情")
                
                dc_data = get_response.get("data")
                assert dc_data["id"] == data_center_id, "获取的数据中心ID不匹配"
                assert dc_data["name"] == create_data["name"], "获取的数据中心名称不匹配"
                assert dc_data["description"] == create_data["description"], "获取的数据中心描述不匹配"
                logger.info(f"获取数据中心详情成功: {dc_data}")
            
            # 3. List查询 (GET)
            with allure.step("3. 获取数据中心列表 (GET)"):
                list_response = data_center_service.get_all_data_centers()
                test_helper.assert_success_response(list_response, "获取数据中心列表")
                
                dc_list = data_center_service.extract_data_center_list(list_response)
                # 验证创建的数据中心在列表中
                found_dc = next((dc for dc in dc_list if dc["id"] == data_center_id), None)
                assert found_dc is not None, "创建的数据中心未在列表中找到"
                logger.info(f"数据中心列表查询成功，找到创建的数据中心")
            
            # 4. 分页查询 (POST)
            with allure.step("4. 分页查询数据中心 (POST)"):
                query_data = test_data_generator.generate_data_center_query_data(name=create_data["name"])
                query_response = data_center_service.query_paged(query_data)
                test_helper.assert_paged_response(query_response, query_data["page"], query_data["size"])
                
                paged_dcs, total = data_center_service.extract_paged_data_centers(query_response)
                # 验证查询结果包含创建的数据中心
                found_dc = next((dc for dc in paged_dcs if dc["id"] == data_center_id), None)
                assert found_dc is not None, "分页查询结果中未找到创建的数据中心"
                logger.info(f"分页查询成功，总数: {total}")
            
            # 5. 根据ID更新 (PUT)
            with allure.step("5. 根据ID更新数据中心 (PUT)"):
                update_data = test_data_generator.generate_data_center_update_data()
                update_response = data_center_service.update_data_center(
                    data_center_id, 
                    update_data["name"], 
                    update_data["description"]
                )
                test_helper.assert_success_response(update_response, "更新数据中心")
                
                # 验证更新结果
                get_updated_response = data_center_service.get_data_center_by_id(data_center_id)
                updated_dc_data = get_updated_response.get("data")
                assert updated_dc_data["name"] == update_data["name"], "数据中心名称未正确更新"
                assert updated_dc_data["description"] == update_data["description"], "数据中心描述未正确更新"
                logger.info(f"更新数据中心成功: {updated_dc_data}")
            
            # 6. 根据ID删除 (DELETE)
            with allure.step("6. 根据ID删除数据中心 (DELETE)"):
                delete_response = data_center_service.delete_data_center(data_center_id)
                test_helper.assert_success_response(delete_response, "删除数据中心")
                
                # 验证删除结果
                get_deleted_response = data_center_service.get_data_center_by_id(data_center_id)
                test_helper.assert_error_response(get_deleted_response, message="已删除的数据中心应无法获取")
                logger.info(f"删除数据中心成功，ID: {data_center_id}")
                data_center_id = None  # 标记已删除，避免重复删除
        
        except Exception as e:
            logger.error(f"DataCenter CRUD流程测试失败: {str(e)}")
            raise
        finally:
            # 清理资源
            if data_center_id:
                try:
                    data_center_service.delete_data_center(data_center_id)
                    logger.info(f"清理数据中心资源: {data_center_id}")
                except Exception as e:
                    logger.warning(f"清理数据中心资源失败: {str(e)}")
    
    @allure.story("批量操作测试")
    @allure.title("测试批量创建和查询操作")
    def test_batch_operations(self, test_helper):
        """测试批量操作"""
        created_tag_ids = []
        created_dc_ids = []
        
        try:
            # 批量创建标签
            with allure.step("批量创建标签"):
                for i in range(3):
                    create_data = test_data_generator.generate_tag_data(
                        name=f"batch_tag_{i}",
                        tag_type="AGENT"
                    )
                    response = tag_service.create_tag(create_data["name"], create_data["tagType"])
                    test_helper.assert_success_response(response, f"创建标签{i}")
                    
                    tag_id = tag_service.extract_tag_id(response)
                    if tag_id:
                        created_tag_ids.append(tag_id)
            
            # 批量创建数据中心
            with allure.step("批量创建数据中心"):
                for i in range(3):
                    create_data = test_data_generator.generate_data_center_data(
                        name=f"batch_dc_{i}"
                    )
                    response = data_center_service.create_data_center(
                        create_data["name"], 
                        create_data["description"]
                    )
                    test_helper.assert_success_response(response, f"创建数据中心{i}")
                    
                    dc_id = data_center_service.extract_data_center_id(response)
                    if dc_id:
                        created_dc_ids.append(dc_id)
            
            # 验证批量查询
            with allure.step("验证批量查询结果"):
                # 查询标签列表
                tag_list_response = tag_service.get_all_tags()
                test_helper.assert_success_response(tag_list_response, "获取标签列表")
                tag_list = tag_service.extract_tag_list(tag_list_response)
                
                # 验证创建的标签都在列表中
                for tag_id in created_tag_ids:
                    found = any(tag["id"] == tag_id for tag in tag_list)
                    assert found, f"标签 {tag_id} 未在列表中找到"
                
                # 查询数据中心列表
                dc_list_response = data_center_service.get_all_data_centers()
                test_helper.assert_success_response(dc_list_response, "获取数据中心列表")
                dc_list = data_center_service.extract_data_center_list(dc_list_response)
                
                # 验证创建的数据中心都在列表中
                for dc_id in created_dc_ids:
                    found = any(dc["id"] == dc_id for dc in dc_list)
                    assert found, f"数据中心 {dc_id} 未在列表中找到"
                
                logger.info(f"批量操作验证成功，创建了{len(created_tag_ids)}个标签和{len(created_dc_ids)}个数据中心")
        
        finally:
            # 清理资源
            for tag_id in created_tag_ids:
                try:
                    tag_service.delete_tag(tag_id)
                except Exception as e:
                    logger.warning(f"清理标签 {tag_id} 失败: {str(e)}")
            
            for dc_id in created_dc_ids:
                try:
                    data_center_service.delete_data_center(dc_id)
                except Exception as e:
                    logger.warning(f"清理数据中心 {dc_id} 失败: {str(e)}")
