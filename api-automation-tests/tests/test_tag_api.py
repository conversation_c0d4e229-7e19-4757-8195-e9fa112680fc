"""
Tag API测试用例
"""
import pytest
import allure
from typing import Dict, Any
from services.tag_service import tag_service
from utils.logger import get_logger
from config.settings import test_data_config

logger = get_logger(__name__)


@allure.feature("Tag管理")
@pytest.mark.tag
class TestTagAPI:
    """Tag API测试类"""
    
    @allure.story("创建标签")
    @allure.title("测试创建标签 - 正常情况")
    @allure.description("测试使用有效数据创建标签")
    def test_create_tag_success(self, tag_test_data, cleanup_resources, test_helper):
        """测试创建标签成功"""
        # 准备测试数据
        create_data = tag_test_data["valid_create_data"]
        
        with allure.step(f"创建标签: {create_data}"):
            response = tag_service.create_tag(create_data["name"], create_data["tagType"])
        
        with allure.step("验证响应"):
            test_helper.assert_success_response(response, "创建标签")
            test_helper.assert_response_data_not_empty(response, "创建标签响应数据")
        
        with allure.step("验证创建的标签数据"):
            data = response.get("data")
            assert data["name"] == create_data["name"], "标签名称不匹配"
            assert data["tagType"] == create_data["tagType"], "标签类型不匹配"
            assert "id" in data, "响应中缺少标签ID"
            
            # 记录创建的资源用于清理
            tag_id = data["id"]
            cleanup_resources["tags"].append(tag_id)
    
    @allure.story("创建标签")
    @allure.title("测试创建标签 - 参数验证")
    @pytest.mark.parametrize("invalid_data", [
        {},
        {"name": ""},
        {"tagType": "AGENT"},
        {"name": "test"},
        {"name": "test", "tagType": "INVALID_TYPE"}
    ])
    def test_create_tag_invalid_data(self, invalid_data, test_helper):
        """测试创建标签时的参数验证"""
        with allure.step(f"使用无效数据创建标签: {invalid_data}"):
            if "name" in invalid_data and "tagType" in invalid_data:
                response = tag_service.create_tag(invalid_data["name"], invalid_data["tagType"])
            elif "name" in invalid_data:
                response = tag_service.create_tag(invalid_data["name"], "AGENT")
            elif "tagType" in invalid_data:
                response = tag_service.create_tag("test_name", invalid_data["tagType"])
            else:
                # 空数据情况，直接调用create方法
                response = tag_service.create(invalid_data)
        
        with allure.step("验证错误响应"):
            test_helper.assert_error_response(response, message="无效数据应返回错误")
    
    @allure.story("查询标签")
    @allure.title("测试获取标签详情")
    def test_get_tag_by_id(self, create_test_tag, test_helper):
        """测试根据ID获取标签详情"""
        # 先创建一个标签
        with allure.step("创建测试标签"):
            create_response, tag_id = create_test_tag()
            test_helper.assert_success_response(create_response, "创建测试标签")
            assert tag_id is not None, "创建标签失败，未获取到ID"
        
        # 获取标签详情
        with allure.step(f"获取标签详情 ID: {tag_id}"):
            response = tag_service.get_tag_by_id(tag_id)
        
        with allure.step("验证响应"):
            test_helper.assert_success_response(response, "获取标签详情")
            test_helper.assert_response_data_not_empty(response, "标签详情数据")
        
        with allure.step("验证标签数据"):
            data = response.get("data")
            assert data["id"] == tag_id, "标签ID不匹配"
            assert "name" in data, "响应中缺少标签名称"
            assert "tagType" in data, "响应中缺少标签类型"
    
    @allure.story("查询标签")
    @allure.title("测试获取不存在的标签")
    def test_get_nonexistent_tag(self, test_helper):
        """测试获取不存在的标签"""
        nonexistent_id = 999999
        
        with allure.step(f"获取不存在的标签 ID: {nonexistent_id}"):
            response = tag_service.get_tag_by_id(nonexistent_id)
        
        with allure.step("验证错误响应"):
            test_helper.assert_error_response(response, message="不存在的标签应返回错误")
    
    @allure.story("更新标签")
    @allure.title("测试更新标签")
    def test_update_tag(self, create_test_tag, tag_test_data, test_helper):
        """测试更新标签"""
        # 先创建一个标签
        with allure.step("创建测试标签"):
            create_response, tag_id = create_test_tag()
            test_helper.assert_success_response(create_response, "创建测试标签")
            assert tag_id is not None, "创建标签失败，未获取到ID"
        
        # 更新标签
        update_data = tag_test_data["valid_update_data"]
        with allure.step(f"更新标签 ID: {tag_id}, 数据: {update_data}"):
            response = tag_service.update_tag(tag_id, update_data["name"])
        
        with allure.step("验证更新响应"):
            test_helper.assert_success_response(response, "更新标签")
        
        # 验证更新结果
        with allure.step("验证更新结果"):
            get_response = tag_service.get_tag_by_id(tag_id)
            test_helper.assert_success_response(get_response, "获取更新后的标签")
            data = get_response.get("data")
            assert data["name"] == update_data["name"], "标签名称未正确更新"
    
    @allure.story("删除标签")
    @allure.title("测试删除标签")
    def test_delete_tag(self, create_test_tag, test_helper):
        """测试删除标签"""
        # 先创建一个标签
        with allure.step("创建测试标签"):
            create_response, tag_id = create_test_tag()
            test_helper.assert_success_response(create_response, "创建测试标签")
            assert tag_id is not None, "创建标签失败，未获取到ID"
        
        # 删除标签
        with allure.step(f"删除标签 ID: {tag_id}"):
            response = tag_service.delete_tag(tag_id)
        
        with allure.step("验证删除响应"):
            test_helper.assert_success_response(response, "删除标签")
        
        # 验证删除结果
        with allure.step("验证标签已被删除"):
            get_response = tag_service.get_tag_by_id(tag_id)
            test_helper.assert_error_response(get_response, message="已删除的标签应无法获取")
    
    @allure.story("查询标签")
    @allure.title("测试获取标签列表")
    def test_list_tags(self, test_helper):
        """测试获取标签列表"""
        with allure.step("获取所有标签列表"):
            response = tag_service.get_all_tags()
        
        with allure.step("验证响应"):
            test_helper.assert_success_response(response, "获取标签列表")
            data = response.get("data")
            assert isinstance(data, list), "标签列表数据应为列表类型"
    
    @allure.story("查询标签")
    @allure.title("测试根据类型获取标签列表")
    @pytest.mark.parametrize("tag_type", test_data_config.TAG_TYPES)
    def test_list_tags_by_type(self, tag_type, test_helper):
        """测试根据类型获取标签列表"""
        with allure.step(f"根据类型获取标签列表: {tag_type}"):
            response = tag_service.list_by_type(tag_type)
        
        with allure.step("验证响应"):
            test_helper.assert_success_response(response, f"获取{tag_type}类型标签列表")
            data = response.get("data")
            assert isinstance(data, list), "标签列表数据应为列表类型"
            
            # 验证返回的标签都是指定类型
            for tag in data:
                assert tag.get("tagType") == tag_type, f"标签类型不匹配: 期望{tag_type}, 实际{tag.get('tagType')}"
    
    @allure.story("查询标签")
    @allure.title("测试分页查询标签")
    def test_query_tags_paged(self, tag_test_data, test_helper):
        """测试分页查询标签"""
        query_data = tag_test_data["valid_query_data"]
        
        with allure.step(f"分页查询标签: {query_data}"):
            response = tag_service.query_paged(query_data)
        
        with allure.step("验证分页响应"):
            test_helper.assert_paged_response(response, query_data["page"], query_data["size"])
    
    @allure.story("查询标签")
    @allure.title("测试标签使用情况查询")
    def test_get_tag_usage(self, create_test_tag, test_helper):
        """测试获取标签使用情况"""
        # 先创建一个标签
        with allure.step("创建测试标签"):
            create_response, tag_id = create_test_tag()
            test_helper.assert_success_response(create_response, "创建测试标签")
            assert tag_id is not None, "创建标签失败，未获取到ID"
        
        # 获取标签使用情况
        with allure.step(f"获取标签使用情况 ID: {tag_id}"):
            response = tag_service.get_tag_usage(tag_id)
        
        with allure.step("验证响应"):
            test_helper.assert_success_response(response, "获取标签使用情况")
            data = response.get("data")
            assert isinstance(data, list), "使用情况数据应为列表类型"
