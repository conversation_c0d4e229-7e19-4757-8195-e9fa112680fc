"""
错误处理和边界测试用例
"""
import pytest
import allure
from typing import Dict, Any
from services.tag_service import tag_service
from services.data_center_service import data_center_service
from utils.logger import get_logger
from utils.test_data_generator import test_data_generator

logger = get_logger(__name__)


@allure.feature("错误处理和边界测试")
class TestErrorHandling:
    """错误处理和边界测试类"""
    
    @allure.story("参数验证")
    @allure.title("测试无效参数处理")
    @pytest.mark.parametrize("invalid_tag_data", [
        {"name": "", "tagType": "AGENT"},  # 空名称
        {"name": None, "tagType": "AGENT"},  # None名称
        {"name": "test", "tagType": ""},  # 空类型
        {"name": "test", "tagType": None},  # None类型
        {"name": "test", "tagType": "INVALID"},  # 无效类型
        {"name": "a" * 256, "tagType": "AGENT"},  # 超长名称
    ])
    def test_invalid_tag_parameters(self, invalid_tag_data, test_helper):
        """测试标签无效参数处理"""
        with allure.step(f"使用无效参数创建标签: {invalid_tag_data}"):
            try:
                response = tag_service.create_tag(
                    invalid_tag_data.get("name"), 
                    invalid_tag_data.get("tagType")
                )
                # 如果没有抛出异常，则验证返回错误响应
                test_helper.assert_error_response(response, message="无效参数应返回错误")
            except (TypeError, ValueError) as e:
                # 参数验证在客户端抛出异常也是正常的
                logger.info(f"客户端参数验证异常: {str(e)}")
                assert True
    
    @allure.story("参数验证")
    @allure.title("测试数据中心无效参数处理")
    @pytest.mark.parametrize("invalid_dc_data", [
        {"name": "", "description": "test"},  # 空名称
        {"name": None, "description": "test"},  # None名称
        {"name": "a" * 256, "description": "test"},  # 超长名称
    ])
    def test_invalid_data_center_parameters(self, invalid_dc_data, test_helper):
        """测试数据中心无效参数处理"""
        with allure.step(f"使用无效参数创建数据中心: {invalid_dc_data}"):
            try:
                response = data_center_service.create_data_center(
                    invalid_dc_data.get("name"), 
                    invalid_dc_data.get("description")
                )
                # 如果没有抛出异常，则验证返回错误响应
                test_helper.assert_error_response(response, message="无效参数应返回错误")
            except (TypeError, ValueError) as e:
                # 参数验证在客户端抛出异常也是正常的
                logger.info(f"客户端参数验证异常: {str(e)}")
                assert True
    
    @allure.story("资源不存在")
    @allure.title("测试访问不存在的资源")
    @pytest.mark.parametrize("nonexistent_id", [0, -1, 999999, 9999999999])
    def test_nonexistent_resource_access(self, nonexistent_id, test_helper):
        """测试访问不存在的资源"""
        with allure.step(f"获取不存在的标签 ID: {nonexistent_id}"):
            response = tag_service.get_tag_by_id(nonexistent_id)
            test_helper.assert_error_response(response, message="不存在的资源应返回错误")
        
        with allure.step(f"获取不存在的数据中心 ID: {nonexistent_id}"):
            response = data_center_service.get_data_center_by_id(nonexistent_id)
            test_helper.assert_error_response(response, message="不存在的资源应返回错误")
        
        with allure.step(f"更新不存在的标签 ID: {nonexistent_id}"):
            response = tag_service.update_tag(nonexistent_id, "updated_name")
            test_helper.assert_error_response(response, message="更新不存在的资源应返回错误")
        
        with allure.step(f"删除不存在的标签 ID: {nonexistent_id}"):
            response = tag_service.delete_tag(nonexistent_id)
            test_helper.assert_error_response(response, message="删除不存在的资源应返回错误")
    
    @allure.story("边界值测试")
    @allure.title("测试分页查询边界值")
    @pytest.mark.parametrize("page_params", [
        {"page": 0, "size": 10},  # 页码为0
        {"page": -1, "size": 10},  # 负页码
        {"page": 1, "size": 0},  # 页大小为0
        {"page": 1, "size": -1},  # 负页大小
        {"page": 1, "size": 1000},  # 超大页大小
        {"page": 999999, "size": 10},  # 超大页码
    ])
    def test_pagination_boundary_values(self, page_params, test_helper):
        """测试分页查询边界值"""
        query_data = {
            "page": page_params["page"],
            "size": page_params["size"],
            "filter": {},
            "sort": {"updateTime": "desc"}
        }
        
        with allure.step(f"测试分页参数: {page_params}"):
            # 测试标签分页查询
            tag_response = tag_service.query_paged(query_data)
            if page_params["page"] <= 0 or page_params["size"] <= 0:
                # 无效参数应返回错误或空结果
                if not test_helper.assert_error_response(tag_response, message="无效分页参数"):
                    # 如果不是错误响应，验证是否返回空结果
                    data = tag_response.get("data", {})
                    if isinstance(data, dict) and "list" in data:
                        assert len(data["list"]) == 0, "无效分页参数应返回空结果"
            else:
                # 有效参数应返回成功响应
                test_helper.assert_success_response(tag_response, "分页查询标签")
    
    @allure.story("重复操作")
    @allure.title("测试重复删除资源")
    def test_duplicate_delete_operations(self, create_test_tag, test_helper):
        """测试重复删除操作"""
        # 创建测试标签
        with allure.step("创建测试标签"):
            create_response, tag_id = create_test_tag()
            test_helper.assert_success_response(create_response, "创建测试标签")
            assert tag_id is not None, "创建标签失败"
        
        # 第一次删除
        with allure.step("第一次删除标签"):
            delete_response1 = tag_service.delete_tag(tag_id)
            test_helper.assert_success_response(delete_response1, "第一次删除标签")
        
        # 第二次删除（重复删除）
        with allure.step("第二次删除标签（重复删除）"):
            delete_response2 = tag_service.delete_tag(tag_id)
            test_helper.assert_error_response(delete_response2, message="重复删除应返回错误")
    
    @allure.story("并发操作")
    @allure.title("测试同名资源创建")
    def test_duplicate_name_creation(self, test_helper):
        """测试创建同名资源"""
        tag_name = f"duplicate_test_{test_data_generator.generate_timestamp_suffix()}"
        created_tag_ids = []
        
        try:
            # 创建第一个标签
            with allure.step("创建第一个标签"):
                response1 = tag_service.create_tag(tag_name, "AGENT")
                test_helper.assert_success_response(response1, "创建第一个标签")
                tag_id1 = tag_service.extract_tag_id(response1)
                if tag_id1:
                    created_tag_ids.append(tag_id1)
            
            # 尝试创建同名标签
            with allure.step("尝试创建同名标签"):
                response2 = tag_service.create_tag(tag_name, "AGENT")
                # 根据业务逻辑，可能允许同名或返回错误
                # 这里记录结果但不强制断言，因为不同系统的处理方式可能不同
                if tag_service.is_success_response(response2):
                    logger.info("系统允许创建同名标签")
                    tag_id2 = tag_service.extract_tag_id(response2)
                    if tag_id2:
                        created_tag_ids.append(tag_id2)
                else:
                    logger.info("系统不允许创建同名标签")
        
        finally:
            # 清理创建的标签
            for tag_id in created_tag_ids:
                try:
                    tag_service.delete_tag(tag_id)
                except Exception as e:
                    logger.warning(f"清理标签 {tag_id} 失败: {str(e)}")
    
    @allure.story("数据完整性")
    @allure.title("测试特殊字符处理")
    @pytest.mark.parametrize("special_name", [
        "test<script>alert('xss')</script>",  # XSS测试
        "test'; DROP TABLE tags; --",  # SQL注入测试
        "test\n\r\t",  # 换行符和制表符
        "test中文名称",  # 中文字符
        "test@#$%^&*()",  # 特殊符号
        "test   spaces   ",  # 多余空格
    ])
    def test_special_characters_handling(self, special_name, test_helper):
        """测试特殊字符处理"""
        created_tag_ids = []
        
        try:
            with allure.step(f"使用特殊字符创建标签: {repr(special_name)}"):
                response = tag_service.create_tag(special_name, "AGENT")
                
                if tag_service.is_success_response(response):
                    # 如果创建成功，验证数据完整性
                    tag_id = tag_service.extract_tag_id(response)
                    if tag_id:
                        created_tag_ids.append(tag_id)
                        
                        # 获取创建的标签，验证名称是否正确存储
                        get_response = tag_service.get_tag_by_id(tag_id)
                        if tag_service.is_success_response(get_response):
                            tag_data = get_response.get("data")
                            stored_name = tag_data.get("name")
                            logger.info(f"原始名称: {repr(special_name)}, 存储名称: {repr(stored_name)}")
                            # 注意：系统可能会对特殊字符进行转义或过滤
                else:
                    # 如果创建失败，记录错误信息
                    logger.info(f"特殊字符 {repr(special_name)} 创建失败: {response.get('message')}")
        
        finally:
            # 清理创建的标签
            for tag_id in created_tag_ids:
                try:
                    tag_service.delete_tag(tag_id)
                except Exception as e:
                    logger.warning(f"清理标签 {tag_id} 失败: {str(e)}")
    
    @allure.story("性能测试")
    @allure.title("测试大数据量查询")
    def test_large_data_query(self, test_helper):
        """测试大数据量查询"""
        with allure.step("测试大页大小查询"):
            query_data = {
                "page": 1,
                "size": 1000,  # 大页大小
                "filter": {},
                "sort": {"updateTime": "desc"}
            }
            
            response = tag_service.query_paged(query_data)
            # 验证系统是否能正常处理大页大小请求
            if tag_service.is_success_response(response):
                data = response.get("data", {})
                actual_size = len(data.get("list", []))
                logger.info(f"请求页大小: {query_data['size']}, 实际返回: {actual_size}")
                # 系统可能会限制最大页大小
                assert actual_size <= query_data["size"], "返回数据量不应超过请求的页大小"
            else:
                logger.info(f"大页大小查询被拒绝: {response.get('message')}")
                # 系统拒绝大页大小请求也是合理的
