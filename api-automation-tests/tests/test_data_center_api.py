"""
DataCenter API测试用例
"""
import pytest
from typing import Dict, Any
from services.data_center_service import data_center_service
from utils.logger import get_logger

logger = get_logger(__name__)


@pytest.mark.datacenter
class TestDataCenterAPI:
    """DataCenter API测试类"""
    
    def test_create_data_center_success(self, data_center_test_data, cleanup_resources, test_helper):
        """测试创建数据中心成功"""
        # 准备测试数据
        create_data = data_center_test_data["valid_create_data"]
        
        response = data_center_service.create_data_center(
            create_data["name"], 
            create_data.get("description")
        )
        
        test_helper.assert_success_response(response, "创建数据中心")
        test_helper.assert_response_data_not_empty(response, "创建数据中心响应数据")
        
        data = response.get("data")
        assert data["name"] == create_data["name"], "数据中心名称不匹配"
        if create_data.get("description"):
            assert data["description"] == create_data["description"], "数据中心描述不匹配"
        assert "id" in data, "响应中缺少数据中心ID"
        
            # 记录创建的资源用于清理
        data_center_id = data["id"]
        cleanup_resources["data_centers"].append(data_center_id)
    
    def test_create_data_center_minimal(self, cleanup_resources, test_helper):
        """测试仅使用必填字段创建数据中心"""
        name = "minimal_test_dc"
        
        with allure.step(f"创建数据中心(仅必填字段): {name}"):
        response = data_center_service.create_data_center(name)
        
        test_helper.assert_success_response(response, "创建数据中心")
        data = response.get("data")
        assert data["name"] == name, "数据中心名称不匹配"
        
            # 记录创建的资源用于清理
        cleanup_resources["data_centers"].append(data["id"])
    
    @pytest.mark.parametrize("invalid_data", [
        {},
        {"name": ""},
        {"description": "test"}
    ])
    def test_create_data_center_invalid_data(self, invalid_data, test_helper):
        """测试创建数据中心时的参数验证"""
        if "name" in invalid_data:
            response = data_center_service.create_data_center(
                invalid_data["name"], 
                invalid_data.get("description")
            )
        else:
                # 空数据情况，直接调用create方法
            response = data_center_service.create(invalid_data)
        
        test_helper.assert_error_response(response, message="无效数据应返回错误")
    
    def test_get_data_center_by_id(self, create_test_data_center, test_helper):
        """测试根据ID获取数据中心详情"""
        # 先创建一个数据中心
        create_response, data_center_id = create_test_data_center()
        test_helper.assert_success_response(create_response, "创建测试数据中心")
        assert data_center_id is not None, "创建数据中心失败，未获取到ID"
        
        # 获取数据中心详情
        response = data_center_service.get_data_center_by_id(data_center_id)
        
        test_helper.assert_success_response(response, "获取数据中心详情")
        test_helper.assert_response_data_not_empty(response, "数据中心详情数据")
        
        data = response.get("data")
        assert data["id"] == data_center_id, "数据中心ID不匹配"
        assert "name" in data, "响应中缺少数据中心名称"
        assert "createTime" in data, "响应中缺少创建时间"
        assert "updateTime" in data, "响应中缺少更新时间"
    
    def test_get_nonexistent_data_center(self, test_helper):
        """测试获取不存在的数据中心"""
        nonexistent_id = 999999
        
        response = data_center_service.get_data_center_by_id(nonexistent_id)
        
        test_helper.assert_error_response(response, message="不存在的数据中心应返回错误")
    
    def test_update_data_center(self, create_test_data_center, data_center_test_data, test_helper):
        """测试更新数据中心"""
        # 先创建一个数据中心
        create_response, data_center_id = create_test_data_center()
        test_helper.assert_success_response(create_response, "创建测试数据中心")
        assert data_center_id is not None, "创建数据中心失败，未获取到ID"
        
        # 更新数据中心
        update_data = data_center_test_data["valid_update_data"]
        response = data_center_service.update_data_center(
            data_center_id, 
            update_data["name"], 
            update_data.get("description")
        )
        
        test_helper.assert_success_response(response, "更新数据中心")
        
        # 验证更新结果
        get_response = data_center_service.get_data_center_by_id(data_center_id)
        test_helper.assert_success_response(get_response, "获取更新后的数据中心")
        data = get_response.get("data")
        assert data["name"] == update_data["name"], "数据中心名称未正确更新"
        if update_data.get("description"):
            assert data["description"] == update_data["description"], "数据中心描述未正确更新"
    
    def test_delete_data_center(self, create_test_data_center, test_helper):
        """测试删除数据中心"""
        # 先创建一个数据中心
        create_response, data_center_id = create_test_data_center()
        test_helper.assert_success_response(create_response, "创建测试数据中心")
        assert data_center_id is not None, "创建数据中心失败，未获取到ID"
        
        # 删除数据中心
        response = data_center_service.delete_data_center(data_center_id)
        
        test_helper.assert_success_response(response, "删除数据中心")
        
        # 验证删除结果
        get_response = data_center_service.get_data_center_by_id(data_center_id)
        test_helper.assert_error_response(get_response, message="已删除的数据中心应无法获取")
    
    def test_list_data_centers(self, test_helper):
        """测试获取数据中心列表"""
        response = data_center_service.get_all_data_centers()
        
        test_helper.assert_success_response(response, "获取数据中心列表")
        data = response.get("data")
        assert isinstance(data, list), "数据中心列表数据应为列表类型"
    
    def test_query_data_centers_paged(self, data_center_test_data, test_helper):
        """测试分页查询数据中心"""
        query_data = data_center_test_data["valid_query_data"]
        
        response = data_center_service.query_paged(query_data)
        
        test_helper.assert_paged_response(response, query_data["page"], query_data["size"])
    
    def test_query_data_centers_by_name(self, create_test_data_center, test_helper):
        """测试按名称查询数据中心"""
        # 先创建一个数据中心
        create_response, data_center_id = create_test_data_center("search_test_dc")
        test_helper.assert_success_response(create_response, "创建测试数据中心")
        created_data = create_response.get("data")
        created_name = created_data["name"]
        
        # 按名称查询
        response = data_center_service.query_data_centers(name=created_name)
        
        test_helper.assert_success_response(response, "按名称查询数据中心")
        data = response.get("data")
        data_centers = data.get("list", [])
        
            # 验证查询结果包含创建的数据中心
        found = False
        for dc in data_centers:
            if dc["id"] == data_center_id:
                found = True
                assert dc["name"] == created_name, "查询结果中数据中心名称不匹配"
                break
        
        assert found, "查询结果中未找到创建的数据中心"
    
    def test_get_data_center_usage(self, create_test_data_center, test_helper):
        """测试获取数据中心使用情况"""
        # 先创建一个数据中心
        create_response, data_center_id = create_test_data_center()
        test_helper.assert_success_response(create_response, "创建测试数据中心")
        assert data_center_id is not None, "创建数据中心失败，未获取到ID"
        
        # 获取数据中心使用情况
        response = data_center_service.get_data_center_usage(data_center_id)
        
        test_helper.assert_success_response(response, "获取数据中心使用情况")
        data = response.get("data")
        assert isinstance(data, list), "使用情况数据应为列表类型"
    
    def test_query_data_center_reference(self, create_test_data_center, test_helper):
        """测试查询数据中心关联数据"""
        # 先创建一个数据中心
        create_response, data_center_id = create_test_data_center()
        test_helper.assert_success_response(create_response, "创建测试数据中心")
        assert data_center_id is not None, "创建数据中心失败，未获取到ID"
        
        # 查询关联数据
        response = data_center_service.query_reference(data_center_id)
        
        test_helper.assert_success_response(response, "查询数据中心关联数据")
        data = response.get("data")
        assert isinstance(data, list), "关联数据应为列表类型"
