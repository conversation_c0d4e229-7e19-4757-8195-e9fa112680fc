"""
API测试配置文件
"""
import os
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings:
    """测试配置类"""
    
    # API基础配置
    BASE_URL: str = os.getenv("BASE_URL", "http://localhost:8080")
    API_VERSION: str = os.getenv("API_VERSION", "v2")
    
    @property
    def api_base_url(self) -> str:
        """获取API基础URL"""
        return f"{self.BASE_URL}/api/{self.API_VERSION}"
    
    # 认证配置
    TOKEN: Optional[str] = os.getenv("TOKEN")

    # 请求配置
    TIMEOUT: int = int(os.getenv("TIMEOUT", "30"))
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # 测试配置
    CLEANUP_AFTER_TEST: bool = os.getenv("CLEANUP_AFTER_TEST", "true").lower() == "true"
    GENERATE_TEST_REPORT: bool = os.getenv("GENERATE_TEST_REPORT", "true").lower() == "true"


# 全局设置实例
settings = Settings()


# API端点配置
class APIEndpoints:
    """API端点配置"""
    
    # Tag相关端点
    TAG_LIST = "ingestion/tag/list"
    TAG_QUERY = "ingestion/tag/query"
    TAG_CREATE = "ingestion/tag"
    TAG_UPDATE = "ingestion/tag/{id}"
    TAG_DELETE = "ingestion/tag/{id}"
    TAG_GET = "ingestion/tag/{id}"
    TAG_USAGE = "ingestion/tag/{id}/usage"
    TAG_LIST_BY_TYPE = "ingestion/tag/listByType"
    
    # DataCenter相关端点
    DATA_CENTER_LIST = "ingestion/data/center/list"
    DATA_CENTER_QUERY = "ingestion/data/center/query"
    DATA_CENTER_CREATE = "ingestion/data/center"
    DATA_CENTER_UPDATE = "ingestion/data/center/{id}"
    DATA_CENTER_DELETE = "ingestion/data/center/{id}"
    DATA_CENTER_GET = "ingestion/data/center/{id}"
    DATA_CENTER_USAGE = "ingestion/data/center/{id}/usage"
    DATA_CENTER_REFERENCE = "ingestion/data/center/reference"
    
    # Cluster相关端点
    CLUSTER_LIST = "ingestion/cluster/list"
    CLUSTER_QUERY = "ingestion/cluster/query"
    CLUSTER_CREATE = "ingestion/cluster"
    CLUSTER_UPDATE = "ingestion/cluster/{id}"
    CLUSTER_DELETE = "ingestion/cluster/{id}"
    CLUSTER_GET = "ingestion/cluster/{id}"
    CLUSTER_GET_BY_NAME = "ingestion/cluster-name/{name}"
    CLUSTER_DEFAULT = "ingestion/cluster/default/{supportOpts}"


# HTTP状态码
class HTTPStatus:
    """HTTP状态码常量"""
    OK = 200
    CREATED = 201
    NO_CONTENT = 204
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_SERVER_ERROR = 500


# 测试数据配置
class TestDataConfig:
    """测试数据配置"""
    
    # Tag测试数据
    TAG_TYPES = ["AGENT", "TABLE", "PIPELINE", "JOB"]
    
    # 默认测试数据
    DEFAULT_TAG_DATA = {
        "name": "test_tag",
        "tagType": "AGENT"
    }
    
    DEFAULT_DATA_CENTER_DATA = {
        "name": "test_data_center",
        "description": "Test data center description"
    }
    
    # 分页查询默认参数
    DEFAULT_QUERY_PARAMS = {
        "page": 1,
        "size": 10,
        "filter": {},
        "sort": {}
    }


# 导出配置
api_endpoints = APIEndpoints()
http_status = HTTPStatus()
test_data_config = TestDataConfig()
