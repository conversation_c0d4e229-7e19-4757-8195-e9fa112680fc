# JAX API自动化测试 Makefile

.PHONY: help install setup test test-tag test-datacenter test-crud test-error test-smoke test-parallel clean report serve-report

# 默认目标
help:
	@echo "JAX API自动化测试 - 可用命令:"
	@echo ""
	@echo "安装和设置:"
	@echo "  install      - 安装依赖包"
	@echo "  setup        - 设置测试环境"
	@echo ""
	@echo "运行测试:"
	@echo "  test         - 运行所有测试"
	@echo "  test-tag     - 运行Tag API测试"
	@echo "  test-datacenter - 运行DataCenter API测试"
	@echo "  test-crud    - 运行CRUD操作测试"
	@echo "  test-error   - 运行错误处理测试"
	@echo "  test-smoke   - 运行冒烟测试"
	@echo "  test-parallel - 并行运行所有测试"
	@echo ""
	@echo "报告和清理:"
	@echo "  report       - 生成测试报告"
	@echo "  serve-report - 启动报告服务器"
	@echo "  clean        - 清理测试文件"
	@echo ""
	@echo "示例:"
	@echo "  make install && make setup && make test"

# 安装依赖
install:
	@echo "安装Python依赖包..."
	pip install -r requirements.txt
	@echo "依赖包安装完成"

# 设置环境
setup:
	@echo "设置测试环境..."
	python run_tests.py --setup-only
	@if [ ! -f .env ]; then \
		echo "复制环境变量配置文件..."; \
		cp .env.example .env; \
		echo "请编辑 .env 文件配置API地址等信息"; \
	fi
	@echo "环境设置完成"

# 运行所有测试
test:
	@echo "运行所有测试..."
	python run_tests.py --type all --verbose

# 运行Tag API测试
test-tag:
	@echo "运行Tag API测试..."
	python run_tests.py --type tag --verbose

# 运行DataCenter API测试
test-datacenter:
	@echo "运行DataCenter API测试..."
	python run_tests.py --type datacenter --verbose

# 运行CRUD操作测试
test-crud:
	@echo "运行CRUD操作测试..."
	python run_tests.py --type crud --verbose

# 运行错误处理测试
test-error:
	@echo "运行错误处理测试..."
	python run_tests.py --type error --verbose

# 运行冒烟测试
test-smoke:
	@echo "运行冒烟测试..."
	python run_tests.py --markers smoke --verbose

# 并行运行测试
test-parallel:
	@echo "并行运行所有测试..."
	python run_tests.py --type all --parallel --verbose

# 生成测试报告
report:
	@echo "生成测试报告..."
	python run_tests.py --type all --report

# 启动报告服务器
serve-report:
	@echo "启动Allure报告服务器..."
	@if [ -d "reports/allure-report" ]; then \
		allure open reports/allure-report; \
	else \
		echo "报告目录不存在，请先运行 make report"; \
	fi

# 清理测试文件
clean:
	@echo "清理测试文件..."
	rm -rf logs/*.log
	rm -rf reports/report.html
	rm -rf reports/allure-results/*
	rm -rf reports/allure-report/*
	rm -rf __pycache__
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} +
	@echo "清理完成"

# 快速开始（安装、设置、运行冒烟测试）
quickstart: install setup test-smoke
	@echo "快速开始完成！"

# 完整测试流程（安装、设置、测试、报告）
full-test: install setup test report
	@echo "完整测试流程完成！"

# 检查代码质量
lint:
	@echo "检查代码质量..."
	@if command -v flake8 >/dev/null 2>&1; then \
		flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics; \
	else \
		echo "flake8未安装，跳过代码检查"; \
	fi

# 格式化代码
format:
	@echo "格式化代码..."
	@if command -v black >/dev/null 2>&1; then \
		black . --line-length 100; \
	else \
		echo "black未安装，跳过代码格式化"; \
	fi

# 开发环境设置
dev-setup: install
	@echo "设置开发环境..."
	pip install black flake8 mypy
	@echo "开发环境设置完成"

# 检查环境
check-env:
	@echo "检查测试环境..."
	@python --version
	@echo "Python版本检查完成"
	@if [ -f .env ]; then \
		echo ".env文件存在"; \
	else \
		echo "警告: .env文件不存在，请运行 make setup"; \
	fi
	@if [ -f requirements.txt ]; then \
		echo "依赖文件存在"; \
	else \
		echo "错误: requirements.txt文件不存在"; \
	fi
