"""
HTTP客户端工具
"""
import json
import time
from typing import Dict, Any, Optional, Union
import requests
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

from config.settings import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class HTTPClient:
    """HTTP客户端类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = settings.api_base_url
        self.timeout = settings.TIMEOUT
        self._setup_session()
    
    def _setup_session(self):
        """设置会话配置"""
        # 设置重试策略
        retry_strategy = Retry(
            total=3,  # 默认重试3次
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认头部
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "API-Test-Client/1.0"
        })
        
        # 如果有token，添加认证头
        if settings.TOKEN:
            self.session.headers.update({
                "Authorization": f"Bearer {settings.TOKEN}"
            })
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        if endpoint.startswith("http"):
            return endpoint
        return f"{self.base_url}/{endpoint.lstrip('/')}"
    
    def _log_request(self, method: str, url: str, **kwargs):
        """记录请求日志"""
        logger.info(f"发送 {method} 请求: {url}")
        if kwargs.get("json"):
            logger.debug(f"请求体: {json.dumps(kwargs['json'], ensure_ascii=False, indent=2)}")
        if kwargs.get("params"):
            logger.debug(f"查询参数: {kwargs['params']}")
    
    def _log_response(self, response: requests.Response, start_time: float):
        """记录响应日志"""
        duration = time.time() - start_time
        logger.info(f"响应状态: {response.status_code}, 耗时: {duration:.2f}s")
        
        try:
            response_data = response.json()
            logger.debug(f"响应体: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        except (json.JSONDecodeError, ValueError):
            logger.debug(f"响应体: {response.text}")
    
    def request(
        self,
        method: str,
        endpoint: str,
        json_data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> requests.Response:
        """发送HTTP请求"""
        url = self._build_url(endpoint)
        start_time = time.time()
        
        # 合并头部
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        # 记录请求日志
        self._log_request(method, url, json=json_data, params=params)
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                json=json_data,
                params=params,
                headers=request_headers,
                timeout=self.timeout,
                **kwargs
            )
            
            # 记录响应日志
            self._log_response(response, start_time)
            
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            raise
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """发送GET请求"""
        return self.request("GET", endpoint, params=params, **kwargs)
    
    def post(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """发送POST请求"""
        return self.request("POST", endpoint, json_data=json_data, **kwargs)
    
    def put(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """发送PUT请求"""
        return self.request("PUT", endpoint, json_data=json_data, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> requests.Response:
        """发送DELETE请求"""
        return self.request("DELETE", endpoint, **kwargs)
    
    def patch(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """发送PATCH请求"""
        return self.request("PATCH", endpoint, json_data=json_data, **kwargs)


# 全局HTTP客户端实例
http_client = HTTPClient()
