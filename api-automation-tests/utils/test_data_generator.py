"""
测试数据生成器
"""
import random
import string
from datetime import datetime
from typing import Dict, Any, List
from faker import Faker

fake = Faker('zh_CN')


class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_random_string(length: int = 10, prefix: str = "") -> str:
        """生成随机字符串"""
        random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))
        return f"{prefix}{random_str}" if prefix else random_str
    
    @staticmethod
    def generate_timestamp_suffix() -> str:
        """生成时间戳后缀"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    @staticmethod
    def generate_tag_data(name: str = None, tag_type: str = "AGENT") -> Dict[str, Any]:
        """生成Tag测试数据"""
        if not name:
            name = f"test_tag_{TestDataGenerator.generate_timestamp_suffix()}"
        
        return {
            "name": name,
            "tagType": tag_type
        }
    
    @staticmethod
    def generate_tag_update_data(name: str = None) -> Dict[str, Any]:
        """生成Tag更新测试数据"""
        if not name:
            name = f"updated_tag_{TestDataGenerator.generate_timestamp_suffix()}"
        
        return {
            "name": name
        }
    
    @staticmethod
    def generate_tag_query_data(
        name: str = None,
        tag_type: str = None,
        page: int = 1,
        size: int = 10
    ) -> Dict[str, Any]:
        """生成Tag查询测试数据"""
        filter_data = {}
        if name:
            filter_data["name"] = name
        if tag_type:
            filter_data["tagType"] = tag_type
        
        return {
            "page": page,
            "size": size,
            "filter": filter_data,
            "sort": {
                "updateTime": "desc"
            }
        }
    
    @staticmethod
    def generate_data_center_data(name: str = None, description: str = None) -> Dict[str, Any]:
        """生成DataCenter测试数据"""
        if not name:
            name = f"test_datacenter_{TestDataGenerator.generate_timestamp_suffix()}"
        if not description:
            description = f"Test data center created at {datetime.now()}"
        
        return {
            "name": name,
            "description": description
        }
    
    @staticmethod
    def generate_data_center_update_data(name: str = None, description: str = None) -> Dict[str, Any]:
        """生成DataCenter更新测试数据"""
        if not name:
            name = f"updated_datacenter_{TestDataGenerator.generate_timestamp_suffix()}"
        if not description:
            description = f"Updated data center at {datetime.now()}"
        
        return {
            "name": name,
            "description": description
        }
    
    @staticmethod
    def generate_data_center_query_data(
        name: str = None,
        page: int = 1,
        size: int = 10
    ) -> Dict[str, Any]:
        """生成DataCenter查询测试数据"""
        filter_data = {}
        if name:
            filter_data["name"] = name
        
        return {
            "page": page,
            "size": size,
            "filter": filter_data,
            "sort": {
                "createTime": "desc",
                "updateTime": "desc"
            }
        }
    
    @staticmethod
    def generate_cluster_data(
        cluster_name: str = None,
        cluster_type: str = "YARN",
        description: str = None
    ) -> Dict[str, Any]:
        """生成Cluster测试数据"""
        if not cluster_name:
            cluster_name = f"test_cluster_{TestDataGenerator.generate_timestamp_suffix()}"
        if not description:
            description = f"Test cluster created at {datetime.now()}"
        
        return {
            "clusterName": cluster_name,
            "clusterType": cluster_type,
            "clusterDescription": description,
            "defaultFlinkCluster": False,
            "defaultSparkCluster": False,
            "defaultMarayarnCluster": False,
            "setting": {
                "resourceManager": "localhost:8088",
                "nameNode": "localhost:9000"
            }
        }


# 全局测试数据生成器实例
test_data_generator = TestDataGenerator()
