{"name": "测试配置加载", "status": "passed", "description": "测试配置是否正确加载", "steps": [{"name": "验证基础配置", "status": "passed", "start": 1751363678764, "stop": 1751363678764}, {"name": "验证API基础URL", "status": "passed", "start": 1751363678764, "stop": 1751363678764}], "attachments": [{"name": "stdout", "source": "46f8a491-d196-4913-ae1c-08b6316ade33-attachment.txt", "type": "text/plain"}], "start": 1751363678764, "stop": 1751363678764, "uuid": "91072739-da9a-4c17-a5c9-00850d665c1b", "historyId": "e1582e02d6460f459743991d9738d54e", "testCaseId": "e1582e02d6460f459743991d9738d54e", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_configuration_loading", "labels": [{"name": "story", "value": "基础功能验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}