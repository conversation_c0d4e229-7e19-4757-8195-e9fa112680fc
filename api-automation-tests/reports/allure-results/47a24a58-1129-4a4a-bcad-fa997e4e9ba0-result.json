{"name": "测试日志功能", "status": "passed", "description": "测试日志功能是否正常", "steps": [{"name": "测试不同级别的日志输出", "status": "passed", "start": 1751363678762, "stop": 1751363678763}], "attachments": [{"name": "stdout", "source": "f0c076d4-c5d4-4cdf-beb8-5d528f21541f-attachment.txt", "type": "text/plain"}], "start": 1751363678762, "stop": 1751363678763, "uuid": "41938ca4-8ede-44c9-9dca-1cf5fd2a7e64", "historyId": "9e02d04bd41e60c1755b059fa9842c1d", "testCaseId": "9e02d04bd41e60c1755b059fa9842c1d", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_logger_functionality", "labels": [{"name": "story", "value": "基础功能验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}