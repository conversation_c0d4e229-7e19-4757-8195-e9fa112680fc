{"name": "测试环境变量", "status": "passed", "description": "测试环境变量是否正确设置", "steps": [{"name": "检查必需的环境变量", "status": "passed", "start": 1751363678770, "stop": 1751363678770}, {"name": "检查可选的环境变量", "status": "passed", "start": 1751363678770, "stop": 1751363678770}], "attachments": [{"name": "stdout", "source": "884aaaca-0599-4906-8e83-67636b7fe36c-attachment.txt", "type": "text/plain"}], "start": 1751363678770, "stop": 1751363678770, "uuid": "5540c94b-7ce9-447c-84fd-e34b90b35da2", "historyId": "a8d453bc7c00d59a4a2a9d7b0361c4d3", "testCaseId": "a8d453bc7c00d59a4a2a9d7b0361c4d3", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_environment_variables", "labels": [{"name": "story", "value": "环境验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}