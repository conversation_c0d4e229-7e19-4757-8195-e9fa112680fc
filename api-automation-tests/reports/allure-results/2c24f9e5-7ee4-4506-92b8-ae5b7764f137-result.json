{"name": "测试URL构建", "status": "passed", "description": "测试URL构建功能", "steps": [{"name": "测试相对路径URL构建", "status": "passed", "start": 1751363678768, "stop": 1751363678768}, {"name": "测试绝对路径URL构建", "status": "passed", "start": 1751363678768, "stop": 1751363678768}], "attachments": [{"name": "stdout", "source": "1358d31e-c097-4c51-b79e-bc38eb087a4a-attachment.txt", "type": "text/plain"}], "start": 1751363678768, "stop": 1751363678768, "uuid": "c60ba04f-ec76-4da2-8c89-4f6f6f3c2a0c", "historyId": "d9e6a46acb790bbc964d6b9f46204da6", "testCaseId": "d9e6a46acb790bbc964d6b9f46204da6", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_url_building", "labels": [{"name": "story", "value": "基础功能验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}