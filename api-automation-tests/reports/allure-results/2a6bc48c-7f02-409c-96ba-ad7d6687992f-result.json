{"name": "测试框架完整性", "status": "passed", "description": "测试框架的完整性", "steps": [{"name": "验证所有必需模块可导入", "status": "passed", "start": 1751363678771, "stop": 1751363678771}, {"name": "验证服务实例", "status": "passed", "start": 1751363678771, "stop": 1751363678771}], "attachments": [{"name": "stdout", "source": "e5a11e15-de7b-468b-9975-ff55e1a0a0be-attachment.txt", "type": "text/plain"}], "start": 1751363678771, "stop": 1751363678772, "uuid": "addbad44-2173-4049-99f9-4480f63d003e", "historyId": "fe6434dc6f1c464c385403899ca56d13", "testCaseId": "fe6434dc6f1c464c385403899ca56d13", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_framework_integrity", "labels": [{"name": "story", "value": "框架完整性验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}