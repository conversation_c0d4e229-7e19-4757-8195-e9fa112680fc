{"name": "测试辅助工具功能", "status": "passed", "description": "测试辅助工具功能", "steps": [{"name": "测试成功响应断言", "status": "passed", "start": 1751363678769, "stop": 1751363678769}, {"name": "测试错误响应断言", "status": "passed", "start": 1751363678769, "stop": 1751363678769}, {"name": "测试数据非空断言", "status": "passed", "start": 1751363678769, "stop": 1751363678769}], "attachments": [{"name": "stdout", "source": "a8c65b63-1467-4eee-b03c-3bb4f48bff63-attachment.txt", "type": "text/plain"}], "start": 1751363678769, "stop": 1751363678769, "uuid": "183baad7-83e0-4f4c-8699-23d05578346b", "historyId": "60381a50437f6859fb42cfab3def79c3", "testCaseId": "60381a50437f6859fb42cfab3def79c3", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_helper_utilities", "labels": [{"name": "story", "value": "测试工具验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}