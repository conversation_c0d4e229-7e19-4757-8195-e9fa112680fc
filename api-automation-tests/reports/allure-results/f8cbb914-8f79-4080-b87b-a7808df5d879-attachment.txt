[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_data_generator[0m:[36m57[0m | [1m生成的Tag数据: {'name': 'test_tag_20250701_175438', 'tagType': 'AGENT'}[0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_data_generator[0m:[36m64[0m | [1m生成的DataCenter数据: {'name': 'test_datacenter_20250701_175438', 'description': 'Test data center created at 2025-07-01 17:54:38.765441'}[0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_data_generator[0m:[36m72[0m | [1m生成的查询数据: {'page': 1, 'size': 10, 'filter': {}, 'sort': {'updateTime': 'desc'}}[0m
