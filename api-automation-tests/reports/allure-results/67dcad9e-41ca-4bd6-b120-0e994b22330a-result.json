{"name": "测试HTTP客户端初始化", "status": "passed", "description": "测试HTTP客户端是否正确初始化", "steps": [{"name": "验证HTTP客户端属性", "status": "passed", "start": 1751363678767, "stop": 1751363678767}, {"name": "验证HTTP客户端头部", "status": "passed", "start": 1751363678767, "stop": 1751363678767}], "attachments": [{"name": "stdout", "source": "d937e0eb-ae51-4648-9a2b-035ccef12aba-attachment.txt", "type": "text/plain"}], "start": 1751363678767, "stop": 1751363678767, "uuid": "b047b586-e72c-4f8b-af67-941dd407e482", "historyId": "1402b92ad5b5ee1608e424eb36a63f63", "testCaseId": "1402b92ad5b5ee1608e424eb36a63f63", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_http_client_initialization", "labels": [{"name": "story", "value": "基础功能验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}