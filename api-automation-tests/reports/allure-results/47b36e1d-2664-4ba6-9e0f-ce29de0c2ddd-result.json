{"name": "测试数据生成器", "status": "passed", "description": "测试数据生成器功能", "steps": [{"name": "测试Tag数据生成", "status": "passed", "start": 1751363678765, "stop": 1751363678765}, {"name": "测试DataCenter数据生成", "status": "passed", "start": 1751363678765, "stop": 1751363678766}, {"name": "测试查询数据生成", "status": "passed", "start": 1751363678766, "stop": 1751363678766}], "attachments": [{"name": "stdout", "source": "f8cbb914-8f79-4080-b87b-a7808df5d879-attachment.txt", "type": "text/plain"}], "start": 1751363678765, "stop": 1751363678766, "uuid": "f4566c0c-0809-454c-8473-901844851fef", "historyId": "34054ae27fa8242b2103554f56ba7de1", "testCaseId": "34054ae27fa8242b2103554f56ba7de1", "fullName": "tests.test_framework_validation.TestFrameworkValidation#test_data_generator", "labels": [{"name": "story", "value": "基础功能验证"}, {"name": "feature", "value": "框架验证"}, {"name": "parentSuite", "value": "tests"}, {"name": "suite", "value": "test_framework_validation"}, {"name": "subSuite", "value": "TestFrameworkValidation"}, {"name": "host", "value": "wangdideMacBook-Pro.local"}, {"name": "thread", "value": "35859-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.test_framework_validation"}]}