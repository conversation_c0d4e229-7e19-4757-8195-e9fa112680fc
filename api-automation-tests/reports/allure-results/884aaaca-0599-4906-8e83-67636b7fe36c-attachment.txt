[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_environment_variables[0m:[36m198[0m | [1mBASE_URL: http://192.168.110.175:9905[0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_environment_variables[0m:[36m199[0m | [1mAPI_VERSION: v2[0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_environment_variables[0m:[36m200[0m | [1mTIMEOUT: 30[0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_environment_variables[0m:[36m201[0m | [1mLOG_LEVEL: INFO[0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_environment_variables[0m:[36m208[0m | [1mTOKEN: [未设置][0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_environment_variables[0m:[36m210[0m | [1mCLEANUP_AFTER_TEST: True[0m
[32m2025-07-01 17:54:38[0m | [1mINFO    [0m | [36mtests.test_framework_validation[0m:[36mtest_environment_variables[0m:[36m211[0m | [1mGENERATE_TEST_REPORT: True[0m
