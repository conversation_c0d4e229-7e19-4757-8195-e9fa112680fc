# 配置变更说明

## 已移除的配置项和测试类型

根据需求，已从测试框架中移除以下配置项和测试类型：

### 1. 移除的配置项

#### 认证相关配置
- ❌ `USERNAME` - 用户名配置
- ❌ `PASSWORD` - 密码配置
- ✅ `TOKEN` - JWT Token配置（保留）

#### 网络请求配置
- ❌ `RETRY_COUNT` - 重试次数配置（现在硬编码为3次）
- ✅ `TIMEOUT` - 请求超时时间（保留）

#### 数据库配置
- ❌ `DB_HOST` - 数据库主机
- ❌ `DB_PORT` - 数据库端口
- ❌ `DB_NAME` - 数据库名称
- ❌ `DB_USER` - 数据库用户名
- ❌ `DB_PASSWORD` - 数据库密码

### 2. 简化的测试类型

#### 移除的测试类型
- ❌ `crud` - CRUD操作测试
- ❌ `error` - 错误处理测试
- ❌ `smoke` - 冒烟测试

#### 保留的测试类型
- ✅ `all` - 运行所有测试
- ✅ `tag` - Tag API测试
- ✅ `datacenter` - DataCenter API测试

### 3. 移除的测试文件
- ❌ `tests/test_crud_operations.py` - CRUD操作测试
- ❌ `tests/test_error_handling.py` - 错误处理测试

### 4. 简化的pytest标记
- ❌ `smoke` - 冒烟测试标记
- ❌ `regression` - 回归测试标记
- ❌ `crud` - CRUD操作测试标记
- ❌ `error` - 错误处理测试标记
- ❌ `boundary` - 边界测试标记
- ❌ `performance` - 性能测试标记
- ✅ `tag` - Tag API测试标记
- ✅ `datacenter` - DataCenter API测试标记
- ✅ `integration` - 集成测试标记
- ✅ `slow` - 慢速测试标记

## 当前保留的配置项

### API基础配置
```bash
BASE_URL=http://localhost:8080    # API服务地址
API_VERSION=v2                    # API版本
```

### 认证配置
```bash
TOKEN=                           # JWT Token（可选）
```

### 测试配置
```bash
TIMEOUT=30                       # 请求超时时间（秒）
LOG_LEVEL=INFO                   # 日志级别
CLEANUP_AFTER_TEST=true          # 测试后清理数据
GENERATE_TEST_REPORT=true        # 生成测试报告
```

## 当前测试运行方式

### 1. 使用运行脚本
```bash
# 运行所有测试
python run_tests.py

# 运行Tag API测试
python run_tests.py --type tag

# 运行DataCenter API测试
python run_tests.py --type datacenter

# 并行执行
python run_tests.py --parallel

# 生成报告
python run_tests.py --report
```

### 2. 使用pytest直接运行
```bash
# 运行所有测试
pytest

# 运行Tag API测试
pytest -m tag

# 运行DataCenter API测试
pytest -m datacenter

# 运行特定测试文件
pytest tests/test_tag_api.py
pytest tests/test_data_center_api.py
```

### 3. 使用Makefile
```bash
# 运行所有测试
make test

# 运行Tag API测试
make test-tag

# 运行DataCenter API测试
make test-datacenter

# 并行测试
make test-parallel
```

## 影响的文件

以下文件已更新以反映配置变更：

### 配置文件
- `config/settings.py` - 移除了相关配置类属性
- `.env.example` - 移除了相关环境变量示例
- `pytest.ini` - 简化了测试标记

### 工具文件
- `utils/http_client.py` - 重试次数硬编码为3次
- `run_tests.py` - 简化了测试类型选项
- `quick_start.py` - 更新了示例测试

### 测试文件
- `conftest.py` - 更新了日志输出
- `tests/test_framework_validation.py` - 移除了相关配置验证
- `tests/test_tag_api.py` - 添加了@pytest.mark.tag标记
- `tests/test_data_center_api.py` - 添加了@pytest.mark.datacenter标记

### 构建文件
- `Makefile` - 简化了测试命令

### 文档文件
- `README.md` - 更新了使用说明
- `USAGE_EXAMPLES.md` - 更新了示例

## 迁移指南

如果您之前使用了被移除的功能，请按以下方式迁移：

### 测试类型迁移
**之前：**
```bash
python run_tests.py --type crud
python run_tests.py --type error
python run_tests.py --type smoke
```

**现在：**
```bash
# CRUD和错误处理测试已整合到tag和datacenter测试中
python run_tests.py --type tag
python run_tests.py --type datacenter
python run_tests.py --type all
```

### pytest标记迁移
**之前：**
```bash
pytest -m smoke
pytest -m crud
pytest -m error
```

**现在：**
```bash
pytest -m tag
pytest -m datacenter
pytest  # 运行所有测试
```

## 配置验证

运行以下命令验证配置是否正确：

```bash
# 验证框架配置
python run_tests.py --type all

# 快速验证
python quick_start.py
```

## 注意事项

1. **简化原则**：框架现在更加专注于核心API测试功能
2. **测试覆盖**：虽然移除了独立的CRUD和错误处理测试文件，但相关测试逻辑已整合到tag和datacenter测试中
3. **扩展性**：如果需要添加新的API资源测试，只需按照tag和datacenter的模式创建新的测试文件
4. **标记使用**：现在使用更简洁的标记系统，主要按API资源类型分类
