version=$(xmllint --xpath "//*[local-name()='project']/*[local-name()='version']/text()" pom.xml)
project="jax"
app_name="jax-super-web"
tag=$version-$(git rev-parse --short HEAD)
harborUsername="admin"
harborPassword="Harbor12345"
harborAddress="hub.eoitek.net"

mkdir -p /etc/docker/certs.d/hub.eoitek.net/
cp hub.eoitek.net.crt /etc/docker/certs.d/hub.eoitek.net/ca.crt
ls -al /etc/docker/certs.d/hub.eoitek.net/

docker login -u $harborUsername -p $harborPassword $harborAddress

imageName="$harborAddress/$project/$app_name:${tag}"
latestImage="$harborAddress/$project/$app_name:latest"

echo $version
docker build  -t   $imageName  ./
docker build  -t   $latestImage  ./
docker push        $imageName
docker push        $latestImage
docker rmi         $imageName  $latestImage
