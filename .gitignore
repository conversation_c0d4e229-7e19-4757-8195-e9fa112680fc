# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

# umi
.umirc.local.ts
config.local.ts
.umi
.umi-production
.umi-test
dist

# storybook
storybook-static

# local history
.history

# build
/dist
*.zip