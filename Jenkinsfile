pipeline {
    agent {
        node {
            label "master"
        }
    }
    parameters {
        string(name: 'FTP', defaultValue: 'ftp://*************/Product_Release/16.JaxSuper/dev/', description: '如果配置了则会上传ftp')
    }
    stages {
        stage('Package') {
            agent {
                docker {
                    image 'hub.eoitek.net/third/devops-tools:1.1'
                    args '-v /home/<USER>/.m2:/root/.m2 -v /etc/localtime:/etc/localtime:ro -v /root/.ssh.sensor:/root/.ssh'
                    reuseNode true
                }
            }
            steps {
                sh "make package-all"
            }
        }
        stage('Upload FTP') {
            when {
                expression { return params.FTP != '' }
            }
            agent {
                docker {
                    image 'hub.eoitek.net/third/devops-tools:1.1'
                    args '-v /home/<USER>/.m2:/root/.m2 -v /etc/localtime:/etc/localtime:ro -v /root/.ssh.sensor:/root/.ssh'
                    reuseNode true
                }
            }
            steps {
                script {
                    SMALL_TAR = sh(returnStdout: true, script: 'find tmp -name "jax-super-small-*.tar.gz"').trim()
                    ALL_TAR = sh(returnStdout: true, script: 'find tmp -name "jax-super-all-*.tar.gz"').trim()
                }
                sh "curl -u development:Eoi123456! -T ${SMALL_TAR} ${params.FTP}"
                sh "curl -u development:Eoi123456! -T ${ALL_TAR} ${params.FTP}"
            }
        }
    }
    post {
        success {
            archiveArtifacts artifacts: 'tmp/', onlyIfSuccessful: true
            cleanWs cleanWhenFailure: true, cleanWhenUnstable: false, deleteDirs: true, notFailBuild: true
        }
        failure {
            echo "failed"
        }
    }
}