package com.eoi.jax.json.schema.builder;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.json.schema.JsSchema;
import com.eoi.jax.json.schema.consts.ICodeEnum;
import com.eoi.jax.json.schema.consts.SchemaKeyword;
import com.eoi.jax.json.schema.consts.SchemaVersion;
import com.eoi.jax.json.schema.util.NumberUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/14
 */
public class DefaultSchemaJsonBuilder {
    private final JsSchema schema;
    private Options options;

    public DefaultSchemaJsonBuilder(JsSchema schema) {
        this.options = createDefaultOptions();
        this.schema = schema;
    }

    public DefaultSchemaJsonBuilder with(Options options) {
        this.options = options;
        return this;
    }

    public Options createDefaultOptions() {
        ObjectMapper mapper = createDefaultMapper();
        return new Options(SchemaVersion.DRAFT_2020_12, mapper);
    }

    public ObjectMapper createDefaultMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);
        mapper.setNodeFactory(JsonNodeFactory.withExactBigDecimals(true));
        return mapper;
    }

    public JsonNode build() {
        return generateJson(schema);
    }

    public ObjectNode generateJson(JsSchema buildSchema) {
        ObjectNode objectNode = new JsonBuilder(options, buildSchema).build();
        if (buildSchema.getProperties() != null) {
            ObjectNode propertiesNode = this.options.getMapper().createObjectNode();
            for (JsSchema property : buildSchema.getProperties()) {
                ObjectNode propertyNode = generateJson(property);
                propertiesNode.set(property.getName(), propertyNode);
            }
            objectNode.set(SchemaKeyword.TAG_PROPERTIES.code(), propertiesNode);
        }
        if (buildSchema.getItems() != null) {
            ObjectNode itemsNode = generateJson(buildSchema.getItems());
            objectNode.set(SchemaKeyword.TAG_ITEMS.code(), itemsNode);
        }
        return objectNode;
    }

    public static class JsonBuilder {
        private final Options buildOptions;
        private final JsSchema buildSchema;
        private final ObjectNode objectNode;

        public JsonBuilder(Options buildOptions, JsSchema buildSchema) {
            this.buildOptions = buildOptions;
            this.buildSchema = buildSchema;
            this.objectNode = buildOptions.getMapper().createObjectNode();
        }

        public ObjectNode build() {
            putExist(SchemaKeyword.TAG_TYPE, buildSchema.getType());
            putExist(SchemaKeyword.TAG_TITLE, buildSchema.getTitle());
            putExist(SchemaKeyword.TAG_FORMAT, buildSchema.getFormat());
            putExist(SchemaKeyword.TAG_DESCRIPTION, buildSchema.getTitle());
            putExist(SchemaKeyword.TAG_PATTERN, buildSchema.getPattern());
            putExist(SchemaKeyword.TAG_LENGTH_MIN, buildSchema.getMinLength());
            putExist(SchemaKeyword.TAG_LENGTH_MAX, buildSchema.getMaxLength());
            putExist(SchemaKeyword.TAG_MULTIPLE_OF, buildSchema.getMultipleOf());
            putExist(SchemaKeyword.TAG_MINIMUM, buildSchema.getMinimum());
            putExist(SchemaKeyword.TAG_MAXIMUM, buildSchema.getMaximum());
            putExist(SchemaKeyword.TAG_MINIMUM_EXCLUSIVE, buildSchema.getExclusiveMinimum());
            putExist(SchemaKeyword.TAG_MAXIMUM_EXCLUSIVE, buildSchema.getExclusiveMaximum());
            putExist(SchemaKeyword.TAG_ENUM, buildSchema.getEnums());
            putExist(SchemaKeyword.TAG_ITEMS_UNIQUE, buildSchema.getUniqueItems());
            putExist(SchemaKeyword.TAG_ITEMS_MIN, buildSchema.getMinItems());
            putExist(SchemaKeyword.TAG_ITEMS_MAX, buildSchema.getMaxItems());
            putExist(SchemaKeyword.TAG_REQUIRED, buildSchema.getRequired());
            putDefault();
            return this.objectNode;
        }

        public void putDefault() {
            if (buildSchema.getDefaultValue() == null) {
                return;
            }
            switch (buildSchema.getType()) {
                case STRING:
                    putExist(SchemaKeyword.TAG_DEFAULT, (String) buildSchema.getDefaultValue());
                    break;
                case INTEGER:
                    putExist(SchemaKeyword.TAG_DEFAULT, NumberUtil.convertLong(String.valueOf(buildSchema.getDefaultValue())));
                    break;
                case NUMBER:
                    putExist(SchemaKeyword.TAG_DEFAULT, NumberUtil.convertDouble(String.valueOf(buildSchema.getDefaultValue())));
                    break;
                case BOOLEAN:
                    putExist(SchemaKeyword.TAG_DEFAULT, NumberUtil.convertBoolean(String.valueOf(buildSchema.getDefaultValue())));
                    break;
                default:
                    // do nothing
            }
        }

        public void putExist(SchemaKeyword key, String value) {
            if (StrUtil.isEmpty(value)) {
                return;
            }
            this.objectNode.put(key.code(), value);
        }

        public void putExist(SchemaKeyword key, Integer value) {
            if (value == null) {
                return;
            }
            this.objectNode.put(key.code(), value);
        }

        public void putExist(SchemaKeyword key, Long value) {
            if (value == null) {
                return;
            }
            this.objectNode.put(key.code(), value);
        }

        public void putExist(SchemaKeyword key, Double value) {
            if (value == null) {
                return;
            }
            this.objectNode.put(key.code(), value);
        }

        public void putExist(SchemaKeyword key, Boolean value) {
            if (value == null) {
                return;
            }
            this.objectNode.put(key.code(), value);
        }

        public void putExist(SchemaKeyword key, ICodeEnum value) {
            if (value == null) {
                return;
            }
            this.objectNode.put(key.code(), value.code());
        }

        public void putExist(SchemaKeyword key, List<String> value) {
            if (value == null) {
                return;
            }
            ArrayNode arrayNode = this.buildOptions.getMapper().createArrayNode();
            for (String item : value) {
                arrayNode.add(item);
            }
            this.objectNode.set(key.code(), arrayNode);
        }
    }

    public static class Options {
        private SchemaVersion version;
        private ObjectMapper mapper;

        public Options(SchemaVersion version, ObjectMapper mapper) {
            this.version = version;
            this.mapper = mapper;
        }

        public SchemaVersion getVersion() {
            return version;
        }

        public void setVersion(SchemaVersion version) {
            this.version = version;
        }

        public ObjectMapper getMapper() {
            return mapper;
        }

        public void setMapper(ObjectMapper mapper) {
            this.mapper = mapper;
        }
    }
}
