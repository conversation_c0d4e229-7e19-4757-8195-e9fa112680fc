package com.eoi.jax.json.schema;

import com.eoi.jax.json.schema.builder.DefaultSchemaJsonBuilder;
import com.eoi.jax.json.schema.consts.JsFormat;
import com.eoi.jax.json.schema.consts.JsType;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
public class JsSchema {
    private String name;
    private JsType type;
    private String title;
    private JsFormat format;
    private String description;
    private Boolean optional;
    private Object defaultValue;
    private String pattern;
    private Integer minLength;
    private Integer maxLength;
    private Integer multipleOf;
    private Double minimum;
    private Double maximum;
    private Double exclusiveMinimum;
    private Double exclusiveMaximum;
    private List<String> enums;
    private Boolean uniqueItems;
    private Integer minItems;
    private Integer maxItems;
    private List<JsSchema> properties;
    private JsSchema items;

    public JsonNode toJson() {
        return new DefaultSchemaJsonBuilder(this).build();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public JsType getType() {
        return type;
    }

    public void setType(JsType type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public JsFormat getFormat() {
        return format;
    }

    public void setFormat(JsFormat format) {
        this.format = format;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getOptional() {
        return optional;
    }

    public void setOptional(Boolean optional) {
        this.optional = optional;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(Object defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public Integer getMinLength() {
        return minLength;
    }

    public void setMinLength(Integer minLength) {
        this.minLength = minLength;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }

    public Integer getMultipleOf() {
        return multipleOf;
    }

    public void setMultipleOf(Integer multipleOf) {
        this.multipleOf = multipleOf;
    }

    public Double getMinimum() {
        return minimum;
    }

    public void setMinimum(Double minimum) {
        this.minimum = minimum;
    }

    public Double getMaximum() {
        return maximum;
    }

    public void setMaximum(Double maximum) {
        this.maximum = maximum;
    }

    public Double getExclusiveMinimum() {
        return exclusiveMinimum;
    }

    public void setExclusiveMinimum(Double exclusiveMinimum) {
        this.exclusiveMinimum = exclusiveMinimum;
    }

    public Double getExclusiveMaximum() {
        return exclusiveMaximum;
    }

    public void setExclusiveMaximum(Double exclusiveMaximum) {
        this.exclusiveMaximum = exclusiveMaximum;
    }

    public List<String> getEnums() {
        return enums;
    }

    public void setEnums(List<String> enums) {
        this.enums = enums;
    }

    public Boolean getUniqueItems() {
        return uniqueItems;
    }

    public void setUniqueItems(Boolean uniqueItems) {
        this.uniqueItems = uniqueItems;
    }

    public Integer getMinItems() {
        return minItems;
    }

    public void setMinItems(Integer minItems) {
        this.minItems = minItems;
    }

    public Integer getMaxItems() {
        return maxItems;
    }

    public void setMaxItems(Integer maxItems) {
        this.maxItems = maxItems;
    }

    public List<JsSchema> getProperties() {
        return properties;
    }

    public void setProperties(List<JsSchema> properties) {
        this.properties = properties;
    }

    public JsSchema getItems() {
        return items;
    }

    public void setItems(JsSchema items) {
        this.items = items;
    }

    public List<String> getRequired() {
        if (JsType.OBJECT != type) {
            return null;
        }
        List<String> list = new ArrayList<>();
        if (properties == null) {
            return list;
        }
        for (JsSchema property : properties) {
            if (Boolean.TRUE.equals(property.getOptional())) {
                continue;
            }
            list.add(property.getName());
        }
        return list;
    }
}
