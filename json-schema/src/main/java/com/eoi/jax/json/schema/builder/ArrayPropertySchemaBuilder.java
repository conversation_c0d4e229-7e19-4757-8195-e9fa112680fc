package com.eoi.jax.json.schema.builder;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.json.schema.annotation.JsArray;
import com.eoi.jax.json.schema.consts.JsType;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2022/10/13
 */
public class ArrayPropertySchemaBuilder extends BasePropertySchemaBuilder {
    private final Class<?> targetClass;
    private final Field targetField;
    private final JsArray annotation;

    public ArrayPropertySchemaBuilder(Class<?> targetClass, Field targetField) {
        super(targetClass, targetField);
        this.targetClass = targetClass;
        this.targetField = targetField;
        this.annotation = targetField.getAnnotation(JsArray.class);
        Assert.notNull(annotation);
    }

    @Override
    public String name() {
        if (StrUtil.isNotEmpty(annotation.value())) {
            return annotation.value();
        }
        return super.name();
    }

    @Override
    public JsType type() {
        return JsType.ARRAY;
    }

    @Override
    public Object defaultValue() {
        return null;
    }
}
