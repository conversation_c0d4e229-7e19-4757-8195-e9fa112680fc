package com.eoi.jax.json.schema.util;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.json.schema.SchemaException;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/17
 */
public class NumberUtil {

    public static Long convertLong(Object from) {
        Long result = null;
        if (from == null) {
            return result;
        } else if (from instanceof Double) {
            result = ((Double) from).longValue();
        } else if (from instanceof Float) {
            result = ((Float) from).longValue();
        } else if (from instanceof Long) {
            result = (Long) from;
        } else if (from instanceof Integer) {
            result = ((Integer) from).longValue();
        } else if (from instanceof Date) {
            result = ((Date) from).getTime();
        } else {
            String fromString = from.toString();
            try {
                result = Long.parseLong(fromString);
            } catch (Exception e) {
                throw new SchemaException(e, "it is not a Long {}", fromString);
            }
        }
        return result;
    }

    public static Double convertDouble(Object from) {
        Double result = null;
        if (from == null) {
            return result;
        }
        if (from instanceof Double) {
            result = (Double) from;
        } else if (from instanceof Float) {
            result = Double.valueOf(String.valueOf(from));
        } else if (from instanceof Long) {
            result = Double.valueOf(String.valueOf(from));
        } else if (from instanceof Integer) {
            result = Double.valueOf(String.valueOf(from));
        } else {
            String fromString = from.toString();
            try {
                result = Double.parseDouble(fromString);
            } catch (Exception e) {
                throw new SchemaException(e, "it is not a Double {}", fromString);
            }
        }
        return result;
    }

    public static Boolean convertBoolean(Object from) {
        if (from == null) {
            return null;
        }
        if (from instanceof Boolean) {
            return (Boolean) from;
        }

        String fromString = from.toString().toLowerCase();
        if (StrUtil.isEmpty(fromString)) {
            return null;
        } else if ("true".equals(fromString) || "yes".equals(fromString) || "1".equals(fromString)) {
            return true;
        } else if ("false".equals(fromString) || "no".equals(fromString) || "0".equals(fromString)) {
            return false;
        } else {
            throw new SchemaException("it is not a Double {}", fromString);
        }
    }
}
