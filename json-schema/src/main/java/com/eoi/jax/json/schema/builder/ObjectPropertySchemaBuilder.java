package com.eoi.jax.json.schema.builder;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.json.schema.annotation.JsObject;
import com.eoi.jax.json.schema.consts.JsType;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2022/10/13
 */
public class ObjectPropertySchemaBuilder extends BasePropertySchemaBuilder {
    private final Class<?> targetClass;
    private final Field targetField;
    private final JsObject annotation;

    public ObjectPropertySchemaBuilder(Class<?> targetClass, Field targetField) {
        super(targetClass, targetField);
        this.targetClass = targetClass;
        this.targetField = targetField;
        this.annotation = targetField.getAnnotation(JsObject.class);
        Assert.notNull(annotation);
    }

    @Override
    public String name() {
        if (StrUtil.isNotEmpty(annotation.value())) {
            return annotation.value();
        }
        return super.name();
    }

    @Override
    public JsType type() {
        return JsType.OBJECT;
    }

    @Override
    public Object defaultValue() {
        return null;
    }
}
