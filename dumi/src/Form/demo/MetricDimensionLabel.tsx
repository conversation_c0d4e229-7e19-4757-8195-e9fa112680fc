import React, { useState } from 'react';
import { Form } from 'antd';
import { MetricDimensionLabel } from '@/modules/MetricModeling/components/MetricDimensionLabel';
import { MetricDimension } from '@/modules/MetricModeling/models';

export default () => {
  const [value, setValue] = useState<MetricDimension[]>([]);
  return <Form layout='vertical'>
  <Form.Item label='维度标签：' name='value'>
    <MetricDimensionLabel value={value} onChange={setValue} />
  </Form.Item>
</Form>;
};
