import React, { useState } from 'react';
import { CodeEditorModal, LangType } from '@/components/business/ui/CodeEditor/CodeEditorModal';
import { Button, Space, Switch } from 'antd';

export default () => {
  const [open, setOpen] = useState(false);
  const [lang, setLang] = useState<LangType>();
  const [value, setValue] = useState<string>('');
  const [readOnly, setReadOnly] = useState(false);

  const onChange = (val: string) => {
    console.log('value changed: ', val);
  };

  return <div>
    <Space>
      <Button onClick={() => {
        setLang('SQL');
        setValue('select * from t;');
        setOpen(true);
      }}>SQL编辑代码
      </Button>
      <Button onClick={() => {
        setLang('JSON');
        setValue('{"a": "b"}');
        setOpen(true);
      }}>JSON编辑代码
      </Button>
      <Button onClick={() => {
        setLang('AVIATOR_SCRIPT');
        setValue('a = 1');
        setOpen(true);
      }}>AVIATOR脚本编辑代码
      </Button>
      只读模式:<Switch checkedChildren="启用" unCheckedChildren='禁用' onChange={(val) => setReadOnly(val)} />
    </Space>
    { open && (
      <CodeEditorModal
        title={`${lang}代码编辑器`}
        value={value}
        lang={lang}
        open={open}
        onCancel={() => setOpen(false)}
        onChange={onChange}
        readOnly={readOnly} />
    ) }
  </div>
};
