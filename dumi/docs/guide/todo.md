# todo

	1. 列表组件寻找解决方案，并逐步替换
	2. 绘图整体解决方案
	3. 项目主题解决方案、主题配置优化，方便二开
	4. 统一工具，lodash moment
	5. 项目组件库集成方案
	6. juperlab的架构模式的研究，并在产品中实施演进

第一个，现在的列表组件有很多问题，也缺失一些功能，比如表头宽度调整、字段显示隐藏等。API也要做些优化，这个解决方案倒是很好找，难点在于我们系统很多地方在用，替换工作量比较大。

第二个，绘图整体解决方案，我们目前也积累了一些成果，但是需要再精细，配合输出文档，以及现有图表的同步修改。

第三个，我们现在项目的主题 配置的变量有些乱，不成体系，需要仔细设计一下，变量定义更合理，方便项目二开，需要配合输出文档，帮助新加入的人快速了解，项目二开主题定制更加容易

第四个，项目中工具函数库 ramda需要用lodash替换，dayjs需要用moment替换，难度不大，需要充分了解两个库，并且对替换的功能做好测试才行，过程中可以深入学习这几个库，就是改的过程需要慎重。

第五个，我们项目需要有个自定义组件的展示、自测工具，类似antd的官方文档之类的一个工具。目前考虑了storyBook和dumi两个工具，各有优劣，需要出具体的集成方案和组件案例，并推进组件的文档话进度。集成有困难，组件文档的书写工作量也很大。

第六个，juperlab，这是一个大数据分析的工具，我们研究，主要研究它的前端架构模式，以及组件化的思想。jupterlab一切皆是组件，连它本身的访问项目都是组件。我们今后项目的架构演进，是要照这个项目思想演进的。公司有产线也要在这个基础上二开，所以我们需要研究一下。



1. 前端集成storyBook，开始组件库建设()
2. 前端主题定制方案文档及工具
3. 前端菜单解耦，采用前端本地服务模式，实现菜单配置化，路由自动生成
4. 探索Swigger的输出生成前端API及类型定义

