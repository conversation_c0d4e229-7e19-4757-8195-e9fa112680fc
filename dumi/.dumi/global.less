@import url('../../node_modules/allotment/dist/style.css');

@import url('@/themes/index.less');
@import url('@/assets/icon-fonts/iconfont.css');

@tailwind utilities;
@tailwind base;
@tailwind components;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100vw;
  height: 100vh;
}

body {
  height: 100%;
  overflow-y: hidden;
  background-color: #f0f0f0;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ka-wrapper,
.ka-content {
  height: 100%;
}

.cm-editor {
  height: 100%;
}

.cm-focused {
  outline: none !important;
}

.w-split-vertical > .w-split-line-bar div {
  cursor: n-resize !important;

  &:hover::after {
    height: 3px;
  }
}

.w-split-horizontal > .w-split-line-bar div {
  cursor: e-resize !important;

  &:hover::after {
    width: 3px;
  }
}

.noclickable,
.noclickable * {
  cursor: not-allowed !important;
  filter: grayscale(100%) !important;
}


body {
  overflow: auto;
}
