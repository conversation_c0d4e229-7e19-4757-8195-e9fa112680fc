package com.eoi.jax.external.blueking.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BKModelAttribute {

    private String id; //如：bk_process_name

    private String name; //如：进程名称

    private String type; //如：singlechar

    private Integer index; //排序

    private List<Map<String, Object>> option;

    private Map<String, String> enumOption; // 针对type是enum的，从option中获取枚举对应的键和值

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public List<Map<String, Object>> getOption() {
        return option;
    }

    public void setOption(List<Map<String, Object>> option) {
        this.option = option;
    }

    public Map<String, String> getEnumOption() {
        return enumOption;
    }

    public void setEnumOption(Map<String, String> enumOption) {
        this.enumOption = enumOption;
    }

    public static BKModelAttribute fromRawResponse(Map<String, Object> map) {
        BKModelAttribute attribute = new BKModelAttribute();
        attribute.setId(map.getOrDefault("bk_property_id", "").toString());
        attribute.setName(map.getOrDefault("bk_property_name", "").toString());
        attribute.setType(map.getOrDefault("bk_property_type", "").toString());
        attribute.setIndex(Integer.parseInt(map.getOrDefault("bk_property_index", "0").toString()));
        if ("enum".equals(attribute.getType()) || "enummulti".equals(attribute.getType())) {
            attribute.setOption((List<Map<String, Object>>) map.get("option"));
        }
        if (attribute.getType().equals("enum")) {
            attribute.setEnumOption(new HashMap<>());
            try {
                List<Map<String, Object>> options = (List) map.get("option");
                for (Map<String, Object> option : options) {
                    String enumKey = option.get("id").toString();
                    String enumValue = option.get("name").toString();
                    attribute.getEnumOption().put(enumKey, enumValue);
                }
            } catch (Exception ignore) { }
        }
        return attribute;
    }
}
