package com.eoi.jax.external.blueking.condition;

import java.util.List;

public class SearchCondition implements Condition {

    private String condition; // AND OR

    private List<Condition> rules;

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public List<Condition> getRules() {
        return rules;
    }

    public void setRules(List<Condition> rules) {
        this.rules = rules;
    }
}
