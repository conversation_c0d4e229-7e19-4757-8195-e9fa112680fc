const scopeList = [
  ['components', '组件相关'],
  ['hooks', 'hook 相关'],
  ['utils', 'utils 相关'],
  ['ui', '对ui的调整'],
  ['styles', '样式相关'],
  ['deps', '项目依赖'],
  ['auth', '对 auth 修改'],
  ['other', '其他修改'],
  ['custom', '自定义']
]

module.exports = {
  types: [
    { value: 'feat', name: 'feat: 新增功能' },
    { value: 'fix', name: 'fix: 修复bug' },
    { value: 'docs', name: 'docs: 文档变更' },
    {
      value: 'style',
      name: 'style: 代码格式(不影响功能, 例如空格, 分号等格式修正)'
    },
    { value: 'refactor', name: 'refactor: 代码重构(不包括bug修复和新增功能)' },
    { value: 'perf', name: 'perf: 性能优化' },
    { value: 'test', name: 'test: 添加修改测试用例' },
    {
      value: 'build',
      name: 'build: 构建流程, 外部依赖变更(如升级 npm 包, 修改 webpack 配置等)'
    },
    { value: 'ci', name: 'ci: 修改 CI 配置, 脚本' },
    {
      value: 'chore',
      name: 'chore: 对构建过程或辅助工具和库的更改(不影响源文件和测试用例)'
    },
    { value: 'revert', name: 'revert: 回滚 commit' }
  ],
  scopes: scopeList.map(([value, desc]) => ({
    value,
    name: `${value.padEnd(30)} (${desc})`
  })),
  messages: {
    type: '请选择提交类型:\n',
    customScope: '请输入修改范围(可选):',
    subject: '请简要描述提交:\n',
    body: '请输入详细描述内容,使用|换行(可选):\n',
    breaking: '请输入不兼容更改列表(可选)\n',
    footer: '请输入要关闭的issues,如:#31,#34(可选):\n',
    confirmCommit: '确认提交?(Y/n)'
  },
  skipQuestion: ['body', 'breaking', 'footer'],
  allowBreakingChanges: ['feat', 'fix'],
  subjectLimit: 80
}
