interface BaseModel {
  createTime?: number;
  createBy?: any;
  updateTime?: number;
  updateBy?: any;
}

type PipelineType = 'streaming' | 'flink_sql' | 'flink_cmd' | 'batch';

interface ProcessModel extends BaseModel {
  id?: string;
  processType: 'ELASTICSEARCH' | 'HIVE' | 'streaming' | 'flink_sql' | 'batch' | 'flink_cmd';
  businessFlowId?: string;
  name: string;
  description: string;
  pipelineType: PipelineType;
  pipelineConfig: PipelineConfig;
  pipelineUi?: Record<string, any>;
  clusterId: string;
  optsId?: string;
  yarnSessionId?: string;
  status?: keyof typeof import('@/constants/').ProcessStatus;
  forceUpdate?: boolean; // 自定义属性，用于用户通过JSON直接修改pipelineData,强制画布更新
  projectAuth?: ProjectAuthModel;
  version?: number;
  forceStart?: boolean;
  tags?: string[];
}

// 算子slot数据模型
interface JobSlotEntity {
  description?: string;
  raw: string;
  parameterizedTypes?: string[];
}

type InputType =
  | 'TEXT'
  | 'PASSWORD'
  | 'SQL'
  | 'JSON'
  | 'SPL'
  | 'JAVASCRIPT'
  | 'AVIATOR_SCRIPT'
  | 'XML'
  | 'JAX_METRIC_SOURCE'
  | 'MODEL_SELECTION'
  | 'JAX_REGISTER_CENTER_NACOS'
  | 'JAX_DIC_ENUM'
  | 'JAVASCRIPT_EXPRESSION'
  | 'GTJA_SYSTEM_NAME'
  | null;

// 算子参数配置数据模型
interface PipelineParameterModel {
  availableCondition?: string;
  requireCondition?: string;
  viewDataMap?: PipelineViewDataListModel[];
  apiVersion?: number;
  name: string;
  label: string;
  type: string[];
  description: string;
  optional: boolean;
  defaultValue: string;
  placeholder?: any;
  candidates?: any;
  inputType: InputType | null;
  range?: any;
  regex?: any;
  algorithmic?: boolean;
  listParameter?: any;
  objectParameters?: any;
  prop?: string;
  prevProp?: string;
  value?: any;
  formRule?: any;
  formProp?: string;
  defaultFormValue?: any;
  tier?: number;
  recommendations?: string[];
  projectAuth?: ProjectAuthModel;
  falseLabel?: string;
  trueLabel?: string;
}

interface PipelineViewDataListModel {
  key: string;
  value: unknown;
}

// 算子详情
interface JobDetailModel {
  id: string;
  type: string;
  name: string;
  conf: PipelineJob;
  shape: string;
  style: Record<string, any>;
  x: number;
  y: number;
  anchorPoints: number[][];
  display?: string;
}

// 作业配置
interface PipelineConfig {
  jobs: PipelineJob[];
  edges: PipelineEdge[];
  opts?: Record<string, any> & {
    extJars?: string[];
  };
}

// 作业连线
interface PipelineEdge {
  from: string;
  to: string;
  edgeId?: string;
  fromSlot?: number;
  toSlot?: number;
  label?: string;
  enableMock?: boolean | null;
  mockId?: string | null;
}

// 作业算子
interface PipelineJob {
  jobId: string;
  jobName: string;
  jobDisplay: string;
  jobDescription: string;
  jobConfig: JobConfig;
  jobOpts: any;
}

// 算子配置
type JobConfig = Record<string, any>;

// 作业日志
interface PipelineLogModel {
  pipelineName: string;
  logType: string;
  pipelineLog: string;
  createTime: number;
}

// 作业控制台日志
interface PipelineConsoleModel {
  pipelineName: string;
  opType: string;
  opTime: number;
  logContent: string;
  createTime: number;
}

interface JobUiData {
  display: string;
  description: string;
  docUrl: string;
  hasDoc: string;
}

type JobOpts = Record<string, any>;

interface GraphJobData extends JobModel {
  jobUiData: JobUiData; // 算子ui数据
  jobConfig: JobConfig;
  jobOpts: JobOpts;
  errorList: any[];
  slotMountResultList: any[];
  summaryStatus: 'SUCCESS' | 'FAILED';
}

interface WebSocketData {
  code: string;
  job_id?: string;
  slot?: string;
  message: string;
}
