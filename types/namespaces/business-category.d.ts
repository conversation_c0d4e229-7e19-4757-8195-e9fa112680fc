declare namespace Biz {
  export interface BizItem {
    id: string;
    name: string;
    code: string;
    description: string;
    parentId: string | number | null;
    isLeaf: boolean;
    parentPath: string;
    createTime: string;
    updateTime: string;
    createUserName: string | null;
    updateUserName: string | null;
    children: BizItem[] | null;
  }
  export type ActionType = 'edit' | 'create' | 'delete' | null;

  export type CurrentNodeType = BizItem | TreeNode | null;
  export interface BizState {
    showModal: boolean;
    actionType: ActionType;
    currentNode: CurrentNodeType;
    actions: {
      toggleModal: () => void;
      setCurrentNode: (data: CurrentNodeType) => void;
      setActionType: (type: ActionType) => void;
    };
  }
  export interface BizItemFormData {
    name?: string;
    code?: string;
    description?: string;
    parentId?: string | number | null;
  }
  interface Api {
    queryBiz: (id: string) => Promise<BizItem>;
    updateBiz: (id: string, data: BizItemFormData) => Promise<BizItem>;
    removeBiz: (id: string) => Promise<BizItem>;
    createBiz: (data: BizItemFormData) => Promise<BizItem>;
    queryBizCondition: (params: any) => Promise<BizItem[]>;
    queryBizUseage: (id: string) => any; // TODO
    queryBizTree: () => Promise<BizItem[]>;
    queryBizTreeRoot: () => Promise<BizItem[]>;
    queryBizTreeChild: (id: string) => Promise<BizItem[]>;
    queryDataDomain: (bizId: string) => Promise<DataDomain[]>;
  }
  export interface TreeNode {
    key: string;
    id: string;
    title: string;
    name: string;
    children: TreeNode[];
    isLeaf: boolean;
    parentId?: string | null;
    parentPath?: string;
    icon?: ReactNode;
  }
}
