pipeline {
    agent {
        node {
            label "master"
        }
    }
    parameters {
        booleanParam(name: 'IS_DOCKER_BUILD', defaultValue: 'true', description: '是否生成镜像')
        booleanParam(name: 'IS_PUBLISH', defaultValue: 'true', description: '是否发布代码')
        string(name: 'PUBLISH_HOST', defaultValue: '***************', description: '发布的机器')
        string(name: 'PUBLISH_PATH', defaultValue: '/opt/app/jax-super-docker/', description: '发布的目录')
    }
    stages {
        stage("Docker") {
            when {
                expression { return params.IS_DOCKER_BUILD }
            }
            agent{
                docker {
                    image 'hub.eoitek.net/third/devops-tools:1.1'
                    args '-v /home/<USER>/.m2:/root/.m2 -v /etc/localtime:/etc/localtime:ro -v /root/.ssh.sensor:/root/.ssh -v /var/run/docker.sock:/var/run/docker.sock -v /usr/bin/docker:/usr/bin/docker'
                    reuseNode true
                }
            }
            steps {
                sh "make docker-image-small"
            }
        }
        stage("Deploy") {
            when {
                expression { return params.IS_PUBLISH }
            }
            agent{
                docker {
                    image 'hub.eoitek.net/third/devops-tools:1.1'
                    args '-v /home/<USER>/.m2:/root/.m2 -v /etc/localtime:/etc/localtime:ro -v /root/.ssh:/root/.ssh'
                    label "sensor-deploy-slave1"
                }
            }
            steps {
                sh "scp docker-run.sh root@${params.PUBLISH_HOST}:${params.PUBLISH_PATH}"
                sh "ssh root@${params.PUBLISH_HOST} 'cd ${params.PUBLISH_PATH} && sh docker-run.sh'"
            }
        }
    }
    post {
        success {
            echo "success"
        }
        failure {
            echo "failed"
        }
    }
}