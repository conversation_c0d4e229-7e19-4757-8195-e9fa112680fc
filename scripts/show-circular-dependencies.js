const madge = require('madge')
const path = require('path')

const distPath = path.resolve(__dirname, '../src')

madge(distPath, {
  tsConfig: path.resolve(__dirname, '../tsconfig.json'),
  fileExtensions: ['js', 'ts', 'jsx', 'tsx'],
  dependencyFilter: (filePath) => !filePath.includes('.umi')
}).then((res) => {
  const result = res.circular()
  console.log('循环依赖列表:')
  result.forEach((dependency) => {
    console.log(dependency.join(' -> '))
  })
})
