pipeline {
    agent {
        node {
            label "master"
        }
    }
    stages {
        stage("Docker") {
            agent{
                docker {
                    image 'hub.eoitek.net/third/devops-tools:1.1'
                    args '-v /home/<USER>/.m2:/root/.m2 -v /etc/localtime:/etc/localtime:ro -v /root/.ssh.sensor:/root/.ssh -v /var/run/docker.sock:/var/run/docker.sock -v /usr/bin/docker:/usr/bin/docker'
                    reuseNode true
                }
            }
            steps {
                sh "make docker-image-all"
            }
        }
    }
    post {
        success {
            echo "success"
        }
        failure {
            echo "failed"
        }
    }
}