package com.eoi.jax.ruleengine.vm.service.impl;

import cn.hutool.json.JSONUtil;
import com.eoi.jax.ruleengine.vm.service.Reporter;

import java.util.Map;

/**
 * 控制台打印
 */
public class StderrReporter implements Reporter {
    @Override
    public void configure(Map<String, Object> config) {

    }

    @Override
    public void doReport(Object alert) {
        System.err.println("告警打印：" + JSONUtil.toJsonStr(alert));
    }
}
