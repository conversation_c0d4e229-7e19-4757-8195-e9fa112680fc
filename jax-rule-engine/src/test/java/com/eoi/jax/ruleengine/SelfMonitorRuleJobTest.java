package com.eoi.jax.ruleengine;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.ruleengine.vm.service.impl.DefaultAlarmCacheServiceImpl;
import com.eoi.jax.ruleengine.vm.service.impl.SelfMonitorAlarmServiceImpl;
import com.eoi.monitor.model.monitor.SelfMonitorBaseMetric;
import com.eoi.monitor.model.monitor.SelfMonitorRuleParam;
import com.eoi.monitor.model.monitor.ThresholdConfig;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


/**
 * @Author: yaru.ma
 * @Date: 2024/3/26
 */
public class SelfMonitorRuleJobTest {

    @Test
    @Ignore
    public void testAlarmJaxWeb() {
        DefaultAlarmCacheServiceImpl activeAlarmCache = new DefaultAlarmCacheServiceImpl();
        SelfMonitorRuleParam alarmRule = new SelfMonitorRuleParam();
        alarmRule.setRuleId(1L);
        alarmRule.setThresholdMethod("LIVENESS");
        alarmRule.setAutoClose(true);
        alarmRule.setConditions(Arrays.asList(getThresholdConfig("00:00", "23:59", "test")));
        alarmRule.setContent("告警内容-不做参数替换");
        alarmRule.setObjectType("JAX_WEB");
        alarmRule.setVmUrl("http://**************:8428/");
        alarmRule.setAnnotations(new HashMap<>());
        alarmRule.setMonitorInterval(300);
        Map<String, Object> reporter = MapUtil.of("type", "stderr");
        alarmRule.setReporters(Arrays.asList(reporter));
        alarmRule.setSampleMetric("min(jax_web_node_lived[120s])");
        alarmRule.setUniqueTags(Arrays.asList());
        String baseMetric = "{\"metricName\":\"jax_web_node_unlived_count\",\"metricPql\":\"min_over_time(jax_web_node_lived[120s])==0\",\"metricLabel\":\"serverAddress\"}";
        SelfMonitorBaseMetric selfMonitorBaseMetric = JSONUtil.toBean(baseMetric, SelfMonitorBaseMetric.class);
        alarmRule.setBaseMetrics(Arrays.asList(selfMonitorBaseMetric));
        alarmRule.setItemName("中台组件可用性");
        SelfMonitorAlarmServiceImpl alarmService = new SelfMonitorAlarmServiceImpl(alarmRule, activeAlarmCache);
        alarmService.dealWithSelfMonitorRule();
    }

    @Test
    @Ignore
    public void testAlarmKafka() {
        DefaultAlarmCacheServiceImpl activeAlarmCache = new DefaultAlarmCacheServiceImpl();
        SelfMonitorRuleParam alarmRule = new SelfMonitorRuleParam();
        alarmRule.setRuleId(1L);
        alarmRule.setThresholdMethod("KAFKA_LAG_TOLERANCE");
        alarmRule.setAutoClose(true);
        ThresholdConfig thresholdConfig = getThresholdConfig("00:00", "23:59", "test");
        thresholdConfig.setThreshold(MapUtil.of("time", 2));
        alarmRule.setConditions(Arrays.asList(thresholdConfig));
        alarmRule.setContent("告警内容-不做参数替换");
        alarmRule.setObjectType("FLINK_JOB");
        alarmRule.setVmUrl("http://**************:8428/");
        alarmRule.setMonitorInterval(300);
        alarmRule.setAnnotations(new HashMap<>());
        Map<String, Object> reporter = MapUtil.of("type", "stderr");
        alarmRule.setReporters(Arrays.asList(reporter));
        alarmRule.setSampleMetric("sum by(topic,consumergroup)(kafka_consumergroup_lag) - ignoring(consumergroup) group_left sum by (topic) (delta(kafka_topic_partition_current_offset[${time}m])) > 0");
        alarmRule.setUniqueTags(Arrays.asList("topic", "consumergroup"));
        String baseMetric = "{\"metricName\":\"value\",\"metricPql\":\"sum by(topic,consumergroup)(kafka_consumergroup_lag)\"}";
        SelfMonitorBaseMetric selfMonitorBaseMetric = JSONUtil.toBean(baseMetric, SelfMonitorBaseMetric.class);
        alarmRule.setBaseMetrics(Arrays.asList(selfMonitorBaseMetric));
        alarmRule.setItemName("flink消费积压");
        SelfMonitorAlarmServiceImpl alarmService = new SelfMonitorAlarmServiceImpl(alarmRule, activeAlarmCache);
        alarmService.dealWithSelfMonitorRule();
    }

    @Test
    @Ignore
    public void testKafka() {
        String json = "{\n" +
                "\t\"vmUrl\": \"http://**************:8428/\",\n" +
                "\t\"reporters\": [{\"type\": \"stderr\",},\n" +
                "\t\t{\n" +
                "\t\t\t\"type\": \"kafka\",\n" +
                "\t\t\t\"parameters\": {\n" +
                "\t\t\t\t\"enable\": true,\n" +
                "\t\t\t\t\"topic\": \"jax_self_monitor_alarm\",\n" +
                "\t\t\t\t\"group\": \"group_jax_self_monitor_alarm\",\n" +
                "\t\t\t\t\"maxThreadSize\": 1,\n" +
                "\t\t\t\t\"queueCapacity\": 1,\n" +
                "\t\t\t\t\"brokerAddress\": \"**************:19092,**************:19092,**************:19092\",\n" +
                "\t\t\t\t\"securityProtocol\": null,\n" +
                "\t\t\t\t\"saslMechanism\": null,\n" +
                "\t\t\t\t\"saslKerberosServiceName\": null,\n" +
                "\t\t\t\t\"saslJaasConfig\": null,\n" +
                "\t\t\t\t\"sslTruststoreLocation\": null,\n" +
                "\t\t\t\t\"sslTruststorePassword\": null,\n" +
                "\t\t\t\t\"sslKeyPassword\": null,\n" +
                "\t\t\t\t\"sslKeystoreLocation\": null,\n" +
                "\t\t\t\t\"sslKeystorePassword\": null,\n" +
                "\t\t\t\t\"sslKeystoreType\": null,\n" +
                "\t\t\t\t\"sslTruststoreType\": null,\n" +
                "\t\t\t\t\"sslEnabledProtocols\": null,\n" +
                "\t\t\t\t\"sslEndpointIdentificationAlgorithm\": null,\n" +
                "\t\t\t\t\"properties\": {}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"ruleId\": 2220806157763584,\n" +
                "\t\"itemId\": 2220797653453824,\n" +
                "\t\"itemName\": \"消费积压\",\n" +
                "\t\"monitorInterval\": 120,\n" +
                "\t\"thresholdMethod\": \"KAFKA_LAG_TOLERANCE\",\n" +
                "\t\"content\": \"实时作业kafka消费积压超过${threshold}分钟\",\n" +
                "\t\"annotations\": {\n" +
                "\t\t\"solutionId\": 1\n" +
                "\t},\n" +
                "\t\"uniqueTags\": [\n" +
                "\t\t\"appResourceId\",\n" +
                "\t\t\"topic\",\n" +
                "\t\t\"consumergroup\"\n" +
                "\t],\n" +
                "\t\"conditions\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"id\": 2220857952961536,\n" +
                "\t\t\t\"name\": \"default\",\n" +
                "\t\t\t\"startTime\": \"01:00\",\n" +
                "\t\t\t\"endTime\": \"23:00\",\n" +
                "\t\t\t\"alarmLevel\": \"warning\",\n" +
                "\t\t\t\"threshold\": {\"threshold\":2}\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"sampleMetric\": \"sum by(topic,consumergroup)(kafka_consumergroup_lag) - ignoring(consumergroup) group_left sum by (topic) (delta(kafka_topic_partition_current_offset[${threshold}m])) > 0\",\n" +
                "\t\"baseMetrics\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"metricName\": \"value\",\n" +
                "\t\t\t\"metricPql\": \"sum by(topic,consumergroup)(kafka_consumergroup_lag)\",\n" +
                "\t\t\t\"metricLabel\": null,\n" +
                "\t\t\t\"value\": null\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"objectType\": \"FLINK\",\n" +
                "\t\"autoClose\": true\n" +
                "}";
        SelfMonitorRuleParam alarmRule = JSONUtil.toBean(json, SelfMonitorRuleParam.class);
        alarmRule.setAnnotations(new HashMap<>());
        DefaultAlarmCacheServiceImpl activeAlarmCache = new DefaultAlarmCacheServiceImpl();
        SelfMonitorAlarmServiceImpl alarmService = new SelfMonitorAlarmServiceImpl(alarmRule, activeAlarmCache);
        alarmService.dealWithSelfMonitorRule();
    }

    @Test
    @Ignore
    public void testMemoryUsage() {
        String json = "{\n" +
                "\t\"vmUrl\": \"http://**************:8428/\",\n" +
                "\t\"reporters\": [{\n" +
                "\t\"type\": \"stderr\"},\n" +
                "\t\t{\n" +
                "\t\t\t\"type\": \"kafka\",\n" +
                "\t\t\t\"parameters\": {\n" +
                "\t\t\t\t\"enable\": true,\n" +
                "\t\t\t\t\"topic\": \"jax_self_monitor_alarm\",\n" +
                "\t\t\t\t\"group\": \"group_jax_self_monitor_alarm\",\n" +
                "\t\t\t\t\"maxThreadSize\": 1,\n" +
                "\t\t\t\t\"queueCapacity\": 1,\n" +
                "\t\t\t\t\"brokerAddress\": \"**************:19092,**************:19092,**************:19092\",\n" +
                "\t\t\t\t\"securityProtocol\": null,\n" +
                "\t\t\t\t\"saslMechanism\": null,\n" +
                "\t\t\t\t\"saslKerberosServiceName\": null,\n" +
                "\t\t\t\t\"saslJaasConfig\": null,\n" +
                "\t\t\t\t\"sslTruststoreLocation\": null,\n" +
                "\t\t\t\t\"sslTruststorePassword\": null,\n" +
                "\t\t\t\t\"sslKeyPassword\": null,\n" +
                "\t\t\t\t\"sslKeystoreLocation\": null,\n" +
                "\t\t\t\t\"sslKeystorePassword\": null,\n" +
                "\t\t\t\t\"sslKeystoreType\": null,\n" +
                "\t\t\t\t\"sslTruststoreType\": null,\n" +
                "\t\t\t\t\"sslEnabledProtocols\": null,\n" +
                "\t\t\t\t\"sslEndpointIdentificationAlgorithm\": null,\n" +
                "\t\t\t\t\"properties\": {}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"ruleId\": 2220769092469760,\n" +
                "\t\"itemId\": 2220754617631744,\n" +
                "\t\"itemName\": \"所在主机的内存使用率\",\n" +
                "\t\"monitorInterval\": 120,\n" +
                "\t\"thresholdMethod\": \"NORMAL\",\n" +
                "\t\"content\": \"中台服务${serverAddress}内存使用率超过${threshold}\",\n" +
                "\t\"annotations\": {\n" +
                "\t\t\"solutionId\": 1\n" +
                "\t},\n" +
                "\t\"uniqueTags\": [\n" +
                "\t\t\"\"\n" +
                "\t],\n" +
                "\t\"conditions\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"id\": 2220822489138176,\n" +
                "\t\t\t\"name\": \"123\",\n" +
                "\t\t\t\"startTime\": \"02:10\",\n" +
                "\t\t\t\"endTime\": \"23:14\",\n" +
                "\t\t\t\"alarmLevel\": \"severe\",\n" +
                "\t\t\t\"threshold\": {\"threshold\":\"0.1\"}\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"sampleMetric\": \"min(jax_web_node_memory_usage[120s])\",\n" +
                "\t\"baseMetrics\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"metricName\": \"value\",\n" +
                "\t\t\t\"metricPql\": \"sample\",\n" +
                "\t\t\t\"metricLabel\": null,\n" +
                "\t\t\t\"value\": null\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"metricName\": \"serverAddress\",\n" +
                "\t\t\t\"metricPql\": \"min_over_time(jax_web_node_memory_usage[120s])\",\n" +
                "\t\t\t\"metricLabel\": \"ip\",\n" +
                "\t\t\t\"value\": null\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"objectType\": \"JAX_WEB\",\n" +
                "\t\"autoClose\": true\n" +
                "}";
        SelfMonitorRuleParam alarmRule = JSONUtil.toBean(json, SelfMonitorRuleParam.class);
        alarmRule.setAnnotations(new HashMap<>());
        DefaultAlarmCacheServiceImpl activeAlarmCache = new DefaultAlarmCacheServiceImpl();
        SelfMonitorAlarmServiceImpl alarmService = new SelfMonitorAlarmServiceImpl(alarmRule, activeAlarmCache);
        alarmService.dealWithSelfMonitorRule();
    }


    private ThresholdConfig getThresholdConfig(String startTime, String endTime, String name) {
        ThresholdConfig thresholdConfig = new ThresholdConfig();
        thresholdConfig.setStartTime(startTime);
        thresholdConfig.setEndTime(endTime);
        thresholdConfig.setAlarmLevel("info");
        thresholdConfig.setName(name);
        thresholdConfig.setThreshold(new HashMap<>());
        return thresholdConfig;
    }
}
