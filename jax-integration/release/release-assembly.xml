<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.1 https://maven.apache.org/xsd/assembly-2.1.1.xsd">
    <!-- Assembles a packaged version targeting OS installation. -->
    <id>release</id>
    <formats>
        <format>dir</format>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.basedir}</directory>
            <includes>
                <include>bin/start</include>
            </includes>
            <outputDirectory>jax-integration</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/target</directory>
            <includes>
                <include>jax-integration-${project.version}.jar</include>
            </includes>
            <outputDirectory>jax-integration/lib</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/target/lib</directory>
            <outputDirectory>jax-integration/lib</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>
