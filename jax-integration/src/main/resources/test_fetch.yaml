baseUrl: http://10.1.133.166
loginPath: /next/api/auth/login
modelInstanceSearchPath: /next/api/gateway/cmdb.instance.PostSearchV3/v3/object/%s/instance/_search
userName: 5204
password: 123456a
clickhouse:
  # jdbc用的是http port
  jdbc: *************************************
  user: default
  password: 123456
  objectTable: default.ods_cmdb_object
  relationTable: default.ods_cmdb_object_relation
objects:
  - id: BIZ
    query: '{"$and":[{"$or":[{"name":{"$like":"%xxx%"}},{"alias":{"$like":"%xxx%"}}]}]}'
    rename: application
    attributes:
      objectName: name
      objectId: instanceId
      applicationName: alias
    relations:
      INCLUDES: include
  - id: APP
    rename: service
    attributes:
      objectName: name
      objectId: instanceId
      serivceName: alias
    relations:
      BELONGS: '!include'
      RUNS: runOn
      DB2: dependOn
  - id: CLUSTER
    rename: appCluster
    attributes:
      objectName: name
      objectId: instanceId
  - id: ASSET
    rename: os
    attributes:
      objectName: name
      ip: ip
      objectId: instanceId
      role: role
    relations:
      BELONGS: '!include'
    filter: role == 'appServer'
  - id: ASSET
    rename: db2
    attributes:
      objectName: name
      ip: ip
      objectId: instanceId
      dbRole: role
    filter: role == 'db2'