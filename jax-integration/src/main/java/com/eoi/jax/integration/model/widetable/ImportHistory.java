package com.eoi.jax.integration.model.widetable;

import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.integration.clickhouse.CKColDef;
import com.eoi.jax.integration.clickhouse.CkRow;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
public class ImportHistory implements CkRow {

    @CKColDef(definition = "date Date")
    private LocalDate date;
    @CKColDef(definition = "source String")
    private String source;
    @CKColDef(definition = "exec_time Datetime", columnName = "exec_time")
    private LocalDateTime execTime;
    @CKColDef(definition = "success Int32")
    private Integer success;
    @CKColDef(definition = "success_object_rows  Nullable(Int32)", columnName = "success_object_rows")
    private Integer successObjectRows;
    @CKColDef(definition = "success_relation_rows Nullable(Int32)", columnName = "success_relation_rows")
    private Integer successRelationRows;

    private static List<Tuple> columns;

    static {
        columns = Arrays.stream(ImportHistory.class.getDeclaredFields())
            .filter(f -> f.isAnnotationPresent(CKColDef.class))
            .map(f -> {
                String columnName = f.getAnnotation(CKColDef.class).columnName();
                if (StrUtil.isBlank(columnName)) {
                    columnName = f.getName();
                }
                return new Tuple(f, f.getAnnotation(CKColDef.class).definition(), columnName);
            }).collect(Collectors.toList());
    }

    @Override
    public int colSize() {
        return columns.size();
    }

    @Override
    public String getColName(int index) {
        return columns.get(index).get(2);
    }

    @Override
    public String getColDef(int index) {
        return columns.get(index).get(1);
    }

    @Override
    public Object getColValue(int index) {
        try {
            Field field = columns.get(index).get(0);
            return field.get(this);
        } catch (Exception ex) {
            return null;
        }
    }


    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public LocalDateTime getExecTime() {
        return execTime;
    }

    public void setExecTime(LocalDateTime execTime) {
        this.execTime = execTime;
    }

    public Integer getSuccess() {
        return success;
    }

    public void setSuccess(Integer success) {
        this.success = success;
    }

    public Integer getSuccessObjectRows() {
        return successObjectRows;
    }

    public void setSuccessObjectRows(Integer successObjectRows) {
        this.successObjectRows = successObjectRows;
    }

    public Integer getSuccessRelationRows() {
        return successRelationRows;
    }

    public void setSuccessRelationRows(Integer successRelationRows) {
        this.successRelationRows = successRelationRows;
    }

    public static List<Tuple> getColumns() {
        return columns;
    }

    public static void setColumns(List<Tuple> columns) {
        ImportHistory.columns = columns;
    }
}
