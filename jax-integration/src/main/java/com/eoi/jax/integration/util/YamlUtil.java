package com.eoi.jax.integration.util;

import cn.hutool.core.util.StrUtil;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
public final class YamlUtil {

    private YamlUtil() {
    }

    public static <T> T loadStrYaml(String content, Class<T> clazz) {
        if (StrUtil.isBlank(content)) {
            throw new IllegalArgumentException("content is blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("clazz is null");
        }
        Yaml yaml = new Yaml();
        return yaml.loadAs(content, clazz);
    }

    public static <T> T loadPathYaml(String yamlPath, Class<T> clazz) {
        if (StrUtil.isBlank(yamlPath)) {
            throw new IllegalArgumentException("yamlPath is blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("clazz is null");
        }
        Yaml yaml = new Yaml(new Constructor(clazz, new LoaderOptions()));
        try {
            InputStream inputStream = new FileInputStream(yamlPath);
            return yaml.load(inputStream);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
    }


    public static <T> T loadJarYaml(String yamlPath, Class<T> clazz) {
        if (StrUtil.isBlank(yamlPath)) {
            throw new IllegalArgumentException("yamlPath is blank");
        }
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("clazz is null");
        }
        Yaml yaml = new Yaml(new Constructor(clazz, new LoaderOptions()));
        InputStream inputStream = YamlUtil.class
            .getClassLoader()
            .getResourceAsStream(yamlPath);
        return yaml.load(inputStream);
    }


}
