package com.eoi.jax.integration.easycmdb;

import java.util.List;

public class FetchConfig {

    private String baseUrl;

    private String loginPath;

    private String modelInstanceSearchPath;

    private String userName;

    private String password;

    private ClickhouseConfig clickhouse;

    private List<FetchObject> objects;

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getLoginPath() {
        return loginPath;
    }

    public void setLoginPath(String loginPath) {
        this.loginPath = loginPath;
    }

    public String getModelInstanceSearchPath() {
        return modelInstanceSearchPath;
    }

    public void setModelInstanceSearchPath(String modelInstanceSearchPath) {
        this.modelInstanceSearchPath = modelInstanceSearchPath;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public ClickhouseConfig getClickhouse() {
        return clickhouse;
    }

    public void setClickhouse(ClickhouseConfig clickhouse) {
        this.clickhouse = clickhouse;
    }

    public List<FetchObject> getObjects() {
        return objects;
    }

    public void setObjects(List<FetchObject> objects) {
        this.objects = objects;
    }
}
