package com.eoi.jax.integration.blueking;

import cn.hutool.json.JSONUtil;
import com.eoi.jax.external.blueking.BluekingCmdbApiService;
import com.eoi.jax.external.blueking.BluekingCmdbApiServiceImpl;
import com.eoi.jax.external.blueking.model.BKClassification;
import com.eoi.jax.external.blueking.model.BKModel;
import com.eoi.jax.external.blueking.model.BKModelAttribute;
import com.eoi.jax.external.blueking.model.InstanceRelationResponse;
import com.eoi.jax.external.blueking.model.request.InstanceSearchRequest;
import com.eoi.jax.external.blueking.model.response.BizHostsTopoResponse;

import java.util.*;

public class TestProgram {

    public static void main(String[] args) {

        if (args.length < 1) {
            System.err.println("至少输入一个需要调用的接口功能");
            System.exit(-1);
        }

        // 功能
        String func = args[0];
        if ("getInstance".equals(func)) {
            getInstance(args);
        } else if ("getBiz".equals(func)) {
            getBiz();
        } else if ("getSetsFromBiz".equals(func)) {
            getSetsFromBiz(args);
        } else if ("getHostsFromBiz".equals(func)) {
            getHostsFromBiz(args);
        } else if ("getHostTopoFromBiz".equals(func)) {
            getHostTopoFromBiz(args);
        } else if ("getInstanceRelations".equals(func)) {
            getInstanceRelations(args);
        } else if ("getClassifications".equals(func)) {
            getClassifications(args);
        } else if ("getModels".equals(func)) {
            getModels(args);
        } else if ("getModelAttributes".equals(func)) {
            getModelAttributes(args);
        } else {
            System.err.println("接口功能不支持");
            System.exit(-1);
        }
    }

    private static BluekingCmdbApiService getService() {
        String baseUrl = System.getenv("BASE_URL");
        String appId = System.getenv("APP_ID");
        String appToken = System.getenv("APP_TOKEN");
        String userName = System.getenv("USER_NAME");
        return new BluekingCmdbApiServiceImpl(baseUrl, appId, appToken, userName);
    }

    public static void getInstance(String[] args) {
        BluekingCmdbApiService service = getService();
        String modelId = args[1];
        InstanceSearchRequest request = new InstanceSearchRequest();
        request.setBk_obj_id(modelId);
        request.setFields(new ArrayList<>());
        request.getFields().add("bk_inst_id");
        request.getFields().add("bk_inst_name");
        List<Map<String, Object>> instances = service.fetchAllInstance(request);
        printInstances(instances);
    }

    public static void getBiz() {
        BluekingCmdbApiService service = getService();
        List<Map<String, Object>> instances = service.fetchAllBiz();
        printInstances(instances);
    }

    public static void getHostsFromBiz(String[] args) {
        BluekingCmdbApiService service = getService();
        String bizId = args[1];
        List<Map<String, Object>> instances = service.fetchAllHostsFromBiz(Integer.valueOf(bizId), null);
        printInstances(instances);
    }

    public static void getSetsFromBiz(String[] args) {
        BluekingCmdbApiService service = getService();
        String bizId = args[1];
        List<Map<String, Object>> instances = service.fetchAllSetFromBiz(Integer.valueOf(bizId), null);
        printInstances(instances);
    }

    public static void getHostTopoFromBiz(String[] args) {
        BluekingCmdbApiService service = getService();
        String bizId = args[1];
        List<BizHostsTopoResponse.HostTopo> instances
                = service.fetchAllHostBizTopoFromBiz(Integer.valueOf(bizId), Arrays.asList("bk_host_id", "bk_host_innerip"));
        for (BizHostsTopoResponse.HostTopo instance : instances) {
            String hostId = instance.getHost().get("bk_host_id").toString();
            String hostIp = instance.getHost().get("bk_host_innerip").toString();
            for (BizHostsTopoResponse.TopoSet topoSet : instance.getTopo()) {
                String setName = topoSet.getBk_set_name().toString();
                for (BizHostsTopoResponse.TopoModule topoModule : topoSet.getModule()) {
                    String moduleName = topoModule.getBk_module_name();
                    System.out.println(String.format("%s,%s,%s,%s", hostId, hostIp, setName, moduleName));
                }
            }
        }
    }

    public static void getInstanceRelations(String[] args) {
        BluekingCmdbApiService service = getService();
        String modelId = args[1];
        String asstName = args[2];
        List<InstanceRelationResponse> relationResponseList =
                service.fetchAllRelationByModel(modelId, Collections.singletonList(asstName));
        for (InstanceRelationResponse instanceRelationResponse : relationResponseList) {
            System.out.println(String.format("%s(%d) --%s--> %s(%d)",
                    instanceRelationResponse.getSrcModel(),
                    instanceRelationResponse.getSrcId(),
                    instanceRelationResponse.getRelType(),
                    instanceRelationResponse.getDstModel(),
                    instanceRelationResponse.getDstId()
            ));
        }
    }

    public static void getClassifications(String[] args) {
        BluekingCmdbApiService service = getService();
        for (BKClassification bkClassification : service.fetchAllClassification()) {
            System.out.println(String.format("%s:%s",
                    bkClassification.getClassificationId(),
                    bkClassification.getClassificationName()));
        }
    }


    public static void getModels(String[] args) {
        BluekingCmdbApiService service = getService();
        List<BKModel> bkModels = service.fetchAllModel(null, null);
        for (BKModel bkModel : bkModels) {
            System.out.println(String.format("%s:%s",
                    bkModel.getObjectId(),
                    bkModel.getName()));
        }
    }

    public static void getModelAttributes(String[] args) {
        BluekingCmdbApiService service = getService();
        String modelId = args[1];
        List<BKModelAttribute> bkModelAttributes = service.fetchModelAttribute(modelId);
        for (BKModelAttribute bkModelAttribute : bkModelAttributes) {
            System.out.println(String.format("%s(%s, %s, %d)", bkModelAttribute.getId(),
                    bkModelAttribute.getName(),
                    bkModelAttribute.getType(),
                    bkModelAttribute.getIndex()));
            if (bkModelAttribute.getType().equals("enum")) {
                System.out.println("Enum: ");
                for (Map.Entry<String, String> enumOption : bkModelAttribute.getEnumOption().entrySet()) {
                    System.out.println(String.format("%s: %s", enumOption.getKey(), enumOption.getValue()));
                }
            }
        }
    }

    static void printInstances(List<Map<String, Object>> instances) {
        for (Map<String, Object> instance : instances) {
            System.out.println(JSONUtil.parse(instance).toStringPretty());
        }
    }
}
