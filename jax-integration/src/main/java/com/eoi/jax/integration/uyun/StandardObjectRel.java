package com.eoi.jax.integration.uyun;

import java.util.Objects;

public class StandardObjectRel {

    private String svid;

    private String dvid;

    private String type;

    public String getSvid() {
        return svid;
    }

    public void setSvid(String svid) {
        this.svid = svid;
    }

    public String getDvid() {
        return dvid;
    }

    public void setDvid(String dvid) {
        this.dvid = dvid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StandardObjectRel that = (StandardObjectRel) o;
        return Objects.equals(svid, that.svid) &&
                Objects.equals(dvid, that.dvid) &&
                Objects.equals(type, that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(svid, dvid, type);
    }
}
