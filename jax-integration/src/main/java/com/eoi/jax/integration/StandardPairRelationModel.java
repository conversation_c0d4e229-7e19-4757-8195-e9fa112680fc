package com.eoi.jax.integration;

import cn.hutool.core.lang.Tuple;
import com.eoi.jax.integration.clickhouse.CKColDef;
import com.eoi.jax.integration.clickhouse.CkRow;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public class StandardPairRelationModel  implements CkRow {

    @CKColDef(definition = "date Date")
    private LocalDate date;

    @CKColDef(definition = "srcId String")
    private String srcId;

    @CKColDef(definition = "srcType String")
    private String srcType;

    @CKColDef(definition = "srcProps Map(String, String)")
    private HashMap<String, String> srcProps;

    @CKColDef(definition = "rel String")
    private String rel;

    @CKColDef(definition = "dstId String")
    private String dstId;

    @CKColDef(definition = "dstType String")
    private String dstType;

    @CKColDef(definition = "dstProps Map(String, String)")
    private HashMap<String, String> dstProps;

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getSrcId() {
        return srcId;
    }

    public void setSrcId(String srcId) {
        this.srcId = srcId;
    }

    public String getSrcType() {
        return srcType;
    }

    public void setSrcType(String srcType) {
        this.srcType = srcType;
    }

    public HashMap<String, String> getSrcProps() {
        return srcProps;
    }

    public void setSrcProps(HashMap<String, String> srcProps) {
        this.srcProps = srcProps;
    }

    public String getRel() {
        return rel;
    }

    public void setRel(String rel) {
        this.rel = rel;
    }

    public String getDstId() {
        return dstId;
    }

    public void setDstId(String dstId) {
        this.dstId = dstId;
    }

    public String getDstType() {
        return dstType;
    }

    public void setDstType(String dstType) {
        this.dstType = dstType;
    }

    public HashMap<String, String> getDstProps() {
        return dstProps;
    }

    public void setDstProps(HashMap<String, String> dstProps) {
        this.dstProps = dstProps;
    }

    static {
        columns = Arrays.stream(StandardPairRelationModel.class.getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(CKColDef.class))
                .map(f -> new Tuple(f, f.getAnnotation(CKColDef.class).definition()))
                .collect(Collectors.toList());
    }

    private static List<Tuple> columns;

    @Override
    public int colSize() {
        return columns.size();
    }

    @Override
    public String getColName(int index) {
        Field field = columns.get(index).get(0);
        return field.getName();
    }

    @Override
    public String getColDef(int index) {
        return columns.get(index).get(1);
    }

    @Override
    public Object getColValue(int index) {
        try {
            Field field = columns.get(index).get(0);
            return field.get(this);
        } catch (Exception ex) {
            return null;
        }
    }
}
