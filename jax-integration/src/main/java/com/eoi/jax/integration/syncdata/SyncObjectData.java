package com.eoi.jax.integration.syncdata;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.integration.api.ClickHouseHelper;
import com.eoi.jax.integration.api.JaxWebApiService;
import com.eoi.jax.integration.clickhouse.ClickhouseWriter;
import com.eoi.jax.integration.model.JaxConfig;
import com.eoi.jax.integration.model.syncconfig.CmdbClickhouseConfig;
import com.eoi.jax.integration.model.syncconfig.CmdbNebulaConfig;
import com.eoi.jax.integration.model.sysconfig.CkWideTableConfig;
import com.eoi.jax.integration.model.sysconfig.SysNebulaConfig;
import com.eoi.jax.integration.model.sysconfig.SystemConfig;
import com.eoi.jax.integration.model.widetable.ImportHistory;
import com.eoi.jax.integration.util.ComUtil;
import com.eoi.jax.integration.util.YamlUtil;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.GnuParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/10
 */
public class SyncObjectData {
    private static final Logger LOGGER = LoggerFactory.getLogger(SyncObjectData.class);
    private static final String LOAD_DATE_OPTION = "loadDate";
    private static final String CONFIG_OPTION = "config";
    private static final String SAVE_DAY_OPTION = "saveDay";
    private static final String ATOMIC_OPTION = "atomic";
    private static final String JAX_ADDRESS_OPTION = "jaxAddress";
    private static final String JAX_APIKEY_OPTION = "jaxApiKey";
    private static final String JAX_SECRETKEY_OPTION = "jaxSecretKey";
    private static JaxWebApiService jaxWebApiService;

    public static void main(String[] args) {
        Options option = createOption();

        if (args.length < 1) {
            printHelpMessage(option);
            System.exit(-1);
        }

        SyncContext syncContext = null;
        try {
            syncContext = buildSyncContext(option, args);
            LOGGER.info("运行参数:{}", syncContext);
        } catch (Exception e) {
            LOGGER.error("参数解析错误", e);
            System.exit(-1);
        }

        SyncDataToClickhouseExecutor clickhouseExecutor = null;
        SyncDataToNebulaExecutor nebulaExecutor = null;
        boolean success = false;
        long successObjectRows = 0L, successRelationRows = 0L;
        try {
            LOGGER.info("同步数据开始");
            clickhouseExecutor = new SyncDataToClickhouseExecutor(syncContext);
            clickhouseExecutor.exec();

            nebulaExecutor = new SyncDataToNebulaExecutor(syncContext);
            nebulaExecutor.exec();

            LOGGER.info("同步数据成功");

            notifyJax(syncContext, nebulaExecutor.getImageSpace());

            success = true;

            clickhouseExecutor.execAfterSuccess();
            nebulaExecutor.execAfterSuccess();

            successObjectRows = nebulaExecutor.getWriteTagCount();
            successRelationRows = nebulaExecutor.getWriteRelationCount();

        } catch (Exception e) {
            success = false;
            LOGGER.error("同步数据异常", e);
        } finally {
            if (Boolean.FALSE == success && syncContext.getAutomic()) {
                LOGGER.info("同步数据失败，开始回滚");
                if (clickhouseExecutor.needRollback()) {
                    clickhouseExecutor.rollback();
                }
                if (nebulaExecutor.needRollback()) {
                    nebulaExecutor.rollback();
                }
            }
            IoUtil.close(clickhouseExecutor);
            IoUtil.close(nebulaExecutor);
        }
        writeExecutionLog(syncContext, success, successObjectRows, successRelationRows);

        if (!success) {
            System.exit(-1);
        }
    }


    private static Options createOption() {
        Options opts = new Options();
        opts.addOption(LOAD_DATE_OPTION, true, "加载日期，格式:yyyy-MM-dd");
        opts.addOption(CONFIG_OPTION, true, "Yaml配置文件路径");
        opts.addOption(SAVE_DAY_OPTION, true, "保存天数");
        opts.addOption(ATOMIC_OPTION, true, "原子性保证");
        opts.addOption(JAX_ADDRESS_OPTION, true, "jax地址，多个用逗号隔开，例如:***************:9900");
        opts.addOption(JAX_APIKEY_OPTION, true, "jax请求apiKey");
        opts.addOption(JAX_SECRETKEY_OPTION, true, "jax请求secretKey");
        return opts;
    }

    private static void printHelpMessage(Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.setOptionComparator(null);
        formatter.printHelp("可选参数:", options);
    }

    private static SyncContext buildSyncContext(Options option, String[] args) throws Exception {
        String loadDate = null;
        String config = null;
        CommandLineParser parser = new GnuParser();
        CommandLine commandLine = parser.parse(option, args);
        loadDate = commandLine.getOptionValue(LOAD_DATE_OPTION);
        DateUtil.parse(loadDate, "yyyy-MM-dd");

        if (commandLine.hasOption(CONFIG_OPTION)) {
            config = commandLine.getOptionValue(CONFIG_OPTION);
        }
        String saveDate = commandLine.hasOption(SAVE_DAY_OPTION) ?
            commandLine.getOptionValue(SAVE_DAY_OPTION) : "3";
        String automic = commandLine.hasOption(ATOMIC_OPTION) ?
            commandLine.getOptionValue(ATOMIC_OPTION) : "true";
        JaxConfig jaxConfig = null;
        if (commandLine.hasOption(JAX_ADDRESS_OPTION)) {
            jaxConfig = new JaxConfig();
            jaxConfig.setAddress(commandLine.getOptionValue(JAX_ADDRESS_OPTION));
            jaxConfig.setApiKey(commandLine.getOptionValue(JAX_APIKEY_OPTION));
            jaxConfig.setSecretKey(commandLine.getOptionValue(JAX_SECRETKEY_OPTION));
        } else {
            jaxConfig = ComUtil.getEnvJaxConfig();
        }

        SyncContext syncContext = new SyncContext();
        syncContext.setLoadDate(loadDate);
        syncContext.setSyncDateTime(LocalDateTime.now());
        syncContext.setSaveDay(Integer.parseInt(saveDate));
        syncContext.setAutomic(Boolean.parseBoolean(automic));
        syncContext.setJaxConfig(jaxConfig);
        if (StrUtil.isNotBlank(config)) {
            SyncContext yamlSyncContext = YamlUtil.loadPathYaml(config, SyncContext.class);
            syncContext.setClickhouse(yamlSyncContext.getClickhouse());
            syncContext.setNebula(yamlSyncContext.getNebula());
        } else {
            if (Objects.isNull(jaxConfig)) {
                throw new RuntimeException("获取到的jax地址配置为空");
            }
            jaxConfig.verify();
            jaxWebApiService = new JaxWebApiService(jaxConfig);
            syncContext.setClickhouse(fetchClickhouseConfig());
            syncContext.setNebula(fetchNebulaConfig());
        }
        return syncContext;
    }


    private static void writeExecutionLog(SyncContext syncContext, boolean success,
                                          Long successObjectRows, Long successRelationRows) {
        CmdbClickhouseConfig clickhouseConfig = syncContext.getClickhouse();
        try (ClickHouseHelper clickHouseHelper = new ClickHouseHelper(CollUtil.newArrayList(clickhouseConfig.getJdbc()),
            clickhouseConfig.getUsername(), clickhouseConfig.getPassword(), clickhouseConfig.getCluster())) {

            ImportHistory importHistory = new ImportHistory();
            importHistory.setDate(LocalDate.parse(syncContext.getLoadDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            importHistory.setSource("all");
            importHistory.setExecTime(syncContext.getSyncDateTime());
            importHistory.setSuccess(success ? 1 : 0);
            importHistory.setSuccessObjectRows(successObjectRows.intValue());
            importHistory.setSuccessRelationRows(successRelationRows.intValue());

            LOGGER.info("记录执行日志:{}", JSONUtil.toJsonStr(importHistory));
            ClickhouseWriter.writeBatch(CollUtil.newArrayList(importHistory), clickhouseConfig.getImportLogTable(),
                clickHouseHelper.getConnection(), false);

        } catch (Exception e) {
            LOGGER.error("写入日志异常", e);
        }
    }


    private static void notifyJax(SyncContext syncContext, String imageSpace) {
        SystemConfig ckSystemConfig = jaxWebApiService.getSystemConfig(18L);
        Map<String, Object> ckWideTableConfigMap = ckSystemConfig.getSetting();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        ckWideTableConfigMap.put("usedPartition", syncContext.getSyncDateTime().format(dtf));
        jaxWebApiService.updateSystemConfig(ckSystemConfig);
        LOGGER.info("通知中台使用CK宽表分区:{}成功", JSONUtil.toJsonStr(ckWideTableConfigMap));

        SystemConfig nebulaSystemConfig = jaxWebApiService.getSystemConfig(19L);
        Map<String, Object> nebulaCmdbConfigMap = nebulaSystemConfig.getSetting();
        nebulaCmdbConfigMap.put("imageSpace", imageSpace);
        jaxWebApiService.updateSystemConfig(nebulaSystemConfig);
        LOGGER.info("通知中台使用Nebula镜像图空间:{}成功", JSONUtil.toJsonStr(nebulaCmdbConfigMap));

    }

    private static CmdbClickhouseConfig fetchClickhouseConfig() {
        SystemConfig systemConfig = jaxWebApiService.getSystemConfig(18L);
        if (Objects.isNull(systemConfig) || Objects.isNull(systemConfig.getId())) {
            throw new RuntimeException("中台'系统管理-维度建模-对象实例CK宽表配置'不正确");
        }
        CkWideTableConfig ckWideTableConfig = BeanUtil.mapToBean(systemConfig.getSetting(), CkWideTableConfig.class, false);

        String setting = systemConfig.getDatasourceResp().getSetting();
        JSONObject datasourceConfig = JSONUtil.parseObj(setting);

        CmdbClickhouseConfig clickhouseConfig = new CmdbClickhouseConfig();
        List<String> ckJdbcUrl = jaxWebApiService.getCkJdbcUrl(systemConfig.getDsId());
        clickhouseConfig.setJdbc(ckJdbcUrl.get(RandomUtil.randomInt(0, ckJdbcUrl.size())));
        clickhouseConfig.setUsername(datasourceConfig.getStr("ckUsername"));
        clickhouseConfig.setPassword(datasourceConfig.getStr("ckPassword"));
        clickhouseConfig.setCluster(datasourceConfig.getStr("ckClusterName"));
        clickhouseConfig.setTmpObjectTable(ckWideTableConfig.getCmdbOdsTable());
        clickhouseConfig.setTmpRelationTable(ckWideTableConfig.getCmdbOdsRelationTable());
        clickhouseConfig.setObjectTable(ckWideTableConfig.getCmdbWideTable());
        clickhouseConfig.setRelationTable(ckWideTableConfig.getCmdbRelationWideTable());
        clickhouseConfig.setImportLogTable(ckWideTableConfig.getImportLogTable());

        clickhouseConfig.verify();
        return clickhouseConfig;
    }

    private static CmdbNebulaConfig fetchNebulaConfig() {
        SystemConfig systemConfig = jaxWebApiService.getSystemConfig(19L);
        if (Objects.isNull(systemConfig) || Objects.isNull(systemConfig.getId())) {
            throw new RuntimeException("中台'系统管理-维度建模-对象实例Nebula配置'不正确");
        }
        SysNebulaConfig sysNebulaConfig = BeanUtil.mapToBean(systemConfig.getSetting(), SysNebulaConfig.class, false);

        String setting = systemConfig.getDatasourceResp().getSetting();
        JSONObject datasourceConfig = JSONUtil.parseObj(setting);

        CmdbNebulaConfig nebulaConfig = new CmdbNebulaConfig();
        nebulaConfig.setGraphAddress(datasourceConfig.getStr("address"));
        nebulaConfig.setMetaAddress(datasourceConfig.getStr("metaAddress"));
        nebulaConfig.setUsername(datasourceConfig.getStr("username"));
        nebulaConfig.setPassword(datasourceConfig.getStr("password"));
        nebulaConfig.setBaseSpace(sysNebulaConfig.getBaseSpace());
        nebulaConfig.setImageSpace(sysNebulaConfig.getImageSpace());
        nebulaConfig.setRelationEdge(sysNebulaConfig.getRelationEdge());
        nebulaConfig.verify();
        return nebulaConfig;
    }

}
