package com.eoi.jax.integration.uyun;

import com.eoi.jax.integration.easycmdb.ClickhouseConfig;

import java.util.List;

public class FetchConfig {

    private String baseUrl;

    private String appKey;

    private ClickhouseConfig clickhouse;

    private List<FetchObject> objects;

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public ClickhouseConfig getClickhouse() {
        return clickhouse;
    }

    public void setClickhouse(ClickhouseConfig clickhouse) {
        this.clickhouse = clickhouse;
    }

    public List<FetchObject> getObjects() {
        return objects;
    }

    public void setObjects(List<FetchObject> objects) {
        this.objects = objects;
    }
}
