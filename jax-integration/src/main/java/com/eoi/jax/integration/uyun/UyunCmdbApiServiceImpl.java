package com.eoi.jax.integration.uyun;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.*;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.integration.uyun.model.request.*;
import com.eoi.jax.integration.uyun.model.response.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class UyunCmdbApiServiceImpl implements UyunCmdbApiService {

    private static final int CONNECTION_TIMEOUT = 5000;

    private static final int SOCKET_TIMEOUT = 15000;

    private final String baseUrl;

    private final String apiKey;

    public UyunCmdbApiServiceImpl(String baseUrl, String apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }


    private Object doExecute(HttpRequest r, TypeReference responseTypeReference) {
        r.contentType("application/json")
                .setConnectionTimeout(CONNECTION_TIMEOUT)
                .setReadTimeout(SOCKET_TIMEOUT);
        HttpResponse response = r.execute();
        if (response.getStatus() != 200) {
            throw new HttpException(String.format("Request return status code %d (not 200)", response.getStatus()));
        }
        String body = response.body();
        return JSONUtil.toBean(body, responseTypeReference, true);
    }

    public List<ModelLayer> getAllLayer() {
        UyunRequestModelLayer uyunRequestModelLayer = new UyunRequestModelLayer(this.apiKey);
        HttpRequest r = uyunRequestModelLayer.convert(this.baseUrl);
        TypeReference responseTypeReference = uyunRequestModelLayer.getResposneTypeReference();
        return (List<ModelLayer>) doExecute(r, responseTypeReference);
    }

    public List<Model> getModelByLayer(String code) {
        UyunRequestModelByLayer uyunRequestModelByLayer = new UyunRequestModelByLayer(this.apiKey, code);
        HttpRequest r = uyunRequestModelByLayer.convert(this.baseUrl);
        TypeReference responseTypeReference = uyunRequestModelByLayer.getResposneTypeReference();
        return (List<Model>) doExecute(r, responseTypeReference);
    }

    public List<Attribute> getAttributeByModel(String modelCode) {
        UyunRequestAttributesByModel uyunRequestAttributesByModel = new UyunRequestAttributesByModel(this.apiKey, modelCode);
        HttpRequest r = uyunRequestAttributesByModel.convert(this.baseUrl);
        TypeReference responseTypeReference = uyunRequestAttributesByModel.getResposneTypeReference();
        return (List<Attribute>) doExecute(r, responseTypeReference);
    }

    public List<Relation> getAllRelation() {
        UyunRequestRelation uyunRequestRelation = new UyunRequestRelation(this.apiKey);
        HttpRequest r = uyunRequestRelation.convert(this.baseUrl);
        TypeReference responseTypeReference = uyunRequestRelation.getResposneTypeReference();
        return (List<Relation>) doExecute(r, responseTypeReference);
    }

    public List getAllResourceByModel(String modelCode, List<String> requiredFields) {
        UyunRequestResourceByModel uyunRequestResourceByModel = new UyunRequestResourceByModel(this.apiKey, modelCode);
        uyunRequestResourceByModel.setRequiredFields(requiredFields);
        return fetchAllDataWithPage(uyunRequestResourceByModel, this::getOnePageResourceByModel);
    }

    private List<Map<String, Object>> getOnePageResourceByModel(UyunPagableRequest request) {
        UyunRequestResourceByModel uyunRequestResourceByModel = (UyunRequestResourceByModel) request;
        HttpRequest r = uyunRequestResourceByModel.convert(this.baseUrl);
        TypeReference responseTypeReference = uyunRequestResourceByModel.getResposneTypeReference();
        DataList<Map<String, Object>> dataList =  (DataList<Map<String, Object>>) doExecute(r, responseTypeReference);
        return dataList.getDataList();
    }

    public List getAllRelationInstanceByModel(String srcCode, String dstCode, String typeCode) {
        UyunRequestRelationInstance uyunRequestRelationInstance = new UyunRequestRelationInstance(
                this.apiKey,
                srcCode,
                dstCode,
                typeCode
        );
        return fetchAllDataWithPage(uyunRequestRelationInstance, this::getOnePageRelationInstance);
    }

    private List<RelationInstance> getOnePageRelationInstance(UyunPagableRequest request) {
        UyunRequestRelationInstance uyunRequestRelationInstance = (UyunRequestRelationInstance) request;
        HttpRequest r = uyunRequestRelationInstance.convert(this.baseUrl);
        TypeReference responseTypeReference = uyunRequestRelationInstance.getResposneTypeReference();
        DataList<RelationInstance> dataList =  (DataList<RelationInstance>) doExecute(r, responseTypeReference);
        return dataList.getDataList();
    }

    private List fetchAllDataWithPage(
            UyunPagableRequest searchRequest,
            Function<UyunPagableRequest, List> underLineFunction) {
        int start = 0;
        List allData = new ArrayList<>();
        List onePageData;
        do {
            searchRequest.setPage_index(start);
            searchRequest.setPage_size(500); //最大500
            onePageData = underLineFunction.apply(searchRequest);
            if (onePageData != null && !onePageData.isEmpty()) {
                allData.addAll(onePageData);
            }
            start = start + 500;
        } while (onePageData != null && onePageData.size() >= 500);
        return allData;
    }
}
