package com.eoi.jax.integration.easycmdb;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.serialize.GlobalSerializeMapping;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.clickhouse.jdbc.ClickHouseDataSource;
import com.eoi.jax.integration.Const;
import com.eoi.jax.integration.StandardObjectModel;
import com.eoi.jax.integration.StandardRelModel;
import com.eoi.jax.integration.api.JaxWebApiService;
import com.eoi.jax.integration.model.nacos.NacosEntityResponse;
import com.eoi.jax.integration.clickhouse.ClickhouseWriter;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import javax.sql.DataSource;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

public class FetchProgram {

    private static Logger logger = LoggerFactory.getLogger(FetchProgram.class);

    private static AviatorEvaluatorInstance aviatorInstance = AviatorEvaluator.newInstance();

    private static final String SOURCE_NAME = "easycmdb";

    @SuppressWarnings("all")
    public static void main(String[] args) throws Exception {
        if (args.length < 1) {
            System.err.println("至少添加一个参数，nacos注册中心名称和配置id，格式为<nacos注册中心名称>/<配置id>，例如: nacos77/easycmdb-sync");
            System.exit(-1);
        }

        // 获取reportDate
        String reportDate = System.getProperty("jax.reportDate");
        if (reportDate == null) {
            System.err.println("需通过-Djax.reportDate=yyyy-MM-dd设置报表日期，一般设置为前一天");
            System.exit(-1);
        }
        final LocalDate d = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(reportDate).toLocalDate();
        final java.time.LocalDate date = java.time.LocalDate.of(d.getYear(), d.getMonthOfYear(), d.getDayOfMonth());

        String isDemo = System.getenv("DEMO"); // 是否读取demo数据

        // 设置请求和响应参数的正反序列化处理，主要处理page_size这种不满足驼峰命名的情况
        GlobalSerializeMapping.put(InstanceSearchRequest.class, new InstanceSearchRequest());
        GlobalSerializeMapping.put(InstanceSearchResponse.class, new InstanceSearchResponse());

        String addresses = System.getenv(Const.API_ADDRESS_ENV_KEY);
        String accessKey = System.getenv(Const.API_AK_ENV_KEY);
        String secretKey = System.getenv(Const.API_SK_ENV_KEY);
        Integer pageSize = Optional.ofNullable(System.getenv(Const.PAGE_SIZE)).map(Integer::valueOf).orElse(500);
        List<String> addressList = Arrays.asList(addresses.split(","));

        JaxWebApiService jaxWebApiService = new JaxWebApiService(addressList, accessKey, secretKey);
        String nacosName = args[0].split("/")[0];
        String configName = args[0].split("/")[1];
        NacosEntityResponse nacosInfo = jaxWebApiService.queryNacosEntityByName(nacosName);
        if (nacosInfo == null) {
            System.err.println(String.format("指定的nacos不存在，请确认地址管理里面存在该nacos地址: %s", nacosName));
            System.exit(-1);
        }

        Yaml yaml = new Yaml();
        FetchConfig fetchConfig = null;
        // 从nacos获取配置
        try {
            String serverAddr = nacosInfo.getAddress();
            Properties properties = new Properties();
            properties.put("serverAddr", serverAddr);
            properties.put("namespace", nacosInfo.getNamespace());
            properties.put("username", nacosInfo.getUsername());
            properties.put("password", nacosInfo.getPassword());
            ConfigService configService = NacosFactory.createConfigService(properties);
            String content = configService.getConfig(configName, "DEFAULT_GROUP", 5000);
            fetchConfig = yaml.loadAs(content, FetchConfig.class);
        } catch (NacosException e) {
            System.err.println(String.format("获取nacos配置失败, %s", e.toString()));
            System.exit(-1);
        }
        // 从resource的yaml中获取配置
        // InputStream inputStream = TestProgram.class.getResourceAsStream("/test_fetch.yaml");
        // fetchConfig = yaml.loadAs(inputStream, FetchConfig.class);

        String baseUrl = fetchConfig.getBaseUrl();
        ApiPath apiPath = new ApiPath();
        apiPath.setLoginPath(fetchConfig.getLoginPath());
        apiPath.setModelInstanceSearchPath(fetchConfig.getModelInstanceSearchPath());
        EasyCmdbApiService easyCmdbApiService;
        if (isDemo != null) {
            easyCmdbApiService = new MockCmdbApiService();
        } else {
            easyCmdbApiService = new EasyCmdbApiServiceImpl(baseUrl, apiPath);
        }
        logger.info("baseUrl: {}, logInUrl: {}, searchPath: {}, userName: {}",
            baseUrl, apiPath.getLoginPath(), apiPath.getModelInstanceSearchPath(), fetchConfig.getUserName());

        // 登录
        logger.info("===开始登录===");
        easyCmdbApiService.login(fetchConfig.getUserName(), fetchConfig.getPassword());
        logger.info("===登录成功===");
        List<FetchObject> fetchObjectList = fetchConfig.getObjects();
        logger.info("===准备拉取{}个模型===", fetchObjectList.size());

        Map<String, StandardObject> allObjects = new HashMap<>();
        Set<StandardObjectRel> allRelations = new HashSet<>();

        for (FetchObject fetchObject : fetchObjectList) {
            InstanceSearchRequest instanceSearchRequest = new InstanceSearchRequest();
            instanceSearchRequest.setPageSize(pageSize);
            List<String> fieldsExpression = fetchObject.getAttributes().values().stream().distinct().collect(Collectors.toList());
            // 通过解析表达式，获取字段引用
            Set<String> fields = new HashSet<>();
            for (String fExpression : fieldsExpression) {
                try {
                    Expression expression = aviatorInstance.compile(fExpression);
                    fields.addAll(expression.getVariableNames());
                } catch (Exception ex) {
                    logger.error("表达式[{}]编译异常", fExpression, ex);
                    throw ex;
                }
            }

            if (fetchObject.getRelations() != null) {
                fields.addAll(new ArrayList<>(fetchObject.getRelations().keySet()));
            }
            instanceSearchRequest.setFields(fields.stream().collect(Collectors.toList()));
            instanceSearchRequest.setIgnoreMissingFieldError(true);
            try {
                if (StrUtil.isNotBlank(fetchObject.getQuery())) {
                    instanceSearchRequest.setQuery(JSONUtil.toBean(fetchObject.getQuery(), new TypeReference<Map<String, Object>>(){}, false));
                }
            } catch (Exception ex) {
                logger.error("解析query条件失败,id:{}, query:{}", fetchObject.getId(), fetchObject.getQuery(), ex);
                throw ex;
            }
            logger.info("===准备拉取模型实例: {}===", fetchObject.getId());
            List<Map<String, Object>> instances = easyCmdbApiService.fetchAllInstance(fetchObject.getId(), instanceSearchRequest);
            logger.info("===模型{} 拉取到{}个实例===", fetchObject.getId(), instances.size());

            mergeInstances(instances, fetchObject, allObjects, allRelations);
        }

        logger.info("===完整性校验===");
        Iterator<StandardObjectRel> iterator = allRelations.iterator();
        int count = 0;
        while (iterator.hasNext()) {
            StandardObjectRel relation = iterator.next();
            if (!allObjects.containsKey(relation.getSvid()) || !allObjects.containsKey(relation.getDvid())) {
                count++;
                iterator.remove();
            }
        }
        logger.info("===由于关系中存在instanceId在对象集合中找不到，移除{}个关系===", count);

        // 结果写文件
//        FileOutputStream objectsOutput = new FileOutputStream("allObjects.json", false);
//        FileOutputStream relsOutput = new FileOutputStream("allRelations.json", false);
//        dumpAllObjectToWriter(objectsOutput, allObjects.values());
//        dumpAllRelationsToWriter(relsOutput, allRelations);
//        objectsOutput.close();
//        relsOutput.close();

        // 结果写ck
        List<StandardObjectModel> instances = allObjects.values().stream().map(a -> {
            StandardObjectModel som = new StandardObjectModel();
            som.setDate(date);
            som.setSource(SOURCE_NAME);
            som.setTag(a.getTag());
            som.setVid(a.getVid());
            som.setProperties(JSONUtil.toJsonStr(a.getProperties()));
            return som;
        }).collect(Collectors.toList());

        List<StandardRelModel> relations = allRelations.stream().map(a -> {
            StandardRelModel srm = new StandardRelModel();
            srm.setDate(date);
            srm.setSource(SOURCE_NAME);
            srm.setSvid(a.getSvid());
            srm.setDvid(a.getDvid());
            srm.setType(a.getType());
            return srm;
        }).collect(Collectors.toList());

        logger.info("===初始化Clickhouse连接===");
        Properties properties = new Properties();
        properties.setProperty("user", fetchConfig.getClickhouse().getUser());
        properties.setProperty("password", fetchConfig.getClickhouse().getPassword());
        properties.setProperty("socket_timeout", "300000");
        properties.setProperty("autoCommit", "true");
        // properties.setProperty("compress_algorith, "gzip");
        properties.setProperty("compress", "0");
        DataSource dataSource = new ClickHouseDataSource(fetchConfig.getClickhouse().getJdbc(), properties);
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            ClickhouseWriter.writeBatch(instances, fetchConfig.getClickhouse().getObjectTable(), connection, false);
            ClickhouseWriter.writeBatch(relations, fetchConfig.getClickhouse().getRelationTable(), connection, false);
        } catch (Exception ex) {
            System.err.println(String.format("写入Clickhouse时出错, %s", ex.toString()));
            throw ex;
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 解析结果并合并
     *
     * @param instances    某模型的全部实例
     * @param fetchObject  模型拉取的配置信息
     * @param allObjects   实例全量集合，合并至此
     * @param allRelations 关系全量集合，合并至此
     */
    public static void mergeInstances(List<Map<String, Object>> instances, FetchObject fetchObject,
                                      Map<String, StandardObject> allObjects, Set<StandardObjectRel> allRelations) {
        // TAG 从 rename 获取
        String tag = fetchObject.getRename();
        // 试图编译出过滤表达式
        Expression exp = null;
        if (StrUtil.isNotBlank(fetchObject.getFilter())) {
            try {
                exp = aviatorInstance.compile(fetchObject.getFilter());
            } catch (Exception ex) {
                logger.error("过滤表达式[{}]编译异常", fetchObject.getFilter(), ex);
                throw ex;
            }
        }

        for (Map<String, Object> instance : instances) {
            if (exp != null) {
                // 表达式过滤
                Boolean ok = (Boolean) exp.execute(instance);
                if (!ok) {
                    continue;
                }
            }

            String currentVid = instance.get("instanceId").toString();
            StandardObject standardObject = new StandardObject();
            standardObject.setTag(tag);
            standardObject.setVid(currentVid);
            // 将实例属性提取出来
            for (Entry<String, String> attribute : fetchObject.getAttributes().entrySet()) {
                try {
                    // key 是目标字段，value是源字段的表达式
                    // 表达式要先compile，再eval
                    Expression expression = aviatorInstance.compile(attribute.getValue());
                    Object evalResult = expression.execute(instance);
                    String key = attribute.getKey();
                    standardObject.getProperties().put(key, evalResult);
                } catch (Exception ex) {
                    logger.error("执行表达式[{}]异常", fetchObject.getFilter(), ex);
                    throw ex;
                }
            }
            // 实例合并到allObjects
            allObjects.put(currentVid, standardObject);

            // 提取关系
            if (fetchObject.getRelations() != null) {
                for (Entry<String, String> rel : fetchObject.getRelations().entrySet()) {
                    // 遍历某种关系，保存下来
                    String targetRel = rel.getValue();
                    boolean reserve = false;
                    // 目标关系以!开始表示关系要取反
                    if (targetRel.startsWith("!")) {
                        reserve = true;
                        targetRel = targetRel.substring(1);
                    }
                    // 获取这种关系类型的全部对端实例
                    List relatedObjects = (List) instance.get(rel.getKey());
                    if (relatedObjects == null) {
                        continue;
                    }
                    for (Object relatedObject : relatedObjects) {
                        Map<String, Object> ro = (Map<String, Object>) relatedObject;
                        String instanceId = ro.get("instanceId").toString();
                        StandardObjectRel standardObjectRel = new StandardObjectRel();
                        standardObjectRel.setType(targetRel);
                        if (reserve) {
                            standardObjectRel.setSvid(instanceId);
                            standardObjectRel.setDvid(currentVid);
                        } else {
                            standardObjectRel.setSvid(currentVid);
                            standardObjectRel.setDvid(instanceId);
                        }
                        allRelations.add(standardObjectRel);
                    }
                }
            }
        }
    }

    public static void dumpAllObjectToWriter(OutputStream stream, Collection<StandardObject> objects) throws Exception {
        OutputStreamWriter writer = new OutputStreamWriter(stream, StandardCharsets.UTF_8);
        for (StandardObject value : objects) {
            JSONUtil.parseObj(value).write(writer, 0, 0);
            writer.write("\n");
        }
        writer.flush();
    }

    public static void dumpAllRelationsToWriter(OutputStream stream, Collection<StandardObjectRel> rels) throws Exception {
        OutputStreamWriter writer = new OutputStreamWriter(stream, StandardCharsets.UTF_8);
        for (StandardObjectRel value : rels) {
            JSONUtil.parseObj(value).write(writer, 0, 0);
            writer.write("\n");
        }
        writer.flush();
    }
}
