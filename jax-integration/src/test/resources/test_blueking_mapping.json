[{"id": "2704907227169792", "configId": "2704907227038720", "sourceModel": "biz", "targetModelTbName": "Application", "sourceModelPath": "bk_organization/biz", "sourceModelPathZh": "组织架构/业务", "targetModel": "2691716208231424", "targetModelPath": "1961930996253696/2374106287277056//2691716208231424", "targetModelPathZh": "业务应用层/应用/应用系统", "filterScript": "biz_code == \"EPCC\"", "fieldMapping": [{"sourceField": "\"biz_\" + biz_code", "useScript": true, "targetField": "objectId"}, {"sourceField": "bk_biz_name", "useScript": false, "targetField": "objectName"}, {"sourceField": "bk_biz_name", "useScript": false, "targetField": "appName"}], "relationMapping": []}, {"id": "2704907227202560", "configId": "2704907227038720", "sourceModel": "host", "targetModelTbName": "System", "sourceModelPath": "bk_host_manage/host", "sourceModelPathZh": "主机管理/主机", "targetModel": "2691716210197504", "targetModelPath": "1748732335950848/1961930995729408//2691716210197504", "targetModelPathZh": "系统层/操作系统/操作系统", "filterScript": "1 == 1", "fieldMapping": [{"sourceField": "\"host_\" + bk_host_outerip", "useScript": true, "targetField": "objectId"}, {"sourceField": "bk_host_outerip", "useScript": false, "targetField": "objectName"}, {"sourceField": "bk_state", "useScript": false, "targetField": "objectStatus"}, {"sourceField": "bk_host_outerip", "useScript": false, "targetField": "ip"}, {"sourceField": "bk_os_type", "useScript": false, "targetField": "osType"}, {"sourceField": "bk_cpu_architecture", "useScript": false, "targetField": "arch"}, {"sourceField": "bk_os_name + \" \" + bk_os_version + \" \" + bk_os_bit", "useScript": true, "targetField": "os"}], "relationMapping": [{"sourceRelationType": "bk_mainline", "sourceModelRelation": "host_bk_mainline_module", "targetRelationType": "belongTo", "targetRelationTypeName": "归属", "reverse": false}, {"sourceRelationType": "run", "sourceModelRelation": "host_run_dameng", "targetRelationType": "runningOn", "targetRelationTypeName": "运行于", "reverse": true}, {"sourceRelationType": "run", "sourceModelRelation": "host_run_MySQL", "targetRelationType": "runningOn", "targetRelationTypeName": "运行于", "reverse": true}]}, {"id": "2704907227202561", "configId": "2704907227038720", "sourceModel": "module", "targetModelTbName": "AppModule", "sourceModelPath": "bk_biz_topo/module", "sourceModelPathZh": "业务拓扑/模块", "targetModel": "2704890573784064", "targetModelPath": "1961930996253696/2374106287277056/2704890573784064", "targetModelPathZh": "业务应用层/应用/应用模块", "filterScript": "1 == 1", "fieldMapping": [{"sourceField": "\"module_\" + bk_module_name + \"_\" + bk_module_id", "useScript": true, "targetField": "objectId"}, {"sourceField": "bk_module_name", "useScript": false, "targetField": "objectName"}, {"sourceField": "\"AppModule\"", "useScript": true, "targetField": "objectType"}], "relationMapping": [{"sourceRelationType": "bk_mainline", "sourceModelRelation": "module_bk_mainline_set", "targetRelationType": "belongTo", "targetRelationTypeName": "归属", "reverse": false}]}, {"id": "2704907227202562", "configId": "2704907227038720", "sourceModel": "set", "targetModelTbName": "AppCluster", "sourceModelPath": "bk_biz_topo/set", "sourceModelPathZh": "业务拓扑/集群", "targetModel": "2704887437755392", "targetModelPath": "1961930996253696/2374106287277056/2704887437755392", "targetModelPathZh": "业务应用层/应用/应用集群", "filterScript": "1 == 1", "fieldMapping": [{"sourceField": "\"set_\" + bk_set_name", "useScript": true, "targetField": "objectId"}, {"sourceField": "bk_service_status", "useScript": false, "targetField": "objectStatus"}, {"sourceField": "bk_set_name", "useScript": false, "targetField": "objectName"}, {"sourceField": "\"set\"", "useScript": true, "targetField": "objectType"}], "relationMapping": [{"sourceRelationType": "bk_mainline", "sourceModelRelation": "set_bk_mainline_biz", "targetRelationType": "belongTo", "targetRelationTypeName": "归属", "reverse": false}]}, {"id": "2705549346964480", "configId": "2704907227038720", "sourceModel": "dameng", "targetModelTbName": "dm", "sourceModelPath": "db/dameng", "sourceModelPathZh": "数据库/达梦", "targetModel": "1748732341160960", "targetModelPath": "1748732335950848/1748732336049152/1748732341160960", "targetModelPathZh": "系统层/数据库/达梦数据库", "filterScript": "1 == 1", "fieldMapping": [{"sourceField": "\"dm_\" + bk_inst_name", "useScript": true, "targetField": "objectId"}, {"sourceField": "bk_inst_name", "useScript": false, "targetField": "objectName"}, {"sourceField": "\"dm\"", "useScript": true, "targetField": "objectType"}], "relationMapping": []}, {"id": "2705568187646976", "configId": "2704907227038720", "sourceModel": "MySQL", "targetModelTbName": "MYSQL", "sourceModelPath": "db/MySQL", "sourceModelPathZh": "数据库/MySQL", "targetModel": "1767781378067456", "targetModelPath": "1748732335950848/1748732336049152/1767781378067456", "targetModelPathZh": "系统层/数据库/MYSQL", "filterScript": "1 == 1", "fieldMapping": [{"sourceField": "\"mysql_\" + bk_inst_name", "useScript": true, "targetField": "objectId"}, {"sourceField": "bk_inst_name", "useScript": false, "targetField": "objectName"}, {"sourceField": "\"MYSQL\"", "useScript": true, "targetField": "objectType"}], "relationMapping": []}]