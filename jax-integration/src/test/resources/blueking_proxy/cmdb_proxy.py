import json
import re
import uuid
from urllib.parse import urlparse, parse_qs, urlunparse, urlencode

import requests
import yaml
from flask import Flask, request, jsonify

app = Flask(__name__)

# 被代理服务的host和port配置
PROXY_HOST = 'http://**************'  # 例如: 'http://127.0.0.1'
PROXY_PORT = 18080  # 例如: 8000

# 固定的校验值
FIXED_BK_APP_CODE = 'cmdb'
FIXED_BK_APP_SECRET = 'LFVUYDHCBNWQSGTPMIEROKJXZALBXGUC'
FIXED_BK_USERNAME = 'bk-cmdb'

# 请求超时时间（秒）
REQUEST_TIMEOUT = 15

# 读取cc.yaml配置文件 (UTF-8编码)
with open('D:\my-workspaces\python3-test\gtja\cc.yaml', 'r', encoding='utf-8') as file:
    config_list = yaml.safe_load(file)

# 将配置转换为字典，使用path为键
config_dict = {str(item['path']).removesuffix("/"): item for item in config_list}


# 根据路径查找对应的代理配置
def find_proxy_config(path):
    return config_dict.get(path.removesuffix("/"))


# 移除不必要的参数
def remove_auth_params(params, keys_to_remove):
    return {k: v for k, v in params.items() if k not in keys_to_remove}


# 验证固定的 bk_app_code, bk_app_secret, bk_username
def validate_credentials(bk_app_code, bk_app_secret, bk_username):
    return (bk_app_code == FIXED_BK_APP_CODE and
            bk_app_secret == FIXED_BK_APP_SECRET and
            bk_username == FIXED_BK_USERNAME)


# 构建标准返回格式
def build_response(result, code, message, data=None):
    return {
        "result": result,
        "code": code,
        "message": message,
        "permission": None,
        "request_id": str(uuid.uuid4()).replace("-", ""),  # 生成唯一请求ID
        "data": data or []
    }


# 处理代理方的返回格式，并转换为标准格式
def handle_proxy_response(proxy_response):
    proxy_json = proxy_response.json()
    if proxy_json.get('bk_error_code') == 0:
        # 请求成功
        return build_response(True, 0, "success", proxy_json.get('data'))
    else:
        # 请求失败
        return build_response(False, proxy_json.get('bk_error_code'), proxy_json.get('bk_error_msg'))

# 解析并替换dest_path中的占位符
def replace_path_variables(dest_path, args):
    pattern = re.compile(r'{(\w+)}')
    def replacer(match):
        key = match.group(1)
        return str(args.get(key, '-1')) # 如果没有找到对应的参数，保留原占位符
    return pattern.sub(replacer, dest_path)


# 添加固定前缀的代理路径
@app.route('/api/c/compapi/<path:path>', methods=['GET', 'POST'])
def proxy(path):
    proxy_config = find_proxy_config('/' + path)
    if not proxy_config:
        return jsonify(build_response(False, 404, 'No proxy configuration found')), 404

    # 从请求中提取认证信息
    bk_app_code = request.args.get('bk_app_code') or request.json.get('bk_app_code')
    bk_app_secret = request.args.get('bk_app_secret') or request.json.get('bk_app_secret')
    bk_username = request.args.get('bk_username') or request.json.get('bk_username')

    # 校验固定的认证参数
    if not validate_credentials(bk_app_code, bk_app_secret, bk_username):
        return jsonify(build_response(False, 1199027, '授权信息查询失败')), 403

    # 添加必要的请求头
    headers = {
        'HTTP_BLUEKING_SUPPLIER_ID': '0',
        'BK_USER': bk_username,
        'Content-Type': 'application/json'
    }

    # 解析并替换dest_path中的占位符为请求参数中的实际值
    # 获取请求参数（GET请求从request.args，POST请求从request.json）
    request_params = request.args if request.method == 'GET' else (request.json or {})
    dest_path = replace_path_variables(proxy_config['dest_path'], request_params)

    # 构建完整的代理服务URL
    proxy_url = f"{PROXY_HOST}:{PROXY_PORT}{dest_path}"

    try:
        # 处理GET请求
        if request.method == 'GET':
            parsed_url = urlparse(proxy_url)
            query_params = parse_qs(parsed_url.query)

            # 移除认证参数
            query_params = remove_auth_params(query_params, ['bk_app_code', 'bk_app_secret', 'bk_username'])

            # 构建目标URL
            dest_url = urlunparse(parsed_url._replace(query=urlencode(query_params, doseq=True)))

            # 使用with确保请求后自动关闭连接
            with requests.get(dest_url, headers=headers, timeout=REQUEST_TIMEOUT) as response:
                return jsonify(handle_proxy_response(response)), response.status_code

        # 处理POST请求
        elif request.method == 'POST':
            json_data = request.json or {}

            # 打印请求的request body（JSON格式）
            print("Request Body (JSON):", json.dumps(json_data, indent=4, ensure_ascii=False))

            # 移除认证参数
            json_data = remove_auth_params(json_data, ['bk_app_code', 'bk_app_secret', 'bk_username'])

            # 使用with确保请求后自动关闭连接
            with requests.post(proxy_url, json=json_data, headers=headers, timeout=REQUEST_TIMEOUT) as response:
                return jsonify(handle_proxy_response(response)), response.status_code

    except requests.exceptions.Timeout:
        return jsonify(build_response(False, 504, '请求超时')), 504


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
