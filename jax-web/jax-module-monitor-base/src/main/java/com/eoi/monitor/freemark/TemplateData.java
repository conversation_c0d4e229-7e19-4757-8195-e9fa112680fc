package com.eoi.monitor.freemark;

import java.util.Map;

public class TemplateData {
    private Map<String, String> labels;
    private Map<String, String> annotations;
    private Double sample;
    private Double base;
    private Double value;

    public TemplateData(Map<String, String> labels, Double sample, Double base, Double value, Map<String, String> annotations) {
        this.labels = labels;
        this.sample = sample;
        this.base = base;
        this.annotations = annotations;
        this.value = value;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public Map<String, String> getAnnotations() {
        return annotations;
    }

    public void setAnnotations(Map<String, String> annotations) {
        this.annotations = annotations;
    }

    public void setLabels(Map<String, String> labels) {
        this.labels = labels;
    }

    public void setSample(Double sample) {
        this.sample = sample;
    }

    public void setBase(Double base) {
        this.base = base;
    }

    public Map<String, String> getLabels() {
        return labels;
    }

    public Double getSample() {
        return sample;
    }

    public Double getBase() {
        return base;
    }
}
