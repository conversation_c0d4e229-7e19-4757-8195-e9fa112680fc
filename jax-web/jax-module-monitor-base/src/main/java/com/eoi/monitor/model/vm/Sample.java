package com.eoi.monitor.model.vm;

import java.util.Map;

public class Sample {
    private Point point;
    private Map<String, String> labels;

    public Point getPoint() {
        return point;
    }

    public void setPoint(Point point) {
        this.point = point;
    }

    public Map<String, String> getLabels() {
        return labels;
    }

    public void setLabels(Map<String, String> labels) {
        this.labels = labels;
    }

    // TODO
    @Override
    public String toString() {
        return "Sample{" +
            "point=" + point +
            ", labels=" + labels +
            '}';
    }
}
