package com.eoi.jax.web.lifecycle.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.LifecycleBackupKindEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.lifecycle.admin.thread.JobTriggerHelper;
import com.eoi.jax.web.lifecycle.enumrate.BatchStatusEnum;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleJobStatusEnum;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleJobTriggerTypeEnum;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleTaskStatusEnum;
import com.eoi.jax.web.lifecycle.model.job.LifecycleBatchRetry;
import com.eoi.jax.web.lifecycle.model.strategy.LifecycleStrategyResp;
import com.eoi.jax.web.lifecycle.service.LifecycleBatchLogService;
import com.eoi.jax.web.lifecycle.service.LifecycleBatchService;
import com.eoi.jax.web.lifecycle.service.LifecycleJobService;
import com.eoi.jax.web.lifecycle.service.LifecycleJobStatusService;
import com.eoi.jax.web.repository.entity.TbLifecycleBatch;
import com.eoi.jax.web.repository.entity.TbLifecycleBatchJob;
import com.eoi.jax.web.repository.entity.TbLifecycleBatchTask;
import com.eoi.jax.web.repository.entity.TbLifecycleStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.eoi.jax.web.lifecycle.enumrate.LifecycleJobStatusEnum.*;

/**
 * 任务状态变化相关
 *
 * @Author: yaru.ma
 * @Date: 2024/11/11
 */
@Service
public class LifecycleJobStatusServiceImpl implements LifecycleJobStatusService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LifecycleJobStatusServiceImpl.class.getName());

    @Autowired
    private JaxRepository jaxRepository;
    @Autowired
    private LifecycleBatchLogService logService;
    @Lazy
    @Autowired
    private LifecycleJobService jobService;
    @Lazy
    @Autowired
    private LifecycleBatchService batchService;

    @Override
    public void stopJob(Long jobId, String reason) {
        AtomicInteger times = new AtomicInteger(0);
        stopJob(jobId, reason, times);
    }

    public void stopJob(Long jobId, String reason, AtomicInteger times) {
        // 以下状态可以直接停止
        List<String> canStoppedStatus = Arrays.asList(QUEUE.code(),
                SCHEDULED.code(), TRIGGER.code());
        TbLifecycleBatchJob entity = jaxRepository.lifecycleJobService().getById(jobId);
        if (entity == null) {
            return;
        }
        Date now = new Date();
        TbLifecycleBatchJob job = new TbLifecycleBatchJob();
        job.setEndTime(now);
        job.setDuration(calcDuration(entity, now));
        boolean success = true;
        if (canStoppedStatus.contains(entity.getStatus())) {
            success = updateJob(jobId, job, false, STOPPED.code(), canStoppedStatus.toArray(new String[]{}));
        } else if (RUNNING.equals(entity.getStatus())) {
            success = updateJob(jobId, job, false, STOPPING.code(), RUNNING.code());
        } else {
            return;
        }
        TbLifecycleBatchJob currentJob = jaxRepository.lifecycleJobService().getById(jobId);
        if (!success && times.incrementAndGet() < 5) {
            LOGGER.warn("【{}】第{}次停止任务失败,任务状态不稳定: {}->{}", jobId, times.get(), entity.getStatus(), currentJob.getStatus());
            stopJob(jobId, reason, times);
        } else if (!success && times.get() >= 5) {
            LOGGER.error("【{}】第{}次停止任务失败,任务状态: {}->{}", jobId, times.get(), entity.getStatus(), currentJob.getStatus());
        } else {
            LOGGER.warn("【{}】停止任务成功,{} -> {}", jobId, entity.getStatus(), currentJob.getStatus());
            logService.log("由于" + reason + ",停止任务", jobId);
            if (STOPPED.equals(currentJob.getStatus())) {
                updateBatchStatus(entity.getBatchId());
            }
        }
    }

    @Override
    public void retry(Long jobId, Boolean fromBeginning) {
        TbLifecycleBatchJob job = jaxRepository.lifecycleJobService().getById(jobId);
        if (!FAILED.equals(job.getStatus())) {
            throw new BizException(ResponseCode.STATUS_NOT_ALLOWED.getCode(), "任务状态不是失败，无法重试");
        }
        boolean executorEmpty = false;
        if (BooleanUtil.isTrue(fromBeginning)) {
            // 批次的执行器地址为空，置空执行器地址
            TbLifecycleBatch batch = jaxRepository.lifecycleBatchService().getById(job.getBatchId());
            executorEmpty = Common.isEmpty(JsonUtil.decode2ListString(batch.getExecutorAddress()));
            // task 重新生成
            jobService.reGenTasks(Arrays.asList(job));
        }
        jaxRepository.lifecycleJobService().update(new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                .eq(TbLifecycleBatchJob::getId, jobId)
                .eq(TbLifecycleBatchJob::getStatus, FAILED)
                .set(TbLifecycleBatchJob::getTriggerType, LifecycleJobTriggerTypeEnum.RETRY.code())
                .set(TbLifecycleBatchJob::getStatus, QUEUE)
                .set(TbLifecycleBatchJob::getStartTime, null)
                .set(executorEmpty, TbLifecycleBatchJob::getExecutorAddress, null)
                .set(TbLifecycleBatchJob::getUpdateTime, new Date()));
        logService.log("任务手动重试", jobId);
        updateBatchStatus(job.getBatchId());
    }

    @Override
    public void batchRetry(LifecycleBatchRetry req) {
        if (req.getBatchId() == null && CollUtil.isEmpty(req.getIds())) {
            return;
        }
        List<TbLifecycleBatchJob> jobs = jaxRepository.lifecycleJobService().list(new LambdaQueryWrapper<TbLifecycleBatchJob>()
                .eq(req.getBatchId() != null && CollUtil.isEmpty(req.getIds()),
                        TbLifecycleBatchJob::getBatchId, req.getBatchId())
                .in(CollUtil.isNotEmpty(req.getIds()), TbLifecycleBatchJob::getId, req.getIds())
                .eq(TbLifecycleBatchJob::getStatus, FAILED));
        if (CollUtil.isEmpty(jobs)) {
            return;
        }
        Map<Long, List<TbLifecycleBatchJob>> batchMap = jobs.stream().collect(Collectors.groupingBy(TbLifecycleBatchJob::getBatchId));
        // 重新生成task
        jobService.reGenTasks(jobs);
        batchMap.forEach((batchId, batchJobs) -> {
            TbLifecycleBatch batch = jaxRepository.lifecycleBatchService().getById(batchId);
            boolean executorEmpty = Common.isEmpty(JsonUtil.decode2ListString(batch.getExecutorAddress()));
            // 是否指定执行器，不指定则清空执行器
            // 状态变更
            batchJobs.forEach(job -> logService.log("任务手动重试", job.getId()));
            jaxRepository.lifecycleJobService().update(new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                    .in(TbLifecycleBatchJob::getId, batchJobs.stream().map(TbLifecycleBatchJob::getId).collect(Collectors.toList()))
                    .eq(TbLifecycleBatchJob::getStatus, FAILED)
                    .set(executorEmpty, TbLifecycleBatchJob::getExecutorAddress, null)
                    .set(TbLifecycleBatchJob::getStartTime, null)
                    .set(TbLifecycleBatchJob::getTriggerType, LifecycleJobTriggerTypeEnum.RETRY.code())
                    .set(TbLifecycleBatchJob::getStatus, QUEUE)
                    .set(TbLifecycleBatchJob::getUpdateTime, new Date()));
            updateBatchStatus(batchId);
        });
    }

    @Override
    public void stoppedJob(Long jobId) {
        TbLifecycleBatchJob tbLifecycleBatchJob = jaxRepository.lifecycleJobService().getById(jobId);
        checkJobStatus(tbLifecycleBatchJob, "停止", STOPPING);
        Date endTime = new Date();
        TbLifecycleBatchJob updateJob = new TbLifecycleBatchJob();
        updateJob.setStatus(STOPPED.code());
        updateJob.setDuration(calcDuration(tbLifecycleBatchJob, endTime));
        updateJob.setEndTime(endTime);
        // 更新状态
        Boolean update = updateJob(jobId, updateJob, false, STOPPED.code(), STOPPING.code());
        LOGGER.info("【{}】停止任务,{} -> {}, 结果{}", jobId, STOPPING.code(), STOPPED.code(), update);
        updateBatchStatus(tbLifecycleBatchJob.getBatchId());
    }

    @Override
    public void jobSuccess(Long jobId) {
        Date endTime = new Date();
        TbLifecycleBatchJob job = jaxRepository.lifecycleJobService().getById(jobId);
        Long executionTime = calcDuration(job, endTime);
        jaxRepository.lifecycleJobService().update(
                new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                        .eq(TbLifecycleBatchJob::getId, jobId)
                        .set(TbLifecycleBatchJob::getStatus, SUCCESS.code())
                        .set(TbLifecycleBatchJob::getEndTime, endTime)
                        .set(TbLifecycleBatchJob::getDuration, executionTime)
        );
        if (job.getChildJobId() != null) {
            List<TbLifecycleBatchTask> jobTasks = jaxRepository.lifecycleTaskService().getJobTasks(jobId);
            List<TbLifecycleBatchTask> childJobTasks = jaxRepository.lifecycleTaskService().getJobTasks(job.getChildJobId());
            // 复制结果给子任务
            copyResult(jobTasks, childJobTasks);
            logService.log(String.format("%s与当前任务有重复,部分步骤跳过", LifecycleBackupKindEnum.fromString(job.getAction()).message()),
                    job.getChildJobId());
        }
        updateBatchStatus(job.getBatchId());
    }

    @Override
    public void jobFailed(Long id, String error, String stack, String message) {
        Date endTime = new Date();
        TbLifecycleBatchJob tbLifecycleBatchJob = jaxRepository.lifecycleJobService().getById(id);
        int failedCount = tbLifecycleBatchJob.getFailedCount() + 1;
        if (failedCount <= tbLifecycleBatchJob.getRetryCount()) {
            // 批次的执行器地址为空，置空执行器地址
            TbLifecycleBatch batch = jaxRepository.lifecycleBatchService().getById(tbLifecycleBatchJob.getBatchId());
            boolean executorEmpty = Common.isEmpty(JsonUtil.decode2ListString(batch.getExecutorAddress()));
            boolean update = jaxRepository.lifecycleJobService().update(
                    new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                            .eq(TbLifecycleBatchJob::getId, id)
                            .eq(TbLifecycleBatchJob::getStatus, RUNNING.code())
                            .set(TbLifecycleBatchJob::getStatus, QUEUE.code())
                            .set(executorEmpty, TbLifecycleBatchJob::getExecutorAddress, null)
                            .set(TbLifecycleBatchJob::getFailedCount, failedCount)
            );
            if (!update) {
                return;
            }
            logService.log("任务失败，将会重试第" + failedCount + "次", id);
            // task 重新生成
            jobService.reGenTasks(Arrays.asList(tbLifecycleBatchJob));
            return;
        }
        Long executionTime = calcDuration(tbLifecycleBatchJob, endTime);
        jaxRepository.lifecycleJobService().update(
                new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                        .eq(TbLifecycleBatchJob::getId, id)
                        .set(TbLifecycleBatchJob::getStatus, FAILED.code())
                        .set(TbLifecycleBatchJob::getEndTime, endTime)
                        .set(TbLifecycleBatchJob::getFailedCount, failedCount)
                        .set(TbLifecycleBatchJob::getDuration, executionTime)
                        .set(TbLifecycleBatchJob::getErrorCode, error)
                        .set(TbLifecycleBatchJob::getFailedReason, message)
                        .set(TbLifecycleBatchJob::getErrorStack, stack)
        );
        childJobFailed(jaxRepository.lifecycleJobService().getById(id));
        updateBatchStatus(tbLifecycleBatchJob.getBatchId());
    }

    @Override
    public void jobDirectFailed(Long id, String error, String stack, String message) {
        Date now = new Date();
        TbLifecycleBatchJob tbLifecycleBatchJob = jaxRepository.lifecycleJobService().getById(id);
        int failedCount = tbLifecycleBatchJob.getRetryCount() + 1;
        boolean update = jaxRepository.lifecycleJobService().update(
                new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                        .eq(TbLifecycleBatchJob::getId, id)
                        .ne(TbLifecycleBatchJob::getStatus, FAILED.code())
                        .set(TbLifecycleBatchJob::getStatus, FAILED.code())
                        .set(TbLifecycleBatchJob::getTriggerTime, now)
                        .set(TbLifecycleBatchJob::getStartTime, now)
                        .set(TbLifecycleBatchJob::getEndTime, now)
                        // 直接失败
                        .set(TbLifecycleBatchJob::getFailedCount, failedCount)
                        .set(TbLifecycleBatchJob::getDuration, 0)
                        .set(TbLifecycleBatchJob::getErrorCode, error)
                        .set(TbLifecycleBatchJob::getFailedReason, message)
                        .set(TbLifecycleBatchJob::getErrorStack, stack)
        );
        if (update) {
            childJobFailed(jaxRepository.lifecycleJobService().getById(id));
            updateBatchStatus(tbLifecycleBatchJob.getBatchId());
        }
    }

    @Override
    public void jobDirectSuccess(List<TbLifecycleBatchJob> jobs) {
        if (CollUtil.isEmpty(jobs)) {
            return;
        }
        Date now = new Date();
        for (TbLifecycleBatchJob job : jobs) {
            TbLifecycleBatchJob successJob = jaxRepository.lifecycleJobService().getById(job.getLastJobId());
            if (successJob != null) {
                String message = String.format("[%s]表的[%s]分区已于[%s]成功备份,执行器为[%s],经检查数据并无变动,跳过本次备份",
                        job.getTbName(), job.getPartitionId(),
                        DateUtil.format(successJob.getScheduledTime(), "yyyy-MM-dd"), job.getExecutorAddress());
                logService.log(message, job.getId());
            } else {
                logService.log(String.format("[%s]表的[%s]分区已成功备份过,经检查数据并无变动,跳过本次备份",
                        job.getTbName(), job.getParentJobId()), job.getId());
            }
        }
        jaxRepository.lifecycleJobService().update(
                new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                        .in(TbLifecycleBatchJob::getId, jobs.stream().map(TbLifecycleBatchJob::getId).collect(Collectors.toList()))
                        .set(TbLifecycleBatchJob::getStatus, SUCCESS.code())
                        .set(TbLifecycleBatchJob::getTriggerTime, now)
                        .set(TbLifecycleBatchJob::getStartTime, now)
                        .set(TbLifecycleBatchJob::getEndTime, now)
                        .set(TbLifecycleBatchJob::getDuration, 0)
        );
    }


    private void checkJobStatus(TbLifecycleBatchJob tbLifecycleBatchJob, String operation, LifecycleJobStatusEnum... codes) {
        List<String> expectStatus = Arrays.stream(codes).map(LifecycleJobStatusEnum::code).collect(Collectors.toList());
        Assert.isTrue(expectStatus.contains(tbLifecycleBatchJob.getStatus()),
                "当前任务状态为" + tbLifecycleBatchJob.getStatus() + "不允许" + operation);
    }

    private void childJobFailed(TbLifecycleBatchJob job) {
        Long childJobId = job.getChildJobId();
        if (childJobId == null) {
            return;
        }
        TbLifecycleBatchJob childJob = jaxRepository.lifecycleJobService().getById(childJobId);
        if (!QUEUE.equals(childJob.getStatus())) {
            LOGGER.debug("child job is not scheduled, ignore");
            return;
        }
        // 标记任务为失败
        jaxRepository.lifecycleJobService().update(
                new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                        .eq(TbLifecycleBatchJob::getId, childJobId)
                        .set(TbLifecycleBatchJob::getStatus, FAILED.code())
                        .set(TbLifecycleBatchJob::getEndTime, new Date())
                        .set(TbLifecycleBatchJob::getErrorCode, job.getErrorCode())
                        .set(TbLifecycleBatchJob::getDuration, 0)
                        .set(TbLifecycleBatchJob::getFailedReason, job.getFailedReason())
                        .set(TbLifecycleBatchJob::getErrorStack, job.getErrorStack())
        );
        // task上也失败，复制job的
        List<TbLifecycleBatchTask> jobTasks = jaxRepository.lifecycleTaskService().getJobTasks(job.getId());
        List<TbLifecycleBatchTask> childJobTasks = jaxRepository.lifecycleTaskService().getJobTasks(job.getChildJobId());
        // 复制结果
        copyResult(jobTasks, childJobTasks);
        LifecycleBackupKindEnum actionEnum = LifecycleBackupKindEnum.fromString(job.getAction());
        logService.log(String.format("父任务【%s】已失败,本任务直接标记为失败，请重试父级任务", actionEnum.message()), childJobId);
        childJobFailed(childJob);
    }

    public void copyResult(List<TbLifecycleBatchTask> jobTasks, List<TbLifecycleBatchTask> childJobTasks) {
        for (TbLifecycleBatchTask jobTask : jobTasks) {
            if (LifecycleTaskStatusEnum.SCHEDULED.equals(jobTask.getStatus())) {
                continue;
            }
            for (TbLifecycleBatchTask childJobTask : childJobTasks) {
                if (childJobTask.getTaskBean().equals(jobTask.getTaskBean())) {
                    childJobTask.setResult(jobTask.getResult());
                    childJobTask.setFailedReason(jobTask.getFailedReason());
                    childJobTask.setStatus(jobTask.getStatus());
                    childJobTask.setErrorStack(jobTask.getErrorStack());
                    jaxRepository.lifecycleTaskService().updateById(childJobTask);
                }
            }
        }
    }


    private Boolean updateJob(Long jobId, TbLifecycleBatchJob job, Boolean updateExecutor, String status, String... expectSatus) {
        LambdaUpdateWrapper<TbLifecycleBatchJob> updateWrapper = new LambdaUpdateWrapper<TbLifecycleBatchJob>()
                .eq(TbLifecycleBatchJob::getId, jobId)
                .in(TbLifecycleBatchJob::getStatus, expectSatus)
                .set(TbLifecycleBatchJob::getStatus, status)
                .set(Objects.nonNull(job.getScheduledTime()), TbLifecycleBatchJob::getScheduledTime, job.getScheduledTime())
                .set(Objects.nonNull(job.getStartTime()), TbLifecycleBatchJob::getStartTime, job.getStartTime())
                .set(Objects.nonNull(job.getEndTime()), TbLifecycleBatchJob::getEndTime, job.getEndTime())
                .set(Objects.nonNull(job.getFailedCount()), TbLifecycleBatchJob::getFailedCount, job.getFailedCount())
                .set(Objects.nonNull(job.getDuration()), TbLifecycleBatchJob::getDuration, job.getDuration())
                .set(Objects.nonNull(job.getTriggerType()), TbLifecycleBatchJob::getTriggerType, job.getTriggerType())
                .set(Objects.nonNull(job.getFailedReason()), TbLifecycleBatchJob::getFailedReason, job.getFailedReason())
                .set(Objects.nonNull(job.getErrorCode()), TbLifecycleBatchJob::getErrorCode, job.getErrorCode())
                .set(Objects.nonNull(job.getErrorStack()), TbLifecycleBatchJob::getErrorStack, job.getErrorStack())
                .set(updateExecutor, TbLifecycleBatchJob::getExecutorAddress, job.getExecutorAddress());
        return jaxRepository.lifecycleJobService().update(updateWrapper);
    }

    private static Long calcDuration(TbLifecycleBatchJob tbLifecycleBatchJob, Date now) {
        return tbLifecycleBatchJob == null || tbLifecycleBatchJob.getStartTime() == null ?
                0 : Math.max((long) Math.ceil((now.getTime() - tbLifecycleBatchJob.getStartTime().getTime()) * 1D / 1000.0), 1);
    }

    @Override
    public String updateBatchStatus(Long batchId) {
        TbLifecycleBatch batch = jaxRepository.lifecycleBatchService().getById(batchId);
        List<TbLifecycleBatchJob> jobs = jaxRepository.lifecycleJobService().list(new LambdaQueryWrapper<TbLifecycleBatchJob>()
                .eq(TbLifecycleBatchJob::getBatchId, batch.getId())
                .select(TbLifecycleBatchJob::getId, TbLifecycleBatchJob::getStatus,
                        TbLifecycleBatchJob::getDuration, TbLifecycleBatchJob::getSourceDataBytes));
        int totalJobs = jobs.size();
        long totalBytes = jobs.stream().filter(x -> x.getSourceDataBytes() != null)
                .mapToLong(TbLifecycleBatchJob::getSourceDataBytes).sum();
        long successJob = jobs.stream().filter(j -> SUCCESS.equals(j.getStatus())).count();
        long failedJob = jobs.stream()
                .filter(j -> FAILED.equals(j.getStatus()) || STOPPED.equals(j.getStatus()))
                .count();
        String status = BatchStatusEnum.RUNNING.code();
        if (successJob == totalJobs) {
            status = BatchStatusEnum.SUCCESS.code();
        } else if (failedJob == totalJobs) {
            status = BatchStatusEnum.FAILED.code();
        } else if (successJob + failedJob == totalJobs) {
            status = BatchStatusEnum.PART_SUCCESS.code();
        }
        boolean isRunning = BatchStatusEnum.RUNNING.equals(batch.getStatus());
        boolean update = jaxRepository.lifecycleBatchService().update(new LambdaUpdateWrapper<TbLifecycleBatch>()
                .eq(TbLifecycleBatch::getId, batch.getId())
                // 状态无变化
                .eq(TbLifecycleBatch::getStatus, batch.getStatus())
                .set(isRunning && !BatchStatusEnum.RUNNING.equals(status), TbLifecycleBatch::getStatus, status)
                .set(isRunning && batch.getEndTime() == null && !BatchStatusEnum.RUNNING.equals(status),
                        TbLifecycleBatch::getEndTime, new Date())
                .set(TbLifecycleBatch::getSuccessJob, successJob)
                .set(TbLifecycleBatch::getFailedJob, failedJob)
                .set(TbLifecycleBatch::getTotalJob, totalJobs)
                .set(TbLifecycleBatch::getDataBytes, totalBytes)
                .set(TbLifecycleBatch::getUpdateTime, new Date())
        );
        if (!BatchStatusEnum.RUNNING.equals(status) && isRunning && update) {
            if (!status.equals(batch.getStatus())) {
                LOGGER.info("{}-{}生命周期执行批次状态更新,{} -> {}", batch.getBatchNo(), batch.getId(), batch.getStatus(), status);
            }
            // 是否生成了下个批次
            TbLifecycleStrategy strategy = jaxRepository.lifecycleStrategyService().getById(batch.getStrategyId());
            if (ObjectUtil.equal(batch.getId(), strategy.getBatchId())) {
                try {
                    batchService.createBatch(new LifecycleStrategyResp().fromEntity(strategy));
                } catch (Exception e) {
                    LOGGER.error("[{}-{}]生成批次失败", strategy.getId(), strategy.getName(), e);
                }
            }
        }
        if (isRunning) {
            // 触发一下调度
            ThreadPoolUtil.THREAD_POOL.submit(() -> JobTriggerHelper.getInstance().triggerImmediately());
        }
        return status;
    }
}
