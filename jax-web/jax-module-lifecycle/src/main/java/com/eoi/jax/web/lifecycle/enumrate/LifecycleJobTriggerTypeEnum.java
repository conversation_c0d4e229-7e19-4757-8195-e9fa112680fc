package com.eoi.jax.web.lifecycle.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;

/**
 * 调度方式
 */
public enum LifecycleJobTriggerTypeEnum implements ICodeMessageEnum {

    SCHEDULED("SCHEDULED", "调度执行"),
    RETRY("RETRY", "重试"),;

    private final String code;

    private final String message;

    LifecycleJobTriggerTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static LifecycleJobTriggerTypeEnum fromString(String code) {
        for (LifecycleJobTriggerTypeEnum value : LifecycleJobTriggerTypeEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
