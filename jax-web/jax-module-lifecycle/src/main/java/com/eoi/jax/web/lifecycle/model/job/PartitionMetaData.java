package com.eoi.jax.web.lifecycle.model.job;

import java.util.List;

/**
 * @Author: yaru.ma
 * @Date: 2024/10/28
 */
public class PartitionMetaData {

    private Long dsId;

    private String database;

    private Long tbId;

    private String tbName;

    private String tbAlias;

    private String localTable;

    private List<String> partitions;

    private List<Long> rows;

    private List<Long> dataBytes;

    private String hiveTbName;

    private Long hiveTbId;

    private String partitionKey;

    private String hiveLocation;

    private String warning;

    public Long getDsId() {
        return dsId;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getLocalTable() {
        return localTable;
    }

    public void setLocalTable(String localTable) {
        this.localTable = localTable;
    }

    public List<String> getPartitions() {
        return partitions;
    }

    public void setPartitions(List<String> partitions) {
        this.partitions = partitions;
    }

    public List<Long> getRows() {
        return rows;
    }

    public void setRows(List<Long> rows) {
        this.rows = rows;
    }

    public List<Long> getDataBytes() {
        return dataBytes;
    }

    public void setDataBytes(List<Long> dataBytes) {
        this.dataBytes = dataBytes;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getTbAlias() {
        return tbAlias;
    }

    public void setTbAlias(String tbAlias) {
        this.tbAlias = tbAlias;
    }

    public String getHiveTbName() {
        return hiveTbName;
    }

    public void setHiveTbName(String hiveTbName) {
        this.hiveTbName = hiveTbName;
    }

    public Long getHiveTbId() {
        return hiveTbId;
    }

    public void setHiveTbId(Long hiveTbId) {
        this.hiveTbId = hiveTbId;
    }

    public String getPartitionKey() {
        return partitionKey;
    }

    public void setPartitionKey(String partitionKey) {
        this.partitionKey = partitionKey;
    }

    public String getHiveLocation() {
        return hiveLocation;
    }

    public void setHiveLocation(String hiveLocation) {
        this.hiveLocation = hiveLocation;
    }

    public String getWarning() {
        return warning;
    }

    public void setWarning(String warning) {
        this.warning = warning;
    }
}
