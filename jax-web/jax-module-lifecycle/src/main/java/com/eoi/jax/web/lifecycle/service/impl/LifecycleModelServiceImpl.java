package com.eoi.jax.web.lifecycle.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.constant.TableType;
import com.eoi.jax.web.core.common.enumrate.TableDeployActionTypeEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.model.lifecycle.TableEntityDeployEvent;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleModelScanErrorEnum;
import com.eoi.jax.web.lifecycle.model.strategy.LifecycleStrategyResp;
import com.eoi.jax.web.lifecycle.service.LifecycleBatchService;
import com.eoi.jax.web.lifecycle.service.LifecycleModelService;
import com.eoi.jax.web.lifecycle.service.LifecycleStrategyMatchService;
import com.eoi.jax.web.repository.entity.TbLifecycleBatch;
import com.eoi.jax.web.repository.entity.TbLifecycleStrategy;
import com.eoi.jax.web.repository.entity.TbLifecycleStrategyMatch;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class LifecycleModelServiceImpl implements LifecycleModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LifecycleModelServiceImpl.class.getName());

    public static final List<String> SUPPORT_TABLE_TYPE = Arrays.asList(TableType.ODS.code(), TableType.DIM.code(),
            TableType.DWD.code(), TableType.DWS.code(), TableType.ADS.code());

    @Autowired
    private JaxRepository jaxRepository;
    @Autowired
    private LifecycleBatchService batchService;
    @Autowired
    private LifecycleStrategyMatchService matchService;

    @Async
    @TransactionalEventListener(classes = TableEntityDeployEvent.class, fallbackExecution = true)
    @Override
    public void handleTableChange(TableEntityDeployEvent event) {
        if (!SUPPORT_TABLE_TYPE.contains(event.getTable().getTbType())) {
            return;
        }
        LOGGER.info("模型[{}]发生变化，生命周期策略即将处理", event.getTable().getTbName());
        try {
            Long currentStrategyId = currentStrategyId(event);
            Long lastMatchStrategyId = lastMatchStrategyId(event);
            // 是否存在变更
            if (!ObjectUtil.equal(currentStrategyId, lastMatchStrategyId)) {
                LOGGER.info("[{}]模型发生[{}]变更,生命周期策略的批次将进行更新", event.getTable().getTbName(), event.getActionTypeEnum().code());
                if (currentStrategyId != null) {
                    TbLifecycleStrategy byId = jaxRepository.lifecycleStrategyService().getById(currentStrategyId);
                    batchService.addModelInBatch(byId.getBatchId(), Arrays.asList(event.getTable().getId()));
                }
                if (lastMatchStrategyId != null) {
                    TbLifecycleStrategy byId = jaxRepository.lifecycleStrategyService().getById(lastMatchStrategyId);
                    batchService.removeModelInBatch(byId.getBatchId(), Arrays.asList(event.getTable().getId()), "模型发生删除动作");
                }
            } else if (event.getActionTypeEnum() == TableDeployActionTypeEnum.REBUILD) {
                if (lastMatchStrategyId != null) {
                    // 删除重建强制停止原有任务
                    TbLifecycleStrategy byId = jaxRepository.lifecycleStrategyService().getById(lastMatchStrategyId);
                    LOGGER.info("[{}]模型发生[{}]变更,匹配策略没有发生变更，仅停止原有任务", event.getTable().getTbName(), event.getActionTypeEnum().code());
                    batchService.removeModelInBatch(byId.getBatchId(), Arrays.asList(event.getTable().getId()), "模型发生删除动作");
                }
            }

        } catch (Exception e) {
            LOGGER.error("处理模型变更事件失败", e);
        }
    }


    /**
     * 批次变化的因素
     *
     * @param batchId
     */
    @Override
    public void handleBatchAdditionImpact(Long batchId) {
        TbLifecycleBatch batch = jaxRepository.lifecycleBatchService().existId(batchId, new BizException(ResponseCode.OPTS_NOT_EXIST));
        TbLifecycleStrategy strategy = jaxRepository.lifecycleStrategyService()
                .existId(batch.getStrategyId(), new BizException(ResponseCode.OPTS_NOT_EXIST));
        // 处理被本策略抢占的模型及策略
        List<TbLifecycleStrategyMatch> allMatches = batchService.currentMatchesInAllStrategy();
        // 本策略将会应用的模型
        List<Long> targetTbIds = allMatches.stream()
                .filter(x -> !LifecycleModelScanErrorEnum.UN_MATCH_STATUS.contains(x.getStatus()) && batchId.equals(x.getBatchId()))
                .map(TbLifecycleStrategyMatch::getTbId).collect(Collectors.toList());
        // 被抢占模型的批次ids
        Map<Long, List<TbLifecycleStrategyMatch>> batchInfluence = allMatches.stream()
                .filter(x -> targetTbIds.contains(x.getTbId()) && !LifecycleModelScanErrorEnum.UN_MATCH_STATUS.contains(x.getStatus())
                        && !x.getBatchId().equals(batchId))
                .collect(Collectors.groupingBy(TbLifecycleStrategyMatch::getBatchId));
        LOGGER.info("[{}]策略启用,纳管模型共有{}个,影响批次{}个", batchId, targetTbIds.size(), batchInfluence.size());
        if (!batchInfluence.isEmpty()) {
            batchInfluence.forEach((b, m) -> {
                LOGGER.info("[{}]策略启用,模型被抢占,被影响的批次[{}],模型个数[{}]", batchId, b, m.size());
                batchService.removeModelInBatch(b, m.stream().map(TbLifecycleStrategyMatch::getTbId).collect(Collectors.toList()),
                        String.format("[%s]策略启用", strategy.getName()));
            });
        }
    }

    /**
     * 批次变化的因素
     *
     * @param batchId
     */
    @Override
    public void handleBatchDeletionImpact(Long batchId, List<Long> matchedTbIds) {
        // 释放被本策略抢占的模型及策略
        List<TbLifecycleStrategyMatch> allMatches = batchService.currentMatchesInAllStrategy();
        Map<Long, Long> strategyMap = jaxRepository.lifecycleStrategyService().list().stream()
                .filter(x -> x.getIsEnabled() == 1)
                .collect(Collectors.toMap(x -> x.getId(), x -> x.getPriority()));
        // 一个模型只有一个批次可以生效
        Map<Long, Optional<TbLifecycleStrategyMatch>> collect = allMatches.stream()
                .filter(x -> matchedTbIds.contains(x.getTbId()) && !x.getBatchId().equals(batchId)
                        && LifecycleModelScanErrorEnum.LOW_PRIORITY.equals(x.getStatus()))
                .filter(x -> strategyMap.containsKey(x.getStrategyId()))
                .collect(Collectors.groupingBy(TbLifecycleStrategyMatch::getTbId,
                        Collectors.reducing((x, y) -> strategyMap.get(x.getStrategyId()) > strategyMap.get(y.getStrategyId()) ? x : y)
                ));
        Map<Long, List<TbLifecycleStrategyMatch>> batchInfluence = collect.values().stream()
                .filter(Optional::isPresent).map(Optional::get)
                .collect(Collectors.groupingBy(TbLifecycleStrategyMatch::getBatchId));
        LOGGER.info("[{}]策略禁用,曾经纳管模型共有{}个,影响批次{}个", batchId, matchedTbIds.size(), batchInfluence.size());
        batchInfluence.forEach((b, m) -> {
            LOGGER.info("[{}]策略禁用,模型被释放,被影响的批次号[{}],模型个数[{}]", batchId, b, m.size());
            batchService.addModelInBatch(b, m.stream().map(TbLifecycleStrategyMatch::getTbId).collect(Collectors.toList()));
        });
    }

    public Long currentStrategyId(TableEntityDeployEvent event) {
        TableDeployActionTypeEnum actionTypeEnum = event.getActionTypeEnum();
        if (TableDeployActionTypeEnum.DELETE == actionTypeEnum) {
            return null;
        }
        // 获取成功发布记录
        TbTableDeploy deploy = jaxRepository.tableDeployService().getLastSuccessTableDeploy(event.getTable().getId());
        if (ObjectUtil.isNull(deploy)) {
            LOGGER.warn("没有查询到模型[{}]的发布记录", event.getTable().getId());
            return null;
        }
        LambdaQueryWrapper<TbLifecycleStrategy> queryWrapper = new LambdaQueryWrapper<TbLifecycleStrategy>()
                .eq(TbLifecycleStrategy::getSourcePlatform, deploy.getPlatform())
                .eq(TbLifecycleStrategy::getIsEnabled, 1)
                .orderByDesc(TbLifecycleStrategy::getPriority);
        List<TbLifecycleStrategy> strategies = jaxRepository.lifecycleStrategyService().list(queryWrapper);
        // 计算这个模型应属于哪个策略
        Long newMatchStrategyId = strategies.stream()
                .map(s -> new LifecycleStrategyResp().fromEntity(s))
                .filter(s -> matchService.matchTb2Strategy(event.getTable(), s)).map(LifecycleStrategyResp::getId)
                .findFirst().orElse(null);
        return newMatchStrategyId;
    }

    public Long lastMatchStrategyId(TableEntityDeployEvent event) {
        List<TbLifecycleStrategy> allStrategies = jaxRepository.lifecycleStrategyService()
                .list(new LambdaQueryWrapper<TbLifecycleStrategy>().eq(TbLifecycleStrategy::getIsEnabled, 1));
        List<Long> curBatchIds = allStrategies.stream().map(TbLifecycleStrategy::getBatchId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        LambdaQueryWrapper<TbLifecycleStrategyMatch> matchWrapper = new LambdaQueryWrapper<>();
        matchWrapper.in(TbLifecycleStrategyMatch::getBatchId, curBatchIds);
        matchWrapper.eq(TbLifecycleStrategyMatch::getTbId, event.getTable().getId());
        matchWrapper.eq(TbLifecycleStrategyMatch::getStatus, LifecycleModelScanErrorEnum.MATCH_SUCCESS.code());
        TbLifecycleStrategyMatch lastMatch = jaxRepository.lifecycleStrategyMatchService().getOne(matchWrapper);
        return lastMatch == null ? null : lastMatch.getStrategyId();
    }

}
