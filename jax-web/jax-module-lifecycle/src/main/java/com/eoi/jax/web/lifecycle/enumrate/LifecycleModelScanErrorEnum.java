package com.eoi.jax.web.lifecycle.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;

import java.util.Arrays;
import java.util.List;

public enum LifecycleModelScanErrorEnum implements ICodeEnum {

    MATCH_SUCCESS("MATCH_SUCCESS", "匹配成功"),
    /**
     * 匹配失败
     */
    LOW_PRIORITY("LOW_PRIORITY", "低优先级"),
    UN_DEPLOYED("UN_DEPLOYED", "模型未发布"),
    PLATFORM_NOT_SUPPORT("PLATFORM_NOT_SUPPORT", "发布平台与策略不符"),
    MODEL_STRATEGY_CHANGE("MODEL_STRATEGY_CHANGE", "模型或其他策略发生变动，移除该批次纳管范围"),
    /**
     * 匹配成功，但未生效
     */
    HIVE_NOT_EXISTS("HIVE_NOT_EXISTS", "目标hive模型[%s]不存在"),
    HIVE_AUTO_CREATE("HIVE_AUTO_CREATE", "目标hive模型暂不支持自动创建"),
    HIVE_UN_DEPLOYED("HIVE_UN_DEPLOYED", "目标hive模型[%s]未发布"),
    HIVE_DEPLOY_WRONG_PLATFORM("HIVE_DEPLOY_WRONG_PLATFORM", "目标hive模型[%s]发布到了非HIVE平台上"),
    SAME_SOURCE_TARGET("SAME_SOURCE_TARGET", "源表和目的表不能是同一个模型"),
    DATASOURCE_ERROR("DATASOURCE_ERROR", "数据源异常"),
    TABLE_NOT_EXITST("TABLE_NOT_EXITST", "表实际不存在"),
    TABLE_NOT_DISTRIBUTED("TABLE_NOT_DISTRIBUTED", "模型非分布式表引擎"),
    PARTITION_UNMATCH("PARTITION_UNMATCH", "hive表已有数据分区,但其分区格式未能识别"),
    PARTITION_KEY_COUNT_NOT_SUPPORT("PARTITION_KEY_COUNT_NOT_SUPPORT", "分区字段为多个，暂不支持"),
    PARTITION_FIELD_NOT_SUPPORT("PARTITION_FIELD_NOT_SUPPORT", "hive模型需包含分区字段[%s],且分区字段类型需为STRING"),
    TARGET_HIVE_VALIDATE_FAILED("TARGET_HIVE_VALIDATE_FAILED", "目标hive模型校验未通过:%s"),
    HIVE_LOCATION_FETCH_ERROR("HIVE_LOCATION_FETCH_ERROR", "获取hive表的hdfs地址失败"),
    ENGINE_NOT_SUPPORT("ENGINE_NOT_SUPPORT", "表引擎不支持"),
    PARTITION_NOT_SUPPORT("PARTITION_NOT_SUPPORT", "表分区格式不支持"),
    FILE_PATH_HAS_UNDEFINED_PARAMS("FILE_PATH_HAS_UNDEFINED_PARAMS", "文件路径包含的部分参数无定义或无值:%s"),
    UNKNOWN_ERROR("UNKNOWN_ERROR", "未知的错误");

    /**
     * 匹配失败
     */
    public static final List<String> UN_MATCH_STATUS = Arrays.asList(
            LOW_PRIORITY.code(),
            UN_DEPLOYED.code(),
            PLATFORM_NOT_SUPPORT.code(),
            MODEL_STRATEGY_CHANGE.code()
    );


    private final String code;

    private final String message;

    LifecycleModelScanErrorEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static LifecycleModelScanErrorEnum fromString(String code) {
        for (LifecycleModelScanErrorEnum value : LifecycleModelScanErrorEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
