package com.eoi.jax.web.lifecycle.model.strategy;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * 主要适用于迁移，过期删除不需要额外配置
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LifecycleMigrateConfig {

    /**
     * 忽略早于多少天的数据
     */
    private Integer ignoreDays;

    /**
     * 迁移开始日期
     */
    private Integer backupStartDays;

    /**
     * 迁移平台
     */
    private List<String> targetPlatform;

    /**
     * 迁移路径
     */
    private String targetFilePath;

    /**
     * hive模型名生成方式
     */
    private String hiveGenerateMode;

    private Boolean autoCreateHiveTable;
    /**
     * hive表名前缀
     */
    private String hiveModelPrefix;
    private String hiveModelSuffix;

    private String hiveModelReplaceSource;
    private String hiveModelReplaceTarget;

    public Integer getIgnoreDays() {
        return ignoreDays;
    }

    public void setIgnoreDays(Integer ignoreDays) {
        this.ignoreDays = ignoreDays;
    }

    public List<String> getTargetPlatform() {
        return targetPlatform;
    }

    public void setTargetPlatform(List<String> targetPlatform) {
        this.targetPlatform = targetPlatform;
    }

    public String getTargetFilePath() {
        return targetFilePath;
    }

    public void setTargetFilePath(String targetFilePath) {
        this.targetFilePath = targetFilePath;
    }

    public String getHiveGenerateMode() {
        return hiveGenerateMode;
    }

    public void setHiveGenerateMode(String hiveGenerateMode) {
        this.hiveGenerateMode = hiveGenerateMode;
    }

    public Boolean getAutoCreateHiveTable() {
        return autoCreateHiveTable;
    }

    public void setAutoCreateHiveTable(Boolean autoCreateHiveTable) {
        this.autoCreateHiveTable = autoCreateHiveTable;
    }

    public String getHiveModelPrefix() {
        return hiveModelPrefix;
    }

    public void setHiveModelPrefix(String hiveModelPrefix) {
        this.hiveModelPrefix = hiveModelPrefix;
    }

    public String getHiveModelSuffix() {
        return hiveModelSuffix;
    }

    public void setHiveModelSuffix(String hiveModelSuffix) {
        this.hiveModelSuffix = hiveModelSuffix;
    }

    public String getHiveModelReplaceSource() {
        return hiveModelReplaceSource;
    }

    public void setHiveModelReplaceSource(String hiveModelReplaceSource) {
        this.hiveModelReplaceSource = hiveModelReplaceSource;
    }

    public String getHiveModelReplaceTarget() {
        return hiveModelReplaceTarget;
    }

    public void setHiveModelReplaceTarget(String hiveModelReplaceTarget) {
        this.hiveModelReplaceTarget = hiveModelReplaceTarget;
    }

    public Integer getBackupStartDays() {
        return backupStartDays;
    }

    public void setBackupStartDays(Integer backupStartDays) {
        this.backupStartDays = backupStartDays;
    }
}
