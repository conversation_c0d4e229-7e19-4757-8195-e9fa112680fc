package com.eoi.jax.web.lifecycle.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.enumrate.LifecycleBackupKindEnum;
import com.eoi.jax.web.core.common.enumrate.LifecyclePlatformEnum;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleModelScanErrorEnum;
import com.eoi.jax.web.lifecycle.model.match.LifecycleStrategyMatchResp;
import com.eoi.jax.web.lifecycle.model.visualization.LifecycleChartResp;
import com.eoi.jax.web.lifecycle.model.visualization.LifecycleResp;
import com.eoi.jax.web.lifecycle.model.visualization.LifecycleVisualizationResp;
import com.eoi.jax.web.lifecycle.service.LifecycleVisualizationService;
import com.eoi.jax.web.lifecycle.util.TimeIntervalUtil;
import com.eoi.jax.web.repository.entity.TbLifecycleBatch;
import com.eoi.jax.web.repository.entity.TbLifecycleStrategyMatch;
import com.eoi.jax.web.repository.search.result.VLifecycleStatisticsResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.eoi.jax.web.lifecycle.enumrate.LifecycleJobStatusEnum.SUCCESS;

/**
 * @Author: yaru.ma
 * @Date: 2024/10/30
 */
@Service
public class LifecycleVisualizationServiceImpl implements LifecycleVisualizationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LifecycleVisualizationServiceImpl.class.getName());

    @Autowired
    private JaxRepository jaxRepository;

    @Override
    public LifecycleResp statistics(String today) {
        LifecycleResp lifecycleResp = new LifecycleResp();
        LocalDate date = today == null ? LocalDate.now().minusDays(1) : LocalDate.parse(today, DateTimeFormatter.ISO_DATE);
        today = DateTimeFormatter.ISO_DATE.format(date);
        String yesterday = DateTimeFormatter.ISO_DATE.format(date.minusDays(1));
        LifecycleVisualizationResp todayStatistics = buildJobStatistics(today);
        LifecycleVisualizationResp yesterdayStatistics = buildJobStatistics(yesterday);
        lifecycleResp.setCurrentStatistics(todayStatistics);
        lifecycleResp.setPreviousDayStatistics(yesterdayStatistics);
        return lifecycleResp;
    }

    private LifecycleVisualizationResp buildJobStatistics(String date) {
        List<VLifecycleStatisticsResult> jobStatistics = jaxRepository.lifecycleStatisticsMapper().jobStatistics(date);
        LifecycleVisualizationResp resp = new LifecycleVisualizationResp();
        // 迁移数据量
        for (VLifecycleStatisticsResult jobStatistic : jobStatistics) {
            if (SUCCESS.equals(jobStatistic.getStatus())) {
                LifecycleBackupKindEnum action = LifecycleBackupKindEnum.fromString(jobStatistic.getAction());
                if (action.getTargetPlatform() == LifecyclePlatformEnum.NONE) {
                    resp.setBackUpColdTotalInBytes(resp.getBackUpColdTotalInBytes() + jobStatistic.getDataBytesSum());
                } else {
                    resp.setBackUpHotTotalInBytes(resp.getBackUpHotTotalInBytes() + jobStatistic.getDataBytesSum());
                }
            }
        }
        // 迁移总量
        resp.setBackUpTotalInBytes(ObjectUtil.defaultIfNull(jaxRepository.lifecycleStatisticsMapper().sumSuccessDataBytes(), 0L));
        // 迁移成功率
        long successJobs = jobStatistics.stream().filter(x -> SUCCESS.equals(x.getStatus()))
                .mapToLong(x -> x.getJobCount().longValue()).sum();
        long totalJobs = jobStatistics.stream().map(VLifecycleStatisticsResult::getJobCount).mapToLong(Long::longValue).sum();
        resp.setBackUpSuccessRate(totalJobs == 0 ? 0 : (double) successJobs / totalJobs);
        // 模型匹配总数
        List<TbLifecycleBatch> tbLifecycleBatches = jaxRepository.lifecycleStatisticsMapper().modelStatistics(date, offset(date, 1));
        resp.setBackUpModelCount((long) tbLifecycleBatches.stream().map(x -> x.getTbCount()).mapToInt(Integer::intValue).sum());
        // 迁移耗时
        long durationSum = getDurationSum(date);
        resp.setBackUpDurationSumInSecond(durationSum);
        return resp;
    }

    private long getDurationSum(String date) {
        DateTime startTime = DateUtil.parseDateTime(date + " 00:00:00");
        DateTime endTime = DateTime.of(startTime.getTime() + 24 * 60 * 60 * 1000);
        List<TbLifecycleBatch> batches = jaxRepository.lifecycleBatchService().list(
                new LambdaQueryWrapper<TbLifecycleBatch>().ge(TbLifecycleBatch::getScheduledTime, startTime)
                        .lt(TbLifecycleBatch::getScheduledTime, endTime)
                        .isNotNull(TbLifecycleBatch::getTriggerTime)
        );
        List<TimeIntervalUtil.TimeInterval> timeIntervals = batches.stream()
                .map(x -> new TimeIntervalUtil.TimeInterval(x.getTriggerTime(), x.getEndTime()))
                .collect(Collectors.toList());
        long durationSum = TimeIntervalUtil.calculateTotalTime(timeIntervals);
        return durationSum;
    }

    @Override
    public List<LifecycleStrategyMatchResp> modelStatistics(String date) {
        List<TbLifecycleBatch> tbLifecycleBatches = jaxRepository.lifecycleStatisticsMapper().modelStatistics(date, offset(date, 1));
        List<Long> batchIds = tbLifecycleBatches.stream().map(TbLifecycleBatch::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(batchIds)) {
            return jaxRepository.lifecycleStrategyMatchService()
                    .list(new LambdaQueryWrapper<TbLifecycleStrategyMatch>()
                            .in(TbLifecycleStrategyMatch::getBatchId, batchIds))
                    .stream()
                    .filter(x -> !LifecycleModelScanErrorEnum.UN_MATCH_STATUS.contains(x.getStatus()))
                    .map(x -> (LifecycleStrategyMatchResp) new LifecycleStrategyMatchResp().fromEntity(x))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public LifecycleChartResp statisticsForChart(String today, String dataType) {
        Boolean isCold = dataType.equals("cold");
        LocalDate date = today == null ? LocalDate.now().minusDays(1) : LocalDate.parse(today, DateTimeFormatter.ISO_DATE);
        today = DateTimeFormatter.ISO_DATE.format(date);
        List<String> actions = Arrays.stream(LifecycleBackupKindEnum.values())
                .filter(it -> isCold == (LifecyclePlatformEnum.NONE == it.getTargetPlatform()))
                .map(LifecycleBackupKindEnum::code)
                .collect(Collectors.toList());
        List<VLifecycleStatisticsResult> charts = jaxRepository.lifecycleStatisticsMapper()
                .backupChartStatistics(today, actions, offset(today, -30));
        List<String> dates = charts.stream().map(VLifecycleStatisticsResult::getScheduledDate).collect(Collectors.toList());
        List<Long> dataInBytes = charts.stream().map(VLifecycleStatisticsResult::getDataBytesSum).collect(Collectors.toList());
        fill0MetricValue(dataInBytes, dates, date.minusDays(30), date);
        return new LifecycleChartResp(dates, dataInBytes);
    }

    private static String offset(String date, int offset) {
        LocalDate today = LocalDate.parse(date, DateTimeFormatter.ISO_DATE);
        return DateTimeFormatter.ISO_DATE.format(today.plusDays(offset));
    }

    private void fill0MetricValue(List<Long> metricValue, List<String> dates,
                                  LocalDate startDate, LocalDate endDate) {
        int index = 0;
        while (endDate.compareTo(startDate) >= 0) {
            String curTime = dates.size() > index ? dates.get(index) : null;
            LocalDate curDate = curTime != null ? LocalDate.parse(curTime, DateTimeFormatter.ISO_DATE) : null;
            if (StrUtil.isBlank(curTime) || curDate.compareTo(endDate) < 0) {
                dates.add(index, DateTimeFormatter.ISO_DATE.format(endDate));
                metricValue.add(index, 0L);
            } else if (curDate.compareTo(endDate) > 0) {
                metricValue.remove(index);
                continue;
            }
            index++;
            endDate = endDate.minusDays(1);
        }
        metricValue.subList(0, index);
        Collections.reverse(metricValue);
        Collections.reverse(dates);
    }
}
