package com.eoi.jax.web.lifecycle.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.LifecyclePlatformEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.exception.LockException;
import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.core.service.BaseService;
import com.eoi.jax.web.core.service.impl.MysqlLockImpl;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.lifecycle.enumrate.BatchStatusEnum;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleBackupModelEnum;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleSelectModeEnum;
import com.eoi.jax.web.lifecycle.enumrate.LifecycleTimeUnitEnum;
import com.eoi.jax.web.lifecycle.model.match.LifecycleStrategyMatch;
import com.eoi.jax.web.lifecycle.model.match.LifecycleStrategyMatchReq;
import com.eoi.jax.web.lifecycle.model.match.StrategyMatchCheck;
import com.eoi.jax.web.lifecycle.model.strategy.*;
import com.eoi.jax.web.lifecycle.service.LifecycleBatchService;
import com.eoi.jax.web.lifecycle.service.LifecycleModelService;
import com.eoi.jax.web.lifecycle.service.LifecycleStrategyMatchService;
import com.eoi.jax.web.lifecycle.service.LifecycleStrategyService;
import com.eoi.jax.web.repository.entity.TbLifecycleBatch;
import com.eoi.jax.web.repository.entity.TbLifecycleStrategy;
import com.eoi.jax.web.repository.entity.TbTable;
import com.eoi.jax.web.repository.service.TbLifecycleStrategyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.eoi.jax.web.lifecycle.enumrate.LifecycleModelScanErrorEnum.*;

@Service
public class LifecycleStrategyServiceImpl extends BaseService<
        TbLifecycleStrategyService,
        TbLifecycleStrategy,
        LifecycleStrategyResp,
        LifecycleStrategyCreateReq,
        LifecycleStrategyUpdateReq,
        LifecycleStrategyQueryReq> implements LifecycleStrategyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LifecycleStrategyServiceImpl.class.getName());

    @Autowired
    private LifecycleBatchService batchService;
    @Autowired
    private JaxRepository jaxRepository;
    @Autowired
    private LifecycleStrategyMatchService matchService;
    @Autowired
    private LifecycleModelService modelService;
    @Autowired
    private MysqlLockImpl lockService;


    public LifecycleStrategyServiceImpl(@Autowired TbLifecycleStrategyService tbLifecycleStrategyService) {
        super(tbLifecycleStrategyService);
    }

    @Override
    public void whenCreate(LifecycleStrategyCreateReq req, TbLifecycleStrategy entity) {
        validateBackupConfig(entity);
        validateSelectMode(entity);
        validatePriority(req.getPriority(), null);
        validateName(req.getName(), null);
        entity.setIsEnabled(0);
        entity.setExecuteCron(toCronExpression(entity));
        super.whenCreate(req, entity);
    }


    @Override
    public void whenUpdate(LifecycleStrategyUpdateReq req, TbLifecycleStrategy entity) {
        validateBackupConfig(entity);
        validateSelectMode(entity);
        validatePriority(req.getPriority(), req.getId());
        validateName(req.getName(), req.getId());
        Assert.isTrue(entity.getIsEnabled() == 0, "启用状态下的生命周期策略不允许编辑");
        entity.setIsEnabled(0);
        entity.setExecuteCron(toCronExpression(entity));
        super.whenUpdate(req, entity);
    }

    @Override
    public Paged<LifecycleStrategyResp> query(LifecycleStrategyQueryReq req) {
        Paged<LifecycleStrategyResp> query = super.query(req);
        List<Long> strategyIds = query.getList().stream().map(LifecycleStrategyResp::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(strategyIds)) {
            Map<Long, String> lastestBatches = jaxRepository.lifecycleBatchService()
                    .latestBatchByStrategyIds(strategyIds, null, Arrays.asList(BatchStatusEnum.SCHEDULED.code()))
                    .stream().collect(Collectors.toMap(TbLifecycleBatch::getStrategyId, it -> it.getStatus()));
            query.getList().forEach(x -> x.setBatchStatus(lastestBatches.getOrDefault(x.getId(), "NOT_EXECUTED")));
        }
        return query;
    }

    @Override
    public LifecycleStrategyResp marshallingRespFrom(TbLifecycleStrategy entity) {
        LifecycleStrategyResp resp = super.marshallingRespFrom(entity);
        if (CollUtil.isNotEmpty(resp.getTbIdExcluded())) {
            List<TbTable> tables = jaxRepository.table().listByIds(resp.getTbIdExcluded());
            resp.setTbExcluded(tables.stream().map(x -> new TableResp().fromEntity(x)).collect(Collectors.toList()));
        }
        if (LifecycleSelectModeEnum.EXACT_MATCH.equals(resp.getSelectMode())) {
            if (CollUtil.isNotEmpty(resp.getSelectConfig().getModelIds())) {
                List<TbTable> tables = jaxRepository.table().listByIds(resp.getSelectConfig().getModelIds());
                resp.setTbSelected(tables.stream().map(x -> new TableResp().fromEntity(x)).collect(Collectors.toList()));
            }
        }
        return resp;
    }

    @Override
    public void whenDelete(TbLifecycleStrategy entity) {
        if (entity.getIsEnabled() == 1) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "数据生命周期策略已开启，请先禁用再删除");
        }
        super.whenDelete(entity);
    }

    @Override
    public LifecycleStrategyResp enable(Long id) {
        LifecycleStrategyResp lifecycleStrategyResp = get(id);
        String lockKey = LifecycleBatchServiceImpl.LOCK_KEY_PREFIX + id;
        try {
            if (!lockService.lock(lockKey, null, 0L)) {
                throw new LockException(ResponseCode.LOCK_FAILED.getCode(), "策略相关批次有变动，暂无法修改，请稍后再试");
            }
            TbLifecycleStrategy strategy = jaxRepository.lifecycleStrategyService()
                    .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
            if (strategy.getIsEnabled() == 1) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "数据生命周期策略已开启，请勿重复开启");
            }
            strategy.setIsEnabled(1);
            ModelBeanUtil.setUpdateDefaultValue(strategy);
            jaxRepository.lifecycleStrategyService().updateById(strategy);
            Long nextBatchId = batchService.createBatch(lifecycleStrategyResp);
            lifecycleStrategyResp.setBatchId(nextBatchId);
        } catch (Exception e) {
            throw e;
        } finally {
            lockService.unLock(lockKey);
        }
        ThreadPoolUtil.THREAD_POOL.submit(() -> {
            try {
                modelService.handleBatchAdditionImpact(lifecycleStrategyResp.getBatchId());
            } catch (Exception e) {
                LOGGER.error("处理对其他批次的影响异常", e);
            }
        });
        return lifecycleStrategyResp;
    }

    @Override
    public LifecycleStrategyResp disable(Long id) {
        TbLifecycleStrategy strategy = jaxRepository.lifecycleStrategyService().existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        List<Long> matchedTbIds = matchService.matchedTbIds(strategy.getBatchId());
        String lockKey = LifecycleBatchServiceImpl.LOCK_KEY_PREFIX + id;
        Long batchId = strategy.getBatchId();
        try {
            if (!lockService.lock(lockKey, null, 0L)) {
                throw new LockException(ResponseCode.LOCK_FAILED.getCode(), "策略相关批次有变动，暂无法修改，请稍后再试");
            }
            if (strategy.getIsEnabled() == 0) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "数据生命周期策略已禁用，请勿重复禁用");
            }
            strategy.setIsEnabled(0);
            strategy.setBatchId(null);
            ModelBeanUtil.setUpdateDefaultValue(strategy);
            jaxRepository.lifecycleStrategyService().updateById(strategy);
            batchService.stopOrDelete(batchId, String.format("[%s]策略禁用", strategy.getName()));
            matchService.deleteMatchByBatchId(batchId);
        } catch (Exception e) {
            lockService.unLock(lockKey);
            throw e;
        } finally {
            lockService.unLock(lockKey);
        }
        ThreadPoolUtil.THREAD_POOL.submit(() -> {
            try {
                modelService.handleBatchDeletionImpact(batchId, matchedTbIds);
            } catch (Exception e) {
                LOGGER.error("处理对其他批次的影响异常", e);
            }
        });
        return get(id);
    }

    @Override
    public Object matchByStrategyId(Long id) {
        LifecycleStrategyResp lifecycleStrategyResp = get(id);
        LifecycleStrategyMatchReq lifecycleStrategyMatchReq = new LifecycleStrategyMatchReq();
        ModelBeanUtil.copyBean(lifecycleStrategyResp, lifecycleStrategyMatchReq);
        lifecycleStrategyMatchReq.setStrategyId(lifecycleStrategyResp.getId());
        return matchService.match(lifecycleStrategyMatchReq);
    }

    @Override
    public LifecycleStrategyResp createAndEnable(LifecycleStrategyCreateReq req) {
        LifecycleStrategyResp lifecycleStrategyResp = create(req);
        return enable(lifecycleStrategyResp.getId());
    }

    @Override
    public LifecycleStrategyResp updateAndEnable(LifecycleStrategyUpdateReq req) {
        LifecycleStrategyResp lifecycleStrategyResp = update(req);
        return enable(lifecycleStrategyResp.getId());
    }

    @Override
    public List<StrategyMatchCheck> saveAndEnableModelImpact(LifecycleStrategyUpdateReq req) {
        TbLifecycleStrategy entity = req.toEntity(new TbLifecycleStrategy());
        validateBackupConfig(entity);
        validateSelectMode(entity);
        validatePriority(req.getPriority(), req.getId());
        validateName(req.getName(), req.getId());
        LifecycleStrategyResp lifecycleStrategyResp = new LifecycleStrategyResp().fromEntity(entity);
        return getImpactModels(lifecycleStrategyResp, true);
    }

    @Override
    public List<StrategyMatchCheck> enableStrategyImpact(Long id, boolean enable) {
        LifecycleStrategyResp lifecycleStrategyResp = get(id);
        return getImpactModels(lifecycleStrategyResp, enable);
    }

    private List<StrategyMatchCheck> getImpactModels(LifecycleStrategyResp strategyResp, boolean enable) {
        // 策略列表
        List<TbLifecycleStrategy> allStrategy = jaxRepository.lifecycleStrategyService().list(new LambdaQueryWrapper<TbLifecycleStrategy>()
                .eq(TbLifecycleStrategy::getIsEnabled, 1)
                .eq(TbLifecycleStrategy::getSourcePlatform, strategyResp.getSourcePlatform()));
        Map<Long, Long> strategyPriorityMap = allStrategy
                .stream().collect(Collectors.toMap(x -> x.getId(), x -> x.getPriority()));
        Map<Long, String> strategyNameMap = allStrategy
                .stream().collect(Collectors.toMap(x -> x.getId(), x -> x.getName()));
        // 没有id先生成一个
        Long targetStrategyId = ObjectUtil.defaultIfNull(strategyResp.getId(), IdUtil.genId());
        strategyPriorityMap.put(targetStrategyId, strategyResp.getPriority());
        strategyNameMap.put(targetStrategyId, strategyResp.getName());
        // 1. 启用禁用前的匹配情况
        List<LifecycleStrategyMatch> originMatches = batchService.currentMatchesInAllStrategy()
                .stream().map(x -> {
                    LifecycleStrategyMatch lifecycleStrategyMatch = new LifecycleStrategyMatch();
                    ModelBeanUtil.copyBean(x, lifecycleStrategyMatch);
                    return lifecycleStrategyMatch;
                }).collect(Collectors.toList());
        // 1.1 原匹配情况，需要去掉不匹配的模型，并计算每个模型的适用策略
        Map<Long, Optional<LifecycleStrategyMatch>> originTbStrategyMap = reduceTableStrategy(originMatches, strategyPriorityMap);
        // 2. 启用禁用后的匹配情况
        List<LifecycleStrategyMatch> newMatches = new ArrayList<>(originMatches);
        if (enable) {
            // 本策略目前匹配的情况
            List<LifecycleStrategyMatch> matchTables = matchService.match(strategyResp);
            matchTables.forEach(m -> m.setStrategyId(targetStrategyId));
            if (strategyResp.getId() != null) {
                // 修改的情况: 删除原有匹配，使用新的匹配
                newMatches.removeIf(x -> x.getStrategyId().equals(targetStrategyId));
            }
            newMatches.addAll(matchTables);
        } else {
            newMatches.removeIf(x -> x.getStrategyId().equals(strategyResp.getId()));
        }
        // 重新匹配
        // 一个模型只有一个批次可以生效
        Map<Long, Optional<LifecycleStrategyMatch>> newOriginTbStrategyMap = reduceTableStrategy(newMatches, strategyPriorityMap);
        List<StrategyMatchCheck> finalModelStrategyList = new ArrayList<>();
        // 比较原策略和现有策略
        originTbStrategyMap.forEach((tbId, strategy) -> {
            LifecycleStrategyMatch lifecycleStrategyMatch = strategy.get();
            Optional<LifecycleStrategyMatch> newStrategyOption = newOriginTbStrategyMap.remove(tbId);
            if (newStrategyOption != null && newStrategyOption.isPresent()
                    && ObjectUtil.equal(newStrategyOption.get().getStrategyId(), lifecycleStrategyMatch.getStrategyId())) {
                return;
            }
            StrategyMatchCheck strategyMatchCheck = new StrategyMatchCheck();
            strategyMatchCheck.setTbId(tbId);
            strategyMatchCheck.setTbName(lifecycleStrategyMatch.getTbName());
            strategyMatchCheck.setTbAlias(lifecycleStrategyMatch.getTbAlias());
            strategyMatchCheck.setOriStrategyId(lifecycleStrategyMatch.getStrategyId());
            strategyMatchCheck.setOriStrategyName(strategyNameMap.get(lifecycleStrategyMatch.getStrategyId()));
            if (newStrategyOption != null && newStrategyOption.isPresent()) {
                strategyMatchCheck.setNewStrategyId(newStrategyOption.map(LifecycleStrategyMatch::getStrategyId).orElse(null));
                strategyMatchCheck.setNewStrategyName(strategyNameMap.get(strategyMatchCheck.getNewStrategyId()));
            }
            finalModelStrategyList.add(strategyMatchCheck);
        });
        // 新增策略
        newOriginTbStrategyMap.forEach((tbId, strategy) -> {
            StrategyMatchCheck strategyMatchCheck = new StrategyMatchCheck();
            strategyMatchCheck.setTbId(tbId);
            strategyMatchCheck.setTbName(strategy.get().getTbName());
            strategyMatchCheck.setTbAlias(strategy.get().getTbAlias());
            strategyMatchCheck.setNewStrategyId(strategy.get().getStrategyId());
            strategyMatchCheck.setNewStrategyName(strategyNameMap.get(strategyMatchCheck.getNewStrategyId()));
            finalModelStrategyList.add(strategyMatchCheck);
        });
        return finalModelStrategyList;
    }

    private Map<Long, Optional<LifecycleStrategyMatch>> reduceTableStrategy(List<LifecycleStrategyMatch> newMatches,
                                                                            Map<Long, Long> strategyMap) {
        // 这几种状态列出来没意义
        List<String> excludeStatus = Arrays.asList(
                UN_DEPLOYED.code(),
                PLATFORM_NOT_SUPPORT.code(),
                MODEL_STRATEGY_CHANGE.code()
        );
        Map<Long, Optional<LifecycleStrategyMatch>> collect = newMatches.stream()
                .filter(x -> strategyMap.containsKey(x.getStrategyId()))
                .filter(x -> !excludeStatus.contains(x.getStatus()))
                .collect(Collectors.groupingBy(LifecycleStrategyMatch::getTbId,
                Collectors.reducing((x, y) -> strategyMap.get(x.getStrategyId()) > strategyMap.get(y.getStrategyId()) ? x : y)
        ));
        return collect;
    }

    private void validateSelectMode(TbLifecycleStrategy entity) {
        LifecycleStrategyResp strategy = new LifecycleStrategyResp().fromEntity(entity);
        switch (LifecycleSelectModeEnum.fromString(strategy.getSelectMode())) {
            case EXACT_MATCH:
                Assert.isTrue(CollUtil.isNotEmpty(strategy.getSelectConfig().getModelIds()), "精确匹配模式下，请选择数据模型");
                break;
            case FUZZY_MATCH:
                Assert.isTrue(StrUtil.isNotBlank(strategy.getSelectConfig().getFuzzyName()), "模糊匹配模式下，请输入模糊匹配名称");
                break;
            case WAREHOUSE_MATCH:
                Assert.notNull(strategy.getSelectConfig().getBizId(), "数仓匹配模式下，业务域配置不可为空");
                if (BooleanUtil.isTrue(strategy.getSelectConfig().getIsAppCatalog())) {
                    Assert.notNull(strategy.getSelectConfig().getMartId(), "数仓匹配模式下，数据集市配置不可为空");
                } else {
                    Assert.notNull(strategy.getSelectConfig().getLayerId(), "数仓匹配模式下，分层配置不可为空");
                    Assert.notNull(strategy.getSelectConfig().getDomId(), "数仓匹配模式下，数据域配置不可为空");
                }
                break;
            default:
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "不支持的匹配模式");
        }
    }

    private void validateBackupConfig(TbLifecycleStrategy entity) {
        LifecycleStrategyResp strategy = new LifecycleStrategyResp().fromEntity(entity);
        if (LifecycleBackupModelEnum.NEAR_REALTIME.equals(strategy.getBackupMode())) {
            Integer backupStartDays = strategy.getBackupConfig().getBackupStartDays();
            Integer retentionDays = strategy.getRetentionDays();
            Integer ignoreDays = strategy.getBackupConfig().getIgnoreDays();
            Assert.isTrue(ObjectUtil.isNotNull(backupStartDays), "备份开始时间不可为空");
            Assert.isTrue(ObjectUtil.isNotNull(ignoreDays), "备份忽略时间不可为空");
            Assert.isTrue(ignoreDays > backupStartDays, "备份忽略时间点不可早于备份起始时间");
            Assert.isTrue(retentionDays > backupStartDays, "数据保留时长应大于备份起始时间");
            for (String targetPlatForm : strategy.getBackupConfig().getTargetPlatform()) {
                switch (LifecyclePlatformEnum.fromString(targetPlatForm)) {
                    case HIVE:
                        Assert.isTrue(StrUtil.isNotBlank(strategy.getBackupConfig().getHiveGenerateMode()), "hive模型名生成方式不可为空");
                        break;
                    case FILE:
                        Assert.isTrue(StrUtil.isNotBlank(strategy.getBackupConfig().getTargetFilePath()), "文件备份模式下，请选择目标路径");
                        Assert.isTrue(strategy.getBackupConfig().getTargetFilePath().endsWith("/"), "路径不合法，必须以‘/’结尾");
                        break;
                    default:
                        throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "不支持的迁移平台");
                }
            }
        } else {
            strategy.setBackupConfig(new LifecycleMigrateConfig());
        }
    }


    private void validatePriority(Long priority, Long id) {
        long count = this.jaxRepository.lifecycleStrategyService().count(new LambdaQueryWrapper<TbLifecycleStrategy>()
                .eq(TbLifecycleStrategy::getPriority, priority)
                .ne(id != null, TbLifecycleStrategy::getId, id));
        if (count > 0) {
            throw new BizException(ResponseCode.LIFECYCLE_PRIORITY_EXISTS);
        }
    }

    private void validateName(String name, Long id) {
        long count = this.jaxRepository.lifecycleStrategyService().count(new LambdaQueryWrapper<TbLifecycleStrategy>()
                .eq(TbLifecycleStrategy::getName, name)
                .ne(id != null, TbLifecycleStrategy::getId, id));
        if (count > 0) {
            throw new BizException(ResponseCode.LIFECYCLE_NAME_EXISTS);
        }
    }

    /**
     * 将 LifecycleTimeCronConfig 对象转换为 Cron 表达式
     *
     * @return Cron 表达式
     */
    public String toCronExpression(TbLifecycleStrategy lifecycleStrategy) {
        String second = "0";
        String minute = lifecycleStrategy.getExecuteTimeOfDay().split(":")[1];
        String hour = lifecycleStrategy.getExecuteTimeOfDay().split(":")[0];
        String dayOfMonth;
        String month = "*";
        String dayOfWeek;
        List<String> executeDays = JsonUtil.decode2ListString(lifecycleStrategy.getExecuteDays());
        switch (LifecycleTimeUnitEnum.fromString(lifecycleStrategy.getExecuteTimeunit())) {
            case MONTH:
                dayOfMonth = String.join(",", executeDays);
                dayOfWeek = "?";
                break;
            case WEEK:
                dayOfMonth = "?";
                dayOfWeek = String.join(",", executeDays);
                break;
            default:
                dayOfMonth = "*";
                dayOfWeek = "?";
                break;
        }
        return String.format("%s %s %s %s %s %s", second, minute, hour, dayOfMonth, month, dayOfWeek);
    }

}
