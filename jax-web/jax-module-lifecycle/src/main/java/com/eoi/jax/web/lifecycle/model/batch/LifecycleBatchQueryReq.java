package com.eoi.jax.web.lifecycle.model.batch;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbLifecycleBatch;

public class LifecycleBatchQueryReq extends BaseQueryReq<TbLifecycleBatch> {

    private LifecycleBatchQueryFilterReq filter = new LifecycleBatchQueryFilterReq();
    private LifecycleBatchQuerySortReq sort = new LifecycleBatchQuerySortReq();

    @Override
    public IFilterReq<TbLifecycleBatch> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbLifecycleBatch> getSort() {
        return sort;
    }
}
