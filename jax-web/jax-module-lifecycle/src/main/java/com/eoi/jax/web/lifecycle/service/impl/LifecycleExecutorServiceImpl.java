package com.eoi.jax.web.lifecycle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.lifecycle.admin.config.LifecycleConfig;
import com.eoi.jax.web.lifecycle.client.LifecycleExecutorClient;
import com.eoi.jax.web.lifecycle.model.executor.*;
import com.eoi.jax.web.lifecycle.service.LifecycleExecutorService;
import com.eoi.jax.web.repository.entity.TbLifecycleExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 */
@Service
public class LifecycleExecutorServiceImpl implements LifecycleExecutorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LifecycleExecutorServiceImpl.class);

    @Autowired
    private JaxRepository jaxRepository;

    @Override
    public List<LifecycleExecutorResp> list() {
        List<TbLifecycleExecutor> list = jaxRepository.lifecycleExecutorService().list();
        return list.stream().map(i -> new LifecycleExecutorResp().fromEntity(i))
                .collect(Collectors.toList());
    }

    @Override
    public LifecycleExecutorResp get(Long id) {
        TbLifecycleExecutor entity = jaxRepository.lifecycleExecutorService()
                .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        return new LifecycleExecutorResp().fromEntity(entity);
    }

    @Override
    public ExecutorLocalLogs listExecutorLogs(Long id) {
        TbLifecycleExecutor entity = jaxRepository.lifecycleExecutorService()
                .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        String authToken = LifecycleConfig.getAdminConfig().getAuthToken();
        LifecycleExecutorClient client = new LifecycleExecutorClient(entity.getExecutorAddress(), authToken);
        return client.listExecutorLogs();
    }

    @Override
    public void downloadExecutorLog(HttpServletResponse response, Long id, String file) {
        TbLifecycleExecutor entity = jaxRepository.lifecycleExecutorService()
                .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        String authToken = LifecycleConfig.getAdminConfig().getAuthToken();
        LifecycleExecutorClient client = new LifecycleExecutorClient(entity.getExecutorAddress(), authToken);
        try {
            response.reset();
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "filename=" + file);
            client.downloadExecutorLog(file, response.getOutputStream());
        } catch (Exception e) {
            throw new BizException(ResponseCode.DOWNLOAD_ERROR, e);
        }
    }

    @Override
    public ExecutorMetric getMetric(Long id) {
        TbLifecycleExecutor entity = jaxRepository.lifecycleExecutorService()
                .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        String authToken = LifecycleConfig.getAdminConfig().getAuthToken();
        LifecycleExecutorClient client = new LifecycleExecutorClient(entity.getExecutorAddress(), authToken);
        return client.getMetric();
    }

    @Override
    public LifecycleExecutorResp register(LifecycleExecutor req) {
        LOGGER.info("执行器注册: {}", req);
        TbLifecycleExecutor entity = createOrUpdate(req);
        return new LifecycleExecutorResp().fromEntity(entity);
    }

    @Override
    public LifecycleExecutorResp heartbeat(LifecycleExecutorHeartbeat req) {
        TbLifecycleExecutor entity = createOrUpdate(req);
        return new LifecycleExecutorResp().fromEntity(entity);
    }

    @Override
    public LifecycleExecutorResp unregister(LifecycleExecutor req) {
        LOGGER.info("执行器注销: {}", req);
        List<TbLifecycleExecutor> list = jaxRepository.lifecycleExecutorService().list(
                new LambdaQueryWrapper<TbLifecycleExecutor>()
                        .eq(TbLifecycleExecutor::getExecutorAddress, req.getExecutorAddress())
        );
        if (list.isEmpty()) {
            return null;
        }
        // 更新失效状态（1-已失效）
        jaxRepository.lifecycleExecutorService().update(
                new LambdaUpdateWrapper<TbLifecycleExecutor>()
                        .set(TbLifecycleExecutor::getIsExpired, 1)
                        .eq(TbLifecycleExecutor::getExecutorAddress, req.getExecutorAddress())
        );
        return new LifecycleExecutorResp().fromEntity(list.get(0));
    }

    @Override
    public LifecycleExecutorResp enable(Long id) {
        TbLifecycleExecutor entity = jaxRepository.lifecycleExecutorService()
                .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        jaxRepository.lifecycleExecutorService().update(
                new LambdaUpdateWrapper<TbLifecycleExecutor>()
                        .set(TbLifecycleExecutor::getIsDisabled, 0)
                        .eq(TbLifecycleExecutor::getId, id)
        );
        return new LifecycleExecutorResp().fromEntity(entity);
    }

    @Override
    public LifecycleExecutorResp disable(Long id) {
        TbLifecycleExecutor entity = jaxRepository.lifecycleExecutorService()
                .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        jaxRepository.lifecycleExecutorService().update(
                new LambdaUpdateWrapper<TbLifecycleExecutor>()
                        .set(TbLifecycleExecutor::getIsDisabled, 1)
                        .eq(TbLifecycleExecutor::getId, id)
        );
        return new LifecycleExecutorResp().fromEntity(entity);
    }

    @Override
    public LifecycleExecutorResp delete(Long id) {
        TbLifecycleExecutor entity = jaxRepository.lifecycleExecutorService()
                .existId(id, new BizException(ResponseCode.ID_NOT_EXISTS));
        jaxRepository.lifecycleExecutorService().removeById(id);
        return new LifecycleExecutorResp().fromEntity(entity);
    }

    private TbLifecycleExecutor createOrUpdate(LifecycleExecutor req) {
        List<TbLifecycleExecutor> list = jaxRepository.lifecycleExecutorService().list(
                new LambdaQueryWrapper<TbLifecycleExecutor>()
                        .eq(TbLifecycleExecutor::getExecutorAddress, req.getExecutorAddress())
        );
        Date now = new Date();
        if (list.isEmpty()) {
            // 执行器不存在，创建执行器
            TbLifecycleExecutor entity = new TbLifecycleExecutor();
            entity.setId(IdUtil.genId());
            entity.setExecutorAddress(req.getExecutorAddress());
            entity.setExecutorVersion(req.getExecutorVersion());
            entity.setIsDisabled(0);
            entity.setIsExpired(0);
            entity.setHeartbeatTime(now);
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            jaxRepository.lifecycleExecutorService().save(entity);
            return entity;
        } else {
            // 执行器已经存在，更新心跳时间、版本、失效状态（0-未失效）
            LambdaUpdateWrapper<TbLifecycleExecutor> wrapper = new LambdaUpdateWrapper<TbLifecycleExecutor>()
                    .set(TbLifecycleExecutor::getHeartbeatTime, now)
                    .set(TbLifecycleExecutor::getExecutorVersion, req.getExecutorVersion())
                    .set(TbLifecycleExecutor::getIsExpired, 0)
                    .eq(TbLifecycleExecutor::getExecutorAddress, req.getExecutorAddress());
            if (req instanceof LifecycleExecutorHeartbeat) {
                LifecycleExecutorHeartbeat heartbeat = (LifecycleExecutorHeartbeat) req;
                String executorMetric = JsonUtil.encode(heartbeat.getMetric());
                wrapper.set(TbLifecycleExecutor::getExecutorMetric, executorMetric);
            }
            jaxRepository.lifecycleExecutorService().update(wrapper);
            return list.get(0);
        }
    }
}
