
package com.eoi.jax.web.lifecycle.model.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbLifecycleBatchJob;

public class LifecycleBatchJobReq implements ISortReq<TbLifecycleBatchJob> {

    private String duration;
    private String sourceDataRows;
    private String sourceDataBytes;
    private String startTime;
    private String endTime;
    private String partitionId;


    @Override
    public QueryWrapper<TbLifecycleBatchJob> order(QueryWrapper<TbLifecycleBatchJob> wrapper) {
        wrapper.lambda().orderBy(isOrder(duration), isAsc(duration), TbLifecycleBatchJob::getDuration);
        wrapper.lambda().orderBy(isOrder(sourceDataRows), isAsc(sourceDataRows), TbLifecycleBatchJob::getSourceDataRows);
        wrapper.lambda().orderBy(isOrder(sourceDataBytes), isAsc(sourceDataBytes), TbLifecycleBatchJob::getSourceDataBytes);
        wrapper.lambda().orderBy(isOrder(startTime), isAsc(startTime), TbLifecycleBatchJob::getStartTime);
        wrapper.lambda().orderBy(isOrder(endTime), isAsc(endTime), TbLifecycleBatchJob::getEndTime);
        wrapper.lambda().orderBy(isOrder(partitionId), isAsc(partitionId), TbLifecycleBatchJob::getPartitionId);
        // 默认排序
        wrapper.lambda().orderByDesc(!isOrder(partitionId), TbLifecycleBatchJob::getPartitionDate);
        wrapper.lambda().orderByDesc(TbLifecycleBatchJob::getUpdateTime);
        return wrapper;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getSourceDataRows() {
        return sourceDataRows;
    }

    public void setSourceDataRows(String sourceDataRows) {
        this.sourceDataRows = sourceDataRows;
    }

    public String getSourceDataBytes() {
        return sourceDataBytes;
    }

    public void setSourceDataBytes(String sourceDataBytes) {
        this.sourceDataBytes = sourceDataBytes;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPartitionId() {
        return partitionId;
    }

    public void setPartitionId(String partitionId) {
        this.partitionId = partitionId;
    }
}
