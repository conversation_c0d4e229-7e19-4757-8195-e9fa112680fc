package com.eoi.jax.web.lifecycle.model.executor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/14
 */
public class ExecutorMetric {
    private FileSystem fileSystem;

    public FileSystem getFileSystem() {
        return fileSystem;
    }

    public void setFileSystem(FileSystem fileSystem) {
        this.fileSystem = fileSystem;
    }

    public static class FileSystem {
        private Long usedSpace;
        private Long totalSpace;
        private List<FileStore> fileStores;

        public Long getUsedSpace() {
            return usedSpace;
        }

        public void setUsedSpace(Long usedSpace) {
            this.usedSpace = usedSpace;
        }

        public Long getTotalSpace() {
            return totalSpace;
        }

        public void setTotalSpace(Long totalSpace) {
            this.totalSpace = totalSpace;
        }

        public List<FileStore> getFileStores() {
            return fileStores;
        }

        public void setFileStores(List<FileStore> fileStores) {
            this.fileStores = fileStores;
        }
    }

    public static class FileStore {
        private String path;
        private Long usedSpace;
        private Long totalSpace;

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public Long getUsedSpace() {
            return usedSpace;
        }

        public void setUsedSpace(Long usedSpace) {
            this.usedSpace = usedSpace;
        }

        public Long getTotalSpace() {
            return totalSpace;
        }

        public void setTotalSpace(Long totalSpace) {
            this.totalSpace = totalSpace;
        }
    }
}
