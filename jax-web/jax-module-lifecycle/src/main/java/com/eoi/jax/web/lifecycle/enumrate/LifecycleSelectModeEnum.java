package com.eoi.jax.web.lifecycle.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;
import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;

public enum LifecycleSelectModeEnum implements ICodeEnum, ICodeMessageEnum {
    /**
     * 精确匹配
     */
    EXACT_MATCH("EXACT_MATCH", "精确匹配"),
    /**
     * 模糊匹配
     */
    FUZZY_MATCH("FUZZY_MATCH", "模糊匹配"),
    /**
     * 数仓匹配
     */
    WAREHOUSE_MATCH("WAREHOUSE_MATCH", "数仓匹配");

    private final String code;

    private final String message;

    LifecycleSelectModeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static LifecycleSelectModeEnum fromString(String code) {
        for (LifecycleSelectModeEnum value : LifecycleSelectModeEnum.values()) {
            if (value.code().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * code
     *
     * @return code
     */
    @Override
    public String code() {
        return code;
    }

    @Override
    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

}
