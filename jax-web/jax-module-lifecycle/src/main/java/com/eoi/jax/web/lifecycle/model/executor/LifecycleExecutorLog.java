package com.eoi.jax.web.lifecycle.model.executor;

import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.repository.entity.TbLifecycleBatchLog;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/11/4
 */
public class LifecycleExecutorLog {
    private Long time;
    private Long jobId;
    private Long taskId;
    private String taskName;
    private String message;

    public TbLifecycleBatchLog toEntity(TbLifecycleBatchLog entity) {
        entity.setId(IdUtil.genId());
        entity.setCreateTime(new Date(getTime()));
        entity.setUpdateTime(new Date(getTime()));
        entity.setJobId(getJobId());
        entity.setTaskId(getTaskId());
        entity.setTaskName(getTaskName());
        entity.setLog(getMessage());
        return entity;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
