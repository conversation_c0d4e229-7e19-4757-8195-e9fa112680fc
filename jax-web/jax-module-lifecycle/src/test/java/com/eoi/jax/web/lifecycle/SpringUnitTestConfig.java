package com.eoi.jax.web.lifecycle;

import com.eoi.jax.web.ingestion.model.DataDeveloperExcelModel;
import com.eoi.jax.web.ingestion.service.IDolphinSchedulerImportService;
import com.eoi.jax.web.repository.entity.TbTable;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@TestConfiguration
public class SpringUnitTestConfig {

    @Bean
    public IDolphinSchedulerImportService buildIDolphinSchedulerImportService() {
        return new IDolphinSchedulerImportService() {
            @Override
            public void saveRows(Boolean skip, List<DataDeveloperExcelModel> val) {

            }

            @Override
            public List<DataDeveloperExcelModel> loadOfflineWorkflowData(List<Long> ids, Map<Long, String> businessTypeMap, Map<Long, TbTable> tableMap) {
                return null;
            }
        };
    }

}
