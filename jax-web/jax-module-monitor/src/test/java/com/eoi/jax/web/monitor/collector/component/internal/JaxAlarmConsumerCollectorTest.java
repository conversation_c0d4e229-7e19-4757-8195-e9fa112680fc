package com.eoi.jax.web.monitor.collector.component.internal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.config.config.SelfMonitorAlarmConfig;
import com.eoi.jax.web.ingestion.factory.datasource.model.KafkaConnection;
import com.eoi.jax.web.ingestion.util.KafkaUtil;
import com.eoi.jax.web.monitor.enumrate.CollectCommand;
import com.eoi.jax.web.monitor.exporter.ConsoleExporter;
import com.eoi.jax.web.monitor.model.collector.CollectContext;
import com.eoi.jax.web.monitor.model.collector.Metric;
import com.eoi.jax.web.monitor.model.collector.NodeConnectionResult;
import com.eoi.jax.web.monitor.util.NetworkUtil;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.common.Node;
import org.apache.kafka.common.TopicPartitionInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class JaxAlarmConsumerCollectorTest {

    @InjectMocks
    private JaxAlarmConsumerCollector collector;

    @Mock
    private SystemConfigHolder systemConfigHolder;

    @Before
    public void setUp() {
    }

    @Test
    public void testCollectConsumerLivedMetric_lived() {
        try (MockedStatic<NetworkUtil> networkUtilMockedStatic = mockStatic(NetworkUtil.class);
             MockedStatic<KafkaUtil> kafkaUtilMockedStatic = mockStatic(KafkaUtil.class)) {
            SelfMonitorAlarmConfig selfMonitorAlarmConfig = new SelfMonitorAlarmConfig();
            selfMonitorAlarmConfig.setEnable(true);
            selfMonitorAlarmConfig.setBrokerAddress("**************:19092,**************:19092,**************:19092");
            selfMonitorAlarmConfig.setGroup("group_jax_self_monitor_alarm1");
            selfMonitorAlarmConfig.setTopic("jax_self_monitor_alarm");
            when(systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_ALARM)).thenReturn(selfMonitorAlarmConfig);
            when(systemConfigHolder.getConfig(SystemConfigEnum.SELF_MONITOR_ALARM.getKey())).thenReturn(BeanUtil.beanToMap(selfMonitorAlarmConfig));

            List<NodeConnectionResult> list = CollUtil.newArrayList(
                new NodeConnectionResult("**************", 19092, true),
                new NodeConnectionResult("**************", 19092, true),
                new NodeConnectionResult("**************", 19092, true)
            );
            networkUtilMockedStatic.when(() -> NetworkUtil.checkServerNodeLived(anyString())).thenReturn(list);

            Map<String, TopicDescription> topicDescriptionMap = new LinkedHashMap<>();
            topicDescriptionMap.put("jax_self_monitor_alarm", new TopicDescription("jax_self_monitor_alarm", false,
                CollUtil.newArrayList(
                    new TopicPartitionInfo(0, new Node(0, "**************", 19092), CollUtil.newArrayList(new Node(0, "**************", 19092)), CollUtil.newArrayList(new Node(0, "**************", 19092))),
                    new TopicPartitionInfo(1, new Node(1, "**************", 19092), CollUtil.newArrayList(new Node(1, "**************", 19092)), CollUtil.newArrayList(new Node(1, "**************", 19092))),
                    new TopicPartitionInfo(2, new Node(2, "**************", 19092), CollUtil.newArrayList(new Node(2, "**************", 19092)), CollUtil.newArrayList(new Node(2, "**************", 19092)))
                )));

            kafkaUtilMockedStatic.when(() -> KafkaUtil.describeTopics(any(KafkaConnection.class), anySet())).thenReturn(topicDescriptionMap);

            kafkaUtilMockedStatic.when(() -> KafkaUtil.getConsumerLag(anyString(), anyString(), anyString(), any(KafkaConnection.class))).thenReturn(200L);
            kafkaUtilMockedStatic.when(() -> KafkaUtil.getTopicOffset(anyString(), anyList(), any(KafkaConnection.class), anyLong())).thenReturn(500L);
            kafkaUtilMockedStatic.when(() -> KafkaUtil.getTopicOffset(anyString(), anyList(), any(KafkaConnection.class), isNull())).thenReturn(700L);


            CollectContext collectContext = new CollectContext();
            collectContext.setExporter(new ConsoleExporter());
            collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
            collector.collect(collectContext);

            List<Metric> metrics = ((ConsoleExporter) collectContext.getExporter()).getMetricList();
            System.out.println(JsonUtil.encode(metrics));
            assertEquals(1, metrics.size());
            assertEquals(1, metrics.get(0).getValue());

        }
    }


    @Test
    public void testCollectConsumerLivedMetric_notLived() {
        try (MockedStatic<NetworkUtil> networkUtilMockedStatic = mockStatic(NetworkUtil.class);
             MockedStatic<KafkaUtil> kafkaUtilMockedStatic = mockStatic(KafkaUtil.class)) {
            SelfMonitorAlarmConfig selfMonitorAlarmConfig = new SelfMonitorAlarmConfig();
            selfMonitorAlarmConfig.setEnable(true);
            selfMonitorAlarmConfig.setBrokerAddress("**************:19092,**************:19092,**************:19092");
            selfMonitorAlarmConfig.setGroup("group_jax_self_monitor_alarm1");
            selfMonitorAlarmConfig.setTopic("jax_self_monitor_alarm");
            when(systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_ALARM)).thenReturn(selfMonitorAlarmConfig);
            when(systemConfigHolder.getConfig(SystemConfigEnum.SELF_MONITOR_ALARM.getKey())).thenReturn(BeanUtil.beanToMap(selfMonitorAlarmConfig));

            List<NodeConnectionResult> list = CollUtil.newArrayList(
                new NodeConnectionResult("**************", 19092, true),
                new NodeConnectionResult("**************", 19092, true),
                new NodeConnectionResult("**************", 19092, true)
            );
            networkUtilMockedStatic.when(() -> NetworkUtil.checkServerNodeLived(anyString())).thenReturn(list);

            Map<String, TopicDescription> topicDescriptionMap = new LinkedHashMap<>();
            topicDescriptionMap.put("jax_self_monitor_alarm", new TopicDescription("jax_self_monitor_alarm", false,
                CollUtil.newArrayList(
                    new TopicPartitionInfo(0, new Node(0, "**************", 19092), CollUtil.newArrayList(new Node(0, "**************", 19092)), CollUtil.newArrayList(new Node(0, "**************", 19092))),
                    new TopicPartitionInfo(1, new Node(1, "**************", 19092), CollUtil.newArrayList(new Node(1, "**************", 19092)), CollUtil.newArrayList(new Node(1, "**************", 19092))),
                    new TopicPartitionInfo(2, new Node(2, "**************", 19092), CollUtil.newArrayList(new Node(2, "**************", 19092)), CollUtil.newArrayList(new Node(2, "**************", 19092)))
                )));

            kafkaUtilMockedStatic.when(() -> KafkaUtil.describeTopics(any(KafkaConnection.class), anySet())).thenReturn(topicDescriptionMap);

            kafkaUtilMockedStatic.when(() -> KafkaUtil.getConsumerLag(anyString(), anyString(), anyString(), any(KafkaConnection.class))).thenReturn(200L);
            kafkaUtilMockedStatic.when(() -> KafkaUtil.getTopicOffset(anyString(), anyList(), any(KafkaConnection.class), anyLong())).thenReturn(500L);
            kafkaUtilMockedStatic.when(() -> KafkaUtil.getTopicOffset(anyString(), anyList(), any(KafkaConnection.class), isNull())).thenReturn(600L);


            CollectContext collectContext = new CollectContext();
            collectContext.setExporter(new ConsoleExporter());
            collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
            collector.collect(collectContext);

            List<Metric> metrics = ((ConsoleExporter) collectContext.getExporter()).getMetricList();
            System.out.println(JsonUtil.encode(metrics));
            assertEquals(1, metrics.size());
            assertEquals(0, metrics.get(0).getValue());

        }
    }


}
