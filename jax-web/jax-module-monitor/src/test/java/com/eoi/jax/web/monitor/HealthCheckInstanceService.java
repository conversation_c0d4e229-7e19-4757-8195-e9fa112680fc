package com.eoi.jax.web.monitor;

import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.repository.entity.TbHealthCheckInstance;
import com.eoi.jax.web.repository.service.TbHealthCheckInstanceService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/4/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class HealthCheckInstanceService {

    @Resource
    private TbHealthCheckInstanceService tbHealthCheckInstanceService;


    @Test
    public void test(){
        // 测试插入
        TbHealthCheckInstance tbHealthCheckInstance = new TbHealthCheckInstance();
        tbHealthCheckInstance.setCheckId(1L);
        tbHealthCheckInstance.setItemId(1L);
        tbHealthCheckInstance.setInstanceId(1L);
        tbHealthCheckInstance.setInstanceName("testInstanceName");
        tbHealthCheckInstance.setInstanceInfo("{}");
        tbHealthCheckInstance.setInstanceType("testInstanceType");
        tbHealthCheckInstance.setStatus("NORMAL");
        tbHealthCheckInstance.setContent("test");
        tbHealthCheckInstance.setSolution("");
        ModelBeanUtil.setCreateDefaultValue(tbHealthCheckInstance);

        boolean save = tbHealthCheckInstanceService.save(tbHealthCheckInstance);
        Assert.assertTrue(save);
        Assert.assertEquals("testInstanceName", tbHealthCheckInstance.getInstanceName());
        Assert.assertEquals("testInstanceType", tbHealthCheckInstance.getInstanceType());
        Assert.assertEquals("NORMAL", tbHealthCheckInstance.getStatus());

        // 测试查询
        TbHealthCheckInstance tbHealthCheckInstance1 = tbHealthCheckInstanceService.getById(tbHealthCheckInstance.getId());
        Assert.assertNotNull(tbHealthCheckInstance1);
        Assert.assertEquals("testInstanceName", tbHealthCheckInstance1.getInstanceName());
        Assert.assertEquals("testInstanceType", tbHealthCheckInstance1.getInstanceType());
        Assert.assertEquals("NORMAL", tbHealthCheckInstance1.getStatus());

        // 测试更新
        tbHealthCheckInstance1.setInstanceName("testInstanceName1");
        tbHealthCheckInstance1.setInstanceType("testInstanceType1");
        tbHealthCheckInstance1.setStatus("EXCEPTION");
        tbHealthCheckInstance1.setSolution("testResolution");
        ModelBeanUtil.setUpdateDefaultValue(tbHealthCheckInstance1);
        boolean update = tbHealthCheckInstanceService.updateById(tbHealthCheckInstance1);
        Assert.assertTrue(update);
        TbHealthCheckInstance tbHealthCheckInstance2 = tbHealthCheckInstanceService.getById(tbHealthCheckInstance1.getId());
        Assert.assertNotNull(tbHealthCheckInstance2);
        Assert.assertEquals("testInstanceName1", tbHealthCheckInstance2.getInstanceName());
        Assert.assertEquals("testInstanceType1", tbHealthCheckInstance2.getInstanceType());
        Assert.assertEquals("EXCEPTION", tbHealthCheckInstance2.getStatus());
        Assert.assertEquals("testResolution", tbHealthCheckInstance2.getSolution());


        // 测试删除
        boolean remove = tbHealthCheckInstanceService.removeById(tbHealthCheckInstance2.getId());
        Assert.assertTrue(remove);
        TbHealthCheckInstance tbHealthCheckInstance3 = tbHealthCheckInstanceService.getById(tbHealthCheckInstance2.getId());
        Assert.assertNull(tbHealthCheckInstance3);

    }

}
