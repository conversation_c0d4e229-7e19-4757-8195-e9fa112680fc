package com.eoi.jax.web.monitor.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.monitor.model.selfmonitor.rule.SelfMonitorAlarmRuleCreateReq;
import com.fasterxml.jackson.core.type.TypeReference;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/12
 */
public class TestArgUtil {
    private static String other_format = "%s.%s(%s);";


    public static void main(String[] args) {
        String jsonStr = "{\"id\":2,\"item_id\":2,\"base_metrics\":\"[{\\\"metricName\\\":\\\"value\\\",\\\"metricPql\\\":\\\"sample\\\"}]\",\"monitor_interval\":120,\"is_enabled\":1,\"object_def\":\"{\\\"objectMode\\\":\\\"TAG_MODE\\\",\\\"tagMode\\\":{\\\"tags\\\":[\\\"\\\"],\\\"objectNameRef\\\":\\\"中台服务\\\"},\\\"instance\\\":{\\\"tags\\\":[\\\"serverAddress\\\"],\\\"objectNameRef\\\":\\\"${serverAddress}\\\"}}\",\"threshold_method\":\"NORMAL\",\"threshold_remark\":\"JVM内存使用率(百分制)\",\"content_template\":\"中台服务${instanceName}JVM内存使用率超过${threshold}%\",\"auto_close\":1,\"xxl_group_id\":2,\"xxl_job_id\":683,\"is_deleted\":0,\"create_time\":\"2024-03-27 09:41:00\",\"update_time\":\"2024-04-09 17:12:09\",\"create_user\":null,\"update_user\":null}";
        Map<String, Object> map = covertToMap(jsonStr);

        SelfMonitorAlarmRuleCreateReq createReq = BeanUtil.mapToBean(map, SelfMonitorAlarmRuleCreateReq.class, true);
//        System.out.println(map);
//        System.out.println(createReq);

        bean(createReq);
    }

    private static Map<String, Object> covertToMap(String jsonStr) {
        Map<String, Object> map = JsonUtil.decode(jsonStr, new TypeReference<Map<String, Object>>() {
        });
        Map<String, Object> result = new HashMap<>();
        map.forEach((k, v) -> {
            if (v instanceof String) {
                String str = (String) v;
                if (str.startsWith("[") && str.endsWith("]")) {
                    v = JsonUtil.decode2ListMap(str);
                } else if (str.startsWith("{") && str.endsWith("}")) {
                    v = JsonUtil.decode2Map(str);
                }
            }
            result.put(StrUtil.toCamelCase(k), v);
        });
        return result;
    }


    private static String bean(Object bean) {
        String simpleName = bean.getClass().getSimpleName();
        String var = StrUtil.lowerFirst(simpleName);
        System.out.println(String.format("%s %s = new %s();", simpleName, var, simpleName));
        // 获取所有字段
        Map<String, Field> fieldMap = ReflectUtil.getFieldMap(bean.getClass());
        // 所有方法
        Set<String> methodNameSet = ReflectUtil.getMethodNames(bean.getClass());
        methodNameSet = methodNameSet.stream().filter(m -> m.startsWith("set")).collect(java.util.stream.Collectors.toSet());

        for (Map.Entry<String, Field> entry : fieldMap.entrySet()) {
            Field field = entry.getValue();
            Object fieldValue = ReflectUtil.getFieldValue(bean, field);
            if (fieldValue == null || ClassUtil.isJdkClass(field.getType())) {
                Optional<String> first = methodNameSet.stream().filter(m -> StrUtil.containsIgnoreCase(m, entry.getKey())).findFirst();
                first.ifPresent(s -> {
                    System.out.println(String.format(other_format,
                        StrUtil.lowerFirst(bean.getClass().getSimpleName()),
                        s,
                        valueFormat(fieldValue)
                    ));
                });
            } else {
                String subVar = bean(fieldValue);
                if (StrUtil.isNotEmpty(var)) {
                    Optional<String> first = methodNameSet.stream().filter(m -> StrUtil.containsIgnoreCase(m, entry.getKey())).findFirst();
                    first.ifPresent(s -> {
                        System.out.println(String.format(other_format, var, s, subVar));
                    });

                }
            }
        }
        return var;
    }


    private static String valueFormat(Object fieldValue) {
        if (fieldValue == null) {
            return "null";
        } else if (ObjectUtil.isBasicType(fieldValue)) {
            if (fieldValue instanceof Long) {
                return String.format("%sL", fieldValue);
            } else if (fieldValue instanceof Double) {
                return String.format("%sD", fieldValue);
            } else {
                return String.format("%s", fieldValue);
            }
        } else if (fieldValue instanceof String) {
            return String.format("\"%s\"", fieldValue);
        } else if (fieldValue instanceof List) {
            return String.format("CollUtil.newArrayList(%s)",
                ((List) fieldValue).stream().map(it -> String.format("\"%s\"", it)).collect(Collectors.joining(",")));
        } else {
            throw new RuntimeException("not support" + fieldValue);
        }
    }


}
