package com.eoi.jax.web.monitor.collector.job;

import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.ingestion.model.storageck.maintenance.StorageCkMaintenanceQueryReq;
import com.eoi.jax.web.ingestion.model.storageck.maintenance.StorageCkMaintenanceResp;
import com.eoi.jax.web.ingestion.service.StorageCkService;
import com.eoi.jax.web.monitor.enumrate.CollectCommand;
import com.eoi.jax.web.monitor.exporter.ConsoleExporter;
import com.eoi.jax.web.monitor.model.collector.CollectContext;
import com.eoi.jax.web.monitor.model.collector.Metric;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StorageCkCollectorTest {

    @InjectMocks
    private StorageCkCollector storageCkCollector;

    @Mock
    private StorageCkService storageCkService;

    @Before
    public void setUp() {
    }

    @Test
    public void testCollectStorageCkMetric_success() {
        when(storageCkService.getMaintenanceList(any(StorageCkMaintenanceQueryReq.class)))
            .thenReturn(new Paged<>(0, createStorageCkMaintenanceResp("RUNNING"))).thenReturn(new Paged<>(0, null));

        CollectContext collectContext = new CollectContext();
        collectContext.setExporter(new ConsoleExporter());
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        storageCkCollector.collect(collectContext);

        List<Metric> exportedMetrics = ((ConsoleExporter) collectContext.getExporter()).getMetricList();
        // Verify the exported metrics
        Assert.assertEquals(2, exportedMetrics.size());

        exportedMetrics.forEach(metric -> Assert.assertEquals(1, metric.getValue()));
    }


    @Test
    public void testCollectStorageCkMetric_failed() {
        when(storageCkService.getMaintenanceList(any(StorageCkMaintenanceQueryReq.class)))
            .thenReturn(new Paged<>(0, createStorageCkMaintenanceResp("RUNNING_FAILED"))).thenReturn(new Paged<>(0, null));

        CollectContext collectContext = new CollectContext();
        collectContext.setExporter(new ConsoleExporter());
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        storageCkCollector.collect(collectContext);

        List<Metric> exportedMetrics = ((ConsoleExporter) collectContext.getExporter()).getMetricList();
        // Verify the exported metrics
        Assert.assertEquals(2, exportedMetrics.size());

        exportedMetrics.forEach(metric -> Assert.assertEquals(0, metric.getValue()));
    }


    private List<StorageCkMaintenanceResp> createStorageCkMaintenanceResp(String status) {
        StorageCkMaintenanceResp resp1 = new StorageCkMaintenanceResp();
        resp1.setStorageCkId(1L);
        resp1.setStorageCkName("test1");
        resp1.setStorageCkStatus(status);

        StorageCkMaintenanceResp resp2 = new StorageCkMaintenanceResp();
        resp2.setStorageCkId(2L);
        resp2.setStorageCkName("test2");
        resp2.setStorageCkStatus(status);
        return CollUtil.newArrayList(resp1, resp2);
    }


}
