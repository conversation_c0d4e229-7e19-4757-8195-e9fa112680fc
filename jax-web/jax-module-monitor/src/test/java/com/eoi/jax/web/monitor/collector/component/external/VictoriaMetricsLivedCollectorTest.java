package com.eoi.jax.web.monitor.collector.component.external;

import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.ingestion.factory.datasource.impl.VictoriaMetricsConnectionServiceImpl;
import com.eoi.jax.web.monitor.enumrate.CollectCommand;
import com.eoi.jax.web.monitor.exporter.ConsoleExporter;
import com.eoi.jax.web.monitor.model.collector.CollectContext;
import com.eoi.jax.web.monitor.model.collector.Metric;
import com.eoi.jax.web.repository.entity.TbDatasource;
import com.eoi.jax.web.repository.entity.TbSystemConfig;
import com.eoi.jax.web.repository.service.TbDatasourceService;
import com.eoi.jax.web.repository.service.TbSystemConfigService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/6/28
 */
@RunWith(MockitoJUnitRunner.class)
public class VictoriaMetricsLivedCollectorTest {

    @InjectMocks
    private VictoriaMetricsLivedCollector victoriaMetricsLivedCollector;

    @Mock
    private SystemConfigHolder systemConfigHolder;
    @Mock
    private TbSystemConfigService systemConfigService;
    @Mock
    private JaxRepository jaxRepository;
    @Mock
    private TbDatasourceService datasourceService;
    @Mock
    private VictoriaMetricsConnectionServiceImpl victoriaMetricsConnectionService;

    @Before
    public void setUp() {
        when(systemConfigHolder.getStr(any(String.class), any(String.class))).thenReturn("http://localhost:8383");

        TbSystemConfig systemConfig = new TbSystemConfig();
        systemConfig.setDsId(1L);
        when(systemConfigService.getOne(any())).thenReturn(systemConfig);

        TbDatasource ds = new TbDatasource();
        ds.setId(1L);
        ds.setName("VM");
        when(jaxRepository.datasourceService()).thenReturn(datasourceService);
        when(datasourceService.getById(any(Long.class))).thenReturn(ds);
    }

    @Test
    public void testCheckVictoriaMetricsIsLived_WhenConnected() {

        when(victoriaMetricsConnectionService.connect(any(String.class), any())).thenReturn(true);

        CollectContext collectContext = new CollectContext();
        collectContext.setExporter(new ConsoleExporter());
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));

        victoriaMetricsLivedCollector.collect(collectContext);

        List<Metric> metrics = ((ConsoleExporter)collectContext.getExporter()).getMetricList();
        assertEquals(1, metrics.size());
        assertEquals(1, (int) metrics.get(0).getValue());
        assertEquals("http://localhost:8383", metrics.get(0).getLabels().get("address"));
        assertEquals("VM", metrics.get(0).getLabels().get("dsName"));
    }

    @Test
    public void testCheckVictoriaMetricsIsLived_WhenNotConnected() {

        CollectContext collectContext = new CollectContext();
        collectContext.setExporter(new ConsoleExporter());
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));

        victoriaMetricsLivedCollector.collect(collectContext);

        List<Metric> metrics = ((ConsoleExporter)collectContext.getExporter()).getMetricList();
        assertEquals(1, metrics.size());
        assertEquals(0, (int) metrics.get(0).getValue());
        assertEquals("http://localhost:8383", metrics.get(0).getLabels().get("address"));
        assertEquals("VM", metrics.get(0).getLabels().get("dsName"));
    }

    @Test
    public void testCheckVictoriaMetricsIsLived_WhenConfigNotFound() {
        when(systemConfigHolder.getStr(any(String.class), any(String.class))).thenReturn("");

        CollectContext collectContext = new CollectContext();
        collectContext.setExporter(new ConsoleExporter());
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));

        victoriaMetricsLivedCollector.collect(collectContext);

        List<Metric> metrics = ((ConsoleExporter)collectContext.getExporter()).getMetricList();
        assertEquals(0, metrics.size());
    }
}
