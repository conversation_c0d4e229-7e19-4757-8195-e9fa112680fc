<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Document</title>
    <style>

        @page {
            size: 900px;
            @top-center {
                content: element(header);
            }
        }

        html, body {
            font-family: 'simsun', serif;
        }

        * {
            padding: 0;
            margin: 0;
        }

        html, body {
            width: 100%;
            padding: 0 12px;
            box-sizing: border-box;
        }

        #container {
            width: 700px;
            margin: 0 auto;
        }

        #page-header {
            position: running(header);
            border-bottom: 1px solid #555;
            padding: 5px;
        }

        #page-header-text {
            text-align: center;
            display:block;
            font-size: 16px;
        }

        #page-header-num:after {
            float: right;
            text-align: right;
            content: counter(page) '/' counter(pages);
        }

        .text-danger {
            color: #ff4d4f;
        }

        .card-item {
            position: relative;
            display: inline-block;
            width: 200px;
            color: white;
            margin: 12px 0;
            height: 90px;
            font-size: 20px;
        }

        .card-item div {
            text-align: center;
            line-height: 90px;
        }

        .card-item-1 {
            background-color: #52c41a;
        }

        .card-item-2 {
            background-color: #faad14;
            margin: 12px 12px;
        }

        .card-item-3 {
            background-color: #ff4d4f;
        }

        .section {
            margin: 12px;
        }

        .section-icon {
            display: inline-block;
            width: 6px;
            height: 20px;
            margin-right: 10px;
            background-color: #007ad2;
            border-radius: 2px;
        }

        table.center {
            width: 100%;
            margin-left: auto;
            margin-right: auto;
        }

        td {
            text-align: center;
        }

        tr th {
            background-color: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            padding: 10px;
        }

        tr td {
            border-bottom: 1px solid #f0f0f0;
            padding: 10px;
            word-wrap: break-word;
        }

        .root-title {
            font-size: 30px;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
<div id="container">
    <div id="page-header">
        <span id="page-header-num"></span>
        <span id="page-header-text">健康检查报告-${date}</span>
    </div>
    <div id="header">
        <div style="font-size: 18px; margin-top: 30px; margin-bottom: 20px">
            <span>本次检查，共完成</span>
            <span class="text-gray-7">${healthCheck.checkItemNum}</span>
            <span> 项自检项，涉及实例 </span>
            <span class="text-gray-7">${healthCheck.instanceNum}</span>
            <span>，耗时 </span>
            #set($min = $healthCheck.consumeSec / 60)
            #set($sec = $healthCheck.consumeSec % 60)
            <span class="text-danger"> ${min}:${sec} </span>
            <span>，共发现 </span>
            <span class="text-danger"> ${healthCheck.exceptionNum} </span>
            <span> 个异常！ </span>
        </div>
        <div class="card-box">
            <div class="card-item-1 card-item">
                <div>正常：${healthCheck.normalNum}</div>
            </div>
            <div class="card-item-2 card-item">
                <div>建议：${healthCheck.suggestionNum}</div>
            </div>
            <div class="card-item-3 card-item">
                <div>异常：${healthCheck.exceptionNum}</div>
            </div>
        </div>
    </div>
    <div class="section">
        <span class="section-icon" style="vertical-align: middle"></span>
        <span style="vertical-align: middle">组件自检</span>
    </div>
    <table class="center">
        <tr>
            <th>组件分类</th>
            <th>检查项</th>
            <th>实例数量</th>
            <th>正常</th>
            <th>建议</th>
            <th>异常</th>
        </tr>
        #foreach($it in ${healthCheck.componentCheck})
            <tr>
                <td>$it.component</td>
                <td>$it.checkItem</td>
                <td>$it.instanceNum</td>
                <td>$it.normalNum</td>
                <td>$it.suggestionNum</td>
                <td>$it.exceptionNum</td>
            </tr>
        #end
    </table>
    <br/>
    <br/>
    <div class="section">
        <span class="section-icon" style="vertical-align: middle"></span>
        <span style="vertical-align: middle">作业自检</span>
    </div>
    <table class="center">
        <tr>
            <th>组件分类</th>
            <th>检查项</th>
            <th>实例数量</th>
            <th>正常</th>
            <th>建议</th>
            <th>异常</th>
        </tr>
        #foreach($it in ${healthCheck.jobCheck})
            <tr>
                <td>$it.component</td>
                <td>$it.checkItem</td>
                <td>$it.instanceNum</td>
                <td>$it.normalNum</td>
                <td>$it.suggestionNum</td>
                <td>$it.exceptionNum</td>
            </tr>
        #end
    </table>
    <br/>
    <br/>
    <div class="section">
        <span class="section-icon" style="vertical-align: middle"></span>
        <span style="vertical-align: middle">数据自检</span>
    </div>
    <table class="center">
        <tr>
            <th>组件分类</th>
            <th>检查项</th>
            <th>实例数量</th>
            <th>正常</th>
            <th>建议</th>
            <th>异常</th>
        </tr>
        #foreach($it in ${healthCheck.dataCheck})
            <tr>
                <td>$it.component</td>
                <td>$it.checkItem</td>
                <td>$it.instanceNum</td>
                <td>$it.normalNum</td>
                <td>$it.suggestionNum</td>
                <td>$it.exceptionNum</td>
            </tr>
        #end
    </table>
    <br/>
    <br/>
    <div class="section">
        <span class="section-icon" style="vertical-align: middle"></span>
        <span style="vertical-align: middle">模型自检</span>
    </div>
    <table class="center">
        <tr>
            <th>组件分类</th>
            <th>检查项</th>
            <th>实例数量</th>
            <th>正常</th>
            <th>建议</th>
            <th>异常</th>
        </tr>
        #foreach($it in ${healthCheck.modelCheck})
            <tr>
                <td>$it.component</td>
                <td>$it.checkItem</td>
                <td>$it.instanceNum</td>
                <td>$it.normalNum</td>
                <td>$it.suggestionNum</td>
                <td>$it.exceptionNum</td>
            </tr>
        #end
    </table>
    <br/>
    <br/>
    <div class="section">
        <span class="section-icon" style="vertical-align: middle"></span>
        <span style="vertical-align: middle">配置自检</span>
    </div>
    <table class="center">
        <tr>
            <th>组件分类</th>
            <th>检查项</th>
            <th>实例数量</th>
            <th>正常</th>
            <th>建议</th>
            <th>异常</th>
        </tr>
        #foreach($it in ${healthCheck.configCheck})
            <tr>
                <td>$it.component</td>
                <td>$it.checkItem</td>
                <td>$it.instanceNum</td>
                <td>$it.normalNum</td>
                <td>$it.suggestionNum</td>
                <td>$it.exceptionNum</td>
            </tr>
        #end
    </table>
    #if($unhealthyInstanceList && !$unhealthyInstanceList.isEmpty())
    <br/>
    <br/>
    <div class="root-title">异常实例（$unhealthyInstanceList.size()）</div>
    <table class="center" style="table-layout: fixed">
        <tr>
            <th>实例名称</th>
            <th>实例类型</th>
            <th>异常内容</th>
            <th>处置建议</th>
        </tr>
        #foreach($it in ${unhealthyInstanceList})
            <tr>
                <td>$it.instanceName</td>
                <td>$it.instanceType</td>
                <td>$it.content</td>
                <td>$it.solution</td>
            </tr>
        #end
    </table>
    #end
    #if($normalList && !$normalList.isEmpty())
    <br/>
    <br/>
    <div class="root-title">正常实例（$normalList.size()）</div>
    <table class="center" style="table-layout: fixed">
        <tr>
            <th>实例名称</th>
            <th>实例类型</th>
        </tr>
        #foreach($it in ${normalList})
            <tr>
                <td>$it.instanceName</td>
                <td>$it.instanceType</td>
            </tr>
        #end
    </table>
    #end
</div>
</body>
</html>
