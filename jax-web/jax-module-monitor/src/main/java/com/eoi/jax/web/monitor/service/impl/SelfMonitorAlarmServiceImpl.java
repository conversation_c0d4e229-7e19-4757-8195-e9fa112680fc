package com.eoi.jax.web.monitor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.*;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.config.config.SelfMonitorAlarmConfig;
import com.eoi.jax.web.core.config.config.SelfMonitorAlarmNotifyConfig;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.core.service.BaseService;
import com.eoi.jax.web.core.util.XXHashUtil;
import com.eoi.jax.web.ingestion.factory.datasource.model.KafkaConnection;
import com.eoi.jax.web.ingestion.model.kafka.KafkaReq;
import com.eoi.jax.web.ingestion.util.KafkaUtil;
import com.eoi.jax.web.monitor.consumer.thread.SelfMonitorAlarmDealParam;
import com.eoi.jax.web.monitor.model.selfmonitor.alert.*;
import com.eoi.jax.web.monitor.model.selfmonitor.rule.SelfMonitorObjectDef;
import com.eoi.jax.web.monitor.plugin.AlarmObjectQueryHelper;
import com.eoi.jax.web.monitor.plugin.SelfMonitorObjectHelper;
import com.eoi.jax.web.monitor.plugin.SelfMonitorXxlJobHelper;
import com.eoi.jax.web.monitor.service.SelfMonitorAlarmService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.service.TbRealtimeCheckRuleService;
import com.eoi.jax.web.repository.service.TbSelfMonitorAlarmService;
import com.eoi.jax.web.repository.service.TbSolutionService;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobInfo;
import com.eoi.jax.web.xxl.service.XxlJobService;
import com.eoi.monitor.model.monitor.MonitorAlarm;
import com.eoi.monitor.model.monitor.SelfMonitorRuleParam;
import freemarker.core.InvalidReferenceException;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Author: yaru.ma
 * @Date: 2024/3/20
 */
@Service
public class SelfMonitorAlarmServiceImpl extends BaseService<
        TbSelfMonitorAlarmService,
        TbSelfMonitorAlarm,
        SelfMonitorAlarmResp,
        SelfMonitorAlarmCreateReq,
        SelfMonitorAlarmCloseReq,
        SelfMonitorAlarmQueryReq> implements SelfMonitorAlarmService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SelfMonitorAlarmServiceImpl.class);

    @Autowired
    private TbSelfMonitorAlarmService alarmService;
    @Autowired
    private JaxRepository jaxRepository;
    @Autowired
    private TbRealtimeCheckRuleService realtimeCheckRuleService;
    @Autowired
    private TbSolutionService tbSolutionService;
    @Autowired
    private AlarmObjectQueryHelper alarmObjectQueryHelper;
    @Autowired
    private SystemConfigHolder systemConfigHolder;
    @Autowired
    private XxlJobService xxlJobService;
    @Autowired
    private SelfMonitorXxlJobHelper xxlJobHelper;

    private KafkaProducer<String, String> producer;
    private String topic;

    public SelfMonitorAlarmServiceImpl(TbSelfMonitorAlarmService alarmService) {
        super(alarmService);
    }


    @PostConstruct
    public void init() {
        systemConfigHolder.addChangeListener(SystemConfigEnum.SELF_MONITOR_ALARM, event -> updateAllXxljob());
        systemConfigHolder.addChangeListener(SystemConfigEnum.JAX_METRICS, event -> updateAllXxljob());
        systemConfigHolder.addChangeListener(SystemConfigEnum.SELF_MONITOR_ALARM_NOTIFY, event -> {
            Map<String, Object> newConfig = event.getNewConfigMap();
            Boolean enable = (Boolean) newConfig.getOrDefault("enable", false);
            if (ConfigChangeType.DELETED.equals(event.getChangeType()) || !enable) {
                closeNotify();
            } else if (ConfigChangeType.ADDED.equals(event.getChangeType()) || ConfigChangeType.MODIFIED.equals(event.getChangeType())) {
                buildKafkaNotify(newConfig);
            }
        });
    }

    private void updateAllXxljob() {
        SelfMonitorAlarmConfig kafkaConfig = systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_ALARM);
        String vmUrl = systemConfigHolder.getStr(SystemConfigEnum.JAX_METRICS.getKey(), "address");
        if (Objects.isNull(kafkaConfig) || StrUtil.hasBlank(kafkaConfig.getBrokerAddress(), kafkaConfig.getTopic(), vmUrl)) {
            return;
        }
        try {
            List<TbSelfMonitorAlarmRule> all = jaxRepository.selfAlarmRule().list();
            List<TbSelfMonitorAlarmRule> enabledRule = all.stream()
                    .filter(x -> x.getIsEnabled() == 1).collect(Collectors.toList());
            List<Integer> xxlJobIds = enabledRule.stream().map(TbSelfMonitorAlarmRule::getXxlJobId).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (enabledRule.size() <= 0) {
                return;
            }
            if (xxlJobIds.isEmpty()) {
                LOGGER.info("初始化自监控xxljob..");
                createTopicIfNotExists(kafkaConfig.getTopic(), BeanUtil.beanToMap(kafkaConfig));
                all.forEach(rule -> xxlJobHelper.update(rule.getId()));
            } else {
                AbstractXxlJobInfo abstractXxlJobInfo = xxlJobService.get(xxlJobIds.get(0));
                SelfMonitorRuleParam ruleParam = JsonUtil.decode(abstractXxlJobInfo.getExecutorParam(), SelfMonitorRuleParam.class);
                SelfMonitorAlarmConfig kafkaReporter = JsonUtil.decode(JsonUtil.encode(ruleParam.getReporters().get(0).get("parameters")),
                        SelfMonitorAlarmConfig.class);
                vmUrl = StrUtil.addPrefixIfNot(vmUrl, "http://");
                vmUrl = StrUtil.removePrefix(vmUrl, "/");
                if (!ObjectUtil.equal(ruleParam.getVmUrl(), vmUrl)) {
                    LOGGER.info("监控配置发生变动,全量更新自监控xxljob配置..");
                    all.forEach(rule -> xxlJobHelper.update(rule.getId()));
                } else if (!ObjectUtil.equal(kafkaReporter.getBrokerAddress(), kafkaConfig.getBrokerAddress())
                        || !ObjectUtil.equal(kafkaConfig.getTopic(), kafkaReporter.getTopic())) {
                    LOGGER.info("监控配置发生变动,全量更新自监控xxljob配置..");
                    createTopicIfNotExists(kafkaConfig.getTopic(), BeanUtil.beanToMap(kafkaConfig));
                    all.forEach(rule -> xxlJobHelper.update(rule.getId()));
                }
            }
        } catch (Exception e) {
            LOGGER.error("自监控规则xxljob更新异常", e);
        }
    }

    private void createTopicIfNotExists(String topicName, Map<String, Object> kafkaConfig) {
        Boolean autoCreateTopic = (Boolean) kafkaConfig.get("autoCreateTopic");
        if (!BooleanUtil.isTrue(autoCreateTopic)) {
            return;
        }
        LOGGER.info("准备创建topic:{}", topicName);
        try {
            KafkaConnection kafkaConnection = BeanUtil.mapToBean(kafkaConfig, KafkaConnection.class, false);
            if (!KafkaUtil.getTopicIsExists(kafkaConnection.getBrokerAddress(), topicName, kafkaConnection)) {
                KafkaUtil.createTopicByAdminClient(new KafkaReq(kafkaConnection.getBrokerAddress(),
                        null, 1, (short) 1,
                        null, topicName, null, kafkaConnection, null));
                LOGGER.info("创建topic成功:{}", topicName);
            }
        } catch (Exception e) {
            LOGGER.error("创建topic失败:{}:{},跳过此步骤", topicName, e.getMessage());
        }
    }

    /**
     * 数据质量告警,内部接口调用
     * 重要组件告警，内部组件调用
     * 自监控，kafka接入
     *
     * @param dealParam
     * @return
     */
    @Override
    public boolean dealAlertMessage(SelfMonitorAlarmDealParam dealParam) {
        MonitorAlarm alarm = dealParam.getAlarm();
        // 自监控: objectId
        TbSelfMonitorAlarmRule rule = isSelfMonitor(dealParam, alarm);
        Boolean hasObjectId = fillSelfMonitorObjectId(rule, dealParam, alarm);
        if (rule != null && !hasObjectId) {
            return false;
        }
        TbSelfMonitorAlarm activeAlarm = getActiveAlarm(alarm);
        Long alarmUid = activeAlarm == null ? calAlarmUid(alarm) : activeAlarm.getUid();
        Long alarmId = activeAlarm == null ? IdUtil.genId() : activeAlarm.getId();
        LOGGER.debug("告警压缩id:{},已存在同一告警:{}", alarmUid, activeAlarm == null);
        // 告警恢复
        if (AlertStatusEnum.CLOSED.equals(alarm.getStatus())) {
            Assert.notNull(activeAlarm, "无活跃告警,可能已恢复:{}", JsonUtil.encode(alarm));
            autoCloseAlarm(activeAlarm.getId(), alarm.getResolvedTime(), null);
            return true;
        }
        TbSelfMonitorEvent event = reBuildEvent(alarm, alarmId, rule);
        LOGGER.debug("原始告警入库:{}", JsonUtil.encode(event));
        jaxRepository.eventService().save(event);
        notifyAlarm(new NotifyAlarm().copyFrom(event));
        TbSelfMonitorAlarm tbAlarm = initOrUpdateAlarm(activeAlarm, event, alarmUid);
        changeResourceTypeIfNeed(activeAlarm, tbAlarm);
        alarmService.saveOrUpdate(tbAlarm);
        LOGGER.debug("告警入库:{}", JsonUtil.encode(tbAlarm));
        return true;
    }

    private void changeResourceTypeIfNeed(TbSelfMonitorAlarm activeAlarm, TbSelfMonitorAlarm tbAlarm) {
        // 新增告警需要识别资源类型
        if (activeAlarm == null && StrUtil.isBlank(tbAlarm.getResourceType())) {
            SelfMonitorObjectTypeEnum objectType = SelfMonitorObjectTypeEnum.fromString(tbAlarm.getObjectType());
            // flink任务需要细分任务类型
            if (objectType == SelfMonitorObjectTypeEnum.FLINK_TASK) {
                TbPipeline one = jaxRepository.pipeline().getOne(new LambdaQueryWrapper<TbPipeline>()
                        .eq(TbPipeline::getProcessId, tbAlarm.getObjectId()));
                tbAlarm.setResourceType(one != null ? one.getResourceType() : ProjectResourceTypeEnum.SELF_MONITOR_OBJECT.code());
            }
            // 无法识别的数据权限用self_monitor_object
            if (StrUtil.isBlank(tbAlarm.getResourceType())) {
                tbAlarm.setResourceType(ProjectResourceTypeEnum.SELF_MONITOR_OBJECT.code());
            }
        }
    }


    @Override
    public void directAlarm(List<MonitorAlarm> alarms) {
        Map<String, Object> selfMonitorCache = new HashMap<>(8);
        alarmObjectQueryHelper.refreshMonitorRule(selfMonitorCache);
        List<SelfMonitorAlarmDealParam> alarmDealParams = alarms.stream()
                .map(x -> new SelfMonitorAlarmDealParam(x, alarmObjectQueryHelper, selfMonitorCache))
                .collect(Collectors.toList());
        alarmDealParams.forEach(this::dealAlertMessage);
    }

    /**
     * @param alarmList
     * @return
     */
    @Override
    public List<TbSelfMonitorEvent> dealAlertMessage(List<MonitorAlarm> alarmList) {
        List<TbSelfMonitorEvent> events = new ArrayList<>();
        Map<String, Object> objectCache = alarmObjectQueryHelper.getObjectCache();
        List<SelfMonitorAlarmDealParam> dealParams = alarmList.stream()
                .map(alarm -> new SelfMonitorAlarmDealParam(alarm, alarmObjectQueryHelper, objectCache))
                .collect(Collectors.toList());
        for (SelfMonitorAlarmDealParam dealParam : dealParams) {
            MonitorAlarm alarm = dealParam.getAlarm();
            // 自监控: objectId
            TbSelfMonitorAlarmRule rule = isSelfMonitor(dealParam, alarm);
            boolean hasObjectId = fillSelfMonitorObjectId(rule, dealParam, alarm);
            if (rule != null && !hasObjectId) {
                continue;
            }
            TbSelfMonitorEvent event = reBuildEvent(alarm, null, rule);
            events.add(event);
        }
        return events;
    }

    @Override
    public Paged<SelfMonitorAlarmResp> query(SelfMonitorAlarmQueryReq req) {
        IPage<TbSelfMonitorAlarm> customPage = alarmService.selectCustomPage(req.page(), req.query());
        return new Paged<>(customPage.getTotal(), marshallingRespListFrom(customPage.getRecords()));
    }


    @Override
    public Map<String, Object> statistics() {
        Map<String, Object> statistics = alarmService.activeAlarmStatistics();
        long sum = statistics.values().stream().map(Objects::toString).mapToLong(Long::valueOf).sum();
        statistics.put("alarmSum", sum);
        return statistics;
    }

    @Override
    public List<AlarmStatisticsObjectTypes> objectStatistics(SelfMonitorAlarmQueryReq req) {
        List<AlarmStatisticsObjectTypes> list = new ArrayList<>();
        List<TbSelfMonitorAlarm> objectStatistics = alarmService.objectStatistics(req.query());
        objectStatistics.stream().collect(Collectors.groupingBy(TbSelfMonitorAlarm::getObjectType))
                .forEach((objectType, objs) -> {
                    SelfMonitorObjectTypeEnum typeEnum = SelfMonitorObjectTypeEnum.fromString(objectType);
                    if (typeEnum != null) {
                        AlarmStatisticsObjectTypes type = new AlarmStatisticsObjectTypes();
                        type.setObjectType(objectType);
                        type.setObjectTypeName(typeEnum.message());
                        type.setObjects(objs.stream().map(x -> new AlarmStatisticsObjectTypes.AlarmStatisticsObject(x.getObjectName(),
                                x.getObjectId())).collect(Collectors.toList()));
                        list.add(type);
                    }
                });
        return list;
    }

    @Override
    public void autoCloseAlarm(Long alarmId, Long closeTime, String closeReason) {
        TbSelfMonitorAlarm alarm = new TbSelfMonitorAlarm();
        alarm.setId(alarmId);
        alarm.setCloseReason(Common.isEmpty(closeReason) ? "自动恢复" : closeReason);
        alarm.setCloseTime(new Date(closeTime));
        alarm.setStatus(AlertStatusEnum.CLOSED.code());
        alarm.setCloseType(AlertCloseTypeEnum.AUTO_CLOSE.code());
        ModelBeanUtil.setUpdateDefaultValue(alarm);
        notifyAlarm(buildRecoveryEvent(alarm));
        alarmService.updateById(alarm);
    }

    private NotifyAlarm buildRecoveryEvent(TbSelfMonitorAlarm alarm) {
        NotifyAlarm notifyAlarm = new NotifyAlarm();
        TbSelfMonitorEvent event = jaxRepository.eventService().getOne(new LambdaQueryWrapper<TbSelfMonitorEvent>()
                .eq(TbSelfMonitorEvent::getAlarmId, alarm.getId())
                .last("limit 1"));
        if (event == null) {
            LOGGER.error("告警[{}]无原始告警，无法推送恢复告警", alarm.getId());
            return null;
        }
        notifyAlarm.setId(event.getId());
        notifyAlarm.setAlarmId(alarm.getId());
        notifyAlarm.setResolvedTime(alarm.getCloseTime());
        notifyAlarm.setCloseReason(alarm.getCloseReason());
        notifyAlarm.setCloseType(alarm.getCloseType());
        notifyAlarm.setStatus(alarm.getStatus());
        notifyAlarm.setContent(alarm.getCloseReason());
        return notifyAlarm;
    }

    private TbSelfMonitorAlarmRule isSelfMonitor(SelfMonitorAlarmDealParam dealParam,
                                                 MonitorAlarm alarm) {
        if (alarm.getRuleId() != null
                && dealParam.getSelfMonitorRule() != null
                && dealParam.getSelfMonitorRule().containsKey(alarm.getRuleId())) {
            return dealParam.getSelfMonitorRule().get(alarm.getRuleId());
        }
        return null;
    }

    @Nullable
    private boolean fillSelfMonitorObjectId(TbSelfMonitorAlarmRule rule, SelfMonitorAlarmDealParam dealParam,
                                            MonitorAlarm alarm) {
        if (rule != null) {
            SelfMonitorObjectDef objectDef = JsonUtil.decode(rule.getObjectDef(), SelfMonitorObjectDef.class);
            Long objectId = getObjectHelper().getOrCalObjectId(dealParam, objectDef);
            if (objectId == null) {
                LOGGER.debug("dealSelfMonitorAlarm: objectId is null, labels:{}", JsonUtil.encode(alarm.getLabels()));
                return false;
            }
            alarm.setObjectId(objectId);
        }
        // 丰富objectName
        String objectDef = rule != null ? rule.getObjectDef() : null;
        alarm.setObjectName(getObjectHelper().getOrCalObjectName(objectDef, alarm, alarm.getObjectId()));
        alarm.setInstanceName(getObjectHelper().getOrCalInstanceName(objectDef, alarm));
        return true;
    }

    /**
     * 接入数据质量原始告警
     *
     * @param tbAlert
     */
    @Override
    public void dealDataQualityAlarm(TbAlert tbAlert, List<Long> modelIds) {
        try {
            SelfMonitorAlarmConfig kafkaConfig = systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_ALARM);
            if (!BooleanUtil.isTrue(Optional.ofNullable(kafkaConfig).map(SelfMonitorAlarmConfig::getDataQualityAlarm).orElse(false))) {
                return;
            }
            // 告警转换
            AlarmUidParam alarmUuidParam = new AlarmUidParam(JsonUtil.encode(getDataQualityLabels(tbAlert.getAlertKey())));
            Long alarmUid = XXHashUtil.hash64(JsonUtil.encode(alarmUuidParam).getBytes(StandardCharsets.UTF_8));
            SelfMonitorAlarmDealParam dealParam = new SelfMonitorAlarmDealParam();
            MonitorAlarm alarm = new MonitorAlarm();
            alarm.setId(tbAlert.getId().toString());
            alarm.setLabels(getDataQualityLabels(tbAlert.getAlertKey()));
            alarm.setAlarmUid(alarmUid);
            alarm.setAlertTime(tbAlert.getActiveTime());
            alarm.setContent(tbAlert.getContent());
            alarm.setValue(String.valueOf(tbAlert.getValue()));
            // 告警对象
            TbPipeline pipeline = jaxRepository.pipeline().getById(tbAlert.getPipelineId());
            if (ProjectResourceTypeEnum.TB_REALTIME_CHECK_TASK.equals(pipeline.getResourceType())) {
                if (CollUtil.isNotEmpty(modelIds)) {
                    Long modelId = modelIds.get(0);
                    alarm.setObjectId(modelId);
                    alarm.setObjectName(jaxRepository.table().getById(modelId).getTbAlias());
                    alarm.setObjectType(SelfMonitorObjectTypeEnum.TABLE.code());
                }
            } else {
                alarm.setObjectId(tbAlert.getPipelineId());
                alarm.setObjectType(SelfMonitorObjectTypeEnum.FLINK_TASK.code());
                alarm.setObjectName(pipeline.getPipelineAlias());
            }
            alarm.setMetricId(null);
            alarm.setMetricName(null);
            alarm.setRuleId(tbAlert.getRuleId());
            TbRealtimeCheckRule rule = realtimeCheckRuleService.getById(tbAlert.getRuleId());
            alarm.setRuleName(rule != null ? rule.getName() : null);
            alarm.setSeverity(tbAlert.getLevel());
            alarm.setSource(AlarmSourceEnum.DATA_QUALITY.code());
            LOGGER.debug("数据质量告警接入:{}", JsonUtil.encode(alarm));
            dealParam.setAlarm(alarm);
            dealAlertMessage(dealParam);
        } catch (Exception e) {
            LOGGER.error("数据质量告警在接入到自监控时出现异常", e);
        }
    }

    @Override
    public void dealDataQualityAlarm(String alertKey, Long resolvedTime, String reason) {
        AlarmUidParam alarmUuidParam = new AlarmUidParam(JsonUtil.encode(getDataQualityLabels(alertKey)));
        Long alarmUid = XXHashUtil.hash64(JsonUtil.encode(alarmUuidParam).getBytes(StandardCharsets.UTF_8));
        TbSelfMonitorAlarm activeAlarm = alarmService.getActiveAlarmByUid(alarmUid, AlertStatusEnum.OPEN.code());
        autoCloseAlarm(activeAlarm.getId(), resolvedTime, reason);
    }

    @Override
    public void closeByIds(SelfMonitorAlarmCloseReq req) {
        if (!Common.isEmpty(req.getIds())) {
            for (Long id : req.getIds()) {
                req.setId(id);
                update(req);
            }
        }
    }

    @Override
    public SelfMonitorAlarmResp update(SelfMonitorAlarmCloseReq req) {
        SelfMonitorAlarmResp update = super.update(req);
        notifyAlarm(buildRecoveryEvent(jaxRepository.alarmService().getById(req.getId())));
        return update;
    }

    @Override
    public Paged<SelfMonitorAlarmResp> queryList(SelfMonitorAlarmQueryReq req) {
        return query(req);
    }

    @Override
    public Map<String, String> allSolutions() {
        return tbSolutionService.list().stream().collect(Collectors.toMap(x -> x.getId().toString(), TbSolution::getSolution));
    }

    private TbSelfMonitorAlarm initOrUpdateAlarm(TbSelfMonitorAlarm activeAlarm, TbSelfMonitorEvent event, Long alarmUid) {
        TbSelfMonitorAlarm alarm = new TbSelfMonitorAlarm();
        alarm.setValue(event.getValue());
        alarm.setContent(event.getContent());
        alarm.setId(event.getAlarmId());
        if (activeAlarm != null) {
            alarm.setId(activeAlarm.getId());
            alarm.setLastTime(event.getActiveTime());
            alarm.setContent(event.getContent());
            alarm.setObjectName(event.getObjectName());
            alarm.setRuleName(event.getRuleName());
            alarm.setOccurCount(activeAlarm.getOccurCount() + 1);
            alarm.setSeverity(maxSeverity(event.getSeverity(), activeAlarm.getSeverity()));
            ModelBeanUtil.setUpdateDefaultValue(alarm);
        } else {
            alarm.setUid(alarmUid);
            alarm.setOccurTime(event.getActiveTime());
            alarm.setLastTime(event.getActiveTime());
            alarm.setOccurCount(1L);
            alarm.setStatus(AlertStatusEnum.OPEN.code());
            alarm.setSeverity(event.getSeverity());
            alarm.setObjectId(event.getObjectId());
            alarm.setAddition(event.getAddition());
            SelfMonitorObjectTypeEnum objectTypeEnum = SelfMonitorObjectTypeEnum.fromString(event.getObjectType());
            alarm.setObjectType(event.getObjectType());
            if (objectTypeEnum != null && objectTypeEnum.resourceType() != null) {
                alarm.setResourceType(objectTypeEnum.resourceType().code());
            }
            alarm.setObjectName(event.getObjectName());
            alarm.setRuleId(event.getRuleId());
            alarm.setRuleName(event.getRuleName());
            alarm.setInstanceName(event.getInstanceName());
            alarm.setMetricId(event.getMetricId());
            alarm.setMetricName(event.getMetricName());
            alarm.setSource(event.getSource());
            alarm.setSolutionId(event.getSolutionId());
            ModelBeanUtil.setCreateDefaultValue(alarm);
            alarm.setId(event.getAlarmId());
        }
        return alarm;
    }

    private String maxSeverity(String severity, String previousSeverity) {
        SelfMonitorAlertSeverityEnum severityEnum = SelfMonitorAlertSeverityEnum.fromString(severity);
        SelfMonitorAlertSeverityEnum previousSeverityEnum = SelfMonitorAlertSeverityEnum.fromString(previousSeverity);
        if (severityEnum == null || previousSeverityEnum == null) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "告警等级不符合规范");
        }
        if (severityEnum.level() >= previousSeverityEnum.level()) {
            return severityEnum.code();
        }
        return previousSeverityEnum.code();
    }

    private TbSelfMonitorEvent reBuildEvent(MonitorAlarm alertDealParam,
                                            Long alarmId, TbSelfMonitorAlarmRule rule) {
        TbSelfMonitorEvent event = new TbSelfMonitorEvent();
        event.setOriginId(alertDealParam.getId());
        event.setAlarmId(alarmId);
        event.setActiveTime(alertDealParam.getAlertTime() != null ? new Date(alertDealParam.getAlertTime()) : null);
        event.setResolvedTime(alertDealParam.getResolvedTime() != null ? new Date(alertDealParam.getResolvedTime()) : null);
        event.setStatus(AlertStatusEnum.OPEN.code());
        event.setSeverity(alertDealParam.getSeverity());
        event.setObjectId(alertDealParam.getObjectId());
        event.setObjectType(alertDealParam.getObjectType());
        event.setObjectName(alertDealParam.getObjectName());
        event.setRuleId(alertDealParam.getRuleId());
        event.setRuleName(alertDealParam.getRuleName());
        event.setValue(alertDealParam.getValue());
        event.setSource(alertDealParam.getSource());
        event.setMetricId(alertDealParam.getMetricId());
        event.setMetricName(alertDealParam.getMetricName());
        event.setContent(alertDealParam.getContent());
        event.setInstanceName(alertDealParam.getInstanceName());
        event.setAddition(JsonUtil.encode(alertDealParam.getLabels()));
        if (rule != null) {
            event.setSolutionId(Long.parseLong(alertDealParam.getAnnotations().get("solutionId").toString()));
            event.setMetricId(rule.getItemId());
            event.setMetricName(alertDealParam.getMetricName());
            event.setContent(buildAlarmContent(alertDealParam));
        }
        ModelBeanUtil.setCreateDefaultValue(event);
        return event;
    }

    /**
     * 告警内容参数替换
     * 支持参数:
     * 1. 规则信息: 规则名称, 阈值设置，阈值策略名,告警等级,监控指标名称,监控组件类型
     * 2. 告警对象信息: 实时指标值,对象名称,labels
     *
     * @return
     */
    public String buildAlarmContent(MonitorAlarm alertDealParam) {
        Map<String, Object> params = new HashMap<>(alertDealParam.getLabels());
        // annotations包含阈值，阈值策略名
        if (alertDealParam.getAnnotations() != null) {
            params.putAll(alertDealParam.getAnnotations());
        }
        params.put("ruleName", "自监控-" + alertDealParam.getMetricName());
        params.put("severity", alertDealParam.getSeverity());
        params.put("metricName", alertDealParam.getMetricName());
        params.put("instanceName", alertDealParam.getInstanceName());
        SelfMonitorObjectTypeEnum typeEnum = SelfMonitorObjectTypeEnum.fromString(alertDealParam.getObjectType());
        params.put("objectType", typeEnum != null ? typeEnum.message() : null);
        params.put("value", alertDealParam.getValue());
        params.put("objectName", alertDealParam.getObjectName());
        return paramSubstitution(alertDealParam.getContent(), params);
    }

    private TbSelfMonitorAlarm getActiveAlarm(MonitorAlarm alarm) {
        if (alarm.getAlarmUid() != null) {
            return jaxRepository.alarmService()
                    .getActiveAlarmByUid(alarm.getAlarmUid(), AlertStatusEnum.OPEN.code());
        }
        if (alarm.getObjectId() != null && alarm.getRuleId() != null) {
            return jaxRepository.alarmService()
                    .getActiveAlarmByObjectRule(alarm.getObjectId(), alarm.getRuleId(),
                            alarm.getInstanceName(), AlertStatusEnum.OPEN.code());
        }
        return null;
    }

    private Long calAlarmUid(MonitorAlarm alarm) {
        AlarmUidParam alarmUuidParam;
        if (alarm.getAlarmUid() != null) {
            return alarm.getAlarmUid();
        } else if (alarm.getObjectId() != null && alarm.getRuleId() != null) {
            alarmUuidParam = new AlarmUidParam(alarm.getObjectId(), alarm.getRuleId(),
                    alarm.getInstanceName(), alarm.getAlertTime());
        } else {
            return IdUtil.genId();
        }
        return XXHashUtil.hash64(JsonUtil.encode(alarmUuidParam).getBytes(StandardCharsets.UTF_8));
    }

    private SelfMonitorObjectHelper getObjectHelper() {
        return ContextHolder.getBean(SelfMonitorObjectHelper.class);
    }


    @NotNull
    private HashMap<String, Object> getDataQualityLabels(String alertKey) {
        return MapUtil.of("alertKey", alertKey);
    }


    public static String paramSubstitution(String content, Map<String, Object> params) {
        Configuration templateConfig = new Configuration(Configuration.VERSION_2_3_22);
        templateConfig.setLogTemplateExceptions(false);
        templateConfig.setTemplateExceptionHandler((e, environment, writer) -> {
            if (!(e instanceof InvalidReferenceException)) {
                throw e;
            }
        });
        try {
            Template template = new Template("", content, templateConfig);
            StringWriter stringWriter = new StringWriter();
            template.process(params, stringWriter);
            return stringWriter.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void notifyAlarm(NotifyAlarm notifyAlarm) {
        if (Objects.isNull(producer) || Objects.isNull(notifyAlarm)) {
            return;
        }
        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(topic, JsonUtil.encode(notifyAlarm));
            Future<RecordMetadata> sendStatus = producer.send(record);
            RecordMetadata recordMetadata = sendStatus.get();
            LOGGER.debug("消息发送成功: topic: {}, offset: {}", recordMetadata.topic(), recordMetadata.offset());
        } catch (Exception e) {
            LOGGER.error("发送失败:  topic: {}, errMsg: {}, {}", topic, e.getMessage(), e);
        }
    }

    /**
     * 额外配置, enable, topic
     *
     * @param configMap
     */
    public void buildKafkaNotify(Map<String, Object> configMap) {
        SelfMonitorAlarmNotifyConfig notifyConfig = BeanUtil.mapToBean(configMap, SelfMonitorAlarmNotifyConfig.class, false);
        Assert.notBlank(notifyConfig.getBrokerAddress(), "kafka broker连接地址不能为空");
        this.topic = notifyConfig.getTopic();
        Assert.notBlank(topic, "kafka topic不能为空");
        createTopicIfNotExists(topic, configMap);
        this.producer = KafkaUtil.getProducer(BeanUtil.mapToBean(configMap, KafkaConnection.class, false));
    }

    private void closeNotify() {
        if (Objects.nonNull(producer)) {
            KafkaProducer<String, String> localProducer = this.producer;
            this.producer = null;
            localProducer.close();
        }
        topic = null;
    }

}
