package com.eoi.jax.web.monitor.check;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.api.dataservice.model.ds.conn.HiveConnectionSetting;
import com.eoi.jax.api.dataservice.util.ConnectionUtil;
import com.eoi.jax.api.dataservice.util.HiveUtil;
import com.eoi.jax.api.dataservice.util.MysqlUtil;
import com.eoi.jax.web.core.client.elasticsearch.AllTemplate;
import com.eoi.jax.web.core.client.elasticsearch.ElasticsearchClient;
import com.eoi.jax.web.core.client.nebula.NebulaClient;
import com.eoi.jax.web.core.common.constant.ElasticsearchConstant;
import com.eoi.jax.web.core.common.constant.TableType;
import com.eoi.jax.web.core.common.enumrate.TableDeployPlatformEnum;
import com.eoi.jax.web.core.common.enumrate.TableDeployStatusEnum;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.clickhouse.ClickhouseModelSetting;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.hive.HiveModelSetting;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.kafka.KafkaModelSetting;
import com.eoi.jax.web.data.modeling.model.tabledeploy.TableDeployModelQueryFilterReq;
import com.eoi.jax.web.data.modeling.model.tabledeploy.TableDeployModelQueryReq;
import com.eoi.jax.web.data.modeling.model.tabledeploy.TableDeployResp;
import com.eoi.jax.web.data.modeling.service.TableDeployService;
import com.eoi.jax.web.ingestion.factory.datasource.model.ElasticsearchConnection;
import com.eoi.jax.web.ingestion.factory.datasource.model.KafkaConnection;
import com.eoi.jax.web.ingestion.factory.datasource.model.MysqlConnection;
import com.eoi.jax.web.ingestion.factory.datasource.model.NebulaConnection;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceResp;
import com.eoi.jax.web.ingestion.plugin.pipeline.NebulaModelSetting;
import com.eoi.jax.web.ingestion.service.ClickhouseService;
import com.eoi.jax.web.ingestion.service.DatasourceService;
import com.eoi.jax.web.ingestion.util.KafkaUtil;
import com.eoi.jax.web.monitor.enumrate.CheckCategoryEnum;
import com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum;
import com.eoi.jax.web.monitor.model.check.CheckContext;
import com.eoi.jax.web.monitor.model.check.HealthCheckInstanceResult;
import com.eoi.jax.web.monitor.model.check.HealthCheckResult;
import com.eoi.jax.web.monitor.model.collector.NodeConnectionResult;
import com.eoi.jax.web.monitor.util.NetworkUtil;
import com.google.common.base.Stopwatch;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.eoi.jax.web.core.common.enumrate.SelfMonitorObjectTypeEnum.TABLE_DEPLOY;
import static com.eoi.jax.web.ingestion.service.impl.ClickhouseServiceImpl.ENGINE_PATTERN;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Service
public class ModelCheckHandle extends AbstractHealthCheckHandle {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelCheckHandle.class);

    public static final Pattern HIVE_PATTERN =
        Pattern.compile("(jdbc:hive2://[^/&?]+)/?([^/&?]*)?(\\?.*)?", Pattern.CASE_INSENSITIVE);

    @Resource
    private TableDeployService tableDeployService;

    @Resource
    private DatasourceService datasourceService;

    @Resource
    private ClickhouseService clickhouseService;

    /**
     * kafka topic缓存
     * <DsId, Set<Topic>>
     */
    private Map<Long, Set<String>> topicCache = new ConcurrentHashMap<>();

    /**
     * es模板缓存
     * <DsId, Map<Template, AllTemplate>>
     */
    private Map<Long, Map<String, AllTemplate>> esTemplateCache = new ConcurrentHashMap<>();

    /**
     * clickhouse表缓存
     * <DsId-Database, Map<Table, Engine>>
     */
    private Map<String, Map<String, String>> clickhoseTableCache = new ConcurrentHashMap<>();


    /**
     * mysql表缓存
     * <DsId-Type, Set<Table>>
     */
    private Map<String, Set<String>> mysqlTableCache = new ConcurrentHashMap<>();

    /**
     * nebula表缓存
     * <DsId-Type, Set<Tag/Edge>>
     */
    private Map<String, Set<String>> nebulaCache = new ConcurrentHashMap<>();

    /**
     * clickhouse表缓存
     * <DsId, Set<Table>>
     */
    private Map<Long, Set<String>> hiveTableCache = new ConcurrentHashMap<>();


    @Override
    public void check(CheckContext context) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        LOGGER.info("模型健康检查开始");
        List<Consumer<CheckContext>> consumerList = new LinkedList<>();
        consumerList.add(this::findDeployFailedModel);
        consumerList.add(this::checkKafkaTopicIsExist);
        consumerList.add(this::checkEsIndexIsExist);
        consumerList.add(this::checkClickHouseTableIsExist);
        consumerList.add(this::checkMysqlTableIsExist);
        consumerList.add(this::checkHiveTableIsExist);
        consumerList.add(this::checkNebulaTagOrEdgeIsExist);

        for (Consumer<CheckContext> consumer : consumerList) {
            try {
                consumer.accept(context);
            } catch (Exception e) {
                LOGGER.error("模型健康检查异常", e);
            }
        }
        this.clear();
        LOGGER.info("模型健康检查结束, 耗时:{}秒", stopwatch.elapsed(TimeUnit.SECONDS));
    }

    /**
     * 获取发布失败的模型
     */
    private void findDeployFailedModel(CheckContext context) {
        HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(), CheckCategoryEnum.MODEL,
            HealthCheckComponentEnum.MODEL_DEPLOY.code(), "模型是否发布成功");
        checkModelDeploy(TableDeployStatusEnum.SUCCESS.code(), it -> {
            HealthCheckInstanceResult instanceResult =
                HealthCheckInstanceResult.createSuccess(it.getId(), it.getName(), TABLE_DEPLOY.code());
            instanceResult.putInstanceInfo("platform", it.getPlatform());
            healthCheckResult.getInstanceList().add(instanceResult);
        });

        checkModelDeploy(TableDeployStatusEnum.FAILURE.code(), it -> {
            HealthCheckInstanceResult instanceResult =
                HealthCheckInstanceResult.createSuccess(it.getId(), it.getName(), TABLE_DEPLOY.code());
            instanceResult.putInstanceInfo("platform", it.getPlatform());
            instanceResult.setExceptionContentAndSolution("模型发布失败",
                HealthCheckComponentEnum.MODEL_DEPLOY.resolution());
            healthCheckResult.getInstanceList().add(instanceResult);
        });

        context.getCheckResultStorage().store(CollUtil.newArrayList(healthCheckResult));
    }

    private void checkModelDeploy(String deployStatus, Consumer<TableDeployResp> consumer) {
        TableDeployModelQueryFilterReq filter = new TableDeployModelQueryFilterReq();
        filter.setDeployStatus(deployStatus);
        for (int i = 0; ; i++) {
            TableDeployModelQueryReq req = new TableDeployModelQueryReq();
            req.setPage(i);
            req.setSize(500);
            req.setFilter(filter);
            Paged<TableDeployResp> paged = tableDeployService.queryByCondition(req);
            if (CollUtil.isEmpty(paged.getList())) {
                break;
            }
            paged.getList().forEach(consumer);
        }
    }


    private void checkKafkaTopicIsExist(CheckContext context) {

        // 检查结果，默认为true
        Map<Long, HealthCheckInstanceResult> checkResultMap = new LinkedHashMap<>(16);
        checkSuccessModelExist(TableDeployPlatformEnum.KAFKA, (dsId, respList) -> {
            respList.forEach(tableDeployResp -> {
                HealthCheckInstanceResult instanceResult = HealthCheckInstanceResult.createSuccess(tableDeployResp.getId(),
                    tableDeployResp.getName(), TABLE_DEPLOY.code());
                instanceResult.putInstanceInfo("platform", tableDeployResp.getPlatform());
                checkResultMap.put(tableDeployResp.getId(), instanceResult);
            });

            Set<String> topicSet = kafkaAllTopic(dsId);
            if (CollUtil.isEmpty(topicSet)) {
                return;
            }
            respList.forEach(tableDeployResp -> {
                KafkaModelSetting kafkaModelSetting = JsonUtil.decode(tableDeployResp.getSetting(), KafkaModelSetting.class);
                if (!topicSet.contains(kafkaModelSetting.getTopicName())) {
                    HealthCheckInstanceResult instanceResult = checkResultMap.get(tableDeployResp.getId());
                    instanceResult.setExceptionContentAndSolution("模型对应的Kafka-Topic不存在",
                        HealthCheckComponentEnum.KAFKA_TOPIC.resolution());
                }
            });


        });
        // 清空缓存
        topicCache.clear();

        store(context, checkResultMap, HealthCheckComponentEnum.KAFKA_TOPIC.code());

    }


    /**
     * 获取kafka数据源的topic
     *
     * @param dsId
     * @return
     */
    private Set<String> kafkaAllTopic(Long dsId) {
        if (topicCache.containsKey(dsId)) {
            return topicCache.get(dsId);
        }
        try {
            DatasourceResp datasourceResp = datasourceService.get(dsId);
            if (Objects.isNull(datasourceResp)) {
                throw new RuntimeException("数据源不存在");
            }

            KafkaConnection kafkaConnection = BeanUtil.mapToBean(datasourceResp.getParamMap(), KafkaConnection.class, false);
            // 使用telnet检查kafka集群是否可用，Kafka-AdminClient会导致CPU飙升
            List<NodeConnectionResult> connectionResultList = NetworkUtil.checkServerNodeLived(kafkaConnection.getBrokerAddress());
            if (connectionResultList.stream().noneMatch(NodeConnectionResult::getLived)) {
                throw new RuntimeException("kafka集群不可用");
            }

            Set<String> topic = KafkaUtil.getTopicListWithException(kafkaConnection);

            topicCache.put(dsId, topic);
        } catch (Exception e) {
            LOGGER.error("获取kafka数据源：{}的topic失败", dsId, e);
            topicCache.put(dsId, CollUtil.newHashSet());
        }
        return topicCache.get(dsId);
    }


    private void checkEsIndexIsExist(CheckContext context) {
        // 检查结果，默认为true
        Map<Long, HealthCheckInstanceResult> checkResultMap = new LinkedHashMap<>(16);

        checkSuccessModelExist(TableDeployPlatformEnum.ELASTICSEARCH, (dsId, respList) -> {
            respList.forEach(tableDeployResp -> {
                HealthCheckInstanceResult instanceResult = HealthCheckInstanceResult.createSuccess(tableDeployResp.getId(),
                    tableDeployResp.getName(), TABLE_DEPLOY.code());
                instanceResult.putInstanceInfo("platform", tableDeployResp.getPlatform());
                checkResultMap.put(tableDeployResp.getId(), instanceResult);
            });

            respList.forEach(tableDeployResp -> {
                Map<String, AllTemplate> allTemplateMap = allEsTemplate(dsId);
                if (CollUtil.isEmpty(allTemplateMap)) {
                    return;
                }
                if (!allTemplateMap.containsKey(generateTemplateName(tableDeployResp))) {
                    HealthCheckInstanceResult instanceResult = checkResultMap.get(tableDeployResp.getId());
                    instanceResult.setExceptionContentAndSolution("模型对应的Elasticsearch索引模板不存在",
                        HealthCheckComponentEnum.ELASTICSEARCH_INDEX_TEMPLATE.resolution());
                }
            });
        });
        // 清空缓存
        esTemplateCache.clear();

        store(context, checkResultMap, HealthCheckComponentEnum.ELASTICSEARCH_INDEX_TEMPLATE.code());

    }


    private void store(CheckContext context,
                       Map<Long, HealthCheckInstanceResult> checkResultMap,
                       String component) {

        HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
            CheckCategoryEnum.MODEL, component, "物理模型是否存在");

        healthCheckResult.getInstanceList().addAll(checkResultMap.values());

        context.getCheckResultStorage().store(CollUtil.newArrayList(healthCheckResult));
    }


    private String generateTemplateName(TableDeployResp deploy) {
        String name = ElasticsearchConstant.TEMPLATE_NAME_FIX_PREFIX + deploy.getTbName() + "_" + deploy.getTbId();
        // elasticsearch的template名字禁止使用大写字母和特殊字符
        return Common.encodeToSafeString(name.toLowerCase());
    }


    private Map<String, AllTemplate> allEsTemplate(Long dsId) {
        if (esTemplateCache.containsKey(dsId)) {
            return esTemplateCache.get(dsId);
        }
        try {
            DatasourceResp datasourceResp = datasourceService.get(dsId);
            if (Objects.isNull(datasourceResp)) {
                throw new RuntimeException("数据源不存在");
            }

            ElasticsearchConnection elasticsearchConnection = BeanUtil.mapToBean(datasourceResp.getParamMap(),
                ElasticsearchConnection.class, false);
            ElasticsearchClient elasticsearchClient = new ElasticsearchClient(elasticsearchConnection.getAddress(),
                elasticsearchConnection.getUsername(), elasticsearchConnection.getPassword());
            // 获取es所有模板
            Map<String, AllTemplate> allTemplateMap = elasticsearchClient.allTemplate(true);
            esTemplateCache.put(dsId, allTemplateMap);

        } catch (Exception e) {
            LOGGER.error("获取es数据源：{}的template失败", dsId, e);
            esTemplateCache.put(dsId, MapUtil.newHashMap());
        }
        return esTemplateCache.get(dsId);
    }


    private void checkClickHouseTableIsExist(CheckContext context) {
        // 检查结果，默认为true
        Map<Long, HealthCheckInstanceResult> checkResultMap = new LinkedHashMap<>(16);

        checkSuccessModelExist(TableDeployPlatformEnum.CLICKHOUSE, (dsId, respList) -> {
            respList.forEach(tableDeployResp -> {
                HealthCheckInstanceResult instanceResult = HealthCheckInstanceResult.createSuccess(tableDeployResp.getId(),
                    tableDeployResp.getName(), TABLE_DEPLOY.code());
                instanceResult.putInstanceInfo("platform", tableDeployResp.getPlatform());
                checkResultMap.put(tableDeployResp.getId(), instanceResult);
            });

            respList.forEach(tableDeployResp -> {
                ClickhouseModelSetting clickhouseModelSetting = JsonUtil.decode(tableDeployResp.getSetting(), ClickhouseModelSetting.class);
                Map<String, String> tableMap = clickhouseAllTables(dsId, clickhouseModelSetting.getDatabase());
                if (CollUtil.isEmpty(tableMap)) {
                    return;
                }
                if (TableType.IDX.code().equals(tableDeployResp.getTbType())) {
                    if (StrUtil.isNotBlank(tableDeployResp.getIdxTbName())) {
                        checkClickhousePhysicalTableOrViewIsExist(tableDeployResp.getId(),
                            tableDeployResp.getIdxTbName(), tableMap, checkResultMap);
                    }
                    if (StrUtil.isNotBlank(tableDeployResp.getDimTbName())) {
                        checkClickhousePhysicalTableOrViewIsExist(tableDeployResp.getId(),
                            tableDeployResp.getDimTbName(), tableMap, checkResultMap);
                    }
                } else {
                    checkClickhousePhysicalTableOrViewIsExist(tableDeployResp.getId(),
                        tableDeployResp.getTbName(), tableMap, checkResultMap);
                }
            });
        });
        // 清空缓存
        clickhoseTableCache.clear();

        store(context, checkResultMap, HealthCheckComponentEnum.CLICKHOUSE_TABLE_OR_VIEW.code());
    }

    private void checkClickhousePhysicalTableOrViewIsExist(Long id, String tableName, Map<String, String> tableMap,
                                                           Map<Long, HealthCheckInstanceResult> checkResultMap) {
        if (!tableMap.containsKey(tableName)) {
            HealthCheckInstanceResult instanceResult = checkResultMap.get(id);
            instanceResult.setExceptionContentAndSolution("模型对应的Clickhouse表/视图不存在",
                    HealthCheckComponentEnum.CLICKHOUSE_TABLE_OR_VIEW.resolution());
        }
    }


    private Map<String, String> clickhouseAllTables(Long dsId, String database) {
        if (clickhoseTableCache.containsKey(dsId + "-" + database)) {
            return clickhoseTableCache.get(dsId + "-" + database);
        }
        try {
            String sql = String.format("SELECT `name`,`engine`,`engine_all` FROM system.tables WHERE database in ('%s');", database);
            List<Map<String, Object>> maps = clickhouseService.querySql(dsId, sql);
            if (CollUtil.isEmpty(maps)) {
                throw new RuntimeException("获取clickhouse数据源的table失败");
            }
            Map<String, String> tableMap = maps.stream()
                .map(it -> new ImmutablePair<>(it.get("name").toString(), it.get("engine").toString().toLowerCase()))
                .collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight));

            for (Map<String, Object> map : maps) {
                // 存在分布式表，跳过分布式表，对应到本地表引擎
                if (!"Distributed".equalsIgnoreCase(map.get("engine").toString())) {
                    continue;
                }
                String distributedTableName = map.get("name").toString();
                Matcher matcher = ENGINE_PATTERN.matcher(map.get("engine_all").toString());
                if (matcher.find()) {
                    String localTable = matcher.group(1);
                    // 不考虑分布式表和本地表跨database
                    if (tableMap.containsKey(localTable)) {
                        tableMap.put(distributedTableName, tableMap.get(localTable));
                    } else {
                        // 本地表不存在
                        tableMap.remove(distributedTableName);
                    }
                }
            }
            clickhoseTableCache.put(dsId + "-" + database, tableMap);
        } catch (Exception e) {
            LOGGER.error("获取clickhouse数据源：{}的table失败", dsId, e);
            clickhoseTableCache.put(dsId + "-" + database, MapUtil.newHashMap());
        }
        return clickhoseTableCache.get(dsId + "-" + database);
    }


    private void checkMysqlTableIsExist(CheckContext context) {
        // 检查结果，默认为true
        Map<Long, HealthCheckInstanceResult> checkResultMap = new LinkedHashMap<>(16);

        checkSuccessModelExist(TableDeployPlatformEnum.MYSQL, (dsId, respList) -> {
            respList.forEach(tableDeployResp -> {
                HealthCheckInstanceResult instanceResult = HealthCheckInstanceResult.createSuccess(tableDeployResp.getId(),
                    tableDeployResp.getName(), TABLE_DEPLOY.code());
                instanceResult.putInstanceInfo("platform", tableDeployResp.getPlatform());
                checkResultMap.put(tableDeployResp.getId(), instanceResult);
            });

            respList.forEach(tableDeployResp -> {
                Set<String> tableSet = mysqlAllTable(dsId, tableDeployResp.getDeployType());
                if (CollUtil.isEmpty(tableSet)) {
                    return;
                }
                if (!tableSet.contains(tableDeployResp.getTbName())) {
                    HealthCheckInstanceResult instanceResult = checkResultMap.get(tableDeployResp.getId());
                    instanceResult.setExceptionContentAndSolution("模型对应的MySQL表或视图不存在",
                        HealthCheckComponentEnum.MYSQL_TABLE_OR_VIEW.resolution());
                }
            });
        });
        mysqlTableCache.clear();

        store(context, checkResultMap, HealthCheckComponentEnum.MYSQL_TABLE_OR_VIEW.code());
    }

    private Set<String> mysqlAllTable(Long dsId, String type) {
        if (mysqlTableCache.containsKey(dsId + "-" + type)) {
            return mysqlTableCache.get(dsId + "-" + type);
        }
        Connection connection = null;
        try {
            DatasourceResp resp = datasourceService.get(dsId);
            if (Objects.isNull(resp)) {
                throw new RuntimeException("数据源不存在");
            }
            MysqlConnection mysqlConnection = BeanUtil.mapToBean(resp.getParamMap(), MysqlConnection.class, false);
            String database = "default";
            List<String> allGroups = ReUtil.getAllGroups(ConnectionUtil.MysqlDatabase.MYSQL_PATTERN, mysqlConnection.getAddress());
            if (CollUtil.isNotEmpty(allGroups) && allGroups.size() >= 3) {
                database = allGroups.get(2);
            }
            String sql;
            if ("TABLE".equalsIgnoreCase(type)) {
                sql = String.format("SELECT table_name FROM information_schema.tables WHERE TABLE_SCHEMA = '%s'", database);
            } else {
                sql = String.format("SELECT table_name FROM information_schema.views WHERE TABLE_SCHEMA = '%s'", database);
            }
            connection = MysqlUtil.getConnection(mysqlConnection.getAddress(), mysqlConnection.getUsername(),
                mysqlConnection.getPassword());
            List<Map<String, Object>> maps = MysqlUtil.query(connection, sql, null);
            if (CollUtil.isEmpty(maps)) {
                throw new RuntimeException("获取mysql数据源的table失败, database: " + database);
            }
            Set<String> tableSet = maps.stream()
                .map(it -> it.get("table_name").toString().toLowerCase())
                .collect(Collectors.toSet());
            mysqlTableCache.put(dsId + "-" + type, tableSet);
        } catch (Exception e) {
            LOGGER.error("获取mysql数据源：{}的table失败", dsId, e);
            mysqlTableCache.put(dsId + "-" + type, CollUtil.newHashSet());
        } finally {
            IoUtil.close(connection);
        }
        return mysqlTableCache.get(dsId + "-" + type);
    }


    private void checkHiveTableIsExist(CheckContext context) {
        // 检查结果，默认为true
        Map<Long, HealthCheckInstanceResult> checkResultMap = new LinkedHashMap<>(16);

        checkSuccessModelExist(TableDeployPlatformEnum.HIVE, (dsId, respList) -> {
            respList.forEach(tableDeployResp -> {
                HealthCheckInstanceResult instanceResult = HealthCheckInstanceResult.createSuccess(tableDeployResp.getId(),
                    tableDeployResp.getName(), TABLE_DEPLOY.code());
                instanceResult.putInstanceInfo("platform", tableDeployResp.getPlatform());
                checkResultMap.put(tableDeployResp.getId(), instanceResult);
            });

            respList.forEach(tableDeployResp -> {
                HiveModelSetting hiveModelSetting = JsonUtil.decode(tableDeployResp.getSetting(), HiveModelSetting.class);
                Set<String> tableSet = hiveAllTable(dsId, hiveModelSetting.getDatabase());
                if (CollUtil.isEmpty(tableSet)) {
                    return;
                }
                if (!tableSet.contains(tableDeployResp.getTbName())) {
                    HealthCheckInstanceResult instanceResult = checkResultMap.get(tableDeployResp.getId());
                    instanceResult.setExceptionContentAndSolution("模型对应的Hive表或视图不存在",
                        HealthCheckComponentEnum.HIVE_TABLE_OR_VIEW.resolution());
                }
            });
        });
        hiveTableCache.clear();

        store(context, checkResultMap, HealthCheckComponentEnum.HIVE_TABLE_OR_VIEW.code());
    }


    private Set<String> hiveAllTable(Long dsId, String database) {
        if (hiveTableCache.containsKey(dsId)) {
            return hiveTableCache.get(dsId);
        }
        Connection connection = null;
        try {
            DatasourceResp resp = datasourceService.get(dsId);
            HiveConnectionSetting hiveConnection = BeanUtil.mapToBean(resp.getParamMap(), HiveConnectionSetting.class, false);
            String jdbcUrl = "";
            List<String> allGroups = ReUtil.getAllGroups(HIVE_PATTERN, hiveConnection.getHiveJdbcUrl());
            if (CollUtil.isEmpty(allGroups)) {
                throw new RuntimeException("解析hive jdbc url失败");
            }
            if (allGroups.size() >= 2) {
                jdbcUrl = allGroups.get(1) + "/" + database;
            }
            if (allGroups.size() >= 4) {
                jdbcUrl = "?" + allGroups.get(3);
            }

            hiveConnection.setHiveJdbcUrl(jdbcUrl);
            connection = HiveUtil.getConnection(hiveConnection);
            List<Map<String, Object>> showTables = HiveUtil.query(connection, "show tables", null);
            if (CollUtil.isEmpty(showTables)) {
                throw new RuntimeException("获取hive数据源的table失败");
            }
            Set<String> tableSet = showTables.stream()
                .map(it -> it.get("tab_name").toString().toLowerCase())
                .collect(Collectors.toSet());
            hiveTableCache.put(dsId, tableSet);
        } catch (Exception e) {
            LOGGER.error("获取hive数据源：{}的table失败", dsId, e);
            hiveTableCache.put(dsId, CollUtil.newHashSet());
        } finally {
            // 关闭连接
            IoUtil.close(connection);
        }
        return hiveTableCache.get(dsId);
    }

    private void checkNebulaTagOrEdgeIsExist(CheckContext context) {
        // 检查结果，默认为true
        Map<Long, HealthCheckInstanceResult> checkResultMap = new LinkedHashMap<>(16);

        checkSuccessModelExist(TableDeployPlatformEnum.NEBULA, (dsId, respList) -> {
            respList.forEach(tableDeployResp -> {
                HealthCheckInstanceResult instanceResult = HealthCheckInstanceResult.createSuccess(tableDeployResp.getId(),
                    tableDeployResp.getName(), TABLE_DEPLOY.code());
                instanceResult.putInstanceInfo("platform", tableDeployResp.getPlatform());
                checkResultMap.put(tableDeployResp.getId(), instanceResult);
            });

            respList.forEach(tableDeployResp -> {
                NebulaModelSetting nebulaModelSetting = JsonUtil.decode(tableDeployResp.getSetting(), NebulaModelSetting.class);
                Set<String> tableSet = nebulaAllTagOrEdge(dsId, nebulaModelSetting.getSpace(), tableDeployResp.getDeployType());
                if (CollUtil.isEmpty(tableSet)) {
                    return;
                }
                if (!tableSet.contains(tableDeployResp.getTbName())) {
                    HealthCheckInstanceResult instanceResult = checkResultMap.get(tableDeployResp.getId());
                    instanceResult.setExceptionContentAndSolution("模型对应的Nebula的Tag或Edge不存在",
                        HealthCheckComponentEnum.NEBULA_TAG_OR_EDGE.resolution());
                }
            });
        });
        // 清空缓存
        nebulaCache.clear();

        store(context, checkResultMap, HealthCheckComponentEnum.NEBULA_TAG_OR_EDGE.code());
    }

    private Set<String> nebulaAllTagOrEdge(Long dsId, String space, String type) {
        if (nebulaCache.containsKey(dsId + "-" + type)) {
            return nebulaCache.get(dsId + "-" + type);
        }
        NebulaClient nebulaClient = null;
        try {
            DatasourceResp resp = datasourceService.get(dsId);
            if (Objects.isNull(resp)) {
                throw new RuntimeException("数据源不存在");
            }
            NebulaConnection nebulaConnection = BeanUtil.mapToBean(resp.getParamMap(), NebulaConnection.class, false);
            nebulaClient = new NebulaClient(nebulaConnection.getAddress(), nebulaConnection.getUsername(), nebulaConnection.getPassword());
            List<String> tagOrEdgeSet = nebulaClient.showAllTagOrEdge(space, type);
            nebulaCache.put(dsId + "-" + type, new HashSet<>(tagOrEdgeSet));
        } catch (Exception e) {
            LOGGER.error("获取nebula数据源：{}的tag/edge失败", dsId, e);
            nebulaCache.put(dsId + "-" + type, CollUtil.newHashSet());
        } finally {
            IoUtil.close(nebulaClient);
        }
        return nebulaCache.get(dsId + "-" + type);
    }


    private void checkSuccessModelExist(TableDeployPlatformEnum platform,
                                        BiConsumer<Long, List<TableDeployResp>> consumer) {
        TableDeployModelQueryFilterReq filter = new TableDeployModelQueryFilterReq();
        filter.setDeployPlatform(platform.code());
        filter.setDeployStatus(TableDeployStatusEnum.SUCCESS.code());
        for (int i = 0; ; i++) {
            TableDeployModelQueryReq req = new TableDeployModelQueryReq();
            req.setPage(i);
            req.setSize(500);
            req.setFilter(filter);
            Paged<TableDeployResp> paged = tableDeployService.queryByCondition(req);
            if (CollUtil.isEmpty(paged.getList())) {
                break;
            }
            // 根据数据源分类
            Map<Long, List<TableDeployResp>> datasourceRespMap =
                paged.getList().stream().collect(Collectors.groupingBy(TableDeployResp::getDsId));
            datasourceRespMap.forEach(consumer);
        }
    }


    private void clear() {
        topicCache.clear();
        esTemplateCache.clear();
        clickhoseTableCache.clear();
        mysqlTableCache.clear();
        nebulaCache.clear();
        hiveTableCache.clear();
    }
}
