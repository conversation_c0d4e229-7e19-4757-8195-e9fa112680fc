package com.eoi.jax.web.monitor.model.healthcheckinstance;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbHealthCheckInstance;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
public class HealthCheckInstanceQueryFilterReq implements IFilterReq<TbHealthCheckInstance> {

    /**
     * 类别
     */
    private Long itemId;

    /**
     * 状态
     */
    private String status;


    @Override
    public QueryWrapper<TbHealthCheckInstance> where(QueryWrapper<TbHealthCheckInstance> wrapper) {
        wrapper.lambda().eq(Objects.nonNull(itemId), TbHealthCheckInstance::getItemId, itemId)
            .eq(StrUtil.isNotBlank(status), TbHealthCheckInstance::getStatus, status);
        return wrapper;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
