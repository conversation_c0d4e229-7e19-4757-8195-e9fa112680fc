package com.eoi.jax.web.monitor.model.collector;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
public class NodeConnectionResult {

    private String ip;

    private Integer port;

    private Boolean lived;

    public NodeConnectionResult() {
    }

    public NodeConnectionResult(String ip, Integer port, Boolean lived) {
        this.ip = ip;
        this.port = port;
        this.lived = lived;
    }

    public String getServer() {
        if (Objects.isNull(port)) {
            return ip;
        }
        return ip + ":" + port;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Boolean getLived() {
        return lived;
    }

    public void setLived(<PERSON><PERSON><PERSON> lived) {
        this.lived = lived;
    }

    @Override
    public String toString() {
        return "NodeConnectionResult{" +
            "ip='" + ip + '\'' +
            ", port=" + port +
            ", lived=" + lived +
            '}';
    }
}
