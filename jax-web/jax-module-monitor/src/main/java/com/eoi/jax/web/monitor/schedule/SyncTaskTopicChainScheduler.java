package com.eoi.jax.web.monitor.schedule;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.enumrate.PipelineStatusEnum;
import com.eoi.jax.web.core.common.enumrate.StorageClusterStatusEnum;
import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.monitor.enumrate.KafkaJobNameRoleEnum;
import com.eoi.jax.web.monitor.model.kafkalag.KafkaTaskRelation;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.service.TbStorageCkHistoryService;
import com.eoi.jax.web.repository.service.TbStorageHiveHistoryService;
import com.eoi.jax.web.repository.service.TbStorageHiveService;
import com.eoi.jax.web.repository.service.TbTaskTopicChainService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import groovy.lang.Tuple4;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 定期发现各类任务中的kafka topic 生产消费关系
 *
 * @Author: yaru.ma
 * @Date: 2024/3/27
 */
@Component
public class SyncTaskTopicChainScheduler {

    @Autowired
    private JaxRepository jaxRepository;
    @Autowired
    private TbStorageHiveService storageHiveService;
    @Autowired
    private TbStorageHiveHistoryService storageHiveHistoryService;
    @Autowired
    private TbStorageCkHistoryService storageCkHistoryService;
    @Autowired
    private TbTaskTopicChainService taskTopicChainService;

    @XxlJob("syncTaskTopicChain")
    public void syncKafkaRelation() {
        List<KafkaTaskRelation> kafkaTaskRelations = new LinkedList<>();
        // 管线作业
        getTopicRelationInPipeline(kafkaTaskRelations);
        // HIVE存储任务
        getTopicRelationInStorageHive(kafkaTaskRelations);
        // CK存储任务
        getTopicRelationInStorageCk(kafkaTaskRelations);
        // model信息
        Map<Long, TbTable> modelIdMap = jaxRepository.table().list().stream().collect(Collectors.toMap(TbTable::getId, x -> x));
        kafkaTaskRelations.stream().filter(x -> ObjectUtil.isNotNull(x.getTbId()))
                .forEach(x -> {
                    TbTable table = modelIdMap.get(x.getTbId());
                    if (table != null) {
                        x.setTopic(table.getTbName());
                        x.setTbName(table.getTbAlias());
                    }
                });
        XxlJobHelper.log("使用模型的任务占比{}/{}个",
                kafkaTaskRelations.stream().filter(x -> ObjectUtil.isNotNull(x.getTbId())).count(),
                kafkaTaskRelations.size());
        // 有无重复消费
        Boolean validated = validateRelation(kafkaTaskRelations);
        // 查历史数据
        List<TbTaskTopicChain> existsRelations = taskTopicChainService.list();
        Map<Tuple4<String, String, String, String>, Long> existIdMap = existsRelations.stream()
                .collect(Collectors.toMap(this::getTuple, TbTaskTopicChain::getId, (x, y) -> x));
        List<TbTaskTopicChain> updateRows = kafkaTaskRelations.stream()
                .peek(x -> x.setId(existIdMap.get(getTuple(x))))
                .filter(x -> x.getId() != null)
                .map(KafkaTaskRelation::toEntity)
                .collect(Collectors.toList());
        XxlJobHelper.log("发生更新{}条", updateRows.size());
        taskTopicChainService.updateBatchById(updateRows);
        List<TbTaskTopicChain> insertRows = kafkaTaskRelations.stream()
                .filter(x -> x.getId() == null)
                .peek(x -> x.setId(IdUtil.genId()))
                .map(KafkaTaskRelation::toEntity)
                .collect(Collectors.toList());
        XxlJobHelper.log("新增{}条", insertRows.size());
        taskTopicChainService.saveBatch(insertRows);
        if (validated) {
            XxlJobHelper.handleSuccess();
        } else {
            XxlJobHelper.handleFail("部分关联关系未通过校验");
        }

    }

    private void getTopicRelationInStorageCk(List<KafkaTaskRelation> kafkaTaskRelations) {
        List<Long> storageCkIds = jaxRepository.storageCkService().list(new LambdaQueryWrapper<TbStorageCk>()
                        .ne(TbStorageCk::getStatus, StorageClusterStatusEnum.STOPPED.code())
                        .ne(TbStorageCk::getStatus, StorageClusterStatusEnum.UNSTART.code()))
                .stream().map(TbStorageCk::getId).collect(Collectors.toList());
        Map<Long, String> clusterMap = jaxRepository.storageClusterService().list().stream()
                .collect(Collectors.toMap(TbStorageCluster::getId, TbStorageCluster::getName));
        List<TbStorageCkHistory> lastDeployList = storageCkHistoryService.selectLastDeployList();
        List<KafkaTaskRelation> ckStorageRelations = lastDeployList.stream()
                .filter(x -> storageCkIds.contains(x.getStorageCkId()))
                .map(x -> KafkaTaskRelation.getRoleInCkStorage(x, clusterMap.get(x.getStorageClusterId())))
                .collect(Collectors.toList());
        kafkaTaskRelations.addAll(ckStorageRelations);
        XxlJobHelper.log("未停止状态的ck存储任务共检测到{}个", ckStorageRelations.size());
    }

    private void getTopicRelationInStorageHive(List<KafkaTaskRelation> kafkaTaskRelations) {
        List<Long> hiveProcessIds = storageHiveService.list(new LambdaQueryWrapper<TbStorageHive>()
                        .ne(TbStorageHive::getStatus, StorageClusterStatusEnum.UNSTART.code())
                        .ne(TbStorageHive::getStatus, StorageClusterStatusEnum.STOPPED.code()))
                .stream().map(TbStorageHive::getId).collect(Collectors.toList());
        List<TbStorageHiveHistory> latestHiveList = storageHiveHistoryService.selectLastDeployList();
        List<KafkaTaskRelation> hiveRelations = latestHiveList.stream()
                .filter(x -> hiveProcessIds.contains(x.getStorageHiveId()))
                .map(x -> KafkaTaskRelation.getRoleInHiveStorage(x))
                .collect(Collectors.toList());
        kafkaTaskRelations.addAll(hiveRelations);
        XxlJobHelper.log("未停止状态的hive存储任务共检测到{}个", hiveRelations.size());
    }

    private void getTopicRelationInPipeline(List<KafkaTaskRelation> kafkaTaskRelations) {
        // tb_pipeline包含es存储任务,管线作业
        List<TbPipeline> pipelines = jaxRepository.pipeline().list(new LambdaQueryWrapper<TbPipeline>()
                        .ne(TbPipeline::getPipelineStatus, PipelineStatusEnum.STOPPED.code())
                        .ne(TbPipeline::getProcessType, "HIVE"))
                .stream().collect(Collectors.toList());
        pipelines.forEach(pipeline -> {
            PipelineConfig pipelineConfig = PipelineConfig.fromString(pipeline.getPipelineConfig());
            pipelineConfig.getJobs().forEach(job -> {
                KafkaJobNameRoleEnum roleEnum = KafkaJobNameRoleEnum.fromString(job.getJobName());
                if (roleEnum != null) {
                    kafkaTaskRelations.addAll(KafkaTaskRelation.getRoleInPipeline(pipeline, job, roleEnum));
                }
            });
        });
        XxlJobHelper.log("未停止状态的管线作业(包含es存储任务)共检测到{}个,topic设置共{}个",
                pipelines.size(), kafkaTaskRelations.size());
    }

    private Boolean validateRelation(List<KafkaTaskRelation> kafkaTaskRelations) {
        List<KafkaTaskRelation> removes = kafkaTaskRelations.stream()
                .filter(x -> ObjectUtil.hasEmpty(x.getTaskName(), x.getTaskType(), x.getTopic(), x.getRoleType()))
                .filter(x -> ObjectUtil.isNotNull(x.getConsumerGroup()))
                .peek(x -> XxlJobHelper.log("校验失败:{}", JsonUtil.encode(x)))
                .collect(Collectors.toList());
        kafkaTaskRelations.removeAll(removes);
        // pipeline的topic消费 可能存在重复,仅保留一个
        List<List<KafkaTaskRelation>> duplicated = kafkaTaskRelations.stream()
                .collect(Collectors.groupingBy(this::getTuple))
                .values().stream().filter(x -> x.size() > 1)
                .peek(x -> XxlJobHelper.log("关联任务关系重复:{}", JsonUtil.encode(x)))
                .collect(Collectors.toList());
        duplicated.forEach(d -> {
            d.remove(0);
            kafkaTaskRelations.removeAll(d);
        });
        return removes.isEmpty();

    }

    private Tuple4<String, String, String, String> getTuple(KafkaTaskRelation x) {
        return Tuple4.tuple(x.getTaskName(), x.getTopic(), x.getTaskType(), x.getRoleType());
    }


    private Tuple4<String, String, String, String> getTuple(TbTaskTopicChain x) {
        return Tuple4.tuple(x.getTaskName(), x.getTopic(), x.getTaskType(), x.getRoleType());
    }

}
