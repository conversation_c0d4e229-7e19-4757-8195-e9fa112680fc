package com.eoi.jax.web.monitor.contorller;

import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpParameters;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.monitor.model.selfmonitor.object.SelfMonitorObjectCreateReq;
import com.eoi.jax.web.monitor.model.selfmonitor.object.SelfMonitorObjectResp;
import com.eoi.jax.web.monitor.model.selfmonitor.object.SelfMonitorObjectUpdateReq;
import com.eoi.jax.web.monitor.service.SelfMonitorObjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: yaru.ma
 * @Date: 2024/3/15
 */
@RestController
public class SelfMonitorObjectController implements V2Controller {

    @Autowired
    private SelfMonitorObjectService selfMonitorObjectService;

    @Operation(summary = "查询自监控树")
    @GetMapping("self-monitor/object/tree")
    public Response<List<SelfMonitorObjectResp>> get() {
        return Response.success(selfMonitorObjectService.listTree());
    }


    @Operation(summary = "创建自监控项")
    @PostMapping("self-monitor/object")
    @AuditLog(category = "运维排障", opAction = OpActionEnum.CREATE, module = "告警中心",
            function = "规则管理", code = "self-monitor-object")
    public Response create(@OpParameters @RequestBody SelfMonitorObjectCreateReq req) {
        return Response.success(selfMonitorObjectService.create(req));
    }

    @Operation(summary = "更新自监控项")
    @PutMapping("self-monitor/object/{id}")
    @AuditLog(category = "运维排障", opAction = OpActionEnum.UPDATE, module = "告警中心",
            function = "规则管理", code = "self-monitor-object")
    public Response update(
            @OpPrimaryKey @Parameter(description = "数据域主键id", required = true) @PathVariable("id") Long id,
            @OpParameters @RequestBody SelfMonitorObjectUpdateReq req
    ) {
        req.setId(id);
        return Response.success(selfMonitorObjectService.update(req));
    }

    @Operation(summary = "删除自监控项")
    @DeleteMapping("self-monitor/object/{id}")
    @AuditLog(category = "运维排障", opAction = OpActionEnum.DELETE, module = "告警中心",
            function = "规则管理", code = "self-monitor-object")
    public Response delete(
            @OpPrimaryKey @OpParameters @Parameter(description = "数据域主键id", required = true) @PathVariable("id") Long id
    ) {
        return Response.success(selfMonitorObjectService.delete(id));
    }

    @Operation(summary = "所有自监控项(包括分类)")
    @GetMapping("self-monitor/object/list")
    public Response listAll() {
        return Response.success(selfMonitorObjectService.all());
    }

}
