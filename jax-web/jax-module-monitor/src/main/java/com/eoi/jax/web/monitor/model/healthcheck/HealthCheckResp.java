package com.eoi.jax.web.monitor.model.healthcheck;

import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.repository.entity.TbHealthCheck;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
public class HealthCheckResp {

    @Schema(description = "id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long id;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    @Schema(description = "运行类型")
    private String runType;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建人id
     */
    private Long createUser;

    /**
     * 更新人id
     */
    private Long updateUser;

    @Schema(description = "消耗时间,单位秒")
    private Long consumeSec;

    /**
     * 检查项计数
     */
    @Schema(description = "检查项计数")
    private Long checkItemNum;

    /**
     * 实例计数
     */
    @Schema(description = "实例计数")
    private Long instanceNum;

    /**
     * 正常计数
     */
    @Schema(description = "正常计数")
    private Long normalNum;

    /**
     * 建议计数
     */
    @Schema(description = "建议计数")
    private Long suggestionNum;

    /**
     * 异常计数
     */
    @Schema(description = "异常计数")
    private Long exceptionNum;

    @Schema(description = "组件检查")
    private List<HealthCheckItemResp> componentCheck;

    @Schema(description = "作业检查")
    private List<HealthCheckItemResp> jobCheck;

    @Schema(description = "数据检查")
    private List<HealthCheckItemResp> dataCheck;

    @Schema(description = "模型检查")
    private List<HealthCheckItemResp> modelCheck;

    @Schema(description = "配置检查")
    private List<HealthCheckItemResp> configCheck;

    @Schema(description = "cron表达式")
    private String cron;

    @Schema(description = "是否开启定时检查")
    private Boolean enable;

    public HealthCheckResp fromEntity(TbHealthCheck tbHealthCheck) {
        HealthCheckResp healthCheckResp = ModelBeanUtil.copyBean(tbHealthCheck, this);
        long time = Optional.ofNullable(tbHealthCheck.getEndTime()).map(Date::getTime).orElse(System.currentTimeMillis());
        healthCheckResp.setConsumeSec((time - tbHealthCheck.getStartTime().getTime()) / 1000);
        return healthCheckResp;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRunType() {
        return runType;
    }

    public void setRunType(String runType) {
        this.runType = runType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Long getConsumeSec() {
        return consumeSec;
    }

    public void setConsumeSec(Long consumeSec) {
        this.consumeSec = consumeSec;
    }

    public Long getCheckItemNum() {
        return checkItemNum;
    }

    public void setCheckItemNum(Long checkItemNum) {
        this.checkItemNum = checkItemNum;
    }

    public Long getInstanceNum() {
        return instanceNum;
    }

    public void setInstanceNum(Long instanceNum) {
        this.instanceNum = instanceNum;
    }

    public Long getNormalNum() {
        return normalNum;
    }

    public void setNormalNum(Long normalNum) {
        this.normalNum = normalNum;
    }

    public Long getSuggestionNum() {
        return suggestionNum;
    }

    public void setSuggestionNum(Long suggestionNum) {
        this.suggestionNum = suggestionNum;
    }

    public Long getExceptionNum() {
        return exceptionNum;
    }

    public void setExceptionNum(Long exceptionNum) {
        this.exceptionNum = exceptionNum;
    }

    public List<HealthCheckItemResp> getComponentCheck() {
        return componentCheck;
    }

    public void setComponentCheck(List<HealthCheckItemResp> componentCheck) {
        this.componentCheck = componentCheck;
    }

    public List<HealthCheckItemResp> getJobCheck() {
        return jobCheck;
    }

    public void setJobCheck(List<HealthCheckItemResp> jobCheck) {
        this.jobCheck = jobCheck;
    }

    public List<HealthCheckItemResp> getDataCheck() {
        return dataCheck;
    }

    public void setDataCheck(List<HealthCheckItemResp> dataCheck) {
        this.dataCheck = dataCheck;
    }

    public List<HealthCheckItemResp> getModelCheck() {
        return modelCheck;
    }

    public void setModelCheck(List<HealthCheckItemResp> modelCheck) {
        this.modelCheck = modelCheck;
    }

    public List<HealthCheckItemResp> getConfigCheck() {
        return configCheck;
    }

    public void setConfigCheck(List<HealthCheckItemResp> configCheck) {
        this.configCheck = configCheck;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
