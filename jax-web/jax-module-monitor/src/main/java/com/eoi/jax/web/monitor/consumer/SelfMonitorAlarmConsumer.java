package com.eoi.jax.web.monitor.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.config.SelfMonitorAlarmConfig;
import com.eoi.jax.web.ingestion.factory.datasource.model.KafkaConnection;
import com.eoi.jax.web.ingestion.util.KafkaUtil;
import com.eoi.jax.web.monitor.consumer.thread.SelfMonitorAlarmDealParam;
import com.eoi.jax.web.monitor.consumer.thread.SelfMonitorConsumerThread;
import com.eoi.jax.web.monitor.plugin.AlarmObjectQueryHelper;
import com.eoi.monitor.model.monitor.MonitorAlarm;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

public class SelfMonitorAlarmConsumer extends Thread {
    private static final Logger LOGGER = LoggerFactory.getLogger(SelfMonitorAlarmConsumer.class);

    private volatile Boolean isRunning;
    private String brokers;
    private String topic;
    private String groupName;
    private KafkaConnection kafkaConnection;
    private AlarmObjectQueryHelper alarmObjectQueryHelper;
    private Integer maxThreadSize;
    private Integer queueCapacity;

    private Map<Integer, SelfMonitorConsumerThread> selfMonitorAlertDealThreadMap = new HashMap<>();

    public SelfMonitorAlarmConsumer(Map<String, Object> configMap,
                                    AlarmObjectQueryHelper alarmObjectQueryHelper) {
        SelfMonitorAlarmConfig alarmConfig = BeanUtil.mapToBean(configMap, SelfMonitorAlarmConfig.class, true);
        this.isRunning = true;
        this.kafkaConnection = BeanUtil.mapToBean(configMap, KafkaConnection.class, true);
        this.brokers = kafkaConnection.getBrokerAddress();
        this.topic = alarmConfig.getTopic();
        this.groupName = alarmConfig.getGroup();
        this.alarmObjectQueryHelper = alarmObjectQueryHelper;
        this.queueCapacity = Math.max(alarmConfig.getQueueCapacity(), 1);
        this.maxThreadSize = Math.max(alarmConfig.getMaxThreadSize(), 1);
        initAlarmConsumerThread();
    }

    private void initAlarmConsumerThread() {
        for (int i = 0; i < maxThreadSize; i++) {
            String threadName = "self-monitor-consumer-" + String.format("%03d", i);
            LinkedBlockingQueue blockingQueue = new LinkedBlockingQueue(queueCapacity);
            SelfMonitorConsumerThread thread = new SelfMonitorConsumerThread(threadName);
            thread.setBlockingQueue(blockingQueue);
            selfMonitorAlertDealThreadMap.put(i, thread);
            thread.start();
            LOGGER.warn(" 告警消费线程[{}]启动了", threadName);
        }
    }

    @Override
    public void run() {
        KafkaConsumer<String, String> consumer = KafkaUtil.getConsumer(brokers, groupName, kafkaConnection);
        consumer.subscribe(Arrays.asList(topic));
        while (isRunning) {
            try {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(2000));
                if (records == null || records.isEmpty()) {
                    continue;
                }
                LOGGER.debug("获取到告警信息数量:{}", records.count());
                // 更新缓存
                Map<String, Object> alarmObjectCache = alarmObjectQueryHelper.getObjectCache();
                for (ConsumerRecord<String, String> record : records) {
                    try {
                        MonitorAlarm alarm = null;
                        try {
                            alarm = JSONUtil.toBean(record.value(), MonitorAlarm.class);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                        if (alarm == null) {
                            LOGGER.error("处理告警信息异常, 接入数据有误：{}", record.value());
                            return;
                        }
                        // 计算出对应的下标值
                        // 默认线程随机
                        int hash = Math.abs(alarm.hashCode() % maxThreadSize);
                        if (alarm.getId() != null) {
                            hash = Math.abs(alarm.getId().hashCode() % maxThreadSize);
                        } else if (alarm.getRuleId() != null) {
                            hash = Math.abs(alarm.getRuleId().hashCode() % maxThreadSize);
                        } else if (CollUtil.isNotEmpty(alarm.getLabels())) {
                            hash = Math.abs(JsonUtil.encode(alarm.getLabels()).hashCode() % maxThreadSize);
                        }

                        // 获取到线程
                        SelfMonitorConsumerThread thread = selfMonitorAlertDealThreadMap.get(hash);

                        if (!thread.isAlive()) {
                            thread.setManualExit(true);
                            thread = dealThreadCopy(hash, thread);
                        }
                        while (thread.getBlockingQueue().remainingCapacity() < 1 && queueCapacity > 1) {
                            LOGGER.debug("线程[{}]队列剩余容量: {}", thread.getName(), thread.getBlockingQueue().remainingCapacity());
                            TimeUnit.MILLISECONDS.sleep(100);
                        }
                        SelfMonitorAlarmDealParam param = new SelfMonitorAlarmDealParam(alarm, alarmObjectQueryHelper, alarmObjectCache);
                        param.setOffset(record.offset());
                        param.setPartition(record.partition());

                        // 将数据塞入线程对应的队列
                        boolean bl = thread.getBlockingQueue().offer(param, 2, TimeUnit.SECONDS);
                        int i = 1;
                        while (!bl) {
                            try {
                                LOGGER.warn("线程[{}]数据装入队列失败, 尝试第[{}]次重新装入, 队列剩余容量: {}, 数据: {}", thread.getName(),
                                        i,
                                        thread.getBlockingQueue().remainingCapacity(),
                                        JsonUtil.encode(param));
                                bl = thread.getBlockingQueue().offer(param, 2, TimeUnit.SECONDS);
                                TimeUnit.MILLISECONDS.sleep(100);
                                i++;
                            } catch (InterruptedException e) {
                                LOGGER.error("数据装入线程池失败:{}, {}", e.getMessage(), e);
                            }
                        }
                    } catch (Exception e) {
                        LOGGER.error("处理告警消息失败： {} {}", e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("拉取告警topic消息失败,可能是连接kafka异常了: {} ", e.getMessage(), e);
            }
        }
        consumer.close();
        selfMonitorAlertDealThreadMap.values().forEach(t -> t.setPeaceExit(true));
    }

    /**
     * 如果处理现场可能挂掉了那么需要新开线程补充以及消费积累数据
     *
     * @param threadIdx
     * @param thread
     * @return
     */
    public SelfMonitorConsumerThread dealThreadCopy(int threadIdx, SelfMonitorConsumerThread thread) {

        String threadName = "self-monitor-consumer-" + String.format("%03d", threadIdx);
        LOGGER.warn("线程[{}]可能已经中断 无法处理任务,剩余任务数:{}", thread.getName(), thread.getBlockingQueue().size());
        SelfMonitorConsumerThread threadNew = new SelfMonitorConsumerThread(thread.getName());
        threadNew.setBlockingQueue(thread.getBlockingQueue());
        threadNew.setSequence(thread.getSequence());
        selfMonitorAlertDealThreadMap.put(threadIdx, threadNew);
        threadNew.start();
        LOGGER.warn(" 告警消费线程[{}]重启了", threadName);
        return threadNew;
    }

    public void close() {
        isRunning = false;
    }

    public static Boolean checkAlarmConfig(Map<String, Object> configMap) {
        try {
            SelfMonitorAlarmConfig alarmConfig = BeanUtil.mapToBean(configMap, SelfMonitorAlarmConfig.class, true);
            Assert.notBlank(alarmConfig.getTopic(), "kafka topic不能为空");
            Assert.notBlank(alarmConfig.getGroup(), "kafka 消费组不能为空");
            Assert.notBlank(alarmConfig.getBrokerAddress(), "kafka broker连接地址不能为空");
        } catch (Exception e) {
            LOGGER.warn("自监控告警配置项不完整:{},{}", JsonUtil.encode(configMap), e.getMessage());
            return false;
        }
        return true;
    }

}
