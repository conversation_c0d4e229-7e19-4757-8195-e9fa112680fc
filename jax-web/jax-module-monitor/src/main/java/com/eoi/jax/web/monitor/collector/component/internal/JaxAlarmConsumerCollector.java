package com.eoi.jax.web.monitor.collector.component.internal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.exception.KafkaApiException;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.config.config.SelfMonitorAlarmConfig;
import com.eoi.jax.web.ingestion.factory.datasource.model.KafkaConnection;
import com.eoi.jax.web.ingestion.util.KafkaUtil;
import com.eoi.jax.web.monitor.collector.AbstractCollector;
import com.eoi.jax.web.monitor.enumrate.CollectCommand;
import com.eoi.jax.web.monitor.model.collector.CollectContext;
import com.eoi.jax.web.monitor.model.collector.Metric;
import com.eoi.jax.web.monitor.model.collector.NodeConnectionResult;
import com.eoi.jax.web.monitor.util.NetworkUtil;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.common.TopicPartitionInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_ALARM_CONSUMER_LIVED;

@Service
public class JaxAlarmConsumerCollector extends AbstractCollector {

    @Autowired
    private SystemConfigHolder systemConfigHolder;

    @Override
    public void initCollectFunction(Map<CollectCommand, Consumer<CollectContext>> localFnMap) {
        localFnMap.put(CollectCommand.LIVED, this::collectConsumerLivedMetric);
    }

    private void collectConsumerLivedMetric(CollectContext collectContext) {
        SelfMonitorAlarmConfig alarmConfig = systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_ALARM);
        if (Objects.isNull(alarmConfig) || StrUtil.hasBlank(alarmConfig.getBrokerAddress(),
            alarmConfig.getGroup(), alarmConfig.getTopic())) {
            return;
        }
        List<Metric> metricList = new LinkedList<>();
        metricList.addAll(collectConsumerLivedMetric());
        collectContext.getExporter().export(metricList);
    }

    /**
     * 使用lag延迟
     *
     * @return
     */
    private List<Metric> collectConsumerLivedMetric() {
        List<Metric> metricList = new LinkedList<>();
        SelfMonitorAlarmConfig kafkaConfig = systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_ALARM);
        if (!Optional.ofNullable(kafkaConfig).map(SelfMonitorAlarmConfig::getEnable).orElse(false)) {
            return metricList;
        }
        Map<String, Object> config = systemConfigHolder.getConfig(SystemConfigEnum.SELF_MONITOR_ALARM.getKey());
        String topicName = kafkaConfig.getTopic();
        KafkaConnection kafkaConnection = BeanUtil.mapToBean(config, KafkaConnection.class, true);
        Set<String> topic = new HashSet<>();
        topic.add(topicName);
        Long lag = null;
        Map<String, Object> labelMap = new LinkedHashMap<>();
        Map<String, TopicDescription> topics = null;
        labelMap.put("brokerAddress", kafkaConfig.getBrokerAddress());
        labelMap.put("consumergroup", kafkaConfig.getGroup());
        try {
            // 确认kakfa正常
            if (!NetworkUtil.checkServerNodeLived(kafkaConnection.getBrokerAddress())
                    .stream().anyMatch(NodeConnectionResult::getLived)) {
                throw new KafkaApiException(ResponseCode.DATASOURCE_KAFKA_API_FAILED.getCode(), "kafka节点均不存在");
            }
            topics = KafkaUtil.describeTopics(kafkaConnection, topic);
            // topic未创建
            if (!topics.containsKey(topicName)) {
                return metricList;
            }
            lag = KafkaUtil.getConsumerLag(kafkaConnection.getBrokerAddress(), topicName,
                    kafkaConfig.getGroup(), kafkaConnection);
        } catch (KafkaApiException e) {
            metricList.add(new Metric(JAX_ALARM_CONSUMER_LIVED.code(), labelMap, 0));
            return metricList;
        }
        labelMap.put("kafka_consumergroup_lag", lag);
        labelMap.put("topic", topicName);
        if (lag <= 0) {
            metricList.add(new Metric(JAX_ALARM_CONSUMER_LIVED.code(), labelMap, 1));
        } else {
            List<Integer> partitions = topics.get(topicName).partitions().stream()
                    .map(TopicPartitionInfo::partition).collect(Collectors.toList());
            long topicOffset = KafkaUtil.getTopicOffset(topicName, partitions, kafkaConnection, null);
            // 5min,不支持调整
            Long ts = System.currentTimeMillis() - 5 * 60000L;
            long topicOffset5min = KafkaUtil.getTopicOffset(topicName, partitions, kafkaConnection, ts);
            long deltaOffset = topicOffset - topicOffset5min;
            labelMap.put("kafka_alarm_topic_offset_5_min", deltaOffset);
            metricList.add(new Metric(JAX_ALARM_CONSUMER_LIVED.code(), labelMap, lag > deltaOffset ? 0 : 1));
        }
        return metricList;
    }
}
