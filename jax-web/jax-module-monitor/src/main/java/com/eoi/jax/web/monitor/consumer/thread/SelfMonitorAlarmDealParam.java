package com.eoi.jax.web.monitor.consumer.thread;

import com.eoi.jax.web.monitor.plugin.AlarmObjectQueryHelper;
import com.eoi.jax.web.repository.entity.TbSelfMonitorAlarmRule;
import com.eoi.monitor.model.monitor.MonitorAlarm;
import groovy.lang.Tuple2;

import java.util.Map;

/**
 * @Author: yaru.ma
 * @Date: 2024/3/22
 */
public class SelfMonitorAlarmDealParam {

    private MonitorAlarm alarm;
    private Map<String, Long> pipelineAppIdCache;
    private Map<Long, Long> pipelineProcessIdCache;
    private Map<Tuple2, Long> consumerPipeline;
    private Map<Long, TbSelfMonitorAlarmRule> selfMonitorRule;
    private Map<Tuple2, Long> ckStorageConsumerMap;
    private int partition;
    private Long offset;
    private Boolean needSendNotify;

    public SelfMonitorAlarmDealParam(MonitorAlarm alarm, AlarmObjectQueryHelper alarmObjectQueryHelper,
                                     Map<String, Object> alarmObjectCache) {
        this.alarm = alarm;
        pipelineAppIdCache = alarmObjectQueryHelper.getPipelineAppIdMap(alarmObjectCache);
        consumerPipeline = alarmObjectQueryHelper.getPipelineConsumer(alarmObjectCache);
        selfMonitorRule = alarmObjectQueryHelper.getSelfMonitorRule(alarmObjectCache);
        ckStorageConsumerMap = alarmObjectQueryHelper.getCkStorageConsumer(alarmObjectCache);
        pipelineProcessIdCache = alarmObjectQueryHelper.getPipelineProcessIdMap(alarmObjectCache);
    }

    public SelfMonitorAlarmDealParam() {
    }

    public MonitorAlarm getAlarm() {
        return alarm;
    }

    public void setAlarm(MonitorAlarm alarm) {
        this.alarm = alarm;
    }

    public Map<String, Long> getPipelineAppIdCache() {
        return pipelineAppIdCache;
    }

    public void setPipelineAppIdCache(Map<String, Long> pipelineAppIdCache) {
        this.pipelineAppIdCache = pipelineAppIdCache;
    }

    public Map<Long, Long> getPipelineProcessIdCache() {
        return pipelineProcessIdCache;
    }

    public void setPipelineProcessIdCache(Map<Long, Long> pipelineProcessIdCache) {
        this.pipelineProcessIdCache = pipelineProcessIdCache;
    }

    public Map<Tuple2, Long> getConsumerPipeline() {
        return consumerPipeline;
    }

    public void setConsumerPipeline(Map<Tuple2, Long> consumerPipeline) {
        this.consumerPipeline = consumerPipeline;
    }

    public Map<Long, TbSelfMonitorAlarmRule> getSelfMonitorRule() {
        return selfMonitorRule;
    }

    public void setSelfMonitorRule(Map<Long, TbSelfMonitorAlarmRule> selfMonitorRule) {
        this.selfMonitorRule = selfMonitorRule;
    }

    public Map<Tuple2, Long> getCkStorageConsumerMap() {
        return ckStorageConsumerMap;
    }

    public void setCkStorageConsumerMap(Map<Tuple2, Long> ckStorageConsumerMap) {
        this.ckStorageConsumerMap = ckStorageConsumerMap;
    }

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }

    public Long getOffset() {
        return offset;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Boolean getNeedSendNotify() {
        return needSendNotify;
    }

    public void setNeedSendNotify(Boolean needSendNotify) {
        this.needSendNotify = needSendNotify;
    }
}
