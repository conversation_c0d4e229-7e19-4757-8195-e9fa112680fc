package com.eoi.jax.web.monitor.model.inspection;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
public class InspectionDashboardCountResp {

    private List<ComponentStatus> internalComponentStatus;

    private List<ExternalComponentStatus> externalComponentStatus;

    private List<JobCountStatistics> cellJobCount;

    private List<JobCountStatistics> realtimeJobCount;

    private List<JobCountStatistics> offlineJobCount;

    private List<JobCountStatistics> xxlJobCount;

    public List<ComponentStatus> getInternalComponentStatus() {
        return internalComponentStatus;
    }

    public void setInternalComponentStatus(List<ComponentStatus> internalComponentStatus) {
        this.internalComponentStatus = internalComponentStatus;
    }

    public List<ExternalComponentStatus> getExternalComponentStatus() {
        return externalComponentStatus;
    }

    public void setExternalComponentStatus(List<ExternalComponentStatus> externalComponentStatus) {
        this.externalComponentStatus = externalComponentStatus;
    }

    public List<JobCountStatistics> getCellJobCount() {
        return cellJobCount;
    }

    public void setCellJobCount(List<JobCountStatistics> cellJobCount) {
        this.cellJobCount = cellJobCount;
    }

    public List<JobCountStatistics> getRealtimeJobCount() {
        return realtimeJobCount;
    }

    public void setRealtimeJobCount(List<JobCountStatistics> realtimeJobCount) {
        this.realtimeJobCount = realtimeJobCount;
    }

    public List<JobCountStatistics> getOfflineJobCount() {
        return offlineJobCount;
    }

    public void setOfflineJobCount(List<JobCountStatistics> offlineJobCount) {
        this.offlineJobCount = offlineJobCount;
    }

    public List<JobCountStatistics> getXxlJobCount() {
        return xxlJobCount;
    }

    public void setXxlJobCount(List<JobCountStatistics> xxlJobCount) {
        this.xxlJobCount = xxlJobCount;
    }
}
