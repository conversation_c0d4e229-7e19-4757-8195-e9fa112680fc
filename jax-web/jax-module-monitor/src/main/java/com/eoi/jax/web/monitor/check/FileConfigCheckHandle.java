package com.eoi.jax.web.monitor.check;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.client.elasticsearch.ClusterHealth;
import com.eoi.jax.web.core.client.elasticsearch.ElasticsearchClient;
import com.eoi.jax.web.core.common.enumrate.SelfMonitorObjectTypeEnum;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.config.config.DataserviceEsConfig;
import com.eoi.jax.web.core.config.config.DataserviceKafkaConfig;
import com.eoi.jax.web.core.config.config.JupyterhubConfig;
import com.eoi.jax.web.core.config.config.MonitorKafkaConfig;
import com.eoi.jax.web.core.config.config.OfflineWorkFlowZookeeper;
import com.eoi.jax.web.core.config.config.SelfMonitorAlarmConfig;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.model.filecheck.FileCheckReq;
import com.eoi.jax.web.core.model.filecheck.FileCheckResp;
import com.eoi.jax.web.core.model.filecheck.YamlCheckReq;
import com.eoi.jax.web.core.model.filecheck.YamlCheckResp;
import com.eoi.jax.web.core.model.jaxcluster.JaxClusterNode;
import com.eoi.jax.web.core.model.jaxcluster.JaxClusterNodes;
import com.eoi.jax.web.core.service.JaxApiService;
import com.eoi.jax.web.core.service.JaxClusterService;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.ingestion.enumrate.ClusterTypeEnum;
import com.eoi.jax.web.ingestion.enumrate.DatasourceStatusEnum;
import com.eoi.jax.web.ingestion.enumrate.JarTypeEnum;
import com.eoi.jax.web.ingestion.enumrate.OptsTypeEnum;
import com.eoi.jax.web.ingestion.factory.datasource.model.KafkaConnection;
import com.eoi.jax.web.ingestion.model.cluster.ClusterResp;
import com.eoi.jax.web.ingestion.model.cluster.YarnCluster;
import com.eoi.jax.web.ingestion.model.datasource.connection.DatasourceConnectionCheckResp;
import com.eoi.jax.web.ingestion.model.jar.JarQueryReq;
import com.eoi.jax.web.ingestion.model.jar.JarResp;
import com.eoi.jax.web.ingestion.model.opts.FlinkOpts;
import com.eoi.jax.web.ingestion.model.opts.OptsResp;
import com.eoi.jax.web.ingestion.model.opts.OptsSettingFactory;
import com.eoi.jax.web.ingestion.service.AlgorithmLabService;
import com.eoi.jax.web.ingestion.service.ClusterService;
import com.eoi.jax.web.ingestion.service.JarService;
import com.eoi.jax.web.ingestion.service.OptsService;
import com.eoi.jax.web.ingestion.util.KafkaUtil;
import com.eoi.jax.web.ingestion.util.RedisUtil;
import com.eoi.jax.web.monitor.enumrate.CheckCategoryEnum;
import com.eoi.jax.web.monitor.enumrate.HealthCheckResultStatusEnum;
import com.eoi.jax.web.monitor.model.check.CheckContext;
import com.eoi.jax.web.monitor.model.check.HealthCheckInstanceResult;
import com.eoi.jax.web.monitor.model.check.HealthCheckResult;
import com.eoi.jax.web.monitor.util.NetworkUtil;
import com.eoi.jax.web.workflow.component.RegistryClient;
import com.google.common.base.Stopwatch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dolphinscheduler.common.enums.NodeType;
import org.apache.groovy.util.Maps;
import org.apache.kafka.clients.admin.TopicDescription;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.eoi.jax.web.core.common.enumrate.SelfMonitorObjectTypeEnum.EXTENSIO_PACK;
import static com.eoi.jax.web.core.common.enumrate.SelfMonitorObjectTypeEnum.SYSTEM_CONFIG;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_DATASERVICE_LOG_ES;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_DATASERVICE_LOG_KAFKA;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_FILE;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_JUPYTERHUB;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_MONITOR_ALARM_KAFKA;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_MONITOR_METRIC_KAFKA;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_REDIS;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_VICTORIAMETRICS;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_WORKFLOW_ZOOKEEPER;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.CONFIG_YAML;
import static com.eoi.jax.web.monitor.enumrate.HealthCheckComponentEnum.EXTENSION_PACK;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Component
public class FileConfigCheckHandle extends AbstractHealthCheckHandle {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileConfigCheckHandle.class);

    private static final Map<String, Set<String>> JAX_WEB_YAML_FILE = Maps.of(
        "${JAX_HOME}/jax/application.yml", CollUtil.newHashSet("server", "jax.serverId", "xxl.job.executor.port")
    );

    private static final String[] JAX_WEB_FILE = new String[]{
        "${JAX_HOME}/start.sh",
        "${JAX_HOME}/stop.sh"
    };

    @Resource
    private JaxClusterService jaxClusterService;

    @Resource
    private JaxApiService jaxApiService;

    @Resource
    private ClusterService clusterService;

    @Resource
    private OptsService optsService;

    @Resource
    private JarService jarService;

    @Resource
    private SystemConfigHolder systemConfigHolder;

    @Resource
    private RegistryClient registryClient;

    @Resource
    private AlgorithmLabService algorithmLabService;


    @Override
    public void check(CheckContext context) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        LOGGER.info("配置检查开始");
        List<Consumer<CheckContext>> list = new LinkedList<>();
        list.add(this::checkJaxYaml);
        list.add(this::checkJaxWebFile);
        list.add(this::checkHadoopConfig);
        list.add(this::checkFlink);
        list.add(this::checkJar);
        list.add(this::checkSystemConfig);

        for (Consumer<CheckContext> consumer : list) {
            try {
                consumer.accept(context);
            } catch (Exception e) {
                LOGGER.error("配置检查执行异常", e);
            }
        }
        LOGGER.info("配置检查结束, 耗时:{}秒", stopwatch.elapsed().getSeconds());
    }


    private void checkJaxYaml(CheckContext context) {
        HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
            CheckCategoryEnum.CONFIG, "JAX_WEB配置文件", "配置一致性");

        for (Map.Entry<String, Set<String>> entry : JAX_WEB_YAML_FILE.entrySet()) {
            List<YamlCheckResp> yamlCheckRespList = fetchYamlMd5(entry.getKey(), entry.getValue());
            if (CollUtil.isEmpty(yamlCheckRespList)) {
                return;
            }

            healthCheckResult.getInstanceList().add(checkAllYamlIsSame(entry.getKey(), yamlCheckRespList));
        }
        context.getCheckResultStorage().store(CollUtil.newArrayList(healthCheckResult));
    }


    private HealthCheckInstanceResult checkAllYamlIsSame(String file, List<YamlCheckResp> yamlCheckRespList) {
        HealthCheckInstanceResult result = HealthCheckInstanceResult.createSuccess(-1L, file,
            SelfMonitorObjectTypeEnum.JAX_WEB_FILE.code());

        List<YamlCheckResp> basePathNotExistList = yamlCheckRespList.stream()
            .filter(it -> !it.getBasePathExist()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(basePathNotExistList)) {
            result.setExceptionContentAndSolution(String.format("服务器[%s]文件或目录不存在",
                    basePathNotExistList.stream().map(YamlCheckResp::getServerAddress).collect(Collectors.joining(","))),
                CONFIG_FILE.resolution());
            return result;
        }

        if (yamlCheckRespList.size() == 1) {
            return result;
        }

        if (yamlCheckRespList.stream().map(YamlCheckResp::getMd5).distinct().count() == 1) {
            return result;
        }

        YamlCheckResp baseFileCheckResp = yamlCheckRespList.get(0);
        yamlCheckRespList.remove(baseFileCheckResp);

        String diffKey = checkYamlContentDiff(baseFileCheckResp, yamlCheckRespList);
        result.setExceptionContentAndSolution(String.format("YAML文件内容不一致，key:[%s]", diffKey), CONFIG_YAML.resolution());

        return result;
    }


    private String checkYamlContentDiff(YamlCheckResp baceFileCheckResp, List<YamlCheckResp> yamlCheckRespList) {
        Map<String, Object> baseConfig = baceFileCheckResp.getConfig();

        Set<String> diffKeySet = CollUtil.newHashSet();

        for (YamlCheckResp yamlCheckResp : yamlCheckRespList) {
            Map<String, Object> config = yamlCheckResp.getConfig();
            // 不一致
            if (baseConfig.size() != config.size()) {
                Collection<String> subtract = baseConfig.size() > config.size() ?
                    CollectionUtils.subtract(baseConfig.keySet(), config.keySet())
                    : CollectionUtils.subtract(config.keySet(), baseConfig.keySet());
                diffKeySet.addAll(subtract);
            }
        }

        if (CollUtil.isNotEmpty(diffKeySet)) {
            return String.join(",", diffKeySet);
        }

        for (YamlCheckResp yamlCheckResp : yamlCheckRespList) {
            Map<String, Object> config = yamlCheckResp.getConfig();
            for (Map.Entry<String, Object> entry : baseConfig.entrySet()) {
                Object s = config.get(entry.getKey());
                if (Objects.isNull(s) || !s.equals(entry.getValue())) {
                    diffKeySet.add(entry.getKey());
                }
            }
        }
        return String.join(",", diffKeySet);
    }

    private void checkJaxWebFile(CheckContext context) {
        HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
            CheckCategoryEnum.CONFIG, "JAX_WEB配置文件", "配置一致性");

        for (String baseFile : JAX_WEB_FILE) {
            try {
                List<FileCheckResp> fileCheckRespList = fetchFileMd5(baseFile, false);
                if (CollUtil.isEmpty(fileCheckRespList)) {
                    return;
                }

                List<HealthCheckInstanceResult> instanceResultList = checkAllFileIsSame(baseFile, fileCheckRespList);
                healthCheckResult.getInstanceList().addAll(instanceResultList);
            } catch (Exception e) {
                LOGGER.error("检查JAX_WEB配置文件异常", e);
            }
        }

        context.getCheckResultStorage().store(CollUtil.newArrayList(healthCheckResult));
    }


    private void checkHadoopConfig(CheckContext context) {
        List<HealthCheckResult> resultList = CollUtil.newLinkedList();
        List<ClusterResp> list = clusterService.all(ClusterTypeEnum.YARN.code());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (ClusterResp resp : list) {
            try {
                YarnCluster yarnCluster = BeanUtil.mapToBean(resp.getSetting(), YarnCluster.class, false);
                HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
                    CheckCategoryEnum.CONFIG, String.format("Hadoop集群%s配置文件", resp.getClusterName()), "配置一致性");

                List<FileCheckResp> fileCheckRespList = fetchFileMd5(yarnCluster.computeHadoopConfPath(), false);
                List<HealthCheckInstanceResult> instanceResultList =
                    checkAllFileIsSame(yarnCluster.computeHadoopConfPath(), fileCheckRespList);
                healthCheckResult.getInstanceList().addAll(instanceResultList);

                resultList.add(healthCheckResult);
            } catch (Exception e) {
                LOGGER.error("检查Hadoop配置文件异常", e);
            }
        }

        context.getCheckResultStorage().store(resultList);
    }

    private void checkFlink(CheckContext context) {
        List<OptsResp> optsRespList = optsService.all(OptsTypeEnum.FLINK.code());
        if (CollUtil.isEmpty(optsRespList)) {
            return;
        }

        List<HealthCheckResult> resultList = CollUtil.newLinkedList();

        for (OptsResp optsResp : optsRespList) {
            try {
                HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
                    CheckCategoryEnum.CONFIG, String.format("Flink:%s框架配置文件", optsResp.getOptsName()), "配置一致性");

                FlinkOpts flinkOpts = (FlinkOpts) OptsSettingFactory.instance(optsResp.getOptsType(), optsResp.getSetting());
                healthCheckResult.getInstanceList().addAll(checkFlinkPath(flinkOpts.computeFlinkConfPath()));
                healthCheckResult.getInstanceList().addAll(checkFlinkPath(flinkOpts.computeFlinkLibPath()));
                healthCheckResult.getInstanceList().addAll(checkFlinkPath(flinkOpts.getJobLib()));
                resultList.add(healthCheckResult);
            } catch (Exception e) {
                LOGGER.error("检查Flink配置文件异常", e);
            }
        }

        context.getCheckResultStorage().store(resultList);
    }


    private List<HealthCheckInstanceResult> checkFlinkPath(String path) {
        List<FileCheckResp> fileCheckRespList = fetchFileMd5(path, false);
        return checkAllFileIsSame(path, fileCheckRespList);
    }


    private void checkJar(CheckContext context) {
        HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
            CheckCategoryEnum.CONFIG, "扩展包", "可用性");

        List<HealthCheckInstanceResult> healthCheckInstanceResultList = new LinkedList<>();
        for (int i = 0; ; i++) {
            JarQueryReq jarQueryReq = new JarQueryReq();
            jarQueryReq.setPage(i);
            jarQueryReq.setSize(500);
            Paged<JarResp> paged = jarService.query(jarQueryReq);
            if (CollUtil.isEmpty(paged.getList())) {
                break;
            }
            for (JarResp jarResp : paged.getList()) {
                try {
                    HealthCheckInstanceResult healthCheckInstanceResult =
                        HealthCheckInstanceResult.createSuccess(jarResp.getId(), jarResp.getJarName(), EXTENSIO_PACK.code());
                    healthCheckInstanceResult.putInstanceInfo("包名称", jarResp.getJarFile());
                    healthCheckInstanceResult.putInstanceInfo("包类型", JarTypeEnum.getMessageByCode(jarResp.getJarType()));
                    if (!jarResp.getFileNotExists()) {
                        healthCheckInstanceResult.setExceptionContentAndSolution("拓展包不存在", EXTENSION_PACK.resolution());
                    }
                } catch (Exception e) {
                    LOGGER.error("检查拓展包异常", e);
                }
            }
        }

        healthCheckResult.setInstanceList(healthCheckInstanceResultList);

        context.getCheckResultStorage().store(CollUtil.newArrayList(healthCheckResult));
    }


    private void checkSystemConfig(CheckContext context) {
        HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
            CheckCategoryEnum.CONFIG, "系统管理-外部配置", "有效性");

        List<Supplier<HealthCheckInstanceResult>> list = new LinkedList<>();
        list.add(this::checkWorkflowZookeeper);
        list.add(this::checkVictoriaMetrics);
        list.add(this::checkJupyterhub);
        list.add(this::checkRedis);
        list.add(this::checkDataserviceLogKafka);
        list.add(this::checkDataserviceLogEs);
        list.add(this::selfMonitorMetricKafka);
        list.add(this::selfMonitorAlarmKafka);

        List<HealthCheckInstanceResult> healthCheckInstanceResultList = list.stream().map(supplier -> {
            try {
                return supplier.get();
            } catch (Exception e) {
                LOGGER.error("检查系统配置异常", e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        healthCheckResult.setInstanceList(healthCheckInstanceResultList);
        context.getCheckResultStorage().store(CollUtil.newArrayList(healthCheckResult));
    }


    private HealthCheckInstanceResult checkWorkflowZookeeper() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "数据开发-离线工作流zookeeper配置", SYSTEM_CONFIG.code());
        OfflineWorkFlowZookeeper zookeeper = systemConfigHolder.getBean(SystemConfigEnum.OFFLINE_WORKFLOW_ZOOKEEPER);
        if (Optional.ofNullable(zookeeper).map(OfflineWorkFlowZookeeper::getEnable).orElse(false)) {
            try {
                if (CollUtil.isEmpty(registryClient.getServerNodeSet(NodeType.MASTER)) ||
                    CollUtil.isEmpty(registryClient.getServerNodeSet(NodeType.WORKER))) {
                    throw new RuntimeException("海豚执行节点异常");
                }
            } catch (Exception e) {
                healthCheckInstanceResult.setExceptionContentAndSolution("离线工作流zookeeper配置未生效, 找不到海豚执行节点",
                    CONFIG_WORKFLOW_ZOOKEEPER.resolution());
            }
        }
        return healthCheckInstanceResult;
    }

    private HealthCheckInstanceResult checkVictoriaMetrics() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "基础配置-VictoriaMetrics采集器配置", SYSTEM_CONFIG.code());
        String vmUrl = systemConfigHolder.getStr(SystemConfigEnum.JAX_METRICS.getKey(), "address");
        if (StrUtil.isBlank(vmUrl)) {
            return healthCheckInstanceResult;
        }
        if (!NetworkUtil.checkHttpConnected(vmUrl)) {
            healthCheckInstanceResult.setExceptionContentAndSolution("VictoriaMetrics采集器配置未生效", CONFIG_VICTORIAMETRICS.resolution());
        }
        return healthCheckInstanceResult;
    }

    private HealthCheckInstanceResult checkJupyterhub() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "算法实验室-Jupyterhub配置", SYSTEM_CONFIG.code());
        JupyterhubConfig jupyterhubConfig = systemConfigHolder.getBean(SystemConfigEnum.ALGORITHM_LAB_JUPYTERHUB);
        if (Objects.isNull(jupyterhubConfig) || StrUtil.isBlank(jupyterhubConfig.getUrl())
            || StrUtil.isBlank(jupyterhubConfig.getToken())) {
            return healthCheckInstanceResult;
        }
        try {
            DatasourceConnectionCheckResp datasourceConnectionCheckResp = algorithmLabService.checkJupyterhubStatus();
            if (!DatasourceStatusEnum.NORMAL.code().equals(datasourceConnectionCheckResp.getStatus())) {
                throw new RuntimeException("Jupyterhub服务异常");
            }
        } catch (Exception e) {
            healthCheckInstanceResult.setExceptionContentAndSolution("算法实验室-Jupyterhub配置未生效", CONFIG_JUPYTERHUB.resolution());
        }
        return healthCheckInstanceResult;
    }


    private HealthCheckInstanceResult checkRedis() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "数据服务-接口数据缓存/分布式流控配置", SYSTEM_CONFIG.code());
        Map<String, Object> configMap = systemConfigHolder.getConfig(SystemConfigEnum.DATASERVICE_REDIS);
        if (Objects.isNull(configMap) || Boolean.FALSE.equals(configMap.getOrDefault("enable", false))) {
            return healthCheckInstanceResult;
        }
        RedissonClient redissonClient = null;
        try {
            redissonClient = RedisUtil.buildRedissonClient(configMap);
            RAtomicLong atomicLong = redissonClient.getAtomicLong("jax_test_" + System.currentTimeMillis());
            atomicLong.expire(Duration.ofSeconds(1));
            atomicLong.incrementAndGet();
        } catch (Exception e) {
            healthCheckInstanceResult.setExceptionContentAndSolution("数据服务-接口数据缓存/分布式流控配置未生效", CONFIG_REDIS.resolution());
        } finally {
            if (Objects.nonNull(redissonClient)) {
                redissonClient.shutdown();
            }
        }
        return healthCheckInstanceResult;
    }

    private HealthCheckInstanceResult checkDataserviceLogKafka() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "数据服务-日志查询kafka中转", SYSTEM_CONFIG.code());
        DataserviceKafkaConfig kafkaConfig = systemConfigHolder.getBean(SystemConfigEnum.DATA_SERVICE_LOG_KAFKA);
        if (!Optional.ofNullable(kafkaConfig).map(DataserviceKafkaConfig::getEnable).orElse(false)) {
            return healthCheckInstanceResult;
        }
        try {
            KafkaConnection kafkaConnection = ModelBeanUtil.copyBean(kafkaConfig, new KafkaConnection());
            TopicDescription topic = KafkaUtil.getTopicDescription(kafkaConfig.getTopic(),
                kafkaConnection.getBrokerAddress(), kafkaConnection);
            if (Objects.isNull(topic)) {
                throw new RuntimeException("topic不存在");
            }
        } catch (Exception e) {
            healthCheckInstanceResult.setExceptionContentAndSolution("数据服务-日志查询kafka中转未生效", CONFIG_DATASERVICE_LOG_KAFKA.resolution());
        }
        return healthCheckInstanceResult;
    }


    private HealthCheckInstanceResult checkDataserviceLogEs() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "数据服务-日志查询ES存储", SYSTEM_CONFIG.code());
        DataserviceEsConfig esConfig = systemConfigHolder.getBean(SystemConfigEnum.DATA_SERVICE_LOG_ES);
        if (Objects.isNull(esConfig) || StrUtil.isBlank(esConfig.getAddress()) || StrUtil.isBlank(esConfig.getIndexTemplate())) {
            return healthCheckInstanceResult;
        }
        try {
            ElasticsearchClient elasticsearchClient = new ElasticsearchClient(esConfig.getAddress(),
                esConfig.getUsername(), esConfig.getPassword());

            ClusterHealth clusterHealth = elasticsearchClient.getClusterHealth();
            if (!("green".equals(clusterHealth.getStatus()) && "yellow".equals(clusterHealth.getStatus()))) {
                throw new RuntimeException("ES集群状态异常");
            }
        } catch (Exception e) {
            healthCheckInstanceResult.setExceptionContentAndSolution("数据服务-日志查询ES存储未生效", CONFIG_DATASERVICE_LOG_ES.resolution());
        }
        return healthCheckInstanceResult;
    }

    private HealthCheckInstanceResult selfMonitorMetricKafka() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "运维排障-指标收集Kafka配置", SYSTEM_CONFIG.code());
        MonitorKafkaConfig kafkaConfig = systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_COLLECT_KAFKA);
        if (!Optional.ofNullable(kafkaConfig).map(MonitorKafkaConfig::getEnable).orElse(false)) {
            return healthCheckInstanceResult;
        }
        try {
            KafkaConnection kafkaConnection = ModelBeanUtil.copyBean(kafkaConfig, new KafkaConnection());
            TopicDescription topic = KafkaUtil.getTopicDescription(kafkaConfig.getTopic(),
                kafkaConnection.getBrokerAddress(), kafkaConnection);
            if (Objects.isNull(topic)) {
                throw new RuntimeException("topic不存在");
            }
        } catch (Exception e) {
            healthCheckInstanceResult.setExceptionContentAndSolution("运维排障-指标收集Kafka配置未生效", CONFIG_MONITOR_METRIC_KAFKA.resolution());
        }
        return healthCheckInstanceResult;
    }

    private HealthCheckInstanceResult selfMonitorAlarmKafka() {
        HealthCheckInstanceResult healthCheckInstanceResult =
            HealthCheckInstanceResult.createSuccess(-1L, "运维排障-自监控告警配置", SYSTEM_CONFIG.code());
        SelfMonitorAlarmConfig kafkaConfig = systemConfigHolder.getBean(SystemConfigEnum.SELF_MONITOR_ALARM);
        if (!Optional.ofNullable(kafkaConfig).map(SelfMonitorAlarmConfig::getEnable).orElse(false)) {
            return healthCheckInstanceResult;
        }
        try {
            KafkaConnection kafkaConnection = ModelBeanUtil.copyBean(kafkaConfig, new KafkaConnection());
            TopicDescription topic = KafkaUtil.getTopicDescription(kafkaConfig.getTopic(),
                kafkaConnection.getBrokerAddress(), kafkaConnection);
            if (Objects.isNull(topic)) {
                throw new RuntimeException("topic不存在");
            }
        } catch (Exception e) {
            healthCheckInstanceResult.setExceptionContentAndSolution("运维排障-自监控告警配置未生效", CONFIG_MONITOR_ALARM_KAFKA.resolution());
        }
        return healthCheckInstanceResult;
    }


    private List<HealthCheckInstanceResult> checkAllFileIsSame(String baseFile, List<FileCheckResp> checkRespList) {
        if (CollUtil.isEmpty(checkRespList)) {
            return CollUtil.newLinkedList();
        }
        List<FileCheckResp> basePathNotExistList = checkRespList.stream().filter(it -> !it.getBasePathExist()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(basePathNotExistList)) {
            HealthCheckInstanceResult result =
                HealthCheckInstanceResult.createSuccess(-1L, baseFile, SelfMonitorObjectTypeEnum.JAX_WEB_FILE.code());
            result.setExceptionContentAndSolution(String.format("服务器:[%s]文件或目录不存在",
                    basePathNotExistList.stream().map(FileCheckResp::getServerAddress).collect(Collectors.joining(","))),
                CONFIG_FILE.resolution());
            return CollUtil.newLinkedList(result);
        }

        if (checkRespList.size() == 1) {
            return checkRespList.get(0).getMd5Map().keySet().stream()
                .map(it -> HealthCheckInstanceResult.createSuccess(-1L, it, SelfMonitorObjectTypeEnum.JAX_WEB_FILE.code()))
                .collect(Collectors.toList());
        }

        // 选择一个文件MD5值最多的作为基准
        Optional<FileCheckResp> respOptional = checkRespList.stream().max(Comparator.comparingInt(o ->
            Optional.ofNullable(o.getMd5Map()).map(Map::size).orElse(0)));
        FileCheckResp baseFileCheckResp = respOptional.orElse(checkRespList.get(0));
        checkRespList.remove(baseFileCheckResp);

        List<HealthCheckInstanceResult> resultList = new LinkedList<>();
        // 检查其他文件是否一致
        for (Map.Entry<String, String> entry : baseFileCheckResp.getMd5Map().entrySet()) {
            HealthCheckInstanceResult result =
                HealthCheckInstanceResult.createSuccess(-1L, entry.getKey(), SelfMonitorObjectTypeEnum.JAX_WEB_FILE.code());

            List<String> noSameFileServerList = checkRespList.stream().filter(it -> {
                String s = it.getMd5Map().get(entry.getKey());
                return Objects.isNull(s) || !s.equals(entry.getValue());
            }).map(FileCheckResp::getServerAddress).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(noSameFileServerList)) {
                result.setStatus(HealthCheckResultStatusEnum.EXCEPTION);
                result.setContent(String.format("检测到服务器:[%s]文件不存在和基准服务器:[%s]文件不一致,路径:%s",
                    String.join(",", noSameFileServerList), baseFileCheckResp.getServerAddress(), baseFile));
                result.setSolution(CONFIG_FILE.resolution());
            }
            resultList.add(result);
        }
        return resultList;
    }


    private List<FileCheckResp> fetchFileMd5(String path, boolean recursive) {
        JaxClusterNodes clusterNodes = jaxClusterService.getClusterNodes();
        List<JaxClusterNode> nodes = clusterNodes.getNodes();

        List<CompletableFuture<FileCheckResp>> completableFutureList = nodes.stream().map(it ->
            CompletableFuture.supplyAsync(() -> {
                try {
                    FileCheckReq req = new FileCheckReq();
                    req.setIp(it.getIp());
                    req.setPort(it.getPort());
                    req.setPath(path);
                    req.setRecursive(recursive);
                    return jaxApiService.computeFileMd5(req);
                } catch (Exception e) {
                    return null;
                }
            }, ThreadPoolUtil.THREAD_POOL)).collect(Collectors.toList());

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
        return completableFutureList.stream().map(it -> it.getNow(null)).filter(Objects::nonNull).collect(Collectors.toList());
    }


    private List<YamlCheckResp> fetchYamlMd5(String path, Set<String> excludeKeyPrefixSet) {
        JaxClusterNodes clusterNodes = jaxClusterService.getClusterNodes();
        List<JaxClusterNode> nodes = clusterNodes.getNodes();

        List<CompletableFuture<YamlCheckResp>> completableFutureList = nodes.stream().map(it ->
            CompletableFuture.supplyAsync(() -> {
                try {
                    YamlCheckReq req = new YamlCheckReq();
                    req.setIp(it.getIp());
                    req.setPort(it.getPort());
                    req.setPath(path);
                    req.setExcludeKeyPrefixSet(excludeKeyPrefixSet);
                    return jaxApiService.computeYamlFileMd5(req);
                } catch (Exception e) {
                    return null;
                }
            }, ThreadPoolUtil.THREAD_POOL)).collect(Collectors.toList());

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
        return completableFutureList.stream().map(it -> it.getNow(null)).filter(Objects::nonNull).collect(Collectors.toList());
    }

}
