package com.eoi.jax.web.monitor.model.healthcheck;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbHealthCheck;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
public class HealthCheckQuerySortReq implements ISortReq<TbHealthCheck> {

    private String startTime;


    @Override
    public QueryWrapper<TbHealthCheck> order(QueryWrapper<TbHealthCheck> wrapper) {
        wrapper.lambda().orderBy(isOrder(startTime), isAsc(startTime), TbHealthCheck::getStartTime);
        return wrapper;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
}
