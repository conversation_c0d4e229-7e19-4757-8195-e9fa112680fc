package com.eoi.jax.web.monitor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.SelfMonitorObjectTypeEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.ingestion.util.VelocityUtil;
import com.eoi.jax.web.monitor.check.AbstractHealthCheckHandle;
import com.eoi.jax.web.monitor.check.DefaultCheckResultStorage;
import com.eoi.jax.web.monitor.check.HealthCheckHandleFactory;
import com.eoi.jax.web.monitor.enumrate.CheckCategoryEnum;
import com.eoi.jax.web.monitor.enumrate.HealthCheckResultStatusEnum;
import com.eoi.jax.web.monitor.model.check.CheckContext;
import com.eoi.jax.web.monitor.model.healthcheck.HealthCheckCronReq;
import com.eoi.jax.web.monitor.model.healthcheck.HealthCheckItemResp;
import com.eoi.jax.web.monitor.model.healthcheck.HealthCheckQueryReq;
import com.eoi.jax.web.monitor.model.healthcheck.HealthCheckResp;
import com.eoi.jax.web.monitor.model.healthcheck.HealthCheckStartReq;
import com.eoi.jax.web.monitor.model.healthcheckinstance.HealthCheckInstanceQueryReq;
import com.eoi.jax.web.monitor.model.healthcheckinstance.HealthCheckInstanceResp;
import com.eoi.jax.web.monitor.service.HealthCheckService;
import com.eoi.jax.web.repository.entity.TbHealthCheck;
import com.eoi.jax.web.repository.entity.TbHealthCheckInstance;
import com.eoi.jax.web.repository.entity.TbHealthCheckItem;
import com.eoi.jax.web.repository.service.TbHealthCheckInstanceService;
import com.eoi.jax.web.repository.service.TbHealthCheckItemService;
import com.eoi.jax.web.repository.service.TbHealthCheckService;
import com.eoi.jax.web.xxl.core.cron.CronExpression;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobInfo;
import com.eoi.jax.web.xxl.model.JobInfoQueryParam;
import com.eoi.jax.web.xxl.service.XxlJobService;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import org.apache.velocity.VelocityContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Service
public class HealthCheckServiceImpl implements HealthCheckService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HealthCheckServiceImpl.class);

    private static final String DEFAULT_XXL_EXECUTOR_APP = "JAX_DEFAULT_EXECUTOR";

    private static final String DEFAULT_XXL_EXECUTOR_NAME = "中台分布式任务执行器";

    private static final int JAX_DEFAULT_EXECUTOR_GROUP_ID = 3;

    private static final String HEALTH_CHECK_JOB_NAME = "健康检查定时任务";

    public static final String HEALTH_CHECK_JOB_HANDLER = "jaxHealthCheckHandler";

    @Resource
    private DefaultCheckResultStorage defaultCheckResultStore;

    @Resource
    private HealthCheckHandleFactory healthCheckHandleFactory;

    @Resource
    private TbHealthCheckService tbHealthCheckService;

    @Resource
    private TbHealthCheckItemService tbHealthCheckItemService;

    @Resource
    private TbHealthCheckInstanceService tbHealthCheckInstanceService;

    @Resource
    private XxlJobService xxlJobService;


    @Override
    public void start(HealthCheckStartReq req) {
        this.healthCheckCanRun();

        TbHealthCheck tbHealthCheck = new TbHealthCheck();
        tbHealthCheck.setStartTime(new Date());
        tbHealthCheck.setStatus("RUNNING");
        tbHealthCheck.setRunType(req.getRunType());
        ModelBeanUtil.setCreateDefaultValue(tbHealthCheck);
        tbHealthCheckService.save(tbHealthCheck);

        List<AbstractHealthCheckHandle> checkHandleList = null;
        if (StrUtil.isBlank(req.getModule())) {
            checkHandleList = healthCheckHandleFactory.allCheckHandle();
        } else {
            checkHandleList = CollUtil.newLinkedList(healthCheckHandleFactory.getCheckHandle(req.getModule()));
        }
        for (AbstractHealthCheckHandle checkHandle : checkHandleList) {
            try {
                checkHandle.check(new CheckContext(tbHealthCheck.getId(), defaultCheckResultStore));
            } catch (Exception e) {
                LOGGER.error("健康检查执行异常", e);
            }
        }

        healthCheckSummary(tbHealthCheck.getId());
    }


    @Override
    public Boolean asyncManualStart(HealthCheckStartReq req) {
        this.healthCheckCanRun();
        ThreadPoolUtil.THREAD_POOL.submit(() -> {
            HealthCheckStartReq startReq = Objects.isNull(req) ? new HealthCheckStartReq() : req;
            req.setRunType("MANUAL");
            start(req);
        });
        return true;
    }


    private void healthCheckCanRun() {
        TbHealthCheck lastTbHealthCheck = tbHealthCheckService.lastHealthCheck();
        if (Objects.nonNull(lastTbHealthCheck) && "RUNNING".equals(lastTbHealthCheck.getStatus())) {
            if (System.currentTimeMillis() - lastTbHealthCheck.getStartTime().getTime() > 24 * 60 * 60 * 1000) {
                LOGGER.error("健康检查任务正在运行中，但已超过24小时，强制停止");
                lastTbHealthCheck.setStatus("FORCE_STOP");
                ModelBeanUtil.setUpdateDefaultValue(lastTbHealthCheck);
                tbHealthCheckService.updateById(lastTbHealthCheck);
            } else {
                throw new BizException(ResponseCode.FAILED.getCode(), "健康检查任务正在运行中，请稍后再试");
            }
        }
    }

    @Override
    public Paged<HealthCheckResp> query(HealthCheckQueryReq req) {
        Page<TbHealthCheck> page = tbHealthCheckService.page(req.page(), req.query());
        if (CollUtil.isEmpty(page.getRecords())) {
            return new Paged<>(page.getTotal(), CollUtil.newArrayList());
        }
        List<HealthCheckResp> respList = page.getRecords().stream()
            .map(it -> new HealthCheckResp().fromEntity(it)).collect(Collectors.toList());
        return new Paged<>(page.getTotal(), respList);
    }


    @Override
    public HealthCheckResp getById(Long id) {
        TbHealthCheck tbHealthCheck;
        if (Objects.isNull(id) || id == -1L) {
            tbHealthCheck = tbHealthCheckService.lastHealthCheck();
        } else {
            tbHealthCheck = tbHealthCheckService.getById(id);
        }
        if (Objects.isNull(tbHealthCheck)) {
            return null;
        }
        HealthCheckResp healthCheckResp = new HealthCheckResp().fromEntity(tbHealthCheck);

        List<TbHealthCheckItem> tbHealthCheckItemList = tbHealthCheckItemService.listByCheckId(healthCheckResp.getId());
        if (CollUtil.isEmpty(tbHealthCheckItemList)) {
            return healthCheckResp;
        }

        // 根据类别分组
        Map<String, List<TbHealthCheckItem>> healthCheckItemMap = tbHealthCheckItemList.stream()
            .collect(Collectors.groupingBy(TbHealthCheckItem::getCategory));
        healthCheckResp.setComponentCheck(healthCheckInstanceItem(healthCheckItemMap.get(CheckCategoryEnum.COMPONENT.code())));
        healthCheckResp.setJobCheck(healthCheckInstanceItem(healthCheckItemMap.get(CheckCategoryEnum.JOB.code())));
        healthCheckResp.setDataCheck(healthCheckInstanceItem(healthCheckItemMap.get(CheckCategoryEnum.DATA.code())));
        healthCheckResp.setModelCheck(healthCheckInstanceItem(healthCheckItemMap.get(CheckCategoryEnum.MODEL.code())));
        healthCheckResp.setConfigCheck(healthCheckInstanceItem(healthCheckItemMap.get(CheckCategoryEnum.CONFIG.code())));


        AbstractXxlJobInfo healthCheckXxlJob = findHealthCheckXxlJob();
        if (Objects.isNull(healthCheckXxlJob)) {
            healthCheckResp.setEnable(false);
        } else {
            healthCheckResp.setEnable(healthCheckXxlJob.getTriggerStatus() == 1);
            healthCheckResp.setCron(healthCheckXxlJob.getJobCron());
        }
        return healthCheckResp;
    }


    @Override
    public Paged<HealthCheckInstanceResp> queryInstance(HealthCheckInstanceQueryReq req) {
        Page<TbHealthCheckInstance> page = tbHealthCheckInstanceService.page(req.page(), req.query());

        if (CollUtil.isEmpty(page.getRecords())) {
            return new Paged<>(page.getTotal(), CollUtil.newArrayList());
        }

        List<HealthCheckInstanceResp> respList = page.getRecords().stream()
            .map(it -> new HealthCheckInstanceResp().fromEntity(it)).collect(Collectors.toList());

        return new Paged<>(page.getTotal(), respList);
    }

    @Override
    public void report(HttpServletResponse response, Long id) {
        HealthCheckResp healthCheckResp = getById(id);
        VelocityContext context = new VelocityContext();
        try {
            String date = DateUtil.format(healthCheckResp.getStartTime(), DatePattern.PURE_DATETIME_PATTERN);
            context.put("date", date);
            context.put("healthCheck", healthCheckResp);
            context.put("unhealthyInstanceList", listByCheckIdAndStatus(id,
                CollUtil.newArrayList(HealthCheckResultStatusEnum.SUGGESTION.code(), HealthCheckResultStatusEnum.EXCEPTION.code())));
            context.put("normalList", listByCheckIdAndStatus(id, CollUtil.newArrayList(HealthCheckResultStatusEnum.NORMAL.code())));
            String content = VelocityUtil.getContent("health_check_report.vm", context);
            response.setContentType("application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                URLEncoder.encode(String.format("健康检查报告-%s.pdf", date), StandardCharsets.UTF_8.name()));
            OutputStream os = response.getOutputStream();

            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.useFont(() -> HealthCheckServiceImpl.class.getClassLoader().getResourceAsStream("simsun.ttf"), "simsun");
            builder.useFastMode();
            builder.withHtmlContent(content, null);
            builder.toStream(os);
            builder.run();
        } catch (Exception e) {
            throw new BizException(ResponseCode.FAILED.getCode(), "构建健康检查报告异常", e);
        }
    }

    private List<TbHealthCheckInstance> listByCheckIdAndStatus(Long id, List<String> statusList) {
        List<TbHealthCheckInstance> list = tbHealthCheckInstanceService.listByCheckIdAndStatus(id, statusList);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        list.forEach(it -> {
            String instanceType = SelfMonitorObjectTypeEnum.getMessageByCode(it.getInstanceType());
            it.setInstanceType(Objects.isNull(instanceType) ? it.getInstanceType() : instanceType);
        });
        return list;
    }


    /**
     * 健康检查统计概要
     *
     * @param id
     */
    private void healthCheckSummary(Long id) {
        TbHealthCheck tbHealthCheck = tbHealthCheckService.getById(id);
        List<TbHealthCheckItem> tbHealthCheckItemList = tbHealthCheckItemService.listByCheckId(tbHealthCheck.getId());
        if (CollUtil.isNotEmpty(tbHealthCheckItemList)) {
            long checkItemNum = tbHealthCheckItemList.stream()
                .map(it -> String.format("%s-%s-%s", it.getCategory(), it.getComponent(), it.getCheckItem())).distinct().count();
            tbHealthCheck.setCheckItemNum(checkItemNum);
            tbHealthCheck.setInstanceNum(tbHealthCheckItemList.stream().map(TbHealthCheckItem::getInstanceNum).reduce(0L, Long::sum));
            tbHealthCheck.setNormalNum(tbHealthCheckItemList.stream().map(TbHealthCheckItem::getNormalNum).reduce(0L, Long::sum));
            tbHealthCheck.setSuggestionNum(tbHealthCheckItemList.stream().map(TbHealthCheckItem::getSuggestionNum).reduce(0L, Long::sum));
            tbHealthCheck.setExceptionNum(tbHealthCheckItemList.stream().map(TbHealthCheckItem::getExceptionNum).reduce(0L, Long::sum));
        }
        tbHealthCheck.setEndTime(new Date());
        tbHealthCheck.setStatus("FINISHED");

        ModelBeanUtil.setUpdateDefaultValue(tbHealthCheck);
        // 更新健康检查统计结果
        tbHealthCheckService.updateById(tbHealthCheck);
    }


    /**
     * 健康检查实例聚合
     *
     * @param list
     * @return
     */
    private List<HealthCheckItemResp> healthCheckInstanceItem(List<TbHealthCheckItem> list) {
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        return list.stream().map(it -> new HealthCheckItemResp().fromEntity(it)).collect(Collectors.toList());
    }


    @Override
    public void healthCheckTimer(HealthCheckCronReq healthCheckCronReq) {
        AbstractXxlJobInfo xxlJob = findHealthCheckXxlJob();
        if (healthCheckCronReq.getEnable()) {
            if (Objects.isNull(xxlJob)) {
                xxlJob = xxlJobService.create(buildXxlJobInfo(null, healthCheckCronReq.getCron()));
            } else {
                if (!xxlJob.getJobCron().equals(healthCheckCronReq.getCron())) {
                    xxlJob = xxlJobService.update(buildXxlJobInfo(xxlJob.getId(), healthCheckCronReq.getCron()));
                }
            }
            AbstractXxlJobInfo xxlJobInfo = xxlJobService.get(xxlJob.getId());
            if (xxlJobInfo.getTriggerStatus() != 1) {
                xxlJobService.start(xxlJob.getId());
            }
        } else {
            // 停止定时任务
            if (Objects.nonNull(xxlJob) && xxlJob.getTriggerStatus() != 0) {
                xxlJobService.stop(xxlJob.getId());
            }
        }
    }

    private AbstractXxlJobInfo findHealthCheckXxlJob() {
        JobInfoQueryParam jobInfoQueryParam = new JobInfoQueryParam(0, 1);
        jobInfoQueryParam.setJobGroup(JAX_DEFAULT_EXECUTOR_GROUP_ID);
        jobInfoQueryParam.setExecutorHandler(HEALTH_CHECK_JOB_HANDLER);
        jobInfoQueryParam.setGlueType("BEAN");
        jobInfoQueryParam.setJobName(HEALTH_CHECK_JOB_NAME);
        jobInfoQueryParam.setTriggerStatus(-1);
        Paged<AbstractXxlJobInfo> paged = xxlJobService.pageList(jobInfoQueryParam);
        if (CollUtil.isNotEmpty(paged.getList())) {
            return paged.getList().get(0);
        }
        return null;
    }

    private AbstractXxlJobInfo buildXxlJobInfo(Integer xxlJobId, String cron) {
        try {
            CronExpression cronExpression = new CronExpression(cron);
            Date firstDate = cronExpression.getNextValidTimeAfter(new Date());
            Date secondDate = cronExpression.getNextValidTimeAfter(firstDate);
            if (secondDate.getTime() - firstDate.getTime() < 24 * 60 * 60 * 1000) {
                throw new BizException(ResponseCode.FAILED.getCode(), "健康检查执行占用资源较高, 执行间隔建议大于24小时");
            }
        } catch (ParseException e) {
            throw new BizException(ResponseCode.XXL_INVALID_CRON);
        }
        AbstractXxlJobInfo xxlJobInfo = new AbstractXxlJobInfo();
        if (Objects.nonNull(xxlJobId)) {
            xxlJobInfo.setId(xxlJobId);
        }
        xxlJobInfo.setJobGroup(JAX_DEFAULT_EXECUTOR_GROUP_ID);
        xxlJobInfo.setJobCron(cron);
        xxlJobInfo.setJobName(HEALTH_CHECK_JOB_NAME);
        xxlJobInfo.setJobDesc("健康检查定时任务");
        xxlJobInfo.setAddTime(null);
        xxlJobInfo.setUpdateTime(null);
        xxlJobInfo.setAuthor(null);
        xxlJobInfo.setAlarmEmail(null);
        xxlJobInfo.setExecutorRouteStrategy("CONSISTENT_HASH");
        xxlJobInfo.setExecutorHandler(HEALTH_CHECK_JOB_HANDLER);
        xxlJobInfo.setExecutorParam("-1");
        xxlJobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        xxlJobInfo.setExecutorTimeout(0);
        xxlJobInfo.setExecutorFailRetryCount(0);
        xxlJobInfo.setGlueType("BEAN");
        xxlJobInfo.setGlueSource("");
        xxlJobInfo.setGlueRemark("");
        xxlJobInfo.setGlueUpdatetime(null);
        xxlJobInfo.setChildJobId(null);
        xxlJobInfo.setTriggerStatus(0);
        xxlJobInfo.setTriggerLastTime(0);
        xxlJobInfo.setTriggerNextTime(0);
        xxlJobInfo.setAppName(DEFAULT_XXL_EXECUTOR_APP);
        xxlJobInfo.setExecutorName(DEFAULT_XXL_EXECUTOR_NAME);
        xxlJobInfo.setIsUserCreated(0);
        return xxlJobInfo;
    }

}
