package com.eoi.jax.web.monitor.service;

import com.eoi.jax.web.core.model.filecheck.FileCheckReq;
import com.eoi.jax.web.core.model.filecheck.FileCheckResp;
import com.eoi.jax.web.core.model.filecheck.YamlCheckReq;
import com.eoi.jax.web.core.model.filecheck.YamlCheckResp;

/**
 * <AUTHOR>
 * @date 2024/4/1
 */
public interface FileCheckService {

    /**
     * 计算文件MD5值
     * @param req
     * @return
     */
    FileCheckResp computeFileMd5(FileCheckReq req);

    /**
     * 计算yaml文件MD5值
     * @param req
     * @return
     */
    YamlCheckResp computeYamlFileMd5(YamlCheckReq req);
}
