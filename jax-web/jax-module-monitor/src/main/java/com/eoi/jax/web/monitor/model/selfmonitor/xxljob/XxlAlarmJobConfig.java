package com.eoi.jax.web.monitor.model.selfmonitor.xxljob;

import com.eoi.jax.web.monitor.model.selfmonitor.rule.SelfMonitorBaseMetric;

import java.util.List;

/**
 * @Author: yaru.ma
 * @Date: 2024/3/15
 */
public class XxlAlarmJobConfig {

    private String vmUrl;

    private List<ReporterParam> reporters;

    private Long ruleId;
    // 告警指标id
    private Long itemId;
    // 告警指标
    private String itemName;

    private Integer monitorInterval;

    private String thresholdMethod;
    /**
     * 告警内容，（支持模版引擎如freemark）
     */
    private String content;

    /**
     * 告警属性
     */
    private AlertAnnotation annotations;

    /**
     * 可以定义对象实例的标签列表
     */
    private List<String> uniqueTags;

    /**
     * 告警生成条件
     */
    private List<ThresholdConfig> conditions;
    /**
     * 采样值
     */
    private String sampleMetric;
    /**
     * 指标种类
     */
    private Boolean isAtomic;

    /**
     * 指标值
     */
    private List<SelfMonitorBaseMetric> baseMetrics;

    private String objectType;

    /**
     * 是否运行自动关闭告警
     */
    private boolean autoClose;

    public String getVmUrl() {
        return vmUrl;
    }

    public void setVmUrl(String vmUrl) {
        this.vmUrl = vmUrl;
    }

    public List<ReporterParam> getReporters() {
        return reporters;
    }

    public void setReporters(List<ReporterParam> reporters) {
        this.reporters = reporters;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public AlertAnnotation getAnnotations() {
        return annotations;
    }

    public void setAnnotations(AlertAnnotation annotations) {
        this.annotations = annotations;
    }

    public List<ThresholdConfig> getConditions() {
        return conditions;
    }

    public void setConditions(List<ThresholdConfig> conditions) {
        this.conditions = conditions;
    }

    public String getSampleMetric() {
        return sampleMetric;
    }

    public void setSampleMetric(String sampleMetric) {
        this.sampleMetric = sampleMetric;
    }

    public List<SelfMonitorBaseMetric> getBaseMetrics() {
        return baseMetrics;
    }

    public void setBaseMetrics(List<SelfMonitorBaseMetric> baseMetrics) {
        this.baseMetrics = baseMetrics;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public boolean getAutoClose() {
        return autoClose;
    }

    public void setAutoClose(boolean autoClose) {
        this.autoClose = autoClose;
    }

    public Integer getMonitorInterval() {
        return monitorInterval;
    }

    public void setMonitorInterval(Integer monitorInterval) {
        this.monitorInterval = monitorInterval;
    }

    public String getThresholdMethod() {
        return thresholdMethod;
    }

    public void setThresholdMethod(String thresholdMethod) {
        this.thresholdMethod = thresholdMethod;
    }

    public List<String> getUniqueTags() {
        return uniqueTags;
    }

    public void setUniqueTags(List<String> uniqueTags) {
        this.uniqueTags = uniqueTags;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Boolean getIsAtomic() {
        return isAtomic;
    }

    public void setIsAtomic(Boolean atomic) {
        this.isAtomic = atomic;
    }
}
