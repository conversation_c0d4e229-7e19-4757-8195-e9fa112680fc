package com.eoi.jax.web.monitor.model.healthcheckinstance;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbHealthCheckInstance;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
public class HealthCheckInstanceQueryReq extends BaseQueryReq<TbHealthCheckInstance> {

    private HealthCheckInstanceQueryFilterReq filter;

    private HealthCheckInstanceQuerySortReq sort;

    @Override
    public HealthCheckInstanceQueryFilterReq getFilter() {
        return filter;
    }

    public void setFilter(HealthCheckInstanceQueryFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public HealthCheckInstanceQuerySortReq getSort() {
        return sort;
    }

    public void setSort(HealthCheckInstanceQuerySortReq sort) {
        this.sort = sort;
    }
}
