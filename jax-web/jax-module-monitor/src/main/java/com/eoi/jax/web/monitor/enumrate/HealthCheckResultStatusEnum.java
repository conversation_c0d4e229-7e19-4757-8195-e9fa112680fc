package com.eoi.jax.web.monitor.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
public enum HealthCheckResultStatusEnum implements ICodeEnum {
    NORMAL("NORMAL", "正常"),
    SUGGESTION("SUGGESTION", "建议"),
    EXCEPTION("EXCEPTION", "异常");

    private final String code;

    private final String message;

    HealthCheckResultStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static HealthCheckResultStatusEnum fromString(String code) {
        for (HealthCheckResultStatusEnum value : HealthCheckResultStatusEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        for (HealthCheckResultStatusEnum value : HealthCheckResultStatusEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }

    public static String getCodeByMessage(String code) {
        for (HealthCheckResultStatusEnum value : HealthCheckResultStatusEnum.values()) {
            if (value.message.equals(code)) {
                return value.code;
            }
        }
        return null;
    }
    
}
