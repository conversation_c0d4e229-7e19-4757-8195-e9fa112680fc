package com.eoi.jax.web.monitor.check;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.eoi.jax.web.core.common.enumrate.SelfMonitorObjectTypeEnum;
import com.eoi.jax.web.monitor.collector.CollectorFactory;
import com.eoi.jax.web.monitor.enumrate.CollectCommand;
import com.eoi.jax.web.monitor.exporter.ConsoleExporter;
import com.eoi.jax.web.monitor.model.check.CheckContext;
import com.eoi.jax.web.monitor.model.collector.CollectContext;
import com.eoi.jax.web.monitor.model.collector.Metric;
import com.google.common.base.Stopwatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_KAFKA_BROKER_LIVED;
import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_KAFKA_CLUSTER_LIVED;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
@Service
public class ExternalComponentCheckHandle extends AbstractComponentCheckHandle {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExternalComponentCheckHandle.class);

    @Resource
    private CollectorFactory collectorFactory;

    @Override
    public void check(CheckContext context) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        LOGGER.info("外部组件检查开始");
        List<Consumer<CheckContext>> list = new LinkedList<>();
        list.add(this::checkKafka);
        list.add(this::checkElasticsearch);
        list.add(this::checkClickhouse);
        list.add(this::checkHadoop);
        list.add(this::checkRedis);
        list.add(this::checkVictoriaMetrics);
        list.add(this::checkNebula);

        for (Consumer<CheckContext> consumer : list) {
            try {
                consumer.accept(context);
            } catch (Exception e) {
                LOGGER.error("内部检查执行异常", e);
            }
        }
        LOGGER.info("外部组件检查结束, 耗时:{}秒", stopwatch.elapsed(TimeUnit.SECONDS));
    }


    private void checkKafka(CheckContext context) {
        // 采集指标
        CollectContext collectContext = new CollectContext();
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.LIVED));
        ConsoleExporter consoleExporter = new ConsoleExporter();
        collectContext.setExporter(consoleExporter);
        collectorFactory.kafkaCollector().collect(collectContext);
        List<Metric> metricList = consoleExporter.getMetricList();
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        checkComponentAllStatus(context, metricList, CollUtil.newArrayList(JAX_KAFKA_CLUSTER_LIVED.code(), JAX_KAFKA_BROKER_LIVED.code()),
            SelfMonitorObjectTypeEnum.KAFKA,
            metric -> new Pair<Long, String>(NumberUtil.parseLong(metric.getOrDefault("dsId", -1).toString()),
                metric.getOrDefault("dsName", "").toString()));
    }


    private void checkElasticsearch(CheckContext context) {
        // 采集指标
        CollectContext collectContext = new CollectContext();
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        ConsoleExporter consoleExporter = new ConsoleExporter();
        collectContext.setExporter(consoleExporter);
        collectorFactory.elasticSearchCollector().collect(collectContext);
        List<Metric> metricList = consoleExporter.getMetricList();
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        checkComponentAllStatus(context, metricList, null, SelfMonitorObjectTypeEnum.ELASTICSEARCH,
            metric -> new Pair<Long, String>(NumberUtil.parseLong(metric.getOrDefault("dsId", -1).toString()),
                metric.getOrDefault("dsName", "").toString()));
    }

    private void checkClickhouse(CheckContext context) {
        // 采集指标
        CollectContext collectContext = new CollectContext();
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        ConsoleExporter consoleExporter = new ConsoleExporter();
        collectContext.setExporter(consoleExporter);
        collectorFactory.clickhouseCollector().collect(collectContext);
        List<Metric> metricList = consoleExporter.getMetricList();
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        checkComponentAllStatus(context, metricList, null, SelfMonitorObjectTypeEnum.CLICKHOUSE,
            metric -> new Pair<Long, String>(NumberUtil.parseLong(metric.getOrDefault("dsId", -1).toString()),
                metric.getOrDefault("dsName", "").toString()));

    }


    private void checkHadoop(CheckContext context) {
        CollectContext collectContext = new CollectContext();
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        ConsoleExporter consoleExporter = new ConsoleExporter();
        collectContext.setExporter(consoleExporter);
        collectorFactory.hadoopCollector().collect(collectContext);
        List<Metric> metricList = consoleExporter.getMetricList();
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        checkComponentAllStatus(context, metricList, null, SelfMonitorObjectTypeEnum.HADOOP,
            metric -> new Pair<Long, String>(NumberUtil.parseLong(metric.getOrDefault("id", -1).toString()),
                metric.getOrDefault("clusterName", "").toString()));
    }


    private void checkRedis(CheckContext context) {
        CollectContext collectContext = new CollectContext();
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        ConsoleExporter consoleExporter = new ConsoleExporter();
        collectContext.setExporter(consoleExporter);
        collectorFactory.redisCollector().collect(collectContext);
        List<Metric> metricList = consoleExporter.getMetricList();
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        checkComponentAllStatus(context, metricList, null, SelfMonitorObjectTypeEnum.REDIS,
            metric -> new Pair<Long, String>(NumberUtil.parseLong(metric.getOrDefault("dsId", -1).toString()),
                metric.getOrDefault("dsName", "").toString()));

    }


    private void checkVictoriaMetrics(CheckContext context) {
        CollectContext collectContext = new CollectContext();
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        ConsoleExporter consoleExporter = new ConsoleExporter();
        collectContext.setExporter(consoleExporter);
        collectorFactory.victoriaMetricsLivedCollector().collect(collectContext);
        List<Metric> metricList = consoleExporter.getMetricList();
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        checkComponentAllStatus(context, metricList, null, SelfMonitorObjectTypeEnum.VICTORIA_METRICS,
            metric -> new Pair<Long, String>(NumberUtil.parseLong(metric.getOrDefault("dsId", -1).toString()),
                    metric.getOrDefault("dsName", "").toString()));

    }

    private void checkNebula(CheckContext checkContex) {
        CollectContext collectContext = new CollectContext();
        collectContext.setCommandList(CollUtil.newArrayList(CollectCommand.ALL));
        ConsoleExporter consoleExporter = new ConsoleExporter();
        collectContext.setExporter(consoleExporter);
        collectorFactory.nebulaCollector().collect(collectContext);
        List<Metric> metricList = consoleExporter.getMetricList();
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        checkComponentAllStatus(checkContex, metricList, null, SelfMonitorObjectTypeEnum.NEBULA,
            metric -> new Pair<Long, String>(NumberUtil.parseLong(metric.getOrDefault("dsId", -1).toString()),
                metric.getOrDefault("dsName", "").toString()));

    }

}
