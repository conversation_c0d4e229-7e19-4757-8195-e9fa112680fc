package com.eoi.jax.web.monitor.model.healthcheckinstance;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.repository.entity.TbHealthCheckInstance;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
public class HealthCheckInstanceResp {

    private Long id;

    /**
     * 检查id
     */
    private Long checkId;

    /**
     * 检查项id
     */
    private Long itemId;

    /**
     * 实例Id
     */
    private Long instanceId;

    /**
     * 实例名称
     */
    private String instanceName;

    /**
     * 实例信息
     */
    private Map<String, Object> instanceInfo;

    /**
     * 实例类型
     */
    private String instanceType;

    /**
     * 状态
     */
    private String status;

    /**
     * 告警内容
     */
    private String content;

    /**
     * 解决方案
     */
    private String solution;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人id
     */
    private Long createUser;

    /**
     * 更新人id
     */
    private Long updateUser;


    public HealthCheckInstanceResp fromEntity(TbHealthCheckInstance tbHealthCheckInstance) {
        HealthCheckInstanceResp resp = ModelBeanUtil.copyBean(tbHealthCheckInstance, this);
        if (StrUtil.isNotBlank(tbHealthCheckInstance.getInstanceInfo())) {
            resp.setInstanceInfo(JsonUtil.decode2Map(tbHealthCheckInstance.getInstanceInfo()));
        }
        return resp;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public Map<String, Object> getInstanceInfo() {
        return instanceInfo;
    }

    public void setInstanceInfo(Map<String, Object> instanceInfo) {
        this.instanceInfo = instanceInfo;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
