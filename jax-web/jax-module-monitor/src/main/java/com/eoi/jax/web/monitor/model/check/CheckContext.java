package com.eoi.jax.web.monitor.model.check;

import com.eoi.jax.web.monitor.check.CheckResultStorage;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
public class CheckContext {

    /**
     * 检查id
     */
    private Long checkId;

    private CheckResultStorage checkResultStorage;

    public CheckContext() {
    }

    public CheckContext(Long checkId, CheckResultStorage checkResultStorage) {
        this.checkId = checkId;
        this.checkResultStorage = checkResultStorage;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public CheckResultStorage getCheckResultStorage() {
        return checkResultStorage;
    }

    public void setCheckResultStorage(CheckResultStorage checkResultStorage) {
        this.checkResultStorage = checkResultStorage;
    }
}
