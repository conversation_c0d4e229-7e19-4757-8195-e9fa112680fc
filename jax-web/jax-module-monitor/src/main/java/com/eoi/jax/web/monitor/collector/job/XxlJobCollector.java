package com.eoi.jax.web.monitor.collector.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.eoi.jax.web.monitor.collector.AbstractCollector;
import com.eoi.jax.web.monitor.enumrate.CollectCommand;
import com.eoi.jax.web.monitor.model.collector.CollectContext;
import com.eoi.jax.web.monitor.model.collector.Metric;
import com.eoi.jax.web.xxl.core.model.XxlJobLog;
import com.eoi.jax.web.xxl.dao.XxlJobLogDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_XXL_JOB_FAILED;

/**
 * <AUTHOR>
 * @date 2024/3/21
 */
@Service
public class XxlJobCollector extends AbstractCollector {

    @Resource
    private XxlJobLogDao xxlJobLogDao;

    private Date lastEndDate;

    @Override
    public void initCollectFunction(Map<CollectCommand, Consumer<CollectContext>> localFnMap) {
        localFnMap.put(CollectCommand.JOB_FAILED, this::collectXxlJobLogMetric);
    }


    private void collectXxlJobLogMetric(CollectContext context) {
        List<Metric> metrics = CollUtil.newLinkedList();
        Date startDate = getStartTime(context), endDate = getEndTime();
        for (int i = 0, size = 1000; ; i++) {
            int start = i * size;
            List<XxlJobLog> xxlJobLogs = xxlJobLogDao.pageList(start, size, -1, -1, null,
                startDate, endDate, 2, null, null, null, true);
            if (CollUtil.isEmpty(xxlJobLogs)) {
                break;
            }
            for (XxlJobLog xxlJobLog : xxlJobLogs) {
                metrics.addAll(collectOneXxlJobLogMetric(xxlJobLog));
            }
        }
        lastEndDate = endDate;
        context.getExporter().export(metrics);
    }


    private List<Metric> collectOneXxlJobLogMetric(XxlJobLog xxlJobLog) {
        List<Metric> metrics = CollUtil.newLinkedList();
        Map<String, Object> labelMap = new LinkedHashMap<>();
        labelMap.put("id", xxlJobLog.getId());
        labelMap.put("jobGroup", xxlJobLog.getJobGroup());
        labelMap.put("jobId", xxlJobLog.getJobId());
        labelMap.put("jobName", xxlJobLog.getJobName());
        labelMap.put("executorName", xxlJobLog.getExecutorName());
        labelMap.put("triggerTimeMs", xxlJobLog.getTriggerTimeMs());
        labelMap.put("triggerCode", xxlJobLog.getTriggerCode());
        labelMap.put("handleCode", xxlJobLog.getHandleCode());
        boolean success = 200 == xxlJobLog.getTriggerCode() && 200 == xxlJobLog.getHandleCode();
        metrics.add(new Metric(JAX_XXL_JOB_FAILED.code(), labelMap, success ? 1 : 0));
        return metrics;
    }


    private Date getStartTime(CollectContext context) {
        if (context.getDebug()) {
            return DateUtil.offset(new Date(), DateField.DAY_OF_YEAR, -1);
        }
        Date lastDate;
        if (Objects.isNull(lastEndDate)) {
            lastDate = new Date(getEndTime().getTime() - 1000);
        } else {
            // 去除毫秒值
            long currentSeconds = (lastEndDate.getTime() / 1000 + 1) * 1000;
            lastDate = new Date(currentSeconds);
        }
        return lastDate;
    }


    private Date getEndTime() {
        // 获取当前时间的毫秒值
        long currentTimeMillis = System.currentTimeMillis();
        // 去除毫秒值
        long currentSeconds = currentTimeMillis / 1000;
        return new Date(currentSeconds * 1000);
    }

}
