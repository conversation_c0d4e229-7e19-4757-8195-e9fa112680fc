package com.eoi.jax.web.monitor.collector.component.internal;

import com.eoi.jax.web.core.util.JaxLoggerUtil;
import com.eoi.jax.web.monitor.collector.AbstractCollector;
import com.eoi.jax.web.monitor.enumrate.CollectCommand;
import com.eoi.jax.web.monitor.model.collector.CollectContext;
import com.eoi.jax.web.monitor.model.collector.Metric;
import com.eoi.jax.web.monitor.model.performance.MonitorRecord;
import com.eoi.jax.web.workflow.enumrate.DbType;
import dm.jdbc.driver.DmDriver;
import org.apache.dolphinscheduler.common.enums.Flag;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_DB_LIVED;
import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_DB_MAX_CONNECTIONS;
import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_DB_THREADS_AVAILABLE_CONNECTIONS;
import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_DB_THREADS_CONNECTIONS;
import static com.eoi.jax.web.monitor.enumrate.MetricNameEnum.JAX_DB_THREADS_RUNNING_CONNECTIONS;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Service
public class DbConnectionCollector extends AbstractCollector {
    private static final Logger LOGGER = JaxLoggerUtil.getScheduleJobLogger();

    private static final String VARIABLE_NAME = "variable_name";

    private static final String UNKNOW_DB_TYPE = "UNKNOW";

    @Resource
    private DataSource dataSource;

    @Override
    public void initCollectFunction(Map<CollectCommand, Consumer<CollectContext>> localFnMap) {
        localFnMap.put(CollectCommand.CPU_MEMORY_THREAD_RESOURCE, this::collectDbThreadMetric);
    }

    public void collectDbThreadMetric(CollectContext collectContext) {
        MonitorRecord monitorRecord = getCurrentDbPerformance();
        if (Objects.isNull(monitorRecord)) {
            return;
        }

        Map<String, Object> labelMap = new LinkedHashMap<>();
        labelMap.put("dbType", monitorRecord.getDbType());

        List<Metric> metricList = new ArrayList<>();
        metricList.add(new Metric(JAX_DB_LIVED.code(), labelMap, Flag.YES == monitorRecord.getState() ? 1 : 0));

        if (!UNKNOW_DB_TYPE.equals(monitorRecord.getDbType())) {
            if (monitorRecord.getMaxConnections() > 0) {
                metricList.add(new Metric(JAX_DB_MAX_CONNECTIONS.code(), labelMap, monitorRecord.getMaxConnections()));
            }
            if (monitorRecord.getThreadsConnections() > 0) {
                metricList.add(new Metric(JAX_DB_THREADS_CONNECTIONS.code(), labelMap, monitorRecord.getThreadsConnections()));
            }
            if (monitorRecord.getThreadsRunningConnections() > 0) {
                metricList.add(new Metric(JAX_DB_THREADS_RUNNING_CONNECTIONS.code(), labelMap,
                    monitorRecord.getThreadsRunningConnections()));
            }
            if (monitorRecord.getMaxConnections() > 0 && monitorRecord.getThreadsConnections() > 0) {
                metricList.add(new Metric(JAX_DB_THREADS_AVAILABLE_CONNECTIONS.code(), labelMap,
                    monitorRecord.getMaxConnections() - monitorRecord.getThreadsConnections()));
            }
        }
        collectContext.getExporter().export(metricList);

    }


    private MonitorRecord getCurrentDbPerformance() {
        try (Connection conn = dataSource.getConnection()) {
            String driverClassName = DriverManager.getDriver(conn.getMetaData().getURL()).getClass().getName();
            if (driverClassName.contains(DbType.MYSQL.toString().toLowerCase())) {
                return mysqlMonitorRecord(conn);
            } else if (driverClassName.contains(DbType.POSTGRESQL.toString().toLowerCase())) {
                return postgresqlMonitorRecord(conn);
            } else if (driverClassName.contains(DmDriver.class.getName())) {
                return dmMonitorRecord(conn);
            } else {
                return unknowMonitorRecord(conn);
            }
        } catch (Exception e) {
            LOGGER.error("获取db监控信息异常, SQLException: {}", e.getMessage(), e);
        }
        return null;
    }


    private MonitorRecord mysqlMonitorRecord(Connection conn) {
        MonitorRecord monitorRecord = new MonitorRecord();
        monitorRecord.setDate(new Date());
        monitorRecord.setDbType(DbType.MYSQL.getDescp());
        monitorRecord.setState(Flag.YES);

        try (Statement pstmt = conn.createStatement()) {
            try (ResultSet rs1 = pstmt.executeQuery("show global variables")) {
                while (rs1.next()) {
                    if ("MAX_CONNECTIONS".equalsIgnoreCase(rs1.getString(VARIABLE_NAME))) {
                        monitorRecord.setMaxConnections(Long.parseLong(rs1.getString("value")));
                    }
                }
            }

            try (ResultSet rs2 = pstmt.executeQuery("show global status")) {
                while (rs2.next()) {
                    if ("MAX_USED_CONNECTIONS".equalsIgnoreCase(rs2.getString(VARIABLE_NAME))) {
                        monitorRecord.setMaxUsedConnections(Long.parseLong(rs2.getString("value")));
                    } else if ("THREADS_CONNECTED".equalsIgnoreCase(rs2.getString(VARIABLE_NAME))) {
                        monitorRecord.setThreadsConnections(Long.parseLong(rs2.getString("value")));
                    } else if ("THREADS_RUNNING".equalsIgnoreCase(rs2.getString(VARIABLE_NAME))) {
                        monitorRecord.setThreadsRunningConnections(Long.parseLong(rs2.getString("value")));
                    }
                }
            }
        } catch (Exception e) {
            monitorRecord.setState(Flag.NO);
            LOGGER.error("SQLException ", e);
        }
        return monitorRecord;
    }


    private MonitorRecord postgresqlMonitorRecord(Connection conn) {
        MonitorRecord monitorRecord = new MonitorRecord();
        monitorRecord.setDate(new Date());
        monitorRecord.setState(Flag.YES);
        monitorRecord.setDbType(DbType.POSTGRESQL.getDescp());

        try (Statement pstmt = conn.createStatement()) {
            try (ResultSet rs1 = pstmt.executeQuery("select count(*) from pg_stat_activity;")) {
                if (rs1.next()) {
                    monitorRecord.setThreadsConnections(rs1.getInt("count"));
                }
            }

            try (ResultSet rs2 = pstmt.executeQuery("show max_connections")) {
                if (rs2.next()) {
                    monitorRecord.setMaxConnections(rs2.getInt("max_connections"));
                }
            }

            try (ResultSet rs3 = pstmt.executeQuery("select count(*) from pg_stat_activity pg where pg.state = 'active';")) {
                if (rs3.next()) {
                    monitorRecord.setThreadsRunningConnections(rs3.getInt("count"));
                }
            }
        } catch (Exception e) {
            monitorRecord.setState(Flag.NO);
            LOGGER.error("SQLException ", e);
        }
        return monitorRecord;
    }


    private MonitorRecord dmMonitorRecord(Connection conn) {
        MonitorRecord monitorRecord = new MonitorRecord();
        monitorRecord.setDate(new Date());
        monitorRecord.setState(Flag.YES);
        monitorRecord.setDbType("DM");

        try (Statement pstmt = conn.createStatement()) {
            try (ResultSet rs1 = pstmt.executeQuery("select count(*) as count from v$sessions")) {
                if (rs1.next()) {
                    monitorRecord.setThreadsConnections(rs1.getInt("count"));
                }
            }

            try (ResultSet rs2 = pstmt.executeQuery("select SF_GET_PARA_VALUE(2,'MAX_SESSIONS') as max_connections")) {
                if (rs2.next()) {
                    monitorRecord.setMaxConnections(rs2.getInt("max_connections"));
                }
            }

            try (ResultSet rs3 = pstmt.executeQuery("select count(*) as count from v$sessions where state='ACTIVE'")) {
                if (rs3.next()) {
                    monitorRecord.setThreadsRunningConnections(rs3.getInt("count"));
                }
            }
        } catch (Exception e) {
            monitorRecord.setState(Flag.NO);
            LOGGER.error("SQLException ", e);
        }
        return monitorRecord;
    }


    private MonitorRecord unknowMonitorRecord(Connection conn) {
        MonitorRecord monitorRecord = new MonitorRecord();
        monitorRecord.setDate(new Date());
        monitorRecord.setState(Flag.YES);
        monitorRecord.setDbType(UNKNOW_DB_TYPE);
        try {
            conn.getMetaData();
            return monitorRecord;
        } catch (Exception e) {
            LOGGER.error("SQLException ", e);
            monitorRecord.setState(Flag.NO);
        }
        return monitorRecord;
    }

}
