package com.eoi.jax.web.monitor.check;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.enumrate.SelfMonitorAlertSeverityEnum;
import com.eoi.jax.web.core.common.enumrate.SelfMonitorObjectTypeEnum;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.monitor.enumrate.CheckCategoryEnum;
import com.eoi.jax.web.monitor.enumrate.HealthCheckResultStatusEnum;
import com.eoi.jax.web.monitor.model.check.CheckContext;
import com.eoi.jax.web.monitor.model.check.HealthCheckInstanceResult;
import com.eoi.jax.web.monitor.model.check.HealthCheckResult;
import com.eoi.jax.web.monitor.model.collector.Metric;
import com.eoi.jax.web.monitor.model.selfmonitor.xxljob.AlertAnnotation;
import com.eoi.jax.web.monitor.plugin.SelfMonitorObjectHelper;
import com.eoi.jax.web.monitor.service.SelfMonitorAlarmRuleService;
import com.eoi.jax.web.monitor.service.SelfMonitorAlarmService;
import com.eoi.jax.web.repository.entity.TbSelfMonitorItem;
import com.eoi.jax.web.repository.entity.TbSolution;
import com.eoi.jax.web.repository.service.TbSolutionService;
import com.eoi.monitor.factory.AlarmDealService;
import com.eoi.monitor.factory.AlarmRuleDealFactory;
import com.eoi.monitor.model.monitor.MonitorAlarm;
import com.eoi.monitor.model.monitor.SelfMonitorRuleParam;
import com.eoi.monitor.model.vm.Point;
import com.eoi.monitor.model.vm.Sample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
public abstract class AbstractComponentCheckHandle extends AbstractHealthCheckHandle {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractComponentCheckHandle.class);

    @Resource
    private SystemConfigHolder systemConfigHolder;

    @Resource
    private JaxRepository jaxRepository;

    @Resource
    private TbSolutionService tbSolutionService;

    @Resource
    private SelfMonitorAlarmRuleService selfMonitorAlarmRuleService;

    @Resource
    private SelfMonitorAlarmService selfMonitorAlarmService;

    @Resource
    private SelfMonitorObjectHelper monitorObjectHelper;

    protected void checkComponentAllStatus(CheckContext context,
                                           List<Metric> metricList,
                                           List<String> metricNameEnumList,
                                           SelfMonitorObjectTypeEnum selfMonitorObjectTypeEnum,
                                           Function<Map<String, Object>, Pair<Long, String>> idCardFunction) {
        // 采集指标
        if (CollUtil.isEmpty(metricList)) {
            return;
        }
        // 过滤需要的指标
        metricList = metricList.stream().filter(it -> {
            if (CollUtil.isEmpty(metricNameEnumList)) {
                return true;
            }
            for (String metricNameEnum : metricNameEnumList) {
                if (it.getName().contains(metricNameEnum)) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());

        List<TbSelfMonitorItem> tbSelfMonitorItemList =
            jaxRepository.selfMonitorItemService().listByObjectType(selfMonitorObjectTypeEnum.code());
        // 过滤监控项
        tbSelfMonitorItemList = tbSelfMonitorItemList.stream().filter(it -> 1 == it.getIsAtomic()).filter(it -> {
            if (CollUtil.isEmpty(metricNameEnumList)) {
                return true;
            }
            for (String metricNameEnum : metricNameEnumList) {
                if (it.getMetricCode().equalsIgnoreCase(metricNameEnum)) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());


        for (TbSelfMonitorItem tbSelfMonitorItem : tbSelfMonitorItemList) {
            try {
                checkComponentItemStatus(context, metricList, selfMonitorObjectTypeEnum, idCardFunction, tbSelfMonitorItem);
            } catch (Exception e) {
                LOGGER.error("检查组件状态异常", e);
            }
        }
    }


    protected void checkComponentItemStatus(CheckContext context,
                                            List<Metric> metricList,
                                            SelfMonitorObjectTypeEnum selfMonitorObjectTypeEnum,
                                            Function<Map<String, Object>, Pair<Long, String>> idCardFunction,
                                            TbSelfMonitorItem tbSelfMonitorItem) {
        // 获取检查规则
        SelfMonitorRuleParam selfMonitorRuleParam = selfMonitorAlarmRuleService.getRule(tbSelfMonitorItem.getMetricCode());

        HealthCheckResult healthCheckResult = new HealthCheckResult(context.getCheckId(),
            CheckCategoryEnum.COMPONENT, selfMonitorObjectTypeEnum.message(), selfMonitorRuleParam.getItemName());

        List<Metric> checkMetricList = new LinkedList<>();
        Map<String, HealthCheckInstanceResult> instanceResultMap = new LinkedHashMap<>();
        for (Metric metric : metricList) {
            // 只处理当前监控项的指标
            if (!metric.getName().equalsIgnoreCase(tbSelfMonitorItem.getMetricCode())) {
                continue;
            }
            Pair<Long, String> idCard = idCardFunction.apply(metric.getLabels());
            instanceResultMap.put(identity(idCard), HealthCheckInstanceResult.createSuccess(idCard.getKey(), idCard.getValue(),
                selfMonitorObjectTypeEnum.code()));
            checkMetricList.add(metric);
        }

        // 执行检查规则, 判断是否有异常
        AlarmDealService service = AlarmRuleDealFactory.getService(selfMonitorRuleParam, getVmUrl(), System.currentTimeMillis());
        List<MonitorAlarm> monitorAlarmList;
        if (selfMonitorRuleParam.getIsAtomic()) {
            monitorAlarmList = service.checkAlarmInRule(convertSample(checkMetricList), true);
        } else {
            monitorAlarmList = service.executeRule(true);
        }
        monitorAlarmList = CollUtil.isEmpty(monitorAlarmList) ? CollUtil.newLinkedList() :
            monitorAlarmList.stream().filter(Objects::nonNull).collect(Collectors.toList());

        // 如果有异常，设置异常状态, 并设置处置建议
        if (CollUtil.isNotEmpty(monitorAlarmList)) {
            String solution = getSolution(selfMonitorRuleParam);
            for (MonitorAlarm monitorAlarm : monitorAlarmList) {
                if (!"OPEN".equalsIgnoreCase(monitorAlarm.getStatus())) {
                    continue;
                }
                HealthCheckInstanceResult instanceResult = instanceResultMap.get(identity(idCardFunction.apply(monitorAlarm.getLabels())));
                if (Objects.isNull(instanceResult)) {
                    LOGGER.error("未找到对应的实例, idCard:{}, instanceResultMap:{}", identity(idCardFunction.apply(monitorAlarm.getLabels())),
                        JsonUtil.encode(instanceResultMap));
                    continue;
                }
                SelfMonitorAlertSeverityEnum severityEnum = SelfMonitorAlertSeverityEnum.fromString(monitorAlarm.getSeverity());
                if (Objects.isNull(severityEnum) || severityEnum.level() > 3) {
                    instanceResult.setStatus(HealthCheckResultStatusEnum.EXCEPTION);
                } else {
                    instanceResult.setStatus(HealthCheckResultStatusEnum.SUGGESTION);
                }
                monitorAlarm.setObjectName(monitorObjectHelper.getOrCalObjectName(selfMonitorRuleParam.getObjectDef(), monitorAlarm,
                        monitorAlarm.getObjectId()));
                monitorAlarm.setInstanceName(monitorObjectHelper.getOrCalInstanceName(selfMonitorRuleParam.getObjectDef(), monitorAlarm));
                instanceResult.setContent(selfMonitorAlarmService.buildAlarmContent(monitorAlarm));
                instanceResult.setSolution(solution);
            }
        }

        healthCheckResult.setInstanceList(CollUtil.newArrayList(instanceResultMap.values()));

        context.getCheckResultStorage().store(CollUtil.newArrayList(healthCheckResult));
    }


    protected List<Sample> convertSample(List<Metric> metricList) {
        return metricList.stream().map(it -> {
            Sample sample = new Sample();
            sample.setPoint(new Point(it.getTimestampMs(), NumberUtil.toBigDecimal(it.getValue()).doubleValue()));
            if (CollUtil.isNotEmpty(it.getLabels())) {
                sample.setLabels(it.getLabels().entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toString())));
            }
            return sample;
        }).collect(Collectors.toList());
    }


    protected String getVmUrl() {
        String vmUrl = systemConfigHolder.getStr(SystemConfigEnum.JAX_METRICS.getKey(), "address");
        if (StrUtil.isBlank(vmUrl)) {
            return null;
        }
        vmUrl = StrUtil.addPrefixIfNot(vmUrl, "http://");
        return StrUtil.removePrefix(vmUrl, "/");
    }


    protected String getSolution(SelfMonitorRuleParam selfMonitorRuleParam) {
        AlertAnnotation alertAnnotation = BeanUtil.mapToBean(selfMonitorRuleParam.getAnnotations(), AlertAnnotation.class, false);
        TbSolution tbSolution = tbSolutionService.getById(alertAnnotation.getSolutionId());

        return tbSolution.getSolution();
    }

    protected String identity(Pair<Long, String> idCard) {
        return String.format("%s-%s", idCard.getKey(), idCard.getValue());
    }

}
