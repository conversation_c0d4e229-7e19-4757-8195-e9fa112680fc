package com.eoi.jax.web.monitor.util;


import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.monitor.model.collector.NodeConnectionResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.Socket;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
public final class NetworkUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(NetworkUtil.class);

    private static final int DEFAULT_TIMEOUT = 3000;

    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    private NetworkUtil() {
    }

    public static boolean checkIpPort(String ipPort) {
        Assert.notBlank(ipPort, "ipPort不能为空");
        String[] split = ipPort.split(":");
        Assert.isTrue(split.length == 2, "ipPort格式错误");
        return checkIpPort(split[0], Integer.parseInt(split[1]));
    }

    /**
     * 检测Ip和端口是否可用
     *
     * @param ip
     * @param port
     * @return
     */
    public static boolean checkIpPort(String ip, int port) {
        Socket socket = new Socket();
        try {
            socket.connect(new InetSocketAddress(ip, port), DEFAULT_TIMEOUT);
            //通过现有方法查看连通状态
            return socket.isConnected();
        } catch (Exception ignored) {
            return false;
        } finally {
            IoUtil.close(socket);
        }
    }

    /**
     * 检测Ip地址
     *
     * @param ip
     * @return
     */
    public static boolean checkIp(String ip) {
        try {
            return InetAddress.getByName(ip).isReachable(DEFAULT_TIMEOUT);
        } catch (IOException ignored) {
            return false;
        }
    }


    public static Pair<String, Integer> getUrlIpAndPort(String httpUrl) {
        try {
            URL url = new URL(httpUrl);
            String ip = url.getHost();
            if (!ReUtil.isMatch(IP_PATTERN, ip)) {
                InetAddress address = InetAddress.getByName(ip);
                ip = address.getHostAddress();
            }
            int port = url.getPort();
            return new Pair<>(ip, port);
        } catch (MalformedURLException | UnknownHostException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     *
     * @param httpUrl
     * @return true表示连接成功
     */
    public static boolean checkHttpConnected(String httpUrl) {
        try {
            Pair<String, Integer> ipAndPort = getUrlIpAndPort(httpUrl);
            return checkIpPort(ipAndPort.getKey(), ipAndPort.getValue());
        } catch (Exception ignore) {
            return false;
        }
    }


    /**
     * 检测服务节点是否存活
     *
     * @param address ip:prot,ip:port形式
     * @return
     */
    public static List<NodeConnectionResult> checkServerNodeLived(String address) {
        Assert.notBlank(address, "地址不能为空");

        List<String> split = StrUtil.splitTrim(address, ",");

        final Semaphore semaphore = new Semaphore(20);
        List<CompletableFuture<NodeConnectionResult>> completableFutureList = new LinkedList<>();
        for (String oneAddress : split) {
            try {
                semaphore.acquire();
                completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                    try {
                        String[] ipPort = oneAddress.split(":");
                        if (ipPort.length == 2 && NumberUtil.isInteger(ipPort[1])) {
                            String ip = ipPort[0];
                            if (!ReUtil.isMatch(IP_PATTERN, ip)) {
                                InetAddress add = InetAddress.getByName(ip);
                                ip = add.getHostAddress();
                            }
                            boolean lived = NetworkUtil.checkIpPort(ip, Integer.parseInt(ipPort[1]));
                            return new NodeConnectionResult(ipPort[0], Integer.parseInt(ipPort[1]), lived);
                        } else {
                            throw new RuntimeException(String.format("解析address:%s失败", oneAddress));
                        }
                    } catch (Exception e) {
                        LOGGER.error("address解析异常,不是一个正常的ipPort地址:{}", oneAddress, e);
                        return new NodeConnectionResult(oneAddress, null, false);
                    } finally {
                        semaphore.release();
                    }
                }, ThreadPoolUtil.THREAD_POOL));
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();

        return completableFutureList.stream().map(it -> it.getNow(null)).filter(Objects::nonNull).collect(Collectors.toList());
    }


}
