<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.eoi.jax</groupId>
        <artifactId>jax-web</artifactId>
        <version>1.11.5</version>
    </parent>

    <artifactId>jax-module-monitor</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-module-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-web-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-web-repository</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-module-ingestion</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-module-data-modeling</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-xxl-job</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-dolphinscheduler</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-module-monitor-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-web-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-pdfbox</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jaxb-api</artifactId>
                    <groupId>javax.xml.bind</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>