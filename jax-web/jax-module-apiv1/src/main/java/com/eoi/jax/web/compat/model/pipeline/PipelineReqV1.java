package com.eoi.jax.web.compat.model.pipeline;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/7
 */
public class PipelineReqV1 {
    private String pipelineName;
    private String pipelineType;
    private Map<String, Object> pipelineConfig;
    private String pipeDescription;
    private String clusterName;
    private String yarnSession;
    private String optsName;

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public String getPipelineType() {
        return pipelineType;
    }

    public void setPipelineType(String pipelineType) {
        this.pipelineType = pipelineType;
    }

    public Map<String, Object> getPipelineConfig() {
        return pipelineConfig;
    }

    public void setPipelineConfig(Map<String, Object> pipelineConfig) {
        this.pipelineConfig = pipelineConfig;
    }

    public String getPipeDescription() {
        return pipeDescription;
    }

    public void setPipeDescription(String pipeDescription) {
        this.pipeDescription = pipeDescription;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getYarnSession() {
        return yarnSession;
    }

    public void setYarnSession(String yarnSession) {
        this.yarnSession = yarnSession;
    }

    public String getOptsName() {
        return optsName;
    }

    public void setOptsName(String optsName) {
        this.optsName = optsName;
    }
}
