package com.eoi.jax.web.compat.model.app;

public enum PipelineStatusV1Enum {
    DRAFT("DRAFT"),
    WAITING_START("WAITING_START"),
    STARTING("STARTING"),
    START_FAILED("START_FAILED"),
    WAITING_STOP("WAITING_STOP"),
    STOPPING("STOPPING"),
    STOP_FAILED("STOP_FAILED"),
    STOPPED("STOPPED"),
    RESTARTING("RESTARTING"),
    DELETING("DELETING"),
    RUNNING("RUNNING"),
    FINISHED("FINISHED"),
    FAILED("FAILED"),
    UNKNOWN("UNKNOWN");

    private final String code;

    PipelineStatusV1Enum(String code) {
        this.code = code;
    }

    public boolean isEqual(String code) {
        return this.code.equals(code);
    }

    public String code() {
        return code;
    }

    public static boolean anyEqual(String code, PipelineStatusV1Enum... statuses) {
        for (PipelineStatusV1Enum status : statuses) {
            if (status.isEqual(code)) {
                return true;
            }
        }
        return false;
    }
}
