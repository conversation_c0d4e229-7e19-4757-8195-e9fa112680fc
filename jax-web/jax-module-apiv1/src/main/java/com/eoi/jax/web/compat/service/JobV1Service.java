package com.eoi.jax.web.compat.service;

import com.eoi.jax.web.compat.model.job.JobRespV1;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.ingestion.model.job.JobQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/8
 */
public interface JobV1Service {

    /**
     * 查询
     * @param req
     * @return
     */
    Paged<JobRespV1> query(JobQueryReq req);

    /**
     * 列表
     * @return
     */
    List<JobRespV1> list();

    /**
     * 详情
     * @param jobName
     * @return
     */
    JobRespV1 get(String jobName);

    /**
     * 详情
     * @param id
     * @return
     */
    JobRespV1 get(Long id);
}
