package com.eoi.jax.web.compat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.compat.model.pipeline.ClusterResourceValidateV1;
import com.eoi.jax.web.compat.model.pipeline.PipelineReqV1;
import com.eoi.jax.web.compat.service.PipelineResourceV1Service;
import com.eoi.jax.web.core.common.enumrate.PipelineTypeEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.MemorySize;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.ingestion.enumrate.ClusterTypeEnum;
import com.eoi.jax.web.ingestion.model.cluster.Cluster;
import com.eoi.jax.web.ingestion.model.cluster.ClusterResourcePool;
import com.eoi.jax.web.ingestion.model.pipelineconf.FlinkPipelineDefine;
import com.eoi.jax.web.ingestion.model.pipelineconf.SparkPipelineDefine;
import com.eoi.jax.web.ingestion.provider.manager.FlinkPipelineManager;
import com.eoi.jax.web.ingestion.provider.manager.SparkPipelineManager;
import com.eoi.jax.web.ingestion.service.ClusterService;
import com.eoi.jax.web.repository.entity.TbCluster;
import com.eoi.jax.web.repository.entity.TbPipeline;
import com.eoi.jax.web.repository.service.TbClusterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/6/14
 */
@Service
public class PipelineResourceV1ServiceImpl implements PipelineResourceV1Service {
    private static final Logger LOGGER = LoggerFactory.getLogger(PipelineResourceV1ServiceImpl.class);

    @Autowired
    private TbClusterService tbClusterService;
    @Autowired
    private ClusterService clusterService;
    @Lazy
    @Autowired
    private FlinkPipelineManager flinkPipelineManager;
    @Lazy
    @Autowired
    private SparkPipelineManager sparkPipelineManager;

    public TbCluster getCluster(PipelineReqV1 pipelineReq) {
        if (pipelineReq.getClusterName() != null) {
            return tbClusterService.getOne(
                    new LambdaQueryWrapper<TbCluster>().eq(TbCluster::getClusterName, pipelineReq.getClusterName())
            );
        } else if (PipelineTypeEnum.isFlink(pipelineReq.getPipelineType())) {
            Cluster cluster = clusterService.getDefaultFlinkCluster();
            return tbClusterService.getById(cluster.getId());
        } else {
            Cluster cluster = clusterService.getDefaultSparkCluster();
            return tbClusterService.getById(cluster.getId());
        }
    }

    public ClusterResourceValidateV1 checkResourcePool(PipelineReqV1 pipelineReq) {
        ClusterResourceValidateV1 validation = new ClusterResourceValidateV1();
        try {
            TbCluster tbCluster = getCluster(pipelineReq);
            TbPipeline tbPipeline = new TbPipeline();
            ModelBeanUtil.copyBean(pipelineReq, flinkPipelineManager);
            tbPipeline.setPipelineConfig(JsonUtil.encode(pipelineReq.getPipelineConfig()));
            tbPipeline.setClusterId(tbCluster.getId());
            ClusterResourcePool pool = clusterService.getResourcePool(tbCluster);
            String pipelineType = pipelineReq.getPipelineType();
            String clusterType = tbCluster.getClusterType();
            boolean isStreaming = PipelineTypeEnum.isStreaming(PipelineTypeEnum.fromString(pipelineType));
            boolean isBatch = PipelineTypeEnum.isBatch(PipelineTypeEnum.fromString(pipelineType));
            boolean isYarn = ClusterTypeEnum.YARN.equals(clusterType);
            boolean isFlinkStandalone = ClusterTypeEnum.FLINK_STANDALONE.equals(clusterType);
            boolean isSparkStandalone = ClusterTypeEnum.SPARK_STANDALONE.equals(clusterType);
            if (isStreaming && isYarn) {
                validation = checkFlinkYarn(flinkPipelineManager.genPipelineDefine(
                        flinkPipelineManager.pipelineCtx(tbPipeline)), pool);
            } else if (isStreaming && isFlinkStandalone) {
                validation = checkFlinkStandalone(flinkPipelineManager.genPipelineDefine(
                        flinkPipelineManager.pipelineCtx(tbPipeline)
                ), pool);
            } else if (isBatch && isYarn) {
                validation = checkSparkYarn(sparkPipelineManager.genPipelineDefine(
                        sparkPipelineManager.pipelineCtx(tbPipeline)
                ), pool);
            } else if (isBatch && isSparkStandalone) {
                validation = checkSparkStandalone(sparkPipelineManager.genPipelineDefine(
                        sparkPipelineManager.pipelineCtx(tbPipeline)
                ), pool);
            }
            validation.setClusterName(tbCluster.getClusterName());
            validation.setClusterType(tbCluster.getClusterType());
        } catch (Exception e) {
            LOGGER.warn("check resource error", e);
        }
        return validation;
    }

    public ClusterResourceValidateV1 checkFlinkStandalone(FlinkPipelineDefine pipeline, ClusterResourcePool pool) {
        ClusterResourceValidateV1 validation = new ClusterResourceValidateV1();
        if (!Boolean.TRUE.equals(pool.getFlinkGot())) {
            return validation;
        }
        int requireSlots = Integer.parseInt(pipeline.getParallelism());
        int availableSlots = pool.getFlink().getSlotsAvailable();
        if (availableSlots >= 0 && requireSlots > availableSlots) {
            validation.setResourceLacking(true);
            validation.setSlotLacking(true);
        }
        return validation;
    }

    public ClusterResourceValidateV1 checkSparkStandalone(SparkPipelineDefine pipeline, ClusterResourcePool pool) {
        ClusterResourceValidateV1 validation = new ClusterResourceValidateV1();
        if (!Boolean.TRUE.equals(pool.getSparkGot())) {
            return validation;
        }
        int executorCores = Integer.parseInt(pipeline.getExecutorCores());
        int driverCores = Integer.parseInt(pipeline.getDriverCores());
        int requireCores = executorCores + driverCores;
        int totalCores = pool.getSpark().getCores();
        int usedCores = pool.getSpark().getCoresUsed();
        int availableCores = totalCores - usedCores;
        if (availableCores >= 0 && requireCores > availableCores) {
            validation.setResourceLacking(true);
            validation.setCoreLacking(true);
        }
        return validation;
    }

    public ClusterResourceValidateV1 checkFlinkYarn(FlinkPipelineDefine pipeline, ClusterResourcePool pool) {
        ClusterResourceValidateV1 validation = new ClusterResourceValidateV1();
        if (!Boolean.TRUE.equals(pool.getYarnGot())) {
            return validation;
        }
        int parallelism = Integer.parseInt(pipeline.getParallelism());
        int yarnSlots = Integer.parseInt(pipeline.getYarnSlots());
        // 计算parallelism/slot向上取整
        int taskManagerCount = (parallelism + yarnSlots - 1) / yarnSlots;
        // 最少需要taskManagerCount＋1个cpu
        int requireCores = taskManagerCount + 1;
        int availableCores = pool.getYarn().getAvailableVirtualCores().intValue();
        if (availableCores >= 0 && requireCores > availableCores) {
            validation.setResourceLacking(true);
            validation.setCoreLacking(true);
        }
        int jobManagerMb = MemorySize.parse(
                pipeline.getYarnJobManagerMemory(),
                MemorySize.MemoryUnit.MEGA_BYTES
        ).getMebiBytes();
        int taskManagerMb = MemorySize.parse(
                pipeline.getYarnTaskManagerMemory(),
                MemorySize.MemoryUnit.MEGA_BYTES
        ).getMebiBytes();
        int requireMb = jobManagerMb + taskManagerMb * taskManagerCount;
        int availableMb = pool.getYarn().getAvailableMB().intValue();
        if (availableMb > 0 && requireMb > availableMb) {
            validation.setResourceLacking(true);
            validation.setMemoryLacking(true);
        }
        return validation;
    }

    public ClusterResourceValidateV1 checkSparkYarn(SparkPipelineDefine pipeline, ClusterResourcePool pool) {
        ClusterResourceValidateV1 validation = new ClusterResourceValidateV1();
        if (!Boolean.TRUE.equals(pool.getYarnGot())) {
            return validation;
        }
        int driverCores = Integer.parseInt(pipeline.getDriverCores());
        int executorCores = Integer.parseInt(pipeline.getExecutorCores());
        int numExecutors = Integer.parseInt(pipeline.getNumExecutors());
        int requireCores = driverCores + executorCores * numExecutors;
        int availableCores = pool.getYarn().getAvailableVirtualCores().intValue();
        if (availableCores >= 0 && requireCores > availableCores) {
            validation.setResourceLacking(true);
            validation.setCoreLacking(true);
        }
        int driverMb = MemorySize.parse(
                pipeline.getDriverMemory(),
                MemorySize.MemoryUnit.BYTES
        ).getMebiBytes();
        int executorMb = MemorySize.parse(
                pipeline.getExecutorMemory(),
                MemorySize.MemoryUnit.BYTES
        ).getMebiBytes();
        int requireMb = driverMb + executorMb * numExecutors;
        int availableMb = pool.getYarn().getAvailableMB().intValue();
        if (availableMb > 0 && requireMb > availableMb) {
            validation.setResourceLacking(true);
            validation.setMemoryLacking(true);
        }
        return validation;
    }
}
