package com.eoi.jax.web.compat.model.pipeline;

public class ClusterResourceValidateV1 {
    private String clusterName;
    private String clusterType;
    private boolean resourceLacking;
    private boolean coreLacking;
    private boolean memoryLacking;
    private boolean slotLacking;

    public String getClusterName() {
        return clusterName;
    }

    public ClusterResourceValidateV1 setClusterName(String clusterName) {
        this.clusterName = clusterName;
        return this;
    }

    public String getClusterType() {
        return clusterType;
    }

    public ClusterResourceValidateV1 setClusterType(String clusterType) {
        this.clusterType = clusterType;
        return this;
    }

    public boolean isResourceLacking() {
        return resourceLacking;
    }

    public ClusterResourceValidateV1 setResourceLacking(boolean resourceLacking) {
        this.resourceLacking = resourceLacking;
        return this;
    }

    public boolean isCoreLacking() {
        return coreLacking;
    }

    public ClusterResourceValidateV1 setCoreLacking(boolean coreLacking) {
        this.coreLacking = coreLacking;
        return this;
    }

    public boolean isMemoryLacking() {
        return memoryLacking;
    }

    public ClusterResourceValidateV1 setMemoryLacking(boolean memoryLacking) {
        this.memoryLacking = memoryLacking;
        return this;
    }

    public boolean isSlotLacking() {
        return slotLacking;
    }

    public ClusterResourceValidateV1 setSlotLacking(boolean slotLacking) {
        this.slotLacking = slotLacking;
        return this;
    }
}
