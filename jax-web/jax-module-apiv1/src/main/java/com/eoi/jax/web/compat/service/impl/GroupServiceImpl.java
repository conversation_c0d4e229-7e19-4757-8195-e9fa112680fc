package com.eoi.jax.web.compat.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.compat.model.app.ApplicationStatusV1Enum;
import com.eoi.jax.web.compat.model.marayarn.GroupInfoQueryReq;
import com.eoi.jax.web.compat.model.marayarn.GroupInfoResp;
import com.eoi.jax.web.compat.service.GroupService;
import com.eoi.jax.web.core.common.enumrate.ApplicationStatusEnum;
import com.eoi.jax.web.ingestion.model.opts.MarayarnOpts;
import com.eoi.jax.web.repository.search.query.ApplicationMaintenanceParam;
import com.eoi.jax.web.repository.search.result.ApplicationMaintenanceResult;
import com.eoi.jax.web.repository.service.TbApplicationService;
import com.eoi.jax.web.repository.util.MybatisOrderUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wenbo.gong on 2020/10/10
 */
@Service
public class GroupServiceImpl implements GroupService {

    @Autowired
    private TbApplicationService tbApplicationService;


    @Override
    public Page<ApplicationMaintenanceResult> listInfoPage(Long id, GroupInfoQueryReq req) {
        ApplicationMaintenanceParam param = new ApplicationMaintenanceParam();
        param.setSearch(req.getFilter().getSearch());
        param.setStatus(req.getFilter().getStatus());
        Page<ApplicationMaintenanceResult> page = new Page<>(req.getPage() + 1, req.getSize());
        page.setOrders(CollUtil.newArrayList(MybatisOrderUtil.getOrderItem("a.id", false)));

        Page<ApplicationMaintenanceResult> resultPage = tbApplicationService.getList(page, param);

        return resultPage;
    }

    @Override
    public List<GroupInfoResp.Item> listAppInfos(List<ApplicationMaintenanceResult> applications) {
        return applications.stream().map(app -> {
            GroupInfoResp.Item appItem = new GroupInfoResp.Item();
            appItem.setId(app.getId());
            appItem.setName(app.getName());
            if (StringUtils.isNotBlank(app.getOptsSetting())) {
                MarayarnOpts opts = JSONUtil.toBean(app.getOptsSetting(), MarayarnOpts.class);
                if (opts != null) {
                    appItem.setCpu(opts.getExecutorCpu());
                    appItem.setMemory(opts.getExecutorMemory());
                }
            }
            if (StringUtils.isNotBlank(app.getSetting())) {
                MarayarnOpts opts = JSONUtil.toBean(app.getSetting(), MarayarnOpts.class);
                if (opts.getExecutorCpu() != null) {
                    appItem.setCpu(opts.getExecutorCpu());
                }
                if (opts.getExecutorMemory() != null) {
                    appItem.setMemory(opts.getExecutorMemory());
                }
            }

            appItem.setStatus(ApplicationStatusV1Enum.mapApplicationStatusToV1Status(app.getStatus()));
            appItem.setTotalInstance(app.getInstanceCount());
            appItem.setDescription(app.getDescription());
            appItem.setRunningInstance(ApplicationStatusEnum.RUNNING.code().equalsIgnoreCase(app.getStatus()) ? app.getInstanceCount() :
                    0);
            appItem.setTrackUrl(app.getTrackUrl());
            return appItem;
        }).collect(Collectors.toList());
    }


}
