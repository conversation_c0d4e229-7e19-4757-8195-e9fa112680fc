package com.eoi.jax.web.compat.controller.xxljob;

import com.eoi.jax.web.core.model.ResponseV1;
import com.eoi.jax.web.xxl.service.XxlJobService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Date;

@RestController
@RequestMapping("api/v1/xxl-job/chart")
public class XxlChartControllerV1 {
    @Resource
    private XxlJobService xxlJobService;

    @Operation(summary = "获取summary")
    @GetMapping("summary")
    public ResponseV1 summary() {
        return ResponseV1.success(xxlJobService.dashboardInfo());
    }

    @Operation(summary = "获取detail")
    @GetMapping("detail")
    public ResponseV1 detail(@RequestParam(value = "start") Long start,
                           @RequestParam(value = "end") Long end) {
        Date startDate = start == null ? null : new Date(start);
        Date endDate = start == null ? null : new Date(end);
        return ResponseV1.success(xxlJobService.chartInfo(startDate, endDate));
    }
}
