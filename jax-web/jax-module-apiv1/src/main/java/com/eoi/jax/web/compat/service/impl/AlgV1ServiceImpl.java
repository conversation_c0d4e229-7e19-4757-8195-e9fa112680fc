package com.eoi.jax.web.compat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eoi.jax.web.compat.model.alg.AlgRespV1;
import com.eoi.jax.web.compat.model.alg.AlgUrlReqV1;
import com.eoi.jax.web.compat.service.AlgV1Service;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.exception.JaxException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.AppConfig;
import com.eoi.jax.web.core.config.ConfigLoader;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.ingestion.model.alg.AlgModel;
import com.eoi.jax.web.ingestion.model.alg.AlgQueryReq;
import com.eoi.jax.web.ingestion.model.alg.ModelValidateResp;
import com.eoi.jax.web.ingestion.provider.JaxValidatorProvider;
import com.eoi.jax.web.ingestion.provider.jar.JarFileUrlProvider;
import com.eoi.jax.web.ingestion.provider.validator.ValidateResult;
import com.eoi.jax.web.repository.entity.TbAlg;
import com.eoi.jax.web.repository.entity.TbJar;
import com.eoi.jax.web.repository.service.TbAlgService;
import com.eoi.jax.web.repository.service.TbJarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/8
 */
@Service
public class AlgV1ServiceImpl implements AlgV1Service {
    @Autowired
    private TbAlgService tbAlgService;
    @Autowired
    private TbJarService tbJarService;
    @Autowired
    private JarFileUrlProvider jarFileUrlProvider;
    @Autowired
    private JaxValidatorProvider jaxValidatorProvider;

    public List<AlgRespV1> list() {
        List<TbAlg> list = tbAlgService.list();
        return respFrom(list);
    }

    public Paged<AlgRespV1> query(AlgQueryReq req) {
        IPage<TbAlg> paged = tbAlgService.page(req.page(), req.query());
        long total = paged.getTotal();
        return new Paged<>(total, paged.getRecords().stream()
            .map(this::respFrom)
            .collect(Collectors.toList())
        );
    }

    public AlgRespV1 get(Long id) {
        TbAlg alg = tbAlgService.getById(id);
        return respFrom(alg);
    }

    public AlgRespV1 get(String algClass) {
        TbAlg alg = tbAlgService.getOne(new LambdaQueryWrapper<TbAlg>().eq(TbAlg::getAlgClass, algClass));
        return respFrom(alg);
    }

    public List<AlgRespV1> getUrl(AlgUrlReqV1 req) {
        List<TbAlg> algs = tbAlgService.list(
            new LambdaQueryWrapper<TbAlg>()
                .in(TbAlg::getAlgClass, req.getAlgClassList()));
        return algs.stream().map(this::genUrl).collect(Collectors.toList());
    }

    public ModelValidateResp validate(AlgModel model) {
        ModelValidateResp resp = new ModelValidateResp();
        if (model.getAlgClass().startsWith(AppConfig.PYTHON_VIRTUAL_PACKAGE_FLINK) ||
            model.getAlgClass().startsWith(AppConfig.PYTHON_VIRTUAL_PACKAGE_SPARK)) {
            // can not validate python, and return success directly
            resp.setSuccess(true);
            resp.setMessage("success");
            return resp;
        }
        TbAlg alg = tbAlgService.getOne(new LambdaQueryWrapper<TbAlg>().eq(TbAlg::getAlgClass, model.getAlgClass()));
        if (alg == null) {
            throw new BizException(ResponseCode.JOB_EMPTY.getCode(), "根据算法类路径[" + model.getAlgClass() + "]找不到算法");
        }
        ValidateResult result = jaxValidatorProvider.checkAlgModel(alg, model);
        resp.setSuccess(!result.getInvalid());
        resp.setMessage(result.getMessage());
        return resp;
    }

    public AlgRespV1 genUrl(TbAlg entity) {
        String baseUrl;
        try {
            baseUrl = ConfigLoader.load().getServer().getListenHttp();
        } catch (Exception e) {
            throw new JaxException(e);
        }
        TbJar tbJar = tbJarService.getById(entity.getJarId());
        String filePath = jarFileUrlProvider.touchJarFile(tbJar);
        String filename;
        String url;
        if (entity.getAlgClass().startsWith(AppConfig.PYTHON_VIRTUAL_PACKAGE_FLINK)) {
            filename = "jar-archive-" + System.currentTimeMillis() + ".jar";
            url = String.format("%s/api/v2/ingestion/jar-archive/%s?filename=%s",
                baseUrl,
                tbJar.getJarName(),
                filename);
        } else {
            filename = new File(filePath).getName();
            url = String.format("%s/%s/%s",
                baseUrl,
                AppConfig.JAX_JOB_JAR_DIR,
                filename);
        }
        AlgRespV1 resp = respFrom(entity, tbJar);
        resp.setAlgParameters(new ArrayList<>());
        resp.setFileName(filename);
        resp.setJarUrl(url);
        return resp;
    }

    public AlgRespV1 respFrom(TbAlg entity) {
        TbJar tbJar = tbJarService.getById(entity.getJarId());
        return respFrom(entity, tbJar);
    }

    public List<AlgRespV1> respFrom(List<TbAlg> entities) {
        List<TbJar> jars = tbJarService.list();
        Map<Long, TbJar> jarMap = jars.stream().collect(Collectors.toMap(TbJar::getId, i -> i));
        return entities.stream()
            .map(i -> respFrom(i, jarMap.get(i.getJarId())))
            .collect(Collectors.toList());
    }

    public AlgRespV1 respFrom(TbAlg entity, TbJar tbJar) {
        AlgRespV1 resp = new AlgRespV1();
        ModelBeanUtil.copyBean(entity, resp);
        resp.setAlgParameters(JsonUtil.decode2ListMap(entity.getAlgParameters()));
        if (tbJar != null) {
            resp.setJarName(tbJar.getJarName());
        }
        return resp;
    }
}
