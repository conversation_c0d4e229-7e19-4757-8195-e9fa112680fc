package com.eoi.jax.web.compat.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.compat.model.cluster.ClusterRespV1;
import com.eoi.jax.web.compat.model.flinkyarnsession.FlinkYarnSessionReqV1;
import com.eoi.jax.web.compat.model.flinkyarnsession.FlinkYarnSessionRespV1;
import com.eoi.jax.web.compat.model.opts.OptsRespV1;
import com.eoi.jax.web.compat.service.FlinkYarnSessionV1Service;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.ingestion.model.flinkyarnsession.FlinkYarnSessionCreateReq;
import com.eoi.jax.web.ingestion.model.flinkyarnsession.FlinkYarnSessionQueryReq;
import com.eoi.jax.web.ingestion.model.flinkyarnsession.FlinkYarnSessionResp;
import com.eoi.jax.web.ingestion.model.flinkyarnsession.FlinkYarnSessionUpdateReq;
import com.eoi.jax.web.ingestion.service.FlinkYarnSessionService;
import com.eoi.jax.web.repository.entity.TbCluster;
import com.eoi.jax.web.repository.entity.TbFlinkYarnSession;
import com.eoi.jax.web.repository.entity.TbOpts;
import com.eoi.jax.web.repository.service.TbClusterService;
import com.eoi.jax.web.repository.service.TbFlinkYarnSessionService;
import com.eoi.jax.web.repository.service.TbOptsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/9
 */
@Service
public class FlinkYarnSessionV1ServiceImpl implements FlinkYarnSessionV1Service {
    @Autowired
    private TbFlinkYarnSessionService tbFlinkYarnSessionService;
    @Autowired
    private TbClusterService tbClusterService;
    @Autowired
    private TbOptsService tbOptsService;
    @Autowired
    private ClusterV1ServiceImpl clusterV1Service;
    @Autowired
    private OptsV1ServiceImpl optsV1Service;
    @Autowired
    private FlinkYarnSessionService flinkYarnSessionService;

    public Paged<FlinkYarnSessionRespV1> query(FlinkYarnSessionQueryReq req) {
        Paged<FlinkYarnSessionResp> paged = flinkYarnSessionService.query(req);
        long total = paged.getTotal();
        List<FlinkYarnSessionRespV1> list = respFrom(paged.getList());
        return new Paged<>(total, list);
    }

    public FlinkYarnSessionRespV1 createOrUpdate(FlinkYarnSessionReqV1 v1) {
        TbFlinkYarnSession entity = tbFlinkYarnSessionService.getOne(
                new LambdaQueryWrapper<TbFlinkYarnSession>()
                        .eq(TbFlinkYarnSession::getYarnSessionName, v1.getYarnSessionName())
        );
        if (entity == null) {
            return create(v1);
        } else {
            return update(v1);
        }
    }

    public FlinkYarnSessionRespV1 create(FlinkYarnSessionReqV1 v1) {
        FlinkYarnSessionCreateReq req = toCreateReq(v1);
        FlinkYarnSessionResp resp;
        if (Boolean.TRUE.equals(v1.getWillPublish())) {
            resp = flinkYarnSessionService.publish(req);
        } else {
            resp = flinkYarnSessionService.create(req);
        }
        return respFrom(resp);
    }

    public FlinkYarnSessionRespV1 update(FlinkYarnSessionReqV1 v1) {
        FlinkYarnSessionUpdateReq req = toUpdateReq(v1);
        FlinkYarnSessionResp resp;
        if (Boolean.TRUE.equals(v1.getWillPublish())) {
            resp = flinkYarnSessionService.publish(req);
        } else {
            resp = flinkYarnSessionService.update(req);
        }
        return respFrom(resp);
    }

    public FlinkYarnSessionRespV1 delete(String yarnSessionName) {
        TbFlinkYarnSession entity = getByName(yarnSessionName);
        FlinkYarnSessionResp resp = flinkYarnSessionService.delete(entity.getId());
        return respFrom(resp);
    }

    public FlinkYarnSessionRespV1 get(String yarnSessionName) {
        TbFlinkYarnSession entity = getByName(yarnSessionName);
        FlinkYarnSessionResp resp = flinkYarnSessionService.get(entity.getId());
        return respFrom(resp);
    }

    public FlinkYarnSessionRespV1 start(String yarnSessionName) {
        TbFlinkYarnSession entity = getByName(yarnSessionName);
        FlinkYarnSessionResp resp = flinkYarnSessionService.start(entity.getId());
        return respFrom(resp);
    }

    public FlinkYarnSessionRespV1 stop(String yarnSessionName) {
        TbFlinkYarnSession entity = getByName(yarnSessionName);
        FlinkYarnSessionResp resp = flinkYarnSessionService.stop(entity.getId(), true);
        return respFrom(resp);
    }

    public TbFlinkYarnSession getByName(String yarnSessionName) {
        TbFlinkYarnSession entity = tbFlinkYarnSessionService.getOne(
                new LambdaQueryWrapper<TbFlinkYarnSession>()
                        .eq(TbFlinkYarnSession::getYarnSessionName, yarnSessionName)
        );
        if (entity == null) {
            throw new BizException(ResponseCode.FLINK_YARN_SESSION_NOT_EXIST);
        }
        return entity;
    }

    public FlinkYarnSessionRespV1 respFrom(FlinkYarnSessionResp resp) {
        TbCluster tbCluster = null;
        if (resp.getClusterId() != null) {
            tbCluster = tbClusterService.getById(resp.getClusterId());
        }
        TbOpts tbOpts = null;
        if (resp.getOptsId() != null) {
            tbOpts = tbOptsService.getById(resp.getOptsId());
        }
        return respFrom(resp, tbCluster, tbOpts);
    }

    public List<FlinkYarnSessionRespV1> respFrom(List<FlinkYarnSessionResp> entities) {
        List<TbCluster> clusterList = tbClusterService.list();
        List<TbOpts> optsList = tbOptsService.list();
        Map<Long, TbCluster> clusterMap = clusterList.stream().collect(Collectors.toMap(TbCluster::getId, i -> i));
        Map<Long, TbOpts> optsMap = optsList.stream().collect(Collectors.toMap(TbOpts::getId, i -> i));
        return entities.stream()
                .map(i -> respFrom(i, clusterMap.get(i.getClusterId()), optsMap.get(i.getOptsId())))
                .collect(Collectors.toList());
    }

    public FlinkYarnSessionRespV1 respFrom(FlinkYarnSessionResp resp, TbCluster tbCluster, TbOpts tbOpts) {
        FlinkYarnSessionRespV1 v1 = new FlinkYarnSessionRespV1();
        ModelBeanUtil.copyBean(resp, v1);
        if (tbCluster != null) {
            v1.setClusterName(tbCluster.getClusterName());
        }
        if (tbOpts != null) {
            v1.setOptsName(tbOpts.getOptsName());
        }
        v1.setCreateTime(resp.getCreateTime().getTime());
        v1.setUpdateTime(resp.getUpdateTime().getTime());
        return v1;
    }

    public FlinkYarnSessionCreateReq toCreateReq(FlinkYarnSessionReqV1 v1) {
        FlinkYarnSessionCreateReq req = new FlinkYarnSessionCreateReq();
        req.setYarnSessionName(v1.getYarnSessionName());
        req.setYarnSessionAlias(StrUtil.subPre(v1.getDescription(), 255));
        req.setYarnQueue(v1.getYarnQueue());
        req.setDescription(v1.getDescription());
        req.setJobManagerMemory(v1.getJobManagerMemory());
        req.setTaskManagerMemory(v1.getTaskManagerMemory());
        req.setSlots(v1.getSlots());
        req.setMaxMemory(v1.getMaxMemory());
        if (StrUtil.isNotEmpty(v1.getClusterName())) {
            ClusterRespV1 cluster = clusterV1Service.get(v1.getClusterName());
            req.setClusterId(cluster.getId());
        }
        if (StrUtil.isNotEmpty(v1.getOptsName())) {
            OptsRespV1 opts = optsV1Service.get(v1.getOptsName());
            req.setOptsId(opts.getId());
        }
        req.setConfList(new ArrayList<>());
        return req;
    }

    public FlinkYarnSessionUpdateReq toUpdateReq(FlinkYarnSessionReqV1 v1) {
        TbFlinkYarnSession entity = getByName(v1.getYarnSessionName());
        FlinkYarnSessionUpdateReq req = new FlinkYarnSessionUpdateReq();
        req.setId(entity.getId());
        req.setYarnSessionAlias(StrUtil.subPre(v1.getDescription(), 255));
        req.setYarnQueue(v1.getYarnQueue());
        req.setDescription(v1.getDescription());
        req.setJobManagerMemory(v1.getJobManagerMemory());
        req.setTaskManagerMemory(v1.getTaskManagerMemory());
        req.setSlots(v1.getSlots());
        req.setMaxMemory(v1.getMaxMemory());
        if (StrUtil.isNotEmpty(v1.getOptsName())) {
            OptsRespV1 opts = optsV1Service.get(v1.getOptsName());
            req.setOptsId(opts.getId());
        }
        req.setConfList(new ArrayList<>());
        return req;
    }
}
