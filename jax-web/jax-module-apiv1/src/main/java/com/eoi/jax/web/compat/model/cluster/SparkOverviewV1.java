package com.eoi.jax.web.compat.model.cluster;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/7
 */
public class SparkOverviewV1 {
    private String url;
    private Integer aliveWorkers;
    private Integer cores;
    private Integer coresUsed;
    private Integer memory;
    private Integer memoryUsed;
    private String status;
    private List<Map<String, Object>> activeApps;
    private List<Map<String, Object>> activeDrivers;
    private Integer appsRunning;
    private Integer driversDunning;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getAliveWorkers() {
        return aliveWorkers;
    }

    public void setAliveWorkers(Integer aliveWorkers) {
        this.aliveWorkers = aliveWorkers;
    }

    public Integer getCores() {
        return cores;
    }

    public void setCores(Integer cores) {
        this.cores = cores;
    }

    public Integer getCoresUsed() {
        return coresUsed;
    }

    public void setCoresUsed(Integer coresUsed) {
        this.coresUsed = coresUsed;
    }

    public Integer getMemory() {
        return memory;
    }

    public void setMemory(Integer memory) {
        this.memory = memory;
    }

    public Integer getMemoryUsed() {
        return memoryUsed;
    }

    public void setMemoryUsed(Integer memoryUsed) {
        this.memoryUsed = memoryUsed;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Map<String, Object>> getActiveApps() {
        return activeApps;
    }

    public void setActiveApps(List<Map<String, Object>> activeApps) {
        this.activeApps = activeApps;
    }

    public List<Map<String, Object>> getActiveDrivers() {
        return activeDrivers;
    }

    public void setActiveDrivers(List<Map<String, Object>> activeDrivers) {
        this.activeDrivers = activeDrivers;
    }

    public Integer getAppsRunning() {
        return appsRunning;
    }

    public void setAppsRunning(Integer appsRunning) {
        this.appsRunning = appsRunning;
    }

    public Integer getDriversDunning() {
        return driversDunning;
    }

    public void setDriversDunning(Integer driversDunning) {
        this.driversDunning = driversDunning;
    }
}
