package com.eoi.jax.web.compat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.compat.model.opts.OptsFlinkRespV1;
import com.eoi.jax.web.compat.model.opts.OptsMarayarnRespV1;
import com.eoi.jax.web.compat.model.opts.OptsRespV1;
import com.eoi.jax.web.compat.model.opts.OptsSparkRespV1;
import com.eoi.jax.web.compat.service.OptsV1Service;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.ingestion.enumrate.OptsTypeEnum;
import com.eoi.jax.web.ingestion.model.opts.FlinkOpts;
import com.eoi.jax.web.ingestion.model.opts.MarayarnOpts;
import com.eoi.jax.web.ingestion.model.opts.SparkOpts;
import com.eoi.jax.web.repository.entity.TbOpts;
import com.eoi.jax.web.repository.service.TbOptsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/7
 */
@Service
public class OptsV1ServiceImpl implements OptsV1Service {
    @Autowired
    private TbOptsService tbOptsService;

    public List<OptsRespV1> list() {
        List<TbOpts> list = tbOptsService.list();
        return list.stream().map(this::respFrom).collect(Collectors.toList());
    }

    public OptsRespV1 get(Long id) {
        TbOpts entity = tbOptsService.getById(id);
        return respFrom(entity);
    }

    public OptsRespV1 get(String optsName) {
        TbOpts entity = tbOptsService.getOne(new LambdaQueryWrapper<TbOpts>().eq(TbOpts::getOptsName, optsName));
        if (entity == null) {
            throw new BizException(ResponseCode.OPTS_NOT_EXIST);
        }
        return respFrom(entity);
    }

    public OptsRespV1 respFrom(TbOpts entity) {
        if (OptsTypeEnum.FLINK.equals(entity.getOptsType())) {
            return respFromFlink(entity);
        }
        if (OptsTypeEnum.SPARK.equals(entity.getOptsType())) {
            return respFromSpark(entity);
        }
        if (OptsTypeEnum.MARAYARN.equals(entity.getOptsType())) {
            return respFromMarayarn(entity);
        }
        return null;
    }

    public OptsFlinkRespV1 respFromFlink(TbOpts entity) {
        OptsFlinkRespV1 resp = new OptsFlinkRespV1();
        resp.setId(entity.getId());
        resp.setFlinkOptsName(entity.getOptsName());
        resp.setOptsDescription(entity.getOptsDescription());
        resp.setVersion(entity.getVersion());
        resp.setCreateTime(entity.getCreateTime().getTime());
        resp.setUpdateTime(entity.getUpdateTime().getTime());
        FlinkOpts setting = JsonUtil.decode(entity.getSetting(), FlinkOpts.class);
        ModelBeanUtil.copyBean(setting, resp);
        return resp;
    }

    public OptsSparkRespV1 respFromSpark(TbOpts entity) {
        OptsSparkRespV1 resp = new OptsSparkRespV1();
        resp.setId(entity.getId());
        resp.setSparkOptsName(entity.getOptsName());
        resp.setOptsDescription(entity.getOptsDescription());
        resp.setVersion(entity.getVersion());
        resp.setCreateTime(entity.getCreateTime().getTime());
        resp.setUpdateTime(entity.getUpdateTime().getTime());
        SparkOpts setting = JsonUtil.decode(entity.getSetting(), SparkOpts.class);
        ModelBeanUtil.copyBean(setting, resp);
        return resp;
    }

    public OptsRespV1 respFromMarayarn(TbOpts entity) {
        OptsMarayarnRespV1 resp = new OptsMarayarnRespV1();
        resp.setId(entity.getId());
        resp.setMarayarnOptsName(entity.getOptsName());
        resp.setOptsDescription(entity.getOptsDescription());
        resp.setVersion(entity.getVersion());
        resp.setCreateTime(entity.getCreateTime().getTime());
        resp.setUpdateTime(entity.getUpdateTime().getTime());
        MarayarnOpts setting = JsonUtil.decode(entity.getSetting(), MarayarnOpts.class);
        ModelBeanUtil.copyBean(setting, resp);
        return resp;
    }
}
