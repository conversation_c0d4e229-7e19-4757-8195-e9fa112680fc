package com.eoi.jax.web.compat.service;


import com.eoi.jax.web.compat.xxljob.XxlJobInfoV1;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobInfo;
import com.eoi.jax.web.xxl.model.ChartInfo;
import com.eoi.jax.web.xxl.model.DashboardInfo;
import com.eoi.jax.web.xxl.model.JobGlueReq;
import com.eoi.jax.web.xxl.model.JobInfoQueryParam;

import java.util.Date;
import java.util.List;

/**
 * core job action for xxl-job
 *
 * <AUTHOR> 2016-5-28 15:30:33
 */
@SuppressWarnings("all")
public interface XxlJobV1Service {

    /**
     * page list
     *
     * @param start
     * @param length
     * @param jobGroup
     * @param jobDesc
     * @param executorHandler
     * @param author
     * @return
     */
    Paged<XxlJobInfoV1> pageList(JobInfoQueryParam jobInfoQueryParam);

    /**
     * @return
     */
    List<XxlJobInfoV1> pipelineList();

    /**
     * @return
     */
    List<XxlJobInfoV1> ruleList();

    /**
     * create job
     *
     * @param jobInfo
     * @return
     */
    XxlJobInfoV1 create(AbstractXxlJobInfo jobInfo);

    /**
     * update job
     *
     * @param jobInfo
     * @return
     */
    XxlJobInfoV1 update(AbstractXxlJobInfo jobInfo);

    /**
     * remove job
     * *
     *
     * @param id
     * @return
     */
    XxlJobInfoV1 remove(int id);

    /**
     * get job
     *
     * @param id
     * @return
     */
    XxlJobInfoV1 get(int id);

    /**
     * start job
     *
     * @param id
     * @return
     */
    XxlJobInfoV1 start(int id);

    /**
     * stop job
     *
     * @param id
     * @return
     */
    XxlJobInfoV1 stop(int id);

    /**
     * update glueSource, glueRemark
     *
     * @param req
     * @return
     */
    XxlJobInfoV1 updateGlue(JobGlueReq req);

    /**
     * dashboard info
     *
     * @return
     */
    DashboardInfo dashboardInfo();

    /**
     * chart info
     *
     * @param startDate
     * @param endDate
     * @return
     */
    ChartInfo chartInfo(Date startDate, Date endDate);

}
