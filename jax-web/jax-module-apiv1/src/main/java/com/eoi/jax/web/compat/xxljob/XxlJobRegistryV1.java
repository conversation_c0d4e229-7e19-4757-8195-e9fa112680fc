package com.eoi.jax.web.compat.xxljob;

import cn.hutool.core.bean.BeanUtil;
import com.eoi.jax.web.xxl.core.model.XxlJobRegistry;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/9/30.
 */
public class XxlJobRegistryV1 {

    private int id;
    private String registryGroup;
    private String registryKey;
    private String registryValue;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZZZ", timezone = "GMT+0")
    private Date updateTime;

    public static XxlJobRegistryV1 copyBean(XxlJobRegistry xxlJobRegistry) {
        if (xxlJobRegistry == null) {
            return null;
        }
        XxlJobRegistryV1 v1 = new XxlJobRegistryV1();
        BeanUtil.copyProperties(xxlJobRegistry, v1);
        return v1;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getRegistryGroup() {
        return registryGroup;
    }

    public void setRegistryGroup(String registryGroup) {
        this.registryGroup = registryGroup;
    }

    public String getRegistryKey() {
        return registryKey;
    }

    public void setRegistryKey(String registryKey) {
        this.registryKey = registryKey;
    }

    public String getRegistryValue() {
        return registryValue;
    }

    public void setRegistryValue(String registryValue) {
        this.registryValue = registryValue;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
