package com.eoi.jax.web.compat.service;

import com.eoi.jax.web.compat.model.opts.OptsRespV1;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/7
 */
public interface OptsV1Service {

    /**
     * 列表
     *
     * @return
     */
    List<OptsRespV1> list();

    /**
     * 详情
     *
     * @param id
     * @return
     */
    OptsRespV1 get(Long id);

    /**
     * 详情
     *
     * @param optsName
     * @return
     */
    OptsRespV1 get(String optsName);
}
