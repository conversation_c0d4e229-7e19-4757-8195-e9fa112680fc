package com.eoi.jax.web.compat.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

public abstract class BaseQuery<T> {
    private Integer page;
    private Integer size;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Page<T> page() {
        if (page != null && size != null) {
            return new Page<>(page + 1L, size);
        }
        //mybatis 分页 从1开始...
        return new Page<>(1, 1000);
    }

}
