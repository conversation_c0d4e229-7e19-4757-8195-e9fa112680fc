package com.eoi.jax.web.compat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.eoi.jax.web.compat.service.XxlJobV1Service;
import com.eoi.jax.web.compat.xxljob.XxlJobInfoV1;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobInfo;
import com.eoi.jax.web.xxl.model.ChartInfo;
import com.eoi.jax.web.xxl.model.DashboardInfo;
import com.eoi.jax.web.xxl.model.JobGlueReq;
import com.eoi.jax.web.xxl.model.JobInfoQueryParam;
import com.eoi.jax.web.xxl.service.XxlJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zsc
 * @create 2023/6/27 9:52
 */
@Service
public class XxlJobV1ServiceImpl implements XxlJobV1Service {

    @Autowired
    private XxlJobService xxlJobService;

    @Override
    public Paged<XxlJobInfoV1> pageList(JobInfoQueryParam jobInfoQueryParam) {
        Paged<AbstractXxlJobInfo> paged = xxlJobService.pageList(jobInfoQueryParam);

        Paged<XxlJobInfoV1> result = new Paged<>();
        if (paged != null) {
            result.setTotal(paged.getTotal());
            if (paged.getList() != null && paged.getList().size() > 0) {
                List<XxlJobInfoV1> list = paged.getList().stream().map(x -> getXxlJobInfoV1(x)).collect(Collectors.toList());
                result.setList(list);
            } else {
                result.setList(new LinkedList<>());
            }
        }
        return result;
    }

    private XxlJobInfoV1 getXxlJobInfoV1(AbstractXxlJobInfo xxlJobInfo) {
        if (xxlJobInfo == null) {
            return null;
        }
        XxlJobInfoV1 v1 = new XxlJobInfoV1();
        BeanUtil.copyProperties(xxlJobInfo, v1);
        return v1;
    }

    @Override
    public List<XxlJobInfoV1> pipelineList() {
        List<AbstractXxlJobInfo> list = xxlJobService.pipelineList();
        if (list != null && list.size() > 0) {
            return list.stream().map(x -> getXxlJobInfoV1(x)).collect(Collectors.toList());
        } else {
            return new LinkedList<>();
        }
    }

    @Override
    public List<XxlJobInfoV1> ruleList() {
        List<AbstractXxlJobInfo> list = xxlJobService.ruleList();
        if (list != null && list.size() > 0) {
            return list.stream().map(x -> getXxlJobInfoV1(x)).collect(Collectors.toList());
        } else {
            return new LinkedList<>();
        }
    }

    @Override
    public XxlJobInfoV1 create(AbstractXxlJobInfo jobInfo) {
        return getXxlJobInfoV1(xxlJobService.create(jobInfo));
    }

    @Override
    public XxlJobInfoV1 update(AbstractXxlJobInfo jobInfo) {
        return getXxlJobInfoV1(xxlJobService.update(jobInfo));
    }

    @Override
    public XxlJobInfoV1 remove(int id) {
        return getXxlJobInfoV1(xxlJobService.remove(id));
    }

    @Override
    public XxlJobInfoV1 get(int id) {
        return getXxlJobInfoV1(xxlJobService.get(id));
    }

    @Override
    public XxlJobInfoV1 start(int id) {
        return getXxlJobInfoV1(xxlJobService.start(id));
    }

    @Override
    public XxlJobInfoV1 stop(int id) {
        return getXxlJobInfoV1(xxlJobService.stop(id));
    }

    @Override
    public XxlJobInfoV1 updateGlue(JobGlueReq req) {
        return getXxlJobInfoV1(xxlJobService.updateGlue(req));
    }

    @Override
    public DashboardInfo dashboardInfo() {
        return xxlJobService.dashboardInfo();
    }

    @Override
    public ChartInfo chartInfo(Date startDate, Date endDate) {
        return xxlJobService.chartInfo(startDate, endDate);
    }
}
