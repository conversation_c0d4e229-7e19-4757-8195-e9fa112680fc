package com.eoi.jax.web.compat.controller.xxljob;

import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.compat.xxljob.XxlJobLogV1;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.ResponseV1;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobInfo;
import com.eoi.jax.web.xxl.core.model.XxlJobLog;
import com.eoi.jax.web.xxl.core.scheduler.XxlJobScheduler;
import com.eoi.jax.web.xxl.service.IXxlJobInfoService;
import com.eoi.jax.web.xxl.dao.XxlJobLogDao;
import com.eoi.jax.web.xxl.model.HandleMessage;
import com.eoi.jax.web.xxl.model.HandleTypeEnum;
import com.eoi.jax.web.xxl.model.JobLogCatReq;
import com.eoi.jax.web.xxl.model.JobLogClearReq;
import com.xxl.job.core.biz.ExecutorBiz;
import com.xxl.job.core.biz.model.KillParam;
import com.xxl.job.core.biz.model.LogParam;
import com.xxl.job.core.biz.model.LogResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.util.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * index controller
 *
 * <AUTHOR> 2015-12-19 16:13:16
 */
@SuppressWarnings("all")
@RestController
@RequestMapping("api/v1/xxl-job/log")
public class XxlJobLogControllerV1 {
    private static Logger logger = LoggerFactory.getLogger(XxlJobLogControllerV1.class);

    @Resource
    private IXxlJobInfoService xxlJobInfoService;
    @Resource
    private XxlJobLogDao xxlJobLogDao;

    @Operation(summary = "列表")
    @GetMapping
    public ResponseV1 list(
        @RequestParam(value = "pageIndex", required = false, defaultValue = "0") int pageIndex,
        @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
        @RequestParam(value = "jobGroup", required = false, defaultValue = "-1") Integer jobGroup,
        @RequestParam(value = "jobId", required = false, defaultValue = "-1") Integer jobId,
        @RequestParam(value = "logStatus", required = false, defaultValue = "-1") Integer logStatus,
        @RequestParam(value = "jobNameDesc", required = false, defaultValue = "") String jobNameDesc,
        @RequestParam(value = "triggerTimeStart", required = false) Long triggerTimeStart,
        @RequestParam(value = "triggerTimeEnd", required = false) Long triggerTimeEnd,
        @RequestParam(value = "glueType", required = false, defaultValue = "") String glueType,
        @RequestParam(value = "orderBy", required = false, defaultValue = "") String orderBy,
        @RequestParam(value = "isAsc", required = false, defaultValue = "false") Boolean isAsc) {
        if (pageIndex < 0) {
            pageIndex = 0;
        }
        if (pageSize < 0) {
            pageSize = 10;
        }
        int start = pageIndex * pageSize;
        int length = pageSize;
        Date startDate = triggerTimeStart == null ? null : new Date(triggerTimeStart);
        Date endDate = triggerTimeEnd == null ? null : new Date(triggerTimeEnd);
        List<XxlJobLog> list = xxlJobLogDao
                .pageList(start, length, jobGroup, jobId, jobNameDesc, startDate, endDate,
                        logStatus, glueType, null, orderBy, isAsc);
        long count = xxlJobLogDao.pageListCount(start, length, jobGroup, jobId, jobNameDesc, startDate,
                endDate, logStatus, glueType, null);

        if (list != null && list.size() > 0) {
            return new ResponseV1().setRetCode(ResponseCode.SUCCESS).
                    setEntity(list.stream().map(x -> XxlJobLogV1.copyBean(x)).collect(Collectors.toList())).
                    setTotal(count);
        }
        return new ResponseV1().setRetCode(ResponseCode.SUCCESS).setEntity(list).setTotal(count);
    }

    @Operation(summary = "详情")
    @GetMapping("{id}")
    public ResponseV1 detail(@PathVariable("id") Integer id) {
        XxlJobLog jobLog = xxlJobLogDao.load(id);
        if (jobLog == null) {
            throw new BizException(ResponseCode.XXL_LOG_NOT_EXIST);
        }
        return ResponseV1.success(XxlJobLogV1.copyBean(jobLog));
    }

    @Operation(summary = "删除")
    @DeleteMapping("{id}")
    public ResponseV1 delete(@PathVariable("id") Integer id) {
        int count = xxlJobLogDao.deleteById(id);
        return ResponseV1.success(count);
    }

    @Operation(summary = "kill")
    @PutMapping("{id}/kill")
    public ResponseV1 kill(@PathVariable("id") Integer id) {
        // base check
        XxlJobLog log = xxlJobLogDao.load(id);
        AbstractXxlJobInfo jobInfo = xxlJobInfoService.loadById(log.getJobId());
        if (jobInfo == null) {
            throw new BizException(ResponseCode.XXL_JOB_NOT_EXIST);
        }
        if (ReturnT.SUCCESS_CODE != log.getTriggerCode()) {
            throw new BizException(ResponseCode.XXL_LOG_KILL_FAILED);
        }

        // request of kill
        ReturnT<String> runResult = null;
        try {
            ExecutorBiz executorBiz = XxlJobScheduler.getExecutorBiz(log.getExecutorAddress());
            runResult = executorBiz.kill(new KillParam(jobInfo.getId()));
        } catch (Exception e) {
            logger.error("kill failed", e);
            throw new BizException(ResponseCode.XXL_LOG_KILL_FAILED, e);
        }

        if (ReturnT.SUCCESS_CODE != runResult.getCode()) {
            logger.warn("kill failed {}", runResult.getMsg());
            throw new BizException(ResponseCode.XXL_LOG_KILL_FAILED);
        }

        log.setHandleCode(ReturnT.FAIL_CODE);
        log.setHandleMsg(JsonUtil.encode(Collections.singletonList(
                new HandleMessage(HandleTypeEnum.KILL.getTitle())
        )));
        log.setHandleTime(new Date());
        xxlJobLogDao.updateHandleInfo(log);
        return new ResponseV1().setRetCode(ResponseCode.SUCCESS).setRetMsg(runResult.getMsg());
    }

    @Operation(summary = "cat")
    @PutMapping("{id}/cat")
    public ResponseV1 cat(@PathVariable("id") Integer id, @RequestBody JobLogCatReq req) {
        try {
            ExecutorBiz executorBiz = XxlJobScheduler.getExecutorBiz(req.getExecutorAddress());
            if (executorBiz == null) {
                return ResponseV1.success(
                        new LogResult(0, 0, "No Log", true)
                );
            }
            ReturnT<LogResult> logResult = executorBiz.log(
                    new LogParam(req.getFromLogTime(), id, req.getFromLineNum())
            );

            // is end
            if (logResult.getContent() != null && logResult.getContent().getFromLineNum() > logResult.getContent().getToLineNum()) {
                XxlJobLog jobLog = xxlJobLogDao.load(id);
                if (jobLog.getHandleCode() > 0) {
                    logResult.getContent().setEnd(true);
                }
            }

            return ResponseV1.success(logResult.getContent());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BizException(ResponseCode.XXL_LOG_CAT_FAILED, e);
        }
    }

    @Operation(summary = "clear")
    @PutMapping("clear")
    public ResponseV1 clear(@RequestBody JobLogClearReq req) {
        Date clearBeforeTime = null;
        int clearBeforeNum = 0;
        int type = req.getType();
        if (type == 1) {
            // 清理一个月之前日志数据
            clearBeforeTime = DateUtil.addMonths(new Date(), -1);
        } else if (type == 2) {
            // 清理三个月之前日志数据
            clearBeforeTime = DateUtil.addMonths(new Date(), -3);
        } else if (type == 3) {
            // 清理六个月之前日志数据
            clearBeforeTime = DateUtil.addMonths(new Date(), -6);
        } else if (type == 4) {
            // 清理一年之前日志数据
            clearBeforeTime = DateUtil.addYears(new Date(), -1);
        } else if (type == 5) {
            // 清理一千条以前日志数据
            clearBeforeNum = 1000;
        } else if (type == 6) {
            // 清理一万条以前日志数据
            clearBeforeNum = 10000;
        } else if (type == 7) {
            // 清理三万条以前日志数据
            clearBeforeNum = 30000;
        } else if (type == 8) {
            // 清理十万条以前日志数据
            clearBeforeNum = 100000;
        } else if (type == 9) {
            // 清理所有日志数据
            clearBeforeNum = 0;
        } else {
            throw new BizException(ResponseCode.XXL_LOG_CLEAR_INVALID);
        }

        List<Long> logIds = null;
        do {
            logIds = xxlJobLogDao.findClearLogIds(req.getJobGroup(), req.getJobId(), clearBeforeTime, clearBeforeNum, 1000);
            if (CollUtil.isNotEmpty(logIds)) {
                xxlJobLogDao.clearLog(logIds);
            }
        } while (CollUtil.isNotEmpty(logIds));

        return ResponseV1.success();
    }

}
