<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jax-web</artifactId>
        <groupId>com.eoi.jax</groupId>
        <version>1.11.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jax-module-apiv1</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-web-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-web-repository</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-module-data-modeling</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-module-ingestion</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-xxl-job</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>