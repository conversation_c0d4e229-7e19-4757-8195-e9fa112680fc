<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jax-web</artifactId>
        <groupId>com.eoi.jax</groupId>
        <version>1.11.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jax-web-test</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eoi.jax</groupId>
            <artifactId>jax-web-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.5.1</version>
        </dependency>

        <dependency>
            <groupId>com.playtika.testcontainers</groupId>
            <artifactId>embedded-mysql</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eoi.testcontainers</groupId>
            <artifactId>embedded-dm</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eoi.testcontainers</groupId>
            <artifactId>embedded-opengauss</artifactId>
        </dependency>
    </dependencies>
</project>