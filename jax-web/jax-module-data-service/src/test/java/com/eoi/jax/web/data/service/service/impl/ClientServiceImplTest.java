package com.eoi.jax.web.data.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.service.SpringUnitTestConfig;
import com.eoi.jax.web.data.service.model.api.ApiSimpleResp;
import com.eoi.jax.web.data.service.model.client.*;
import com.eoi.jax.web.repository.entity.TbClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class ClientServiceImplTest {

    @Autowired
    private ClientServiceImpl clientServiceImplUnderTest;

    @Before
    public void setUp() {
        clientServiceImplUnderTest = new ClientServiceImpl(null);
        ReflectionTestUtils.setField(clientServiceImplUnderTest, "tbClientService", null);
        ReflectionTestUtils.setField(clientServiceImplUnderTest, "tbClientApiService", null);
        ReflectionTestUtils.setField(clientServiceImplUnderTest, "tagService", null);
        ReflectionTestUtils.setField(clientServiceImplUnderTest, "tagRelationService", null);
        ReflectionTestUtils.setField(clientServiceImplUnderTest, "kafkaScramProcessingService", null);
    }

    @Test
    public void testGetByApiKey() {
        // Setup
        // Run the test
        final TbClient result = clientServiceImplUnderTest.getByApiKey("apiKey");

        // Verify the results
    }

    @Test
    public void testQuery() {
        // Setup
        final ClientQueryReq req = new ClientQueryReq();
        req.setSize(0);
        final ClientQueryFilterReq filter = new ClientQueryFilterReq();
        filter.setUpdateTime("updateTime");
        filter.setName("name");
        filter.setApiKey("apiKey");
        req.setFilter(filter);

        // Run the test
        final Paged<ClientResp> result = clientServiceImplUnderTest.query(req);

        // Verify the results
    }

    @Test
    public ClientResp testCreate() {
        // Setup
        final ClientCreateReq req = new ClientCreateReq();
        req.setName("name");
        req.setApiKey("apiKey");
        req.setSecretKey("secretKey");
        req.setDescription("description");
        req.setVerifySecret(false);

        // Run the test
        final ClientResp result = clientServiceImplUnderTest.create(req);

        final ClientUpdateReq req2 = new ClientUpdateReq();
        BeanUtil.copyProperties(result,req2);
        final ClientResp result2 = clientServiceImplUnderTest.update(req2);
        Long clientId = result2.getId();

      final ClientResp result3 = clientServiceImplUnderTest.delete(clientId);
        // Verify the results
        return result;
    }




    @Test
    public void testGeneratorApiKey() {
        clientServiceImplUnderTest.generatorApiKey();
    }

    @Test
    public void testGeneratorSecretKey() {
        clientServiceImplUnderTest.generatorSecretKey();
    }

    @Test
    public void testQueryApiList() {
        // Setup
        final ClientApiAuthQueryReq req = new ClientApiAuthQueryReq();
        req.setSize(2);
        final ClientApiAuthFilterReq filter = new ClientApiAuthFilterReq();
        filter.setApiName("apiName");
        filter.setShowAll(false);
        req.setFilter(filter);
        final ClientQuerySortReq sort = new ClientQuerySortReq();
        req.setSort(sort);

        // Run the test
        final Paged<ApiSimpleResp> result = clientServiceImplUnderTest.queryApiList(0L, req);

        // Verify the results
    }

    @Test
    public void testQueryApiAuthList() {
        // Setup
        final ClientApiAuthQueryReq req = new ClientApiAuthQueryReq();
        req.setSize(1);
        final ClientApiAuthFilterReq filter = new ClientApiAuthFilterReq();
        filter.setApiName("apiName");
        filter.setShowAll(false);
        req.setFilter(filter);
        final ClientQuerySortReq sort = new ClientQuerySortReq();
        req.setSort(sort);

        // Run the test
        final Paged<ApiSimpleResp> result = clientServiceImplUnderTest.queryApiAuthList(0L, req);

        // Verify the results
    }

    @Test
    public void testAuthRemove() {
        // Setup
        final ClientApiRemoveReq req = new ClientApiRemoveReq();
        req.setClientId(0L);
        req.setApiId(0L);

        // Run the test
        final Boolean result = clientServiceImplUnderTest.authRemove(req);

        // Verify the results
        assertThat(result).isFalse();
    }
}
