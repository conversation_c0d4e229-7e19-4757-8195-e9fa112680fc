package com.eoi.jax.web.data.service.component;

import com.eoi.jax.web.data.service.component.ratelimit.LocalRateLimitPolicy;
import com.eoi.jax.web.data.service.model.apicomponent.DataServiceLog;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/9/8
 */
public class LocalRateLimitPolicyTest {

    @Test
    public void testBase() {
        String apiPath = "www_" + System.currentTimeMillis();
        String apiKey = "123";
        LocalRateLimitPolicy localRateLimitPolicy = new LocalRateLimitPolicy(null);
        DataServiceLog dataServiceLog = new DataServiceLog();
        dataServiceLog.setApiPath(apiPath);
        dataServiceLog.setApiId(123L);
        dataServiceLog.setApiKey(apiKey);
        dataServiceLog.setClientId(456L);
        localRateLimitPolicy.setRateLimitPermits(dataServiceLog, "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":30},\"cache\":{\"enable\":false,\"unit\":null,\"period\":null}}", null);
        boolean b = localRateLimitPolicy.tryAcquire(apiPath, apiKey);
        Assert.assertTrue(b);
    }


    @Test
    public void testBase2() {
        String apiPath = "www_" + System.currentTimeMillis();
        String apiKey = "123";
        LocalRateLimitPolicy localRateLimitPolicy = new LocalRateLimitPolicy(null);
        DataServiceLog dataServiceLog = new DataServiceLog();
        dataServiceLog.setApiPath(apiPath);
        dataServiceLog.setApiId(123L);
        dataServiceLog.setApiKey(apiKey);
        dataServiceLog.setClientId(456L);
        localRateLimitPolicy.setRateLimitPermits(dataServiceLog, "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":30},\"cache\":{\"enable\":false,\"unit\":null,\"period\":null}}", null);
        boolean b = localRateLimitPolicy.tryAcquire(apiPath, apiKey);
        Assert.assertTrue(b);
        b = localRateLimitPolicy.tryAcquire(apiPath, apiKey);
        Assert.assertFalse(b);
        DataServiceLog dataServiceLog1 = new DataServiceLog();
        dataServiceLog1.setApiPath(apiPath);
        dataServiceLog1.setApiKey(apiKey);
        localRateLimitPolicy.restoreApiInfo(dataServiceLog1);
        Assert.assertEquals(123L, dataServiceLog1.getApiId().longValue());
        Assert.assertEquals(456L, dataServiceLog1.getClientId().longValue());
    }

    @Test
    public void testBase3() {
        String apiPath = "www_" + System.currentTimeMillis();
        String apiKey = "123";
        LocalRateLimitPolicy localRateLimitPolicy = new LocalRateLimitPolicy(null);
        DataServiceLog dataServiceLog = new DataServiceLog();
        dataServiceLog.setApiPath(apiPath);
        dataServiceLog.setApiId(123L);
        dataServiceLog.setApiKey(apiKey);
        dataServiceLog.setClientId(456L);
        localRateLimitPolicy.setRateLimitPermits(dataServiceLog, "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":600},\"cache\":{\"enable\":false,\"unit\":null,\"period\":null}}",
                "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":20}}");
        boolean b = localRateLimitPolicy.tryAcquire(apiPath, apiKey);
        Assert.assertTrue(b);
        b = localRateLimitPolicy.tryAcquire(apiPath, apiKey);
        Assert.assertFalse(b);
        DataServiceLog dataServiceLog1 = new DataServiceLog();
        dataServiceLog1.setApiPath(apiPath);
        dataServiceLog1.setApiKey(apiKey);
        localRateLimitPolicy.restoreApiInfo(dataServiceLog1);
        Assert.assertEquals(123L, dataServiceLog1.getApiId().longValue());
        Assert.assertEquals(456L, dataServiceLog1.getClientId().longValue());
    }

    @Test
    public void testBase4() {
        String apiPath = "www_" + System.currentTimeMillis();
        String apiKey = "123";
        LocalRateLimitPolicy localRateLimitPolicy = new LocalRateLimitPolicy(null);
        DataServiceLog dataServiceLog = new DataServiceLog();
        dataServiceLog.setApiPath(apiPath);
        dataServiceLog.setApiId(123L);
        dataServiceLog.setApiKey(apiKey);
        dataServiceLog.setClientId(456L);
        localRateLimitPolicy.setRateLimitPermits(dataServiceLog, null, null);
        for (int i = 0; i < 1000; i++) {
            boolean b = localRateLimitPolicy.tryAcquire(apiPath, apiKey);
            Assert.assertTrue(b);
        }
    }

}
