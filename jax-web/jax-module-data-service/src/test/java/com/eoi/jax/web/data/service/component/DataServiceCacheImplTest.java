package com.eoi.jax.web.data.service.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.eoi.jax.web.SpringUnitTestConfig;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.data.service.component.cache.DataServiceCache;
import com.eoi.jax.web.data.service.model.apicomponent.ApiPublishEvent;
import com.eoi.jax.web.repository.entity.TbSystemConfig;
import com.eoi.jax.web.repository.service.TbSystemConfigService;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/9/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class DataServiceCacheImplTest {

    @Autowired
    private TbSystemConfigService tbSystemConfigService;

    @Autowired
    private SystemConfigHolder systemConfigHolder;

    @Autowired
    private DataServiceCache dataServiceCache;

    private void updateRedisConfig() {
        LambdaUpdateWrapper<TbSystemConfig> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(TbSystemConfig::getSetting, "{\"enable\":true,\"database\":0,\"connectionPoolSize\":30,\"mode\":\"single\",\"password\":\"\",\"address\":\"***************:16379\"}");
        wrapper.eq(TbSystemConfig::getPrefixKey, SystemConfigEnum.DATASERVICE_REDIS.getKey());
        tbSystemConfigService.update(wrapper);
    }

    @Ignore
    @Test
    public void testBase() throws InterruptedException {
        updateRedisConfig();
        systemConfigHolder.refreshConfig();
        TimeUnit.SECONDS.sleep(3);
        Long api = 123L;
        String apiExtraSetting = "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":30},\"cache\":{\"enable\":true,\"unit\":\"SECONDS\",\"period\":10}}";
        String requestParam = "{\"pageNum\":0,\"pageSize\":10,\"param\":{\"aaa\":1,\"bbb\":\"3232\"}}";
        String response = "{\"name\":\"BeJson\",\"url\":\"http://www.bejson.com\",\"page\":88,\"isNonProfit\":true,\"address\":{\"street\":\"科技园路.\",\"city\":\"江苏苏州\",\"country\":\"中国\"},\"links\":[{\"name\":\"Google\",\"url\":\"http://www.google.com\"},{\"name\":\"Baidu\",\"url\":\"http://www.baidu.com\"},{\"name\":\"SoSo\",\"url\":\"http://www.SoSo.com\"}]}";
        dataServiceCache.set(api, apiExtraSetting, requestParam, response);
        Object o = dataServiceCache.get(api, requestParam);
        Assert.assertEquals(response, (String) o);
    }

    /**
     * 测试超时
     */
    @Ignore
    @Test
    public void testOvertime() throws InterruptedException {
        updateRedisConfig();
        systemConfigHolder.refreshConfig();
        TimeUnit.SECONDS.sleep(3);
        Long api = 123L;
        String apiExtraSetting = "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":30},\"cache\":{\"enable\":true,\"unit\":\"SECONDS\",\"period\":4}}";
        String requestParam = "{\"pageNum\":0,\"pageSize\":10,\"param\":{\"aaa\":1,\"bbb\":\"3232\"}}";
        String response = "{\"name\":\"BeJson\",\"url\":\"http://www.bejson.com\",\"page\":88,\"isNonProfit\":true,\"address\":{\"street\":\"科技园路.\",\"city\":\"江苏苏州\",\"country\":\"中国\"},\"links\":[{\"name\":\"Google\",\"url\":\"http://www.google.com\"},{\"name\":\"Baidu\",\"url\":\"http://www.baidu.com\"},{\"name\":\"SoSo\",\"url\":\"http://www.SoSo.com\"}]}";
        dataServiceCache.set(api, apiExtraSetting, requestParam, response);
        String o = (String) dataServiceCache.get(api, requestParam);
        Assert.assertEquals(response, o);
        TimeUnit.SECONDS.sleep(5);
        o = (String) dataServiceCache.get(api, requestParam);
        Assert.assertNull(o);
    }


    /**
     * 测试使用不同的请求参数
     */
    @Ignore
    @Test
    public void testBase1() throws InterruptedException {
        updateRedisConfig();
        systemConfigHolder.refreshConfig();
        TimeUnit.SECONDS.sleep(3);
        Long api = 123L;
        String apiExtraSetting = "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":30},\"cache\":{\"enable\":true,\"unit\":\"SECONDS\",\"period\":10}}";
        String requestParam = "{\"pageNum\":0,\"pageSize\":10,\"param\":{\"aaa\":1,\"bbb\":\"3232\"}}";
        String response = "{\"name\":\"BeJson\",\"url\":\"http://www.bejson.com\",\"page\":88,\"isNonProfit\":true,\"address\":{\"street\":\"科技园路.\",\"city\":\"江苏苏州\",\"country\":\"中国\"},\"links\":[{\"name\":\"Google\",\"url\":\"http://www.google.com\"},{\"name\":\"Baidu\",\"url\":\"http://www.baidu.com\"},{\"name\":\"SoSo\",\"url\":\"http://www.SoSo.com\"}]}";
        dataServiceCache.set(api, apiExtraSetting, requestParam, response);
        String o = (String) dataServiceCache.get(api, requestParam + "111");
        Assert.assertNull(o);
    }


    /**
     * 测试空response
     */
    @Ignore
    @Test
    public void testBase2() throws InterruptedException {
        updateRedisConfig();
        systemConfigHolder.refreshConfig();
        TimeUnit.SECONDS.sleep(3);
        Long api = 123L;
        String apiExtraSetting = "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":30},\"cache\":{\"enable\":true,\"unit\":\"SECONDS\",\"period\":10}}";
        String requestParam = "{\"pageNum\":0,\"pageSize\":10,\"param\":{\"aaa\":1,\"bbb\":\"3232\"}}";
        dataServiceCache.set(api, apiExtraSetting, requestParam, null);
        String o = (String) dataServiceCache.get(api, requestParam);
        Assert.assertNull(o);
    }

    /**
     * 测试接收到发布事件
     * @throws InterruptedException
     */
    @Ignore
    @Test
    public void testBase3() throws InterruptedException {
        updateRedisConfig();
        systemConfigHolder.refreshConfig();
        TimeUnit.SECONDS.sleep(4);
        Long api = 123L;
        String apiExtraSetting = "{\"rateLimit\":{\"enable\":true,\"unit\":\"MINUTES\",\"period\":30},\"cache\":{\"enable\":true,\"unit\":\"SECONDS\",\"period\":20}}";
        String requestParam = "{\"pageNum\":0,\"pageSize\":10,\"param\":{\"aaa\":1,\"bbb\":\"3232\"}}";
        String response = "{\"name\":\"BeJson\",\"url\":\"http://www.bejson.com\",\"page\":88,\"isNonProfit\":true,\"address\":{\"street\":\"科技园路.\",\"city\":\"江苏苏州\",\"country\":\"中国\"},\"links\":[{\"name\":\"Google\",\"url\":\"http://www.google.com\"},{\"name\":\"Baidu\",\"url\":\"http://www.baidu.com\"},{\"name\":\"SoSo\",\"url\":\"http://www.SoSo.com\"}]}";
        dataServiceCache.set(api, apiExtraSetting, requestParam, response);
        Object o = dataServiceCache.get(api, requestParam);
        Assert.assertEquals(response, (String) o);
        ContextHolder.publishEvent(new ApiPublishEvent(api, null, null));
        TimeUnit.SECONDS.sleep(2);
        o = (String) dataServiceCache.get(api, requestParam);
        Assert.assertNull(o);
    }


}
