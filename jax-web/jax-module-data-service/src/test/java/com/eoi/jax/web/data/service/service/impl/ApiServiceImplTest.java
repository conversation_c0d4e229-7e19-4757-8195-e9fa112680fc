package com.eoi.jax.web.data.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.web.core.common.enumrate.ApiConsumeModeEnum;
import com.eoi.jax.web.core.common.enumrate.ApiModelEnum;
import com.eoi.jax.web.core.common.enumrate.ApiRequestTypeEnum;
import com.eoi.jax.web.core.common.enumrate.ApiStatusEnum;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.service.SpringUnitTestConfig;
import com.eoi.jax.web.data.service.model.api.*;
import com.eoi.jax.web.data.service.model.api.group.ApiGroupCreateReq;
import com.eoi.jax.web.data.service.model.api.group.ApiGroupResp;
import com.eoi.jax.web.data.service.model.api.history.*;
import com.eoi.jax.web.data.service.model.apiquery.DsTestQueryReq;
import com.eoi.jax.web.data.service.model.metadata.TableMetadata;
import com.eoi.jax.web.data.service.service.ApiGroupService;
import com.eoi.jax.web.data.service.service.DataApiQueryService;
import com.eoi.jax.web.data.service.service.IDsApiService;
import com.eoi.jax.web.data.service.sqlparser.OriginSqlFinderRequest;
import com.eoi.jax.web.data.service.sqlparser.OriginSqlFinderResponse;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceCreateReq;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceResp;
import com.eoi.jax.web.ingestion.service.DatasourceService;
import com.eoi.jax.web.repository.entity.TbApiHistory;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class ApiServiceImplTest {

    @Resource
    private DataApiQueryService dataApiQueryService;

    @Resource
    private ApiServiceImpl apiServiceImplUnderTest;

    @Resource
    private ApiGroupService apiGroupService;

    @Autowired
    private DatasourceService datasourceService;


    @Resource
    private ApiHistoryServiceImpl apiHistoryServiceImplUnderTest;



    @Test
    public void testMarshallingRespFrom() {
        apiHistoryServiceImplUnderTest.marshallingRespFrom(new TbApiHistory());
    }

    @Test
    public void mainTest() {
        ApiGroupCreateReq req = new ApiGroupCreateReq();
        req.setName("分组1");
        req.setParentId(null);
        req.setDescription("desc");
        ApiGroupResp group = apiGroupService.create(req);
        assertThat(group.getId()).isNotNull();

        String datasourceJson = "{\n" +
                "  \"name\": \"单元测试mysql\",\n" +
                "  \"code\": \"unitmyql\",\n" +
                "  \"paramMap\": {\n" +
                "    \"address\": \"***********************************************\",\n" +
                "    \"username\": \"root\",\n" +
                "    \"password\": \"MySQL@123\"\n" +
                "  },\n" +
                "  \"platform\": \"MYSQL\",\n" +
                "  \"connectMode\": \"MANUAL\",\n" +
                "  \"status\": \"ABNORMAL\",\n" +
                "  \"connectDetail\": \"{\\\"status\\\":\\\"ABNORMAL\\\",\\\"normalUrls\\\":[],\\\"abnormalUrlMap\\\":{\\\"**********************************************\\\":\\\"数据源连接失败, Access denied for user 'root'@'***************' (using password: YES)\\\"},\\\"errMsg\\\":null}\"\n" +
                "}";
        DatasourceCreateReq datasourceCreateReq = JSONUtil.toBean(datasourceJson, DatasourceCreateReq.class);
        DatasourceResp datasourceResp = datasourceService.create(datasourceCreateReq);

        String createJson = "{\n" +
                "  \"name\": \"zsc-test\",\n" +
                "  \"apiPath\": \"tttx\",\n" +
                "  \"requestType\": \"GET\",\n" +
                "  \"tags\": [],\n" +
                "  \"description\": \"sdc\",\n" +
                "  \"dsType\": \"CLICKHOUSE\",\n" +
                "  \"dsId\": \"1027439683863552\",\n" +
                "  \"setting\": {\n" +
                "    \"script\": \"select id,name from tb_api\",\n" +
                "    \"enablePaged\": false,\n" +
                "    \"requestColumns\": [],\n" +
                "    \"responseColumns\": [\n" +
                "      {\n" +
                "        \"id\": 25117.029293316697,\n" +
                "        \"name\": \"aa\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"\",\n" +
                "        \"masking\": {\n" +
                "          \"type\": \"\",\n" +
                "          \"rule\": {\n" +
                "            \"frontNum\": 0,\n" +
                "            \"middleNum\": 0,\n" +
                "            \"backNum\": 0\n" +
                "          }\n" +
                "        }\n" +
                "      }\n" +
                "    ],\n" +
                "    \"serviceSettings\": [],\n" +
                "    \"prevScript\": \"\",\n" +
                "    \"postScript\": \"\",\n" +
                "    \"pageColumns\": []\n" +
                "  },\n" +
                "  \"apiMode\": \"SQL\",\n" +
                "  \"consumeMode\": \"HTTP\",\n" +
                "  \"apiGroupId\": \"1692327993707520\"\n" +
                "}";
        final ApiCreateReq req2 = JSONUtil.toBean(createJson, ApiCreateReq.class);
        req2.setDsId(datasourceResp.getId());
        req2.setApiGroupId(group.getId());
        final ApiResp result2 = apiServiceImplUnderTest.create(req2);

        // update
        final ApiUpdateReq req3 = new ApiUpdateReq();
        BeanUtil.copyProperties(result2, req3);
        apiServiceImplUnderTest.update(req3);

        apiServiceImplUnderTest.usage(result2.getId());
        final ApiResp result4 = apiServiceImplUnderTest.get(result2.getId());

        apiServiceImplUnderTest.selectByApiPath("tttx");

        Boolean result = apiServiceImplUnderTest.publishApiById(result2.getId());
        apiServiceImplUnderTest.update(req3);
        apiServiceImplUnderTest.publishApiById(result2.getId());
        apiServiceImplUnderTest.update(req3);
        apiServiceImplUnderTest.publishApiById(result2.getId());

        apiServiceImplUnderTest.online(Arrays.asList(result2.getId()));

        apiServiceImplUnderTest.offline(Arrays.asList(result2.getId()));

        apiServiceImplUnderTest.delete(result2.getId());

        String createJson2 = "{\n" +
                "  \"name\": \"zsc-test2\",\n" +
                "  \"apiPath\": \"tttx2\",\n" +
                "  \"requestType\": \"GET\",\n" +
                "  \"tags\": [],\n" +
                "  \"description\": \"sdc\",\n" +
                "  \"dsType\": \"CLICKHOUSE\",\n" +
                "  \"dsId\": \"1027439683863552\",\n" +
                "  \"setting\": {\n" +
                "    \"script\": \"select id,name from tb_api\",\n" +
                "    \"enablePaged\": false,\n" +
                "    \"requestColumns\": [],\n" +
                "    \"responseColumns\": [\n" +
                "      {\n" +
                "        \"id\": 25117.029293316697,\n" +
                "        \"name\": \"aa\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"\",\n" +
                "        \"masking\": {\n" +
                "          \"type\": \"\",\n" +
                "          \"rule\": {\n" +
                "            \"frontNum\": 0,\n" +
                "            \"middleNum\": 0,\n" +
                "            \"backNum\": 0\n" +
                "          }\n" +
                "        }\n" +
                "      }\n" +
                "    ],\n" +
                "    \"serviceSettings\": [],\n" +
                "    \"prevScript\": \"\",\n" +
                "    \"postScript\": \"\",\n" +
                "    \"pageColumns\": []\n" +
                "  },\n" +
                "  \"apiMode\": \"SQL\",\n" +
                "  \"consumeMode\": \"HTTP\",\n" +
                "  \"apiGroupId\": \"1692327993707520\"\n" +
                "}";
        final ApiCreateReq req5 = JSONUtil.toBean(createJson2, ApiCreateReq.class);
        req5.setDsId(datasourceResp.getId());
        req5.setApiGroupId(group.getId());
        ApiResp result5 = apiServiceImplUnderTest.publish(req5);

        final ImportExcelReq req6 = new ImportExcelReq();
        req6.setIds(Arrays.asList(result5.getId()));
        req6.setType("type");
        req6.setCondition("condition");
        req6.setJaxSuperProjectId(null);
        final List<ApiImportModel> result6 = apiServiceImplUnderTest.exportData(req6);

        final ApiTopicQueryReq req7 = new ApiTopicQueryReq();
        req7.setPage(1);
        req7.setSize(1);
        final ApiTopicReq filter = new ApiTopicReq();
        filter.setName("tb_api");
        filter.setDsId(datasourceResp.getId());
        filter.setDatabase("jax_super_dev");
        req7.setFilter(filter);
        final Paged<TableMetadata> result7 = apiServiceImplUnderTest.getTableByDsId(req7);


        String analyRequestJson = "{\n" +
                "  \"dataSourceId\": \"1027439683863552\",\n" +
                "  \"apiMode\": \"SQL\",\n" +
                "  \"apiSql\": \"select id,name from tb_api\"\n" +
                "}";
        try {
            ApiTestReq analyRequest = JSONUtil.toBean(analyRequestJson, ApiTestReq.class);
            analyRequest.setDataSourceId(datasourceResp.getId());
            final OriginSqlFinderRequest result8 = apiServiceImplUnderTest.
                    analyzeParamRequest(analyRequest);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String analyResponseJson = "{\n" +
                "  \"dataSourceId\": \"1027439683863552\",\n" +
                "  \"apiMode\": \"SQL\",\n" +
                "  \"apiSql\": \"select id,name from tb_api\"\n" +
                "}";
        try {
            ApiTestReq analyResponse = JSONUtil.toBean(analyResponseJson, ApiTestReq.class);
            analyResponse.setDataSourceId(datasourceResp.getId());
            final OriginSqlFinderResponse result9 = apiServiceImplUnderTest.
                    analyzeParamResponse(analyResponse);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String testJson = "{\n" +
                "  \"createUserName\": null,\n" +
                "  \"updateUserName\": null,\n" +
                "  \"id\": \"2484671561959424\",\n" +
                "  \"name\": \"zsc-test2\",\n" +
                "  \"apiGroupId\": \"1692327993707520\",\n" +
                "  \"apiGroupName\": \"全息\",\n" +
                "  \"consumeMode\": \"HTTP\",\n" +
                "  \"apiPath\": \"/api/open/data-service/tttx2\",\n" +
                "  \"requestType\": \"GET\",\n" +
                "  \"apiMode\": \"SQL\",\n" +
                "  \"dsType\": \"CLICKHOUSE\",\n" +
                "  \"dsId\": \"1027439683863552\",\n" +
                "  \"dsName\": \"TESET-162\",\n" +
                "  \"topic\": null,\n" +
                "  \"databaseName\": null,\n" +
                "  \"dataTable\": null,\n" +
                "  \"customDsId\": null,\n" +
                "  \"version\": 1,\n" +
                "  \"status\": \"OFFLINE\",\n" +
                "  \"isPublished\": 0,\n" +
                "  \"publishedId\": 1806585144544432000,\n" +
                "  \"setting\": {\n" +
                "    \"prevScript\": \"\",\n" +
                "    \"sortColumns\": [],\n" +
                "    \"serviceSettings\": [],\n" +
                "    \"requestColumns\": [],\n" +
                "    \"responseColumns\": [\n" +
                "      {\n" +
                "        \"masking\": {\n" +
                "          \"rule\": {\n" +
                "            \"frontNum\": 0,\n" +
                "            \"middleNum\": 0,\n" +
                "            \"backNum\": 0,\n" +
                "            \"expression\": []\n" +
                "          },\n" +
                "          \"type\": \"\"\n" +
                "        },\n" +
                "        \"name\": \"aa\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"postScript\": \"\",\n" +
                "    \"enablePaged\": false,\n" +
                "    \"pageColumns\": [],\n" +
                "    \"script\": \"select id,name from tb_api\"\n" +
                "  },\n" +
                "  \"extraSetting\": null,\n" +
                "  \"handleClass\": null,\n" +
                "  \"description\": \"sdc\",\n" +
                "  \"isDeleted\": 0,\n" +
                "  \"createTime\": \"2024-06-28 14:49:19\",\n" +
                "  \"updateTime\": \"2024-06-28 15:47:31\",\n" +
                "  \"createUser\": null,\n" +
                "  \"updateUser\": null,\n" +
                "  \"tags\": null,\n" +
                "  \"authTimes\": 0,\n" +
                "  \"reqParam\": {\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 5,\n" +
                "    \"param\": {}\n" +
                "  }\n" +
                "}";

        DsTestQueryReq apiTestReq = JSONUtil.toBean(testJson, DsTestQueryReq.class);
        apiTestReq.setDsId(datasourceResp.getId());
        apiTestReq.setApiGroupId(group.getId());
        dataApiQueryService.testQuery(apiTestReq, new MockHttpServletRequest());

        mainTestHistory();
    }

    @Test
    public void testAll() {
        // Setup
        // Run the test
        final List<ApiResp> result = apiServiceImplUnderTest.all();

        // Verify the results
    }

    @Test
    public void testQuery() {
        // Setup
        final ApiQueryReq req = new ApiQueryReq();
        req.setPage(1);
        req.setSize(10);
        final ApiQueryFilterReq filter = new ApiQueryFilterReq();
        filter.setIdList(Arrays.asList(0L));
        req.setFilter(filter);
        final ApiQuerySortReq sort = new ApiQuerySortReq();
        sort.setUpdateTime("updateTime");
        sort.setName("name");
        sort.setConsumeMode("consumeMode");
        sort.setStatus("name");
        req.setSort(sort);

        // Run the test
        final Paged<ApiResp> result = apiServiceImplUnderTest.query(req);

        // Verify the results
    }

    public void mainTestHistory() {
        Long apiId = 123L;

        final ApiHistoryCreateReq req = new ApiHistoryCreateReq();
        req.setApiId(apiId);
        req.setName("aa");
        req.setApiGroupId(0L);
        req.setApiPath("");
        req.setDsId(0L);
        req.setTopic("");
        req.setDatabaseName("");
        req.setDataTable("");
        req.setVersion(1);
        req.setDsType("");
        req.setConsumeMode(ApiConsumeModeEnum.KAFKA);
        req.setRequestType(ApiRequestTypeEnum.GET);
        req.setApiMode(ApiModelEnum.KAFKA);
        req.setStatus(ApiStatusEnum.DRAFT);
        req.setSetting(Maps.newHashMap());
        req.setHandleClass("aad");
        req.setDescription("");
        final ApiHistoryResp result = apiHistoryServiceImplUnderTest.create(req);

        final ApiHistoryUpdateReq req2 = new ApiHistoryUpdateReq();
        BeanUtil.copyProperties(result, req2);
        final ApiHistoryResp result2 = apiHistoryServiceImplUnderTest.update(req2);

        final List<ApiHistoryResp> result3 = apiHistoryServiceImplUnderTest.getByApiId(apiId);
        final ApiHistoryResp result5 = apiHistoryServiceImplUnderTest.delete(req2.getId());


        // 查询历史
        List<ApiHistoryResp> result6 = apiHistoryServiceImplUnderTest.all();

        final ApiHistoryCompareReq req7 = new ApiHistoryCompareReq();
        req7.setSourceId(result6.get(0).getId());
        req7.setTargetId(result6.get(1).getId());
        // Run the test
        final ApiHistoryCompareResp result7 = apiHistoryServiceImplUnderTest.compare(req7);

        apiHistoryServiceImplUnderTest.rollback(result6.get(0).getId());
        apiHistoryServiceImplUnderTest.getHistoryForRollback(result6.get(0).getId());
        apiHistoryServiceImplUnderTest.listByApiId(result6.get(0).getApiId());

        final IDsApiService result8 = apiHistoryServiceImplUnderTest
                .getDsApiService("apiModel", "handleClass");

        final ApiHistoryQueryReq req8 = new ApiHistoryQueryReq();
        final ApiHistoryQueryFilterReq filter = new ApiHistoryQueryFilterReq();
        filter.setApiId(0L);
        req8.setFilter(filter);
        final ApiHistoryQuerySortReq sort = new ApiHistoryQuerySortReq();
        sort.setVersion("version");
        req8.setSort(sort);
        apiHistoryServiceImplUnderTest.query(req8);
    }


}
