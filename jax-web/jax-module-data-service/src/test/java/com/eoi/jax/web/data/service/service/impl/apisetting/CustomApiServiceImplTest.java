package com.eoi.jax.web.data.service.service.impl.apisetting;

import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.model.apisetting.CustomApiSettingModel;
import com.eoi.jax.api.dataservice.model.apisetting.IApiSettingModel;
import com.eoi.jax.web.data.service.SpringUnitTestConfig;
import com.eoi.jax.web.data.service.model.apisetting.ValidateApiSettingReq;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceResp;
import com.eoi.jax.web.repository.entity.TbApi;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;


@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
public class CustomApiServiceImplTest {

    @Autowired
    private CustomApiServiceImpl customApiServiceImplUnderTest;

    @Test
    public void testGetApiSettingModelInstance() {
        // Setup
        final Map<String, Object> settingMap = new HashMap<>();

        // Run the test
        final IApiSettingModel result = customApiServiceImplUnderTest.getApiSettingModelInstance(settingMap);

        // Verify the results
    }

    @Test
    public void testValidate() {
        String json ="{\n" +
                "  \"createUserName\": null,\n" +
                "  \"updateUserName\": null,\n" +
                "  \"id\": \"1838680204053504\",\n" +
                "  \"name\": \"查询指标集列表\",\n" +
                "  \"apiGroupId\": \"1779964984593408\",\n" +
                "  \"apiGroupName\": \"海通 \",\n" +
                "  \"consumeMode\": \"HTTP\",\n" +
                "  \"apiPath\": \"jaxMetricSetList\",\n" +
                "  \"requestType\": \"GET\",\n" +
                "  \"apiMode\": \"CUSTOM\",\n" +
                "  \"dsType\": \"CUSTOM\",\n" +
                "  \"dsId\": null,\n" +
                "  \"dsName\": null,\n" +
                "  \"topic\": null,\n" +
                "  \"databaseName\": null,\n" +
                "  \"dataTable\": null,\n" +
                "  \"customDsId\": \"[]\",\n" +
                "  \"version\": 3,\n" +
                "  \"status\": \"OFFLINE\",\n" +
                "  \"isPublished\": 1,\n" +
                "  \"publishedId\": 1725447065421914000,\n" +
                "  \"setting\": {\n" +
                "    \"requestColumns\": [\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"defaultValue\": \"\",\n" +
                "        \"name\": \"setName\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"position\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"operation\": \"\",\n" +
                "        \"required\": false,\n" +
                "        \"desc\": \"指标集名称\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"defaultValue\": \"\",\n" +
                "        \"name\": \"objId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"position\": \"\",\n" +
                "        \"type\": \"LONG\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"operation\": \"\",\n" +
                "        \"required\": false,\n" +
                "        \"desc\": \"对象模型id\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"defaultValue\": \"\",\n" +
                "        \"name\": \"categoryId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"position\": \"\",\n" +
                "        \"type\": \"LONG\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"operation\": \"\",\n" +
                "        \"required\": false,\n" +
                "        \"desc\": \"对象分类id\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"responseColumns\": [\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"categoryId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"categoryId\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"categoryName\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"categoryName\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"categoryCode\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"categoryCode\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"categoryParentId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"categoryParentId\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"categoryParentPath\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"LONG\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"categoryParentPath\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"objId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"INT\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"objId\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"objName\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"objName\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"objTypeCode\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"objTypeCode\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"objCategoryId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"objCategoryId\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"objCategoryPath\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"objCategoryPath\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"setId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"setId\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"setName\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"setName\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"setCode\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"setCode\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"columnType\": \"\",\n" +
                "        \"name\": \"setObjId\",\n" +
                "        \"mappingField\": \"\",\n" +
                "        \"type\": \"STRING\",\n" +
                "        \"sampleValue\": \"\",\n" +
                "        \"desc\": \"setObjId\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"serviceSettings\": [],\n" +
                "    \"prevScript\": \"\",\n" +
                "    \"postScript\": \"\",\n" +
                "    \"datasources\": [],\n" +
                "    \"accessJaxMeta\": false\n" +
                "  },\n" +
                "  \"handleClass\": \"com.eoi.jax.api.assetmanager.service.MetricSetListServiceImpl\",\n" +
                "  \"description\": null,\n" +
                "  \"isDeleted\": 0,\n" +
                "  \"createTime\": \"2023-11-13 10:41:07\",\n" +
                "  \"updateTime\": \"2023-11-17 17:33:32\",\n" +
                "  \"createUser\": null,\n" +
                "  \"updateUser\": null,\n" +
                "  \"tags\": null,\n" +
                "  \"authTimes\": 1\n" +
                "}";
        // Setup
        final ValidateApiSettingReq req = new ValidateApiSettingReq();
        final TbApi tbApi = JSONUtil.toBean(json, TbApi.class);
        CustomApiSettingModel settings = new CustomApiSettingModel();
        req.setApiSettingModel(settings);
        req.setTbApi(tbApi);

        // Configure DatasourceService.get(...).
        final DatasourceResp datasourceResp = new DatasourceResp();
        datasourceResp.setId(0L);
        datasourceResp.setCode("code");
        datasourceResp.setName("name");
        datasourceResp.setParamMap(new HashMap<>());
        datasourceResp.setPlatform("platform");


        // Run the test
        customApiServiceImplUnderTest.validate(req);

        // Verify the results
    }

    @Test
    public void testCheckParamNameCorrect() {
        assertThat(customApiServiceImplUnderTest.checkParamNameCorrect("value")).isFalse();
    }
}
