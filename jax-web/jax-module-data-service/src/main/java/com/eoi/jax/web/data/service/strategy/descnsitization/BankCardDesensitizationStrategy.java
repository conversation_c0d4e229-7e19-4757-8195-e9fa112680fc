package com.eoi.jax.web.data.service.strategy.descnsitization;


import com.eoi.jax.api.dataservice.enumrate.DataMaskingEnum;
import com.eoi.jax.web.data.service.strategy.AbstractDesensitizationStrategy;
import com.eoi.jax.api.dataservice.model.mask.MaskingRule;

public class BankCardDesensitizationStrategy extends AbstractDesensitizationStrategy {
    @Override
    public MaskingRule getMaskingRule() {
        return new MaskingRule(4, null, 4, "*");
    }
    @Override
    public DataMaskingEnum getMaskingType() {
        return DataMaskingEnum.BANK_CARD;
    }
}
