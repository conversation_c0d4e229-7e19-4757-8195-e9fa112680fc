package com.eoi.jax.web.data.service.model.client.api;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * @Author: tangy
 * @Date: 2023/7/24
 * @Desc:
 **/
public class ClientApiItem implements Serializable {


    /**
     * api接口id
     */
    @Schema(description = "api接口id")
    private Long apiId;



    /**
     * VALIDITY-有效，INVALIDITY-失效
     */
    @Schema(description = "VALIDITY-有效，INVALIDITY-失效")
    private String status;

    @Schema(description = "备注")
    /**
     * 备注信息
     */
    private String remark;

    public Long getApiId() {
        return apiId;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
