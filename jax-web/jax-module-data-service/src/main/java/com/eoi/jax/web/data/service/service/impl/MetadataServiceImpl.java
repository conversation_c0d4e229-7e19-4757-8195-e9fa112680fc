package com.eoi.jax.web.data.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.data.service.factory.MetadataServiceFactory;
import com.eoi.jax.web.data.service.model.metadata.ColumnMetadata;
import com.eoi.jax.web.data.service.model.metadata.ColumnMetadataReq;
import com.eoi.jax.web.data.service.model.metadata.DatabaseTableTreeResp;
import com.eoi.jax.web.data.service.model.metadata.ModelTableMetadata;
import com.eoi.jax.web.data.service.model.metadata.TableMetadata;
import com.eoi.jax.web.data.service.model.metadata.TableMetadataReq;
import com.eoi.jax.web.data.service.service.DatasourceHelperService;
import com.eoi.jax.web.data.service.service.DatasourceMetadataService;
import com.eoi.jax.web.data.service.service.MetadataService;
import com.eoi.jax.web.repository.entity.TbColumnDeploy;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.service.TbColumnDeployService;
import com.eoi.jax.web.repository.service.TbTableDeployService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
@Service
public class MetadataServiceImpl implements MetadataService {

    @Resource
    private DatasourceHelperService datasourceHelperService;

    @Resource
    private TbTableDeployService tbTableDeployService;

    @Resource
    private TbColumnDeployService tbColumnDeployService;

    /**
     * 所有数据库
     *
     * @param dsId
     * @return
     */
    @Override
    public List<String> allDatabase(Long dsId) {
        DsDatasource dsDatasource = datasourceHelperService.getDsDatasource(dsId);
        DatasourceMetadataService datasourceMetadataService = MetadataServiceFactory.getServiceInstance(dsDatasource.getPlatform());
        return datasourceMetadataService.allDatabase(dsDatasource);
    }

    /**
     * 所有表
     *
     * @param tableMetadataReq
     * @return
     */
    @Override
    public List<TableMetadata> allTable(TableMetadataReq tableMetadataReq) {
        DsDatasource dsDatasource = datasourceHelperService.getDsDatasource(tableMetadataReq.getDsId());
        DatasourceMetadataService datasourceMetadataService = MetadataServiceFactory.getServiceInstance(dsDatasource.getPlatform());
        return datasourceMetadataService.allTable(dsDatasource, tableMetadataReq.getDatabase());
    }


    @Override
    public List<DatabaseTableTreeResp> databaseTableTree(Long dsId, String useDatabase) {
        DsDatasource dsDatasource = datasourceHelperService.getDsDatasource(dsId);
        DatasourceMetadataService datasourceMetadataService = MetadataServiceFactory.getServiceInstance(dsDatasource.getPlatform());
        // 获取所有数据库
        List<String> databaseList;
        if (datasourceMetadataService.hasSchema()) {
            databaseList = datasourceMetadataService.allDatabaseSchema(dsDatasource, useDatabase);
        } else {
            databaseList = StrUtil.isNotBlank(useDatabase) ? CollUtil.newArrayList(useDatabase) :
                datasourceMetadataService.allDatabase(dsDatasource);
        }
        // 遍历数库获取所有表
        try {
            List<CompletableFuture<DatabaseTableTreeResp>> completableFutureList = new ArrayList<>();
            Semaphore semaphore = new Semaphore(8);
            for (String database : databaseList) {
                semaphore.acquire();
                CompletableFuture<DatabaseTableTreeResp> completableFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        DatabaseTableTreeResp databaseTableTreeResp = new DatabaseTableTreeResp();
                        databaseTableTreeResp.setDatabase(database);
                        databaseTableTreeResp.setModelTableList(getTableMetadataByDatabase(datasourceMetadataService,
                            dsDatasource, database));
                        return databaseTableTreeResp;
                    } finally {
                        semaphore.release();
                    }
                }, ThreadPoolUtil.THREAD_POOL);
                completableFutureList.add(completableFuture);
            }
            // 等待执行完成
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
            return completableFutureList.stream().map(it -> it.getNow(null)).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("查询库下所有表异常", e);
        }
    }


    /**
     * 根据database查询表
     *
     * @param datasourceMetadataService
     * @param dsDatasource
     * @param database
     * @return
     */
    private List<ModelTableMetadata> getTableMetadataByDatabase(DatasourceMetadataService datasourceMetadataService,
                                                                DsDatasource dsDatasource,
                                                                String database) {
        List<TableMetadata> tableMetadataList = datasourceMetadataService.allTable(dsDatasource, database);
        if (CollUtil.isEmpty(tableMetadataList)) {
            return CollUtil.newArrayList();
        }

        List<ModelTableMetadata> modelTableMetadataList = new ArrayList<>();
        List<List<TableMetadata>> splitList = CollUtil.split(tableMetadataList, 100);
        for (List<TableMetadata> list : splitList) {
            // 查询模型
            List<TbTableDeploy> tbTableDeployList = tbTableDeployService.getLastDeployList(dsDatasource.getPlatform(), "SUCCESS", null,
                list.stream().map(it -> it.getTableName().toLowerCase()).collect(Collectors.toList()), dsDatasource.getId(), true);
            Map<String, TbTableDeploy> map = CollUtil.isEmpty(tbTableDeployList) ? MapUtil.newHashMap() :
                tbTableDeployList.stream().collect(Collectors.toMap(it -> it.getTbName().toLowerCase(), it -> it,
                    (x, y) -> x.getUpdateTime().after(y.getUpdateTime()) ? x : y));
            for (TableMetadata tableMetadata : list) {
                ModelTableMetadata modelTableMetadata = ModelBeanUtil.copyBean(tableMetadata, new ModelTableMetadata());
                TbTableDeploy deploy = map.get(tableMetadata.getTableName().toLowerCase());
                if (Objects.nonNull(deploy)) {
                    modelTableMetadata.setDeployId(deploy.getTbId());
                    modelTableMetadata.setTbId(deploy.getTbId());
                    modelTableMetadata.setTbName(deploy.getTbName());
                    modelTableMetadata.setTbAlias(deploy.getTbAlias());
                }
                modelTableMetadataList.add(modelTableMetadata);
            }
        }
        return modelTableMetadataList;
    }


    @Override
    public List<ColumnMetadata> modelColumns(ColumnMetadataReq columnMetadataReq) {
        List<ColumnMetadata> columnMetadataList = allColumn(columnMetadataReq);
        if (Objects.isNull(columnMetadataReq.getTbId())) {
            return columnMetadataList;
        }
        // 查询最后一个发布成功的模型
        TbTableDeploy tbTableDeploy = tbTableDeployService.getLastSuccessTableDeploy(columnMetadataReq.getTbId());
        if (Objects.isNull(tbTableDeploy)) {
            throw new BizException(ResponseCode.FAILED.getCode(), "根据模型id:" + columnMetadataReq.getTbId() + "找不到模型");
        }
        // 查询模型字段
        List<TbColumnDeploy> tbColumnDeployList = tbColumnDeployService.getByTableDeployId(tbTableDeploy.getId());
        Map<String, TbColumnDeploy> tbColumnDeployMap = CollUtil.isEmpty(tbColumnDeployList) ? MapUtil.newHashMap() :
            tbColumnDeployList.stream().collect(Collectors.toMap(it -> it.getColName().toLowerCase(), it -> it));

        for (ColumnMetadata columnMetadata : columnMetadataList) {
            TbColumnDeploy tbColumnDeploy = tbColumnDeployMap.get(columnMetadata.getColumnName());
            if (Objects.nonNull(tbColumnDeploy)) {
                columnMetadata.setColName(tbColumnDeploy.getColName());
                columnMetadata.setColDisplay(tbColumnDeploy.getColDisplay());
                columnMetadata.setColType(tbColumnDeploy.getColType());
            }
        }
        return columnMetadataList;
    }

    /**
     * 所有字段
     *
     * @param columnMetadataReq
     * @return
     */
    private List<ColumnMetadata> allColumn(ColumnMetadataReq columnMetadataReq) {
        DsDatasource dsDatasource = datasourceHelperService.getDsDatasource(columnMetadataReq.getDsId());
        DatasourceMetadataService datasourceMetadataService = MetadataServiceFactory.getServiceInstance(dsDatasource.getPlatform());
        return datasourceMetadataService.allColumn(dsDatasource, columnMetadataReq.getDatabase(), columnMetadataReq.getTable());
    }


}
