package com.eoi.jax.web.data.service.model.apisetting;

import com.eoi.jax.api.dataservice.model.apisetting.IApiSettingModel;
import com.eoi.jax.web.repository.entity.TbApi;

/**
 * <AUTHOR> zsc
 * @create 2023/1/6 10:25
 */
public class ValidateApiSettingReq {

    /**
     * api setting
     */
    private IApiSettingModel apiSettingModel;

    private TbApi tbApi;

    public TbApi getTbApi() {
        return tbApi;
    }

    public void setTbApi(TbApi tbApi) {
        this.tbApi = tbApi;
    }

    public IApiSettingModel getApiSettingModel() {
        return apiSettingModel;
    }

    public void setApiSettingModel(IApiSettingModel apiSettingModel) {
        this.apiSettingModel = apiSettingModel;
    }
}
