package com.eoi.jax.web.data.service.model.api;

import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2023/10/31
 */
public class ApiImportModel extends AbstractExcelRowResp {

    /**
     * 接口名
     */
    @Excel(name = "接口名称*", no = 0)
    @Schema(description = "接口名称")
    private String name;

    /**
     * api分组id
     */
    @Schema(description = "分组id")
    private Long apiGroupId;

    /**
     * 分组名称
     */
    @Excel(name = "分组名称*", no = 1)
    @Schema(description = "分组名称")
    private String apiGroupName;

    /**
     * 消费模式，HTTP-http请求，KAFKA-kafka消费
     */
    @Excel(name = "消费模式*", no = 2, selects = {"HTTP", "KAFKA"}, selectEns = {"HTTP", "KAFKA"})
    @Schema(description = "消费模式")
    private String consumeMode;

    /**
     * api路径
     */
    @Excel(name = "api路径", no = 3)
    @Schema(description = "api路径")
    private String apiPath;

    /**
     * 请求方式，GET-get请求，POST-post请求
     */
    @Excel(name = "请求方式", no = 4, selects = {"GET", "POST"}, selectEns = {"GET", "POST"})
    @Schema(description = "请求方式")
    private String requestType;

    /**
     * api模式，standard-标准模式，sql-基础，uq - uq模式，custom-自定义模式
     */
    @Excel(name = "api模式", no = 5, selects = {"kafka模式", "标准模式", "sql模式", "uq模式", "自定义模式"},
            selectEns = {"KAFKA", "STANDARD", "SQL", "UQ", "CUSTOM"})
    @Schema(description = "api模式")
    private String apiMode;

    /**
     * 数据源类型KAFKA, ELASTICSEARCH, CLICKHOUSE, MYSQL
     */
    @Excel(name = "数据源类型*", no = 6, selects = {"KAFKA", "MYSQL", "CLICKHOUSE", "HIVE", "CUSTOM"},
            selectEns = {"KAFKA", "MYSQL", "CLICKHOUSE", "HIVE", "CUSTOM"})
    @Schema(description = "数据源类型")
    private String dsType;

    /**
     * 数据源id
     */
    @Schema(description = "数据源id")
    private Long dsId;


    @Excel(name = "数据源名称", no = 7)
    @Schema(description = "数据源名称")
    private String dsName;

    /**
     * kafka主题
     */
    @Excel(name = "数据源类型", no = 8)
    @Schema(description = "kafka主题")
    private String topic;

    /**
     * 数据库
     */
    @Excel(name = "数据库", no = 9)
    @Schema(description = "数据库")
    private String databaseName;

    /**
     * 数据表
     */
    @Excel(name = "数据表", no = 10)
    @Schema(description = "数据表")
    private String dataTable;

    /**
     * 配置信息，api不同模式有不同配置
     */
    @Excel(name = "配置信息*", no = 11)
    @Schema(description = "配置信息")
    private String setting;

    /**
     * 高级配置，限流以及缓存等
     */
    @Excel(name = "高级配置", no = 12)
    @Schema(description = "高级配置")
    private String extraSetting;

    /**
     * 自定义处理的全限定类名
     */
    @Excel(name = "自定义处理的全限定类名", no = 13)
    @Schema(description = "自定义处理的全限定类名")
    private String handleClass;

    /**
     * 描述信息
     */
    @Excel(name = "描述信息", no = 14)
    @Schema(description = "描述信息")
    private String description;

    /**
     * 标签信息
     */
    @Excel(name = "标签", no = 15)
    @Schema(description = "标签")
    private String tags;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getApiGroupId() {
        return apiGroupId;
    }

    public void setApiGroupId(Long apiGroupId) {
        this.apiGroupId = apiGroupId;
    }

    public String getApiGroupName() {
        return apiGroupName;
    }

    public void setApiGroupName(String apiGroupName) {
        this.apiGroupName = apiGroupName;
    }

    public String getConsumeMode() {
        return consumeMode;
    }

    public void setConsumeMode(String consumeMode) {
        this.consumeMode = consumeMode;
    }

    public String getApiPath() {
        return apiPath;
    }

    public void setApiPath(String apiPath) {
        this.apiPath = apiPath;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getApiMode() {
        return apiMode;
    }

    public void setApiMode(String apiMode) {
        this.apiMode = apiMode;
    }

    public String getDsType() {
        return dsType;
    }

    public void setDsType(String dsType) {
        this.dsType = dsType;
    }

    public Long getDsId() {
        return dsId;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

    public String getDsName() {
        return dsName;
    }

    public void setDsName(String dsName) {
        this.dsName = dsName;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getDataTable() {
        return dataTable;
    }

    public void setDataTable(String dataTable) {
        this.dataTable = dataTable;
    }

    public String getSetting() {
        return setting;
    }

    public void setSetting(String setting) {
        this.setting = setting;
    }

    public String getExtraSetting() {
        return extraSetting;
    }

    public void setExtraSetting(String extraSetting) {
        this.extraSetting = extraSetting;
    }

    public String getHandleClass() {
        return handleClass;
    }

    public void setHandleClass(String handleClass) {
        this.handleClass = handleClass;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }
}
