package com.eoi.jax.web.data.service.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.enumrate.DsColumnTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsOperationEnum;
import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zsc
 * @create 2023/8/18 10:03
 */
public class ParamUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ParamUtil.class);

    public static void convertBetweenValue(Map<String, Object> mergeMap, String operation, String columnName, Object mergeValue) {
        try {
            if (mergeValue == null) {
                return;
            }
            if (DsOperationEnum.BETWEEN.code().equals(operation)) {
                if (mergeValue instanceof List) {
                    List<Object> list = (List<Object>) mergeValue;
                    if (list.size() != 2) {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                    Object obj1 = list.get(0);
                    Object obj2 = list.get(1);
                    if (!(obj1 instanceof String) && !NumberUtil.isNumber(String.valueOf(obj1))) {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                    if (!(obj2 instanceof String) && !NumberUtil.isNumber(String.valueOf(obj2))) {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                    mergeMap.put(SqlUtil.getBetweenFirstParamName(columnName), obj1);
                    mergeMap.put(SqlUtil.getBetweenSecondParamName(columnName), obj2);
                } else {
                    throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                            ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                }
            }
            if (DsOperationEnum.NOT_BETWEEN.code().equals(operation)) {
                if (mergeValue instanceof List) {
                    List<Object> list = (List<Object>) mergeValue;
                    if (list.size() != 2) {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                    Object obj1 = list.get(0);
                    Object obj2 = list.get(1);
                    if (!(obj1 instanceof String) && !NumberUtil.isNumber(String.valueOf(obj1))) {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                    if (!(obj2 instanceof String) && !NumberUtil.isNumber(String.valueOf(obj2))) {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                    mergeMap.put(SqlUtil.getNotBetweenFirstParamName(columnName), obj1);
                    mergeMap.put(SqlUtil.getNotBetweenSecondParamName(columnName), obj2);
                } else {
                    throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                            ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                }
            }
        } catch (Exception e) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    "参数" + columnName + "值 " + mergeValue + " 与定义操作符 " + operation + " 要求的数据格式 [数字/字符串,数字/字符串] 不一致：" + e.getMessage());
        }
    }

    @SuppressWarnings("all")
    public static Object convertReqVal(String columnName, String columnType, Object reqVal, String operation) {
        if (reqVal == null) {
            return null;
        }
        try {
            if (DsColumnTypeEnum.OBJECT.code().equals(columnType)) {
                if (reqVal instanceof String) {
                    try {
                        JSONObject jsonObject = JSONUtil.parseObj(reqVal, true);
                        Map<String, Object> map = JSONUtil.toBean(jsonObject, Map.class);
                        reqVal = map;
                    } catch (Exception e) {
                        reqVal = reqVal;
                    }
                }
            }
            if (DsColumnTypeEnum.INT.code().equals(columnType)) {
                reqVal = Integer.parseInt(String.valueOf(reqVal));
            }
            if (DsColumnTypeEnum.LONG.code().equals(columnType)) {
                reqVal = Long.parseLong(String.valueOf(reqVal));
            }
            if (DsColumnTypeEnum.DOUBLE.code().equals(columnType)) {
                reqVal = Double.parseDouble(String.valueOf(reqVal));
            }
            if (DsColumnTypeEnum.STRING.code().equals(columnType)) {
                reqVal = String.valueOf(reqVal);
                if (DsOperationEnum.IN.code().equals(operation) || DsOperationEnum.NOT_IN.code().equals(operation)
                        || DsOperationEnum.BETWEEN.code().equals(operation) || DsOperationEnum.NOT_BETWEEN.code().equals(operation)) {
                    reqVal = getStrings((String) reqVal);
                }
            }
            if (DsColumnTypeEnum.BOOLEAN.code().equals(columnType)) {
                String s = String.valueOf(reqVal);
                if ("true".equalsIgnoreCase(s) || "false".equalsIgnoreCase(s) || "0".equalsIgnoreCase(s) || "1".equalsIgnoreCase(s)) {
                    reqVal = Boolean.parseBoolean(s);
                } else {
                    throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                            ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                }
            }
            if (DsColumnTypeEnum.LIST_OBJECT.code().equals(columnType)) {
                if (reqVal instanceof String) {
                    try {
                        List<Object> mapList = JSONUtil.toList(JSONUtil.parseArray((String) reqVal, true), Object.class);
                        reqVal = mapList;
                    } catch (Exception e) {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                } else if (!(reqVal instanceof List)) {
                    throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                            ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                }
            }
            if (DsColumnTypeEnum.LIST_INT.code().equals(columnType)) {
                List<String> list = getListString(reqVal);
                List<Integer> result = new LinkedList();
                for (String s : list) {
                    result.add(Integer.parseInt(s));
                }
                reqVal = result;
            }
            if (DsColumnTypeEnum.LIST_LONG.code().equals(columnType)) {
                List<String> list = getListString(reqVal);
                List<Long> result = new LinkedList();
                for (String s : list) {
                    result.add(Long.parseLong(s));
                }
                reqVal = result;
            }
            if (DsColumnTypeEnum.LIST_DOUBLE.code().equals(columnType)) {
                List<String> list = getListString(reqVal);
                List<Double> result = new LinkedList();
                for (String s : list) {
                    result.add(Double.parseDouble(s));
                }
                reqVal = result;
            }
            if (DsColumnTypeEnum.LIST_STRING.code().equals(columnType)) {
                reqVal = getListString(reqVal);
            }
            if (DsColumnTypeEnum.LIST_BOOLEAN.code().equals(columnType)) {
                List<String> list = getListString(reqVal);
                List<Boolean> result = new LinkedList();
                for (String s : list) {
                    if ("true".equalsIgnoreCase(s) || "false".equalsIgnoreCase(s)
                            || "0".equalsIgnoreCase(s) || "1".equalsIgnoreCase(s)) {
                        result.add(Boolean.parseBoolean(s));
                    } else {
                        throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                                ResponseCode.ILLEGAL_REQUEST_PARAM.getMessage());
                    }
                }
                reqVal = result;
            }
            // 处理空集合
            if (reqVal instanceof List) {
                List list = (List) reqVal;
                if (list.size() == 0) {
                    reqVal = null;
                }
            }
        } catch (Exception e) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    "参数" + columnName + "值 " + reqVal + " 与定义类型" + columnType + "不一致：" + e.getMessage());
        }
        return reqVal;
    }


    public static List<String> getListString(Object reqVal) {
        if (reqVal instanceof List) {
            List<Object> list = (List) reqVal;
            return list.stream().map(x -> String.valueOf(x)).collect(Collectors.toList());
        } else {
            return getStrings(String.valueOf(reqVal));
        }
    }

    public static List<String> getStrings(String value) {
        value = value.trim();
        if (value.startsWith("[") && value.endsWith("]")) {
            return Arrays.asList(value.substring(1, value.length() - 1).split(","));
        } else {
            return Arrays.asList(value.split(","));
        }
    }

}
