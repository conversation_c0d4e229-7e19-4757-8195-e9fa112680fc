package com.eoi.jax.web.data.service.model.api.group;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbApiGroup;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: tangy
 * @Date: 2023/7/24
 * @Desc: api分组接口增加
 **/
public class ApiGroupCreateReq implements ICreateModel<TbApiGroup> {
    /**
     * 接口名
     */
    @Schema(description = "接口名", required = true)
    private String name;
    /**
     * 上级Id
     */
    @Schema(description = "上级节点Id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long parentId;


    /**
     * 描述信息
     */
    @Schema(description = "描述信息")
    private String description;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public TbApiGroup toEntity() {
        TbApiGroup entityNew = ICreateModel.super.toEntity(new TbApiGroup());
        return entityNew;
    }
}
