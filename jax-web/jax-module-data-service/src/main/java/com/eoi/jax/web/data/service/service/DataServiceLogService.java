package com.eoi.jax.web.data.service.service;

import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.service.model.apicomponent.DataServiceLogReq;
import com.eoi.jax.web.data.service.model.apicomponent.DataServiceLogResp;

/**
 * <AUTHOR>
 * @date 2023/8/23
 */
public interface DataServiceLogService {


    /**
     * 根据条件查询数据服务日志
     *
     * @param req
     * @return
     */
    Paged<DataServiceLogResp> query(DataServiceLogReq req);

}
