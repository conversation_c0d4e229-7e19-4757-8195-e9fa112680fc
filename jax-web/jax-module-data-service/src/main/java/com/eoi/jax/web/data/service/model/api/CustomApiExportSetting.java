package com.eoi.jax.web.data.service.model.api;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.model.apisetting.*;
import com.eoi.jax.api.dataservice.model.mask.ApiSettingMasking;
import com.eoi.jax.api.dataservice.model.meta.DsDatasourceParamMeta;
import com.eoi.jax.api.dataservice.model.meta.DsReqParamMeta;
import com.eoi.jax.api.dataservice.model.meta.DsRespParamMeta;
import com.eoi.jax.api.dataservice.model.meta.ServiceParamMeta;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.data.service.model.handler.DsHandlerResp;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class CustomApiExportSetting {

    private String prevScript;

    private String postScript;

    private List<Datasource> datasources = new LinkedList<>();

    private List<RequestColumn> requestColumns = new LinkedList<>();

    private List<ResponseColumn> responseColumns = new LinkedList<>();

    private List<ServiceSetting> serviceSettings = new LinkedList<>();

    public static class Datasource {
        private String name;
        private String customDsName;
        private String type;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long dsId;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCustomDsName() {
            return customDsName;
        }

        public void setCustomDsName(String customDsName) {
            this.customDsName = customDsName;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Long getDsId() {
            return dsId;
        }

        public void setDsId(Long dsId) {
            this.dsId = dsId;
        }
    }


    public static class ServiceSetting {
        private String name;
        private String value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public static class RequestColumn {
        private String name;
        private String defaultValue;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }
    }

    public static class ResponseColumn {
        private String name;
        private ApiSettingMasking masking;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public ApiSettingMasking getMasking() {
            return masking;
        }

        public void setMasking(ApiSettingMasking masking) {
            this.masking = masking;
        }
    }

    public String getPrevScript() {
        return prevScript;
    }

    public void setPrevScript(String prevScript) {
        this.prevScript = prevScript;
    }

    public String getPostScript() {
        return postScript;
    }

    public void setPostScript(String postScript) {
        this.postScript = postScript;
    }

    public List<Datasource> getDatasources() {
        return datasources;
    }

    public void setDatasources(List<Datasource> datasources) {
        this.datasources = datasources;
    }

    public List<RequestColumn> getRequestColumns() {
        return requestColumns;
    }

    public void setRequestColumns(List<RequestColumn> requestColumns) {
        this.requestColumns = requestColumns;
    }

    public List<ResponseColumn> getResponseColumns() {
        return responseColumns;
    }

    public void setResponseColumns(List<ResponseColumn> responseColumns) {
        this.responseColumns = responseColumns;
    }

    public List<ServiceSetting> getServiceSettings() {
        return serviceSettings;
    }

    public void setServiceSettings(List<ServiceSetting> serviceSettings) {
        this.serviceSettings = serviceSettings;
    }

    public CustomApiSettingModel fillPropertiesWithOldSetting(CustomApiSettingModel oldSettings, Map<String, Long> dsMap) {
        CustomApiSettingModel newSetting = new CustomApiSettingModel();
        ModelBeanUtil.copyBean(oldSettings, newSetting);
        if (prevScript != null) {
            newSetting.setPrevScript(prevScript);
        }
        if (postScript != null) {
            newSetting.setPostScript(postScript);
        }
        for (ApiSettingDatasource meta : newSetting.getDatasources()) {
            for (Datasource ds : this.getDatasources()) {
                if (ds.getName().equals(meta.getName())) {
                    if (StrUtil.isNotBlank(ds.getCustomDsName())) {
                        meta.setDsId(dsMap.get(ds.getCustomDsName()));
                    } else {
                        meta.setDsId(ds.getDsId());
                    }
                    break;
                }
            }
        }
        for (ApiServiceSetting meta : newSetting.getServiceSettings()) {
            for (ServiceSetting setting : this.serviceSettings) {
                if (setting.getName().equals(meta.getName()) && setting.getValue() != null) {
                    meta.setValue(setting.getValue());
                    break;
                }
            }
        }
        for (ApiSettingReqColumn meta : newSetting.getRequestColumns()) {
            for (RequestColumn column : this.requestColumns) {
                if (column.getName().equals(meta.getName()) && column.getDefaultValue() != null) {
                    meta.setDefaultValue(column.getDefaultValue());
                    break;
                }
            }
        }
        for (ApiSettingRespColumn meta : newSetting.getResponseColumns()) {
            for (ResponseColumn column : this.responseColumns) {
                if (column.getName().equals(meta.getName()) && column.getMasking() != null) {
                    meta.setMasking(column.getMasking());
                    break;
                }
            }
        }
        return newSetting;
    }

    public CustomApiSettingModel fillPropertiesWithHandler(DsHandlerResp handler, Map<String, Long> dsMap) {
        CustomApiSettingModel newSetting = new CustomApiSettingModel();
        List<ApiSettingDatasource> newDatasources = new LinkedList<>();
        List<ApiSettingReqColumn> newRequestColumns = new LinkedList<>();
        List<ApiSettingRespColumn> newResponseColumns = new LinkedList<>();
        List<ApiServiceSetting> serviceSetting = new LinkedList<>();
        newSetting.setDatasources(newDatasources);
        newSetting.setRequestColumns(newRequestColumns);
        newSetting.setResponseColumns(newResponseColumns);
        newSetting.setServiceSettings(serviceSetting);
        newSetting.setAccessJaxMeta(handler.getAccessJaxMeta());
        newSetting.setEnablePaged(handler.getEnablePaged());
        newSetting.setPostScript(postScript);
        newSetting.setPrevScript(prevScript);
        List<DsDatasourceParamMeta> dsDatasourceParamList = JSONUtil.toList(JSONUtil.parseArray(handler.getDsSetting()),
                DsDatasourceParamMeta.class);
        List<DsReqParamMeta> dsReqParamMetaList = JSONUtil.toList(JSONUtil.parseArray(handler.getReqSetting()),
                DsReqParamMeta.class);
        List<DsRespParamMeta> dsRespParamMetaList = JSONUtil.toList(JSONUtil.parseArray(handler.getRespSetting()),
                DsRespParamMeta.class);
        List<ServiceParamMeta> sericeParamMetaList = JSONUtil.toList(JSONUtil.parseArray(handler.getServiceSetting()),
                ServiceParamMeta.class);
        for (DsDatasourceParamMeta meta : dsDatasourceParamList) {
            ApiSettingDatasource newDs = new ApiSettingDatasource();
            newDs.setName(meta.getName());
            newDs.setType(meta.getType().code());
            newDs.setDisplay(meta.getDisplay());
            newDs.setDescription(meta.getDescription());
            for (Datasource ds : this.getDatasources()) {
                if (ds.getName().equals(meta.getName())) {
                    if (StrUtil.isNotBlank(ds.getCustomDsName())) {
                        newDs.setDsId(dsMap.get(ds.getCustomDsName()));
                    } else {
                        newDs.setDsId(ds.getDsId());
                    }
                    break;
                }
            }
            newDatasources.add(newDs);
        }
        for (ServiceParamMeta meta : sericeParamMetaList) {
            ApiServiceSetting newServiceSetting = new ApiServiceSetting();
            ModelBeanUtil.copyBean(meta, newServiceSetting);
            newServiceSetting.setValue(meta.getValue().toString());
            serviceSetting.add(newServiceSetting);
            for (ServiceSetting setting : this.serviceSettings) {
                if (setting.getName().equals(meta.getName()) && setting.getValue() != null) {
                    newServiceSetting.setValue(setting.getValue());
                    break;
                }
            }
        }
        mappingRequestColumns(newRequestColumns, dsReqParamMetaList);
        mappingRespColumns(newResponseColumns, dsRespParamMetaList);
        return newSetting;
    }

    private void mappingRequestColumns(List<ApiSettingReqColumn> newRequestColumns,
                                       List<DsReqParamMeta> dsReqParamMetaList) {
        for (DsReqParamMeta meta : dsReqParamMetaList) {
            ApiSettingReqColumn newReqCol = new ApiSettingReqColumn();
            newReqCol.setName(meta.getName());
            newReqCol.setDesc(meta.getDesc());
            newReqCol.setType(meta.getType());
            newReqCol.setRequired(meta.getRequired());
            newReqCol.setMappingField("");
            newReqCol.setOperation("");
            newReqCol.setSampleValue(meta.getSampleValue());
            newReqCol.setPosition("");
            newReqCol.setDefaultValue(meta.getDefaultValue());
            newReqCol.setColumnType("");
            for (RequestColumn column : this.requestColumns) {
                if (column.getName().equals(meta.getName()) && column.getDefaultValue() != null) {
                    newReqCol.setDefaultValue(column.getDefaultValue());
                    break;
                }
            }
            newRequestColumns.add(newReqCol);
        }
    }

    private void mappingRespColumns(List<ApiSettingRespColumn> newResponseColumns,
                                    List<DsRespParamMeta> dsRespParamMetaList) {
        for (DsRespParamMeta meta : dsRespParamMetaList) {
            ApiSettingRespColumn newRespCol = new ApiSettingRespColumn();
            newRespCol.setName(meta.getName());
            newRespCol.setDesc(meta.getDesc());
            newRespCol.setType(meta.getType());
            newRespCol.setMappingField("");
            newRespCol.setSampleValue(meta.getSampleValue());
            newRespCol.setColumnType("");
            for (ResponseColumn column : this.responseColumns) {
                if (column.getName().equals(meta.getName()) && column.getMasking() != null) {
                    newRespCol.setMasking(column.getMasking());
                    break;
                }
            }
            newResponseColumns.add(newRespCol);
        }
    }
}
