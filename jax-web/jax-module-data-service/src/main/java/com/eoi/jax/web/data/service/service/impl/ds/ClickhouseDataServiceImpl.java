package com.eoi.jax.web.data.service.service.impl.ds;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.api.dataservice.model.apisetting.IApiSettingModel;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.ds.DsApi;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.CkManConnectionSetting;
import com.eoi.jax.api.dataservice.model.request.DataServiceRequest;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.api.dataservice.util.ClickhouseUtil;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.MybatisSqlParseException;
import com.eoi.jax.web.core.integration.model.ck.CkCluster;
import com.eoi.jax.web.core.integration.model.ck.CkManBaseRequestInfo;
import com.eoi.jax.web.core.integration.service.CkManService;
import com.eoi.jax.web.data.service.sqlparser.MyBatisUtil;
import com.eoi.jax.web.data.service.util.SqlUtil;
import com.eoi.jax.web.ingestion.model.datasource.connection.DatasourceConnectionCheckReq;
import com.eoi.jax.web.ingestion.model.datasource.connection.DatasourceConnectionCheckResp;
import com.eoi.jax.web.ingestion.service.DatasourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.sql.Connection;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> zsc
 * @create 2023/7/20 16:01
 */
@Service
public class ClickhouseDataServiceImpl implements IDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClickhouseDataServiceImpl.class);

    @Resource
    private DatasourceService datasourceService;

    @Resource
    private CkManService ckManService;

    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        Connection jdbcConn = null;
        String originSql = "";
        String querySql = "";
        StringBuffer log = dataServiceSetting.getLog() == null ? new StringBuffer() : dataServiceSetting.getLog();
        dataServiceSetting.setLog(log);
        try {
            Map<String, DsDatasource> dsDatasourceMap = dataServiceSetting.getDsDatasourceMap();
            DsDatasource datasource = dsDatasourceMap.get(IDataService.DATASOURCE_KEY);
            DataServiceRequest reqParam = dataServiceSetting.getDataServiceRequest();
            DsApi dsApi = dataServiceSetting.getDsApi();
            CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) datasource.getConnectionSetting();
            if (ckManConnectionSetting.getCkJdbcUrl() == null) {
                throw new DataServiceException(ResponseCode.LOGIN_FAIL.getCode(), "获取CK数据库连接失败,请确认用户名和密码是否正确");
            }
            jdbcConn = ClickhouseUtil.getConnection(ckManConnectionSetting.getCkJdbcUrl(), ckManConnectionSetting.getCkUsername(),
                    ckManConnectionSetting.getCkPassword());

            List<Map<String, Object>> mapList = new LinkedList<>();
            Long totalNum = null;
            IApiSettingModel apiSettingModel = dsApi.getApiSettingModel();
            originSql = apiSettingModel.getScript();
            log.append("\n原始sql语句：").append(originSql);
            LOGGER.debug("api接口" + dsApi.getName() + "原始sql语句：" + originSql);
            String queryXmlSql =
                    "<select id=\"jaxApi" + System.currentTimeMillis() + "\"> " + originSql + " </select>";
            querySql = MyBatisUtil.parseDynamicXmlFormXmlStr(queryXmlSql, reqParam.getParam());
            log.append("\n替换参数后的sql语句：").append(querySql);
            if (apiSettingModel.getEnablePaged()) {
                String countSql = SqlUtil.getCountSqlFromScript(querySql);
                LOGGER.debug("api接口" + dsApi.getName() + "执行统计count语句：" + countSql);
                totalNum = ClickhouseUtil.count(jdbcConn, countSql);
                if (totalNum > 0) {
                    querySql = SqlUtil.getPageQuerySqlFromScript(querySql, reqParam.getPageNum(), reqParam.getPageSize());
                }
            }
            if (totalNum == null || totalNum > 0) {
                LOGGER.debug("api接口" + dsApi.getName() + "执行查询语句：" + querySql);
                mapList = ClickhouseUtil.query(jdbcConn, querySql, SqlUtil.MAX_PAGE_SIZE);
            }

            if (mapList == null) {
                mapList = new LinkedList<>();
            }
            DataServiceResponseData data = new DataServiceResponseData();
            data.setRows(mapList);
            data.setTotalNum((totalNum == null || totalNum < 1) ? mapList.size() : totalNum);
            data.setPageNum(apiSettingModel.getEnablePaged() ? reqParam.getPageNum() : null);
            data.setPageSize(apiSettingModel.getEnablePaged() ? reqParam.getPageSize() : null);
            dataServiceSetting.setSql(querySql);
            return data;
        } catch (DataServiceException e) {
            LOGGER.error("数据服务 " + dataServiceSetting.getDsApi().getName() + " 的请求参数:" +
                            JSONUtil.toJsonPrettyStr(dataServiceSetting.getDataServiceRequest()) +
                            ", \n原始sql：" + originSql + "，\n实际查询sql：" + querySql,
                    e);
            throw e;
        } catch (MybatisSqlParseException e) {
            LOGGER.error("数据服务 " + dataServiceSetting.getDsApi().getName() + " 的请求参数:" +
                            JSONUtil.toJsonPrettyStr(dataServiceSetting.getDataServiceRequest()) +
                            ", \n原始sql：" + originSql,
                    e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("数据服务 " + dataServiceSetting.getDsApi().getName() + " 的请求参数:" +
                            JSONUtil.toJsonPrettyStr(dataServiceSetting.getDataServiceRequest()) +
                            ", \n原始sql：" + originSql + "，\n实际查询sql：" + querySql,
                    e);
            throw new DataServiceException(ResponseCode.SQL_EXECUTE_ERROR.getCode(), e.getMessage());
        } finally {
            ClickhouseUtil.close(null, null, jdbcConn);
        }
    }

    private Connection getJdbcConnection(DsDatasource datasource) {
        CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) datasource.getConnectionSetting();
        DatasourceConnectionCheckReq checkReq = new DatasourceConnectionCheckReq();
        checkReq.setPlatform(datasource.getPlatform());
        checkReq.setConnectMode(datasource.getConnectMode());
        checkReq.setParamMap(BeanUtil.beanToMap(datasource.getConnectionSetting()));
        DatasourceConnectionCheckResp checkResp = datasourceService.connectionCheck(checkReq);
        if (checkResp.getNormalUrls() == null || checkResp.getNormalUrls().size() < 1) {
            throw new DataServiceException(ResponseCode.DATASOURCE_CONNECT_ERROR.getCode(),
                    "ckMan连接失败，ckMan ip=" + ckManConnectionSetting.getAddress());
        }
        String ckManUrl = checkResp.getNormalUrls().get(0);
        // get ck address
        List<CkCluster> clusters = ckManService.getClusters(new CkManBaseRequestInfo(ckManUrl, ckManConnectionSetting.getPrivateKey()));
        CkCluster cluster = null;
        for (CkCluster c : clusters) {
            if (c.getCluster().equals(ckManConnectionSetting.getCkClusterName())) {
                cluster = c;
                break;
            }
        }
        if (cluster == null) {
            throw new DataServiceException(ResponseCode.DATASOURCE_CONNECT_ERROR.getCode(), "ckMan没有找到可用ck集群 [" +
                    ckManConnectionSetting.getCkClusterName() + "]，ckMan ip=" + ckManConnectionSetting.getAddress());
        }

        String username = ckManConnectionSetting.getCkUsername();
        String password = ckManConnectionSetting.getCkPassword();
        Integer port = cluster.getHttpPort();
        Map<String, String> errMsg = new LinkedHashMap<>(16);
        Connection jdbcConn = null;
        for (String host : cluster.getHosts()) {
            String url = String.format("jdbc:clickhouse://%s:%s", host, port);
            try {
                jdbcConn = ClickhouseUtil.getConnection(url, username, password);
                if (jdbcConn != null) {
                    break;
                }
            } catch (Exception e) {
                LOGGER.error("获取clickhouse jdbc连接失败,address=" + url, e);
                errMsg.put(url, e.getMessage());
            }
        }
        if (jdbcConn == null) {
            throw new DataServiceException(ResponseCode.DATASOURCE_CONNECT_ERROR.getCode(), "获取clickhouse连接失败，" +
                    errMsg);
        }
        return jdbcConn;
    }


}
