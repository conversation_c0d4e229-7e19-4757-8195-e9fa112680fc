package com.eoi.jax.web.data.service.controller;

import com.eoi.jax.api.dataservice.model.request.DataServiceRequest;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.service.service.DataApiQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> zsc
 * @create 2023/4/23 17:17
 */
@RestController
public class DataServiceOpenApiController {

    public static final String API_QUERY_URL = "api/open/data-service/";

    @Resource
    private DataApiQueryService dataApiQueryService;

    @Parameters(value = {@Parameter(name = "ApiKey", in = ParameterIn.HEADER, description = "ApiKey"),
        @Parameter(name = "Date", in = ParameterIn.HEADER, description = "GMT时间戳"),
        @Parameter(name = "Nonce", in = ParameterIn.HEADER, description = "请求唯一id"),
        @Parameter(name = "Authorization", in = ParameterIn.HEADER, description = "用户计算的签名"),
    })
    @Operation(summary = "数据服务接口查询")
    @RequestMapping(API_QUERY_URL + "**")
    public Response apiQuery(@RequestBody(required = false) DataServiceRequest param, HttpServletRequest request) {
        return dataApiQueryService.apiQuery(param, request);
    }

}
