package com.eoi.jax.web.data.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.api.dataservice.enumrate.DsColumnTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;
import com.eoi.jax.api.dataservice.model.apisetting.*;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.CkManConnectionSetting;
import com.eoi.jax.api.dataservice.util.ClickhouseUtil;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.ApiModelEnum;
import com.eoi.jax.web.core.common.enumrate.ApiStatusEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.exception.MybatisSqlParseException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.BaseAuthExcelService;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.data.service.factory.DsApiFactory;
import com.eoi.jax.web.data.service.factory.MetadataServiceFactory;
import com.eoi.jax.web.data.service.model.api.*;
import com.eoi.jax.web.data.service.model.api.group.ApiGroupResp;
import com.eoi.jax.web.data.service.model.apicomponent.ApiPublishEvent;
import com.eoi.jax.web.data.service.model.apisetting.ValidateApiSettingReq;
import com.eoi.jax.web.data.service.model.handler.DsHandlerResp;
import com.eoi.jax.web.data.service.model.metadata.TableMetadata;
import com.eoi.jax.web.data.service.service.*;
import com.eoi.jax.web.data.service.sqlparser.OriginSqlFinder;
import com.eoi.jax.web.data.service.sqlparser.OriginSqlFinderRequest;
import com.eoi.jax.web.data.service.sqlparser.OriginSqlFinderResponse;
import com.eoi.jax.web.data.service.sqlparser.SqlParserUtil;
import com.eoi.jax.web.ingestion.enumrate.DatasourcePlatformEnum;
import com.eoi.jax.web.ingestion.enumrate.TagRelationRecordTypeEnum;
import com.eoi.jax.web.ingestion.enumrate.TagTypeEnum;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceResp;
import com.eoi.jax.web.ingestion.model.tag.TagResp;
import com.eoi.jax.web.ingestion.service.DatasourceService;
import com.eoi.jax.web.ingestion.service.KafkaService;
import com.eoi.jax.web.ingestion.service.TagRelationService;
import com.eoi.jax.web.ingestion.service.TagService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.search.query.ApiQueryParam;
import com.eoi.jax.web.repository.search.query.CustomOrder;
import com.eoi.jax.web.repository.service.*;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import java.sql.Connection;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <p>
 * 接口定义表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Service
public class ApiServiceImpl extends BaseAuthExcelService<TbApiService,
    TbApi,
    ApiResp,
    ApiCreateReq,
    ApiUpdateReq,
    ApiQueryReq,
    ApiImportModel> implements ApiService {

    @Resource
    private TbApiService tbApiService;
    @Resource
    private JaxRepository jaxRepository;
    @Resource
    private TbApiGroupService tbApiGroupService;
    @Resource
    private TbApiHistoryService tbApiHistoryService;
    @Resource
    private UrlShortPathService urlShortPathService;

    @Resource
    private TbClientApiService tbClientApiService;
    @Resource
    private TagService tagService;
    @Resource
    private TagRelationService tagRelationService;


    @Resource
    private DatasourceHelperService datasourceHelperService;
    @Resource
    private DatasourceService datasourceService;
    @Resource
    private ApiGroupService apiGroupService;
    @Resource
    private DsHandlerService dsHandlerService;

    @Resource
    private KafkaScramProcessingService kafkaScramProcessingService;

    @Autowired
    private KafkaService kafkaService;
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiServiceImpl.class.getName());

    public ApiServiceImpl(IDaoService<TbApi> dao) {
        super(dao);
    }

    @Override
    public List<ApiResp> all() {
        return super.allWithProjectAuth();
    }

    @Override
    public ApiResp get(Long id) {
        TbApi tbApi = tbApiService.selectByIdWithProjectAuth(id, new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "数据不存在或没有访问权限"));
        return marshallingRespFrom(tbApi);
    }

    /**
     * 根据apiPath查询
     *
     * @param apiPath
     * @return
     */
    @Override
    public TbApi selectByApiPath(String apiPath) {
        return tbApiService.getOne(new LambdaQueryWrapper<TbApi>().eq(TbApi::getApiPath, apiPath));
    }

    @Override
    public Paged<ApiResp> query(ApiQueryReq req) {
        ApiQuerySortReq sort = req.getSort();
        List<CustomOrder> customOrders = new ArrayList<>();
        if (!StringUtils.isEmpty(sort.getName())) {
            CustomOrder nameOrder = new CustomOrder();
            nameOrder.setColumnName("name");
            nameOrder.setType(sort.getName());
            customOrders.add(nameOrder);
        }
        if (!StringUtils.isEmpty(sort.getStatus())) {
            CustomOrder nameOrder = new CustomOrder();
            nameOrder.setColumnName("status");
            nameOrder.setType(sort.getStatus());
            customOrders.add(nameOrder);
        }

        if (!StringUtils.isEmpty(sort.getConsumeMode())) {
            CustomOrder nameOrder = new CustomOrder();
            nameOrder.setColumnName("consume_mode");
            nameOrder.setType(sort.getStatus());
            customOrders.add(nameOrder);
        }
        if (customOrders.size() == 0 || !StringUtils.isEmpty(sort.getUpdateTime())) {
            CustomOrder nameOrder = new CustomOrder();
            nameOrder.setColumnName("update_time");
            nameOrder.setType(StringUtils.isEmpty(sort.getUpdateTime()) ? "desc" : sort.getUpdateTime());
            customOrders.add(nameOrder);
        }
        ApiQueryParam queryParam = ModelBeanUtil.copyBean(req.getFilter(), new ApiQueryParam());
        queryParam.setCustomOrders(customOrders);
        Page<TbApi> paged = tbApiService.pageQuery(req.page(), queryParam);
        long total = paged.getTotal();
        List<ApiResp> list = marshallingRespListFrom(paged.getRecords());
        return new Paged<>(total, list);
    }

    @Override
    public ApiResp create(ApiCreateReq req) {
        ApiResp apiResp = super.create(req);
        apiResp.setTags(req.getTags());
        dealTagAdd(apiResp);
        return apiResp;
    }

    @Override
    public ApiResp update(ApiUpdateReq req) {
        ApiResp apiResp = super.update(req);
        apiResp.setTags(req.getTags());
        dealTagAdd(apiResp);
        return apiResp;
    }

    @Override
    public void whenDelete(TbApi entity) {
        List<VApiUsage> usage = tbApiService.usage(entity.getId());
        Assert.isTrue(ApiStatusEnum.ONLINE.notEquals(entity.getStatus()),
            "当前api已上线,请将服务下线后重试");
        Assert.isTrue(usage.size() == 0,
            "当前api已授权给个[" + usage.size() + "]客户端,请取消授权后重试");

        super.whenDelete(entity);
        // 删除发布历史表
        tbApiHistoryService.remove(Wrappers.<TbApiHistory>lambdaQuery()
            .eq(TbApiHistory::getApiId, entity.getId()));

    }

    @Override
    public ApiResp delete(Long id) {
        return super.delete(id);
    }

    @Override
    public Boolean online(List<Long> ids) {
        List<TbApiHistory> currentPublished = new ArrayList<>();
        List<TbApi> all = tbApiService.list(Wrappers.<TbApi>lambdaQuery().in(TbApi::getId, ids));
        List<TbApi> collect =
            all.stream().filter(item -> !ApiStatusEnum.ONLINE.equals(item.getStatus()) && 1 == item.getIsPublished())
                .collect(Collectors.toList());
        List<String> unPublishedItem =
            all.stream().filter(item -> 1 != item.getIsPublished()).collect(Collectors.toList())
                .stream().map(item -> item.getName()).collect(Collectors.toList());
        collect.forEach(item -> {
            item.setStatus(ApiStatusEnum.ONLINE.code());
            TbApiHistory latestHistory = tbApiHistoryService.getOne(Wrappers.<TbApiHistory>lambdaQuery()
                .eq(TbApiHistory::getApiId, item.getId())
                .eq(TbApiHistory::getVersion, item.getVersion()));
            latestHistory.setStatus(ApiStatusEnum.ONLINE.code());
            currentPublished.add(latestHistory);
            item.setPublishedId(latestHistory.getId());
        });
        if (collect.size() > 0) {
            tbApiService.updateBatchById(collect);
        }
        if (currentPublished.size() > 0) {
            tbApiHistoryService.updateBatchById(currentPublished);
        }
        Assert.isTrue(unPublishedItem.size() == 0,
            "api服务[" + StringUtils.join(unPublishedItem, ",") + "]未发布或发布后变更，需重新发布后才能上线");
        return Boolean.TRUE;
    }

    @Override
    public Boolean offline(List<Long> ids) {
        List<TbApiHistory> currentPublished = new ArrayList<>();
        List<TbApi> all = tbApiService.list(Wrappers.<TbApi>lambdaQuery().in(TbApi::getId, ids));
        List<TbApi> collect = all.stream().filter(item -> ApiStatusEnum.ONLINE.equals(item.getStatus()))
            .collect(Collectors.toList());
        collect.forEach(item -> {
            item.setStatus(ApiStatusEnum.OFFLINE.code());
            TbApiHistory latestHistory = tbApiHistoryService.getOne(Wrappers.<TbApiHistory>lambdaQuery()
                .eq(TbApiHistory::getApiId, item.getId())
                .eq(TbApiHistory::getVersion, item.getVersion()));
            latestHistory.setStatus(ApiStatusEnum.OFFLINE.code());
            currentPublished.add(latestHistory);
        });
        if (collect.size() > 0) {
            tbApiService.updateBatchById(collect);
        }

        if (currentPublished.size() > 0) {
            tbApiHistoryService.updateBatchById(currentPublished);
        }
        List<String> unPublishedItem =
            all.stream().filter(item -> 1 != item.getIsPublished()).collect(Collectors.toList())
                .stream().map(item -> item.getName()).collect(Collectors.toList());
        Assert.isTrue(unPublishedItem.size() == 0, "api服务[" + StringUtils.join(unPublishedItem,
            ",") + "]未发布无需下线");
        return true;
    }

    @Override
    public ApiResp marshallingRespFrom(TbApi tbApi) {
        ApiResp apiResp = super.marshallingRespFrom(tbApi);
        Map<String, Object> settingMap = new HashMap<>(16);
        if (StringUtils.isNotEmpty(tbApi.getSetting())) {
            try {
                settingMap = JSONUtil.toBean(tbApi.getSetting(), Map.class);
            } catch (Exception e) {
                LOGGER.warn("解析API[{}]配置信息[{}]失败", tbApi.getName(), tbApi.getSetting());
            }
        }
        ApiExtraSetting extraSettingMap = null;
        if (StringUtils.isNotEmpty(tbApi.getExtraSetting())) {
            try {
                extraSettingMap = JSONUtil.toBean(tbApi.getExtraSetting(), ApiExtraSetting.class);
            } catch (Exception e) {
                LOGGER.warn("解析API[{}]高级配置信息[{}]失败", tbApi.getName(), tbApi.getExtraSetting());
            }
        }

        try {
            ApiGroupResp apiGroupResp = apiGroupService.get(apiResp.getApiGroupId());
            apiResp.setApiGroupName(apiGroupResp.getName());
        } catch (Exception e) {
            LOGGER.error("apiGroupId[{}]不存在", apiResp.getApiGroupId());
        }
        apiResp.setSetting(settingMap);
        apiResp.setExtraSetting(extraSettingMap);

        if (apiResp.getDsId() != null) {
            DatasourceResp datasourceResp = null;
            try {
                datasourceResp = datasourceService.get(apiResp.getDsId());
            } catch (Exception e) {
                LOGGER.warn("未知的数据源：{}, msg: {}", apiResp.getDsId(), e.getMessage(), e);
                apiResp.setDsName(String.valueOf(apiResp.getDsId()));
            }
            if (datasourceResp != null) {
                apiResp.setDsName(datasourceResp.getName());
            }
        }
        Long count = tbClientApiService.count(Wrappers.<TbClientApi>lambdaQuery().eq(TbClientApi::getApiId, apiResp.getId()));
        apiResp.setAuthTimes(count.intValue());
        List<TbTagRelation> byRecordIdAndRecordType = tagRelationService
            .getByRecordIdAndRecordType(apiResp.getId(), TagRelationRecordTypeEnum.TB_API);
        List<Long> tagidList = byRecordIdAndRecordType.stream().map(item -> item.getTagId()).collect(Collectors.toList());
        if (tagidList.size() > 0) {
            List<TagResp> tagResps = tagService.listByTagIdList(tagidList);
            List<String> collect1 = tagResps.stream().map(item -> item.getName()).collect(Collectors.toList());
            apiResp.setTags(collect1);
        }
        return apiResp;
    }

    @Override
    public void whenCreate(ApiCreateReq req, TbApi entity) {
        super.whenCreate(req, entity);
        entity.setIsPublished(0);
        entity.setStatus(ApiStatusEnum.DRAFT.code());
        checkGroupIdExist(entity);
        entity.setSetting(JSONUtil.toJsonStr(req.getSetting()));
        entity.setExtraSetting(JSONUtil.toJsonStr(req.getExtraSetting()));
        verifyParamAndRebuildSetting(entity);
    }

    private void checkGroupIdExist(TbApi entity) {
        if (entity.getApiGroupId() != null) {
            tbApiGroupService.existId(entity.getApiGroupId(),
                new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "分组信息不存在"));
        }
    }

    @Override
    public TbApi unMarshallingUpdateReq(ApiUpdateReq req, TbApi entity) {
        Long oldDsId = entity.getDsId();
        String oldCustomDsId = entity.getCustomDsId();
        String oldTopic = entity.getTopic();
        String oldSetting = entity.getSetting();
        Long newDsId = req.getDsId();
        String newTopic = req.getTopic();


        if (ApiModelEnum.KAFKA.equals(entity.getApiMode())) {
            if (!newDsId.equals(oldDsId) || !newTopic.equals(oldTopic)) {
                long num = kafkaScramProcessingService.checkTopicVerifySecret(entity);
                Assert.isTrue(num <= 0, "数据源的topic[" + entity.getTopic() + "]关联了[" + num + "]个认证的客户端");
            }
        }
        if (ApiModelEnum.CUSTOM.equals(entity.getApiMode())) {
            Map<String, Object> settingMap = null;
            try {
                settingMap = JSONUtil.toBean(oldSetting, Map.class);
            } catch (Exception e) {
                LOGGER.warn("解析API[{}]配置信息[{}]失败:{}", req.getName(), req.getSetting(), e.getMessage());
                settingMap = new HashMap<>(1);
            }
            CustomApiSettingModel customApiSettingModel = BeanUtil.mapToBean(settingMap, CustomApiSettingModel.class, false);
            List<ApiSettingDatasource> collect = customApiSettingModel.getDatasources()
                .stream().filter(item -> DatasourcePlatformEnum.KAFKA.code().equalsIgnoreCase(item.getType()))
                .collect(Collectors.toList());
            String newCustomDsId = entity.getCustomDsId();
            if (collect.size() > 0) {
                boolean checkReference = false;
                if (!newCustomDsId.equals(oldCustomDsId)) {
                    checkReference = true;
                }
                if (StringUtils.isNotEmpty(newTopic) && !newTopic.equals(oldTopic)) {
                    checkReference = true;
                }
                if (checkReference) {

                    long num = kafkaScramProcessingService.checkTopicVerifySecret(entity);
                    Assert.isTrue(num <= 0, "数据源关联了认证无法修改");
                }
            }
        }
        return super.unMarshallingUpdateReq(req, entity);
    }

    /**
     * 更新kafka数据服务，有强认证时：禁止变更数据源和topic，
     * 提示：数据源xxx的topic xxx关联了认证，不能变更
     * 更新kafka数据服务，无强认证时：允许变更数据源和topic
     *
     * @param req
     * @param entity
     */
    @Override
    public void whenUpdate(ApiUpdateReq req, TbApi entity) {
        super.whenUpdate(req, entity);
        entity.setDescription(Optional.ofNullable(req.getDescription()).orElse(""));
        entity.setIsPublished(0);
        checkGroupIdExist(entity);
        entity.setSetting(JSONUtil.toJsonStr(req.getSetting()));
        entity.setExtraSetting(JSONUtil.toJsonStr(req.getExtraSetting()));
        verifyParamAndRebuildSetting(entity);
    }

    private void checkEnableUpdate(TbApi entity) {
        ApiResp apiResp = get(entity.getId());
        Assert.isTrue(ApiStatusEnum.OFFLINE.equals(apiResp.getStatus()), "已下线的Api才允许修改");
    }

    private void verifyParamAndRebuildSetting(TbApi req) {
        Assert.isTrue(StringUtils.isNotBlank(req.getName()), "Api名字不能为空");

        if (!ApiModelEnum.CUSTOM.code().equals(req.getApiMode())) {
            Assert.isTrue(req.getDsId() != null, "数据源Id不能为空");
            DatasourceResp datasourceResp = null;
            try {
                datasourceResp = datasourceService.get(req.getDsId());
            } catch (Exception e) {

            }
            Assert.isTrue(datasourceResp != null, "数据源Id不存在");
            req.setDsType(datasourceResp.getPlatform());
        }
        if (StringUtils.isEmpty(req.getApiPath())) {
            req.setApiPath(generateApiPath());
        }
        Map<String, Object> settingMap = null;
        try {
            settingMap = JSONUtil.toBean(req.getSetting(), Map.class);
        } catch (Exception e) {
            LOGGER.warn("解析API[{}]配置信息[{}]失败:{}", req.getName(), req.getSetting(), e.getMessage());
            settingMap = new HashMap<>(1);
        }

        IDsApiService dsApiService = getDsApiService(req.getApiMode(), null);
        IApiSettingModel apiSettingModelInstance = dsApiService.getApiSettingModelInstance(settingMap);
        ValidateApiSettingReq validateApiSettingReq = new ValidateApiSettingReq();
        validateApiSettingReq.setTbApi(req);
        validateApiSettingReq.setApiSettingModel(apiSettingModelInstance);
        dsApiService.validate(validateApiSettingReq);

        req.setSetting(JSONUtil.toJsonStr(apiSettingModelInstance));
        List<ApiResp> checkName = all(Wrappers.<TbApi>lambdaQuery().eq(TbApi::getName, req.getName()));
        Assert.isTrue(checkName.size() <= 1, "Api名字已经存在");
        if (checkName.size() == 1) {
            Assert.isTrue(checkName.get(0).getId().equals(req.getId()), "Api名字已经存在");
        }

        Boolean apiPathExists = tbApiService.checkApiPathExists(req.getId(), req.getApiPath());
        Assert.isTrue(!apiPathExists, "apiPath已经存在");
        if (StringUtils.isEmpty(req.getStatus())) {
            req.setStatus(ApiStatusEnum.OFFLINE.getCode());
        }
    }

    @Override
    public ApiResp publish(ApiCreateReq req) {
        ApiResp apiResp = create(req);

        publishApiById(apiResp.getId());
        return apiResp;
    }

    @Override
    public Boolean publishApiById(Long id) {
        Assert.notNull(id, "接口不存在");
        TbApi tbApi = tbApiService.getById(id);
        Assert.notNull(tbApi, "接口不存在");
        Assert.isTrue(1 != tbApi.getIsPublished(), "接口已经发布");

        TbApiHistory oldTbApiHistory = Objects.isNull(tbApi.getPublishedId()) ? null :
            tbApiHistoryService.getById(tbApi.getPublishedId());
        Integer versionNum = tbApiHistoryService.getApiLastVersion(tbApi.getId());
        tbApi.setVersion(versionNum + 1);
        if (ApiStatusEnum.DRAFT.code().equals(tbApi.getStatus())) {
            tbApi.setStatus(ApiStatusEnum.OFFLINE.code());
        }
        tbApi.setIsPublished(1);

        TbApiHistory tbApiHistory = new TbApiHistory();
        ModelBeanUtil.copyBean(tbApi, tbApiHistory);
        tbApiHistory.setApiId(tbApi.getId());
        tbApiHistory.setId(null);
        tbApiHistory.setCreateTime(new Date());
        tbApiHistory.setUpdateTime(tbApiHistory.getCreateTime());
        tbApiHistoryService.save(tbApiHistory);
        tbApi.setPublishedId(tbApiHistory.getId());
        if (tbApiService.updateById(tbApi)) {
            Optional<String> oldExtraSettingOptional = Optional.ofNullable(oldTbApiHistory).map(TbApiHistory::getExtraSetting);
            if (oldExtraSettingOptional.isPresent() || StringUtils.isNotBlank(tbApiHistory.getExtraSetting())) {
                ContextHolder.publishEvent(new ApiPublishEvent(tbApi.getId(),
                    oldExtraSettingOptional.orElse(null), tbApiHistory.getExtraSetting()));
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public ApiResp publish(ApiUpdateReq req) {
        ApiResp update = update(req);
        publishApiById(update.getId());
        return update;
    }

    public OriginSqlFinder apiTestForRequest(ApiTestReq apiTestReq) throws Exception {

        return getSqlRequestParams(apiTestReq);
    }

    private OriginSqlFinder getSqlRequestParams(ApiTestReq apiTestReq) throws Exception {
        return apiTest(apiTestReq, false);
    }

    public OriginSqlFinder apiTestForResponse(ApiTestReq apiTestReq) throws Exception {

        return apiTest(apiTestReq, true);
    }

    @Override
    public OriginSqlFinder apiTest(ApiTestReq apiTestReq, Boolean withResponse) throws Exception {
        Assert.isTrue(StringUtils.isNoneEmpty(apiTestReq.getApiSql()), "请传入apiSql");
        Long dataSourceId = apiTestReq.getDataSourceId();


        OriginSqlFinder originSqlFinder = SqlParserUtil.parseSelectSql(apiTestReq.getApiSql(), !withResponse);
        List<Map<String, Object>> query = null;
        if (!withResponse) {
            return originSqlFinder;
        }
        if (originSqlFinder.getSqlParseError() != null) {
            DsDatasource datasource = datasourceHelperService.getDsDatasource(dataSourceId);
            if (DatasourcePlatformEnum.CLICKHOUSE.equals(datasource.getPlatform())) {
                CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) datasource.getConnectionSetting();
                Connection jdbcConn = ClickhouseUtil.getConnection(ckManConnectionSetting.getCkJdbcUrl(),
                    ckManConnectionSetting.getCkUsername(),
                    ckManConnectionSetting.getCkPassword());
                query = ClickhouseUtil.query(jdbcConn, originSqlFinder.getErrorSql(), 1);
            } else {
                query = new ArrayList<>();
            }
            if (query.size() > 0) {
                Set<String> strings = query.get(0).keySet();
                originSqlFinder.setColumns(new ArrayList<>(strings));
                List<ApiSettingRespColumn> respColumns = new ArrayList<>();
                for (String key : strings) {
                    ApiSettingRespColumn respColumn = new ApiSettingRespColumn();
                    respColumn.setName(key);
                    respColumns.add(respColumn);
                }
                originSqlFinder.setResponseParams(respColumns);
            } else {
                throw new MybatisSqlParseException(ResponseCode.SQL_PARSE_ERROR.getCode(), "解析SQL异常" + originSqlFinder.getSqlParseError());
            }
        }

        if (originSqlFinder.getResponseParams().size() > 0) {
            Map<String, Integer> responseParamMap = new HashMap<>(8);

            originSqlFinder.getResponseParams().forEach(item -> {

                item.setName(SqlParserUtil.responseColumnNameFormatter(item.getName()));
                Integer columNum = responseParamMap.get(item.getName());
                if (columNum != null) {
                    item.setName(item.getName() + "(" + columNum + ")");
                    responseParamMap.put(item.getName(), columNum + 1);

                } else {
                    responseParamMap.put(item.getName(), 1);
                }
                if (item.getName().endsWith("id") || item.getName().endsWith("Id")) {
                    item.setType(DsColumnTypeEnum.LONG.code());
                } else {
                    item.setType(DsColumnTypeEnum.STRING.code());
                }
            });

        }
        return originSqlFinder;
    }

    @Override
    public String generateApiPath() {
        return urlShortPathService.generateShortUrl();
    }

    public IApiSettingModel getSettingModel(String apiModel, String handleClass, Map<String, Object> settingMap) {
        return getDsApiService(apiModel, handleClass).getApiSettingModelInstance(settingMap);
    }

    public IDsApiService getDsApiService(String apiModel, String handleClass) {
        IDsApiService serviceInstance = DsApiFactory.getServiceInstance(apiModel, handleClass);
        return serviceInstance;
    }

    @Override
    public OriginSqlFinderRequest analyzeParamRequest(ApiTestReq apiTestReq) throws Exception {
        OriginSqlFinder originSqlFinder = apiTestForRequest(apiTestReq);
        OriginSqlFinderRequest request = new OriginSqlFinderRequest();
        request.setRequestParams(originSqlFinder.getRequestParams());
        request.setParamSorts(originSqlFinder.getParamSorts());
        return request;
    }

    @Override
    public OriginSqlFinderResponse analyzeParamResponse(ApiTestReq apiTestReq) throws Exception {
        OriginSqlFinder originSqlFinder = apiTestForResponse(apiTestReq);
        OriginSqlFinderResponse response = new OriginSqlFinderResponse();
        List<ApiSettingRespColumn> collect = originSqlFinder.getResponseParams().stream().map(item -> {
            item.setName(item.getName().replace("`", ""));
            return item;
        }).collect(Collectors.toList());
        response.setResponseParams(collect);
        return response;
    }


    /**
     * 处理Api标签
     *
     * @param apiResp
     */
    private void dealTagAdd(ApiResp apiResp) {
        if (CollUtil.isEmpty(apiResp.getTags())) {
            tagRelationService.removeByRecordIdAndRecordType(apiResp.getId(), TagRelationRecordTypeEnum.TB_API);
            return;
        }
        tagRelationService.saveTagAndTagRelation(TagTypeEnum.API, apiResp.getTags(),
            TagRelationRecordTypeEnum.TB_API, apiResp.getId());
    }


    @Override
    public Paged<TableMetadata> getTableByDsId(ApiTopicQueryReq req) {
        Paged<TableMetadata> paged = new Paged<>();
        DsDatasource dsDatasource = null;
        try {
            dsDatasource = datasourceHelperService.getDsDatasource(req.getFilter().getDsId());
        } catch (Exception e) {
            LOGGER.warn("未知的数据源：{}, msg: {}", req.getFilter().getDsId(), e.getMessage(), e);
        }

        Assert.notNull(dsDatasource, "指定数据源不存在");

        List<TableMetadata> tableMetadata = getTableList(dsDatasource, req.getFilter().getDatabase());
        if (StringUtils.isNotEmpty(req.getFilter().getName()) && tableMetadata.size() > 0) {
            tableMetadata = tableMetadata.stream()
                .filter(item -> item.getTableName().contains(req.getFilter().getName()))
                .collect(Collectors.toList());
        }
        paged.setTotal(tableMetadata.size());
        paged.setList(getTableListForLimit(tableMetadata, req.getPage(), req.getSize()));
        return paged;
    }

    private List<TableMetadata> getTableListForLimit(List<TableMetadata> tableMetadata,
                                                     Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageSize == null) {
            return tableMetadata;
        }
        if (tableMetadata == null) {
            return CollUtil.newArrayList();
        }
        int startIdx = pageNum * pageSize;
        int endIdx = (pageNum + 1) * pageSize;

        if (startIdx < 0) {
            startIdx = 0;
        }
        if (endIdx > tableMetadata.size()) {
            endIdx = tableMetadata.size();
        }

        if (startIdx > endIdx) {
            return CollUtil.newArrayList();
        }
        return tableMetadata.subList(startIdx, endIdx);
    }

    private List<TableMetadata> getTableList(DsDatasource dsDatasource, String database) {
        List<TableMetadata> tableMetadataList = null;

        if (DatasourcePlatformEnum.KAFKA.code().equals(dsDatasource.getPlatform())) {
            List<String> topicList = kafkaService.listTopic(dsDatasource.getId());
            tableMetadataList = topicList.stream().map(item -> {
                TableMetadata tableMetadata1 = new TableMetadata();
                tableMetadata1.setTableName(item);
                tableMetadata1.setComment(item);
                return tableMetadata1;
            }).collect(Collectors.toList());
        } else {
            DatasourceMetadataService datasourceMetadataService = MetadataServiceFactory.getServiceInstance(dsDatasource.getPlatform());
            tableMetadataList = datasourceMetadataService.allTable(dsDatasource, database);
        }

        return tableMetadataList;
    }

    @Override
    public List<ApiAuthResp> getAuthList(Long apiId) {
        ApiResp apiResp = get(apiId);
        Assert.notNull(apiResp, "api不存在");
        List<VApiUsage> usage = tbApiService.usage(apiId);
        List<ApiAuthResp> apiAuthResps = usage.stream().map(item -> {
            ApiAuthResp authResp = new ApiAuthResp();
            authResp.setClientId(item.getClientId());
            authResp.setClientName(item.getClientName());
            authResp.setDesc(item.getRemark());
            return authResp;
        }).collect(Collectors.toList());
        return apiAuthResps;
    }

    @Override
    public List<UsageResp> usageByEntity(TbApi entity) {

        List<UsageResp> result = new ArrayList<>();
        List<ApiAuthResp> authList = getAuthList(entity.getId());
        authList.stream().forEach(item -> {
            UsageResp usageResp = ModelBeanUtil.copyBean(entity, new UsageResp());
            usageResp.setRelatedObjId(item.getClientId());
            usageResp.setRelatedObjName(item.getClientName());
            usageResp.setRelatedObjType("DataServiceClient");
            usageResp.setType("DataServiceApi");
            result.add(usageResp);

        });
        return result;
    }

    /**
     * excel中sheet页名称
     *
     * @return
     */
    @Override
    public String sheetName() {
        return "数据服务";
    }

    /**
     * 样例数据
     *
     * @return
     */
    @Override
    public List<ApiImportModel> example() {
        return CollUtil.newArrayList();
    }

    /**
     * 校验数据
     * 1.名称转id (例如目录名转目录id)
     * 2.必填项校验，校验不通过则标记该行导入失败
     * 3.标记系统已存在项 (code/nameEn重复)
     *
     * @param importRows
     */
    @Override
    public void verifyData(List<ApiImportModel> importRows) {
        if (CollUtil.isEmpty(importRows)) {
            return;
        }
        List<String> groupNameList = importRows.stream().map(ApiImportModel::getApiGroupName)
            .distinct().collect(Collectors.toList());
        Map<String, TbApiGroup> groupNameMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(groupNameList)) {
            List<TbApiGroup> list = tbApiGroupService.list(Wrappers.<TbApiGroup>lambdaQuery()
                .in(TbApiGroup::getName, groupNameList));
            groupNameMap = CollUtil.isEmpty(list) ? MapUtil.newHashMap() :
                list.stream().collect(Collectors.toMap(TbApiGroup::getName, it -> it, (v1, v2) -> v1));
        }

        String platform = importRows.stream().map(ApiImportModel::getDsType).filter(StrUtil::isNotBlank)
            .filter(it -> !DsDatasourceTypeEnum.CUSTOM.code().equals(it)).distinct()
            .collect(Collectors.joining(","));
        Map<String, DatasourceResp> datasourceMap = MapUtil.newHashMap();
        if (StrUtil.isNotBlank(platform)) {
            List<DatasourceResp> datasourceRespList = datasourceService.listByPlatform(platform);
            datasourceMap = CollUtil.isEmpty(datasourceRespList) ? MapUtil.newHashMap() :
                datasourceRespList.stream().collect(Collectors.toMap(DatasourceResp::getName, it -> it, (v1, v2) -> v1));
        }
        Map<String, Long> dsMap = jaxRepository.datasourceService().list().stream()
                .collect(Collectors.toMap(x -> x.getName(), x -> x.getId(), (x, y) -> y));
        for (ApiImportModel it : importRows) {
            TbApiGroup tbApiGroup = groupNameMap.get(it.getApiGroupName());
            if (Objects.nonNull(tbApiGroup)) {
                it.setApiGroupId(tbApiGroup.getId());
            } else {
                it.setFailedReason("分组不存在");
                continue;
            }
            if (StrUtil.isNotBlank(it.getDsName())) {
                DatasourceResp datasourceResp = datasourceMap.get(it.getDsName());
                if (Objects.nonNull(datasourceResp)) {
                    it.setDsId(datasourceResp.getId());
                } else {
                    it.setFailedReason("数据源不存在");
                    continue;
                }
            }
            List<TbApi> tbApiList = tbApiService.list(Wrappers.<TbApi>lambdaQuery()
                .eq(TbApi::getName, it.getName())
                .eq(TbApi::getApiGroupId, it.getApiGroupId()));
            if (CollUtil.isNotEmpty(tbApiList)) {
                TbApi tbApi = tbApiList.get(0);
                // 权限判断
                if (ContextHolder.getProjectAuthorityEnable()) {
                    TbApi authTbApi = tbApiService.selectByIdWithProjectAuth(tbApi.getId());
                    if (Objects.isNull(authTbApi) || !authTbApi.hasWritePrivilege()) {
                        it.setFailedReason("无权限修改");
                        continue;
                    }
                }
                if (it.isSuccess() && "ONLINE".equals(tbApi.getStatus())) {
                    it.setFailedReason("根据Api名称找到对应API, API已上线，不能修改");
                    continue;
                }
                fillDsIdInSettings(it, tbApi, dsMap);
                it.setId(tbApi.getId());
                it.setCoverField("name");
            } else {
                fillDsIdInSettings(it, null, dsMap);
            }
        }

        importRows.stream().filter(AbstractExcelRowResp::isSuccess).forEach(it -> {
            ApiUpdateReq apiUpdateReq = apiImportModelToApiUpdateReq(it);
            try {
                if (Objects.nonNull(it.getId())) {
                    verifyParamAndRebuildSetting(apiUpdateReq.toEntity(new TbApi()));
                } else {
                    verifyParamAndRebuildSetting(ModelBeanUtil.copyBean(apiUpdateReq, new ApiCreateReq()).toEntity());
                }
            } catch (Exception e) {
                it.setFailedReason(e.getMessage());
            }
        });
    }

    private void fillDsIdInSettings(ApiImportModel it, TbApi tbApi, Map<String, Long> dsMap) {
        if (!ApiModelEnum.CUSTOM.code().equals(it.getApiMode())) {
            return;
        }
        String settingJson = StrUtil.nullToDefault(it.getSetting(), StrUtil.EMPTY_JSON);
        CustomApiExportSetting setting = JsonUtil.decode(settingJson, CustomApiExportSetting.class);
        if (tbApi == null) {
            // 新增自定义服务，settings用handler补全
            DsHandlerResp handler = dsHandlerService.showByClassName(it.getHandleClass());
            CustomApiSettingModel customApiSettingModel = setting.fillPropertiesWithHandler(handler, dsMap);
            it.setSetting(JsonUtil.encode(customApiSettingModel));
        } else {
            // 修改补全settings，用原有的设置补全
            CustomApiSettingModel oldSettings = JsonUtil.decode(tbApi.getSetting(), CustomApiSettingModel.class);
            CustomApiSettingModel customApiSettingModel = setting.fillPropertiesWithOldSetting(oldSettings, dsMap);
            it.setSetting(JsonUtil.encode(customApiSettingModel));
        }
    }

    /**
     * 导入到系统中
     *
     * @param skip
     * @param importRows
     */
    @Override
    public void saveRows(Boolean skip, List<ApiImportModel> importRows) {
        saveRows(skip, importRows,
            (x -> {
                ApiUpdateReq apiUpdateReq = apiImportModelToApiUpdateReq(x);
                return ModelBeanUtil.copyBean(apiUpdateReq, new ApiCreateReq());
            }),
            this::apiImportModelToApiUpdateReq);
    }


    private ApiUpdateReq apiImportModelToApiUpdateReq(ApiImportModel apiImportModel) {
        ApiUpdateReq apiUpdateReq = ModelBeanUtil.copyBean(apiImportModel, new ApiUpdateReq());
        if (StrUtil.isNotBlank(apiImportModel.getSetting())) {
            apiUpdateReq.setSetting(JsonUtil.decode(apiImportModel.getSetting(), new TypeReference<Map<String, Object>>() {
            }));
        }
        if (StrUtil.isNotBlank(apiImportModel.getExtraSetting())) {
            apiUpdateReq.setExtraSetting(JsonUtil.decode(apiImportModel.getExtraSetting(), new TypeReference<Map<String, Object>>() {
            }));
        }
        if (StrUtil.isNotBlank(apiImportModel.getTags())) {
            apiUpdateReq.setTags(JsonUtil.decode(apiImportModel.getTags(), new TypeReference<List<String>>() {
            }));
        }
        return apiUpdateReq;
    }

    /**
     * 导出
     *
     * @param req
     * @return
     */
    @Override
    public List<ApiImportModel> exportData(ImportExcelReq req) {
        List<ApiResp> tbApiList = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getIds())) {
            List<List<Long>> splitList = CollUtil.split(req.getIds(), 100);
            for (List<Long> list : splitList) {
                tbApiList.addAll(exportQuery(() -> {
                    ApiQueryFilterReq filterReq = new ApiQueryFilterReq();
                    filterReq.setIdList(list);
                    return filterReq;
                }));
            }
        } else if (StrUtil.isNotBlank(req.getCondition())) {
            tbApiList.addAll(exportQuery(() -> {
                ApiQueryReq apiQueryReq = JsonUtil.decode(req.getCondition(), ApiQueryReq.class);
                return apiQueryReq.getFilter();
            }));
        } else {
            tbApiList.addAll(exportQuery(ApiQueryFilterReq::new));
        }
        Map<Long, String> dsMap = jaxRepository.datasourceService().list().stream()
                .collect(Collectors.toMap(TbDatasource::getId, TbDatasource::getName));
        return tbApiList.stream().map(it -> {
            ApiImportModel apiImportModel = ModelBeanUtil.copyBean(it, new ApiImportModel());
            if (Objects.nonNull(it.getSetting())) {
                if (ApiModelEnum.CUSTOM.code().equals(it.getApiMode())) {
                    CustomApiExportSetting setting = JsonUtil.decode(JsonUtil.encode(it.getSetting()), CustomApiExportSetting.class);
                    for (CustomApiExportSetting.Datasource datasource : setting.getDatasources()) {
                        datasource.setCustomDsName(dsMap.get(datasource.getDsId()));
                        datasource.setDsId(null);
                    }
                    apiImportModel.setSetting(JSONUtil.toJsonStr(setting));
                } else {
                    apiImportModel.setSetting(JSONUtil.toJsonStr(it.getSetting()));
                }
            }
            if (Objects.nonNull(it.getExtraSetting())) {
                apiImportModel.setExtraSetting(JSONUtil.toJsonStr(it.getExtraSetting()));
            }
            if (Objects.nonNull(it.getTags())) {
                apiImportModel.setTags(JSONUtil.toJsonStr(it.getTags()));
            }
            apiImportModel.setId(null);
            return apiImportModel;
        }).collect(Collectors.toList());

    }


    private List<ApiResp> exportQuery(Supplier<ApiQueryFilterReq> supplier) {
        List<ApiResp> tbApiList = new ArrayList<>();
        for (int i = 0, size = 100; ; i++) {
            ApiQueryReq apiQueryReq = new ApiQueryReq();
            apiQueryReq.setPage(i);
            apiQueryReq.setSize(size);
            apiQueryReq.setFilter(supplier.get());
            Paged<ApiResp> paged = this.query(apiQueryReq);
            if (CollUtil.isEmpty(paged.getList())) {
                break;
            }
            tbApiList.addAll(paged.getList());
        }
        return tbApiList;
    }

}
