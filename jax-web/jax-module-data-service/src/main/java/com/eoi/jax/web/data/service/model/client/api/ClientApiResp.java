package com.eoi.jax.web.data.service.model.client.api;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.repository.entity.TbClientApi;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/7/24
 * @Desc:
 **/
@OpPrimaryKey(name = "id")
@OpPrimaryName(name = "name")
public class ClientApiResp extends ProjectAuthRespModel implements IRespModel<TbClientApi>, IProjectAuthModel, IUserInfoExtensionModel {
    /**
     * id
     */
    @Schema(description = "id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long id;
    /**
     * 授权客户端id
     */
    @Schema(description = "授权客户端id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long clientId;

    /**
     * api接口id
     */
    @Schema(description = "api接口id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long apiId;


    /**
     * VALIDITY-有效，INVALIDITY-失效
     */
    @Schema(description = "VALIDITY-有效，INVALIDITY-失效")
    private String status;

    @Schema(description = "备注")
    /**
     * 备注信息
     */
    private String remark;

    /**
     * 高级配置，限流以及缓存等
     */
    @Schema(description = "高级配置，限流以及缓存等")
    private Map<String, Object> extraSetting;

    /**
     * 是否逻辑删除
     */
    @Schema(description = "是否逻辑删除")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @Schema(description = "创建人Id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long createUser;

    /**
     * 更新人Id
     */
    @Schema(description = "更新人Id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long updateUser;

    /**
     * 授权api数量
     */
    @Schema(description = "授权api数量")
    private Integer apiNum;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getApiId() {
        return apiId;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Map<String, Object> getExtraSetting() {
        return extraSetting;
    }

    public void setExtraSetting(Map<String, Object> extraSetting) {
        this.extraSetting = extraSetting;
    }

    public Integer getApiNum() {
        return apiNum;
    }

    public void setApiNum(Integer apiNum) {
        this.apiNum = apiNum;
    }

    @Override
    public String toString() {
        return "ClientApiResp{" +
                "id=" + id +
                ", clientId=" + clientId +
                ", apiId=" + apiId +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                ", extraSetting=" + extraSetting +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                ", apiNum=" + apiNum +
                '}';
    }
}
