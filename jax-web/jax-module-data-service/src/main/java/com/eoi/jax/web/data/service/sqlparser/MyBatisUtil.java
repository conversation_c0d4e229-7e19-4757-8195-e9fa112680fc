package com.eoi.jax.web.data.service.sqlparser;

import cn.hutool.json.JSONUtil;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.MybatisSqlParseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.builder.BuilderException;
import org.apache.ibatis.builder.SqlSourceBuilder;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.ParameterMode;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.parsing.XNode;
import org.apache.ibatis.parsing.XPathParser;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.scripting.xmltags.XMLScriptBuilder;
import org.apache.ibatis.session.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.StringReader;
import java.util.List;
import java.util.UUID;

/**
 * @Author: tangy
 * @Date: 2023/7/4
 * @Desc:
 **/
public class MyBatisUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(MyBatisUtil.class);
    private static final Configuration CONFIGURATION = new Configuration();
    private static DocumentBuilder documentBuilder;

    static {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(false);
        try {
            documentBuilder = dbf.newDocumentBuilder();
        } catch (ParserConfigurationException e) {
            LOGGER.error("创建对象失败: {}", e.getMessage());
        }
    }


    /**
     * 该方法主要用来解析动态sql,可以使用mybatis的所有标签
     * 解析和赋值的方式都是由mybatis 完成的
     * 赋值绑定几乎完全使用该类 {@link  org.apache.ibatis.scripting.defaults.DefaultParameterHandler#setParameters(java.sql.PreparedStatement)}
     *
     * @param xmlSql          eg:  <select> mybatisXML sql 语句</select>
     * @param parameterObject 对应的参数
     * @return 解析后的sql 语句
     */
    public static String parseDynamicXmlFormXmlStr(String xmlSql, Object parameterObject) {
        try {
            LOGGER.debug("原始sqlXml:{} , params:{}", xmlSql, JSONUtil.toJsonStr(parameterObject));
            //解析成xml
            Document doc = parseXmlDocument(xmlSql);
            if (doc == null) {
                return null;
            }
            //走mybatis 流程 parse成Xnode
            XNode xNode = new XNode(new XPathParser(doc, false), doc.getFirstChild(), null);
            // 之前的所有步骤 都是为了构建 XMLScriptBuilder 对象,
            XMLScriptBuilder xmlScriptBuilder = new XMLScriptBuilder(CONFIGURATION, xNode);

            //解析 静态xml 和动态的xml
            SqlSource sqlSource = xmlScriptBuilder.parseScriptNode();
            MappedStatement ms = new MappedStatement.Builder(CONFIGURATION, UUID.randomUUID().toString(), sqlSource, null).build();

            //将原始sql 与 参数绑定
            BoundSql boundSql = ms.getBoundSql(parameterObject);

            //获得 预编译后的 sql
            String resultSql = boundSql.getSql();
            //将'  ？  '和"  ？  " 替换为 ？
            String executeSql = resultSql.replaceAll("(\'\\s*\\?\\s*\')|(\"\\s*\\?\\s*\")", "?");
            List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
            if (parameterMappings != null) {
                for (int i = 0; i < parameterMappings.size(); i++) {
                    ParameterMapping parameterMapping = parameterMappings.get(i);
                    if (parameterMapping.getMode() != ParameterMode.OUT) {
                        Object value;
                        String propertyName = parameterMapping.getProperty();
                        if (boundSql.hasAdditionalParameter(propertyName)) {
                            value = boundSql.getAdditionalParameter(propertyName);
                        } else if (parameterObject == null) {
                            value = null;
                        } else {
                            MetaObject metaObject = CONFIGURATION.newMetaObject(parameterObject);
                            value = metaObject.getValue(propertyName);
                        }
                        executeSql = executeSql.replaceFirst("[?]", value instanceof String ? "'" + value + "'" : String.valueOf(value));
                    }
                }
            }
            //格式化 sql 移除多余空格
            LOGGER.debug("removeExtraWhitespace -> executeSql: {}", SqlSourceBuilder.removeExtraWhitespaces(executeSql));
            return executeSql;
        } catch (BuilderException e) {
            LOGGER.error("sql解析失败：{} ", e.getCause().getMessage(), e);
            throw new MybatisSqlParseException(ResponseCode.SQL_PARSE_ERROR.getCode(), "sql解析失败：" + e.getCause().getMessage(), e);
        } catch (Exception e) {
            LOGGER.error("解析SQL异常：{}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }


    public static synchronized Document parseXmlDocument(String xmlString) {
        if (StringUtils.isBlank(xmlString)) {
            LOGGER.error("动态解析的SQL 不能为空!!");
            return null;
        }
        try {
            return documentBuilder.parse(new InputSource(new StringReader(xmlString)));
        } catch (Exception e) {
            LOGGER.error("SQL语句解析异常,格式错误:{}", e.getMessage(), e);
            throw new RuntimeException("SQL语句解析异常,格式错误: " + e.getMessage());
        }
    }
}
