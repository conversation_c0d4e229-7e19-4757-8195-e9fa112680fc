package com.eoi.jax.web.data.service.sqlparser;

import org.apache.ibatis.builder.BaseBuilder;
import org.apache.ibatis.builder.BuilderException;
import org.apache.ibatis.parsing.XNode;
import org.apache.ibatis.scripting.xmltags.ChooseSqlNode;
import org.apache.ibatis.scripting.xmltags.ForEachSqlNode;
import org.apache.ibatis.scripting.xmltags.IfSqlNode;
import org.apache.ibatis.scripting.xmltags.MixedSqlNode;
import org.apache.ibatis.scripting.xmltags.SetSqlNode;
import org.apache.ibatis.scripting.xmltags.SqlNode;
import org.apache.ibatis.scripting.xmltags.StaticTextSqlNode;
import org.apache.ibatis.scripting.xmltags.TextSqlNode;
import org.apache.ibatis.scripting.xmltags.TrimSqlNode;
import org.apache.ibatis.scripting.xmltags.VarDeclSqlNode;
import org.apache.ibatis.scripting.xmltags.WhereSqlNode;
import org.apache.ibatis.session.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/7/4
 * @Desc:
 **/
public class OriginXmlScriptBuilder extends BaseBuilder {
    private static Logger log = LoggerFactory.getLogger("SqlParserUtil");

    private final XNode context;
    private boolean isDynamic;
    private final Class<?> parameterType;
    private final Map<String, NodeHandler> nodeHandlerMap = new HashMap<>();

    public OriginXmlScriptBuilder(Configuration configuration, XNode context) {
        this(configuration, context, null);
    }

    public OriginXmlScriptBuilder(Configuration configuration, XNode context, Class<?> parameterType) {
        super(configuration);
        this.context = context;
        this.parameterType = parameterType;
        initNodeHandlerMap();
    }


    private void initNodeHandlerMap() {
        nodeHandlerMap.put("trim", new TrimHandler());
        nodeHandlerMap.put("where", new WhereHandler());
        nodeHandlerMap.put("set", new SetHandler());
        nodeHandlerMap.put("foreach", new ForEachHandler());
        nodeHandlerMap.put("if", new IfHandler());
        nodeHandlerMap.put("choose", new ChooseHandler());
        nodeHandlerMap.put("when", new IfHandler());
        nodeHandlerMap.put("otherwise", new OtherwiseHandler());
        nodeHandlerMap.put("bind", new BindHandler());
    }

    public Map<String, OriginParamType> parseScriptNode() {
        List<SqlNode> allContents = new ArrayList<>();
        List<OriginParamType> paramList = new ArrayList<>();
        parseDynamicTags(context, allContents, paramList);
        Map<String, OriginParamType> paramMap = OriginParamParser.paseParamToMap(paramList);

        return paramMap;
    }

    public void checkHaveParam(XNode node, String data, List<OriginParamType> paramList) {

        OriginParamParser parser = new OriginParamParser("#{", "}");
        if (!node.getName().equalsIgnoreCase("foreach")
        ) {

            List<String> pramMap = parser.parse(data);
            pramMap.forEach(item -> {
                OriginParamType type = new OriginParamType();
                type.setName(item);
                if (!item.contains(".")) {
                    paramList.add(type);
                }
            });
        }
    }

    protected MixedSqlNode parseDynamicTags(XNode node, List<SqlNode> allContents, List<OriginParamType> paramList) {
        List<SqlNode> contents = new ArrayList<>();
        NodeList children = node.getNode().getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            XNode child = node.newXNode(children.item(i));
            if (child.getNode().getNodeType() == Node.CDATA_SECTION_NODE || child.getNode().getNodeType() == Node.TEXT_NODE) {
                String data = child.getStringBody("");
                TextSqlNode textSqlNode = new TextSqlNode(data);
                if (textSqlNode.isDynamic()) {
                    contents.add(textSqlNode);
                    isDynamic = true;
                } else {
                    checkHaveParam(node, data, paramList);
                    contents.add(new StaticTextSqlNode(data));
                }
            } else if (child.getNode().getNodeType() == Node.ELEMENT_NODE) {
                // issue #628
                String nodeName = child.getNode().getNodeName();
                NodeHandler handler = nodeHandlerMap.get(nodeName);
                if (handler == null) {
                    throw new BuilderException("Unknown element <" + nodeName + "> in SQL statement.");
                }
                handler.handleNode(child, contents, paramList);
                isDynamic = true;
            }
        }
        if (allContents != null) {
            allContents.addAll(contents);
        }
        return new MixedSqlNode(contents);
    }

    private interface NodeHandler {
        /**
         * 处理标签数据
         * @param nodeToHandle
         * @param targetContents
         * @param paramList
         */
        void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> paramList);
    }

    private class BindHandler implements NodeHandler {
        BindHandler() {
            // Prevent Synthetic Access
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            final String name = nodeToHandle.getStringAttribute("name");
            final String expression = nodeToHandle.getStringAttribute("value");
            final VarDeclSqlNode node = new VarDeclSqlNode(name, expression);
            targetContents.add(node);
        }
    }

    private class TrimHandler implements NodeHandler {
        TrimHandler() {
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            MixedSqlNode mixedSqlNode = parseDynamicTags(nodeToHandle, targetContents, params);
            String prefix = nodeToHandle.getStringAttribute("prefix");
            String prefixOverrides = nodeToHandle.getStringAttribute("prefixOverrides");
            String suffix = nodeToHandle.getStringAttribute("suffix");
            String suffixOverrides = nodeToHandle.getStringAttribute("suffixOverrides");
            TrimSqlNode trim = new TrimSqlNode(configuration, mixedSqlNode, prefix, prefixOverrides, suffix, suffixOverrides);
            targetContents.add(trim);
        }
    }

    private class WhereHandler implements NodeHandler {
        WhereHandler() {
            // Prevent Synthetic Access
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            MixedSqlNode mixedSqlNode = parseDynamicTags(nodeToHandle, targetContents, params);
            WhereSqlNode where = new WhereSqlNode(configuration, mixedSqlNode);
            targetContents.add(where);
        }
    }

    private class SetHandler implements NodeHandler {
        SetHandler() {
            // Prevent Synthetic Access
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            MixedSqlNode mixedSqlNode = parseDynamicTags(nodeToHandle, targetContents, params);
            SetSqlNode set = new SetSqlNode(configuration, mixedSqlNode);
            targetContents.add(set);
        }
    }

    private class ForEachHandler implements NodeHandler {
        ForEachHandler() {
            // Prevent Synthetic Access
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            MixedSqlNode mixedSqlNode = parseDynamicTags(nodeToHandle, targetContents, params);
            String collection = nodeToHandle.getStringAttribute("collection");
            Boolean nullable = nodeToHandle.getBooleanAttribute("nullable");
            String item = nodeToHandle.getStringAttribute("item");
            String index = nodeToHandle.getStringAttribute("index");
            String open = nodeToHandle.getStringAttribute("open");
            String close = nodeToHandle.getStringAttribute("close");
            String separator = nodeToHandle.getStringAttribute("separator");
            OriginParamType originParamType = new OriginParamType();
            originParamType.setName(collection);
            originParamType.setType("LIST");
            if (checkParamTypeValid(originParamType)) {
                params.add(originParamType);
            }
            ForEachSqlNode forEachSqlNode = new ForEachSqlNode(configuration, mixedSqlNode,
                    collection, nullable, index, item, open, close, separator);
            targetContents.add(forEachSqlNode);
        }
    }

    private boolean checkParamTypeValid(OriginParamType originParamType) {
        return !originParamType.getName().contains(".");
    }

    private class IfHandler implements NodeHandler {
        IfHandler() {
            // Prevent Synthetic Access
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            MixedSqlNode mixedSqlNode = parseDynamicTags(nodeToHandle, targetContents, params);
            String test = nodeToHandle.getStringAttribute("test");

            try {
                params.addAll(OriginParamParser.paseTestParams(test));
            } catch (Exception e) {
                log.error("解析参数失败, 无法识别的条件: {}", test);
            }
            IfSqlNode ifSqlNode = new IfSqlNode(mixedSqlNode, test);
            targetContents.add(ifSqlNode);
        }
    }

    private class OtherwiseHandler implements NodeHandler {
        OtherwiseHandler() {
            // Prevent Synthetic Access
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            MixedSqlNode mixedSqlNode = parseDynamicTags(nodeToHandle, targetContents, params);
            //TODO === 处理参数
            targetContents.add(mixedSqlNode);
        }
    }

    private class ChooseHandler implements NodeHandler {
        ChooseHandler() {
            // Prevent Synthetic Access
        }

        @Override
        public void handleNode(XNode nodeToHandle, List<SqlNode> targetContents, List<OriginParamType> params) {
            List<SqlNode> whenSqlNodes = new ArrayList<>();
            List<SqlNode> otherwiseSqlNodes = new ArrayList<>();
            handleWhenOtherwiseNodes(nodeToHandle, whenSqlNodes, otherwiseSqlNodes, params);
            SqlNode defaultSqlNode = getDefaultSqlNode(otherwiseSqlNodes);
            ChooseSqlNode chooseSqlNode = new ChooseSqlNode(whenSqlNodes, defaultSqlNode);
            targetContents.add(chooseSqlNode);
        }

        private void handleWhenOtherwiseNodes(XNode chooseSqlNode, List<SqlNode> ifSqlNodes,
                                              List<SqlNode> defaultSqlNodes, List<OriginParamType> params) {
            List<XNode> children = chooseSqlNode.getChildren();
            for (XNode child : children) {
                String nodeName = child.getNode().getNodeName();
                NodeHandler handler = nodeHandlerMap.get(nodeName);
                if (child.getName().equalsIgnoreCase("when")) {
                    String test = child.getStringAttribute("test");
                    try {
                        params.addAll(OriginParamParser.paseTestParams(test));
                    } catch (Exception e) {
                        log.error("解析参数失败, 无法识别的条件: {}", test);
                    }
                }
                if (handler instanceof IfHandler) {
                    handler.handleNode(child, ifSqlNodes, params);
                } /*else if (handler instanceof OtherwiseHandler) {
                    handler.handleNode(child, defaultSqlNodes, params);
                }*/
            }
        }

        private SqlNode getDefaultSqlNode(List<SqlNode> defaultSqlNodes) {
            SqlNode defaultSqlNode = null;
            if (defaultSqlNodes.size() == 1) {
                defaultSqlNode = defaultSqlNodes.get(0);
            } else if (defaultSqlNodes.size() > 1) {
                throw new BuilderException("Too many default (otherwise) elements in choose statement.");
            }
            return defaultSqlNode;
        }
    }

}
