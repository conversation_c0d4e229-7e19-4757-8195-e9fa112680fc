package com.eoi.jax.web.data.service.component.collector;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.data.service.model.apicomponent.DataServiceLog;
import com.eoi.jax.web.ingestion.factory.datasource.model.KafkaConnection;
import com.eoi.jax.web.ingestion.util.KafkaUtil;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;

/**
 * Kafka收集器
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
public class DataServiceLogKafkaCollector implements DataServiceLogCollector {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataServiceLogKafkaCollector.class);

    private KafkaProducer<String, String> producer;
    private String topic;


    /**
     * 额外配置, enable, topic
     *
     * @param configMap
     */
    public DataServiceLogKafkaCollector(Map<String, Object> configMap) {
        KafkaConnection kafkaConnection = BeanUtil.mapToBean(configMap, KafkaConnection.class, true);
        Assert.notBlank(kafkaConnection.getBrokerAddress(), "kafka broker连接地址不能为空");

        this.topic = (String) configMap.get("topic");
        Assert.notBlank(topic, "kafka topic不能为空");
        this.producer = KafkaUtil.getProducer(kafkaConnection);
    }

    /**
     * 日志收集
     *
     * @param dataServiceLog
     */
    @Override
    public void collect(DataServiceLog dataServiceLog) {
        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(topic, JsonUtil.encode(dataServiceLog));
            producer.send(record);
            LOGGER.debug("消息发送成功: topic: {}", topic);
        } catch (Exception e) {
            LOGGER.error("发送失败: topic: {}, errMsg: {}", topic, e.getMessage(), e);
        }
    }


    @Override
    public void close() {
        if (Objects.nonNull(producer)) {
            KafkaProducer<String, String> localProducer = this.producer;
            this.producer = null;
            localProducer.close();
        }
        topic = null;
    }


}
