package com.eoi.jax.web.data.service.service;

import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.service.model.apiquery.DsPreviewReq;
import com.eoi.jax.web.data.service.model.apiquery.DsPreviewResp;
import com.eoi.jax.web.data.service.model.apiquery.DsSqlPreviewReq;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/3
 */
public interface JdbcService {

    /**
     * SQL查询预览
     *
     * @param req
     * @return
     */
    Response<DsPreviewResp> preview(DsSqlPreviewReq req);

    /**
     * 指定表查询预览
     * @param param
     * @param reqParamMap
     * @param sql
     * @return
     */
    DataServiceResponseData preview(DsPreviewReq param, Map<String, Object> reqParamMap, String sql);

}
