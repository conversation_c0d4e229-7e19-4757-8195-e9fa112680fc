package com.eoi.jax.web.data.service.model.api.history;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: tangy
 * @Date: 2023/8/3
 * @Desc:
 **/
public class ApiHistoryCompareReq implements Serializable {

    @Schema(description = "源Id, 如果跟最新api比较不传值")
    private Long sourceId;
    @Schema(description = "目标id", required = true)
    @NotNull(message = "目标id不能为空")
    private Long targetId;

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }
}
