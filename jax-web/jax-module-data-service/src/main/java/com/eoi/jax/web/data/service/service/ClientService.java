package com.eoi.jax.web.data.service.service;

import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.service.IBaseProjectAuthService;
import com.eoi.jax.web.data.service.model.api.ApiSimpleResp;
import com.eoi.jax.web.data.service.model.client.ClientApiAuthQueryReq;
import com.eoi.jax.web.data.service.model.client.ClientApiRemoveReq;
import com.eoi.jax.web.data.service.model.client.ClientCreateReq;
import com.eoi.jax.web.data.service.model.client.ClientQueryReq;
import com.eoi.jax.web.data.service.model.client.ClientResp;
import com.eoi.jax.web.data.service.model.client.ClientUpdateReq;
import com.eoi.jax.web.repository.entity.TbClient;
import com.eoi.jax.web.repository.service.TbClientService;

import java.util.List;

/**
 * <p>
 * 授权客户端表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
public interface ClientService extends IBaseProjectAuthService<
        TbClientService,
        TbClient,
        ClientResp,
        ClientCreateReq,
        ClientUpdateReq,
        ClientQueryReq> {

    /**
     * 获取所有信息
     *
     * @return
     */
    List<ClientResp> all();

    /**
     * 根据apiKey查询
     *
     * @param apiKey
     * @return
     */
    TbClient getByApiKey(String apiKey);

    /**
     * 分页查询
     *
     * @param req
     * @return
     */
    Paged<ClientResp> query(ClientQueryReq req);

    /**
     * 新增
     *
     * @param req
     * @return
     */
    ClientResp create(ClientCreateReq req);

    /**
     * 修改
     *
     * @param req
     * @return
     */
    ClientResp update(ClientUpdateReq req);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    ClientResp delete(Long id);

    /**
     * 生成apiKey
     *
     * @return
     */
    String generatorApiKey();

    /**
     * 生成secretKey
     *
     * @return
     */
    String generatorSecretKey();

    /**
     * 查询api
     *
     * @param clientId
     * @param req
     * @return
     */
    Paged<ApiSimpleResp> queryApiList(Long clientId, ClientApiAuthQueryReq req);

    /**
     * 获取客户端授权api列表
     *
     * @param clientId
     * @param req
     * @return
     */
    Paged<ApiSimpleResp> queryApiAuthList(Long clientId, ClientApiAuthQueryReq req);

    /**
     * 取消授权
     *
     * @param req
     * @return
     */
    Boolean authRemove(ClientApiRemoveReq req);
}
