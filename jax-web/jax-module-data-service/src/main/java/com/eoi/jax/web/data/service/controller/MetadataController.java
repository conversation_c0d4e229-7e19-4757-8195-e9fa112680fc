package com.eoi.jax.web.data.service.controller;

import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.service.model.metadata.*;
import com.eoi.jax.web.data.service.service.MetadataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 数据库元数据接口
 *
 * <AUTHOR>
 * @date 2023/8/7
 */
@RestController
public class MetadataController implements V2Controller {

    @Resource
    private MetadataService metadataService;

    @Operation(summary = "获取数据源下所有库")
    @GetMapping("data-service/metadata/databases/{dsId}")
    public Response<List<String>> databases(@Parameter(description = "数据源Id", required = true)
                                            @PathVariable(value = "dsId") Long dsId) {
        return Response.success(metadataService.allDatabase(dsId));
    }

    @Operation(summary = "根据数据源和数据库获取所有表")
    @PostMapping("data-service/metadata/tables")
    public Response<List<TableMetadata>> tables(@Valid @RequestBody TableMetadataReq tableMetadataReq) {
        return Response.success(metadataService.allTable(tableMetadataReq));
    }

    @Operation(summary = "数据库表树形结构")
    @GetMapping("data-service/metadata/database-table-tree/{dsId}")
    public Response<List<DatabaseTableTreeResp>> databaseTableTree(
        @Parameter(description = "数据源Id", required = true) @PathVariable(value = "dsId") Long dsId,
        @RequestParam(value = "database", required = false) String useDatabase) {
        return Response.success(metadataService.databaseTableTree(dsId, useDatabase));
    }

    @Operation(summary = "根据数据源,数据库和表，获取表模型字段")
    @PostMapping("data-service/metadata/columns")
    public Response<List<ColumnMetadata>> modelColumn(@Valid @RequestBody ColumnMetadataReq columnMetadataReq) {
        List<ColumnMetadata> columnMetadata = null;
        try {
            columnMetadata = metadataService.modelColumns(columnMetadataReq);
        } catch (DataServiceException e) {
            throw new BizException(ResponseCode.FAILED.getCode(), e.getMessage());
        }
        return Response.success(columnMetadata);

    }

}
