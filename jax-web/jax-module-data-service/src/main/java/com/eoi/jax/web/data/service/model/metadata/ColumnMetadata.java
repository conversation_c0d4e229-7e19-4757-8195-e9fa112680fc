package com.eoi.jax.web.data.service.model.metadata;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2023/8/4
 */
public class ColumnMetadata {

    /**
     * 字段名
     */
    @Schema(description = "字段名")
    private String columnName;
    /**
     * 字段类型
     */
    @Schema(description = "字段类型")
    private String columnType;

    @Schema(description = "参数类型")
    private String type;

    /**
     * 字段长度
     */
    @Schema(description = "字段长度")
    private int size;
    /**
     * 小数
     */
    @Schema(description = "字段小数精度")
    private int digits;
    /**
     * 备注
     */
    @Schema(description = "字段描述")
    private String comment;

    @Schema(description = "类型分类,Number，String，Datetime，Unknown")
    private String classification;

    @Schema(description = "模型字段名称, 模型相关信息")
    private String colName;

    @Schema(description = "模型字段显示名, 模型相关信息")
    private String colDisplay;

    @Schema(description = "模型字段类型, 模型相关信息")
    private String colType;


    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getColumnType() {
        return columnType;
    }

    public void setColumnType(String columnType) {
        this.columnType = columnType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getDigits() {
        return digits;
    }

    public void setDigits(int digits) {
        this.digits = digits;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }
}
