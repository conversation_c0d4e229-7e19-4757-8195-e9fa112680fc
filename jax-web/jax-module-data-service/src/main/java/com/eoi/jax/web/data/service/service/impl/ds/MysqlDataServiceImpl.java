package com.eoi.jax.web.data.service.service.impl.ds;

import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.api.dataservice.model.apisetting.IApiSettingModel;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.ds.DsApi;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.MysqlConnectionSetting;
import com.eoi.jax.api.dataservice.model.request.DataServiceRequest;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.MybatisSqlParseException;
import com.eoi.jax.web.data.service.sqlparser.MyBatisUtil;
import com.eoi.jax.api.dataservice.util.MysqlUtil;
import com.eoi.jax.web.data.service.util.SqlUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> zsc
 * @create 2023/7/20 16:01
 */
@Service
public class MysqlDataServiceImpl implements IDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MysqlDataServiceImpl.class);


    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        Connection jdbcConn = null;
        String originSql = "";
        String querySql = "";
        StringBuffer log = dataServiceSetting.getLog() == null ? new StringBuffer() : dataServiceSetting.getLog();
        dataServiceSetting.setLog(log);
        log.append("请求参数：").append(JSONUtil.toJsonStr(dataServiceSetting.getDataServiceRequest()));
        try {
            Map<String, DsDatasource> dsDatasourceMap = dataServiceSetting.getDsDatasourceMap();
            DsDatasource datasource = dsDatasourceMap.get(IDataService.DATASOURCE_KEY);
            DataServiceRequest reqParam = dataServiceSetting.getDataServiceRequest();
            DsApi dsApi = dataServiceSetting.getDsApi();
            MysqlConnectionSetting mysqlConnection = (MysqlConnectionSetting) datasource.getConnectionSetting();
            jdbcConn = MysqlUtil.getProxyConnection(mysqlConnection);

            List<Map<String, Object>> mapList = new LinkedList<>();
            Long totalNum = null;
            IApiSettingModel apiSettingModel = dsApi.getApiSettingModel();
            originSql = apiSettingModel.getScript();
            log.append("\n原始sql语句：").append(originSql);
            LOGGER.debug("api接口" + dsApi.getName() + "原始sql语句：" + originSql);
            String queryXmlSql =
                    "<select id=\"jaxApi" + System.currentTimeMillis() + "\"> " + originSql + " </select>";
            querySql = MyBatisUtil.parseDynamicXmlFormXmlStr(queryXmlSql, reqParam.getParam());
            log.append("\n替换参数后的sql语句：").append(querySql);
            if (apiSettingModel.getEnablePaged()) {
                String countSql = SqlUtil.getCountSqlFromScript(querySql);
                LOGGER.debug("api接口" + dsApi.getName() + "执行统计count语句：" + countSql);
                totalNum = MysqlUtil.count(jdbcConn, countSql);
                if (totalNum > 0) {
                    querySql = SqlUtil.getPageQuerySqlFromScript(querySql, reqParam.getPageNum(), reqParam.getPageSize());
                }
            }
            if (totalNum == null || totalNum > 0) {
                LOGGER.debug("api接口" + dsApi.getName() + "执行查询语句：" + querySql);
                mapList = MysqlUtil.query(jdbcConn, querySql, SqlUtil.MAX_PAGE_SIZE);
            }

            if (mapList == null) {
                mapList = new LinkedList<>();
            }
            DataServiceResponseData data = new DataServiceResponseData();
            data.setRows(mapList);
            data.setTotalNum((totalNum == null || totalNum < 1) ? mapList.size() : totalNum);
            data.setPageNum(apiSettingModel.getEnablePaged() ? reqParam.getPageNum() : null);
            data.setPageSize(apiSettingModel.getEnablePaged() ? reqParam.getPageSize() : null);
            dataServiceSetting.setSql(querySql);
            return data;
        } catch (DataServiceException e) {
            LOGGER.error("数据服务 " + dataServiceSetting.getDsApi().getName() + " 的请求参数:" +
                            JSONUtil.toJsonPrettyStr(dataServiceSetting.getDataServiceRequest()) +
                            ", \n原始sql：" + originSql + "，\n实际查询sql：" + querySql,
                    e);
            throw e;
        } catch (MybatisSqlParseException e) {
            LOGGER.error("数据服务 " + dataServiceSetting.getDsApi().getName() + " 的请求参数:" +
                            JSONUtil.toJsonPrettyStr(dataServiceSetting.getDataServiceRequest()) +
                            ", \n原始sql：" + originSql,
                    e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("数据服务 " + dataServiceSetting.getDsApi().getName() + " 的请求参数:" +
                            JSONUtil.toJsonPrettyStr(dataServiceSetting.getDataServiceRequest()) +
                            ", \n原始sql：" + originSql + "，\n实际查询sql：" + querySql,
                    e);
            throw new DataServiceException(ResponseCode.SQL_EXECUTE_ERROR.getCode(), e.getMessage());
        } finally {
            MysqlUtil.close(null, null, jdbcConn);
        }
    }

}
