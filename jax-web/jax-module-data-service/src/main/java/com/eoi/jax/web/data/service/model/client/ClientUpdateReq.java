package com.eoi.jax.web.data.service.model.client;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbClient;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;

/**
 * @Author: tangy
 * @Date: 2023/7/24
 * @Desc:
 **/
public class ClientUpdateReq implements IUpdateModel<TbClient> {
    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 接口名
     */
    @Schema(description = "客户端名称")
    @NotNull(message = "客户端名称不能为空")
    private String name;

    /**
     * api key或kafka用户名
     */
    @Schema(description = "key或kafka用户名")
    @NotNull(message = "apiKey不能为空")
    private String apiKey;

    /**
     * secret key或kafka密码
     */
    @Schema(description = "secret key或kafka密码")
    private String secretKey;

    /**
     * 描述信息
     */
    @Schema(description = "描述信息")
    private String description;

    /**
     * 是否需要授权访问
     */
    @Schema(description = "是否需要校验密钥")
    private Boolean verifySecret;

    public Boolean getVerifySecret() {
        return verifySecret;
    }

    public void setVerifySecret(Boolean verifySecret) {
        this.verifySecret = verifySecret;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public TbClient toEntity(TbClient entityOld) {
        TbClient entityNew = IUpdateModel.super.toEntity(entityOld);
        return entityNew;
    }
}
