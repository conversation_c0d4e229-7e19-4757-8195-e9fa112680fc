package com.eoi.jax.web.data.service.strategy.descnsitization;


import com.eoi.jax.api.dataservice.enumrate.DataMaskingEnum;
import com.eoi.jax.web.data.service.strategy.AbstractDesensitizationStrategy;
import com.eoi.jax.api.dataservice.model.mask.MaskingRule;

public class PhoneDesensitizationStrategy extends AbstractDesensitizationStrategy {
    @Override
    public MaskingRule getMaskingRule() {
        return new MaskingRule(3, 5, 3, "*");
    }
    @Override
    public DataMaskingEnum getMaskingType() {
        return DataMaskingEnum.PHONE;
    }
}
