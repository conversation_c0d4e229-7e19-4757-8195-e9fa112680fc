package com.eoi.jax.web.data.service.sqlparser;


import com.eoi.jax.api.dataservice.model.apisetting.ApiSettingReqColumn;
import com.eoi.jax.api.dataservice.model.apisetting.ApiSettingSortColumn;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/8/4
 * @Desc:
 **/
public class OriginSqlFinderRequest implements Serializable {
    @Schema(description = "请求参数列表")
    private List<ApiSettingReqColumn> requestParams;

    @Schema(description = "排序信息")
    private List<ApiSettingSortColumn> paramSorts;

    public List<ApiSettingReqColumn> getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(List<ApiSettingReqColumn> requestParams) {
        this.requestParams = requestParams;
    }

    public List<ApiSettingSortColumn> getParamSorts() {
        return paramSorts;
    }

    public void setParamSorts(List<ApiSettingSortColumn> paramSorts) {
        this.paramSorts = paramSorts;
    }
}
