package com.eoi.jax.web.data.service.sqlparser;

import ognl.ASTAnd;
import ognl.ASTChain;
import ognl.ASTConst;
import ognl.ASTMethod;
import ognl.ASTOr;
import ognl.ASTProperty;
import ognl.Node;
import ognl.Ognl;
import ognl.OgnlException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.scripting.xmltags.SqlNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: tangy
 * @Date: 2023/7/4
 * @Desc:
 **/
public class OriginParamParser {

    private static Logger log = LoggerFactory.getLogger("SqlParserUtil");
    private static final Map<String, Node> EXPRESSION_CACHE = new ConcurrentHashMap<>(1);
    private final String openToken;
    private final String closeToken;

    public OriginParamParser(String openToken, String closeToken) {

        this.openToken = openToken;
        this.closeToken = closeToken;
    }

    public static Map<String, OriginParamType> paseParamToMap(List<OriginParamType> paramList) {
        List<String> parentList = new ArrayList<>();
        Map<String, OriginParamType> partenMap = new HashMap<>(1);
        Map<String, OriginParamType> paramMap = new HashMap<>(1);
        for (OriginParamType originParamType : paramList) {
            OriginParamType originParamType1 = paramMap.get(originParamType.getName());
            if (originParamType.getParent() != null) {
                parentList.add(originParamType.getParent());
                OriginParamType parentParam = new OriginParamType();
                parentParam.setName(originParamType.getParent());
                parentParam.setType("OBJECT");
                partenMap.put(originParamType.getParent(), parentParam);
            }
            if (originParamType1 == null) {
                paramMap.put(originParamType.getName(), originParamType);
            } else if (!originParamType.getType().equals(originParamType1.getType())) {
                if (originParamType.getType().equalsIgnoreCase("LIST")) {
                    paramMap.put(originParamType.getName(), originParamType);
                }
            }

        }
        paramMap.putAll(partenMap);
        return paramMap;
    }

    public Map parseParams(List<SqlNode> allContents) {

        return null;
    }

    public List<String> parse(String text) {
        List<String> paramList = new ArrayList<>();
        if (text == null || text.isEmpty()) {
            return paramList;
        }
        // search open token
        int start = text.indexOf(openToken);
        if (start == -1) {
            return paramList;
        }
        char[] src = text.toCharArray();
        int offset = 0;
        final StringBuilder builder = new StringBuilder();
        StringBuilder expression = null;
        do {
            if (start > 0 && src[start - 1] == '\\') {
                // this open token is escaped. remove the backslash and continue.
                builder.append(src, offset, start - offset - 1).append(openToken);
                offset = start + openToken.length();
            } else {
                // found open token. let's search close token.
                if (expression == null) {
                    expression = new StringBuilder();
                } else {
                    expression.setLength(0);
                }
                builder.append(src, offset, start - offset);
                offset = start + openToken.length();
                int end = text.indexOf(closeToken, offset);
                while (end > -1) {
                    if (end > offset && src[end - 1] == '\\') {
                        // this close token is escaped. remove the backslash and continue.
                        expression.append(src, offset, end - offset - 1).append(closeToken);
                        offset = end + closeToken.length();
                        end = text.indexOf(closeToken, offset);
                    } else {
                        expression.append(src, offset, end - offset);
                        break;
                    }
                }
                if (end == -1) {
                    // close token was not found.
                    builder.append(src, start, src.length - start);
                    offset = src.length;
                } else {
                    paramList.add(expression.toString());
                    offset = end + closeToken.length();
                }
            }
            start = text.indexOf(openToken, offset);
        } while (start > -1);
        if (offset < src.length) {
            builder.append(src, offset, src.length - offset);
        }
        return paramList;
    }

    public static List<OriginParamType> paseTestParams(String text) {
        List<OriginParamType> child = new ArrayList<>();
        if (StringUtils.isEmpty(text)) {
            return child;
        }
        Map<String, OriginParamType> paramMap = new HashMap<>(1);

        try {
            Node node = parseExpression(text);
            if (node instanceof ASTAnd || node instanceof ASTOr) {
                for (int i = 0; i < node.jjtGetNumChildren(); i++) {
                    Node subNode = node.jjtGetChild(i);
                    try {
                        if (subNode instanceof ASTProperty) {
                            OriginParamType originParamType = paseParamNode(subNode.jjtGetChild(0));
                            if (checkParamTypeValid(originParamType)) {
                                paramMap.put(originParamType.getName(), originParamType);
                            }
                        } else {
                            OriginParamType originParamType = paseParamNode(subNode.jjtGetChild(0));
                            if (checkParamTypeValid(originParamType)) {
                                paramMap.put(originParamType.getName(), originParamType);
                            }
                        }
                    } catch (Exception e) {
                        log.error("无法解析脚本：{} ", subNode.jjtGetChild(0).toString());
                    }
                }
            } else if (node instanceof ASTProperty) {
                try {
                    OriginParamType originParamType = paseParamNode(node.jjtGetChild(0));
                    if (checkParamTypeValid(originParamType)) {
                        paramMap.put(originParamType.getName(), originParamType);
                    }
                } catch (Exception e) {
                    log.error("无法解析脚本：{} ", node.jjtGetChild(0).toString());
                }
            } else {
                try {
                    OriginParamType originParamType = paseParamNode(node.jjtGetChild(0));
                    if (checkParamTypeValid(originParamType)) {
                        paramMap.put(originParamType.getName(), originParamType);
                    }
                } catch (Exception e) {
                    log.error("无法解析脚本：{} ", node.jjtGetChild(0).toString());
                }
            }

        } catch (OgnlException e) {
            throw new RuntimeException(e);
        }
        return new ArrayList<>(paramMap.values());
    }

    private static boolean checkParamTypeValid(OriginParamType originParamType) {
        return !originParamType.getName().contains(".");
    }

    private static Node parseExpression(String expression) throws OgnlException {
        Node node = EXPRESSION_CACHE.get(expression);
        if (node == null) {
            node = (Node) Ognl.parseExpression(expression);
            EXPRESSION_CACHE.put(expression, node);
        }
        return node;
    }


    public static OriginParamType paseParamNode(Node paramNode) {
        OriginParamType originParamType = new OriginParamType();
        String attrName = null;
        originParamType.setType("UNKNOWN");
        if (paramNode instanceof ASTConst) {
            attrName = String.valueOf(((ASTConst) paramNode).getValue());
        } else if (paramNode instanceof ASTProperty) {
            attrName = String.valueOf(((ASTConst) ((ASTProperty) paramNode).jjtGetChild(0)).getValue());
        } else if (paramNode instanceof ASTChain) {
            ASTProperty astProperty = (ASTProperty) paramNode.jjtGetChild(0);

            String value2 = null;
            if (paramNode.jjtGetChild(1) instanceof ASTMethod) {
                ASTMethod astPropertyValue = (ASTMethod) paramNode.jjtGetChild(1);
                value2 = astPropertyValue.getMethodName();
            } else {
                ASTProperty astPropertyValue = (ASTProperty) paramNode.jjtGetChild(1);
                value2 = String.valueOf(((ASTConst) (astPropertyValue).jjtGetChild(0)).getValue());
                Assert.isTrue(paramNode.jjtGetNumChildren() <= 3, "解析请求参数异常, " +
                        "不支持参数格式[" + paramNode.toString() + "]格式,最多只支持两层嵌套");
                if (paramNode.jjtGetNumChildren() > 2) {
                    String lastProperty = null;
                    if (paramNode.jjtGetChild(2) instanceof ASTMethod) {
                        ASTMethod lastPropertyValue = (ASTMethod) paramNode.jjtGetChild(2);
                        lastProperty = lastPropertyValue.getMethodName();
                    } else {
                        ASTProperty lastPropertyValue = (ASTProperty) paramNode.jjtGetChild(2);
                        lastProperty = String.valueOf(((ASTConst) (lastPropertyValue).jjtGetChild(0)).getValue());
                    }
                    Assert.isTrue("size".equals(lastProperty.toLowerCase())
                            || "length".equals(lastProperty.toLowerCase()), "解析请求参数异常, " +
                            "不支持参数格式[" + paramNode.toString() + "]");
                    originParamType.setType("LIST");
                }
            }
            String value1 = String.valueOf(((ASTConst) (astProperty).jjtGetChild(0)).getValue());
            if ("size".equals(value2.toLowerCase()) || "length".equals(value2.toLowerCase())) {
                attrName = value1;
                originParamType.setType("LIST");
                if (astProperty.jjtGetNumChildren() > 1) {
                    originParamType.setParent(value1);
                }
            } else {
                attrName = value2;
                originParamType.setParent(value1);
            }
            if (StringUtils.isEmpty(originParamType.getType())) {
                originParamType.setType("OBJECT");
            }
        }
        originParamType.setName(attrName);
        return originParamType;
    }

}
