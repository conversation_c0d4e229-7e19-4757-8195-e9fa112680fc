package com.eoi.jax.web.data.service.component.ratelimit;

import com.eoi.jax.web.core.common.enumrate.ConfigChangeType;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.data.service.model.apicomponent.DataServiceLog;
import com.eoi.jax.web.data.service.service.ClientApiService;
import com.eoi.jax.web.repository.service.TbApiService;
import com.eoi.jax.web.repository.service.TbClientApiService;
import com.eoi.jax.web.repository.service.TbClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/27
 */
@Component
public class RateLimitPolicyWrapper implements RateLimitPolicy {

    private static final Logger LOG = LoggerFactory.getLogger(RateLimitPolicyWrapper.class);

    @Autowired
    private TbApiService tbApiService;

    @Autowired
    private TbClientService tbClientService;

    @Autowired
    private TbClientApiService tbClientApiService;

    @Autowired
    private ClientApiService clientApiService;

    @Autowired
    private SystemConfigHolder systemConfigHolder;

    private RateLimitPolicy rateLimitPolicy;

    @PostConstruct
    public void init() {
        systemConfigHolder.addChangeListener(SystemConfigEnum.DATASERVICE_REDIS, redisEvent -> {
            try {
                LOG.info("收到数据服务redis配置变化:{}", JsonUtil.encode(redisEvent));
                Map<String, Object> newConfig = redisEvent.getNewConfigMap();
                Boolean enable = (Boolean) newConfig.getOrDefault("enable", false);
                RateLimitPolicy policy = null;
                if (!enable || ConfigChangeType.DELETED.equals(redisEvent.getChangeType())) {
                    // redis关闭，或者redis删除
                    if (Objects.isNull(rateLimitPolicy) || rateLimitPolicy instanceof RedisRateLimitPolicy) {
                        this.close();
//                        policy = new LocalRateLimitPolicy(clientApiService
                    }
                } else {
                    this.close();
                    policy = new RedisRateLimitPolicy(clientApiService,
                            systemConfigHolder.getConfig(SystemConfigEnum.DATASERVICE_REDIS));
                }
                this.rateLimitPolicy = policy;
            } catch (Exception e) {
                LOG.error("创建rateLimitPolicy异常", e);
            }
        });
    }

    /**
     * 尝试获取令牌
     *
     * @param apiPath
     * @param apiKey
     * @return
     */
    @Override
    public boolean tryAcquire(String apiPath, String apiKey) {
        if (Objects.nonNull(this.rateLimitPolicy)) {
            return this.rateLimitPolicy.tryAcquire(apiPath, apiKey);
        }
        return true;
    }

    /**
     * 复原限流的关键信息
     *
     * @param dataServiceLog
     */
    @Override
    public void restoreApiInfo(DataServiceLog dataServiceLog) {
        if (Objects.nonNull(this.rateLimitPolicy)) {
            this.rateLimitPolicy.restoreApiInfo(dataServiceLog);
        }
    }

    @Override
    public void setRateLimitPermits(DataServiceLog dataServiceLog, String apiExtraSetting, String clientApiExtraSetting) {
        if (Objects.nonNull(this.rateLimitPolicy)) {
            this.rateLimitPolicy.setRateLimitPermits(dataServiceLog, apiExtraSetting, clientApiExtraSetting);
        }
    }

    /**
     * 关闭
     */
    @Override
    public void close() {
        try {
            if (Objects.isNull(this.rateLimitPolicy)) {
                return;
            }
            RateLimitPolicy policy = this.rateLimitPolicy;
            // 先删除引用，然后关闭
            this.rateLimitPolicy = null;
            policy.close();
        } catch (Exception e) {
            LOG.error("关闭限流策略异常", e);
        }
    }


}
