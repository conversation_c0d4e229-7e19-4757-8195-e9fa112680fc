package com.eoi.jax.web.data.service.model.apicomponent;

/**
 * <AUTHOR>
 * @date 2023/7/26
 */
public class DataServiceLog {

    /**
     * 请求ip
     */
    private String ip;
    /**
     * apiId
     */
    private Long apiId;

    /**
     * 服务路径
     */
    private String apiPath;
    /**
     * clientId
     */
    private Long clientId;
    /**
     * 授权用户
     */
    private String apiKey;
    /**
     * 请求唯一Id
     */
    private String nonce;
    /**
     * 请求返回码
     */
    private String retCode;
    /**
     * header中的httpDate时间
     */
    private Long requestTime;
    /**
     * 异常消息
     */
    private String msg;
    /**
     * 请求body
     */
    private String requestParam;
    /**
     * 响应body
     */
    private String responseBody;
    /**
     * 响应时间
     */
    private Long responseTime;
    /**
     * 耗时
     */
    private Long timeConsuming;


    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Long getApiId() {
        return apiId;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    public String getApiPath() {
        return apiPath;
    }

    public void setApiPath(String apiPath) {
        this.apiPath = apiPath;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getRetCode() {
        return retCode;
    }

    public void setRetCode(String retCode) {
        this.retCode = retCode;
    }

    public Long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Long requestTime) {
        this.requestTime = requestTime;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getRequestParam() {
        return requestParam;
    }

    public void setRequestParam(String requestParam) {
        this.requestParam = requestParam;
    }

    public String getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public Long getTimeConsuming() {
        return timeConsuming;
    }

    public void setTimeConsuming(Long timeConsuming) {
        this.timeConsuming = timeConsuming;
    }
}
