package com.eoi.jax.web.data.service.component.ratelimit;

import cn.hutool.core.lang.Pair;
import com.eoi.jax.web.data.service.model.apicomponent.DataServiceLog;
import com.eoi.jax.web.data.service.service.ClientApiService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.util.concurrent.RateLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 本地限流
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
public class LocalRateLimitPolicy extends AbstractRateLimitPolicy {

    private static final Logger LOG = LoggerFactory.getLogger(RedisRateLimitPolicy.class);

    private volatile Cache<RateLimiterKeyContainer, RateLimiterContainer> rateLimitCache;

    public LocalRateLimitPolicy(ClientApiService clientApiService) {
        super(clientApiService);
    }


    @SuppressWarnings("UnstableApiUsage")
    @Override
    public boolean tryAcquire(String apiPath, String apiKey) {
        Cache<RateLimiterKeyContainer, RateLimiterContainer> cache = getRateLimitCache();
        try {
            RateLimiterContainer rateLimiterContainer = cache.getIfPresent(new RateLimiterKeyContainer(apiPath, apiKey));
            if (Objects.isNull(rateLimiterContainer)) {
                return true;
            }
            return rateLimiterContainer.getRateLimiter().tryAcquire();
        } catch (Exception e) {
            LOG.error("本地限流异常", e);
            return true;
        }
    }

    /**
     * 复原限流的关键信息
     *
     * @param dataServiceLog
     */
    @Override
    public void restoreApiInfo(DataServiceLog dataServiceLog) {
        Cache<RateLimiterKeyContainer, RateLimiterContainer> cache = getRateLimitCache();
        try {
            RateLimiterContainer rateLimiterContainer = cache.getIfPresent(new RateLimiterKeyContainer(dataServiceLog.getApiPath(),
                    dataServiceLog.getApiKey()));
            if (Objects.nonNull(rateLimiterContainer)) {
                dataServiceLog.setApiId(rateLimiterContainer.getApiId());
                dataServiceLog.setClientId(rateLimiterContainer.getClientId());
            }
        } catch (Exception e) {
            LOG.error("恢复API信息异常", e);
        }
    }

    public Cache<RateLimiterKeyContainer, RateLimiterContainer> getRateLimitCache() {
        if (Objects.isNull(rateLimitCache)) {
            synchronized (this) {
                if (Objects.isNull(rateLimitCache)) {
                    rateLimitCache = CacheBuilder.newBuilder().concurrencyLevel(8)
                            // 设置读写1分后过期
                            .expireAfterAccess(1, TimeUnit.MINUTES)
                            // 设置缓存最大容量
                            .maximumSize(Math.max(countRateLimitKey() * 2, 5000)).build();
                }
            }
        }
        return rateLimitCache;
    }

    @SuppressWarnings("UnstableApiUsage")
    @Override
    public void setRateLimitPermits(DataServiceLog dataServiceLog, String apiExtraSetting, String clientApiExtraSetting) {
        try {
            Pair<Integer, TimeUnit> permitPair = getPermits(apiExtraSetting, clientApiExtraSetting);
            if (permitPair.getKey().equals(-1)) {
                return;
            }
            double permitsPerSecond = TimeUnit.SECONDS.equals(permitPair.getValue()) ? permitPair.getKey() : permitPair.getKey() / 60.0;
            Cache<RateLimiterKeyContainer, RateLimiterContainer> cache = getRateLimitCache();
            RateLimiterKeyContainer rateLimiterKeyContainer = new RateLimiterKeyContainer(dataServiceLog.getApiPath(),
                    dataServiceLog.getApiKey());
            RateLimiterContainer rateLimiterContainer = cache.getIfPresent(rateLimiterKeyContainer);
            if (Objects.isNull(rateLimiterContainer)) {
                // 创建新的
                cache.put(rateLimiterKeyContainer, new RateLimiterContainer(permitPair.getKey(), dataServiceLog.getApiId(),
                        dataServiceLog.getClientId(),
                        RateLimiter.create(permitsPerSecond)));
            } else {
                // 更新限流
                if (permitPair.getKey() != rateLimiterContainer.getPermitsPerSecond()) {
                    // 重置限流计数
                    rateLimiterContainer.getRateLimiter().setRate(permitsPerSecond);
                    rateLimiterContainer.setPermitsPerSecond(permitPair.getKey());
                    rateLimiterContainer.setApiId(dataServiceLog.getApiId());
                    rateLimiterContainer.setClientId(dataServiceLog.getClientId());
                }
            }
        } catch (Exception e) {
            LOG.error("设置本地限流异常", e);
        }
    }

    /**
     * 关闭
     */
    @Override
    public void close() {
        try {
            if (Objects.nonNull(this.rateLimitCache)) {
                // 清除缓存
                this.rateLimitCache.invalidateAll();
                this.rateLimitCache = null;
            }
        } catch (Exception e) {
            LOG.error("关闭缓存异常", e);
        }
    }


    public Long countRateLimitKey() {
        ClientApiService clientApiService = getClientApiService();
        if (Objects.isNull(clientApiService)) {
            return 10000L;
        }
        long count = clientApiService.countAll();
        return Math.max(10000L, count * 2);
    }


    private static class RateLimiterKeyContainer {

        private String apiPath;

        private String apiKey;

        RateLimiterKeyContainer(String apiPath, String apiKey) {
            this.apiPath = apiPath;
            this.apiKey = apiKey;
        }

        public String getApiPath() {
            return apiPath;
        }

        public void setApiPath(String apiPath) {
            this.apiPath = apiPath;
        }

        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            RateLimiterKeyContainer that = (RateLimiterKeyContainer) o;
            return apiPath.equals(that.apiPath) && apiKey.equals(that.apiKey);
        }

        @Override
        public int hashCode() {
            return Objects.hash(apiPath, apiKey);
        }
    }

    private static class RateLimiterContainer {

        private long permitsPerSecond;

        private Long apiId;

        private Long clientId;

        private RateLimiter rateLimiter;

        RateLimiterContainer(long permitsPerSecond, Long apiId, Long clientId, RateLimiter rateLimiter) {
            this.permitsPerSecond = permitsPerSecond;
            this.apiId = apiId;
            this.clientId = clientId;
            this.rateLimiter = rateLimiter;
        }

        public long getPermitsPerSecond() {
            return permitsPerSecond;
        }

        public void setPermitsPerSecond(long permitsPerSecond) {
            this.permitsPerSecond = permitsPerSecond;
        }

        public Long getApiId() {
            return apiId;
        }

        public void setApiId(Long apiId) {
            this.apiId = apiId;
        }

        public Long getClientId() {
            return clientId;
        }

        public void setClientId(Long clientId) {
            this.clientId = clientId;
        }

        public RateLimiter getRateLimiter() {
            return rateLimiter;
        }

        public void setRateLimiter(RateLimiter rateLimiter) {
            this.rateLimiter = rateLimiter;
        }
    }

}
