package com.eoi.jax.web.data.service.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.service.model.apiquery.DsPreviewReq;
import com.eoi.jax.web.data.service.model.apiquery.DsSqlPreviewReq;
import com.eoi.jax.web.data.service.model.apiquery.DsTestQueryReq;
import com.eoi.jax.web.data.service.service.DataApiQueryService;
import com.eoi.jax.web.data.service.service.JdbcService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * <AUTHOR> zsc
 * @create 2023/4/23 17:17
 */
@RestController
public class DataServiceController implements V2Controller {

    @Resource
    private DataApiQueryService dataApiQueryService;

    @Resource
    private JdbcService jdbcService;

    @Operation(summary = "测试查询")
    @PostMapping("data-service/api/test/query")
    public Response testQuery(@Valid @RequestBody DsTestQueryReq param, HttpServletRequest request) {
        return dataApiQueryService.testQuery(param, request);
    }

    @Operation(summary = "数据预览")
    @PostMapping("data-service/api/preview")
    public Response preview(@Valid @RequestBody DsPreviewReq param) {
        return dataApiQueryService.preview(param);
    }

    @Operation(summary = "sql-数据预览")
    @PostMapping("data-service/api/sql-preview")
    public Response sqlPreview(@Valid @RequestBody DsSqlPreviewReq param) {
        return jdbcService.preview(param);
    }

}
