package com.eoi.jax.web.data.service.model.client;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbClient;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: tangy
 * @Date: 2023/7/24
 * @Desc:
 **/
public class ClientApiAuthFilterReq implements IFilterReq<TbClient> {

    /**
     * api名称
     */
    @Schema(description = "api名称")
    private String apiName;
    /**
     * 是否显示全部Api
     */
    @Schema(description = "是否显示全部Api,针对客户端添加授权服务生效,false表示不显示已授权的api")
    private Boolean showAll;


    @Override
    public QueryWrapper<TbClient> where(QueryWrapper<TbClient> wrapper) {
        return wrapper;
    }

    public String getApiName() {
        return StringUtils.isNotBlank(apiName) ? "%" + apiName + "%" : apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public Boolean getShowAll() {
        return showAll == null ? true : showAll;
    }

    public void setShowAll(Boolean showAll) {
        this.showAll = showAll;
    }
}
