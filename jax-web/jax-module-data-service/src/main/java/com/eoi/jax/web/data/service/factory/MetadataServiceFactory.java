package com.eoi.jax.web.data.service.factory;

import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.util.SpringBeanUtil;
import com.eoi.jax.web.data.service.service.DatasourceMetadataService;
import com.eoi.jax.web.data.service.service.impl.metadata.*;
import com.eoi.jax.web.ingestion.enumrate.DatasourcePlatformEnum;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
public class MetadataServiceFactory {

    public static DatasourceMetadataService getServiceInstance(String datasourceType) {
        DatasourceMetadataService service = null;
        try {
            if (DatasourcePlatformEnum.MYSQL.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(MysqlDatasourceMetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.HIVE.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(HiveDatasourceMetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.CLICKHOUSE.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(ClickHouseDatasourceMetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.DB2.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(Db2MetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.SQLSERVER.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(SqlServerMetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.ORACLE.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(OracleMetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.POSTGRESQL.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(PostgreSqlMetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.SYBASE.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(SybaseMetadataServiceImpl.class);
            } else if (DatasourcePlatformEnum.DAMENG.code().equals(datasourceType)) {
                service = (DatasourceMetadataService) SpringBeanUtil.getBean(DamengMetadataServiceImpl.class);
            } else {
                throw new DataServiceException(ResponseCode.FAILED.getCode(), "不支持的数据源模式：" + datasourceType);
            }
        } catch (Exception e) {
            throw new DataServiceException(ResponseCode.FAILED.getCode(), "获取数据服务处理实例失败：" + e.getMessage());
        }
        return service;
    }

}
