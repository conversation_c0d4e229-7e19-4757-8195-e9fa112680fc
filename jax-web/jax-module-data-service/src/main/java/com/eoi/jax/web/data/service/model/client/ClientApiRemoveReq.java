package com.eoi.jax.web.data.service.model.client;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: tangy
 * @Date: 2023/8/10
 * @Desc:
 **/
public class ClientApiRemoveReq implements Serializable {
    @Schema(description = "clientId")
    @NotNull(message = "客户端id不能为空")
    private Long clientId;

    @Schema(description = "apiId")
    @NotNull(message = "apiId不能为空")
    private Long apiId;

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getApiId() {
        return apiId;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }
}
