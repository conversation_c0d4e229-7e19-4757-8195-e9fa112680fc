package com.eoi.jax.api.dataservice.model.ds.conn;

import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 */
public class CkManConnectionSetting implements IDatasourceConnectionSetting {

    private String address;

    private String privateKey;

    private Long registerCenterId;

    private String nacosGroupName;

    private String ckClusterName;

    private String ckUsername;

    private String ckPassword;

    private String ckJdbcUrl;

    private Map<String, String> jdbcProp;

    public String getCkClusterName() {
        return ckClusterName;
    }

    public void setCkClusterName(String ckClusterName) {
        this.ckClusterName = ckClusterName;
    }

    public String getNacosGroupName() {
        return nacosGroupName;
    }

    public void setNacosGroupName(String nacosGroupName) {
        this.nacosGroupName = nacosGroupName;
    }

    public Long getRegisterCenterId() {
        return registerCenterId;
    }

    public void setRegisterCenterId(Long registerCenterId) {
        this.registerCenterId = registerCenterId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getCkUsername() {
        return ckUsername;
    }

    public void setCkUsername(String ckUsername) {
        this.ckUsername = ckUsername;
    }

    public String getCkPassword() {
        return ckPassword;
    }

    public void setCkPassword(String ckPassword) {
        this.ckPassword = ckPassword;
    }

    public String getCkJdbcUrl() {
        return ckJdbcUrl;
    }

    public void setCkJdbcUrl(String ckJdbcUrl) {
        this.ckJdbcUrl = ckJdbcUrl;
    }

    public Map<String, String> getJdbcProp() {
        return jdbcProp;
    }

    public void setJdbcProp(Map<String, String> jdbcProp) {
        this.jdbcProp = jdbcProp;
    }
}
