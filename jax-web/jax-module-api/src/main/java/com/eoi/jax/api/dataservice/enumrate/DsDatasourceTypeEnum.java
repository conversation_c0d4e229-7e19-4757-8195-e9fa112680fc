package com.eoi.jax.api.dataservice.enumrate;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public enum DsDatasourceTypeEnum {

    KAFKA("KAFKA", "kafka数据源"),

    ELASTICSEARCH("ELAST<PERSON><PERSON>AR<PERSON>", "elasticsearch数据源"),

    CLICKHOUSE("CLICKHOUSE", "clickhouse数据源"),

    MYSQL("MYSQL", "mysql数据源"),

    HIVE("HIVE", "hive数据源"),

    NEBULA("NEBULA", "nebular数据源"),

    REDIS("REDIS", "redis数据源"),

    VICTORIA_METRICS("VICTORIA_METRICS", "vm数据源"),

    CUSTOM("CUSTOM", "自定义数据源"),
    /**
     * DB2数据源
     */
    DB2("DB2", "DB2数据源"),
    /**
     * SQLSERVER数据源
     */
    SQLSERVER("SQLSERVER", "SQLSERVER数据源"),
    /**
     * ORACLE数据源
     */
    ORACLE("ORACLE", "ORACLE数据源"),
    /**
     * POSTGRESQL数据源
     */
    POSTGRESQL("POSTGRESQL", "POSTGRESQL数据源"),
    /**
     * SYBASE数据源
     */
    SYBASE("SYBASE", "SYBASE数据源"),
    /**
     * 达梦数据源
     */
    DAMENG("DAMENG", "达梦数据源"),
    /**
     * DORIS数据源
     */
    DORIS("DORIS", "DORIS数据源");

    private final String code;

    private final String message;

    DsDatasourceTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    public static DsOperationEnum fromString(String code) {
        for (DsOperationEnum value : DsOperationEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
