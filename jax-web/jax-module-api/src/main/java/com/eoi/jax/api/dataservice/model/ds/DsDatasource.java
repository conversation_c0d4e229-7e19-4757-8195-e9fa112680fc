package com.eoi.jax.api.dataservice.model.ds;


import com.eoi.jax.api.dataservice.model.ds.conn.IDatasourceConnectionSetting;

/**
 * <AUTHOR> zsc
 * @create 2023/7/21 9:47
 */
public class DsDatasource {

    /**
     * 数据源id
     */
    private Long id;

    /**
     * 数据源标示
     */
    private String code;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类型：KAFKA, ELASTICSEARCH, CLICKHOUSE, MYSQL等
     */
    private String platform;

    /**
     * 数据源状态：NORMAL, ABNORMAL等
     */
    private String status;

    /**
     * 接入模式：NACOS_AUTO，NACOS_3RD, MANUAL
     */
    private String connectMode;

    /**
     * 备注信息
     */
    private String description;

    /**
     * 注册中心的id
     */
    private Long registerCenterId;


    /**
     * 数据源连接配置信息
     */
    private IDatasourceConnectionSetting connectionSetting;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getConnectMode() {
        return connectMode;
    }

    public void setConnectMode(String connectMode) {
        this.connectMode = connectMode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getRegisterCenterId() {
        return registerCenterId;
    }

    public void setRegisterCenterId(Long registerCenterId) {
        this.registerCenterId = registerCenterId;
    }

    public IDatasourceConnectionSetting getConnectionSetting() {
        return connectionSetting;
    }

    public void setConnectionSetting(IDatasourceConnectionSetting connectionSetting) {
        this.connectionSetting = connectionSetting;
    }
}
