package com.eoi.jax.api.dataservice.exception;


/**
 * <AUTHOR>
 * @date 2022/12/14
 */
public class DataServiceException extends RuntimeException {
    private final String code;
    private final String message;
    private Throwable cause;

    public DataServiceException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public DataServiceException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
        this.cause = cause;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public Throwable getCause() {
        return cause;
    }
}
