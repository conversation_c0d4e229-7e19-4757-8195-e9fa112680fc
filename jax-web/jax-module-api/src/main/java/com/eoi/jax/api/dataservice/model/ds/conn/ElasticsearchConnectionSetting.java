package com.eoi.jax.api.dataservice.model.ds.conn;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 */
public class ElasticsearchConnectionSetting implements IDatasourceConnectionSetting {
    private String address;

    private String authType;

    private String username;

    private String password;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "ElasticsearchConnection{" +
                "address='" + address + '\'' +
                ", authType='" + authType + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
