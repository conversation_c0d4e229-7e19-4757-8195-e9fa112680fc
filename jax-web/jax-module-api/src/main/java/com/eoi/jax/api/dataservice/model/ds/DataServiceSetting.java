package com.eoi.jax.api.dataservice.model.ds;


import com.eoi.jax.api.dataservice.model.request.DataServiceRequest;

import java.sql.Connection;
import java.util.Map;

/**
 * <AUTHOR> zsc
 * @create 2023/7/20 15:45
 */
public class DataServiceSetting {
    /**
     * api对应的数据源
     */
    private Map<String, DsDatasource> dsDatasourceMap;

    /**
     * api请求参数
     */
    private DataServiceRequest dataServiceRequest;

    /**
     * 数据服务api信息
     */
    private DsApi dsApi;

    /**
     * 中台数据库的jdbc连接
     */
    private Connection jaxJdbcConnection;

    /**
     * 用来记录一些需要返回前端排查问题的日志信息（仅在测试查询api中生效）
     */
    private StringBuffer log = new StringBuffer();

    /**
     * 实际执行sql
     */
    private String sql;

    public Connection getJaxJdbcConnection() {
        return jaxJdbcConnection;
    }

    public void setJaxJdbcConnection(Connection jaxJdbcConnection) {
        this.jaxJdbcConnection = jaxJdbcConnection;
    }

    public Map<String, DsDatasource> getDsDatasourceMap() {
        return dsDatasourceMap;
    }

    public void setDsDatasourceMap(Map<String, DsDatasource> dsDatasourceMap) {
        this.dsDatasourceMap = dsDatasourceMap;
    }

    public DsApi getDsApi() {
        return dsApi;
    }

    public void setDsApi(DsApi dsApi) {
        this.dsApi = dsApi;
    }

    public DataServiceRequest getDataServiceRequest() {
        return dataServiceRequest;
    }

    public void setDataServiceRequest(DataServiceRequest dataServiceRequest) {
        this.dataServiceRequest = dataServiceRequest;
    }

    public StringBuffer getLog() {
        return log;
    }

    public void setLog(StringBuffer log) {
        this.log = log;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    @Override
    public String toString() {
        return "DataServiceSetting{" +
                "dsDatasourceMap=" + dsDatasourceMap +
                ", dataServiceRequest=" + dataServiceRequest +
                ", dsApi=" + dsApi +
                ", jaxJdbcConnection=" + jaxJdbcConnection +
                '}';
    }
}
