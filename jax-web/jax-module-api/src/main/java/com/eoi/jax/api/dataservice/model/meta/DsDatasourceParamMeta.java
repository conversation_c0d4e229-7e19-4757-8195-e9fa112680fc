package com.eoi.jax.api.dataservice.model.meta;


import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;

/**
 * <AUTHOR> zsc
 * @create 2023/8/2 10:17
 */
public class DsDatasourceParamMeta {

    /**
     * 数据源名称,用于在map中获取具体数据源配置
     */
    private String name;

    /**
     * 数据源类型
     */
    private DsDatasourceTypeEnum type;

    /**
     * 数据源在前端页面的展示名称
     */
    String display;

    /**
     * 描述信息
     *
     * @return
     */
    String description;


    public DsDatasourceParamMeta() {

    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DsDatasourceTypeEnum getType() {
        return type;
    }

    public void setType(DsDatasourceTypeEnum type) {
        this.type = type;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "DsDatasourceParamMeta{" +
                "name='" + name + '\'' +
                ", type=" + type +
                ", display='" + display + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
