package com.eoi.jax.web.data.quality.model.alert.visualization;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * @Author: tangy
 * @Date: 2023/5/26
 * @Desc: 告警信息数据可视化
 **/
public class AlertVisualizationResp implements Serializable {

    @Schema(description = "异常模型")
    private AlertChartResp tableAlert;
    @Schema(description = "异常作业")
    private AlertChartResp pipelineAlert;
    @Schema(description = "告警模型排列")
    private AlertChartResp tableAlertTop;
    @Schema(description = "告警作业排序")
    private AlertChartResp pipelineAlertTop;
    @Schema(description = "规则模板排序")
    private AlertChartResp ruleAlertTop;

    public AlertChartResp getTableAlert() {
        return tableAlert;
    }

    public void setTableAlert(AlertChartResp tableAlert) {
        this.tableAlert = tableAlert;
    }

    public AlertChartResp getPipelineAlert() {
        return pipelineAlert;
    }

    public void setPipelineAlert(AlertChartResp pipelineAlert) {
        this.pipelineAlert = pipelineAlert;
    }

    public AlertChartResp getTableAlertTop() {
        return tableAlertTop;
    }

    public void setTableAlertTop(AlertChartResp tableAlertTop) {
        this.tableAlertTop = tableAlertTop;
    }

    public AlertChartResp getPipelineAlertTop() {
        return pipelineAlertTop;
    }

    public void setPipelineAlertTop(AlertChartResp pipelineAlertTop) {
        this.pipelineAlertTop = pipelineAlertTop;
    }

    public AlertChartResp getRuleAlertTop() {
        return ruleAlertTop;
    }

    public void setRuleAlertTop(AlertChartResp ruleAlertTop) {
        this.ruleAlertTop = ruleAlertTop;
    }
}
