package com.eoi.jax.web.data.quality.service.impl;

import com.eoi.jax.web.data.quality.service.MemoryCacheService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.service.*;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: tangy
 * @Date: 2023/7/14
 * @Desc:
 **/
@Service
public class MemoryCacheServiceImpl implements MemoryCacheService {
    private static Logger logger = LoggerFactory.getLogger(MemoryCacheService.class.getName());

    private final Cache<String, Map<Long, TbPipeline>> pipelineMapCache =
            CacheBuilder.newBuilder().expireAfterWrite(5L, TimeUnit.MINUTES).build();
    private final Cache<String, Map<Long, TbRealtimeCheckRule>> realtimeCheckRuleMapCache =
            CacheBuilder.newBuilder().expireAfterWrite(5L, TimeUnit.MINUTES).build();
    private final Cache<String,
            Map<Long, TbRealtimeCheckTask>> realtimeCheckTaskMapCache =
            CacheBuilder.newBuilder().expireAfterWrite(5L, TimeUnit.MINUTES).build();
    private final Cache<String,
            Map<Long, TbRealtimeCheckRuleTemplate>> realtimeCheckRuleTemplateMapCache =
            CacheBuilder.newBuilder().expireAfterWrite(5L, TimeUnit.MINUTES).build();
    private final Cache<String,
            Map<Long, TbTable>> tableMapCache =
            CacheBuilder.newBuilder().expireAfterWrite(5L, TimeUnit.MINUTES).build();


    @Resource
    private TbTableService tbTableService;
    @Resource
    private TbRealtimeCheckTaskService tbRealtimeCheckTaskService;
    @Resource
    private TbRealtimeCheckRuleService tbRealtimeCheckRuleService;
    @Resource
    private TbRealtimeCheckRuleTemplateService checkRuleTemplateService;
    @Resource
    private TbPipelineService pipelineService;

    @Override
    public Map<Long, TbPipeline> getPipelineMapCache() {
        Map<Long, TbPipeline> pipelineMap = pipelineMapCache.getIfPresent("pipelineMap");
        if (pipelineMap == null) {
            pipelineMap = queryPipelineMapCache();
        }
        return pipelineMap;
    }

    @Override
    public Map<Long, TbRealtimeCheckRule> getRealtimeCheckRuleMapCache() {
        Map<Long, TbRealtimeCheckRule> realtimeCheckRuleMap = realtimeCheckRuleMapCache.getIfPresent("realtimeCheckRuleMap");
        if (realtimeCheckRuleMap == null) {
            realtimeCheckRuleMap = queryRealtimeCheckRuleMap();
        }
        return realtimeCheckRuleMap;
    }

    @Override
    public Map<Long, TbRealtimeCheckTask> getRealtimeCheckTaskMapCache() {
        Map<Long, TbRealtimeCheckTask> realtimeCheckTaskMap = realtimeCheckTaskMapCache.getIfPresent("realtimeCheckTaskMap");

        if (realtimeCheckTaskMap == null) {
            realtimeCheckTaskMap = queryRealtimeCheckTaskMap();
        }
        return realtimeCheckTaskMap;
    }


    @Override
    public Map<Long, TbRealtimeCheckRuleTemplate> getRealtimeCheckRuleTemplateMapCache() {
        Map<Long, TbRealtimeCheckRuleTemplate> realtimeCheckRuleTemplateMap = realtimeCheckRuleTemplateMapCache
                .getIfPresent("realtimeCheckRuleTemplateMap");

        if (realtimeCheckRuleTemplateMap == null) {
            realtimeCheckRuleTemplateMap = queryRealtimeCheckRuleTemplateMap();
        }

        return realtimeCheckRuleTemplateMap;
    }


    @Override
    public Map<Long, TbTable> getTableMapCache() {
        Map<Long, TbTable> tableMap = tableMapCache.getIfPresent("tableMap");
        if (tableMap == null) {
            tableMap = queryTableMap();
        }
        return tableMap;
    }


    private synchronized Map<Long, TbPipeline> queryPipelineMapCache() {
        Map<Long, TbPipeline> pipelineMap = pipelineMapCache.getIfPresent("pipelineMap");
        if (pipelineMap == null) {
            logger.debug("查询数据{}", "pipelineMap");
            List<TbPipeline> pipelineList = pipelineService.list();
            pipelineMap = pipelineList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            pipelineMapCache.put("pipelineMap", pipelineMap);
        }
        return pipelineMap;
    }


    private synchronized Map<Long, TbRealtimeCheckRule> queryRealtimeCheckRuleMap() {
        Map<Long, TbRealtimeCheckRule> realtimeCheckRuleMap = realtimeCheckRuleMapCache.getIfPresent("realtimeCheckRuleMap");
        if (realtimeCheckRuleMap == null) {
            logger.debug("查询数据{}", "realtimeCheckRuleMap");
            List<TbRealtimeCheckRule> realtimeCheckRuleList = tbRealtimeCheckRuleService.list();
            realtimeCheckRuleMap = realtimeCheckRuleList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            realtimeCheckRuleMapCache.put("realtimeCheckRuleMap", realtimeCheckRuleMap);
        }
        return realtimeCheckRuleMap;
    }


    private synchronized Map<Long, TbRealtimeCheckTask> queryRealtimeCheckTaskMap() {
        Map<Long, TbRealtimeCheckTask> realtimeCheckTaskMap =
                realtimeCheckTaskMapCache.getIfPresent("realtimeCheckTaskMap");
        if (realtimeCheckTaskMap == null) {
            logger.debug("查询数据{}", "realtimeCheckTaskMap");
            List<TbRealtimeCheckTask> realtimeCheckTaskList = tbRealtimeCheckTaskService.list();
            realtimeCheckTaskMap = realtimeCheckTaskList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            realtimeCheckTaskMapCache.put("realtimeCheckTaskMap", realtimeCheckTaskMap);
        }
        return realtimeCheckTaskMap;
    }

    private synchronized Map<Long, TbRealtimeCheckRuleTemplate> queryRealtimeCheckRuleTemplateMap() {
        Map<Long, TbRealtimeCheckRuleTemplate> realtimeCheckRuleTemplateMap = realtimeCheckRuleTemplateMapCache
                .getIfPresent("realtimeCheckRuleTemplateMap");
        if (realtimeCheckRuleTemplateMap == null) {
            logger.debug("查询数据{}", "realtimeCheckRuleTemplateMap");
            List<TbRealtimeCheckRuleTemplate> realtimeCheckRuleTemplateList = checkRuleTemplateService.list();
            realtimeCheckRuleTemplateMap = realtimeCheckRuleTemplateList.stream()
                    .collect(Collectors.toMap(item -> item.getId(), item -> item));
            realtimeCheckRuleTemplateMapCache.put("realtimeCheckRuleTemplateMap", realtimeCheckRuleTemplateMap);
        }
        return realtimeCheckRuleTemplateMap;
    }

    private synchronized Map<Long, TbTable> queryTableMap() {
        Map<Long, TbTable> tableMap = tableMapCache.getIfPresent("tableMap");
        if (tableMap == null) {
            logger.debug("查询数据{}", "tableMap");
            List<TbTable> tbTableList = tbTableService.list();
            tableMap = tbTableList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            tableMapCache.put("tableMap", tableMap);
        }
        return tableMap;
    }
}
