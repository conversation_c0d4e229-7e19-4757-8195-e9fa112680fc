package com.eoi.jax.web.data.quality.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.excel.BaseAuthExcelService;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleBaseValueEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleCalculationRuleEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleSampleMethodEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleTemplateCategoryEnum;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.ExpectedValue;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.RealtimeCheckRuleTemplateCategoryCountResp;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.RealtimeCheckRuleTemplateCreateReq;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.RealtimeCheckRuleTemplateExcelMode;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.RealtimeCheckRuleTemplateQueryReq;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.RealtimeCheckRuleTemplateResp;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.RealtimeCheckRuleTemplateUpdateReq;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.RuleTemplateExtraSetting;
import com.eoi.jax.web.data.quality.service.RealtimeCheckRuleTemplateService;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckRuleTemplate;
import com.eoi.jax.web.repository.search.result.CommonCountResult;
import com.eoi.jax.web.repository.service.TbRealtimeCheckRuleService;
import com.eoi.jax.web.repository.service.TbRealtimeCheckRuleTemplateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@Service
public class RealtimeCheckRuleTemplateServiceImpl extends BaseAuthExcelService<
    TbRealtimeCheckRuleTemplateService,
    TbRealtimeCheckRuleTemplate,
    RealtimeCheckRuleTemplateResp,
    RealtimeCheckRuleTemplateCreateReq,
    RealtimeCheckRuleTemplateUpdateReq,
    RealtimeCheckRuleTemplateQueryReq,
    RealtimeCheckRuleTemplateExcelMode> implements RealtimeCheckRuleTemplateService {

    @Autowired
    private TbRealtimeCheckRuleTemplateService tbRealtimeCheckRuleTemplateService;

    @Autowired
    private TbRealtimeCheckRuleService tbRealtimeCheckRuleService;


    public RealtimeCheckRuleTemplateServiceImpl(TbRealtimeCheckRuleTemplateService tbRealtimeCheckRuleTemplateService) {
        super(tbRealtimeCheckRuleTemplateService);
    }


    @Override
    public Paged<RealtimeCheckRuleTemplateResp> query(RealtimeCheckRuleTemplateQueryReq req) {
        IPage<TbRealtimeCheckRuleTemplate> resultPage = tbRealtimeCheckRuleTemplateService.selectCustomPage(req.page(),
            req.query(), req.getFilter().getTaskId());
        Paged<RealtimeCheckRuleTemplateResp> paged = new Paged<>(resultPage.getTotal(), marshallingRespListFrom(resultPage.getRecords()));
        if (CollUtil.isNotEmpty(paged.getList())) {
            // 计算引用
            List<CommonCountResult> commonCountResultList = tbRealtimeCheckRuleService.countByTemplateId(
                paged.getList().stream().map(RealtimeCheckRuleTemplateResp::getId).collect(Collectors.toList()));
            if (CollUtil.isEmpty(commonCountResultList)) {
                commonCountResultList = new ArrayList<>();
            }
            Map<Long, Long> map = commonCountResultList.stream()
                .collect(Collectors.toMap(it -> Long.valueOf(it.getResultKey()), CommonCountResult::getResultCount));
            for (RealtimeCheckRuleTemplateResp resp : paged.getList()) {
                resp.setReferenceCount(map.getOrDefault(resp.getId(), 0L));
            }
        }

        return paged;
    }

    @Override
    public void whenCreate(RealtimeCheckRuleTemplateCreateReq req, TbRealtimeCheckRuleTemplate entity) {
        super.whenCreate(req, entity);
        entity.setIsBuiltIn(0);
    }

    @Override
    public void whenUpdate(RealtimeCheckRuleTemplateUpdateReq req, TbRealtimeCheckRuleTemplate entity) {
        super.whenUpdate(req, entity);
        entity.setIsBuiltIn(0);
    }

    @Override
    public RealtimeCheckRuleTemplateResp delete(Long id) {
        List<CommonCountResult> commonCountResultList = tbRealtimeCheckRuleService.countByTemplateId(
            CollUtil.newArrayList(id));
        if (CollUtil.isNotEmpty(commonCountResultList) && commonCountResultList.get(0).getResultCount() > 0) {
            throw new BizException(ResponseCode.DELETE_IN_USE.getCode(), "规则模式使用中, 不能删除");
        }

        return super.delete(id);
    }


    @Override
    public List<RealtimeCheckRuleTemplateCategoryCountResp> countByCategory() {
        List<String> categoryList = Arrays.stream(RealtimeCheckRuleTemplateCategoryEnum.values())
            .map(RealtimeCheckRuleTemplateCategoryEnum::code).collect(Collectors.toList());
        List<CommonCountResult> commonCountResultList = tbRealtimeCheckRuleTemplateService.countByCategory(categoryList);
        if (CollUtil.isEmpty(commonCountResultList)) {
            CollUtil.newArrayList();
        }
        Map<String, Long> map = commonCountResultList.stream()
            .collect(Collectors.toMap(CommonCountResult::getResultKey,
                CommonCountResult::getResultCount));

        return Arrays.stream(RealtimeCheckRuleTemplateCategoryEnum.values()).map(it -> {
            RealtimeCheckRuleTemplateCategoryCountResp resp = new RealtimeCheckRuleTemplateCategoryCountResp();
            resp.setCategory(it.message());
            resp.setCategoryCode(it.code());
            resp.setCount(map.getOrDefault(it.code(), 0L));
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public String sheetName() {
        return "实时数据规则模板";
    }

    @Override
    public List<RealtimeCheckRuleTemplateExcelMode> example() {
        return null;
    }

    @Override
    public void verifyData(List<RealtimeCheckRuleTemplateExcelMode> importRows) {
        if (CollUtil.isEmpty(importRows)) {
            return;
        }
        List<RealtimeCheckRuleTemplateResp> realtimeCheckRuleTemplateResps = allWithProjectAuth();
        Map<String, RealtimeCheckRuleTemplateResp> ruleTemplateRespMap =
            realtimeCheckRuleTemplateResps.stream().collect(Collectors.toMap(item -> item.getName(), item -> item, (v1, v2) -> v1));
        importRows.forEach(item -> {
            RealtimeCheckRuleTemplateResp realtimeCheckRuleTemplateResp = ruleTemplateRespMap.get(item.getName());
            if (realtimeCheckRuleTemplateResp != null) {
                item.setCoverField("name");
                item.setId(realtimeCheckRuleTemplateResp.getId());
            }
        });
    }

    @Override
    public void saveRows(Boolean skip, List<RealtimeCheckRuleTemplateExcelMode> importRows) {
        if (CollUtil.isEmpty(importRows)) {
            return;
        }
        verifyData(importRows);
        importRows.forEach(item -> {
            item.setBaseValueType(RealtimeCheckRuleBaseValueEnum.getCodeByMessage(item.getBaseValueType()));
            item.setCalculationRule(RealtimeCheckRuleCalculationRuleEnum.getCodeByMessage(item.getCalculationRule()));
            item.setCategory(RealtimeCheckRuleTemplateCategoryEnum.getCodeByMessage(item.getCategory()));
            item.setSampleMethod(RealtimeCheckRuleSampleMethodEnum.getCodeByMessage(item.getSampleMethod()));
            List<ExpectedValue> expectedList = null;
            if (StringUtils.isNotEmpty(item.getExpectedValue())) {
                expectedList = JSONUtil.toList(JSONUtil.parseArray(item.getExpectedValue()),
                    ExpectedValue.class);
            }
            item.setExpectedValueList(expectedList);
            RuleTemplateExtraSetting extraSetting = null;
            if (StringUtils.isNotEmpty(item.getExtraSetting())) {
                extraSetting = JSONUtil.toBean(item.getExtraSetting(), RuleTemplateExtraSetting.class);
            }
            item.setExtraSettingObject(extraSetting);


        });

        saveRows(skip, importRows,
            (x -> {
                RealtimeCheckRuleTemplateCreateReq createReq = importAndCreate(x);
                return createReq;
            }),
            this::importAndUpdateReq);
    }

    private RealtimeCheckRuleTemplateUpdateReq importAndUpdateReq(RealtimeCheckRuleTemplateExcelMode excelMode) {
        RealtimeCheckRuleTemplateUpdateReq dataReq = new RealtimeCheckRuleTemplateUpdateReq();
        ModelBeanUtil.copyBean(excelMode, dataReq);
        dataReq.setExpectedValueList(excelMode.getExpectedValueList());
        dataReq.setExtraSetting(excelMode.getExtraSettingObject());
        return dataReq;
    }

    private RealtimeCheckRuleTemplateCreateReq importAndCreate(RealtimeCheckRuleTemplateExcelMode excelMode) {
        RealtimeCheckRuleTemplateCreateReq dataReq = new RealtimeCheckRuleTemplateCreateReq();
        ModelBeanUtil.copyBean(excelMode, dataReq);
        dataReq.setExpectedValueList(excelMode.getExpectedValueList());
        dataReq.setExtraSetting(excelMode.getExtraSettingObject());

        return dataReq;
    }

    @Override
    public List<RealtimeCheckRuleTemplateExcelMode> exportData(ImportExcelReq req) {
        QueryWrapper wrapper = new QueryWrapper();
        buildExcelExportQueryWrapper(wrapper, req, RealtimeCheckRuleTemplateQueryReq.class);

        List<RealtimeCheckRuleTemplateExcelMode> list = new ArrayList<>();
        List<RealtimeCheckRuleTemplateResp> realtimeCheckRuleTemplateResps = allWithProjectAuth(wrapper);
        realtimeCheckRuleTemplateResps.stream().filter(item -> !item.getIsBuiltIn())
            .forEach(item -> {
                RealtimeCheckRuleTemplateExcelMode ruleTemplateExcelMode = new RealtimeCheckRuleTemplateExcelMode();
                ModelBeanUtil.copyBean(item, ruleTemplateExcelMode);

                ruleTemplateExcelMode.setBaseValueType(RealtimeCheckRuleBaseValueEnum.getMessageByCode(item.getBaseValueType()));
                ruleTemplateExcelMode.setCalculationRule(RealtimeCheckRuleCalculationRuleEnum.getMessageByCode(item.getCalculationRule()));
                ruleTemplateExcelMode.setCategory(RealtimeCheckRuleTemplateCategoryEnum.getMessageByCode(item.getCategory()));
                ruleTemplateExcelMode.setSampleMethod(RealtimeCheckRuleSampleMethodEnum.getMessageByCode(item.getSampleMethod()));

                ruleTemplateExcelMode
                    .setExpectedValue(item.getExpectedValueList() == null ? null :
                        JSON.toJSONString(item.getExpectedValueList()));
                ruleTemplateExcelMode
                    .setExtraSetting(item.getExtraSetting() == null ? null : JSON.toJSONString(item.getExtraSetting()));

                list.add(ruleTemplateExcelMode);
            });
        return list;
    }
}
