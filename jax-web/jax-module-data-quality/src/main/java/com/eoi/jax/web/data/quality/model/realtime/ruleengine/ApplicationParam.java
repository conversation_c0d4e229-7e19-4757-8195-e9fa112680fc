package com.eoi.jax.web.data.quality.model.realtime.ruleengine;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/5/12 10:35
 */
public class ApplicationParam {
    private String vmUrl;

    private List<ReporterParam> reporters;

    private List<RuleParam> rules;

    public String getVmUrl() {
        return vmUrl;
    }

    public void setVmUrl(String vmUrl) {
        this.vmUrl = vmUrl;
    }

    public List<ReporterParam> getReporters() {
        return reporters;
    }

    public void setReporters(List<ReporterParam> reporters) {
        this.reporters = reporters;
    }

    public List<RuleParam> getRules() {
        return rules;
    }

    public void setRules(List<RuleParam> rules) {
        this.rules = rules;
    }
}
