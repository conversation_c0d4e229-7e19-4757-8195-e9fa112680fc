package com.eoi.jax.web.data.quality.model.alert.table;

import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.repository.entity.TbAlertTableRelation;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Author: tangy
 * @Date: 2023/5/25
 * @Desc:
 **/
public class AlertTableRelationResp implements IRespModel<TbAlertTableRelation> {
    @Schema(description = "模型名称")
    private String tableName;
    @Schema(description = "模型id")
    private Long tbId;
    @Schema(description = "作业id")
    private Long pipelineId;
    @Schema(description = "模型当前版本信息")
    private Integer version;


    /**
     * 激活时间
     */
    @Schema(description = "激活时间")
    private Long firedTime;

    /**
     * 规则id
     */
    @Schema(description = "规则id")
    private Long ruleId;

    /**
     * 告警等级
     */
    @Schema(description = "告警等级")
    private String alertLevel;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getFiredTime() {
        return firedTime;
    }

    public void setFiredTime(Long firedTime) {
        this.firedTime = firedTime;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getAlertLevel() {
        return alertLevel;
    }

    public void setAlertLevel(String alertLevel) {
        this.alertLevel = alertLevel;
    }

    @Override
    public Long getId() {
        return null;
    }

    @Override
    public void setId(Long id) {

    }

    @Override
    public Date getCreateTime() {
        return null;
    }

    @Override
    public void setCreateTime(Date createTime) {

    }

    @Override
    public Date getUpdateTime() {
        return null;
    }

    @Override
    public void setUpdateTime(Date updateTime) {

    }

    @Override
    public Long getCreateUser() {
        return null;
    }

    @Override
    public void setCreateUser(Long createUser) {

    }

    @Override
    public Long getUpdateUser() {
        return null;
    }

    @Override
    public void setUpdateUser(Long updateUser) {

    }
}
