package com.eoi.jax.web.data.quality.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.AlarmFieldTemplateEnum;
import com.eoi.jax.web.core.common.enumrate.AlertLevelEnum;
import com.eoi.jax.web.core.common.enumrate.AlertStatusEnum;
import com.eoi.jax.web.core.common.enumrate.PipelineTypeEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.service.BaseService;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleBaseValueEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleCalculationRuleEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleSampleMethodEnum;
import com.eoi.jax.web.data.quality.model.alert.*;
import com.eoi.jax.web.data.quality.model.alert.table.AlertTableListResp;
import com.eoi.jax.web.data.quality.model.realtime.rule.RuleExtraSetting;
import com.eoi.jax.web.data.quality.model.realtime.ruletemplate.ExpectedValue;
import com.eoi.jax.web.data.quality.plugin.sampler.MessageRateSampler;
import com.eoi.jax.web.data.quality.producer.AlertDataNotifyProducerService;
import com.eoi.jax.web.data.quality.service.AlertService;
import com.eoi.jax.web.ingestion.model.pipeline.PipelineResp;
import com.eoi.jax.web.monitor.service.SelfMonitorAlarmService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.search.query.AlertParam;
import com.eoi.jax.web.repository.search.query.AlertParamWithProjectAuth;
import com.eoi.jax.web.repository.search.result.*;
import com.eoi.jax.web.repository.service.*;
import com.eoi.jax.web.repository.util.MybatisOrderUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import freemarker.template.Template;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.StringReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zsc
 * @create 2023/4/24 10:13
 */
@Service
public class AlertServiceImpl extends BaseService<
        TbAlertService,
        TbAlert,
        AlertResp,
        AlertCreateReq,
        AlertUpdateReq,
        AlertQueryReq> implements AlertService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlertServiceImpl.class.getName());

    @Resource
    private TbAlertService tbAlertService;

    @Resource
    private TbRealtimeCheckRuleTemplateService realtimeCheckRuleTemplateService;

    @Resource
    private TbRealtimeCheckRuleService realtimeCheckRuleService;
    @Resource
    private TbPipelineService tbPipelineService;

    @Resource
    private TbTableService tbTableService;
    @Resource
    private AlertDataNotifyProducerService alertDataNotifyProducerService;

    @Autowired
    private TbStorageHiveService storageHiveService;
    @Resource
    private TbAlertTableRelationService tbAlertTableRelationService;
    @Resource
    private TbRealtimeCheckRuleService tbRealtimeCheckRuleService;
    @Resource
    private TbRealtimeCheckTaskService tbRealtimeCheckTaskService;
    @Resource
    private TbRealtimeCheckRuleTemplateService tbRealtimeCheckRuleTemplateService;
    @Resource
    private SelfMonitorAlarmService selfMonitorAlarmService;

    public AlertServiceImpl(IDaoService<TbAlert> dao) {
        super(dao);
    }


    @Override
    public AlertResp update(AlertUpdateReq req) {
        AlertResp resp = super.update(req);
        return resp;
    }

    /**
     * 获取
     *
     * @param id
     * @return
     */
    @Override
    public AlertResp get(Long id) {
        AlertParamWithProjectAuth alertParam = new AlertParamWithProjectAuth(id);
        if (ContextHolder.getProjectAuthorityEnable()) {
            alertParam.setProjectId(ContextHolder.getCurrentProjectId());
        }
        Page<AlertResult> resultPage = tbAlertService.queryList(new Page<>(0, 10), alertParam);
        if (CollUtil.isEmpty(resultPage.getRecords())) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS, "数据不存在");
        }
        AlertResp alertResp = new AlertResp();
        BeanUtil.copyProperties(resultPage.getRecords().get(0), alertResp);
        return alertResp;
    }


    public Paged<AlertListResp> queryList(AlertQueryReq req) {
        AlertQueryFilterReq filter = (AlertQueryFilterReq) req.getFilter();
        AlertQuerySortReq sort = (AlertQuerySortReq) req.getSort();
        Page<AlertResult> page = new Page<>(req.getPage() + 1, req.getSize());
        // list有几个排序字段就会生成几个排序
        page.setOrders(CollUtil.newArrayList(
                MybatisOrderUtil.getOrderItem("a.active_time", sort.isAsc(sort.getActiveTime()))
        ));

        AlertParamWithProjectAuth alertParam = new AlertParamWithProjectAuth();
        BeanUtil.copyProperties(filter, alertParam);
        if (ContextHolder.getProjectAuthorityEnable()) {
            alertParam.setProjectId(ContextHolder.getCurrentProjectId());
        }
        Page<AlertResult> resultPage = tbAlertService.queryList(page, alertParam);

        if (CollUtil.isEmpty(resultPage.getRecords())) {
            return new Paged<>(0, CollUtil.newArrayList());
        }
        List<Long> ruleIds = resultPage.getRecords().stream().map(AlertResult::getRuleId).distinct().collect(Collectors.toList());
        List<RealtimeCheckRuleTemplateResult> templates = tbRealtimeCheckRuleService.queryTemplateByIds(ruleIds);
        Map<Long, RealtimeCheckRuleTemplateResult> templateResultMap = templates.stream()
                .collect(Collectors.toMap(RealtimeCheckRuleTemplateResult::getRuleId, it -> it, (v1, v2) -> v1));

        Paged<AlertListResp> paged = new Paged<AlertListResp>();
        paged.setTotal(resultPage.getTotal());
        List<AlertListResp> list = resultPage.getRecords()
                .stream().map(it -> {
                    AlertListResp alertListResp = new AlertListResp();
                    ModelBeanUtil.copyBean(it, alertListResp);
                    if (StrUtil.isNotBlank(it.getLabels())) {
                        alertListResp.setLabels(JsonUtil.decode(it.getLabels(), new TypeReference<Map<String, String>>() {
                        }));
                    }
                    RealtimeCheckRuleTemplateResult templateResult = templateResultMap.get(alertListResp.getRuleId());
                    if (templateResult == null) {
                        LOGGER.error("根据规则id[{}]未找到指定模型", it.getRuleId());
                        return alertListResp;
                    }
                    alertListResp.setRuleTemplateId(templateResult.getTemplateId());
                    alertListResp.setRuleTemplateName(templateResult.getTemplateName());
                    return alertListResp;
                })
                .collect(Collectors.toList());
        paged.setList(list);
        return paged;
    }

    @Override
    public AlertResp delete(Long id) {
        return super.delete(id);
    }

    @Override
    public void closeById(Long id) {
        tbAlertService.closeById(id);
    }

    @Override
    public void closeByIds(List<Long> ids) {
        tbAlertService.closeByIds(ids);
    }

    @Override
    public int countByTable(AlertParam param) {
        return tbAlertService.countByTable(param);
    }

    @Override
    public int countByPipeline(AlertParam param) {
        return tbAlertService.countByPipeline(param);
    }

    @Override
    public List<AlertLevelCountResult> countByLevel(AlertParam param) {
        return tbAlertService.countByLevel(param);
    }

    @Override
    public List<TbTable> loadTableList() {
        return tbTableService.list(Wrappers.<TbTable>lambdaQuery());
    }

    @Override
    public QueryStatisticsResp queryStatistics(AlertQueryReq req) {

        AlertParam alertParam = new AlertParam();
        BeanUtil.copyProperties(req.getFilter(), alertParam);

        alertParam.setWarnLevel(null);
        if (StringUtils.isEmpty(alertParam.getStatus())) {
            alertParam.setStatus("OPEN");
        }
        QueryStatisticsResp queryStatisticsResp = new QueryStatisticsResp();
        queryStatisticsResp.setStandalonePipleline(0);
        queryStatisticsResp.setTableNum(0);
        queryStatisticsResp.setPipelineList(getPipelineList(alertParam));

        alertParam.setTableId(req.getFilter().getTableId());
        queryStatisticsResp.setTableList(getTableList(alertParam));
        Map<String, AlertLevelCountResult> levelCountMap =
                countByLevel(alertParam).stream().collect(Collectors.toMap(item -> item.getAlertLevel(),
                        item -> item));
        queryStatisticsResp.setCriticalNum(levelCountMap.get(AlertLevelEnum.CRITICAL.code()) == null ? 0 :
                levelCountMap.get(AlertLevelEnum.CRITICAL.code()).getTotalNum());
        queryStatisticsResp.setWarningNum(levelCountMap.get(AlertLevelEnum.WARNING.code()) == null ? 0 :
                levelCountMap.get(AlertLevelEnum.WARNING.code()).getTotalNum());
        queryStatisticsResp.setInfoNum(levelCountMap.get(AlertLevelEnum.INFO.code()) == null ? 0 :
                levelCountMap.get(AlertLevelEnum.INFO.code()).getTotalNum());
        return queryStatisticsResp;
    }

    private List<SelectOption> getTableList(AlertParam alertParam) {
        return tbAlertService.getTableList(alertParam);
    }

    private List<SelectOption> getPipelineList(AlertParam alertParam) {
        alertParam.setTableId(null);
        return tbAlertService.getPipelineList(alertParam);
    }

    @Override
    public boolean dealAlertMessage(AlertDealParam alertDealParam) {
        JaxAlert jaxAlert = alertDealParam.getJaxAlert();
        LOGGER.debug("开始处理告警信息, ruleId: {}, alertKey: {}, level: {}", jaxAlert.getRuleId(), jaxAlert.getAlertKey(), jaxAlert.getLevel());
        if (jaxAlert.getRuleId() == null) {
            LOGGER.warn("规则id[ruleId]不能为空");
        }
        if (JaxAlert.ALERT_ACTION_CLOSE.equals(jaxAlert.getAction())) {
            tbAlertService.closeByAlertKey(jaxAlert.getAlertKey(), jaxAlert.getResolvedTime(), "自动关闭");
            selfMonitorAlarmService.dealDataQualityAlarm(jaxAlert.getAlertKey(), jaxAlert.getResolvedTime(), "自动关闭");
        }
        if (!JaxAlert.ALERT_ACTION_OPEN.equals(jaxAlert.getAction())) {
            return true;
        }

        TbPipeline jaxPipeline = null;
        Map<String, String> annotationMap = new HashMap<>(0);
        if (jaxAlert.getAnnotations() != null) {
            annotationMap.putAll(jaxAlert.getAnnotations());
        }
        if (jaxAlert.getLabels() != null) {
            annotationMap.putAll(jaxAlert.getLabels());
        }
        jaxAlert.setAnnotations(annotationMap);

        if (StringUtils.isEmpty(annotationMap.get("jax_pipeline_id"))) {
            LOGGER.warn("作业id[jax_pipeline_id]不能为空");
        } else {
            jaxPipeline = alertDealParam.getPipelineMap().get(Long.parseLong(annotationMap.get("jax_pipeline_id")));
            if (jaxPipeline == null) {
                jaxPipeline = tbPipelineService.getById(Long.parseLong(annotationMap.get("jax_pipeline_id")));
            }
        }
        List<Long> modelFormConfig = getModelFormConfig(jaxPipeline);

        TbAlert tbAlert = reBuildAlert(jaxAlert, annotationMap, modelFormConfig);
        if (jaxPipeline == null) {
            LOGGER.error("告警信息: {}, 作业id: {} 不存在", tbAlert.getAlertKey(), tbAlert.getPipelineId());
            tbAlert.setRemark("数据缺失,主动关闭");
            tbAlert.setStatus(AlertStatusEnum.CLOSED.code());
        } else {
            //报存告警关联信息
            List<TbAlertTableRelation> alertTableRelations = new ArrayList<>();
            for (Long modelId : modelFormConfig) {
                alertTableRelations.add(getTbAlertTableRelation(tbAlert, modelId));
            }
            List<Long> tableIds = alertTableRelations.stream()
                    .map(item -> item.getTableId()).collect(Collectors.toList());
            tbAlert.setCreateTime(new Date(tbAlert.getFiredTime()));
            tbAlert.setUpdateTime(new Date(tbAlert.getFiredTime()));
            transferAlertContent(tbAlert, jaxPipeline, tableIds, alertDealParam);
            tbAlertService.save(tbAlert);
            if (alertTableRelations.size() > 0) {
                try {
                    alertTableRelations.forEach(item -> {
                        item.setAlertId(tbAlert.getId());
                    });
                    tbAlertTableRelationService.saveBatch(alertTableRelations);
                } catch (Exception e) {
                    LOGGER.error("报错告警关联模型失败：{},{}", e.getMessage(), e);
                }
            }
            selfMonitorAlarmService.dealDataQualityAlarm(tbAlert, modelFormConfig);
        }
        return true;
    }

    @NotNull
    private static TbAlert reBuildAlert(JaxAlert jaxAlert, Map<String, String> annotationMap, List<Long> modelFormConfig) {
        TbAlert tbAlert = new TbAlert();
        tbAlert.setAlertKey(jaxAlert.getAlertKey());
        tbAlert.setRuleId(jaxAlert.getRuleId());
        tbAlert.setLevel(jaxAlert.getLevel());
        if (annotationMap.get("jax_pipeline_id") != null) {
            String pipelineId = annotationMap.get("jax_pipeline_id");
            tbAlert.setPipelineId(StringUtils.isNotBlank(pipelineId) ? Long.parseLong(pipelineId) : null);
            tbAlert.setTableNum(modelFormConfig.size());
        }
        tbAlert.setSubject(jaxAlert.getSubject());
        tbAlert.setContent(jaxAlert.getContent());
        tbAlert.setLabels(JSONUtil.toJsonStr(jaxAlert.getLabels()));
        tbAlert.setAnnotations(JSONUtil.toJsonStr(jaxAlert.getAnnotations()));
        tbAlert.setStatus(AlertStatusEnum.OPEN.code());
        tbAlert.setActiveTime(jaxAlert.getActiveTime());
        tbAlert.setFiredTime(jaxAlert.getFiredTime());
        tbAlert.setResolvedTime(jaxAlert.getResolvedTime());
        tbAlert.setLastSendTime(jaxAlert.getLastSentTime());
        tbAlert.setBase(jaxAlert.getBase());
        tbAlert.setSample(jaxAlert.getSample());
        tbAlert.setValue(jaxAlert.getValue());
        tbAlert.setTriggerCondition(jaxAlert.getCondition());
        tbAlert.setIsDeleted(0);
        tbAlert.setCreateTime(new Date());
        tbAlert.setUpdateTime(new Date());
        tbAlert.setUpdateUser(ContextHolder.getUserId());
        tbAlert.setCreateUser(ContextHolder.getUserId());
        return tbAlert;
    }

    @NotNull
    private static TbAlertTableRelation getTbAlertTableRelation(TbAlert tbAlert, Long modelId) {
        TbAlertTableRelation alertTableRelation = new TbAlertTableRelation();
        alertTableRelation.setAlertId(tbAlert.getId());
        alertTableRelation.setAlertKey(tbAlert.getAlertKey());
        alertTableRelation.setAlertLevel(tbAlert.getLevel());
        alertTableRelation.setStatus(tbAlert.getStatus());
        alertTableRelation.setTableId(modelId);
        alertTableRelation.setFiredTime(tbAlert.getFiredTime());
        alertTableRelation.setIsDeleted(0);
        alertTableRelation.setRuleId(tbAlert.getRuleId());
        alertTableRelation.setPipelineId(tbAlert.getPipelineId());
        alertTableRelation.setCreateTime(new Date());
        alertTableRelation.setUpdateTime(alertTableRelation.getCreateTime());

        return alertTableRelation;
    }

    /**
     * 处理告警内容格式
     *
     * @param tbAlert
     * @param jaxPipeline
     * @param tableIds
     * @param alertDealParam
     */
    private void transferAlertContent(TbAlert tbAlert, TbPipeline jaxPipeline,
                                      List<Long> tableIds,
                                      AlertDealParam alertDealParam) {
        TbRealtimeCheckRule ruleInfo = alertDealParam.getRealtimeCheckRuleMap().get(tbAlert.getRuleId());
        if (ruleInfo == null) {
            ruleInfo = tbRealtimeCheckRuleService.getById(tbAlert.getRuleId());
        }
        if (ruleInfo == null) {
            LOGGER.error("告警规则：{} 不存在", tbAlert.getRuleId());
            return;
        }
        TbRealtimeCheckTask taskInfo = alertDealParam.getRealtimeCheckTaskMap().get(ruleInfo.getTaskId());
        if (taskInfo == null) {
            taskInfo = tbRealtimeCheckTaskService.getById(ruleInfo.getTaskId());
        }
        if (taskInfo == null) {
            LOGGER.error("告警任务：{} 不存在", ruleInfo.getTaskId());
            return;
        }
        TbRealtimeCheckRuleTemplate ruleTemplate = alertDealParam.getRealtimeCheckRuleTemplateMap()
                .get(ruleInfo.getTemplateId());
        if (ruleTemplate == null) {
            ruleTemplate = tbRealtimeCheckRuleTemplateService.getById(ruleInfo.getTemplateId());
        }
        Map<String, Object> messageMap = new HashMap<String, Object>(8);
        List<String> tableNames = getTableNames(tableIds, alertDealParam);
        List<String> groupFields = null;
        if (StringUtils.isNotEmpty(ruleInfo.getGroupField())) {
            groupFields = JsonUtil.decode(ruleInfo.getGroupField(), new TypeReference<List<String>>() {
            });
        } else {
            groupFields = CollUtil.newArrayList();
        }

        messageMap.put("taskName", taskInfo.getName());
        messageMap.put("ruleName", ruleInfo.getName());
        messageMap.put("ruleInfo", buildRuleInfo(ruleInfo, groupFields));
        messageMap.put("ruleTemplateName", ruleTemplate == null ? "规则已删除" : ruleTemplate.getName());
        messageMap.put("jobName", jaxPipeline.getPipelineAlias());
        messageMap.put("tableName", StringUtils.join(tableNames, ","));
        messageMap.put("sampleValue", tbAlert.getSample());
        messageMap.put("baseValue", tbAlert.getBase());
        messageMap.put("value", tbAlert.getValue());
        if (groupFields.size() > 0) {
            messageMap.put("groupField", "[" + (StringUtils.join(groupFields, ", ") + "]"));
            List<String> groupValueList = new ArrayList<>();
            for (String field : groupFields
            ) {
                String filedKey = new MessageRateSampler().replaceSpecialCharacte(field);
                groupValueList.add(alertDealParam.getJaxAlert().getLabels().get(filedKey));
            }
            messageMap.put("groupValue", "[" + (StringUtils.join(groupValueList, ", ") + "]"));
        } else {
            messageMap.put("groupField", "");
            messageMap.put("groupValue", "");
        }

        messageMap.put("condition", ruleInfo.getCalculationRule());
        if (StringUtils.isNotEmpty(tbAlert.getContent())) {
            freemarkerFormatContent(tbAlert, alertDealParam.getNeedSendNotify(), messageMap);
        }
    }

    @NotNull
    private String buildRuleInfo(TbRealtimeCheckRule ruleInfo, List<String> groupFields) {
        StringBuffer ruleInfoBuffer = new StringBuffer("{");
        if (StringUtils.isNotEmpty(ruleInfo.getGroupField())) {
            ruleInfoBuffer.append(" 分组字段: ").append("[").append(StringUtils.join(groupFields, ",")).append("]").append(",");
        }
        if (StringUtils.isNotEmpty(ruleInfo.getSampleMethod())) {
            ruleInfoBuffer.append(" 采样方式: ").append(RealtimeCheckRuleSampleMethodEnum
                    .getMessageByCode(ruleInfo.getSampleMethod())).append(",");
        }
        if (ruleInfo.getSamplePeriod() != null) {
            ruleInfoBuffer.append(" 采样周期: ").append(ruleInfo.getSamplePeriod()).append("分钟").append(",");
        }
        if (StringUtils.isNotEmpty(ruleInfo.getCalculationRule())) {
            ruleInfoBuffer.append(" 采样规则: ").append(RealtimeCheckRuleCalculationRuleEnum
                    .getMessageByCode(ruleInfo.getCalculationRule())).append(",");
        }
        if (ruleInfo.getBaseValueN() != null) {
            String baseValueType = RealtimeCheckRuleBaseValueEnum.getMessageByCode(ruleInfo.getBaseValueType());
            ruleInfoBuffer.append(" 基准值: ").append(baseValueType);
            if (ruleInfo.getBaseValueN() != null) {
                ruleInfoBuffer.append(ruleInfo.getBaseValueN()).append("个周期");
            }
            ruleInfoBuffer.append(",");
        }
        Map<String, String> codeToValueMap = getRuleDetailCodeMap();
        //处理告警等级
        if (StringUtils.isNotEmpty(ruleInfo.getExpectedValue())) {
            transferExpectedValue(ruleInfo, ruleInfoBuffer, codeToValueMap);
        }
        //处理扩展属性 RuleExtraSetting
        if (StringUtils.isNotEmpty(ruleInfo.getExtraSetting())) {
            transferExtraSetting(ruleInfo, ruleInfoBuffer, codeToValueMap);
        }
        //告警压缩抑制策略
        if (StringUtils.isNotEmpty(ruleInfo.getAlarmStrategy())) {
            transferAlarmStrategy(ruleInfo, ruleInfoBuffer);
        }
        ruleInfoBuffer.append("}");
        return ruleInfoBuffer.toString();
    }

    private List<String> getTableNames(List<Long> tableIds, AlertDealParam alertDealParam) {
        if (tableIds.size() > 0) {
            if (alertDealParam.getTableMap() == null || alertDealParam.getTableMap().size() == 0) {
                List<TbTable> list = tbTableService.list(Wrappers.<TbTable>lambdaQuery().in(TbTable::getId, tableIds));
                return list.stream().map(item -> item.getTbAlias()).collect(Collectors.toList());
            } else {
                return
                        tableIds.stream()
                                .filter(item -> {
                                    if (alertDealParam.getTableMap().get(item) == null) {
                                        LOGGER.error("找不到模型：{}", item);
                                        return false;
                                    } else {
                                        return true;
                                    }
                                })
                                .map(item -> alertDealParam.getTableMap().get(item).getTbAlias()).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    private void freemarkerFormatContent(TbAlert tbAlert, Boolean needSendNotify, Map<String,
            Object> messageMap) {

        AlarmFieldTemplateEnum[] values = AlarmFieldTemplateEnum.values();
        try {
            Template tmp = new Template(null, new StringReader(tbAlert.getContent()), null);
            for (AlarmFieldTemplateEnum alarmFieldTemplateEnum : values) {
                if (messageMap.get(alarmFieldTemplateEnum.name()) == null) {
                    messageMap.put(alarmFieldTemplateEnum.name(), "");
                }
            }
            String message = FreeMarkerTemplateUtils.processTemplateIntoString(tmp, messageMap);
            tbAlert.setContent(message);
            if (needSendNotify) {
                alertDataNotifyProducerService.sendMessage(JSONUtil.toJsonStr(tbAlert));
            }
        } catch (Exception e) {
            LOGGER.error("告警消息通知异常：{},{}", e.getMessage(), e);
        }
    }

    /**
     * 处理告警期望值
     *
     * @param ruleInfo
     * @param ruleInfoBuffer
     * @param codeToValueMap
     */
    private static void transferExpectedValue(TbRealtimeCheckRule ruleInfo,
                                              StringBuffer ruleInfoBuffer,
                                              Map<String, String> codeToValueMap) {
        List<ExpectedValue> expectedValues = null;
        try {
            expectedValues = JSONUtil.toList(JSONUtil.parseArray(ruleInfo.getExpectedValue()),
                    ExpectedValue.class);
        } catch (Exception e) {
            LOGGER.error("解析规则期望值异常： {}, {}", e.getMessage(), ruleInfo.getExpectedValue(), e);
            expectedValues = new ArrayList<>(0);
        }
        ruleInfoBuffer.append(" 告警阈值: [");
        for (ExpectedValue expectedValue : expectedValues) {
            String level = AlertLevelEnum.getMessageByCode(expectedValue.getAlarmLevel());
            ruleInfoBuffer.append(" {告警等级: ").append(level).append(", 取值范围: (");
            ExpectedValue.Condition condition = expectedValue.getCondition();
            if (condition == null) {
                continue;
            }
            String logicalOperator = condition.getLogicalOperator();
            List<ExpectedValue.ConditionOperator> operatorList = condition.getOperatorList();
            int i = 0;
            for (ExpectedValue.ConditionOperator con : operatorList) {
                if (StringUtils.isEmpty(con.getValue())) {
                    continue;
                }
                if (i > 0) {
                    ruleInfoBuffer.append(codeToValueMap.get(logicalOperator));
                }
                ruleInfoBuffer.append(codeToValueMap.get(con.getOperator())).append(con.getValue());
                i++;
            }
            ruleInfoBuffer.append(")");
            ruleInfoBuffer.append("}");
        }
        ruleInfoBuffer.append("],");
    }

    /**
     * 告警详情扩展属性转码
     *
     * @param ruleInfo
     * @param ruleInfoBuffer
     * @param codeToValueMap
     */
    private static void transferExtraSetting(TbRealtimeCheckRule ruleInfo,
                                             StringBuffer ruleInfoBuffer,
                                             Map<String, String> codeToValueMap) {
        RuleExtraSetting ruleExtraSetting = null;
        try {
            ruleExtraSetting = JSONUtil.toBean(ruleInfo.getExtraSetting(), RuleExtraSetting.class);
        } catch (Exception e) {
            LOGGER.error("解析规则扩展属性异常： {}, {}", e.getMessage(), ruleInfo.getExtraSetting(), e);
            ruleExtraSetting = new RuleExtraSetting();
        }
        if (StringUtils.isNotEmpty(ruleExtraSetting.getDataTimeType())
                && codeToValueMap.get(ruleExtraSetting.getDataTimeType()) != null) {
            ruleInfoBuffer.append(" 数据时间: ");
            ruleInfoBuffer.append(codeToValueMap.get(ruleExtraSetting.getDataTimeType())).append(",");
        }
        if (StringUtils.isNotEmpty(ruleExtraSetting.getBaseTimeType())
                && codeToValueMap.get(ruleExtraSetting.getBaseTimeType()) != null) {
            ruleInfoBuffer.append(" 基准时间: ");
            ruleInfoBuffer.append(codeToValueMap.get(ruleExtraSetting.getBaseTimeType()))
                    .append(ruleExtraSetting.getBaseTimeFixedThreshold()).append(",");
        }
        if (StringUtils.isNotEmpty(ruleExtraSetting.getStatisticalMethod())
                && codeToValueMap.get(ruleExtraSetting.getStatisticalMethod()) != null) {
            ruleInfoBuffer.append(" 统计方式: ");
            ruleInfoBuffer.append(codeToValueMap.get(ruleExtraSetting.getStatisticalMethod()))
                    .append(",");
        }
        if (ruleExtraSetting.getFieldErrTypeList() != null && ruleExtraSetting.getFieldErrTypeList().size() > 0) {
            List<String> collect = ruleExtraSetting.getFieldErrTypeList()
                    .stream()
                    .map(item -> codeToValueMap.get(item)).collect(Collectors.toList());
            ruleInfoBuffer.append(" 规则白名单: ");
            ruleInfoBuffer.append(collect.toString())
                    .append(",");
        }
        if (ruleExtraSetting.getWhiteList() != null && ruleExtraSetting.getWhiteList().size() > 0) {

            ruleInfoBuffer.append(" 字段白名单: ");
            ruleInfoBuffer.append(ruleExtraSetting.getWhiteList().toString())
                    .append(",");
        }

        if (ruleExtraSetting.getBlackList() != null && ruleExtraSetting.getBlackList().size() > 0) {

            ruleInfoBuffer.append(" 字段黑名单: ");
            ruleInfoBuffer.append(ruleExtraSetting.getBlackList().toString())
                    .append(",");
        }
    }

    /**
     * 处理告警策略
     *
     * @param ruleInfo
     * @param ruleInfoBuffer
     */
    private static void transferAlarmStrategy(TbRealtimeCheckRule ruleInfo, StringBuffer ruleInfoBuffer) {
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONUtil.parseObj(ruleInfo.getAlarmStrategy());
        } catch (Exception e) {
            LOGGER.error("告警压缩抑制策略异常： {}, {}", e.getMessage(), ruleInfo.getAlarmStrategy(), e);
            jsonObject = new JSONObject();
        }
        Integer duration = jsonObject.getInt("duration");
        Integer suppression = jsonObject.getInt("suppression");
        if (duration != null || suppression != null) {
            ruleInfoBuffer.append(" 告警压缩策略: [");
            if (duration != null) {
                ruleInfoBuffer.append("在").append(duration).append("秒内PQL查询均可命中则触发告警,");
            }
            if (suppression != null) {
                ruleInfoBuffer.append("在").append(suppression).append("秒内抑制相同告警出现");
            }
            ruleInfoBuffer.append("]");
        }
    }


    /**
     * 规则详情转码
     *
     * @return
     */
    private static Map<String, String> getRuleDetailCodeMap() {
        Map<String, String> codeToValueMap = new HashMap<>(18);
        codeToValueMap.put("and", " 且 ");
        codeToValueMap.put("or", " 或 ");
        codeToValueMap.put("gt", "大于");
        codeToValueMap.put("ge", "大于等于");
        codeToValueMap.put("lt", "小于");
        codeToValueMap.put("le", "小于等于");
        codeToValueMap.put("process-time", "处理时间");
        codeToValueMap.put("event-time", "业务时间");
        codeToValueMap.put("fixed-threshold", "固定阈");
        codeToValueMap.put("data-field", "基于数据中字段");
        codeToValueMap.put("distinction", "区分规则");
        codeToValueMap.put("no-distinction", "不区分规则");
        codeToValueMap.put("ERR_MISSING", "主键/非空字段缺失检查");
        codeToValueMap.put("ERR_TYPE", "日字段类型不匹配检查");
        codeToValueMap.put("ERR_ENUM", "字段数据字典检查");
        codeToValueMap.put("ERR_NOTNULL", "非空字段检查");
        codeToValueMap.put("ERR_LENGTH", "字段长度检查");
        codeToValueMap.put("ERR_PRECISION", "字段精度检查");
        return codeToValueMap;
    }

    private List<Long> getModelFormConfig(TbPipeline pipeline) {

        List<Long> fromJobList = new ArrayList<>();
        if (pipeline == null) {
            return fromJobList;
        }
        PipelineResp pipelineResp = new PipelineResp().fromEntity(pipeline);
        Map<String, Object> pipelineConfig = pipelineResp.getPipelineConfig();
        List<Map<String, Object>> jobsList = (List<Map<String, Object>>) pipelineConfig.get("jobs");
        if (PipelineTypeEnum.STREAMING.code().equalsIgnoreCase(pipelineResp.getPipelineType())) {
            for (Map<String, Object> jobInfo : jobsList) {
                Map<String, Object> jobConfig = (Map<String, Object>) jobInfo.get("jobConfig");
                if (jobConfig.get("modelId") != null) {
                    fromJobList.add(Long.parseLong(String.valueOf(jobConfig.get("modelId"))));
                }
            }

        } else {
            TbStorageHive storageHive = storageHiveService.getById(pipelineResp.getProcessId());
            if (storageHive == null) {
                LOGGER.error(" 查找作业pipelineId[{}], processId: [{}]关联模型异常找不到数据", pipelineResp.getId(), pipelineResp.getProcessId());
                return fromJobList;
            }
            fromJobList.add(storageHive.getKafkaTbId());
            fromJobList.add(storageHive.getHiveTbId());
        }
        return fromJobList;
    }

    @Override
    public List<AlertTableListResp> alertTableList(Long id) {
        List<AlertTableListResp> respList = new ArrayList<AlertTableListResp>();
        if (id == null) {
            return new ArrayList<AlertTableListResp>();
        }
        List<AlertTableListResult> results =
                tbAlertTableRelationService.listByAlertId(id);
        for (AlertTableListResult tableListResult : results) {
            AlertTableListResp tableListResp = new AlertTableListResp();
            BeanUtil.copyProperties(tableListResult, tableListResp);
            respList.add(tableListResp);
        }
        return respList;
    }
}
