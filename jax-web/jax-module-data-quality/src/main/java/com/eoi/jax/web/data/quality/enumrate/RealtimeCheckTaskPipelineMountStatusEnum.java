package com.eoi.jax.web.data.quality.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;
import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
public enum RealtimeCheckTaskPipelineMountStatusEnum implements ICodeEnum, ICodeMessageEnum {
    /**
     * 未挂载
     */
    UNMOUNTED("UNMOUNTED", "未挂载"),
    /**
     * 需要移除
     */
    NEED_UNINSTALL("NEED_UNINSTALL", "需要移除"),
    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),
    /**
     * 部分失败
     */
    PARTIAL_FAILED("PARTIAL_FAILED", "部分失败"),
    /**
     * 失败
     */
    FAILED("FAILED", "失败");


    private final String code;

    private final String message;

    RealtimeCheckTaskPipelineMountStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static RealtimeCheckTaskPipelineMountStatusEnum fromString(String code) {
        for (RealtimeCheckTaskPipelineMountStatusEnum value : RealtimeCheckTaskPipelineMountStatusEnum.values()) {
            if (value.code().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        for (RealtimeCheckTaskPipelineMountStatusEnum value : RealtimeCheckTaskPipelineMountStatusEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }

    /**
     * code
     *
     * @return code
     */
    @Override
    public String code() {
        return code;
    }

    @Override
    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

}
