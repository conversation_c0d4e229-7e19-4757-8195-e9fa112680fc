package com.eoi.jax.web.data.quality.model.realtime.config;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
public class TimeIntervalDiffMetricCollectorConfig extends BaseMetricCollectorConfig {

    /**
     * 数据时间模式
     */
    private String dataTimeMode;

    private String timeField;

    private String timeFormat;

    /**
     * 基准时间
     */
    private String intervalField;

    private Integer intervalThreshold;

    private Integer intervalPeriod;

    public String getDataTimeMode() {
        return dataTimeMode;
    }

    public void setDataTimeMode(String dataTimeMode) {
        this.dataTimeMode = dataTimeMode;
    }

    public String getTimeField() {
        return timeField;
    }

    public void setTimeField(String timeField) {
        this.timeField = timeField;
    }

    public String getTimeFormat() {
        return timeFormat;
    }

    public void setTimeFormat(String timeFormat) {
        this.timeFormat = timeFormat;
    }

    public String getIntervalField() {
        return intervalField;
    }

    public void setIntervalField(String intervalField) {
        this.intervalField = intervalField;
    }

    public Integer getIntervalThreshold() {
        return intervalThreshold;
    }

    public void setIntervalThreshold(Integer intervalThreshold) {
        this.intervalThreshold = intervalThreshold;
    }

    public Integer getIntervalPeriod() {
        return intervalPeriod;
    }

    public void setIntervalPeriod(Integer intervalPeriod) {
        this.intervalPeriod = intervalPeriod;
    }
}
