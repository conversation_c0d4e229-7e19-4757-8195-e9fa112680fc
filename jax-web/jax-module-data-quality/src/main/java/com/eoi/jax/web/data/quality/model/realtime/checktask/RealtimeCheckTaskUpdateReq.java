package com.eoi.jax.web.data.quality.model.realtime.checktask;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.data.quality.model.realtime.rule.RealtimeCheckRuleSaveReq;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckTask;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/15
 */
public class RealtimeCheckTaskUpdateReq implements IUpdateModel<TbRealtimeCheckTask> {

    @Schema(description = "id")
    private Long id;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    @Schema(description = "任务名称")
    private String name;

    /**
     * 运行模式
     */
    @NotBlank(message = "运行模式不能为空")
    @Schema(description = "运行模式")
    private String runMode;

    /**
     * 作业选择方式
     */
    @Schema(description = "作业选择方式")
    private String selectMode;

    /**
     * 选择的pipeline任务
     */
    @Schema(description = "选择的pipeline任务")
    private SelectedPipelineTask selectedPipelineTask;

    /**
     * 算子名称
     */
    @Schema(description = "算子名称")
    private String jobName;

    /**
     * 算子中文名称
     */
    @Schema(description = "算子名称")
    private String jobDisplay;

    /**
     * 算子输出挂载点
     */
    @Schema(description = "算子输出挂载点")
    private Integer jobSlot;

    /**
     * kafka模型id
     */
    @Schema(description = "kafka模型id")
    private Long tbId;

    /**
     * 集群
     */
    @Schema(description = "集群")
    private Long clusterId;

    /**
     * yarnSession_Id
     */
    @Schema(description = "yarnSession_Id")
    private Long yarnSessionId;

    /**
     * 框架
     */
    @Schema(description = "框架")
    private Long optsId;

    /**
     * 用户自定义设置
     */
    @Schema(description = "用户自定义设置")
    private Map<String, Object> customSetting;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    @NotNull(message = "检测规则不能为空")
    @Schema(description = "检测规则")
    private List<RealtimeCheckRuleSaveReq> checkRuleList;

    @Schema(description = "立即重启")
    private Boolean restartNow = false;

    @Override
    public TbRealtimeCheckTask toEntity(TbRealtimeCheckTask tbRealtimeCheckTask) {
        TbRealtimeCheckTask entity = IUpdateModel.super.toEntity(tbRealtimeCheckTask);
        if (Objects.nonNull(this.getSelectedPipelineTask())) {
            entity.setSelectedPipelineTask(JsonUtil.encode(this.getSelectedPipelineTask()));
        }
        if (Objects.nonNull(customSetting)) {
            entity.setCustomSetting(JsonUtil.encode(customSetting));
        }
        return entity;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRunMode() {
        return runMode;
    }

    public void setRunMode(String runMode) {
        this.runMode = runMode;
    }

    public String getSelectMode() {
        return selectMode;
    }

    public void setSelectMode(String selectMode) {
        this.selectMode = selectMode;
    }

    public SelectedPipelineTask getSelectedPipelineTask() {
        return selectedPipelineTask;
    }

    public void setSelectedPipelineTask(SelectedPipelineTask selectedPipelineTask) {
        this.selectedPipelineTask = selectedPipelineTask;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobDisplay() {
        return jobDisplay;
    }

    public void setJobDisplay(String jobDisplay) {
        this.jobDisplay = jobDisplay;
    }

    public Integer getJobSlot() {
        return jobSlot;
    }

    public void setJobSlot(Integer jobSlot) {
        this.jobSlot = jobSlot;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public Long getYarnSessionId() {
        return yarnSessionId;
    }

    public void setYarnSessionId(Long yarnSessionId) {
        this.yarnSessionId = yarnSessionId;
    }

    public Long getOptsId() {
        return optsId;
    }

    public void setOptsId(Long optsId) {
        this.optsId = optsId;
    }

    public Map<String, Object> getCustomSetting() {
        return customSetting;
    }

    public void setCustomSetting(Map<String, Object> customSetting) {
        this.customSetting = customSetting;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<RealtimeCheckRuleSaveReq> getCheckRuleList() {
        return checkRuleList;
    }

    public void setCheckRuleList(List<RealtimeCheckRuleSaveReq> checkRuleList) {
        this.checkRuleList = checkRuleList;
    }

    public Boolean getRestartNow() {
        return restartNow;
    }

    public void setRestartNow(Boolean restartNow) {
        this.restartNow = restartNow;
    }
}
