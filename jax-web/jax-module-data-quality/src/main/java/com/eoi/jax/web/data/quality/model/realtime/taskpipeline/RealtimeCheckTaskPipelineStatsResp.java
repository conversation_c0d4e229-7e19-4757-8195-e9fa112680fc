package com.eoi.jax.web.data.quality.model.realtime.taskpipeline;

/**
 * <AUTHOR>
 * @date 2024/10/10
 */
public class RealtimeCheckTaskPipelineStatsResp {

    private Long total;

    private Long pendingCount;

    private Long successCount;

    private Long failCount;

    public RealtimeCheckTaskPipelineStatsResp() {
    }

    public RealtimeCheckTaskPipelineStatsResp(Long total, Long pendingCount, Long successCount, Long failCount) {
        this.total = total;
        this.pendingCount = pendingCount;
        this.successCount = successCount;
        this.failCount = failCount;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getPendingCount() {
        return pendingCount;
    }

    public void setPendingCount(Long pendingCount) {
        this.pendingCount = pendingCount;
    }

    public Long getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Long getFailCount() {
        return failCount;
    }

    public void setFailCount(Long failCount) {
        this.failCount = failCount;
    }
}
