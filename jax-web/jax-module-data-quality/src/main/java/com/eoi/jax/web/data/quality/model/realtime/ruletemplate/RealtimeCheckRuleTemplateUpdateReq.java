package com.eoi.jax.web.data.quality.model.realtime.ruletemplate;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckRuleTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
public class RealtimeCheckRuleTemplateUpdateReq implements IUpdateModel<TbRealtimeCheckRuleTemplate> {

    @Schema(description = "id")
    private Long id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    @Schema(description = "模板名称")
    private String name;

    /**
     * 类别
     */
    @NotBlank(message = "类别不能为空")
    @Schema(description = "类别")
    private String category;

    /**
     * 采样方式
     */
    @NotBlank(message = "采样方式不能为空")
    @Schema(description = "采样方式")
    private String sampleMethod;

    /**
     * 采样周期
     */
    @Schema(description = "采样周期")
    private Integer samplePeriod;

    /**
     * 计算规则
     */
    @NotBlank(message = "计算规则不能为空")
    @Schema(description = "计算规则")
    private String calculationRule;

    /**
     * 基准值类型
     */
    @Schema(description = "基准值类型")
    private String baseValueType;

    /**
     * 基准值
     */
    @Schema(description = "基准值")
    private Integer baseValueN;

    /**
     * 期望值
     */
    @Schema(description = "期望值")
    private List<ExpectedValue> expectedValueList;

    /**
     * 采样配置
     */
    @Schema(description = "模板拓展配置")
    private RuleTemplateExtraSetting extraSetting;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;


    @Override
    public TbRealtimeCheckRuleTemplate toEntity(TbRealtimeCheckRuleTemplate tbRealtimeCheckRuleTemplate) {
        TbRealtimeCheckRuleTemplate entity = IUpdateModel.super.toEntity(tbRealtimeCheckRuleTemplate);
        if (Objects.nonNull(this.getExtraSetting())) {
            entity.setExtraSetting(JsonUtil.encode(this.getExtraSetting()));
        }
        if (Objects.nonNull(this.getExpectedValueList())) {
            tbRealtimeCheckRuleTemplate.setExpectedValue(JsonUtil.encode(this.getExpectedValueList()));
        }
        return entity;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSampleMethod() {
        return sampleMethod;
    }

    public void setSampleMethod(String sampleMethod) {
        this.sampleMethod = sampleMethod;
    }

    public Integer getSamplePeriod() {
        return samplePeriod;
    }

    public void setSamplePeriod(Integer samplePeriod) {
        this.samplePeriod = samplePeriod;
    }

    public String getCalculationRule() {
        return calculationRule;
    }

    public void setCalculationRule(String calculationRule) {
        this.calculationRule = calculationRule;
    }

    public String getBaseValueType() {
        return baseValueType;
    }

    public void setBaseValueType(String baseValueType) {
        this.baseValueType = baseValueType;
    }

    public Integer getBaseValueN() {
        return baseValueN;
    }

    public void setBaseValueN(Integer baseValueN) {
        this.baseValueN = baseValueN;
    }

    public List<ExpectedValue> getExpectedValueList() {
        return expectedValueList;
    }

    public void setExpectedValueList(List<ExpectedValue> expectedValueList) {
        this.expectedValueList = expectedValueList;
    }

    public RuleTemplateExtraSetting getExtraSetting() {
        return extraSetting;
    }

    public void setExtraSetting(RuleTemplateExtraSetting extraSetting) {
        this.extraSetting = extraSetting;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
