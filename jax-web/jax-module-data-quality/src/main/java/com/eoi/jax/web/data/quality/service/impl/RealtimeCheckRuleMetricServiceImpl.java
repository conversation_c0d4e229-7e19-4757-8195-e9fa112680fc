package com.eoi.jax.web.data.quality.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.AlertKafkaConfig;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.integration.model.prometheus.MetricType;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryRangeMetric;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryRangeReq;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryRangeResp;
import com.eoi.jax.web.core.integration.service.PrometheusService;
import com.eoi.jax.web.core.util.JaxDateUtil;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleCalculationRuleEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleSampleMethodEnum;
import com.eoi.jax.web.data.quality.model.realtime.config.MetricCollectorConfigBuildContext;
import com.eoi.jax.web.data.quality.model.realtime.config.PqlContainer;
import com.eoi.jax.web.data.quality.model.realtime.metric.CalculateMetricResp;
import com.eoi.jax.web.data.quality.model.realtime.metric.PromRangeDataResp;
import com.eoi.jax.web.data.quality.model.realtime.metric.SampleMetricReq;
import com.eoi.jax.web.data.quality.model.realtime.metric.SampleMetricResp;
import com.eoi.jax.web.data.quality.plugin.sampler.RealTimeCheckTaskSamplerFactory;
import com.eoi.jax.web.data.quality.service.RealtimeCheckRuleMetricService;
import com.eoi.jax.web.ingestion.util.PromQueryUtils;
import com.eoi.jax.web.repository.entity.TbAlert;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckRule;
import com.eoi.jax.web.repository.service.TbAlertService;
import com.eoi.jax.web.repository.service.TbRealtimeCheckRuleService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Service
public class RealtimeCheckRuleMetricServiceImpl implements RealtimeCheckRuleMetricService {

    @Autowired
    private PrometheusService prometheusService;

    @Autowired
    private SystemConfigHolder systemConfigHolder;

    @Autowired
    private AlertKafkaConfig alertKafkaConfig;

    @Autowired
    private TbRealtimeCheckRuleService tbRealtimeCheckRuleService;

    @Autowired
    private TbAlertService tbAlertService;


    @Override
    public SampleMetricResp originalValue(SampleMetricReq sampleMetricReq) {
        final String promUrl = getPromUrl();
        PqlContainer pqlContainer = generatePql(sampleMetricReq);

        SampleMetricResp sampleMetricResp = new SampleMetricResp();
        TbRealtimeCheckRule tbRealtimeCheckRule = tbRealtimeCheckRuleService.getById(sampleMetricReq.getRuleId());
        MetricType type = getMetricType(tbRealtimeCheckRule);

        Pair<Long, List<PromRangeDataResp.Chart>> samplePair = queryRangeValuesAsResp(promUrl, pqlContainer.getSample(), sampleMetricReq);
        PromRangeDataResp sample = PromRangeDataResp.create("采样值", type,
                samplePair.getValue(), samplePair.getKey());
        sampleMetricResp.setSample(sample);
        if (StrUtil.isNotBlank(pqlContainer.getBase())) {
            Pair<Long, List<PromRangeDataResp.Chart>> basePair = queryRangeValuesAsResp(promUrl, pqlContainer.getBase(), sampleMetricReq);
            PromRangeDataResp base = PromRangeDataResp.create("基准值", type,
                    basePair.getValue(), basePair.getKey());
            sampleMetricResp.setBase(base);
        }
        return sampleMetricResp;
    }


    private MetricType getMetricType(TbRealtimeCheckRule tbRealtimeCheckRule) {
        MetricType type;
        if (StrUtil.equalsAny(tbRealtimeCheckRule.getSampleMethod(),
                RealtimeCheckRuleSampleMethodEnum.AVG_QUEUE_TIME_DELAY.code(),
                RealtimeCheckRuleSampleMethodEnum.AVG_BIZ_TIME_DELAY.code())) {
            type = MetricType.ShortTime;
        } else if (StrUtil.equalsAny(tbRealtimeCheckRule.getSampleMethod(),
                RealtimeCheckRuleSampleMethodEnum.MESSAGE_RATE.code())) {
            type = MetricType.ShortRate;
        } else if (StrUtil.equalsAny(tbRealtimeCheckRule.getSampleMethod(),
                RealtimeCheckRuleSampleMethodEnum.DATA_FREQUENCY_DEVIATION.code())) {
            type = MetricType.SecondTime;
        } else {
            type = MetricType.ShortNum;
        }
        return type;
    }


    @Override
    public CalculateMetricResp calculateValue(SampleMetricReq sampleMetricReq) {
        PqlContainer pqlContainer = generatePql(sampleMetricReq);

        String pql = pqlContainer.getValue().replaceAll("sample", String.format("(%s)", pqlContainer.getSample()));
        if (StrUtil.isNotBlank(pqlContainer.getBase())) {
            pql = pql.replaceAll("base", String.format("(%s)", pqlContainer.getBase()));
        }
        // 查询计算值
        Pair<Long, List<PromRangeDataResp.Chart>> pair = queryRangeValuesAsResp(getPromUrl(), pql, sampleMetricReq);

        // 判断单位
        TbRealtimeCheckRule tbRealtimeCheckRule = tbRealtimeCheckRuleService.getById(sampleMetricReq.getRuleId());
        MetricType type;
        if (StrUtil.equalsAny(tbRealtimeCheckRule.getCalculationRule(),
                RealtimeCheckRuleCalculationRuleEnum.SAMPLE_VALUE_AND_BASE_VALUE_VOLATILITY.code(),
                RealtimeCheckRuleCalculationRuleEnum.SAMPLE_VALUE_AND_BASE_VALUE_RATIO.code())) {
            type = MetricType.PercentType;
        } else {
            type = getMetricType(tbRealtimeCheckRule);
        }
        PromRangeDataResp promRangeDataResp = PromRangeDataResp.create("计算值", type,
                pair.getValue(), pair.getKey());

        CalculateMetricResp calculateMetricResp = new CalculateMetricResp();
        calculateMetricResp.setCalculateValue(promRangeDataResp);
        if (CollUtil.isEmpty(calculateMetricResp.getCalculateValue().getChartList())) {
            return calculateMetricResp;
        }

        // 查询告警点
        List<TbAlert> tbAlertList = queryAlert(sampleMetricReq);
        if (CollUtil.isEmpty(tbAlertList)) {
            return calculateMetricResp;
        }

        // 根据label分类告警信息
        Map<String, List<TbAlert>> tbAlertMap = tbAlertList.stream().collect(Collectors.groupingBy(it -> {
            Map<String, String> labelMap = JsonUtil.decode(it.getLabels(), new TypeReference<Map<String, String>>() {
            });
            return labelMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue)
                    .collect(Collectors.joining(","));
        }, Collectors.toList()));

        // 根据label分类图
        List<PromRangeDataResp.Chart> chartList = promRangeDataResp.getChartList();
        Map<String, PromRangeDataResp.Chart> chartMap = chartList.stream().collect(Collectors.toMap(it -> it.getLabel().entrySet().stream()
                .sorted(Map.Entry.comparingByKey()).map(a -> a.getValue().toString()).collect(Collectors.joining(",")), it -> it));

        for (Map.Entry<String, List<TbAlert>> entry : tbAlertMap.entrySet()) {
            PromRangeDataResp.Chart chart = chartMap.get(entry.getKey());
            if (Objects.isNull(chart)) {
                continue;
            }
            List<PromRangeDataResp.Coordinate> values = chart.getValues();
            // 告警点裁剪
            List<TbAlert> cutTbAlertList = cutTbAlertList(values.size(), pair.getKey(), entry.getValue());
            // 删除相同时间的点
            Set<Long> alertSet = cutTbAlertList.stream().map(it -> it.getFiredTime() / 1000).collect(Collectors.toSet());
            values.removeIf(it -> alertSet.contains(it.getX()));
            for (TbAlert tbAlert : cutTbAlertList) {
                PromRangeDataResp.Coordinate coordinate = new PromRangeDataResp.Coordinate();
                coordinate.setX(tbAlert.getFiredTime() / 1000);
                coordinate.setY(String.valueOf(tbAlert.getValue()));
                coordinate.setAlarm(tbAlert.getLevel());
                // 将告警点插入图中
                values.add(coordinate);
            }
            // 重新根据时间排序
            chart.setValues(values.stream().sorted(Comparator.comparing(PromRangeDataResp.Coordinate::getX))
                    .collect(Collectors.toList()));
            // 重新计算max和min
            chart.statistics();
        }
        return calculateMetricResp;
    }

    private List<TbAlert> queryAlert(SampleMetricReq sampleMetricReq) {
        // 查询告警点
        LambdaQueryWrapper<TbAlert> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TbAlert::getId, TbAlert::getFiredTime, TbAlert::getValue, TbAlert::getLabels, TbAlert::getLevel);
        queryWrapper.eq(Objects.nonNull(sampleMetricReq.getAlertId()), TbAlert::getId, sampleMetricReq.getAlertId());
        queryWrapper.eq(Objects.nonNull(sampleMetricReq.getPipelineId()), TbAlert::getPipelineId, sampleMetricReq.getPipelineId());
        queryWrapper.eq(Objects.nonNull(sampleMetricReq.getRuleId()), TbAlert::getRuleId, sampleMetricReq.getRuleId());
        queryWrapper.between(TbAlert::getFiredTime, JaxDateUtil.parseIso(sampleMetricReq.getBeginTime()).getTime(),
                JaxDateUtil.parseIso(sampleMetricReq.getEndTime()).getTime());
        List<TbAlert> resultList = new ArrayList<>();
        for (int i = 1; ; i++) {
            Page<TbAlert> page = tbAlertService.page(new Page<>(i, 5000, false), queryWrapper);
            if (CollUtil.isEmpty(page.getRecords())) {
                break;
            }
            resultList.addAll(page.getRecords());
        }
        return resultList;
    }


    /**
     * 当用户选择的时间跨度比较大的时候，会出现正常的点较少，而异常的告警点较多的情况，进而导致量国道，最终页面卡顿，故而需要根据情况裁剪告警点
     *
     * @param chatPointSize 正常数据点个数
     * @param step          步长
     * @param tbAlertList   异常告警数据
     * @return
     */
    private List<TbAlert> cutTbAlertList(int chatPointSize, Long step, List<TbAlert> tbAlertList) {
        if (CollUtil.isEmpty(tbAlertList)) {
            return tbAlertList;
        }
        // 异常告警点个数小于正常点个数，则跳过裁剪
        if (tbAlertList.size() <= chatPointSize) {
            return tbAlertList;
        }
        // 排序
        tbAlertList = tbAlertList.stream().sorted(Comparator.comparing(TbAlert::getFiredTime)).collect(Collectors.toList());
        long min = tbAlertList.get(0).getFiredTime();
        long max = tbAlertList.get(tbAlertList.size() - 1).getFiredTime();

        long startPoint = min;
        int index = 0;
        List<TbAlert> resultList = new ArrayList<>();
        while (startPoint < max) {
            long endPoint = startPoint + step * 1000;
            // 取步长中的第一个告警点
            resultList.add(tbAlertList.get(index));
            while (index < tbAlertList.size() && tbAlertList.get(index).getFiredTime() < endPoint) {
                // 推动index增长
                index++;
            }
            startPoint = endPoint;
        }

        return resultList;
    }


    private PqlContainer generatePql(SampleMetricReq sampleMetricReq) {
        TbRealtimeCheckRule tbRealtimeCheckRule = tbRealtimeCheckRuleService.getById(sampleMetricReq.getRuleId());
        MetricCollectorConfigBuildContext context = new MetricCollectorConfigBuildContext();
        context.setBuildMetricChart(true);
        context.setPipelineId(sampleMetricReq.getPipelineId());
        context.setFieldErrTypeList(sampleMetricReq.getFieldErrTypeList());
        context.setTbRealtimeCheckRule(tbRealtimeCheckRule);
        if (Objects.nonNull(sampleMetricReq.getAlertId())) {
            TbAlert tbAlert = tbAlertService.getById(sampleMetricReq.getAlertId());
            // 设置告警的label作为图的label
            context.setLabelMap(JsonUtil.decode(tbAlert.getLabels(), new TypeReference<LinkedHashMap<String, String>>() {
            }));
        }
        return RealTimeCheckTaskSamplerFactory.buildPql(context);
    }


    private Pair<Long, List<PromRangeDataResp.Chart>> queryRangeValuesAsResp(String promUrl, String qpl, SampleMetricReq sampleMetricReq) {
        PrometheusQueryRangeReq rangeParams = PromQueryUtils.createRangeReq(qpl,
                parseIsoAsTimeSec(sampleMetricReq.getBeginTime()),
                parseIsoAsTimeSec(sampleMetricReq.getEndTime()),
                sampleMetricReq.getWidth());
        PrometheusQueryRangeResp resp = prometheusService.queryRange(rangeParams, promUrl);
        LinkedList<PrometheusQueryRangeMetric> result = resp.getResult();
        if (CollUtil.isEmpty(result)) {
            return new Pair<>(rangeParams.getStep(), null);
        }

        return new Pair<>(rangeParams.getStep(), result.stream().map(it -> {
            PromRangeDataResp.Chart chart = new PromRangeDataResp.Chart();
            chart.setLabel(it.getMetric());
            LinkedList<Object[]> values = it.getValues();

            List<PromRangeDataResp.Coordinate> coordinateList = values.stream().map(v -> {
                Optional<Object[]> vOptional = Optional.ofNullable(v);
                PromRangeDataResp.Coordinate coordinate = new PromRangeDataResp.Coordinate();
                coordinate.setX(Long.valueOf(vOptional.map(a -> a[0]).get().toString()));
                coordinate.setY((String) vOptional.map(a -> a[1]).get());
                return coordinate;
            }).filter(y -> NumberUtil.isNumber(y.getY())).collect(Collectors.toList());
            chart.setValues(coordinateList);
            chart.statistics();
            return chart;
        }).collect(Collectors.toList()));
    }


    private String getPromUrl() {
        String vmUrl = systemConfigHolder.getStr(SystemConfigEnum.JAX_METRICS.getKey(), "address");
        String usedUrl = StrUtil.isNotBlank(vmUrl) ? vmUrl : alertKafkaConfig.getVmUrl();
        if (StrUtil.isBlank(usedUrl)) {
            throw new BizException(ResponseCode.FAILED.getCode(), "请在'系统管理->基础配置'配置VictoriaMetrics采集器");
        }
        URL url = URLUtil.url(usedUrl);
        return url.getProtocol() + "://" + url.getHost() + ":" + url.getPort();
    }


    private Long parseIsoAsTimeSec(String isoTimeStr) {
        Assert.notBlank(isoTimeStr, "isoTimeStr时间不能为空");
        return JaxDateUtil.parseIso(isoTimeStr).getTime() / 1000;
    }


}
