package com.eoi.jax.web.data.quality.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;
import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;

/**
 * <AUTHOR>
 * @date 2023/5/9
 */
public enum RealtimeCheckRuleBaseValueEnum implements ICodeEnum, ICodeMessageEnum {
    /**
     * N个周期前值(同比)
     */
    N_PERIOD_VALUE("N_PERIOD_VALUE", "N个周期前值(同比)"),
    /**
     * 前N周期平均值(移动平均)
     */
    AVG_N_PERIOD_VALUE("AVG_N_PERIOD_VALUE", "前N周期平均值(移动平均)"),
    /**
     * 周期内消息条数(不包括规则代码)
     */
    PERIOD_MESSAGE_NUMBER_EXCLUDE_FILTER("PERIOD_MESSAGE_NUMBER_EXCLUDE_FILTER", "周期内消息条数(不包括过滤条件)"),
    /**
     * 周期内消息条数(包括规则代码)
     */
    PERIOD_MESSAGE_NUMBER_INCLUDE_FILTER("PERIOD_MESSAGE_NUMBER_INCLUDE_FILTER", "周期内消息条数(包括过滤条件)");

    private final String code;

    private final String message;

    RealtimeCheckRuleBaseValueEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static RealtimeCheckRuleBaseValueEnum fromString(String code) {
        for (RealtimeCheckRuleBaseValueEnum value : RealtimeCheckRuleBaseValueEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        for (RealtimeCheckRuleBaseValueEnum value : RealtimeCheckRuleBaseValueEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }
    public static String getCodeByMessage(String code) {
        for (RealtimeCheckRuleBaseValueEnum value : RealtimeCheckRuleBaseValueEnum.values()) {
            if (value.message.equals(code)) {
                return value.code;
            }
        }
        return null;
    }


}
