package com.eoi.jax.web.data.quality.model.realtime.metric;

import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public class SampleMetricReq {

    @NotNull(message = "检测任务Id不能为空")
    @Schema(description = "检测任务Id")
    private Long id;

    @NotNull(message = "检测规则Id")
    @Schema(description = "检测规则Id")
    private Long ruleId;

    @NotNull(message = "管线作业Id不能为空")
    private Long pipelineId;

    @Schema(description = "告警id")
    private Long alertId;

    @Schema(description = "字段异常类型列")
    private List<String> fieldErrTypeList;

    @Schema(description = "开始时间,格式：yyyy-MM-dd'T'HH:mm:ss.SSSZZZ")
    @NotBlank(message = "开始时间不能为空")
    private String beginTime;

    @Schema(description = "结束时间,格式：yyyy-MM-dd'T'HH:mm:ss.SSSZZZ")
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    @Schema(description = "图表组件宽度，单位：px")
    @NotNull(message = "图表组件宽度不能为空")
    @Min(value = 1, message = "图表组件宽度必须大于等于1")
    private Integer width;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public Long getAlertId() {
        return alertId;
    }

    public void setAlertId(Long alertId) {
        this.alertId = alertId;
    }

    public List<String> getFieldErrTypeList() {
        return fieldErrTypeList;
    }

    public void setFieldErrTypeList(List<String> fieldErrTypeList) {
        this.fieldErrTypeList = fieldErrTypeList;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }
}
