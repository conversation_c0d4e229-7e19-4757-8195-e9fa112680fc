package com.eoi.jax.web.data.quality.model.realtime.config;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
public class PqlContainer {

    private String sample;

    private String base;

    private String value;

    private List<String> ignoreLabels;

    public String getSample() {
        return sample;
    }

    public void setSample(String sample) {
        this.sample = sample;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<String> getIgnoreLabels() {
        return ignoreLabels;
    }

    public void setIgnoreLabels(List<String> ignoreLabels) {
        this.ignoreLabels = ignoreLabels;
    }
}
