package com.eoi.jax.web.data.quality.model.alert;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.util.JaxDateUtil;
import com.eoi.jax.web.repository.entity.TbAlert;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class AlertQueryFilterReq implements IFilterReq<TbAlert> {

    @Schema(name = "content", example = "告警内容")
    private String content;

    @Schema(name = "subject", example = "类型")
    private String subject;

    @Schema(name = "ruleTemplateId", example = "规则模型Id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long ruleTemplateId;

    @Schema(name = "ruleId", example = "规则Id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long ruleId;
    @Schema(name = "taskId", example = "任务Id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long taskId;
    @Schema(name = "pipelineId", description = "作业Id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long pipelineId;
    @Schema(name = "tableId", description = "模型Id")
    private Long tableId;

    @Schema(name = "status", description = "告警状态", title = "告警状态", type = "String", example = "告警状态:AlertStatusEnum")
    private String status;
    @Schema(name = "warnLevel", description = "告警等级", title = "告警等级", type = "String", example = "告警等级:AlertLevelEnum")
    private String warnLevel;
    @Schema(name = "limitPipeline", example = "只显示附加作业异常告警")
    private Boolean limitPipeline;

    @Schema(name = "limitTable", example = "显示有模型的异常告警")
    private Boolean limitTable;

    @Schema(name = "time", example = "告警激活时间[\"2023-06-02T00:00:14.870+0800\",\"2023-06-02T00:00:14.870+0800\"]")
    private List<String> time;

    public List<String> getTime() {
        return time;
    }


    /**
     * 请求时间
     */
    @Schema(hidden = true)
    private Long startTime;
    @Schema(hidden = true)
    private Long endTime;

    public void setTime(List<String> time) {
        this.time = time;
        if (time.size() == 2) {
            if (this.startTime == null) {
                this.startTime = JaxDateUtil.parseIso(time.get(0)).getTime();
            }

            if (this.endTime == null) {
                this.endTime = JaxDateUtil.parseIso(time.get(1)).getTime();
            }
        }
    }

    public Long getStartTime() {
        return startTime == null ?
                DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd").getTime()
                : startTime;
    }


    public Long getEndTime() {
        return endTime == null ? System.currentTimeMillis() : endTime;
    }


    @Override
    public QueryWrapper<TbAlert> where(QueryWrapper<TbAlert> wrapper) {
        wrapper.lambda().like(StringUtils.isNotBlank(content), TbAlert::getContent, content);
        wrapper.lambda().like(StringUtils.isNotBlank(subject), TbAlert::getSubject, subject);
        wrapper.lambda().eq(StringUtils.isNotBlank(status), TbAlert::getStatus, status);
        wrapper.lambda().eq(ruleId != null, TbAlert::getRuleId, ruleId);
        wrapper.lambda().eq(pipelineId != null, TbAlert::getPipelineId, pipelineId);
        return wrapper;
    }


    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getLimitPipeline() {
        return limitPipeline;
    }

    public void setLimitPipeline(Boolean limitPipeline) {
        this.limitPipeline = limitPipeline;
    }

    public Boolean getLimitTable() {
        return limitTable;
    }

    public void setLimitTable(Boolean limitTable) {
        this.limitTable = limitTable;
    }

    public Long getRuleTemplateId() {
        return ruleTemplateId;
    }

    public void setRuleTemplateId(Long ruleTemplateId) {
        this.ruleTemplateId = ruleTemplateId;
    }

    public String getWarnLevel() {
        return warnLevel;
    }

    public void setWarnLevel(String warnLevel) {
        this.warnLevel = warnLevel;
    }

    public Long getTableId() {
        return tableId;
    }

    public void setTableId(Long tableId) {
        this.tableId = tableId;
    }
}
