package com.eoi.jax.web.data.quality.model.alert;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.repository.entity.TbAlert;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class AlertResp implements IRespModel<TbAlert> {
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "告警key")
    private String alertKey;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "规则id")
    private Long ruleId;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "告警等级")
    private String level;

    @Schema(description = "告警主题")
    private String subject;

    @Schema(description = "告警内容")
    private String content;

    @Schema(description = "标签")
    private String labels;

    @Schema(description = "附加信息")
    private String annotations;

    @Schema(description = "状态")
    private String status;
    @Schema(description = "模型名称")
    private String tableName;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "作业id")
    private Long pipelineId;

    @Schema(description = "作业名称")
    private String pipelineName;

    @Schema(description = "触发时间")
    private Long activeTime;

    @Schema(description = "激活时间")
    private Long firedTime;

    @Schema(description = "处理时间")
    private Long resolvedTime;

    @Schema(description = "最后发送时间")
    private Long lastSendTime;

    @Schema(description = "触发条件", hidden = true)
    private String triggerCondition;

    @Schema(description = "基础值")
    private Double base;

    @Schema(description = "采样值")
    private Double sample;

    @Schema(description = "结果值")
    private Double value;

    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "模型数量")
    private Integer tableNum;

    @Override
    public AlertResp fromEntity(TbAlert table) {
        IRespModel.super.fromEntity(table);
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getAlertKey() {
        return alertKey;
    }

    public void setAlertKey(String alertKey) {
        this.alertKey = alertKey;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getLabels() {
        return labels;
    }

    public void setLabels(String labels) {
        this.labels = labels;
    }

    public String getAnnotations() {
        return annotations;
    }

    public void setAnnotations(String annotations) {
        this.annotations = annotations;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public Long getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Long activeTime) {
        this.activeTime = activeTime;
    }

    public Long getFiredTime() {
        return firedTime;
    }

    public void setFiredTime(Long firedTime) {
        this.firedTime = firedTime;
    }

    public Long getResolvedTime() {
        return resolvedTime;
    }

    public void setResolvedTime(Long resolvedTime) {
        this.resolvedTime = resolvedTime;
    }

    public Long getLastSendTime() {
        return lastSendTime;
    }

    public void setLastSendTime(Long lastSendTime) {
        this.lastSendTime = lastSendTime;
    }

    public Double getBase() {
        return base;
    }

    public void setBase(Double base) {
        this.base = base;
    }

    public Double getSample() {
        return sample;
    }

    public void setSample(Double sample) {
        this.sample = sample;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return null;
    }

    @Override
    public void setCreateUser(Long createUser) {

    }

    @Override
    public Long getUpdateUser() {
        return null;
    }

    @Override
    public void setUpdateUser(Long updateUser) {

    }

    public Integer getTableNum() {
        return tableNum;
    }

    public void setTableNum(Integer tableNum) {
        this.tableNum = tableNum;
    }

    public String getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(String triggerCondition) {
        this.triggerCondition = triggerCondition;
    }
}
