package com.eoi.jax.web.data.quality.service;

import com.eoi.jax.web.repository.entity.*;

import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/7/14
 * @Desc:
 **/
public interface MemoryCacheService {
    /**
     * 获取管线作业缓存列表
     * @return
     */
    Map<Long, TbPipeline> getPipelineMapCache();

    /**
     * 获取校验规则缓存列表
     * @return
     */
    Map<Long, TbRealtimeCheckRule> getRealtimeCheckRuleMapCache();


    /**
     * 获取校验任务缓存列表
     * @return
     */
    Map<Long, TbRealtimeCheckTask> getRealtimeCheckTaskMapCache();

    /**
     * 获取校验规则模板缓存列表
     * @return
     */

    Map<Long, TbRealtimeCheckRuleTemplate> getRealtimeCheckRuleTemplateMapCache();

    /**
     * 获取模型缓存列表
     * @return
     */

    Map<Long, TbTable> getTableMapCache();
}
