package com.eoi.jax.web.data.quality.model.alert;

import com.eoi.jax.web.repository.search.result.SelectOption;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/6/5
 * @Desc: 告警列表统计信息
 **/
public class QueryStatisticsResp {
    @Schema(name = "standalonePipleline", example = "非独立作业数量")
    private Integer standalonePipleline;

    @Schema(name = "tableNum", example = "模型数量")
    private Integer tableNum;

    @Schema(name = "pipelineList", example = "附加作业列表")
    private List<SelectOption> pipelineList;
    @Schema(name = "tableNum", example = "模型列表")
    private List<SelectOption> tableList;
    @Schema(name = "criticalNum", example = "重要告警数量")
    private Integer criticalNum;
    @Schema(name = "warningNum", example = "警告告警数量")
    private Integer warningNum;
    @Schema(name = "infoNum", example = "信息告警数量")
    private Integer infoNum;

    public Integer getStandalonePipleline() {
        return standalonePipleline;
    }

    public void setStandalonePipleline(Integer standalonePipleline) {
        this.standalonePipleline = standalonePipleline;
    }

    public Integer getTableNum() {
        return tableNum;
    }

    public void setTableNum(Integer tableNum) {
        this.tableNum = tableNum;
    }

    public Integer getCriticalNum() {
        return criticalNum;
    }

    public void setCriticalNum(Integer criticalNum) {
        this.criticalNum = criticalNum;
    }

    public Integer getWarningNum() {
        return warningNum;
    }

    public void setWarningNum(Integer warningNum) {
        this.warningNum = warningNum;
    }

    public Integer getInfoNum() {
        return infoNum;
    }

    public void setInfoNum(Integer infoNum) {
        this.infoNum = infoNum;
    }

    public List<SelectOption> getPipelineList() {
        return pipelineList;
    }

    public void setPipelineList(List<SelectOption> pipelineList) {
        this.pipelineList = pipelineList;
    }

    public List<SelectOption> getTableList() {
        return tableList;
    }

    public void setTableList(List<SelectOption> tableList) {
        this.tableList = tableList;
    }
}
