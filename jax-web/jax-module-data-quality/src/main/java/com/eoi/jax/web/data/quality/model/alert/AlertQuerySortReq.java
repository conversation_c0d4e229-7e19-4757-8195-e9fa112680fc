package com.eoi.jax.web.data.quality.model.alert;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbAlert;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class AlertQuerySortReq implements ISortReq<TbAlert> {
    private String activeTime;
    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbAlert> order(QueryWrapper<TbAlert> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbAlert::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbAlert::getUpdateTime)
                .orderBy(isOrder(activeTime), isAsc(activeTime), TbAlert::getActiveTime);
        return wrapper;
    }

    public String getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(String activeTime) {
        this.activeTime = activeTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
