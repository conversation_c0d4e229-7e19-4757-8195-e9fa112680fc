package com.eoi.jax.web.data.quality.model.realtime.config;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
public class TimeBackCountMetricCollectorConfig extends BaseMetricCollectorConfig {

    private String timeField;

    private String timeFormat;

    public String getTimeField() {
        return timeField;
    }

    public void setTimeField(String timeField) {
        this.timeField = timeField;
    }

    public String getTimeFormat() {
        return timeFormat;
    }

    public void setTimeFormat(String timeFormat) {
        this.timeFormat = timeFormat;
    }

}
