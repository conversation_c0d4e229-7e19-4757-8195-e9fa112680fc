package com.eoi.jax.web.data.quality.plugin.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleBaseValueEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckRuleCalculationRuleEnum;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckTaskRunModeEnum;
import com.eoi.jax.web.data.quality.model.realtime.config.BaseMetricCollectorConfig;
import com.eoi.jax.web.data.quality.model.realtime.config.DataInvalidCountMetricCollectorConfig;
import com.eoi.jax.web.data.quality.model.realtime.config.MetricCollectorConfigBuildContext;
import com.eoi.jax.web.data.quality.model.realtime.config.PqlContainer;
import com.eoi.jax.web.data.quality.model.realtime.rule.RuleExtraSetting;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineEdge;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineJob;
import com.eoi.jax.web.ingestion.plugin.pipeline.ModelJobPipelinePluginImpl;
import com.eoi.jax.web.ingestion.provider.manager.PipelineCtx;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.service.TbColumnDictService;
import com.eoi.jax.web.repository.service.TbEnumValueService;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
public class FieldSpecificationExceptionIncrementSampler extends BaseRealTimeCheckTaskSampler {


    @Override
    public List<BaseMetricCollectorConfig> buildMonitorOperatorConfig(MetricCollectorConfigBuildContext context) {
        List<BaseMetricCollectorConfig> metricCollectorConfigList = CollUtil.newArrayList(buildDataInvalidCount(context));

        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        if (RealtimeCheckRuleCalculationRuleEnum.SAMPLE_VALUE_AND_BASE_VALUE_RATIO.code()
                .equals(tbRealtimeCheckRule.getCalculationRule())) {
            metricCollectorConfigList.add(buildMessageTotal(context));
        }
        return metricCollectorConfigList;
    }

    @Override
    protected PqlContainer buildSampleValuePql(MetricCollectorConfigBuildContext context) {
        PqlContainer pqlContainer = new PqlContainer();
        pqlContainer.setSample(getSampleValuePql(context));
        pqlContainer.setBase(null);
        pqlContainer.setValue("sample");
        return pqlContainer;
    }

    @Override
    protected PqlContainer buildBaseValueVolatilityPql(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        PqlContainer pqlContainer = new PqlContainer();
        pqlContainer.setSample(getSampleValuePql(context));
        pqlContainer.setValue("(sample-base)*100 / base");
        if (RealtimeCheckRuleBaseValueEnum.N_PERIOD_VALUE.code().equals(tbRealtimeCheckRule.getBaseValueType())) {
            pqlContainer.setBase(getBaseValuePeriodPql(context));
        } else if (RealtimeCheckRuleBaseValueEnum.AVG_N_PERIOD_VALUE.code().equals(tbRealtimeCheckRule.getBaseValueType())) {
            pqlContainer.setBase(getBaseValueAvgPeriodPql(context));
        } else {
            throw new BizException(ResponseCode.FAILED.getCode(), StrUtil.format("计算规则:{}不支持该基准值计算方式:{}",
                    RealtimeCheckRuleCalculationRuleEnum.SAMPLE_VALUE_AND_BASE_VALUE_RATIO.message(),
                    tbRealtimeCheckRule.getBaseValueType()));
        }
        return pqlContainer;
    }

    @Override
    protected PqlContainer buildBaseValueRatioPql(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        PqlContainer pqlContainer = new PqlContainer();
        pqlContainer.setSample(getSampleValuePql(context));
        pqlContainer.setValue("sample * 100 / base");
        if (RealtimeCheckRuleBaseValueEnum.N_PERIOD_VALUE.code().equals(tbRealtimeCheckRule.getBaseValueType())) {
            pqlContainer.setBase(getBaseValuePeriodPql(context));
        } else if (RealtimeCheckRuleBaseValueEnum.AVG_N_PERIOD_VALUE.code().equals(tbRealtimeCheckRule.getBaseValueType())) {
            pqlContainer.setBase(getBaseValueAvgPeriodPql(context));
        } else if (RealtimeCheckRuleBaseValueEnum.PERIOD_MESSAGE_NUMBER_INCLUDE_FILTER.code()
                .equals(tbRealtimeCheckRule.getBaseValueType())) {
            // 此处的sample和base的label标签不一致，需要通知告警引擎忽略
            pqlContainer.setIgnoreLabels(CollUtil.newArrayList("jax_error_code"));
            if (context.getBuildMetricChart()) {
                // 查看指标图，添加ignoring(jax_error_code)，忽略label不一致的问题
                pqlContainer.setValue("sample * 100 / ignoring(jax_error_code) base");
            }
            pqlContainer.setBase(getBaseValueIncludePql(context));
        } else {
            throw new BizException(ResponseCode.FAILED.getCode(), StrUtil.format("计算规则:{}不支持该基准值计算方式:{}",
                    RealtimeCheckRuleCalculationRuleEnum.SAMPLE_VALUE_AND_BASE_VALUE_RATIO.message(),
                    tbRealtimeCheckRule.getBaseValueType()));
        }
        return pqlContainer;
    }

    @Override
    protected PqlContainer buildBaseValueDifferencePql(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        PqlContainer pqlContainer = new PqlContainer();
        pqlContainer.setSample(getSampleValuePql(context));
        pqlContainer.setValue("sample - base");
        if (RealtimeCheckRuleBaseValueEnum.N_PERIOD_VALUE.code().equals(tbRealtimeCheckRule.getBaseValueType())) {
            pqlContainer.setBase(getBaseValuePeriodPql(context));
        } else if (RealtimeCheckRuleBaseValueEnum.AVG_N_PERIOD_VALUE.code().equals(tbRealtimeCheckRule.getBaseValueType())) {
            pqlContainer.setBase(getBaseValueAvgPeriodPql(context));
        } else {
            throw new BizException(ResponseCode.FAILED.getCode(), StrUtil.format("计算规则:{}不支持该基准值计算方式:{}",
                    RealtimeCheckRuleCalculationRuleEnum.SAMPLE_VALUE_AND_BASE_VALUE_DIFFERENCE.message(),
                    tbRealtimeCheckRule.getBaseValueType()));
        }
        return pqlContainer;
    }

    /**
     * 构建pql语句的lable值
     *
     * @param context
     * @return
     */
    @Override
    protected String buildPqlLabel(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        if (context.getBuildMetricChart()) {
            Map<String, String> labelMap = CollUtil.isNotEmpty(context.getLabelMap()) ? context.getLabelMap() :
                    new LinkedHashMap<>();
            labelMap.put("jax_rule_id", tbRealtimeCheckRule.getId().toString());
            labelMap.put("jax_pipeline_id", context.getPipelineId().toString());
            labelMap.put("jax_error_code", CollUtil.isEmpty(context.getFieldErrTypeList()) ? "ERROR" :
                    String.join("|", context.getFieldErrTypeList()));
            return labelMap.entrySet().stream().map(it -> String.format("%s='%s'", it.getKey(), it.getValue()))
                    .collect(Collectors.joining(","));
        } else {
            RuleExtraSetting ruleExtraSetting = JsonUtil.decode(tbRealtimeCheckRule.getExtraSetting(),
                    new TypeReference<RuleExtraSetting>() {
                    });
            return String.format("jax_rule_id='%s',jax_error_code=~'%s'", tbRealtimeCheckRule.getId(),
                    "no-distinction".equals(ruleExtraSetting.getStatisticalMethod()) ?
                            "ERROR" : String.join("|", ruleExtraSetting.getFieldErrTypeList()));
        }
    }

    /**
     * 获取pql格式分组字段
     *
     * @param context@return
     */
    @Override
    public String getGroupNameStr(MetricCollectorConfigBuildContext context) {
        if (context.getBuildMetricChart() && CollUtil.isNotEmpty(context.getLabelMap())) {
            // 告警列表查看指标时的场景
            return String.join(",", context.getLabelMap().keySet());
        }

        List<String> list = CollUtil.newArrayList("jax_rule_id", "jax_pipeline_id", "jax_error_code");
        Map<String, String> groupBysMap = buildGroupBys(context);
        list.addAll(groupBysMap.values());
        return String.join(",", list);
    }

    private String getSampleValuePql(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        return String.format("sum by(%s) " +
                        "(delta(flink_taskmanager_job_task_operator_jax_data_invalid_count_%s{%s}[%sm]))",
                getGroupNameStr(context), tbRealtimeCheckRule.getId(),
                buildPqlLabel(context), tbRealtimeCheckRule.getSamplePeriod());
    }

    private String getBaseValueIncludePql(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        return String.format("sum by(%s) " +
                        "(delta(flink_taskmanager_job_task_operator_jax_message_total_%s{%s}[%sm]))",
                getMessageTotalGroupNameStr(context), tbRealtimeCheckRule.getId(),
                buildMessageTotalPqlLabel(context), tbRealtimeCheckRule.getSamplePeriod());
    }

    /**
     * 构建message_total专有的GroupName
     * @param context
     * @return
     */
    private String getMessageTotalGroupNameStr(MetricCollectorConfigBuildContext context) {
        if (context.getBuildMetricChart() && CollUtil.isNotEmpty(context.getLabelMap())) {
            context.getLabelMap().remove("jax_error_code");
            // 告警列表查看指标时的场景
            return String.join(",", context.getLabelMap().keySet());
        }

        List<String> list = CollUtil.newArrayList("jax_rule_id", "jax_pipeline_id");
        Map<String, String> groupBysMap = buildGroupBys(context);
        list.addAll(groupBysMap.values());
        return String.join(",", list);
    }

    /**
     * 构建message_total专有的PqlLabel
     * @param context
     * @return
     */
    protected String buildMessageTotalPqlLabel(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        if (context.getBuildMetricChart()) {
            Map<String, String> labelMap = CollUtil.isNotEmpty(context.getLabelMap()) ? context.getLabelMap() :
                    new LinkedHashMap<>();
            labelMap.remove("jax_error_code");
            labelMap.put("jax_rule_id", tbRealtimeCheckRule.getId().toString());
            labelMap.put("jax_pipeline_id", context.getPipelineId().toString());
            return labelMap.entrySet().stream().map(it -> String.format("%s='%s'", it.getKey(), it.getValue()))
                    .collect(Collectors.joining(","));
        } else {
            return String.format("jax_rule_id='%s'", tbRealtimeCheckRule.getId());
        }
    }

    private String getBaseValuePeriodPql(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        return String.format("sum by(%s) " +
                        "(delta(flink_taskmanager_job_task_operator_jax_data_invalid_count_%s{%s}[%sm] " +
                        " offset %sm))",
                getGroupNameStr(context), tbRealtimeCheckRule.getId(),
                buildPqlLabel(context),
                tbRealtimeCheckRule.getSamplePeriod(),
                tbRealtimeCheckRule.getSamplePeriod() * tbRealtimeCheckRule.getBaseValueN());
    }

    private String getBaseValueAvgPeriodPql(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        return String.format("avg_over_time( (sum by(%s)  " +
                        "(delta(flink_taskmanager_job_task_operator_jax_data_invalid_count_%s{%s}[%sm])))" +
                        " [%sm:%sm] offset %sm )",
                getGroupNameStr(context), tbRealtimeCheckRule.getId(), buildPqlLabel(context),
                tbRealtimeCheckRule.getSamplePeriod(), tbRealtimeCheckRule.getSamplePeriod() * tbRealtimeCheckRule.getBaseValueN(),
                tbRealtimeCheckRule.getSamplePeriod(), tbRealtimeCheckRule.getSamplePeriod());
    }

    private static Map<String, BiConsumer<TripleTuple, DataInvalidCountMetricCollectorConfig.FieldValidate>> fieldValidateFnMap;


    public DataInvalidCountMetricCollectorConfig buildDataInvalidCount(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        DataInvalidCountMetricCollectorConfig metricsCollectorConfig = new DataInvalidCountMetricCollectorConfig();
        metricsCollectorConfig.setFunction("data_invalid_count");
        metricsCollectorConfig.setName(String.format("jax_data_invalid_count_%s", tbRealtimeCheckRule.getId()));
        metricsCollectorConfig.setFilter(buildFilter(context));
        metricsCollectorConfig.setGroupBys(buildGroupBys(context));
        metricsCollectorConfig.setLabels(buildLabel(context));

        RuleExtraSetting ruleExtraSetting = JsonUtil.decode(tbRealtimeCheckRule.getExtraSetting(), new TypeReference<RuleExtraSetting>() {
        });

        // 用户配置的检测方法
        List<BiConsumer<TripleTuple, DataInvalidCountMetricCollectorConfig.FieldValidate>> fieldValidateFnList =
                getFieldValidateFnMap(ruleExtraSetting.getFieldErrTypeList());

        JaxRepository jaxRepository = ContextHolder.getBean(JaxRepository.class);
        // 根据模型id找到已发布的kafka模型
        TbTableDeploy lastSuccessTableDeploy = jaxRepository.tableDeployService()
                .getLastSuccessTableDeploy(findModelId(context, ruleExtraSetting));
        // 找到模型发布的字段
        List<TbColumnDeploy> tbColumnDeployList = jaxRepository.columnDeployService().getByTableDeployId(lastSuccessTableDeploy.getId());

        TbColumnDictService tbColumnDictService = jaxRepository.columnDict();
        TbEnumValueService tbEnumValueService = jaxRepository.enumValue();
        List<DataInvalidCountMetricCollectorConfig.FieldValidate> fieldValidateList = tbColumnDeployList.stream().filter(it -> {
            if (CollUtil.isNotEmpty(ruleExtraSetting.getBlackList()) && ruleExtraSetting.getBlackList().contains(it.getColName())) {
                // 列名称在黑名单中，不检测
                return false;
            }
            if (CollUtil.isNotEmpty(ruleExtraSetting.getWhiteList())) {
                // 白名单不为空，只检测白名单中的列
                return ruleExtraSetting.getWhiteList().contains(it.getColName());
            } else {
                return true;
            }
        }).map(it -> {
            TripleTuple tripleTuple = new TripleTuple();
            tripleTuple.setTbColumnDeploy(it);
            if (Objects.nonNull(it.getDicId())) {
                // 获取字段标准
                TbColumnDict tbColumnDict = tbColumnDictService.getById(it.getDicId());
                tripleTuple.setTbColumnDict(tbColumnDict);
                if (Objects.nonNull(tbColumnDict.getEnumId())) {
                    // 获取字段枚举列表
                    tripleTuple.setTbEnumValueList(tbEnumValueService.selectByEnumId(tbColumnDict.getEnumId()));
                }
            }

            final DataInvalidCountMetricCollectorConfig.FieldValidate fieldValidate =
                    new DataInvalidCountMetricCollectorConfig.FieldValidate();
            fieldValidate.setValidateField(it.getColName());
            for (BiConsumer<TripleTuple, DataInvalidCountMetricCollectorConfig.FieldValidate> consumer : fieldValidateFnList) {
                consumer.accept(tripleTuple, fieldValidate);
            }
            return fieldValidate;
        }).collect(Collectors.toList());

        DataInvalidCountMetricCollectorConfig.ValidateConfig validateConfig = new DataInvalidCountMetricCollectorConfig.ValidateConfig();
        validateConfig.setCollectByErrorAggregate(true);
        validateConfig.setCollectByErrorErrorCode(true);
        validateConfig.setFieldValidates(fieldValidateList);
        metricsCollectorConfig.setValidateConfig(validateConfig);
        return metricsCollectorConfig;
    }

    /**
     * 构建jax_message_total
     *
     * @return
     */
    public BaseMetricCollectorConfig buildMessageTotal(MetricCollectorConfigBuildContext context) {
        TbRealtimeCheckRule tbRealtimeCheckRule = context.getTbRealtimeCheckRule();
        BaseMetricCollectorConfig baseMetricCollectorConfig = new BaseMetricCollectorConfig();
        baseMetricCollectorConfig.setFunction("message_count");
        baseMetricCollectorConfig.setName(String.format("jax_message_total_%s", tbRealtimeCheckRule.getId()));
        baseMetricCollectorConfig.setGroupBys(buildGroupBys(context));
        baseMetricCollectorConfig.setLabels(buildLabel(context));
        baseMetricCollectorConfig.setFilter(buildFilter(context));
        return baseMetricCollectorConfig;
    }

    /**
     * 找到模型
     */
    private Long findModelId(MetricCollectorConfigBuildContext context, RuleExtraSetting ruleExtraSetting) {
        TbRealtimeCheckTask tbRealtimeCheckTask = context.getTbRealtimeCheckTask();
        if (RealtimeCheckTaskRunModeEnum.STANDALONE.code().equals(tbRealtimeCheckTask.getRunMode())) {
            // 独立作业
            return tbRealtimeCheckTask.getTbId();
        }

        PipelineCtx pipelineCtx = context.getPipelineCtx();
        TbPipeline tbPipeline = pipelineCtx.getPipeline();
        final Map<String, Object> pipelineUiMap = JsonUtil.decode(tbPipeline.getPipelineUi(), new TypeReference<Map<String, Object>>() {
        });

        PipelineConfig pipelineConfig = pipelineCtx.getPipelineConfig();
        // 将pipelineUi上的display放到PipelineJob中
        Map<String, PipelineJob> pipelineJobMap = pipelineConfig.getJobs().stream().map(it -> {
            // 复制对象，不修改原始的PipelineJob
            PipelineJob copyPipelineJob = ModelBeanUtil.copyBean(it, new PipelineJob());
            if (StrUtil.isBlank(it.getJobDisplay())) {
                //noinspection unchecked
                Map<String, Object> uiMap = (Map<String, Object>) pipelineUiMap.getOrDefault(it.getJobId(), MapUtil.newHashMap());
                copyPipelineJob.setJobDisplay((String) uiMap.get("display"));
            }
            return copyPipelineJob;
        }).collect(Collectors.toMap(PipelineJob::getJobId, it -> it));

        // 找到被挂载的算子
        PipelineJob mountedPipelineJob = pipelineJobMap.values().stream()
                .filter(it -> it.getJobName().equals(tbRealtimeCheckTask.getJobName())
                        && it.getJobDisplay().equals(tbRealtimeCheckTask.getJobDisplay())).findFirst().orElse(null);
        if (Objects.isNull(mountedPipelineJob)) {
            throw new BizException(ResponseCode.FAILED.getCode(), "管线作业中找不到要被挂载的算子");
        }

        // 查找模型算子
        List<PipelineJob> modelJobList = findModelJob(mountedPipelineJob.getJobId(), pipelineJobMap,
                pipelineConfig.getEdges(), ruleExtraSetting.getModelSelect());
        String modelSelect = "up".equals(ruleExtraSetting.getModelSelect()) ? "相邻向上模型" : "相邻向下模型";
        if (CollUtil.isEmpty(modelJobList)) {
            throw new BizException(ResponseCode.FAILED.getCode(), StrUtil.format("根据{}算法找不到模型算子", modelSelect));
        } else if (modelJobList.size() > 1) {
            throw new BizException(ResponseCode.FAILED.getCode(), StrUtil.format("根据{}算法中找到多个模型算子, 无法确定使用哪个模型{}",
                    modelSelect, JsonUtil.encode(modelJobList.stream().map(PipelineJob::getJobDisplay).collect(Collectors.toList()))));
        } else {
            PipelineJob pipelineJob = modelJobList.get(0);
            Map<String, Object> jobConfig = pipelineJob.getJobConfig();
            return Long.valueOf((String) jobConfig.get("modelId"));
        }
    }


    private List<PipelineJob> findModelJob(String mountedPipelineJobId, Map<String, PipelineJob> pipelineJobMap,
                                           List<PipelineEdge> edges, String modelSelect) {
        List<PipelineJob> resultList = new ArrayList<>();
        // 当前系统中的模型算子
        List<String> targetModelJobList = ContextHolder.getBean(ModelJobPipelinePluginImpl.class).allModelJob();

        Queue<String> queue = new LinkedList<>();
        queue.add(mountedPipelineJobId);
        while (!queue.isEmpty()) {
            String jobId = queue.poll();
            // 判断当前算子是否为我们需要的算子
            PipelineJob pipelineJob = pipelineJobMap.get(jobId);
            if (targetModelJobList.contains(pipelineJob.getJobName())) {
                resultList.add(pipelineJob);
            }
            if ("up".equals(modelSelect)) {
                // 找到上游的算子
                edges.stream().filter(it -> it.getTo().equals(jobId)).forEach(it -> queue.add(it.getFrom()));
            } else {
                // 找到下游的算子
                edges.stream().filter(it -> it.getFrom().equals(jobId)).forEach(it -> queue.add(it.getTo()));
            }
        }
        return resultList;
    }


    private static List<BiConsumer<TripleTuple, DataInvalidCountMetricCollectorConfig.FieldValidate>> getFieldValidateFnMap(
            List<String> fieldErrTypeList) {
        if (Objects.isNull(fieldValidateFnMap)) {
            buildFieldValidateFnMap();
        }
        return fieldValidateFnMap.entrySet().stream().filter(it -> fieldErrTypeList.contains(it.getKey()))
                .map(Map.Entry::getValue).collect(Collectors.toList());
    }

    /**
     * 极端环境下存在重复构建，不影响最终结果
     */
    public static void buildFieldValidateFnMap() {
        Map<String, BiConsumer<TripleTuple, DataInvalidCountMetricCollectorConfig.FieldValidate>> tmpFieldValidateFnMap = new HashMap<>(10);
        //  缺失检查：错误类型标签ERR_MISSING
        tmpFieldValidateFnMap.put("ERR_MISSING", (tripleTuple, fieldValidate) -> {
            Optional<TbColumnDeploy> tbColumnDeployOptional = Optional.ofNullable(tripleTuple.getTbColumnDeploy());
            if (tbColumnDeployOptional.map(TbColumnDeploy::getIsPrimaryKey).orElse(0) == 1) {
                fieldValidate.setMissingEnabled(true);
            }
        });
        // 类型检测：错误类型标签ERR_TYPE
        tmpFieldValidateFnMap.put("ERR_TYPE", (tripleTuple, fieldValidate) -> {
            TbColumnDeploy tbColumnDeploy = tripleTuple.getTbColumnDeploy();
            fieldValidate.setTypeEnabled(true);
            fieldValidate.setTypeExpect(tbColumnDeploy.getColType());
        });
        //  非空检测：错误类型标签ERR_NOTNULL
        tmpFieldValidateFnMap.put("ERR_NOTNULL", (tripleTuple, fieldValidate) -> {
            Optional<TbColumnDeploy> tbColumnDeployOptional = Optional.ofNullable(tripleTuple.getTbColumnDeploy());
            if (tbColumnDeployOptional.map(TbColumnDeploy::getIsNotNull).orElse(0) == 1) {
                fieldValidate.setNotnullEnabled(true);
            }
        });
        //  长度检测：错误类型标签ERR_LENGTH
        tmpFieldValidateFnMap.put("ERR_LENGTH", (tripleTuple, fieldValidate) -> {
            TbColumnDict tbColumnDict = tripleTuple.getTbColumnDict();
            if (Objects.isNull(tbColumnDict) || StrUtil.isBlank(tbColumnDict.getColLength())) {
                return;
            }
            fieldValidate.setLengthEnabled(true);
            fieldValidate.setLengthMax(Integer.valueOf(tbColumnDict.getColLength()));
        });
        // 精度检测：错误类型标签ERR_PRECISION
        tmpFieldValidateFnMap.put("ERR_PRECISION", (tripleTuple, fieldValidate) -> {
            TbColumnDict tbColumnDict = tripleTuple.getTbColumnDict();
            if (Objects.isNull(tbColumnDict) || StrUtil.isBlank(tbColumnDict.getNumPrecision())) {
                return;
            }
            fieldValidate.setPrecisionEnabled(true);
            fieldValidate.setPrecisionMax(Integer.valueOf(tbColumnDict.getNumPrecision()));
        });
        //  枚举检测：错误类型标签ERR_ENUM
        tmpFieldValidateFnMap.put("ERR_ENUM", (tripleTuple, fieldValidate) -> {
            List<TbEnumValue> tbEnumValueList = tripleTuple.getTbEnumValueList();
            if (CollUtil.isEmpty(tbEnumValueList)) {
                return;
            }
            fieldValidate.setEnumEnabled(true);
            fieldValidate.setEnumExpect(tbEnumValueList.stream().map(TbEnumValue::getEnumValue).collect(Collectors.toList()));
        });

        fieldValidateFnMap = tmpFieldValidateFnMap;
    }


    private static class TripleTuple {

        private TbColumnDeploy tbColumnDeploy;

        private TbColumnDict tbColumnDict;

        private List<TbEnumValue> tbEnumValueList;

        public TbColumnDeploy getTbColumnDeploy() {
            return tbColumnDeploy;
        }

        public void setTbColumnDeploy(TbColumnDeploy tbColumnDeploy) {
            this.tbColumnDeploy = tbColumnDeploy;
        }

        public TbColumnDict getTbColumnDict() {
            return tbColumnDict;
        }

        public void setTbColumnDict(TbColumnDict tbColumnDict) {
            this.tbColumnDict = tbColumnDict;
        }

        public List<TbEnumValue> getTbEnumValueList() {
            return tbEnumValueList;
        }

        public void setTbEnumValueList(List<TbEnumValue> tbEnumValueList) {
            this.tbEnumValueList = tbEnumValueList;
        }
    }


}
