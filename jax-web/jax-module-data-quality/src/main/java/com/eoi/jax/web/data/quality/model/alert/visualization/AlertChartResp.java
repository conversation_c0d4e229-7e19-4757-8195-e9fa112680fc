package com.eoi.jax.web.data.quality.model.alert.visualization;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/5/26
 * @Desc: 告警统计图表
 **/
public class AlertChartResp implements Serializable {

    @Schema(description = "分类")
    private String category;

    @Schema(description = "告警统计时间")
    private String alarmTime;
    @Schema(description = "告警统计开始时间")
    private Date alarmStartTime;
    @Schema(description = "告警统计截止时间")
    private Date alarmEndTime;
    @Schema(description = "数据内容")
    private List<ChartCell> items;

    private Integer totalNum;
    @Schema(description = "图例", hidden = true)
    @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
    private Map<String, String> legend;

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<ChartCell> getItems() {
        return items;
    }

    public void setItems(List<ChartCell> items) {
        this.items = items;
    }

    public Map<String, String> getLegend() {
        return legend;
    }

    public void setLegend(Map<String, String> legend) {
        this.legend = legend;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public String getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(String alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Date getAlarmStartTime() {
        return alarmStartTime;
    }

    public void setAlarmStartTime(Date alarmStartTime) {
        this.alarmStartTime = alarmStartTime;
    }

    public Date getAlarmEndTime() {
        return alarmEndTime;
    }

    public void setAlarmEndTime(Date alarmEndTime) {
        this.alarmEndTime = alarmEndTime;
    }
}
