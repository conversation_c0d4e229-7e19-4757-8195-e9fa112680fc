package com.eoi.jax.web.data.quality.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.quality.model.realtime.metric.SampleMetricReq;
import com.eoi.jax.web.data.quality.model.realtime.rule.RealtimeCheckRuleResp;
import com.eoi.jax.web.data.quality.service.RealtimeCheckRuleMetricService;
import com.eoi.jax.web.data.quality.service.RealtimeCheckRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/6/1
 */
@RestController
public class RealtimeCheckRuleController implements V2Controller {

    @Autowired
    private RealtimeCheckRuleService realtimeCheckRuleService;

    @Autowired
    private RealtimeCheckRuleMetricService realtimeCheckRuleMetricService;

    @Operation(summary = "根据id查询详情")
    @GetMapping("realtime/check-rule/{id}")
    public Response<RealtimeCheckRuleResp> get(@Parameter(description = "实时检测规则id", required = true) @PathVariable("id") Long id) {
        return Response.success(realtimeCheckRuleService.get(id));
    }

    @Operation(summary = "检测规则原始值指标")
    @PostMapping("realtime/check-rule/metric/original-value")
    public Response originalValue(@Valid @RequestBody SampleMetricReq sampleMetricReq) {
        return Response.success(realtimeCheckRuleMetricService.originalValue(sampleMetricReq));
    }


    @Operation(summary = "检测规则计算值指标")
    @PostMapping("realtime/check-rule/metric/calculate-value")
    public Response calculateValue(@Valid @RequestBody SampleMetricReq sampleMetricReq) {
        return Response.success(realtimeCheckRuleMetricService.calculateValue(sampleMetricReq));
    }

}
