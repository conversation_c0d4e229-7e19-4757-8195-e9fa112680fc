package com.eoi.jax.web.data.quality.plugin.metric;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.data.quality.enumrate.RealtimeCheckTaskPipelineMountStatusEnum;
import com.eoi.jax.web.data.quality.service.RealtimeCheckTaskPipelineService;
import com.eoi.jax.web.data.quality.service.RealtimeCheckTaskService;
import com.eoi.jax.web.ingestion.model.cluster.Cluster;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineJob;
import com.eoi.jax.web.ingestion.plugin.BasePipelinePlugin;
import com.eoi.jax.web.ingestion.plugin.IPipelinePlugin;
import com.eoi.jax.web.ingestion.provider.manager.PipelineCtx;
import com.eoi.jax.web.ingestion.provider.manager.PipelineJar;
import com.eoi.jax.web.ingestion.service.ClusterService;
import com.eoi.jax.web.repository.entity.TbJar;
import com.eoi.jax.web.repository.entity.TbJob;
import com.eoi.jax.web.repository.entity.TbPipeline;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckTask;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckTaskPipeline;
import com.eoi.jax.web.repository.service.TbJarService;
import com.eoi.jax.web.repository.service.TbJobService;
import com.eoi.jax.web.repository.service.TbRealtimeCheckTaskPipelineService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.eoi.jax.web.data.quality.plugin.metric.CheckTaskMetricCollectorBuildHelper.METRIC_EXPORT_JOB;

/**
 * <AUTHOR>
 * @date 2023/5/19
 */
@Component
public class MetricExportPipelinePluginImpl extends BasePipelinePlugin implements IPipelinePlugin {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetricExportPipelinePluginImpl.class);

    @Autowired
    private CheckTaskMetricCollectorBuildHelper checkTaskMetricCollectorBuildHelper;

    @Autowired
    private RealtimeCheckTaskService realtimeCheckTaskService;

    @Autowired
    private RealtimeCheckTaskPipelineService realtimeCheckTaskPipelineService;

    @Autowired
    private TbRealtimeCheckTaskPipelineService tbRealtimeCheckTaskPipelineService;

    @Autowired
    private TbJarService tbJarService;
    @Autowired
    private TbJobService tbJobService;
    @Autowired
    private ClusterService clusterService;

    @Override
    public void startReq(PipelineCtx pipeline) {
        process(pipeline);
    }


    @Override
    public void debugReq(PipelineCtx pipeline) {
        process(pipeline);
    }


    private void process(PipelineCtx pipelineCtx) {
        TbPipeline tbPipeline = pipelineCtx.getPipeline();
        // 根据pipelineId找到所有的检测任务
        List<TbRealtimeCheckTaskPipeline> tbRealtimeCheckTaskPipelineList = realtimeCheckTaskPipelineService
                .listByPipelineId(tbPipeline.getId());
        // 删除状态为NEED_UNINSTALL的记录
        Iterator<TbRealtimeCheckTaskPipeline> iterator = tbRealtimeCheckTaskPipelineList.iterator();
        while (iterator.hasNext()) {
            TbRealtimeCheckTaskPipeline tbRealtimeCheckTaskPipeline = iterator.next();
            if (RealtimeCheckTaskPipelineMountStatusEnum.NEED_UNINSTALL.code().equals(tbRealtimeCheckTaskPipeline.getStatus())) {
                realtimeCheckTaskPipelineService.deleteByTaskIdOrPipelineId(
                        tbRealtimeCheckTaskPipeline.getTaskId(), tbRealtimeCheckTaskPipeline.getPipelineId());
                iterator.remove();
            }
        }
        if (CollUtil.isEmpty(tbRealtimeCheckTaskPipelineList)) {
            return;
        }
        LOGGER.info("检测到管线作业需挂载检测算子, 检测任务个数:{}", tbRealtimeCheckTaskPipelineList.size());
        // 查询检测任务详情
        List<TbRealtimeCheckTask> tbRealtimeCheckTaskList = realtimeCheckTaskService.selectByTaskIdList(tbRealtimeCheckTaskPipelineList
                .stream().map(TbRealtimeCheckTaskPipeline::getTaskId).distinct().collect(Collectors.toList()));

        // 根据算子名称，中文名，和slot分类
        Map<String, List<TbRealtimeCheckTask>> groupedCheckTaskMap = tbRealtimeCheckTaskList.stream()
                .collect(Collectors.groupingBy(it -> String.join("-", it.getJobName(), it.getJobDisplay(), it.getJobSlot().toString()),
                        Collectors.toList()));

        for (List<TbRealtimeCheckTask> checkTaskList : groupedCheckTaskMap.values()) {
            try {
                TbRealtimeCheckTask checkTask = checkTaskList.get(0);
                // 找到要被挂载的算子
                PipelineJob mountedPipelineJob = findMountedPipelineJob(pipelineCtx, checkTask);

                // 构建监控算子
                PipelineJob metricPipelineJob = checkTaskMetricCollectorBuildHelper.buildMetricPipelineJob(tbPipeline.getId(),
                        checkTaskList, pipelineCtx);
                if (Objects.isNull(metricPipelineJob)) {
                    continue;
                }
                // 将监控算子放入管线作业图中
                PipelineConfig pipelineConfig = pipelineCtx.getPipelineConfig();
                pipelineConfig.getJobs().add(metricPipelineJob);
                pipelineConfig.getEdges().add(checkTaskMetricCollectorBuildHelper.buildPipelineEdge(mountedPipelineJob.getJobId(),
                        metricPipelineJob.getJobId(), checkTask.getJobSlot(), 0));
            } catch (Exception e) {
                LOGGER.info("挂载监控算子失败", e);
                for (TbRealtimeCheckTask checkTask : checkTaskList) {
                    tbRealtimeCheckTaskPipelineService.updateMountStatus(checkTask.getId(), tbPipeline.getId(),
                            RealtimeCheckTaskPipelineMountStatusEnum.FAILED.code(),
                            JsonUtil.encode(MapBuilder.create(MapUtil.newHashMap()).put("-1", e.getMessage()).map()));
                }
            }
        }
        // 添加需要的jar
        addMetricExportJobJar(pipelineCtx);
    }

    /**
     * 根据配置的检测任务获取要被挂载管线作业算子
     *
     * @param checkTask
     * @return
     */
    private PipelineJob findMountedPipelineJob(PipelineCtx pipelineCtx, TbRealtimeCheckTask checkTask) {
        TbPipeline tbPipeline = pipelineCtx.getPipeline();
        Map<String, Object> pipelineUiMap = JsonUtil.decode(tbPipeline.getPipelineUi(), new TypeReference<Map<String, Object>>() {
        });

        // 获取原始任务算子
        List<PipelineJob> jobList = pipelineCtx.getPipelineConfig().getJobs();
        // 根据检测任务配置的jobName进行过滤
        List<PipelineJob> bindPipelineJobList = jobList.stream().filter(it -> it.getJobName().equals(checkTask.getJobName()))
                .collect(Collectors.toList());

        PipelineJob mountedPipelineJob = null;
        for (PipelineJob pipelineJob : bindPipelineJobList) {
            String jobDisplay;
            if (StrUtil.isNotBlank(pipelineJob.getJobDisplay())) {
                jobDisplay = pipelineJob.getJobDisplay();
            } else {
                //noinspection unchecked
                Map<String, Object> uiMap = (Map<String, Object>) pipelineUiMap.getOrDefault(pipelineJob.getJobId(), MapUtil.newHashMap());
                jobDisplay = (String) uiMap.get("display");
            }
            if (checkTask.getJobDisplay().equals(jobDisplay)) {
                if (Objects.nonNull(mountedPipelineJob)) {
                    throw new BizException(ResponseCode.FAILED.getCode(),
                            StrUtil.format("管线作业存在同名的算子:{}, 算子名称:{}, 无法确定挂载位置",
                                    checkTask.getJobName().substring(checkTask.getJobName().lastIndexOf(".") + 1),
                                    checkTask.getJobDisplay()));
                } else {
                    mountedPipelineJob = pipelineJob;
                }
            }
        }
        if (Objects.isNull(mountedPipelineJob)) {
            throw new BizException(ResponseCode.FAILED.getCode(), StrUtil.format("未在管线作业中找到目标算子"));
        }
        return mountedPipelineJob;
    }

    /**
     * 添加Metric Export Job Jar
     * @param pipeline
     */
    private void addMetricExportJobJar(PipelineCtx pipeline) {
        if (pipeline.getPipelineConfig().getJobs().stream()
                .noneMatch(job -> METRIC_EXPORT_JOB.equals(job.getJobName()))) {
            return;
        }

        LOGGER.info("Metric Export Pipeline {} delegate jobs {}",
                pipeline.getPipeline().getPipelineName(),
                METRIC_EXPORT_JOB
        );
        List<TbJob> jobJars = tbJobService.list(new LambdaQueryWrapper<TbJob>()
                .select(TbJob::getJobName, TbJob::getJarId)
                .eq(TbJob::getJobName, METRIC_EXPORT_JOB));
        List<Long> jarIds = jobJars.stream().map(TbJob::getJarId)
                .distinct()
                .filter(i -> pipeline.getJars().stream().noneMatch(j -> j.getJar().getId().equals(i)))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(jarIds)) {
            return;
        }
        List<TbJar> jars = tbJarService.list(new LambdaQueryWrapper<TbJar>()
                .in(TbJar::getId, jarIds));
        Map<Long, Cluster> clusters = new HashMap<>(4);
        for (TbJar jar : jars) {
            Cluster cluster = null;
            Long clusterId = jar.getClusterId();
            if (clusterId != null) {
                cluster = clusters.computeIfAbsent(clusterId, id -> clusterService.getCluster(id));
            }
            PipelineJar pipelineJar = new PipelineJar(jar, cluster);
            pipeline.getJars().add(pipelineJar);
            LOGGER.info("Metric Export Pipeline {} add delegate job jar {}",
                    pipeline.getPipeline().getPipelineName(),
                    jar.getJarName()
            );
        }
    }

}
