<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.xxl.dao.XxlJobGroupMapper">
	
	<resultMap id="XxlJobGroup" type="com.eoi.jax.web.xxl.core.model.AbstractXxlJobGroup" >
		<result column="id" property="id" />
	    <result column="app_name" property="appName" />
	    <result column="executor_name" property="executorName" />
		<result column="address_type" property="addressType" />
		<result column="address_list" property="addressList" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.app_name,
		t.executor_name,
		t.address_type,
		t.address_list
	</sql>

	<select id="count" resultType="java.lang.Integer">
		select count(*) from xxl_job_group
	</select>


	<select id="findAll" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		ORDER BY t.app_name, t.executor_name, t.id ASC
	</select>

	<select id="findByAddressType" parameterType="java.lang.Integer" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		WHERE t.address_type = #{addressType}
		ORDER BY t.app_name, t.executor_name, t.id ASC
	</select>

	<insert id="save" parameterType="com.eoi.jax.web.xxl.core.model.AbstractXxlJobGroup" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO xxl_job_group ( app_name, executor_name, address_type, address_list)
		values ( #{appName}, #{executorName}, #{addressType}, #{addressList});
	</insert>

	<update id="update" parameterType="com.eoi.jax.web.xxl.core.model.AbstractXxlJobGroup" >
		UPDATE xxl_job_group
		SET app_name = #{appName},
			executor_name = #{executorName},
			address_type = #{addressType},
			address_list = #{addressList}
		WHERE id = #{id}
	</update>

	<delete id="remove" parameterType="java.lang.Integer" >
		DELETE FROM xxl_job_group
		WHERE id = #{id}
	</delete>

	<select id="load" parameterType="java.lang.Integer" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		WHERE t.id = #{id}
	</select>

	<select id="pageList" parameterType="java.util.HashMap" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="appName != null and appName != ''">
				AND t.app_name like CONCAT(CONCAT('%', #{appName}), '%')
			</if>
			<if test="executorName != null and executorName != ''">
				AND t.executor_name like CONCAT(CONCAT('%', #{executorName}), '%')
			</if>
			<if test="appExecutorName != null and appExecutorName != ''">
				AND (
					t.app_name like CONCAT(CONCAT('%', #{appExecutorName}), '%') OR
					t.executor_name like CONCAT(CONCAT('%', #{appExecutorName}), '%')
				)
			</if>
		</trim>
		ORDER BY t.app_name, t.executor_name, t.id ASC
		<if test="offset >= 0 and pagesize >= 0">
			LIMIT #{pagesize} OFFSET #{offset}
		</if>
	</select>

	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM xxl_job_group AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="appName != null and appName != ''">
				AND t.app_name like CONCAT(CONCAT('%', #{appName}), '%')
			</if>
			<if test="executorName != null and executorName != ''">
				AND t.executor_name like CONCAT(CONCAT('%', #{appName}), '%')
			</if>
			<if test="appExecutorName != null and appExecutorName != ''">
				AND (
					t.app_name like CONCAT(CONCAT('%', #{appExecutorName}), '%') OR
					t.executor_name like CONCAT(CONCAT('%', #{appExecutorName}), '%')
				)
			</if>
		</trim>
	</select>

</mapper>