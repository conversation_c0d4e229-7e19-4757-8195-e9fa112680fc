package com.eoi.jax.web.xxl.core.trigger;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.xxl.core.conf.XxlJobConfig;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobGroup;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobInfo;
import com.eoi.jax.web.xxl.core.model.XxlJobLog;
import com.eoi.jax.web.xxl.core.route.ExecutorRouteStrategyEnum;
import com.eoi.jax.web.xxl.core.scheduler.XxlJobScheduler;
import com.eoi.jax.web.xxl.core.util.CommonUtil;
import com.eoi.jax.web.xxl.model.ExecutorInfo;
import com.eoi.jax.web.xxl.model.RouteMessage;
import com.eoi.jax.web.xxl.model.TriggerMessage;
import com.xxl.job.core.biz.ExecutorBiz;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.enums.ExecutorBlockStrategyEnum;
import com.xxl.job.core.util.IpUtil;
import com.xxl.job.core.util.ThrowableUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * xxl-job trigger
 * Created by xuxueli on 17/7/13.
 */
public class XxlJobTrigger {
    private static Logger logger = LoggerFactory.getLogger(XxlJobTrigger.class);

    /**
     * trigger job
     *
     * @param jobId
     * @param triggerType
     * @param failRetryCount        >=0: use this param
     *                              <0: use param from job info config
     * @param executorShardingParam
     * @param executorParam         null: use job param
     *                              not null: cover job param
     * @param addressList           null: use executor addressList
     *                              not null: cover
     */
    public static void trigger(int jobId,
                               TriggerTypeEnum triggerType,
                               int failRetryCount,
                               String executorShardingParam,
                               String executorParam,
                               String addressList) {

        // load data
        AbstractXxlJobInfo jobInfo = XxlJobConfig.getAdminConfig().getXxlJobInfoService().loadById(jobId);
        if (jobInfo == null) {
            logger.warn(">>>>>>>>>>>> trigger fail, jobId invalid，jobId={}", jobId);
            return;
        }
        if (executorParam != null) {
            jobInfo.setExecutorParam(executorParam);
        }
        int finalFailRetryCount = failRetryCount >= 0 ? failRetryCount : jobInfo.getExecutorFailRetryCount();
        AbstractXxlJobGroup group = XxlJobConfig.getAdminConfig().getXxlJobGroupDao().load(jobInfo.getJobGroup());

        // cover addressList
        if (addressList != null && addressList.trim().length() > 0) {
            group.setAddressType(1);
            group.setAddressList(addressList.trim());
        }

        // sharding param
        int[] shardingParam = null;
        if (executorShardingParam != null) {
            String[] shardingArr = executorShardingParam.split("/");
            if (shardingArr.length == 2 && CommonUtil.isInteger(shardingArr[0]) && CommonUtil.isInteger(shardingArr[1])) {
                shardingParam = new int[2];
                shardingParam[0] = Integer.valueOf(shardingArr[0]);
                shardingParam[1] = Integer.valueOf(shardingArr[1]);
            }
        }
        if (ExecutorRouteStrategyEnum.SHARDING_BROADCAST == ExecutorRouteStrategyEnum.match(jobInfo.getExecutorRouteStrategy(), null)
                && group.getRegistryList() != null && !group.getRegistryList().isEmpty()
                && shardingParam == null) {
            for (int i = 0; i < group.getRegistryList().size(); i++) {
                processTrigger(group, jobInfo, finalFailRetryCount, triggerType, i, group.getRegistryList().size());
            }
        } else {
            if (shardingParam == null) {
                shardingParam = new int[]{0, 1};
            }
            processTrigger(group, jobInfo, finalFailRetryCount, triggerType, shardingParam[0], shardingParam[1]);
        }

    }

    /**
     * @param group               job group, registry list may be empty
     * @param jobInfo
     * @param finalFailRetryCount
     * @param triggerType
     * @param index               sharding index
     * @param total               sharding index
     */
    private static void processTrigger(AbstractXxlJobGroup group, AbstractXxlJobInfo jobInfo,
                                       int finalFailRetryCount,
                                       TriggerTypeEnum triggerType, int index, int total) {

        // param
        // block strategy
        ExecutorBlockStrategyEnum blockStrategy = ExecutorBlockStrategyEnum
                .match(jobInfo.getExecutorBlockStrategy(), ExecutorBlockStrategyEnum.SERIAL_EXECUTION);
        // route strategy
        ExecutorRouteStrategyEnum executorRouteStrategyEnum =
                ExecutorRouteStrategyEnum.match(jobInfo.getExecutorRouteStrategy(), null);
        String shardingParam = (ExecutorRouteStrategyEnum.SHARDING_BROADCAST == executorRouteStrategyEnum) ?
                String.valueOf(index).concat("/").concat(String.valueOf(total)) : null;

        // 1、save log-id
        XxlJobLog jobLog = new XxlJobLog();
        jobLog.setJobGroup(jobInfo.getJobGroup());
        jobLog.setJobId(jobInfo.getId());
        jobLog.setTriggerTime(new Date());
        XxlJobConfig.getAdminConfig().getXxlJobLogDao().save(jobLog);
        logger.debug(">>>>>>>>>>> xxl-job trigger start, jobId:{} logId:{}", jobInfo.getId(), jobLog.getId());

        // 2、init trigger-param
        TriggerParam triggerParam = new TriggerParam();
        triggerParam.setJobId(jobInfo.getId());
        triggerParam.setExecutorHandler(jobInfo.getExecutorHandler());
        triggerParam.setExecutorParams(jobInfo.getExecutorParam());
        triggerParam.setExecutorBlockStrategy(jobInfo.getExecutorBlockStrategy());
        triggerParam.setExecutorTimeout(jobInfo.getExecutorTimeout());
        triggerParam.setLogId(jobLog.getId());
        triggerParam.setLogDateTime(jobLog.getTriggerTime().getTime());
        triggerParam.setGlueType(jobInfo.getGlueType());
        triggerParam.setGlueSource(jobInfo.getGlueSource());
        triggerParam.setGlueUpdatetime(jobInfo.getGlueUpdatetime().getTime());
        triggerParam.setBroadcastIndex(index);
        triggerParam.setBroadcastTotal(total);

        // 3、init address
        String address = null;
        RouteMessage routeMessage = null;
        if (group.getRegistryList() != null && !group.getRegistryList().isEmpty()) {
            routeMessage = executorRouteStrategyEnum.getRouter().route(triggerParam, group.getRegistryList());
            if (routeMessage.getCode() == ReturnT.SUCCESS_CODE) {
                address = routeMessage.getAddress();
            }
        } else {
            routeMessage = new RouteMessage(null, ReturnT.FAIL_CODE, "", null);
        }

        // 4、trigger remote executor
        ExecutorInfo triggerResult = null;
        if (address != null) {
            ReturnT<ExecutorInfo> returnT = runExecutor(triggerParam, address);
            triggerResult = returnT.getContent();
        } else {
            triggerResult = new ExecutorInfo(null, ReturnT.FAIL_CODE, "");
        }

        // 5、collection trigger info
        TriggerMessage triggerMsg = new TriggerMessage();
        triggerMsg.setTriggerType(triggerType.getTitle());
        triggerMsg.setAdminAddress(IpUtil.getIp());
        triggerMsg.setRegisterType(group.getAddressType());
        triggerMsg.setRegisterAddress(group.getRegistryList());
        triggerMsg.setRouteStrategy(executorRouteStrategyEnum.getTitle());
        triggerMsg.setRouteShardingParam(shardingParam);
        triggerMsg.setBlockStrategy(blockStrategy.getTitle());
        triggerMsg.setTimeout(jobInfo.getExecutorTimeout());
        triggerMsg.setFailRetryCount(finalFailRetryCount);
        triggerMsg.setRouteMessage(routeMessage);
        triggerMsg.setTriggerResult(triggerResult);

        // 6、save log trigger-info
        jobLog.setExecutorAddress(address);
        jobLog.setExecutorHandler(jobInfo.getExecutorHandler());
        jobLog.setExecutorParam(jobInfo.getExecutorParam());
        jobLog.setExecutorShardingParam(shardingParam);
        jobLog.setExecutorFailRetryCount(finalFailRetryCount);
        jobLog.setTriggerCode(triggerResult.getCode());
        jobLog.setTriggerMsg(JsonUtil.encode(triggerMsg));
        XxlJobConfig.getAdminConfig().getXxlJobLogDao().updateTriggerInfo(jobLog);

        logger.debug(">>>>>>>>>>> xxl-job trigger end, jobId:{} logId:{}", jobInfo.getId(), jobLog.getId());
    }

    /**
     * run executor
     *
     * @param triggerParam
     * @param address
     * @return
     */
    public static ReturnT<ExecutorInfo> runExecutor(TriggerParam triggerParam, String address) {
        ReturnT<String> runResult = null;
        try {
            triggerParam.setGlueType(JaxGlueTypeEnum.match(triggerParam.getGlueType()).name());
            ExecutorBiz executorBiz = XxlJobScheduler.getExecutorBiz(address);
            runResult = executorBiz.run(triggerParam);
            if (ReturnT.FAIL_CODE == runResult.getCode()) {
                logger.warn(">>>>>>>>>>> xxl-job trigger jobId {} executor address {} failed",
                        triggerParam.getJobId(),
                        address);
            }
        } catch (Exception e) {
            logger.error(">>>>>>>>>>> xxl-job trigger error, please check if the executor[{}] is running.", address, e);
            runResult = new ReturnT<String>(ReturnT.FAIL_CODE, ThrowableUtil.toString(e));
        }

        ExecutorInfo info = new ExecutorInfo();
        info.setAddress(address);
        info.setCode(runResult.getCode());
        info.setMsg(runResult.getMsg());

        ReturnT<ExecutorInfo> result = new ReturnT<>();
        result.setCode(runResult.getCode());
        result.setContent(info);
        return result;
    }

}
