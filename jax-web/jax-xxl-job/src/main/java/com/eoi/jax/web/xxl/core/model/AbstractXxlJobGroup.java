package com.eoi.jax.web.xxl.core.model;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.xxl.model.AbstractXxlProjectAuthorityEntity;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by xuxueli on 16/9/30.
 */
public class AbstractXxlJobGroup extends AbstractXxlProjectAuthorityEntity {

    @OpPrimaryKey
    private int id;

    @OpPrimaryName
    @Schema(description = "app名称")
    private String appName;

    @Schema(description = "执行器名称")
    private String executorName;

    @Schema(description = "执行器地址类型：0=自动注册、1=手动录入")
    private int addressType;

    @Schema(description = "执行器地址列表，多地址逗号分隔(手动录入)")
    private String addressList;

    @Schema(description = "执行器地址列表(系统注册)")
    private List<String> registryList;

    public List<String> getRegistryList() {
        if (addressList != null && addressList.trim().length() > 0) {
            registryList = new ArrayList<String>(Arrays.asList(addressList.split(",")));
        }
        return registryList;
    }

    public Integer getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public int getAddressType() {
        return addressType;
    }

    public void setAddressType(int addressType) {
        this.addressType = addressType;
    }

    public String getAddressList() {
        return addressList;
    }

    public void setAddressList(String addressList) {
        this.addressList = addressList;
    }

}
