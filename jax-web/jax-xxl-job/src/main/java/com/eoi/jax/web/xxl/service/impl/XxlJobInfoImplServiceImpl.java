package com.eoi.jax.web.xxl.service.impl;

import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.enumrate.ProjectResourceTypeEnum;
import com.eoi.jax.web.core.mybatis.strategy.ProjectAuthConstant;
import com.eoi.jax.web.xxl.core.model.AbstractXxlJobInfo;
import com.eoi.jax.web.xxl.dao.XxlJobInfoMapper;
import com.eoi.jax.web.xxl.model.JobInfoQueryParam;
import com.eoi.jax.web.xxl.service.IXxlJobInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/10/25
 * @Desc:
 **/
@Service
public class XxlJobInfoImplServiceImpl extends AbstractXxlJonProjectAuth implements IXxlJobInfoService {
    @Resource
    private XxlJobInfoMapper xxlJobInfoMapper;

    @Override
    public List<AbstractXxlJobInfo> findAll() {
        return xxlJobInfoMapper.findAll();
    }

    @Override
    public List<AbstractXxlJobInfo> findByNameList(List<String> jobNameList) {
        return xxlJobInfoMapper.findByNameList(jobNameList);
    }

    @Override
    public List<AbstractXxlJobInfo> pageList(JobInfoQueryParam jobInfoQueryParam) {
        return xxlJobInfoMapper.pageList(jobInfoQueryParam.getStart(), jobInfoQueryParam.getLength(),
                jobInfoQueryParam.getJobGroup(), jobInfoQueryParam.getTriggerStatus(), jobInfoQueryParam.getJobDesc(),
                jobInfoQueryParam.getJobName(), jobInfoQueryParam.getJobNameDesc(), jobInfoQueryParam.getExecutorHandler(),
                jobInfoQueryParam.getAuthor(), jobInfoQueryParam.getGlueType(), jobInfoQueryParam.getGlueTypeNin(),
                -1, null, jobInfoQueryParam.getOrderBy(), jobInfoQueryParam.isAsc(),
                jobInfoQueryParam.getJobIds(), jobInfoQueryParam.getIsUserCreated());
    }

    @Override
    public int pageListCount(JobInfoQueryParam jobInfoQueryParam) {
        return xxlJobInfoMapper.pageListCount(jobInfoQueryParam.getStart(), jobInfoQueryParam.getLength(),
                jobInfoQueryParam.getJobGroup(), jobInfoQueryParam.getTriggerStatus(), jobInfoQueryParam.getJobDesc(),
                jobInfoQueryParam.getJobName(), jobInfoQueryParam.getJobNameDesc(), jobInfoQueryParam.getExecutorHandler(),
                jobInfoQueryParam.getAuthor(), jobInfoQueryParam.getGlueType(), jobInfoQueryParam.getGlueTypeNin(),
                jobInfoQueryParam.getIdNeq(),
                jobInfoQueryParam.getJobNameEq(), jobInfoQueryParam.getIsUserCreated());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(AbstractXxlJobInfo info) {
        info.setIsUserCreated(info.getIsUserCreated() == null ? 1 : info.getIsUserCreated());
        int save = xxlJobInfoMapper.save(info);
        info.setProjectAuthIsOwner(true);
        info.setProjectAuthProjectId(ContextHolder.getCurrentProjectId());
        info.setProjectAuthPrivilege(ProjectAuthConstant.ALL_PRIVILEGE);
        addProjectAuth(info, ProjectResourceTypeEnum.XXL_JOB_INFO.code(), ContextHolder.getUser());
        return save;
    }

    @Override
    public AbstractXxlJobInfo loadById(int id) {
        return xxlJobInfoMapper.loadById(id);
    }

    @Override
    public int update(AbstractXxlJobInfo xxlJobInfo) {
        return xxlJobInfoMapper.update(xxlJobInfo);
    }

    @Override
    public int delete(long id) {
        int delete = xxlJobInfoMapper.delete(id);
        deleteProjectAuth(id, ProjectResourceTypeEnum.XXL_JOB_INFO.code());
        return delete;
    }

    @Override
    public List<AbstractXxlJobInfo> getJobsByGroup(int jobGroup) {
        return xxlJobInfoMapper.getJobsByGroup(jobGroup);
    }

    @Override
    public int findAllCount() {
        return xxlJobInfoMapper.findAllCount();
    }

    @Override
    public List<AbstractXxlJobInfo> scheduleJobQuery(long maxNextTime, int pagesize) {
        return xxlJobInfoMapper.scheduleJobQuery(maxNextTime, pagesize);
    }

    @Override
    public int scheduleUpdate(AbstractXxlJobInfo xxlJobInfo) {
        return xxlJobInfoMapper.scheduleUpdate(xxlJobInfo);
    }

    @Override
    public void scheduleUpdateBatch(List<AbstractXxlJobInfo> list) {
        xxlJobInfoMapper.scheduleUpdateBatch(list);
    }
}
