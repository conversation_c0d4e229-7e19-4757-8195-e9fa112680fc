package com.eoi.jax.web.xxl.model;

import java.util.List;

public class ChartInfo {
    private List<String> triggerDayList;
    private List<Integer> triggerDayCountRunningList;
    private List<Integer> triggerDayCountSucList;
    private List<Integer> triggerDayCountFailList;
    private Integer triggerCountRunningTotal;
    private Integer triggerCountSucTotal;
    private Integer triggerCountFailTotal;

    public List<String> getTriggerDayList() {
        return triggerDayList;
    }

    public ChartInfo setTriggerDayList(List<String> triggerDayList) {
        this.triggerDayList = triggerDayList;
        return this;
    }

    public List<Integer> getTriggerDayCountRunningList() {
        return triggerDayCountRunningList;
    }

    public ChartInfo setTriggerDayCountRunningList(List<Integer> triggerDayCountRunningList) {
        this.triggerDayCountRunningList = triggerDayCountRunningList;
        return this;
    }

    public List<Integer> getTriggerDayCountSucList() {
        return triggerDayCountSucList;
    }

    public ChartInfo setTriggerDayCountSucList(List<Integer> triggerDayCountSucList) {
        this.triggerDayCountSucList = triggerDayCountSucList;
        return this;
    }

    public List<Integer> getTriggerDayCountFailList() {
        return triggerDayCountFailList;
    }

    public ChartInfo setTriggerDayCountFailList(List<Integer> triggerDayCountFailList) {
        this.triggerDayCountFailList = triggerDayCountFailList;
        return this;
    }

    public Integer getTriggerCountRunningTotal() {
        return triggerCountRunningTotal;
    }

    public ChartInfo setTriggerCountRunningTotal(Integer triggerCountRunningTotal) {
        this.triggerCountRunningTotal = triggerCountRunningTotal;
        return this;
    }

    public Integer getTriggerCountSucTotal() {
        return triggerCountSucTotal;
    }

    public ChartInfo setTriggerCountSucTotal(Integer triggerCountSucTotal) {
        this.triggerCountSucTotal = triggerCountSucTotal;
        return this;
    }

    public Integer getTriggerCountFailTotal() {
        return triggerCountFailTotal;
    }

    public ChartInfo setTriggerCountFailTotal(Integer triggerCountFailTotal) {
        this.triggerCountFailTotal = triggerCountFailTotal;
        return this;
    }
}
