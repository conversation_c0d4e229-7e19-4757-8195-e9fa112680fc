package com.eoi.jax.web.xxl.config;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.config.ConfigLoader;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class XxlExecutorJobConfig {
    private static final Logger LOGGER = LoggerFactory.getLogger(XxlExecutorJobConfig.class);
    public static final String APP_NAME = "JAX_DEFAULT_EXECUTOR";

    @Value("${xxl.job.accessToken:}")
    private String accessToken;

    @Value("${xxl.job.admin.addresses:}")
    private String adminAddresses;

    @Value("${xxl.job.executor.ip:}")
    private String ip;

    @Value("${xxl.job.executor.port:9998}")
    private int port;

    @Value("${xxl.job.executor.logpath:logs/xxl}")
    private String logPath;

    @Value("${xxl.job.executor.logretentiondays:7}")
    private int logRetentionDays;

    private XxlJobSpringExecutor xxlJobSpringExecutor;

    public String getAccessToken() {
        return accessToken;
    }

    public String getAdminAddresses() throws IOException {
        if (StringUtils.isEmpty(adminAddresses)
            || adminAddresses.contains("127.0.0.1")
            || adminAddresses.contains("localhost")
            || adminAddresses.startsWith("http://:")) {
            return Common.urlPathsJoin(ConfigLoader.load().getServer().getListenHttp(), "/api/v3/xxl-job");
        }
        return adminAddresses;
    }

    public String getIp() throws IOException {
        if (StrUtil.isNotBlank(ip)) {
            return ip;
        }
        return ConfigLoader.load().getServer().getListenAddress();
    }

    public int getPort() {
        return port;
    }

    public String getLogPath() {
        return logPath;
    }

    public int getLogRetentionDays() {
        return logRetentionDays;
    }

    public XxlJobSpringExecutor xxlJobExecutor() throws IOException {
        LOGGER.info(">>>>>>>>>>> executor config init.");
        XxlJobSpringExecutor executor = new XxlJobSpringExecutor();
        executor.setAdminAddresses(getAdminAddresses());
        executor.setAppname(APP_NAME);
        executor.setIp(getIp());
        executor.setPort(getPort());
        executor.setAccessToken(getAccessToken());
        executor.setLogPath(getLogPath());
        executor.setLogRetentionDays(getLogRetentionDays());
        return executor;
    }

    public void initialize(ApplicationContext applicationContext) throws IOException {
        xxlJobSpringExecutor = xxlJobExecutor();
        xxlJobSpringExecutor.setApplicationContext(applicationContext);
        xxlJobSpringExecutor.afterSingletonsInstantiated();
    }

    public void destroy() {
        if (xxlJobSpringExecutor != null) {
            xxlJobSpringExecutor.destroy();
        }
    }
}
