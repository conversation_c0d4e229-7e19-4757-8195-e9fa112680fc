package com.eoi.jax.web.xxl.service.impl;

import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.ProjectAuthException;
import com.eoi.jax.web.repository.entity.TbProjectResource;
import com.eoi.jax.web.repository.entity.TbUser;
import com.eoi.jax.web.repository.service.TbProjectResourceService;
import com.eoi.jax.web.xxl.model.AbstractXxlProjectAuthorityEntity;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/10/25
 * @Desc:
 **/
public abstract class AbstractXxlJonProjectAuth {
    @Resource
    private TbProjectResourceService tbProjectResourceService;

    /**
     * 增加权限
     *
     * @param entity
     * @param resourceType
     * @param user
     */
    protected void addProjectAuth(AbstractXxlProjectAuthorityEntity entity, String resourceType,
                                   TbUser user) {
        if (!ContextHolder.getProjectAuthorityEnable()) {
            return;
        }
        TbProjectResource projectResource = new TbProjectResource();
        projectResource.setProjectId(entity.getProjectAuthProjectId());
        projectResource.setIsOwner(1);
        projectResource.setPrivilege(entity.getProjectAuthPrivilege());
        projectResource.setResourceType(resourceType);
        projectResource.setResourceId(entity.getId().longValue());
        projectResource.setCreateTime(new Date());
        projectResource.setCreateUser(user.getId());
        projectResource.setUpdateTime(new Date());
        if (projectResource.getPrivilege() == null) {
            throw new ProjectAuthException(ResponseCode.PROJECT_AUTH_NO_PROJECT_ID);
        }
        tbProjectResourceService.save(projectResource);
    }

    /**
     * 删除权限
     *
     * @param resourceId
     * @param resourceType
     */
    protected void deleteProjectAuth(Long resourceId, String resourceType) {
        if (!ContextHolder.getProjectAuthorityEnable()) {
            return;
        }
        Map<String, Object> columnMap = new HashMap<>(2);
        columnMap.put("resource_id", resourceId);
        columnMap.put("resource_type", resourceType);
        tbProjectResourceService.removeByMap(columnMap);
    }

}
