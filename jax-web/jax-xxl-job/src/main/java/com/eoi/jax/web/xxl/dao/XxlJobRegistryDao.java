package com.eoi.jax.web.xxl.dao;

import com.eoi.jax.web.xxl.core.model.XxlJobRegistry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/9/30.
 */
@Mapper
public interface XxlJobRegistryDao {

    /**
     * 查询
     *
     * @param timeout
     * @param nowTime
     * @return
     */
    List<Integer> findDead(@Param("timeout") int timeout,
                           @Param("nowTime") Date nowTime);

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    int removeDead(@Param("ids") List<Integer> ids);

    /**
     * 查询全部
     *
     * @param timeout
     * @param nowTime
     * @return
     */
    List<XxlJobRegistry> findAll(@Param("timeout") int timeout,
                                 @Param("nowTime") Date nowTime);

    /**
     * 更新
     *
     * @param registryGroup
     * @param registryKey
     * @param registryValue
     * @param updateTime
     * @return
     */
    int registryUpdate(@Param("registryGroup") String registryGroup,
                       @Param("registryKey") String registryKey,
                       @Param("registryValue") String registryValue,
                       @Param("updateTime") Date updateTime);

    /**
     * 保存
     *
     * @param registryGroup
     * @param registryKey
     * @param registryValue
     * @param updateTime
     * @return
     */
    int registrySave(@Param("registryGroup") String registryGroup,
                     @Param("registryKey") String registryKey,
                     @Param("registryValue") String registryValue,
                     @Param("updateTime") Date updateTime);

    /**
     * 删除
     *
     * @param registryGroup
     * @param registryKey
     * @param registryValue
     * @return
     */
    int registryDelete(@Param("registryGroup") String registryGroup,
                       @Param("registryKey") String registryKey,
                       @Param("registryValue") String registryValue);

}
