package com.eoi.jax.web.xxl.model;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.model.IProjectAuthModelResp;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * @Author: tangy
 * @Date: 2023/10/25
 * @Desc:
 **/
public abstract class AbstractXxlProjectAuthorityEntity {
    @JsonIgnore
    @TableField(exist = false)
    private Boolean projectAuthIsOwner;
    @JsonIgnore
    @TableField(exist = false)
    private Long projectAuthProjectId;
    @JsonIgnore
    @TableField(exist = false)
    private String projectAuthPrivilege;
    @JsonIgnore
    @TableField(exist = false)
    private String projectAuthOwnerProjectName;
    @JsonIgnore
    @TableField(exist = false)
    private Long projectAuthOwnerProjectId;
    @JsonIgnore
    @TableField(exist = false)
    private String projectAuthResourceType;

    public boolean hasWritePrivilege() {
        if (Boolean.TRUE.equals(projectAuthIsOwner)) {
            return true;
        }
        if (StringUtils.isEmpty(projectAuthPrivilege)) {
            return false;
        }
        if (projectAuthPrivilege.contains("*") || projectAuthPrivilege.contains(":write")) {
            return true;
        }
        return false;
    }

    /**
     * 获取id
     *
     * @return
     */
    public abstract Integer getId();

    public Boolean getProjectAuthIsOwner() {
        return projectAuthIsOwner;
    }

    public void setProjectAuthIsOwner(Boolean projectAuthIsOwner) {
        this.projectAuthIsOwner = projectAuthIsOwner;
    }

    public Long getProjectAuthProjectId() {
        return projectAuthProjectId;
    }

    public void setProjectAuthProjectId(Long projectAuthProjectId) {
        this.projectAuthProjectId = projectAuthProjectId;
    }

    public String getProjectAuthPrivilege() {
        return projectAuthPrivilege;
    }

    public void setProjectAuthPrivilege(String projectAuthPrivilege) {
        this.projectAuthPrivilege = projectAuthPrivilege;
    }

    public String getProjectAuthOwnerProjectName() {
        return projectAuthOwnerProjectName;
    }

    public void setProjectAuthOwnerProjectName(String projectAuthOwnerProjectName) {
        this.projectAuthOwnerProjectName = projectAuthOwnerProjectName;
    }

    public Long getProjectAuthOwnerProjectId() {
        return projectAuthOwnerProjectId;
    }

    public void setProjectAuthOwnerProjectId(Long projectAuthOwnerProjectId) {
        this.projectAuthOwnerProjectId = projectAuthOwnerProjectId;
    }

    public String getProjectAuthResourceType() {
        return projectAuthResourceType;
    }

    public void setProjectAuthResourceType(String projectAuthResourceType) {
        this.projectAuthResourceType = projectAuthResourceType;
    }

    @JsonGetter("projectAuth")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public IProjectAuthModelResp getProjectAuth() {
        if (StrUtil.isBlank(this.projectAuthResourceType) || !Boolean.TRUE.equals(ContextHolder.getProjectAuthorityEnable())) {
            return null;
        }
        ProjectAuthRespModel.ProjectAuthModelResp projectAuthModelResp = new ProjectAuthRespModel.ProjectAuthModelResp();
        projectAuthModelResp.setIsOwner(this.projectAuthIsOwner);
        projectAuthModelResp.setProjectId(this.projectAuthProjectId);
        projectAuthModelResp.setOwnerProjectName(this.projectAuthOwnerProjectName);
        projectAuthModelResp.setOwnerProjectId(this.projectAuthOwnerProjectId);
        projectAuthModelResp.setResourceType(this.projectAuthResourceType);
        projectAuthModelResp.setPrivilegeList(StrUtil.isEmpty(this.projectAuthPrivilege) ?
                new ArrayList<>() :
                Arrays.asList(this.projectAuthPrivilege.split(",")));
        projectAuthModelResp.setHasResource(this.projectAuthProjectId != null);
        return projectAuthModelResp;
    }
}
