package com.eoi.jax.web.xxl.compat;

import com.xxl.job.core.biz.model.ReturnT;

/**
 * <AUTHOR>
 * @Date 2024/12/10
 */
public class HandleCallbackParamV2 {
    private static final long serialVersionUID = 42L;

    private long logId;
    private long logDateTim;

    private ReturnT<String> executeResult;

    public HandleCallbackParamV2() {
    }

    public HandleCallbackParamV2(long logId, long logDateTim, ReturnT<String> executeResult) {
        this.logId = logId;
        this.logDateTim = logDateTim;
        this.executeResult = executeResult;
    }

    public long getLogId() {
        return logId;
    }

    public void setLogId(long logId) {
        this.logId = logId;
    }

    public long getLogDateTim() {
        return logDateTim;
    }

    public void setLogDateTim(long logDateTim) {
        this.logDateTim = logDateTim;
    }

    public ReturnT<String> getExecuteResult() {
        return executeResult;
    }

    public void setExecuteResult(ReturnT<String> executeResult) {
        this.executeResult = executeResult;
    }

    @Override
    public String toString() {
        return "HandleCallbackParam{" +
                "logId=" + logId +
                ", logDateTim=" + logDateTim +
                ", executeResult=" + executeResult +
                '}';
    }
}
