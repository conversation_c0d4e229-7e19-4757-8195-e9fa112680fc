package com.eoi.jax.web.xxl.core.route.strategy;


import com.eoi.jax.web.xxl.core.route.AbstractExecutorRouter;
import com.eoi.jax.web.xxl.model.RouteMessage;
import com.xxl.job.core.biz.model.TriggerParam;

import java.util.List;

public class ExecutorRouteShardIndex extends AbstractExecutorRouter {

    @Override
    public RouteMessage route(TriggerParam triggerParam, List<String> addressList) {
        int index = triggerParam.getBroadcastIndex();
        String address;
        if (0 < index && index < addressList.size()) {
            address = addressList.get(index);
        } else {
            address = addressList.get(0);
        }
        return new RouteMessage(address);
    }

}
