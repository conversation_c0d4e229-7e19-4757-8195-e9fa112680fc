package com.eoi.jax.web.xxl.model;

import com.xxl.job.core.glue.GlueTypeEnum;

@SuppressWarnings("all")
public class GlueTypeCode {
    public static final String GLUE_GROOVY =
        "package com.xxl.job.service.handler;\n" +
            "\n" +
            "import com.xxl.job.core.log.XxlJobLogger;\n" +
            "import com.xxl.job.core.biz.model.ReturnT;\n" +
            "import com.xxl.job.core.handler.IJobHandler;\n" +
            "\n" +
            "public class DemoGlueJobHandler extends IJobHandler {\n" +
            "\n" +
            "\t@Override\n" +
            "\tpublic ReturnT<String> execute(String param) throws Exception {\n" +
            "\t\tXxlJobLogger.log(\"XXL-JOB, Hello World.\");\n" +
            "\t\treturn ReturnT.SUCCESS;\n" +
            "\t}\n" +
            "\n" +
            "}\n";
    public static final String GLUE_SHELL =
        "#!/bin/bash\n" +
            "echo \"xxl-job: hello shell\"\n" +
            "\n" +
            "echo \"脚本位置：$0\"\n" +
            "echo \"任务参数：$1\"\n" +
            "echo \"分片序号 = $2\"\n" +
            "echo \"分片总数 = $3\"\n" +
            "\n" +
            "echo \"Good bye!\"\n" +
            "exit 0\n";
    public static final String GLUE_PYTHON =
        "#!/usr/bin/python\n" +
            "# -*- coding: UTF-8 -*-\n" +
            "import time\n" +
            "import sys\n" +
            "\n" +
            "print \"xxl-job: hello python\"\n" +
            "\n" +
            "print \"脚本位置：\", sys.argv[0]\n" +
            "print \"任务参数：\", sys.argv[1]\n" +
            "print \"分片序号：\", sys.argv[2]\n" +
            "print \"分片总数：\", sys.argv[3]\n" +
            "\n" +
            "print \"Good bye!\"\n" +
            "exit(0)\n";
    public static final String GLUE_PHP =
        "<?php\n" +
            "\n" +
            "    echo \"xxl-job: hello php  \\n\";\n" +
            "\n" +
            "    echo \"脚本位置：$argv[0]  \\n\";\n" +
            "    echo \"任务参数：$argv[1]  \\n\";\n" +
            "    echo \"分片序号 = $argv[2]  \\n\";\n" +
            "    echo \"分片总数 = $argv[3]  \\n\";\n" +
            "\n" +
            "    echo \"Good bye!  \\n\";\n" +
            "    exit(0);\n" +
            "\n" +
            "?>\n";
    public static final String GLUE_NODEJS =
        "#!/usr/bin/env node\n" +
            "console.log(\"xxl-job: hello nodejs\")\n" +
            "\n" +
            "var arguments = process.argv\n" +
            "\n" +
            "console.log(\"脚本位置: \" + arguments[1])\n" +
            "console.log(\"任务参数: \" + arguments[2])\n" +
            "console.log(\"分片序号: \" + arguments[3])\n" +
            "console.log(\"分片总数: \" + arguments[4])\n" +
            "\n" +
            "console.log(\"Good bye!\")\n" +
            "process.exit(0)\n";
    public static final String GLUE_POWERSHELL =
        "Write-Host \"xxl-job: hello powershell\"\n" +
            "\n" +
            "Write-Host \"脚本位置: \" $MyInvocation.MyCommand.Definition\n" +
            "Write-Host \"任务参数: \"\n" +
            "\tif ($args.Count -gt 2) { $args[0..($args.Count-3)] }\n" +
            "Write-Host \"分片序号: \" $args[$args.Count-2]\n" +
            "Write-Host \"分片总数: \" $args[$args.Count-1]\n" +
            "\n" +
            "Write-Host \"Good bye!\"\n" +
            "exit 0\n";

    public static String getDefault(GlueTypeEnum glueTypeEnum) {
        if (glueTypeEnum == null) {
            return "";
        }
        switch (glueTypeEnum) {
            case GLUE_GROOVY:
                return GLUE_GROOVY;
            case GLUE_SHELL:
                return GLUE_SHELL;
            case GLUE_PYTHON:
                return GLUE_PYTHON;
            case GLUE_PHP:
                return GLUE_PHP;
            case GLUE_NODEJS:
                return GLUE_NODEJS;
            case GLUE_POWERSHELL:
                return GLUE_POWERSHELL;
            default:
                return "";
        }
    }
}
