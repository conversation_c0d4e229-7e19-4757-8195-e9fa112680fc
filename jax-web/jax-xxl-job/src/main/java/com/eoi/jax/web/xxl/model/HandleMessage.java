package com.eoi.jax.web.xxl.model;

public class HandleMessage {
    private String type;
    private String msg;
    private Long time;

    public HandleMessage() {
    }

    public HandleMessage(String type) {
        this(type, null);
    }

    public HandleMessage(String type, String msg) {
        this(type, msg, System.currentTimeMillis());
    }

    public HandleMessage(String type, String msg, Long time) {
        this.type = type;
        this.msg = msg;
        this.time = time;
    }

    public String getType() {
        return type;
    }

    public HandleMessage setType(String type) {
        this.type = type;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public HandleMessage setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public Long getTime() {
        return time;
    }

    public HandleMessage setTime(Long time) {
        this.time = time;
        return this;
    }
}
