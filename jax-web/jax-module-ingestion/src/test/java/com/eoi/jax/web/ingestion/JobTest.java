package com.eoi.jax.web.ingestion;
import java.util.Date;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.repository.entity.TbJob;
import com.eoi.jax.web.repository.service.TbJobService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/8/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class JobTest {

    @Resource
    private TbJobService tbJobService;

    @Test
    public void test() {
        TbJob tbJob = new TbJob();
        tbJob.setId(111111111L);
        tbJob.setJobName("com.eoi.jax.flink.job.process.StringDecoderJob");
        tbJob.setJobDisplay("哈哈");
        tbJob.setJobType("streaming");
        tbJob.setJobRole("process");
        tbJob.setJobCategory("文本解析");
        tbJob.setJobDescription("");
        tbJob.setInTypes("");
        tbJob.setOutTypes("");
        tbJob.setExperimental("");
        tbJob.setApiVersion("");
        tbJob.setInternal(0);
        tbJob.setParameters("");
        tbJob.setHasIcon(0);
        tbJob.setHasDoc(0);
        tbJob.setIcon(new byte[0]);
        tbJob.setDoc(new byte[0]);
        tbJob.setJarId(0L);
        tbJob.setCreateTime(new Date());
        tbJob.setUpdateTime(new Date());
        tbJobService.save(tbJob);

        tbJobService.removeById(111111111L);

        tbJobService.save(tbJob);

        System.out.println(JsonUtil.encode(tbJobService.getById(111111111L)));

    }


}
