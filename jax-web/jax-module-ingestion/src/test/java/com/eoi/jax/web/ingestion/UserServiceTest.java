package com.eoi.jax.web.ingestion;

import com.eoi.jax.web.repository.entity.TbUser;
import com.eoi.jax.web.repository.service.TbUserService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class UserServiceTest {

    @Autowired
    private TbUserService userService;

    @Test
    public void test() {
        long id = System.currentTimeMillis();
        TbUser createReq = new TbUser();
        createReq.setName("");
        createReq.setGuid("");
        createReq.setPlatform("");
        createReq.setAccount("");
        createReq.setPassword("");
        createReq.setSalt("");
        createReq.setEmail("");
        createReq.setPhone("");
        createReq.setRemark("");
        createReq.setDescription("");
        createReq.setIsDeleted(0);
        createReq.setCreateTime(new Date());
        createReq.setUpdateTime(new Date());
        createReq.setCreateUser(0L);
        createReq.setUpdateUser(0L);
        createReq.setName("junit-test-name");
        createReq.setId(id);

        // 测试插入
        boolean flag = userService.save(createReq);
        Assert.assertTrue(flag);

        // 测试查询
        TbUser queryResp = userService.getById(id);
        Assert.assertNotNull(queryResp);
        Assert.assertEquals("junit-test-name", queryResp.getName());

        // 测试更新
        TbUser updateReq = queryResp;
        updateReq.setName("交易分析-1");
        flag = userService.updateById(updateReq);
        Assert.assertTrue(flag);

        // 测试删除
        flag = userService.removeById(id);
        Assert.assertTrue(flag);

    }

}
