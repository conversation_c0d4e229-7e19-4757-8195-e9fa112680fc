package com.eoi.jax.web.ingestion;

import com.eoi.jax.web.repository.entity.TbStorageClusterHistory;
import com.eoi.jax.web.repository.service.TbStorageClusterHistoryService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class StorageClusterHistoryServiceTest {

    @Autowired
    private TbStorageClusterHistoryService storageClusterHistoryService;

    @Test
    public void test() {
        long id = System.currentTimeMillis();
        TbStorageClusterHistory createReq = new TbStorageClusterHistory();
        createReq.setName("junit-test-name");
        createReq.setStorageClusterId(id);
        createReq.setOptsId(0L);
        createReq.setAppId(0L);
        createReq.setAppName("");
        createReq.setScript("");
        createReq.setKafkaDsId(0L);
        createReq.setCkDsId(0L);
        createReq.setCkMaxConnection(0);
        createReq.setRegisterCenterId(0L);
        createReq.setNacosGroupName("");
        createReq.setNacosDataId("");
        createReq.setClusterId(0L);
        createReq.setResourceQueue("");
        createReq.setInstanceNum(0);
        createReq.setCpuNum(0);
        createReq.setMemory(0);
        createReq.setSetting("");
        createReq.setStatus("");
        createReq.setIsPublished(0);
        createReq.setAppStatus("");
        createReq.setLastStartTime(new Date());
        createReq.setErrMsg("");
        createReq.setIsDeleted(0);
        createReq.setCreateTime(new Date());
        createReq.setUpdateTime(new Date());
        createReq.setCreateUser(0L);
        createReq.setUpdateUser(0L);
        createReq.setId(id);


        // 测试插入
        boolean flag = storageClusterHistoryService.save(createReq);
        Assert.assertTrue(flag);

        // 测试查询
        TbStorageClusterHistory queryResp = storageClusterHistoryService.getById(id);
        Assert.assertNotNull(queryResp);
        Assert.assertEquals("junit-test-name", queryResp.getName());

        // 测试更新
        TbStorageClusterHistory updateReq = queryResp;
        updateReq.setName("交易分析-1");
        flag = storageClusterHistoryService.updateById(updateReq);
        Assert.assertTrue(flag);

        // 测试删除
        flag = storageClusterHistoryService.removeById(id);
        Assert.assertTrue(flag);

    }

}
