package com.eoi.jax.web.ingestion;

import com.eoi.jax.web.core.common.enumrate.RunningStatusEnum;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.common.enumrate.IngestionTypeEnum;
import com.eoi.jax.web.repository.entity.TbIngestion;
import com.eoi.jax.web.repository.service.TbIngestionService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class IngestionServiceTest {

    @Autowired
    private TbIngestionService tbIngestionService;

    @Test
    public void test() {
        // 测试插入
        TbIngestion tbIngestion = new TbIngestion();
        tbIngestion.setBusinessFlowId(1L);
        tbIngestion.setName("交易分析");
        tbIngestion.setIngestionType(IngestionTypeEnum.FILE.code());
        tbIngestion.setDescription("");
        tbIngestion.setCellId(1L);
        tbIngestion.setSetting("{}");
//        tbIngestion.setStatus("");
        tbIngestion.setIsPublished(0);
        tbIngestion.setTbId(1L);
        ModelBeanUtil.setCreateDefaultValue(tbIngestion);
        boolean save = tbIngestionService.save(tbIngestion);
        Assert.assertTrue(save);

        // 测试查询
        TbIngestion tbIngestion1 = tbIngestionService.getById(tbIngestion.getId());
        Assert.assertNotNull(tbIngestion1);
        Assert.assertEquals(1L, tbIngestion1.getBusinessFlowId().longValue());
        Assert.assertEquals("交易分析", tbIngestion1.getName());

        // 测试更新
        tbIngestion.setBusinessFlowId(2L);
        tbIngestion.setStatus(RunningStatusEnum.RUNNING.code());
        tbIngestion.setIsPublished(1);
        boolean b = tbIngestionService.updateById(tbIngestion);
        Assert.assertTrue(b);
        TbIngestion tbIngestion2 = tbIngestionService.getById(tbIngestion.getId());
        Assert.assertEquals(2L, tbIngestion2.getBusinessFlowId().longValue());
        Assert.assertEquals(RunningStatusEnum.RUNNING.code(), tbIngestion2.getStatus());
        Assert.assertEquals(1, tbIngestion2.getIsPublished().intValue());

        // 测试删除
        boolean b1 = tbIngestionService.removeById(tbIngestion2.getId());
        Assert.assertTrue(b1);
        TbIngestion tbIngestion3 = tbIngestionService.getById(tbIngestion.getId());
        Assert.assertNull(tbIngestion3);

    }


}
