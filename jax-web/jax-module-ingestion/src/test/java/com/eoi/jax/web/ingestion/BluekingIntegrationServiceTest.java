package com.eoi.jax.web.ingestion;

import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.repository.entity.TbBluekingIntegration;
import com.eoi.jax.web.repository.service.TbBluekingIntegrationService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class BluekingIntegrationServiceTest {

    @Resource
    private TbBluekingIntegrationService tbBluekingIntegrationService;

    @Test
    public void test() {
        TbBluekingIntegration tbBluekingIntegration = new TbBluekingIntegration();
        tbBluekingIntegration.setBusinessFlowId(1L);
        tbBluekingIntegration.setName("haha");
        tbBluekingIntegration.setBkDsId(1L);
        ModelBeanUtil.setCreateDefaultValue(tbBluekingIntegration);

        // 测试插入
        boolean flag = tbBluekingIntegrationService.save(tbBluekingIntegration);
        Assert.assertTrue(flag);

        // 测试查询
        TbBluekingIntegration bluekingIntegration = tbBluekingIntegrationService.getById(tbBluekingIntegration.getId());
        Assert.assertNotNull(bluekingIntegration);
        Assert.assertEquals("haha", bluekingIntegration.getName());

        // 测试更新
        bluekingIntegration.setName("haha2");
        flag = tbBluekingIntegrationService.updateById(bluekingIntegration);
        Assert.assertTrue(flag);
        bluekingIntegration = tbBluekingIntegrationService.getById(tbBluekingIntegration.getId());

        Assert.assertNotNull(bluekingIntegration);
        Assert.assertEquals("haha2", bluekingIntegration.getName());

        // 测试删除
        boolean b = tbBluekingIntegrationService.removeById(bluekingIntegration.getId());
        Assert.assertTrue(b);

    }


}
