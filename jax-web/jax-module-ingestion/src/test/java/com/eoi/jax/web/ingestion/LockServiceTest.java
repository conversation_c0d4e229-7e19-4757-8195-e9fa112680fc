package com.eoi.jax.web.ingestion;

import com.eoi.jax.web.repository.entity.TbLock;
import com.eoi.jax.web.repository.service.TbLockService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class LockServiceTest {

    @Autowired
    private TbLockService lockService;

    @Test
    public void test() {
        long id = System.currentTimeMillis();
        TbLock createReq = new TbLock();
        createReq.setResource("junit-test-name");
        createReq.setCount(0);
        createReq.setOwner("1");
        createReq.setDescription("");
        createReq.setCreateTime(new Date());
        createReq.setUpdateTime(new Date());
        createReq.setTimeout(new Date());
        createReq.setId(id);

        // 测试插入
        boolean flag = lockService.save(createReq);
        Assert.assertTrue(flag);

        // 测试查询
        TbLock queryResp = lockService.getById(id);
        Assert.assertNotNull(queryResp);
        Assert.assertEquals("junit-test-name", queryResp.getResource());

        // 测试更新
        TbLock updateReq = queryResp;
        updateReq.setResource("交易分析-1");
        flag = lockService.updateById(updateReq);
        Assert.assertTrue(flag);

        // 测试删除
        flag = lockService.removeById(id);
        Assert.assertTrue(flag);

    }

}
