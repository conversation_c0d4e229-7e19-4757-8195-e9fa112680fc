package com.eoi.jax.web.ingestion;

import com.eoi.jax.web.core.common.enumrate.RunningStatusEnum;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.repository.entity.TbStorageHiveHistory;
import com.eoi.jax.web.repository.service.TbStorageHiveHistoryService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023/4/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class StorageHiveHistoryServiceTest {

    @Autowired
    private TbStorageHiveHistoryService tbStorageHiveHistoryService;


    @Test
    public void test() {
        TbStorageHiveHistory tbStorageHiveHistory = new TbStorageHiveHistory();
        tbStorageHiveHistory.setId(1L);
        tbStorageHiveHistory.setStorageHiveId(1L);
        tbStorageHiveHistory.setBusinessFlowId(1L);
        tbStorageHiveHistory.setName("test");
        tbStorageHiveHistory.setKafkaTbId(1L);
        tbStorageHiveHistory.setStrategy("earliest");
        tbStorageHiveHistory.setConsumeGroup("123");
        tbStorageHiveHistory.setDataFormat("JSON");
        tbStorageHiveHistory.setIgnoreParseError(1);
        tbStorageHiveHistory.setIgnoreMissingField(1);
        tbStorageHiveHistory.setHiveTbId(3L);
        tbStorageHiveHistory.setHiveVersion("2.1.1");
        tbStorageHiveHistory.setSchemaMapping("{}");
        tbStorageHiveHistory.setFileSystemSetting("{}");
        tbStorageHiveHistory.setClusterId(1L);
        tbStorageHiveHistory.setYarnSessionId(1L);
        tbStorageHiveHistory.setOptsId(1L);
        tbStorageHiveHistory.setCustomSetting(null);
        tbStorageHiveHistory.setScript("");
        tbStorageHiveHistory.setStatus(RunningStatusEnum.UNSTART.code());
        tbStorageHiveHistory.setIsPublished(0);
        tbStorageHiveHistory.setPipelineId(null);
        tbStorageHiveHistory.setIsDeleted(0);
        ModelBeanUtil.setCreateDefaultValue(tbStorageHiveHistory);
        boolean save = tbStorageHiveHistoryService.save(tbStorageHiveHistory);
        Assert.assertTrue(save);

        // 测试查询
        TbStorageHiveHistory tbStorageHiveHistory1 = tbStorageHiveHistoryService.getById(tbStorageHiveHistory.getId());
        Assert.assertNotNull(tbStorageHiveHistory1);
        Assert.assertEquals(1L, tbStorageHiveHistory1.getBusinessFlowId().longValue());
        Assert.assertEquals(1L, tbStorageHiveHistory1.getKafkaTbId().longValue());
        Assert.assertEquals("earliest", tbStorageHiveHistory1.getStrategy());
        Assert.assertEquals(1L, tbStorageHiveHistory1.getClusterId().longValue());


        // 测试更新
        tbStorageHiveHistory.setKafkaTbId(2L);
        tbStorageHiveHistory.setStrategy("latest");
        boolean update = tbStorageHiveHistoryService.updateById(tbStorageHiveHistory);
        Assert.assertTrue(update);
        TbStorageHiveHistory tbStorageHiveHistory2 = tbStorageHiveHistoryService.getById(tbStorageHiveHistory.getId());
        Assert.assertNotNull(tbStorageHiveHistory2);
        Assert.assertEquals(2L, tbStorageHiveHistory2.getKafkaTbId().longValue());
        Assert.assertEquals("latest", tbStorageHiveHistory2.getStrategy());

        // 测试删除
        boolean b = tbStorageHiveHistoryService.removeById(tbStorageHiveHistory2.getId());
        Assert.assertTrue(b);
        TbStorageHiveHistory tbStorageHive3 = tbStorageHiveHistoryService.getById(tbStorageHiveHistory2.getId());
        Assert.assertNull(tbStorageHive3);


    }

}
