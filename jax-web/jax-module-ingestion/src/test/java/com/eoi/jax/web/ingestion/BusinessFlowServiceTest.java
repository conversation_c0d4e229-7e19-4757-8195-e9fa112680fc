package com.eoi.jax.web.ingestion;

import com.eoi.jax.web.ingestion.model.businessflow.BusinessFlowCreateReq;
import com.eoi.jax.web.ingestion.model.businessflow.BusinessFlowResp;
import com.eoi.jax.web.ingestion.model.businessflow.BusinessFlowUpdateReq;
import com.eoi.jax.web.ingestion.service.BusinessFlowService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class BusinessFlowServiceTest {
    Logger logger = LoggerFactory.getLogger(BusinessFlowServiceTest.class);
    @Autowired
    private BusinessFlowService businessFlowService;

    @Test
    public void test() {
        BusinessFlowCreateReq businessFlowCreateReq = new BusinessFlowCreateReq();
        businessFlowCreateReq.setName("交易分析");

        // 测试插入
        BusinessFlowResp businessFlowResp = businessFlowService.create(businessFlowCreateReq);
        Assert.assertNotNull(businessFlowResp);

        // 测试查询
        BusinessFlowResp businessFlowResp1 = businessFlowService.get(businessFlowResp.getId());
        Assert.assertNotNull(businessFlowResp1);
        Assert.assertEquals("交易分析", businessFlowResp1.getName());

        // 测试更新
        BusinessFlowUpdateReq updateReq = new BusinessFlowUpdateReq();
        updateReq.setId(businessFlowResp1.getId());
        updateReq.setName("交易分析-1");
        BusinessFlowResp updateBusinessFlowResp = businessFlowService.update(updateReq);
        Assert.assertNotNull(updateBusinessFlowResp);
        Assert.assertEquals("交易分析-1", updateBusinessFlowResp.getName());

        // 测试删除
        BusinessFlowResp deleteBusinessFlowResp = businessFlowService.delete(updateBusinessFlowResp.getId());
        Assert.assertNotNull(deleteBusinessFlowResp);
        BusinessFlowResp deleteBusinessFlowResp1 = null;
        try {
            deleteBusinessFlowResp1 = businessFlowService.get(updateBusinessFlowResp.getId());
            Assert.assertNull(deleteBusinessFlowResp1);
        } catch (Exception e) {
            logger.info("deleteBusinessFlowResp1 is null 正确结果");
        }
    }

}
