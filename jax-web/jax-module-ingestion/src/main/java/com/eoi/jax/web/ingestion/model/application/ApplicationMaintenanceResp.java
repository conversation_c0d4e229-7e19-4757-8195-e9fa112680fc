package com.eoi.jax.web.ingestion.model.application;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class ApplicationMaintenanceResp  extends ProjectAuthRespModel implements IProjectAuthModel, IUserInfoExtensionModel {
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "主键id")
    @OpPrimaryKey
    private Long id;

    @Schema(description = "作业名称")
    @OpPrimaryName
    private String name;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "是否已发布")
    private Integer isPublished;

    @Schema(description = "执行器实例数")
    private Integer instanceCount;

    @Schema(description = "cpu")
    private Integer executorCpu;

    @Schema(description = "内存")
    private Integer executorMemory;

    @Schema(description = "资源队列")
    private String yarnQueue;

    @Schema(description = "集群id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long clusterId;

    @Schema(description = "运行集群名称")
    private String clusterName;

    @Schema(description = "marayarn框架id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long optsId;

    @Schema(description = "作业类型")
    private String appType;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "yarn应用id")
    private String yarnApplicationId;

    @Schema(description = "启动时间")
    private Date startTime;

    @Schema(description = "停止时间")
    private Date endTime;

    @Schema(description = "追踪作业运行地址")
    private String trackUrl;

    private Date createTime;

    private Date updateTime;

    @JsonSerialize(using = LongStringSerializer.class)
    private Long createUser;

    @JsonSerialize(using = LongStringSerializer.class)
    private Long updateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Integer isPublished) {
        this.isPublished = isPublished;
    }

    public Integer getInstanceCount() {
        return instanceCount;
    }

    public void setInstanceCount(Integer instanceCount) {
        this.instanceCount = instanceCount;
    }

    public Integer getExecutorCpu() {
        return executorCpu;
    }

    public void setExecutorCpu(Integer executorCpu) {
        this.executorCpu = executorCpu;
    }

    public Integer getExecutorMemory() {
        return executorMemory;
    }

    public void setExecutorMemory(Integer executorMemory) {
        this.executorMemory = executorMemory;
    }

    public String getYarnQueue() {
        return yarnQueue;
    }

    public void setYarnQueue(String yarnQueue) {
        this.yarnQueue = yarnQueue;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public Long getOptsId() {
        return optsId;
    }

    public void setOptsId(Long optsId) {
        this.optsId = optsId;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getYarnApplicationId() {
        return yarnApplicationId;
    }

    public void setYarnApplicationId(String yarnApplicationId) {
        this.yarnApplicationId = yarnApplicationId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getTrackUrl() {
        return trackUrl;
    }

    public void setTrackUrl(String trackUrl) {
        this.trackUrl = trackUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
