package com.eoi.jax.web.ingestion.enumrate;

import com.eoi.jax.web.core.common.constant.ICodeEnum;

/**
 * <AUTHOR>
 * @date 2023/3/15
 */
public enum OpTypeEnum implements ICodeEnum {
    START("start"),
    STOP("stop"),
    GET("get"),
    LIST("list"),
    DELETE("delete");

    private final String code;

    OpTypeEnum(String code) {
        this.code = code;
    }

    @Override
    public String code() {
        return code;
    }
}
