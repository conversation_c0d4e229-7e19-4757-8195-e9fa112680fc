package com.eoi.jax.web.ingestion.model.tag;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbTag;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public class TagQueryReq extends BaseQueryReq<TbTag> {

    private TagQueryFilterReq filter = new TagQueryFilterReq();

    private TagQuerySortReq sort = new TagQuerySortReq();

    @Override
    public TagQueryFilterReq getFilter() {
        return filter;
    }

    @Override
    public TagQuerySortReq getSort() {
        return sort;
    }


    public void setFilter(TagQueryFilterReq filter) {
        this.filter = filter;
    }

    public void setSort(TagQuerySortReq sort) {
        this.sort = sort;
    }
}
