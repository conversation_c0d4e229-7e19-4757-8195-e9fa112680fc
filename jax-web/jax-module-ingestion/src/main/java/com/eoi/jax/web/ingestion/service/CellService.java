package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.core.common.exception.ParamValidationException;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.ingestion.model.cell.CellCreateReq;
import com.eoi.jax.web.ingestion.model.cell.CellQueryReq;
import com.eoi.jax.web.ingestion.model.cell.CellResp;
import com.eoi.jax.web.ingestion.model.cell.CellUpdateReq;
import com.eoi.jax.web.ingestion.model.datasource.connection.DatasourceConnectionCheckResp;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public interface CellService {
    /**
     * 分页查询数据中心
     *
     * @param req
     * @return
     */
    Paged<CellResp> query(CellQueryReq req);

    /**
     * 获取cell地址
     * @param resp
     * @return
     */
    String[] fetchCellUrl(CellResp resp);

    /**
     * 根据dsId获取数据
     *
     * @param dsId
     * @return
     */
    List<CellResp> getByDsId(Long dsId);

    /**
     * 根据id查询数据中心
     *
     * @param id
     * @return
     */
    CellResp get(Long id);


    /**
     * 创建数据中心
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    CellResp create(CellCreateReq req);


    /**
     * 更新数据中心
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    CellResp update(CellUpdateReq req);


    /**
     * 删除数据中心
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    CellResp delete(Long id);

    /**
     * 数据中心全量查询
     * @param type cell类型
     * @return
     */
    List<CellResp> all(String type);


    /**
     * 关联使用
     *
     * @param id
     * @return
     */
    List<UsageResp> usage(Long id);


    /**
     * 测试连接并更新数据
     *
     * @param id
     * @return
     * @throws ParamValidationException
     */
    DatasourceConnectionCheckResp connectionCheck(Long id) throws ParamValidationException;

    /**
     * 根据cell网关id获取cell地址
     * @param id
     * @return
     */
    List<String> buildCellUrl(Long id);

}
