package com.eoi.jax.web.ingestion.model.process;

import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.ingestion.provider.manager.PipelineConfigOpts;
import com.eoi.jax.web.repository.entity.TbProcess;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/6
 */
public class ProcessResp extends ProjectAuthRespModel
        implements IRespModel<TbProcess>, IProjectAuthModel, IUserInfoExtensionModel {
    @OpPrimaryKey
    private Long id;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long businessFlowId;
    @OpPrimaryName
    private String name;
    private Boolean isPublished;
    private Integer version;
    private String status;
    private String description;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long pipelineId;
    private String pipelineName;
    private String pipelineType;
    private Map<String, Object> pipelineConfig;
    private Map<String, Object> pipelineUi;
    private String pipelineStatus;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long clusterId;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long optsId;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long yarnSessionId;
    private String extJarName;
    private String startCmd;
    private Date createTime;
    private Date updateTime;
    private Long createUser;
    private Long updateUser;
    private List<String> tags;

    @Override
    public ProcessResp fromEntity(TbProcess entity) {
        IRespModel.super.fromEntity(entity);
        this.setPipelineConfig(JsonUtil.decode2Map(entity.getPipelineConfig()));
        this.setPipelineUi(JsonUtil.decode2Map(entity.getPipelineUi()));
        if (CollUtil.isNotEmpty(this.getPipelineConfig())) {
            Map<String, Object> opts = PipelineConfig.fromString(entity.getPipelineConfig()).getOpts();
            this.setExtJarName(CollUtil.get(PipelineConfigOpts.fromMap(opts).getExtJars(), 0));
        }
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public String getPipelineType() {
        return pipelineType;
    }

    public void setPipelineType(String pipelineType) {
        this.pipelineType = pipelineType;
    }

    public Map<String, Object> getPipelineConfig() {
        return pipelineConfig;
    }

    public void setPipelineConfig(Map<String, Object> pipelineConfig) {
        this.pipelineConfig = pipelineConfig;
    }

    public Map<String, Object> getPipelineUi() {
        return pipelineUi;
    }

    public void setPipelineUi(Map<String, Object> pipelineUi) {
        this.pipelineUi = pipelineUi;
    }

    public String getPipelineStatus() {
        return pipelineStatus;
    }

    public void setPipelineStatus(String pipelineStatus) {
        this.pipelineStatus = pipelineStatus;
    }

    public Boolean getPublished() {
        return isPublished;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public Long getOptsId() {
        return optsId;
    }

    public void setOptsId(Long optsId) {
        this.optsId = optsId;
    }

    public Long getYarnSessionId() {
        return yarnSessionId;
    }

    public void setYarnSessionId(Long yarnSessionId) {
        this.yarnSessionId = yarnSessionId;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getStartCmd() {
        return startCmd;
    }

    public void setStartCmd(String startCmd) {
        this.startCmd = startCmd;
    }

    public String getExtJarName() {
        return extJarName;
    }

    public void setExtJarName(String extJarName) {
        this.extJarName = extJarName;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }
}
