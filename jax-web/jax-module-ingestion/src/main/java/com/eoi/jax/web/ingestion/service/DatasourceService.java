package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.core.common.exception.ParamValidationException;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceCreateReq;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceQueryReq;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceResp;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceUpdateReq;
import com.eoi.jax.web.ingestion.model.datasource.connection.DatasourceConnectionCheckReq;
import com.eoi.jax.web.ingestion.model.datasource.connection.DatasourceConnectionCheckResp;
import com.eoi.jax.web.repository.entity.TbDatasource;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public interface DatasourceService {
    /**
     * 分页查询数据中心
     *
     * @param req
     * @return
     */
    Paged<DatasourceResp> query(DatasourceQueryReq req);

    /**
     * 根据平台类型获取Datasource
     * @param platform 平台类型
     * @return
     */
    List<DatasourceResp> listByPlatform(String platform);

    /**
     * 根据id查询数据中心
     *
     * @param id
     * @return
     */
    DatasourceResp get(Long id);

    /**
     * 根据code查询数据源
     * @param code
     * @return
     */
    DatasourceResp getByCode(String code);

    /**
     * 创建数据中心
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    DatasourceResp create(DatasourceCreateReq req);


    /**
     * 更新数据中心
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    DatasourceResp update(DatasourceUpdateReq req);


    /**
     * 删除数据中心
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    DatasourceResp delete(Long id);

    /**
     * 数据中心全量查询
     *
     * @return
     */
    List<DatasourceResp> all();

    /**
     * 测试连接
     *
     * @param req
     * @return
     * @throws ParamValidationException
     */
    DatasourceConnectionCheckResp connectionCheck(DatasourceConnectionCheckReq req) throws ParamValidationException;

    /**
     * 测试连接并更新数据
     *
     * @param id
     * @param updateStatusToDb
     * @return
     * @throws ParamValidationException
     */
    DatasourceConnectionCheckResp connectionCheck(Long id, boolean updateStatusToDb) throws ParamValidationException;

    /**
     * 刷新所有节点
     * @param req
     * @return
     */
    Map<String, Object> refreshServerNode(DatasourceConnectionCheckReq req);

    /**
     * 关联使用
     *
     * @param id
     * @return
     */
    List<UsageResp> usage(Long id);

    /**
     * 注册为grafana数据源
     * @param id
     * @return
     */
    Boolean registerGrafanaDs(Long id);

    /**
     * 注册为grafana数据源
     * @return
     */
    List<TbDatasource> registerGrafanaDs();
}
