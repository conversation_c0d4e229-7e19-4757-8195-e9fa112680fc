package com.eoi.jax.web.ingestion.model.ingestionmanager;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbIngestion;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
public class IngestionCloneReq implements ICreateModel<TbIngestion> {

    @Schema(description = "源数据集成ID")
    @NotNull(message = "源数据集成id不能为空")
    private Long id;

    @Schema(description = "业务流程id")
    @NotNull(message = "业务流程id不能为空")
    private Long businessFlowId;

    @Schema(description = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public TbIngestion toEntity() {
        return toEntity(new TbIngestion());
    }
}
