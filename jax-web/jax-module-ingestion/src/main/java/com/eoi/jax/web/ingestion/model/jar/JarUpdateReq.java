package com.eoi.jax.web.ingestion.model.jar;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.ingestion.provider.filesystem.JarFileUtil;
import com.eoi.jax.web.repository.entity.TbJar;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
public class JarUpdateReq extends JarUploadReq implements IUpdateModel<TbJar> {
    private Long id;

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public TbJar toEntity(TbJar tbJar) {
        TbJar jar = IUpdateModel.super.toEntity(tbJar);
        jar.setJarUuid(JarFileUtil.genJarUuid());
        return jar;
    }
}
