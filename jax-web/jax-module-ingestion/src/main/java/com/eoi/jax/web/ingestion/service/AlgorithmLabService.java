package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.core.service.IBaseProjectAuthService;
import com.eoi.jax.web.ingestion.model.algorithmlab.AlgorithmLabCreateReq;
import com.eoi.jax.web.ingestion.model.algorithmlab.AlgorithmLabQueryReq;
import com.eoi.jax.web.ingestion.model.algorithmlab.AlgorithmLabResp;
import com.eoi.jax.web.ingestion.model.algorithmlab.AlgorithmLabUpdateReq;
import com.eoi.jax.web.ingestion.model.datasource.connection.DatasourceConnectionCheckResp;
import com.eoi.jax.web.repository.entity.TbAlgorithmLab;
import com.eoi.jax.web.repository.service.TbAlgorithmLabService;

/**
 * <AUTHOR>
 * @date 2024/2/20
 */
public interface AlgorithmLabService extends IBaseProjectAuthService<
    TbAlgorithmLabService,
    TbAlgorithmLab,
    AlgorithmLabResp,
    AlgorithmLabCreateReq,
    AlgorithmLabUpdateReq,
    AlgorithmLabQueryReq> {

    /**
     * 算法实验室创建
     *
     * @param req
     * @return
     */
    @Override
    AlgorithmLabResp create(AlgorithmLabCreateReq req);

    /**
     * 更新算法实验室
     *
     * @param req
     * @return
     */
    @Override
    AlgorithmLabResp update(AlgorithmLabUpdateReq req);

    /**
     * 算法实验室删除
     *
     * @param id
     * @return
     */
    @Override
    AlgorithmLabResp delete(Long id);

    /**
     * 检查Jupyterhub状态
     *
     * @return
     */
    DatasourceConnectionCheckResp checkJupyterhubStatus();

    /**
     * 获取Jupyterhub访问地址
     *
     * @param id
     * @return
     */
    String jupyterhubAccessUrl(Long id);

    /**
     * 获取Jupyterhub用户
     *
     * @param jaxToken
     * @return
     */
    String jupyterhubUser(String jaxToken);

    /**
     * 同步所有Jupyterhub用户状态
     */
    void syncAllJupyterhubServerStatus();

    /**
     * 同步单个Jupyterhub用户状态
     *
     * @param id
     * @return
     */
    AlgorithmLabResp syncOneJupyterhubServerStatus(Long id);

    /**
     * 停止Jupyterhub服务
     * @param id
     * @return
     */
    AlgorithmLabResp stopJupyterhubServer(Long id);
}
