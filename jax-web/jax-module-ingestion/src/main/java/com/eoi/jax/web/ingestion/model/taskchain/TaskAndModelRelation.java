package com.eoi.jax.web.ingestion.model.taskchain;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/6/14
 * @Desc:
 **/
public class TaskAndModelRelation {
    private LinkNodeInfo nodeInfo;
    private Map<Long, ModelInfo> modelMap;
    private List<Long> inputModel = new ArrayList<>();

    public LinkNodeInfo getNodeInfo() {
        return nodeInfo;
    }

    public void setNodeInfo(LinkNodeInfo nodeInfo) {
        this.nodeInfo = nodeInfo;
    }

    public Map<Long, ModelInfo> getModelMap() {
        return modelMap;
    }

    public void setModelMap(Map<Long, ModelInfo> modelMap) {
        this.modelMap = modelMap;
    }

    public List<Long> getInputModel() {
        return inputModel;
    }

    public void setInputModel(List<Long> inputModel) {
        this.inputModel = inputModel;
    }
}
