package com.eoi.jax.web.ingestion.model.datacenter;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbDataCenter;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class DataCenterQueryReq extends BaseQueryReq<TbDataCenter> {

    private DataCenterQueryFilterReq filter = new DataCenterQueryFilterReq();
    private DataCenterQuerySortReq sort = new DataCenterQuerySortReq();

    @Override
    public IFilterReq<TbDataCenter> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbDataCenter> getSort() {
        return sort;
    }
}
