package com.eoi.jax.web.ingestion.model.storagehive;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbStorageHive;

/**
 * <AUTHOR>
 * @date 2023/3/31
 */
public class StorageHiveQuerySortReq implements ISortReq<TbStorageHive> {

    private String createTime;

    private String updateTime;

    @Override
    public QueryWrapper<TbStorageHive> order(QueryWrapper<TbStorageHive> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbStorageHive::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbStorageHive::getUpdateTime);
        return wrapper;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
