package com.eoi.jax.web.ingestion.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.core.service.BaseService;
import com.eoi.jax.web.core.util.ValidatorUtils;
import com.eoi.jax.web.ingestion.model.jaxclusterfile.JaxClusterFileCreateReq;
import com.eoi.jax.web.ingestion.model.jaxclusterfile.JaxClusterFileQueryReq;
import com.eoi.jax.web.ingestion.model.jaxclusterfile.JaxClusterFileResp;
import com.eoi.jax.web.ingestion.model.jaxclusterfile.JaxClusterFileUpdateReq;
import com.eoi.jax.web.ingestion.service.JaxClusterFileService;
import com.eoi.jax.web.repository.entity.TbJaxClusterFile;
import com.eoi.jax.web.repository.entity.TbUser;
import com.eoi.jax.web.repository.service.TbJaxClusterFileService;
import com.eoi.jax.web.repository.service.TbUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
@Service
public class JaxClusterFileServiceImpl extends BaseService<
        TbJaxClusterFileService,
        TbJaxClusterFile,
        JaxClusterFileResp,
        JaxClusterFileCreateReq,
        JaxClusterFileUpdateReq,
        JaxClusterFileQueryReq> implements JaxClusterFileService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StorageClusterServiceImpl.class.getName());


    @Autowired
    private TbJaxClusterFileService tbJaxClusterFileService;

    @Resource
    private TbUserService tbUserService;


    public JaxClusterFileServiceImpl(@Autowired TbJaxClusterFileService tbJaxClusterFileService) {
        super(tbJaxClusterFileService);
    }

    /**
     * 新增
     *
     * @param req
     * @return
     */
    @Override
    public JaxClusterFileResp create(JaxClusterFileCreateReq req) {
        ValidatorUtils.validateEntity(req);
        return super.create(req);
    }

    /**
     * 更新
     *
     * @param req
     * @return
     */
    @Override
    public JaxClusterFileResp update(JaxClusterFileUpdateReq req) {
        ValidatorUtils.validateEntity(req);
        return super.update(req);
    }

    @Override
    public List<JaxClusterFileResp> getAllVersions(String type, Long recordId, String fileName) {
        List<TbJaxClusterFile> list = tbJaxClusterFileService.getAllVersions(type, recordId, fileName);

        List<JaxClusterFileResp> results = new LinkedList<>();
        // 查询所有用户，丰富用户名到结果中
        if (CollUtil.isNotEmpty(list)) {
            List<TbUser> userList = tbUserService.list();
            results = list.stream().map(x -> {
                JaxClusterFileResp resp = new JaxClusterFileResp().fromEntity(x);
                for (TbUser user : userList) {
                    if (user.getId().equals(x.getCreateUser())) {
                        resp.setCreateUserName(user.getName());
                    }
                    if (user.getId().equals(x.getUpdateUser())) {
                        resp.setUpdateUserName(user.getName());
                    }
                }
                return resp;
            }).collect(Collectors.toList());
        }
        return results;
    }

}
