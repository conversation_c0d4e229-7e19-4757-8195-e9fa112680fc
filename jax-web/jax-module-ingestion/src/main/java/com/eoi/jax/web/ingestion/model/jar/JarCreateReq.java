package com.eoi.jax.web.ingestion.model.jar;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.ingestion.provider.filesystem.JarFileUtil;
import com.eoi.jax.web.repository.entity.TbJar;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
public class JarCreateReq extends JarUploadReq implements ICreateModel<TbJar> {

    @Override
    public TbJar toEntity() {
        TbJar jar = toEntity(new TbJar());
        jar.setJarUuid(JarFileUtil.genJarUuid());
        return jar;
    }
}
