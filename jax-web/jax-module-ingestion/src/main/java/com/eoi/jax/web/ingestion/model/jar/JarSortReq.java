package com.eoi.jax.web.ingestion.model.jar;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbJar;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
public class JarSortReq implements ISortReq<TbJar> {
    private String jarName;
    private String jarType;
    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbJar> order(QueryWrapper<TbJar> wrapper) {
        wrapper.lambda()
                .orderBy(isOrder(jarName), isAsc(jarName), TbJar::getJarName)
                .orderBy(isOrder(jarType), isAsc(jarType), TbJar::getJarType)
                .orderBy(isOrder(createTime), isAsc(createTime), TbJar::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbJar::getUpdateTime)
                .orderByDesc(TbJar::getId);
        return wrapper;
    }

    public String getJarName() {
        return jarName;
    }

    public void setJarName(String jarName) {
        this.jarName = jarName;
    }

    public String getJarType() {
        return jarType;
    }

    public void setJarType(String jarType) {
        this.jarType = jarType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
