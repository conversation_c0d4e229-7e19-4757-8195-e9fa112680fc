package com.eoi.jax.web.ingestion.model.flinkmetric;

import com.eoi.jax.web.core.util.JaxDateUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * JobIdRangeReq
 *
 * <AUTHOR>
 * @date 2023/4/23 16:05
*/
public class JobIdRangeReq {

    private String prometheusUrl;

    @Schema(description = "FlinkJobID")
    private String jobId;

    @Schema(description = "开始时间,格式：yyyy-MM-dd'T'HH:mm:ss.SSSZZZ")
    private String beginTime;

    @Schema(description = "结束时间,格式：yyyy-MM-dd'T'HH:mm:ss.SSSZZZ")
    private String endTime;

    private Long startSec;
    private Long endSec;

    @Schema(description = "图表组件宽度，单位：px")
    @NotNull(message = "图表组件宽度不能为空")
    @Min(value = 1, message = "图表组件宽度必须大于等于1")
    private Integer width;

    public JobIdRangeReq() { }

    public JobIdRangeReq(String prometheusUrl, String jobId, Long start, Long end, Integer width) {
        this.prometheusUrl = prometheusUrl;
        this.jobId = jobId;
        this.startSec = start;
        this.endSec = end;
        this.width = width;
    }

    public Long getStartSec() {
        return parseIsoAsTimeSec(beginTime);
    }

    public void setStartSec(Long startSec) {
        this.startSec = startSec;
    }

    public Long getEndSec() {
        return parseIsoAsTimeSec(endTime);
    }

    public void setEndSec(Long endSec) {
        this.endSec = endSec;
    }

    public String getPrometheusUrl() {
        return prometheusUrl;
    }

    public void setPrometheusUrl(String prometheusUrl) {
        this.prometheusUrl = prometheusUrl;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    private Long parseIsoAsTimeSec(String isoTimeStr) {
        Long timeSec = null;
        if (null != isoTimeStr) {
            timeSec = JaxDateUtil.parseIso(isoTimeStr).getTime() / 1000;
        }
        return timeSec;
    }


}
