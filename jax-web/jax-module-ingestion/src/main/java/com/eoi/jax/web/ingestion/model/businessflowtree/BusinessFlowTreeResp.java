package com.eoi.jax.web.ingestion.model.businessflowtree;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.repository.entity.TbBusinessFlowTree;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class BusinessFlowTreeResp extends ProjectAuthRespModel implements IRespModel<TbBusinessFlowTree>,
        IProjectAuthModel, IUserInfoExtensionModel {
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "业务流程id")
    private Long businessFlowId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "关联类型")
    private String referenceType;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "关联数据id")
    private Long referenceId;

    @Schema(description = "父级id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long parentId;

    @Schema(description = "父级ids，用/分割")
    private String parentPath;

    @Schema(description = "树节点类型")
    private String category;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "是否已发布")
    private Integer isPublished;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    /**
     * 项目资源类型
     */
    @Schema(description = "项目资源类型")
    private String resourceType;
    @Override
    public BusinessFlowTreeResp fromEntity(TbBusinessFlowTree table) {
        IRespModel.super.fromEntity(table);
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceType() {
        return referenceType;
    }

    public void setReferenceType(String referenceType) {
        this.referenceType = referenceType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public Long getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(Long referenceId) {
        this.referenceId = referenceId;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Integer isPublished) {
        this.isPublished = isPublished;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentPath() {
        return parentPath;
    }

    public void setParentPath(String parentPath) {
        this.parentPath = parentPath;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }
}
