package com.eoi.jax.web.ingestion.model.cluster;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.base.param.Parameter;
import com.eoi.jax.web.core.config.AppConfig;

/**
 * <AUTHOR>
 * @date 2023/2/21
 */
public class FlinkStandaloneCluster implements IClusterSetting, ISupportFlink, ISupportPython {
    @Parameter(
            label = "Flink Server",
            description = "Flink Job Manager地址，用于提交作业，如localhost:8081"
    )
    private String flinkServer;
    @Parameter(
            label = "Flink Web",
            description = "Flink Job Manager Web地址，如http://localhost:8081"
    )
    private String flinkWebUrl;
    @Parameter(
            label = "Python Home",
            description = "运行Python作业所需要的Python环境，\n" +
                    "需要在集群所有节点上部署中台的Python解释器环境，\n" +
                    "并设置Python解释器环境的绝对路径，例如/home/<USER>",
            optional = true
    )
    private String pythonEnv;
    @Parameter(
            label = "超时时间(毫秒)",
            description = "超时时间(毫秒)，超过超时间仍没有成功提交的作业将被取消提交"
    )
    private Long timeoutMs;

    @Override
    public void fillValue() {
        if (timeoutMs == null) {
            timeoutMs = 300000L;
        }
        if (StrUtil.isEmpty(flinkServer)) {
            flinkServer = "localhost:8081";
        }
        if (StrUtil.isEmpty(flinkWebUrl)) {
            flinkWebUrl = "http://localhost:8081";
        }
        if (StrUtil.isEmpty(pythonEnv)) {
            pythonEnv = AppConfig.DEFAULT_PYTHON_ENV;
        }
    }

    @Override
    public String getFlinkServer() {
        return flinkServer;
    }

    public void setFlinkServer(String flinkServer) {
        this.flinkServer = flinkServer;
    }

    public String getFlinkWebUrl() {
        return flinkWebUrl;
    }

    public void setFlinkWebUrl(String flinkWebUrl) {
        this.flinkWebUrl = flinkWebUrl;
    }

    @Override
    public String getPythonEnv() {
        return pythonEnv;
    }

    public void setPythonEnv(String pythonEnv) {
        this.pythonEnv = pythonEnv;
    }

    public Long getTimeoutMs() {
        return timeoutMs;
    }

    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }
}
