package com.eoi.jax.web.ingestion.model.registercenter;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.ingestion.model.datacenter.DataCenterResp;
import com.eoi.jax.web.repository.entity.TbRegisterCenter;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class RegisterCenterResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbRegisterCenter> {
    @Schema(description = "主键id")
    @OpPrimaryKey
    private Long id;

    @Schema(description = "注册中心名称")
    @OpPrimaryName
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "注册中心类型：（默认）NACOS, ZK, EUREKA")
    private String type;

    @Schema(description = "注册中心地址")
    private String address;

    @Schema(description = "注册中心命名空间")
    private String namespace;

    @Schema(description = "注册中心用户名")
    private String username;

    @Schema(description = "注册中心密码")
    private String password;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    @Schema(description = "数据中心列表")
    private List<DataCenterResp> dataCenterList = new LinkedList<>();

    public List<DataCenterResp> getDataCenterList() {
        return dataCenterList;
    }

    public void setDataCenterList(List<DataCenterResp> dataCenterList) {
        this.dataCenterList = dataCenterList;
    }

    @Override
    public RegisterCenterResp fromEntity(TbRegisterCenter table) {
        IRespModel.super.fromEntity(table);
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
