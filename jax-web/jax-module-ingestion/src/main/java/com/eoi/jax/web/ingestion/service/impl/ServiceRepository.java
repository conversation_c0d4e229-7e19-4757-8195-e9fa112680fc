package com.eoi.jax.web.ingestion.service.impl;

import com.eoi.jax.web.ingestion.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> zsc
 * @create 2023/2/7 14:05
 */
@Service
public class ServiceRepository {
    @Autowired
    private BusinessFlowTreeService businessFlowTreeService;

    @Autowired
    private BusinessFlowService businessFlowService;

    @Autowired
    private CellAgentService cellAgentService;

    @Autowired
    private CellService cellService;

    @Autowired
    private ClickhouseService clickhouseService;

    @Autowired
    private DataCenterService dataCenterService;

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Autowired
    private IngestionAgentService ingestionAgentService;

    @Autowired
    private IngestionJobService ingestionJobService;

    @Autowired
    private IngestionJobTaskService ingestionJobTaskService;

    @Autowired
    private IngestionService ingestionService;

    @Autowired
    private RegisterCenterService registerCenterService;

    @Autowired
    private StorageClusterService storageClusterService;

    @Autowired
    private TagRelationService tagRelationService;

    @Autowired
    private TagService tagService;

    public BusinessFlowTreeService getBusinessFlowTreeService() {
        return businessFlowTreeService;
    }

    public BusinessFlowService getBusinessFlowService() {
        return businessFlowService;
    }

    public CellAgentService getCellAgentService() {
        return cellAgentService;
    }

    public CellService getCellService() {
        return cellService;
    }

    public ClickhouseService getClickhouseService() {
        return clickhouseService;
    }

    public DataCenterService getDataCenterService() {
        return dataCenterService;
    }

    public ElasticsearchService getElasticsearchService() {
        return elasticsearchService;
    }

    public IngestionAgentService getIngestionAgentService() {
        return ingestionAgentService;
    }

    public IngestionJobService getIngestionJobService() {
        return ingestionJobService;
    }

    public IngestionJobTaskService getIngestionJobTaskService() {
        return ingestionJobTaskService;
    }

    public IngestionService getIngestionService() {
        return ingestionService;
    }

    public RegisterCenterService getRegisterCenterService() {
        return registerCenterService;
    }

    public StorageClusterService getStorageClusterService() {
        return storageClusterService;
    }

    public TagRelationService getTagRelationService() {
        return tagRelationService;
    }

    public TagService getTagService() {
        return tagService;
    }
}
