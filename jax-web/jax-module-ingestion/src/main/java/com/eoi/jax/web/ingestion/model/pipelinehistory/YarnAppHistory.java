package com.eoi.jax.web.ingestion.model.pipelinehistory;

import com.eoi.jax.web.core.client.yarn.YarnApplication;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/11
 */
public class YarnAppHistory {
    private String prometheusUrl;
    private String trackUrl;
    private String applicationId;
    private YarnApplication application;
    private List<YarnAppAttemptInfo> jobAttempts;

    public String getPrometheusUrl() {
        return prometheusUrl;
    }

    public void setPrometheusUrl(String prometheusUrl) {
        this.prometheusUrl = prometheusUrl;
    }

    public String getTrackUrl() {
        return trackUrl;
    }

    public void setTrackUrl(String trackUrl) {
        this.trackUrl = trackUrl;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public YarnApplication getApplication() {
        return application;
    }

    public void setApplication(YarnApplication application) {
        this.application = application;
        if (application != null) {
            this.applicationId = application.getId();
            this.trackUrl = application.getTrackingUrl();
        }
    }

    public List<YarnAppAttemptInfo> getJobAttempts() {
        return jobAttempts;
    }

    public void setJobAttempts(List<YarnAppAttemptInfo> jobAttempts) {
        this.jobAttempts = jobAttempts;
    }
}
