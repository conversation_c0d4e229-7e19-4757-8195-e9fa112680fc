package com.eoi.jax.web.ingestion.model.excel;

import com.baomidou.mybatisplus.annotation.TableField;

/**
 * @Author: tangy
 * @Date: 2023/11/30
 * @Desc:
 **/
public class ProcessExcelExportModel {
    /**
     * 业务流程id
     */
    private String businessFlowName;

    /**
     * 存储集群名称
     */
    private String name;

    private String pipelineName;

    private String pipelineType;
    private String description;

    @TableField
    private String startCmd;

    @TableField
    private String pipelineConfig;

    @TableField
    private String pipelineUi;


    /**
     * 集群
     */
    private String clusterName;
    /**
     * 集群
     */
    private String clusterType;

    /**
     * YarnSession 名称
     */
    private String yarnSessionName;

    /**
     * 框架
     */
    private String optsName;

    /**
     * 框架
     */
    private String optsType;

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public String getPipelineType() {
        return pipelineType;
    }

    public void setPipelineType(String pipelineType) {
        this.pipelineType = pipelineType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStartCmd() {
        return startCmd;
    }

    public void setStartCmd(String startCmd) {
        this.startCmd = startCmd;
    }

    public String getPipelineConfig() {
        return pipelineConfig;
    }

    public void setPipelineConfig(String pipelineConfig) {
        this.pipelineConfig = pipelineConfig;
    }

    public String getPipelineUi() {
        return pipelineUi;
    }

    public void setPipelineUi(String pipelineUi) {
        this.pipelineUi = pipelineUi;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getClusterType() {
        return clusterType;
    }

    public void setClusterType(String clusterType) {
        this.clusterType = clusterType;
    }

    public String getYarnSessionName() {
        return yarnSessionName;
    }

    public void setYarnSessionName(String yarnSessionName) {
        this.yarnSessionName = yarnSessionName;
    }

    public String getOptsName() {
        return optsName;
    }

    public void setOptsName(String optsName) {
        this.optsName = optsName;
    }

    public String getOptsType() {
        return optsType;
    }

    public void setOptsType(String optsType) {
        this.optsType = optsType;
    }
}
