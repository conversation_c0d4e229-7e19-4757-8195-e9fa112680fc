package com.eoi.jax.web.ingestion.model.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Set;

/**
 * @Author: tangy
 * @Date: 2023/5/24
 * @Desc: 匹配增加标签
 **/
public class TaskAddTagSingleReq {
    @Schema(description = "记录id")
    private Long recodeId;
    @Schema(description = "标签名称集合")
    private Set<String> tags;

    public Long getRecodeId() {
        return recodeId;
    }

    public void setRecodeId(Long recodeId) {
        this.recodeId = recodeId;
    }

    public Set<String> getTags() {
        return tags;
    }

    public void setTags(Set<String> tags) {
        this.tags = tags;
    }
}
