package com.eoi.jax.web.ingestion.model.cell;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/2/6
 */
public class CellAgentInfoReq {

    @Schema(description = "网关名称")
    @NotNull(message = "网关名称不能为空")
    private Long cellId;

    @NotNull(message = "agentId不能为空")
    @Schema(description = "agentId")
    private Long agentId;

    public Long getCellId() {
        return cellId;
    }

    public void setCellId(Long cellId) {
        this.cellId = cellId;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

}
