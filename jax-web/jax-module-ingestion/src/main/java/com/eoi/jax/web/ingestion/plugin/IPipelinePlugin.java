package com.eoi.jax.web.ingestion.plugin;

import com.eoi.jax.manager.api.JobStartParam;
import com.eoi.jax.manager.api.JobStartResult;
import com.eoi.jax.manager.api.JobStopParam;
import com.eoi.jax.manager.api.JobStopResult;
import com.eoi.jax.web.ingestion.model.pipeline.PipelineCreateReq;
import com.eoi.jax.web.ingestion.model.pipeline.PipelineUpdateReq;
import com.eoi.jax.web.ingestion.provider.manager.PipelineCtx;

/**
 * 管线作业 Pipeline 插件
 * <p>
 * 在Pipeline的生命周期里注入插件逻辑
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
public interface IPipelinePlugin {

    /**
     * 创建请求
     *
     * @param req
     */
    void createReq(PipelineCreateReq req);

    /**
     * 更新请求
     *
     * @param req
     */
    void updateReq(PipelineUpdateReq req);

    /**
     * 启动请求
     *
     * @param pipeline
     */
    void startReq(PipelineCtx pipeline);

    /**
     * 停止请求
     *
     * @param pipeline
     */
    void stopReq(PipelineCtx pipeline);

    /**
     * 删除请求
     *
     * @param pipeline
     */
    void deleteReq(PipelineCtx pipeline);

    /**
     * 调试请求
     *
     * @param pipeline
     */
    void debugReq(PipelineCtx pipeline);

    /**
     * 准备启动
     *
     * @param param
     * @param pipeline
     */
    void beforeStart(JobStartParam param, PipelineCtx pipeline);

    /**
     * 启动结束
     *
     * @param result
     * @param pipeline
     */
    void afterStart(JobStartResult result, PipelineCtx pipeline);

    /**
     * 准备停止
     *
     * @param param
     * @param pipeline
     */
    void beforeStop(JobStopParam param, PipelineCtx pipeline);

    /**
     * 停止结束
     *
     * @param result
     * @param pipeline
     */
    void afterStop(JobStopResult result, PipelineCtx pipeline);

    /**
     * 准备删除
     *
     * @param param
     * @param pipeline
     */
    void beforeDelete(JobStopParam param, PipelineCtx pipeline);

    /**
     * 删除结束
     *
     * @param result
     * @param pipeline
     */
    void afterDelete(JobStopResult result, PipelineCtx pipeline);
}
