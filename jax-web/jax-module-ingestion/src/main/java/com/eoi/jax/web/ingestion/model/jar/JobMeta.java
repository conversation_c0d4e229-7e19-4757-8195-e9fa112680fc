package com.eoi.jax.web.ingestion.model.jar;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27
 * <p>
 * copy from com.eoi.jax.tool.JobMeta
 */
public class JobMeta {
    private String jobName;
    private List<TypeDescriptor> inTypes;
    private List<TypeDescriptor> outTypes;
    private Experimantal experimantal;
    private JobInfo jobInfo;
    private JobParameters parameters;
    // doc的二进制内容，不参与序列化
    @JsonIgnore
    private byte[] doc;
    // icon的二进制内容，不参与序列化
    @JsonIgnore
    private byte[] icon;

    public String getJobName() {
        return jobName;
    }

    public JobMeta setJobName(String jobName) {
        this.jobName = jobName;
        return this;
    }

    public List<TypeDescriptor> getInTypes() {
        return inTypes;
    }

    public JobMeta setInTypes(List<TypeDescriptor> inTypes) {
        this.inTypes = inTypes;
        return this;
    }

    public List<TypeDescriptor> getOutTypes() {
        return outTypes;
    }

    public JobMeta setOutTypes(List<TypeDescriptor> outTypes) {
        this.outTypes = outTypes;
        return this;
    }

    public Experimantal getExperimantal() {
        return experimantal;
    }

    public JobMeta setExperimantal(Experimantal experimantal) {
        this.experimantal = experimantal;
        return this;
    }

    public JobInfo getJobInfo() {
        return jobInfo;
    }

    public JobMeta setJobInfo(JobInfo jobInfo) {
        this.jobInfo = jobInfo;
        return this;
    }

    public JobParameters getParameters() {
        return parameters;
    }

    public JobMeta setParameters(JobParameters parameters) {
        this.parameters = parameters;
        return this;
    }

    public byte[] getDoc() {
        return doc;
    }

    public JobMeta setDoc(byte[] doc) {
        this.doc = doc;
        return this;
    }

    public byte[] getIcon() {
        return icon;
    }

    public JobMeta setIcon(byte[] icon) {
        this.icon = icon;
        return this;
    }

    public static class Experimantal {
        private String message;

        public String getMessage() {
            return message;
        }

        public Experimantal setMessage(String message) {
            this.message = message;
            return this;
        }
    }

    public static class TypeDescriptor {
        private String raw;
        private List<String> parameterizedTypes;
        private String description;

        public String getRaw() {
            return raw;
        }

        public TypeDescriptor setRaw(String raw) {
            this.raw = raw;
            return this;
        }

        public List<String> getParameterizedTypes() {
            return parameterizedTypes;
        }

        public TypeDescriptor setParameterizedTypes(List<String> parameterizedTypes) {
            this.parameterizedTypes = parameterizedTypes;
            return this;
        }

        public String getDescription() {
            return description;
        }

        public TypeDescriptor setDescription(String description) {
            this.description = description;
            return this;
        }
    }

    public static class JobInfo {
        private int apiVersion;
        private String name;
        private String type;
        private String role;
        private String category;
        private String display;
        private String description;
        private String doc;
        private String icon;
        private Boolean internal;

        public int getApiVersion() {
            return apiVersion;
        }

        public JobInfo setApiVersion(int apiVersion) {
            this.apiVersion = apiVersion;
            return this;
        }

        public String getName() {
            return name;
        }

        public JobInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getType() {
            return type;
        }

        public JobInfo setType(String type) {
            this.type = type;
            return this;
        }

        public String getRole() {
            return role;
        }

        public JobInfo setRole(String role) {
            this.role = role;
            return this;
        }

        public String getCategory() {
            return category;
        }

        public JobInfo setCategory(String category) {
            this.category = category;
            return this;
        }

        public String getDisplay() {
            return display;
        }

        public JobInfo setDisplay(String display) {
            this.display = display;
            return this;
        }

        public String getDescription() {
            return description;
        }

        public JobInfo setDescription(String description) {
            this.description = description;
            return this;
        }

        public String getDoc() {
            return doc;
        }

        public JobInfo setDoc(String doc) {
            this.doc = doc;
            return this;
        }

        public String getIcon() {
            return icon;
        }

        public JobInfo setIcon(String icon) {
            this.icon = icon;
            return this;
        }

        public Boolean getInternal() {
            return internal;
        }

        public JobInfo setInternal(Boolean internal) {
            this.internal = internal;
            return this;
        }
    }

    public static class JobParameters {
        private List<JobParameter> parameters;

        public List<JobParameter> getParameters() {
            return parameters;
        }

        public JobParameters setParameters(List<JobParameter> parameters) {
            this.parameters = parameters;
            return this;
        }
    }

    public static class JobParameter {
        private int apiVersion;
        private String name;
        private String label;
        private String[] type;
        private String description;
        private boolean optional;
        private String defaultValue;
        private String placeholder;
        private String[] candidates;
        private String inputType;
        private String range;
        private String regex;
        private int order;
        private boolean algorithmic;
        private String requireCondition;
        private String availableCondition;
        private String[] recommendations;
        private JobParameter listParameter;
        private List<JobParameter> objectParameters;
        private String trueLabel;
        private String falseLabel;
        private boolean objectTitle;

        public int getApiVersion() {
            return apiVersion;
        }

        public JobParameter setApiVersion(int apiVersion) {
            this.apiVersion = apiVersion;
            return this;
        }

        public String getName() {
            return name;
        }

        public JobParameter setName(String name) {
            this.name = name;
            return this;
        }

        public String getLabel() {
            return label;
        }

        public JobParameter setLabel(String label) {
            this.label = label;
            return this;
        }

        public String[] getType() {
            return type;
        }

        public JobParameter setType(String[] type) {
            this.type = type;
            return this;
        }

        public String getDescription() {
            return description;
        }

        public JobParameter setDescription(String description) {
            this.description = description;
            return this;
        }

        public boolean isOptional() {
            return optional;
        }

        public JobParameter setOptional(boolean optional) {
            this.optional = optional;
            return this;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public JobParameter setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
            return this;
        }

        public String getPlaceholder() {
            return placeholder;
        }

        public JobParameter setPlaceholder(String placeholder) {
            this.placeholder = placeholder;
            return this;
        }

        public String[] getCandidates() {
            return candidates;
        }

        public JobParameter setCandidates(String[] candidates) {
            this.candidates = candidates;
            return this;
        }

        public String getInputType() {
            return inputType;
        }

        public JobParameter setInputType(String inputType) {
            this.inputType = inputType;
            return this;
        }

        public String getRange() {
            return range;
        }

        public JobParameter setRange(String range) {
            this.range = range;
            return this;
        }

        public String getRegex() {
            return regex;
        }

        public JobParameter setRegex(String regex) {
            this.regex = regex;
            return this;
        }

        public int getOrder() {
            return order;
        }

        public JobParameter setOrder(int order) {
            this.order = order;
            return this;
        }

        public boolean isAlgorithmic() {
            return algorithmic;
        }

        public JobParameter setAlgorithmic(boolean algorithmic) {
            this.algorithmic = algorithmic;
            return this;
        }

        public String getRequireCondition() {
            return requireCondition;
        }

        public void setRequireCondition(String requireCondition) {
            this.requireCondition = requireCondition;
        }

        public String getAvailableCondition() {
            return availableCondition;
        }

        public void setAvailableCondition(String availableCondition) {
            this.availableCondition = availableCondition;
        }

        public String[] getRecommendations() {
            return recommendations;
        }

        public JobParameter setRecommendations(String[] recommendations) {
            this.recommendations = recommendations;
            return this;
        }

        public JobParameter getListParameter() {
            return listParameter;
        }

        public JobParameter setListParameter(JobParameter listParameter) {
            this.listParameter = listParameter;
            return this;
        }

        public List<JobParameter> getObjectParameters() {
            return objectParameters;
        }

        public JobParameter setObjectParameters(List<JobParameter> objectParameters) {
            this.objectParameters = objectParameters;
            return this;
        }

        public String getTrueLabel() {
            return trueLabel;
        }

        public JobParameter setTrueLabel(String trueLabel) {
            this.trueLabel = trueLabel;
            return this;
        }

        public String getFalseLabel() {
            return falseLabel;
        }

        public JobParameter setFalseLabel(String falseLabel) {
            this.falseLabel = falseLabel;
            return this;
        }

        public boolean isObjectTitle() {
            return objectTitle;
        }

        public JobParameter setObjectTitle(boolean objectTitle) {
            this.objectTitle = objectTitle;
            return this;
        }
    }
}
