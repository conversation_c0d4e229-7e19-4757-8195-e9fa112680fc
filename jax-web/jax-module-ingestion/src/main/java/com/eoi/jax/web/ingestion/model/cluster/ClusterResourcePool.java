package com.eoi.jax.web.ingestion.model.cluster;

import com.eoi.jax.web.core.client.flink.FlinkOverview;
import com.eoi.jax.web.core.client.spark.SparkOverview;
import com.eoi.jax.web.core.client.yarn.YarnOverview;

/**
 * <AUTHOR>
 * @date 2023/3/12
 */
public class ClusterResourcePool {
    private boolean got = false;
    private Double percent;
    private Double cpuPercent;
    private Double memoryPercent;
    private Double slotPercent;
    private FlinkOverview flink;
    private SparkOverview spark;
    private YarnOverview yarn;
    private Long cpuTotal;
    private Long cpuAvailable;
    private Long memoryTotal;
    private Long memoryAvailable;

    public Long getCpuTotal() {
        return cpuTotal;
    }

    public void setCpuTotal(Long cpuTotal) {
        this.cpuTotal = cpuTotal;
    }

    public Long getCpuAvailable() {
        return cpuAvailable;
    }

    public void setCpuAvailable(Long cpuAvailable) {
        this.cpuAvailable = cpuAvailable;
    }

    public Long getMemoryTotal() {
        return memoryTotal;
    }

    public void setMemoryTotal(Long memoryTotal) {
        this.memoryTotal = memoryTotal;
    }

    public Long getMemoryAvailable() {
        return memoryAvailable;
    }

    public void setMemoryAvailable(Long memoryAvailable) {
        this.memoryAvailable = memoryAvailable;
    }

    public boolean isGot() {
        return got;
    }

    public void setGot(boolean got) {
        this.got = got;
    }

    public boolean getFlinkGot() {
        return flink != null;
    }

    public boolean getSparkGot() {
        return spark != null;
    }

    public boolean getYarnGot() {
        return yarn != null;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public Double getCpuPercent() {
        return cpuPercent;
    }

    public void setCpuPercent(Double cpuPercent) {
        this.cpuPercent = cpuPercent;
    }

    public Double getMemoryPercent() {
        return memoryPercent;
    }

    public void setMemoryPercent(Double memoryPercent) {
        this.memoryPercent = memoryPercent;
    }

    public Double getSlotPercent() {
        return slotPercent;
    }

    public void setSlotPercent(Double slotPercent) {
        this.slotPercent = slotPercent;
    }

    public FlinkOverview getFlink() {
        return flink;
    }

    public SparkOverview getSpark() {
        return spark;
    }

    public YarnOverview getYarn() {
        return yarn;
    }

    public void setFlink(FlinkOverview flink) {
        this.flink = flink;
        this.slotPercent = (double) (100 *
                (flink.getSlotsTotal() - flink.getSlotsAvailable()) / flink.getSlotsTotal());
        this.percent = slotPercent;
    }

    public void setSpark(SparkOverview spark) {
        this.spark = spark;
        this.cpuPercent = (double) (100 * spark.getCoresUsed() / spark.getCores());
        this.memoryPercent = (double) (100 * spark.getMemoryUsed() / spark.getMemory());
        this.percent = Math.min(cpuPercent, memoryPercent);
        this.cpuTotal = spark.getCores() != null ? spark.getCores().longValue() : 0;
        if (spark.getCores() != null && spark.getCoresUsed() != null) {
            this.cpuAvailable = spark.getCores().longValue() - spark.getCoresUsed().longValue();
        }
        this.memoryTotal = spark.getMemory() != null ? spark.getMemory().longValue() : 0;
        if (spark.getMemory() != null && spark.getMemoryUsed() != null) {
            this.memoryAvailable = spark.getMemory().longValue() - spark.getMemoryUsed().longValue();
        }
    }

    public void setYarn(YarnOverview yarn) {
        this.yarn = yarn;
        this.cpuPercent = (double) (100 *
                (yarn.getTotalVirtualCores() - yarn.getAvailableVirtualCores()) / yarn.getTotalVirtualCores());
        this.memoryPercent = (double) (100 *
                (yarn.getTotalMB() - yarn.getAvailableMB()) / yarn.getTotalMB());
        this.percent = Math.min(cpuPercent, memoryPercent);
        this.cpuTotal = yarn.getTotalVirtualCores();
        this.cpuAvailable = yarn.getAvailableVirtualCores();
        this.memoryTotal = yarn.getTotalMB();
        this.memoryAvailable = yarn.getAvailableMB();
    }
}
