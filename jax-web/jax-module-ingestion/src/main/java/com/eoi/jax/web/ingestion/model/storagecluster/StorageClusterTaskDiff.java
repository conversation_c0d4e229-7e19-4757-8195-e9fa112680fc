package com.eoi.jax.web.ingestion.model.storagecluster;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class StorageClusterTaskDiff implements Serializable {
    @Schema(description = "taskId")
    private String taskId;

    @Schema(description = "task名称")
    private String taskName;

    @Schema(description = "本地task状态")
    private String taskStatus;

    @Schema(description = "nacos上配置的名称")
    private String nacosTaskName;

    public StorageClusterTaskDiff() {
    }

    public StorageClusterTaskDiff(String taskId, String taskName, String nacosTaskName) {
        this.taskId = taskId;
        this.taskName = taskName;
        this.nacosTaskName = nacosTaskName;
    }

    public String getTaskId() {
        return taskId;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getNacosTaskName() {
        return nacosTaskName;
    }

    public void setNacosTaskName(String nacosTaskName) {
        this.nacosTaskName = nacosTaskName;
    }
}
