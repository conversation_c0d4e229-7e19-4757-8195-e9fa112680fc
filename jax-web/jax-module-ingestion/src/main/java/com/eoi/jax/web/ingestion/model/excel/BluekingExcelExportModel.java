package com.eoi.jax.web.ingestion.model.excel;

import cn.hutool.json.JSONArray;

/**
 * @Author: tangy
 * @Date: 2023/11/30
 * @Desc:
 **/
public class BluekingExcelExportModel {
    /**
     * 业务流程id
     */
    private String businessFlowName;

    /**
     * 存储集群名称
     */
    private String name;

    /**
     * kafka模型id
     */
    private String bkDsName;

    /**
     * 映射配置
     */
    private JSONArray jsonArray;


    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBkDsName() {
        return bkDsName;
    }

    public void setBkDsName(String bkDsName) {
        this.bkDsName = bkDsName;
    }

    public JSONArray getJsonArray() {
        return jsonArray;
    }

    public void setJsonArray(JSONArray jsonArray) {
        this.jsonArray = jsonArray;
    }
}
