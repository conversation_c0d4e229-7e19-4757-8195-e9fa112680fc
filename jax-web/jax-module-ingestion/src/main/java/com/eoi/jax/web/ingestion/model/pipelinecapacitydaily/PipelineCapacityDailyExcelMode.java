package com.eoi.jax.web.ingestion.model.pipelinecapacitydaily;

import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2024/1/18
 */
public class PipelineCapacityDailyExcelMode extends AbstractExcelRowResp {

    @Schema(description = "管线作业名称")
    @Excel(name = "管线作业名称*", no = 0)
    private String pipelineAlias;

    /**
     * 最大CPU使用率(每分钟)与CPU配额比例
     */
    @Schema(description = "最大CPU使用率(每分钟)")
    @Excel(name = "最大CPU使用率(每分钟)", no = 1)
    private Long maxCpuRate;

    @Schema(description = "最大CPU使用率(每分钟)与CPU配额比")
    @Excel(name = "最大CPU使用率(每分钟)与CPU配额比", no = 2)
    private Long maxCpuRateQuotaRatio;

    /**
     * 平均CPU使用率(每分钟)与CPU配额比例
     */
    @Schema(description = "平均CPU使用率(每分钟)")
    @Excel(name = "平均CPU使用率(每分钟)", no = 3)
    private Long avgCpuRate;

    @Schema(description = "平均CPU使用率(每分钟)与CPU配额比")
    @Excel(name = "平均CPU使用率(每分钟)与CPU配额比", no = 4)
    private Long avgCpuRateQuotaRatio;

    /**
     * 数据处理量
     */
    @Schema(description = "数据处理量")
    @Excel(name = "数据处理量", no = 5)
    private Long recordCount;

    /**
     * 重启次数
     */
    @Schema(description = "重启次数")
    @Excel(name = "重启次数", no = 6)
    private Integer restartCount;

    /**
     * 最大fullGC次数(每分钟)
     */
    @Schema(description = "最大fullGC次数(每分钟)")
    @Excel(name = "最大fullGC次数(每分钟)", no = 7)
    private Integer maxFullGcCount;

    /**
     * 并行度
     */
    @Schema(description = "并行度")
    @Excel(name = "并行度", no = 8)
    private Integer parallelism;

    /**
     * 插槽数
     */
    @Schema(description = "插槽数")
    @Excel(name = "插槽数", no = 9)
    private Integer slot;

    /**
     * 统计天
     */
    @Schema(description = "统计日期")
    @Excel(name = "统计日期", no = 10)
    private String statisticalDay;


    public String getPipelineAlias() {
        return pipelineAlias;
    }

    public void setPipelineAlias(String pipelineAlias) {
        this.pipelineAlias = pipelineAlias;
    }

    public Long getMaxCpuRate() {
        return maxCpuRate;
    }

    public void setMaxCpuRate(Long maxCpuRate) {
        this.maxCpuRate = maxCpuRate;
    }

    public Long getMaxCpuRateQuotaRatio() {
        return maxCpuRateQuotaRatio;
    }

    public void setMaxCpuRateQuotaRatio(Long maxCpuRateQuotaRatio) {
        this.maxCpuRateQuotaRatio = maxCpuRateQuotaRatio;
    }

    public Long getAvgCpuRate() {
        return avgCpuRate;
    }

    public void setAvgCpuRate(Long avgCpuRate) {
        this.avgCpuRate = avgCpuRate;
    }

    public Long getAvgCpuRateQuotaRatio() {
        return avgCpuRateQuotaRatio;
    }

    public void setAvgCpuRateQuotaRatio(Long avgCpuRateQuotaRatio) {
        this.avgCpuRateQuotaRatio = avgCpuRateQuotaRatio;
    }

    public Long getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(Long recordCount) {
        this.recordCount = recordCount;
    }

    public Integer getRestartCount() {
        return restartCount;
    }

    public void setRestartCount(Integer restartCount) {
        this.restartCount = restartCount;
    }

    public Integer getMaxFullGcCount() {
        return maxFullGcCount;
    }

    public void setMaxFullGcCount(Integer maxFullGcCount) {
        this.maxFullGcCount = maxFullGcCount;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public void setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
    }

    public Integer getSlot() {
        return slot;
    }

    public void setSlot(Integer slot) {
        this.slot = slot;
    }

    public String getStatisticalDay() {
        return statisticalDay;
    }

    public void setStatisticalDay(String statisticalDay) {
        this.statisticalDay = statisticalDay;
    }
}
