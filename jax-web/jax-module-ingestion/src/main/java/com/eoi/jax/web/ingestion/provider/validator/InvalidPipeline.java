package com.eoi.jax.web.ingestion.provider.validator;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/23
 */
public class InvalidPipeline {
    private String invalidJobId;
    private Map<String, String> invalidJobConfig;

    public String getInvalidJobId() {
        return invalidJobId;
    }

    public InvalidPipeline setInvalidJobId(String invalidJobId) {
        this.invalidJobId = invalidJobId;
        return this;
    }

    public Map<String, String> getInvalidJobConfig() {
        return invalidJobConfig;
    }

    public InvalidPipeline setInvalidJobConfig(Map<String, String> invalidJobConfig) {
        this.invalidJobConfig = invalidJobConfig;
        return this;
    }
}
