package com.eoi.jax.web.ingestion.model.ingestioninfo;

import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/1/3
 */
public class CustomJdbcReq {

    @NotBlank(message = "jdcbUrl不能为空")
    @Schema(description = "JDBC URL")
    private String jdbcUrl;

    @NotBlank(message = "driver不能为空")
    @Schema(description = "JDBC Driver")
    private String jdbcDriver;

    @NotBlank(message = "jdbcJar不能为空")
    @Schema(description = "JDBC JAR")
    private String jdbcJar;

    @NotBlank(message = "用户名不能为空")
    @Schema(description = "用户名")
    private String userName;

    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码")
    private String password;

    public String getJdbcUrl() {
        return jdbcUrl;
    }

    public void setJdbcUrl(String jdbcUrl) {
        this.jdbcUrl = jdbcUrl;
    }

    public String getJdbcDriver() {
        return jdbcDriver;
    }

    public void setJdbcDriver(String jdbcDriver) {
        this.jdbcDriver = jdbcDriver;
    }

    public String getJdbcJar() {
        return jdbcJar;
    }

    public void setJdbcJar(String jdbcJar) {
        this.jdbcJar = jdbcJar;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
