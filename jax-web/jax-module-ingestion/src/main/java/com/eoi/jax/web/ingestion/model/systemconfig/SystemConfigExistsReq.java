package com.eoi.jax.web.ingestion.model.systemconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/8/15
 */
public class SystemConfigExistsReq {

    @NotBlank(message = "key不能为空")
    @Schema(description = "key")
    private String key;


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

}
