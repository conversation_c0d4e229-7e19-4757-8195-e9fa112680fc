package com.eoi.jax.web.ingestion.model.businessflowtree;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbBusinessFlowTree;

/**
 * @Author: tangy
 * @Date: 2023/11/29
 * @Desc:
 **/
public class DataDevelopmentQueryReq extends BaseQueryReq<TbBusinessFlowTree> {
    private BusinessFlowTreeQuerySortReq sort;
    private DataDevelopmentQueryFilter filter;

    @Override
    public DataDevelopmentQueryFilter getFilter() {
        return filter;
    }

    @Override
    public BusinessFlowTreeQuerySortReq getSort() {
        return sort;
    }

    public void setSort(BusinessFlowTreeQuerySortReq sort) {
        this.sort = sort;
    }

    public void setFilter(DataDevelopmentQueryFilter filter) {
        this.filter = filter;
    }
}
