package com.eoi.jax.web.ingestion.model.storageck.maintenance;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class StorageCkMetricKafkaConsumerLagsResp {

    @Schema(description = "弹性作业appId")
    private String appId;

    @Schema(description = "容器id")
    private String containerId;

    @Schema(description = "kafka主题名称")
    private String topic;

    @Schema(description = "kafka消费者组名称")
    private String consumer;

    @Schema(description = "task任务")
    private String task;

    @Schema(description = "指标值")
    private List<Object[]> values;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getContainerId() {
        return containerId;
    }

    public void setContainerId(String containerId) {
        this.containerId = containerId;
    }

    public List<Object[]> getValues() {
        return values;
    }

    public void setValues(List<Object[]> values) {
        this.values = values;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getConsumer() {
        return consumer;
    }

    public void setConsumer(String consumer) {
        this.consumer = consumer;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }
}
