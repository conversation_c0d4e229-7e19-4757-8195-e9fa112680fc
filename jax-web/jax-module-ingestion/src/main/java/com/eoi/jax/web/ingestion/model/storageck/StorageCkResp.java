package com.eoi.jax.web.ingestion.model.storageck;

import cn.hutool.json.JSONUtil;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.repository.entity.TbStorageCk;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class StorageCkResp extends ProjectAuthRespModel
        implements IRespModel<TbStorageCk>, IProjectAuthModel, IUserInfoExtensionModel {
    @Schema(description = "主键id")
    @OpPrimaryKey
    private Long id;

    /**
     * 存储任务名称
     */
    @Schema(description = "存储任务名称")
    @OpPrimaryName
    private String name;

    /**
     * 业务流程id
     */
    @Schema(description = "业务流程id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long businessFlowId;

    /**
     * 存储集群id
     */
    @Schema(description = "存储集群id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long kafkaTbId;

    /**
     * 消费策略，全量-ALL、首次最新-NEW
     */
    @Schema(description = "消费策略")
    private String strategy;

    /**
     * 消费者分组名
     */
    @Schema(description = "消费者分组名")
    private String consumeGroup;

    /**
     * ck模型id
     */
    @Schema(description = "ck模型id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long ckTbId;

    /**
     * 写入模式
     */
    @Schema(description = "写入模式")
    private String writeMode;

    /**
     * 开启自动字段映射
     */
    @Schema(description = "自动字段映射")
    private Integer enableAutoSchema;

    /**
     * 字段映射，json字符串
     */
    @Schema(description = "字段映射")
    private List<StorageCkSchemaMapping> schemaMappingList;

    /**
     * 开启动态映射
     */
    @Schema(description = "动态映射")
    private Integer enableDynamicSchema;

    /**
     * 最大字段数
     */
    @Schema(description = "最大字段数")
    private Integer maxDims;

    /**
     * 白名单，json字符串
     */
    @Schema(description = "白名单")
    private String whitelist;

    /**
     * 黑名单，json字符串
     */
    @Schema(description = "黑名单")
    private String blacklist;

    /**
     * 分片key
     */
    @Schema(description = "分片key")
    private String shardingKey;

    /**
     * 指标维度模型id
     */
    @Schema(description = "指标维度模型id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long dimTbId;

    /**
     * 指标值模型id
     */
    @Schema(description = "指标值模型id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long idxTbId;

    /**
     * 存储集群id
     */
    @Schema(description = "存储集群id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long storageClusterId;

    /**
     * 批写入最大延迟，Default to 5, max to 600.
     */
    @Schema(description = "批写入最大延迟")
    private Integer flushInterval;

    /**
     * 批写入最大数量，Default to 262114, max to 1048576.
     */
    @Schema(description = "批写入最大数量")
    private Integer batchBufferSize;

    /**
     * 非task的高级配置
     */
    @Schema(description = "非task的高级配置")
    private Map<String, Object> runningSettingMap;

    @Schema(description = "状态")
    private String status;

    /**
     * 是否已发布
     */
    @Schema(description = "是否已发布")
    private Integer isPublished;

    /**
     * 最后启动时间
     */
    @Schema(description = "是否已发布")
    private Date lastStartTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errMsg;

    /**
     * 任务标签
     */
    private Set<String> tagList;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long kafkaTdId;

    @JsonSerialize(using = LongStringSerializer.class)
    private Long ckTdId;

    @JsonSerialize(using = LongStringSerializer.class)
    private Long dimTdId;

    @JsonSerialize(using = LongStringSerializer.class)
    private Long idxTdId;

    private Date createTime;

    private Date updateTime;

    private Long createUser;

    private Long updateUser;

    @Override
    public StorageCkResp fromEntity(TbStorageCk table) {
        IRespModel.super.fromEntity(table);
        if (StringUtils.isNotBlank(table.getSchemaMapping())) {
            this.schemaMappingList = JSONUtil.toList(JSONUtil.parseArray(table.getSchemaMapping()), StorageCkSchemaMapping.class);
        }
        if (StringUtils.isNotBlank(table.getRunningSetting())) {
            this.runningSettingMap = JSONUtil.toBean(table.getRunningSetting(), Map.class);
        }
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public Long getKafkaTbId() {
        return kafkaTbId;
    }

    public void setKafkaTbId(Long kafkaTbId) {
        this.kafkaTbId = kafkaTbId;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public Long getKafkaTdId() {
        return kafkaTdId;
    }

    public void setKafkaTdId(Long kafkaTdId) {
        this.kafkaTdId = kafkaTdId;
    }

    public Long getCkTdId() {
        return ckTdId;
    }

    public void setCkTdId(Long ckTdId) {
        this.ckTdId = ckTdId;
    }

    public Long getDimTdId() {
        return dimTdId;
    }

    public void setDimTdId(Long dimTdId) {
        this.dimTdId = dimTdId;
    }

    public Long getIdxTdId() {
        return idxTdId;
    }

    public void setIdxTdId(Long idxTdId) {
        this.idxTdId = idxTdId;
    }

    public String getConsumeGroup() {
        return consumeGroup;
    }

    public void setConsumeGroup(String consumeGroup) {
        this.consumeGroup = consumeGroup;
    }

    public Long getCkTbId() {
        return ckTbId;
    }

    public void setCkTbId(Long ckTbId) {
        this.ckTbId = ckTbId;
    }

    public String getWriteMode() {
        return writeMode;
    }

    public void setWriteMode(String writeMode) {
        this.writeMode = writeMode;
    }

    public Integer getEnableAutoSchema() {
        return enableAutoSchema;
    }

    public void setEnableAutoSchema(Integer enableAutoSchema) {
        this.enableAutoSchema = enableAutoSchema;
    }

    public List<StorageCkSchemaMapping> getSchemaMappingList() {
        return schemaMappingList;
    }

    public void setSchemaMappingList(List<StorageCkSchemaMapping> schemaMappingList) {
        this.schemaMappingList = schemaMappingList;
    }

    public Integer getEnableDynamicSchema() {
        return enableDynamicSchema;
    }

    public void setEnableDynamicSchema(Integer enableDynamicSchema) {
        this.enableDynamicSchema = enableDynamicSchema;
    }

    public Integer getMaxDims() {
        return maxDims;
    }

    public void setMaxDims(Integer maxDims) {
        this.maxDims = maxDims;
    }

    public String getWhitelist() {
        return whitelist;
    }

    public void setWhitelist(String whitelist) {
        this.whitelist = whitelist;
    }

    public String getBlacklist() {
        return blacklist;
    }

    public void setBlacklist(String blacklist) {
        this.blacklist = blacklist;
    }

    public String getShardingKey() {
        return shardingKey;
    }

    public void setShardingKey(String shardingKey) {
        this.shardingKey = shardingKey;
    }

    public Long getDimTbId() {
        return dimTbId;
    }

    public void setDimTbId(Long dimTbId) {
        this.dimTbId = dimTbId;
    }

    public Long getIdxTbId() {
        return idxTbId;
    }

    public void setIdxTbId(Long idxTbId) {
        this.idxTbId = idxTbId;
    }

    public Long getStorageClusterId() {
        return storageClusterId;
    }

    public void setStorageClusterId(Long storageClusterId) {
        this.storageClusterId = storageClusterId;
    }

    public Integer getFlushInterval() {
        return flushInterval;
    }

    public void setFlushInterval(Integer flushInterval) {
        this.flushInterval = flushInterval;
    }

    public Integer getBatchBufferSize() {
        return batchBufferSize;
    }

    public void setBatchBufferSize(Integer batchBufferSize) {
        this.batchBufferSize = batchBufferSize;
    }

    public Map<String, Object> getRunningSettingMap() {
        return runningSettingMap;
    }

    public void setRunningSettingMap(Map<String, Object> runningSettingMap) {
        this.runningSettingMap = runningSettingMap;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Integer isPublished) {
        this.isPublished = isPublished;
    }

    public Date getLastStartTime() {
        return lastStartTime;
    }

    public void setLastStartTime(Date lastStartTime) {
        this.lastStartTime = lastStartTime;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public Set<String> getTagList() {
        return tagList;
    }

    public void setTagList(Set<String> tagList) {
        this.tagList = tagList;
    }

    @Override
    public String toString() {
        return "StorageCkResp{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", businessFlowId=" + businessFlowId +
                ", kafkaTbId=" + kafkaTbId +
                ", strategy='" + strategy + '\'' +
                ", consumeGroup='" + consumeGroup + '\'' +
                ", ckTbId=" + ckTbId +
                ", writeMode='" + writeMode + '\'' +
                ", enableAutoSchema=" + enableAutoSchema +
                ", schemaMappingList=" + schemaMappingList +
                ", enableDynamicSchema=" + enableDynamicSchema +
                ", maxDims=" + maxDims +
                ", whitelist='" + whitelist + '\'' +
                ", blacklist='" + blacklist + '\'' +
                ", shardingKey='" + shardingKey + '\'' +
                ", dimTbId=" + dimTbId +
                ", idxTbId=" + idxTbId +
                ", storageClusterId=" + storageClusterId +
                ", flushInterval=" + flushInterval +
                ", batchBufferSize=" + batchBufferSize +
                ", runningSettingMap=" + runningSettingMap +
                ", status='" + status + '\'' +
                ", isPublished=" + isPublished +
                ", lastStartTime=" + lastStartTime +
                ", errMsg='" + errMsg + '\'' +
                ", tagList=" + tagList +
                ", kafkaTdId=" + kafkaTdId +
                ", ckTdId=" + ckTdId +
                ", dimTdId=" + dimTdId +
                ", idxTdId=" + idxTdId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                '}';
    }
}
