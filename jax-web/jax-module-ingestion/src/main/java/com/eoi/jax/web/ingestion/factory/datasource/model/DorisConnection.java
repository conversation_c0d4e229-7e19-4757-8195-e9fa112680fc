package com.eoi.jax.web.ingestion.factory.datasource.model;

import com.eoi.jax.web.ingestion.factory.datasource.IDatasourceConnectionModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 */
public class DorisConnection implements IDatasourceConnectionModel {

    @JsonIgnore
    private String platform;

    @Schema(description = "fe地址")
    @NotBlank(message = "fe地址不能为空")
    private String feAddress;

    @Schema(description = "http端口")
    @NotNull(message = "http端口不能为空")
    private Integer httpPort;

    @Schema(description = "查询端口")
    @NotNull(message = "查询端口不能为空")
    private Integer queryPort;

    @Schema(description = "clickhouse的jdbc参数")
    private Map<String, String> jdbcProp;

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    private String username;

    @Schema(description = "密码")
    private String password;

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getFeAddress() {
        return feAddress;
    }

    public void setFeAddress(String feAddress) {
        this.feAddress = feAddress;
    }

    public Integer getHttpPort() {
        return httpPort;
    }

    public void setHttpPort(Integer httpPort) {
        this.httpPort = httpPort;
    }

    public Integer getQueryPort() {
        return queryPort;
    }

    public void setQueryPort(Integer queryPort) {
        this.queryPort = queryPort;
    }

    public Map<String, String> getJdbcProp() {
        return jdbcProp;
    }

    public void setJdbcProp(Map<String, String> jdbcProp) {
        this.jdbcProp = jdbcProp;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "DorisConnection{" +
                "platform='" + platform + '\'' +
                ", feAddress=" + feAddress +
                ", httpPort=" + httpPort +
                ", queryPort=" + queryPort +
                ", jdbcProp=" + jdbcProp +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
