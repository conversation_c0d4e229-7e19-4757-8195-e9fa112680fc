package com.eoi.jax.web.ingestion.model.datacenter;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbDataCenter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class DataCenterCreateReq implements ICreateModel<TbDataCenter> {

    @Schema(description = "数据中心名称")
    @NotBlank(message = "数据中心名称不能为空")
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Override
    public TbDataCenter toEntity() {
        return toEntity(new TbDataCenter());
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
