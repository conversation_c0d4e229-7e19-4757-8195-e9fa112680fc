package com.eoi.jax.web.ingestion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.manager.marayarn.MarayarnJobGetResult;
import com.eoi.jax.web.core.client.yarn.YarnAppLog;
import com.eoi.jax.web.core.client.yarn.YarnRestClient;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.*;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.exception.LockException;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.excel.BaseAuthExcelService;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.core.service.impl.MysqlLockImpl;
import com.eoi.jax.web.core.util.ValidatorUtils;
import com.eoi.jax.web.ingestion.enumrate.OpLogTypeEnum;
import com.eoi.jax.web.ingestion.enumrate.OpObjTypeEnum;
import com.eoi.jax.web.ingestion.model.application.*;
import com.eoi.jax.web.ingestion.model.application.deploy.ApplicationDeployResp;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeCreateReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeResp;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeUpdateReq;
import com.eoi.jax.web.ingestion.model.cluster.Cluster;
import com.eoi.jax.web.ingestion.model.cluster.YarnCluster;
import com.eoi.jax.web.ingestion.model.opts.MarayarnOpts;
import com.eoi.jax.web.ingestion.model.projectresource.ApplicationResourceResp;
import com.eoi.jax.web.ingestion.model.projectresource.ProjectResourceReq;
import com.eoi.jax.web.ingestion.model.projectresource.ProjectResourceResp;
import com.eoi.jax.web.ingestion.provider.manager.MarayarnApplicationManager;
import com.eoi.jax.web.ingestion.service.*;
import com.eoi.jax.web.repository.entity.TbApplication;
import com.eoi.jax.web.repository.entity.TbApplicationDeploy;
import com.eoi.jax.web.repository.entity.TbOperatorLog;
import com.eoi.jax.web.repository.entity.TbStorageCluster;
import com.eoi.jax.web.repository.search.query.ApplicationMaintenanceParam;
import com.eoi.jax.web.repository.search.result.ApplicationMaintenanceResult;
import com.eoi.jax.web.repository.service.TbApplicationDeployService;
import com.eoi.jax.web.repository.service.TbApplicationService;
import com.eoi.jax.web.repository.service.TbClusterService;
import com.eoi.jax.web.repository.service.TbOperatorLogService;
import com.eoi.jax.web.repository.util.MybatisOrderUtil;
import com.eoi.marayarn.AMResponse;
import com.eoi.marayarn.ApplicationInfo;
import com.eoi.marayarn.ScaleRequest;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
@Service
public class ApplicationServiceImpl extends BaseAuthExcelService<
        TbApplicationService,
        TbApplication,
        ApplicationResp,
        ApplicationCreateReq,
        ApplicationUpdateReq,
        ApplicationQueryReq,
        ApplicationExcelModel> implements ApplicationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationServiceImpl.class.getName());

    private static final String FLOW_TREE_REFERENCE_TYPE = BusinessFlowTreeReferenceTypeEnum.APPLICATION.code();
    private static final String FLOW_TREE_CATEGORY = BusinessFlowTreeCategoryEnum.REAL_TIME_DATA_PROCESSING.code();

    private static final long BLOCK_TIMEOUT = 20 * 1000L;
    @Resource
    private TbClusterService tbClusterService;

    @Resource
    private ProjectResourceService projectResourceService;

    @Autowired
    private TbApplicationService tbApplicationService;

    @Autowired
    private TbApplicationDeployService tbApplicationDeployService;

    @Autowired
    private MysqlLockImpl lockService;

    @Autowired
    private BusinessFlowTreeService businessFlowTreeService;

    @Autowired
    private JaxRepository jaxRepository;

    @Autowired
    private TbOperatorLogService tbOperatorLogService;

    @Autowired
    private MarayarnApplicationManager marayarnApplicationManager;

    @Autowired
    private ClusterService clusterService;

    @Autowired
    private OptsService optsService;

    public ApplicationServiceImpl(@Autowired TbApplicationService tbApplicationService) {
        super(tbApplicationService);
    }


    /**
     * 新增
     *
     * @param req
     * @return
     */
    @Override
    public ApplicationResp create(ApplicationCreateReq req) {
        ValidatorUtils.validateEntity(req);
        TbApplication tbApplication = req.toEntity();
        tbApplication.setAppType(ApplicationTypeEnum.GENERAL.code());
        return insert(tbApplication);
    }

    @Override
    public ApplicationResp insert(TbApplication tbApplication) {
        tbApplication.setStatus(RunningStatusEnum.UNSTART.code());
        tbApplication.setIsPublished(0);
        tbApplication.setCreateTime(new Date());
        tbApplication.setUpdateTime(new Date());
        tbApplication.setIsDeleted(0);

        tbApplicationService.save(tbApplication);
        createBusinessFlowTree(tbApplication);
        return new ApplicationResp().fromEntity(tbApplication);
    }


    /**
     * 更新
     *
     * @param req
     * @return
     */
    @Override
    public ApplicationResp update(ApplicationUpdateReq req) {
        ValidatorUtils.validateEntity(req);
        TbApplication dbApplication = getById(req.getId());
        TbApplication tbApplication = req.toEntity(dbApplication);
        return update(tbApplication);
    }

    @Override
    public ApplicationResp update(TbApplication tbApplication) {
        tbApplication.setIsPublished(0);
        tbApplication.setUpdateTime(new Date());
        tbApplicationService.updateById(tbApplication);
        updateBusinessFlowTree(tbApplication);

        return new ApplicationResp().fromEntity(tbApplication);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public ApplicationResp delete(Long id) {
        TbApplication app = getById(id);
        String lockKey = getLockKeyById(id);
        try {
            if (!lockService.lock(lockKey, null, 5 * 1000L)) {
                throw new BizException(ResponseCode.LOCK_FAILED.getCode(), "弹性作业删除失败，获取资源锁失败。");
            }
            List<UsageResp> usageRespList = usage(id);
            if (usageRespList.size() > 0) {
                throw new BizException(ResponseCode.DELETE_IN_USE, "使用中，不能删除");
            }
            TbApplicationDeploy deploy = tbApplicationDeployService.getByAppId(id);
            if (deploy != null) {
                LOGGER.info("如果存在am,则需要先停止am再删除");
                if (ApplicationStatusEnum.canStop(deploy.getStatus())) {
                    boolean isStopped = marayarnApplicationManager.checkIsStopped(id);
                    if (!isStopped) {
                        boolean success = marayarnApplicationManager.stop(deploy);
                        if (!success) {
                            throw new BizException(ResponseCode.APP_STOP_FAILED.getCode(), "删除失败，弹性作业停止失败");
                        }
                    }
                }
                // delete app_deploy
                tbApplicationDeployService.removeById(deploy.getId());
            }
            // delete flow tree
            deleteBusinessFlowTree(app);
            // delete log
            tbOperatorLogService.remove(
                    new LambdaQueryWrapper<TbOperatorLog>()
                            .eq(TbOperatorLog::getObjId, id)
                            .eq(TbOperatorLog::getObjType, OpObjTypeEnum.APPLICATION.code())
            );
            return super.delete(id);
        } catch (Exception e) {
            LOGGER.error("删除弹性作业 {} {} 失败", app.getId(), app.getName(), e);
            throw e;
        } finally {
            lockService.unLock(lockKey);
        }

    }

    @Override
    public Paged<ApplicationMaintenanceResp> getList(ApplicationQueryReq req) {
        ApplicationQueryFilterReq filter = (ApplicationQueryFilterReq) req.getFilter();
        ApplicationQuerySortReq sort = (ApplicationQuerySortReq) req.getSort();
        Page<ApplicationMaintenanceResult> page = new Page<>(req.getPage() + 1, req.getSize());
        page.setOrders(CollUtil.newArrayList(MybatisOrderUtil.getOrderItem("a.update_time", sort.isAsc(sort.getUpdateTime()))));

        ApplicationMaintenanceParam param = new ApplicationMaintenanceParam();
        BeanUtil.copyProperties(filter, param);
        Page<ApplicationMaintenanceResult> resultPage = tbApplicationService.getList(page, param);
        if (CollUtil.isEmpty(resultPage.getRecords())) {
            return new Paged<>(0, CollUtil.newArrayList());
        }
        Paged<ApplicationMaintenanceResp> paged = new Paged<>();
        paged.setTotal(resultPage.getTotal());

        List<ApplicationMaintenanceResp> list = new LinkedList<>();
        paged.setList(list);
        for (ApplicationMaintenanceResult result : resultPage.getRecords()) {
            ApplicationMaintenanceResp maintenance = new ApplicationMaintenanceResp();
            list.add(maintenance);
            BeanUtil.copyProperties(result, maintenance);
            if (StringUtils.isNotBlank(result.getOptsSetting())) {
                MarayarnOpts opts = JSONUtil.toBean(result.getOptsSetting(), MarayarnOpts.class);
                maintenance.setExecutorCpu(opts.getExecutorCpu());
                maintenance.setExecutorMemory(opts.getExecutorMemory());
                maintenance.setYarnQueue(opts.getYarnQueue());
            }
            if (StringUtils.isNotBlank(result.getSetting())) {
                MarayarnOpts opts = JSONUtil.toBean(result.getSetting(), MarayarnOpts.class);
                if (opts.getExecutorCpu() != null) {
                    maintenance.setExecutorCpu(opts.getExecutorCpu());
                }
                if (opts.getExecutorMemory() != null) {
                    maintenance.setExecutorMemory(opts.getExecutorMemory());
                }
                if (StringUtils.isNotBlank(opts.getYarnQueue())) {
                    maintenance.setYarnQueue(opts.getYarnQueue());
                }
            }
            if (StringUtils.isNotBlank(result.getDeployStatus())) {
                maintenance.setStatus(result.getDeployStatus());
            }
        }

        return paged;
    }

    /**
     * 根据id获取
     *
     * @param id
     * @return
     */
    @Override
    public ApplicationResp get(Long id) {
        ApplicationResp resp = super.get(id);
        return resp;
    }

    @Override
    public ApplicationResp createAndStart(ApplicationCreateReq req) {
        ApplicationResp resp = create(req);
        return start(resp.getId(), req.getForceStart());
    }

    @Override
    public List<ApplicationResp> batchStart(List<Long> ids, boolean forceStart) {
        if (ids == null || ids.size() == 0) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "id列表不能为空");
        }

        // 资源配额
        if (projectResourceService.checkNeedCalResource()) {
            List<ApplicationResp> apps = all(new LambdaQueryWrapper<TbApplication>().in(TbApplication::getId, ids));
            List<ApplicationResourceResp> resourceRespList = new ArrayList<>();
            // allOf提交多个任务,用for循环提交，并等待全部完成
            List<CompletableFuture<Void>> allFutureList = new ArrayList<>();
            for (ApplicationResp app : apps) {
                allFutureList.add(
                        CompletableFuture.runAsync(() -> {
                            try {

                                ApplicationDeployResp applicationDeployResp = new ApplicationDeployResp();
                                BeanUtil.copyProperties(app, applicationDeployResp);
                                ApplicationResourceResp currentResource =
                                        projectResourceService.getApplicationResource(applicationDeployResp);
                                resourceRespList.add(currentResource);

                            } catch (Exception e) {
                                LOGGER.error("作业<" + app.getName() + ">资源使用情况计算失败", e);
                            }
                        })
                );
            }
            // 等待全部完成
            CompletableFuture.allOf(allFutureList.toArray(new CompletableFuture[0])).join();
            long totalCurrentCpu = resourceRespList.stream().mapToLong(ApplicationResourceResp::getCpuCount).sum();
            long totalCurrentMem = resourceRespList.stream().mapToLong(ApplicationResourceResp::getMemCount).sum();
            ProjectResourceReq projectResourceReq = new ProjectResourceReq();
            projectResourceReq.setStartCheck(false);
            ProjectResourceResp projectResource = projectResourceService.getProjectResource(projectResourceReq);
            projectResource.checkResource(totalCurrentCpu, totalCurrentMem, forceStart);
        }

        List<ApplicationResp> list = new LinkedList<>();
        for (Long id : ids) {
            try {
                list.add(start(id, forceStart));
            } catch (Exception e) {
                LOGGER.error("批量启动弹性作业失败:" + e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<ApplicationResp> batchStop(List<Long> ids) {
        Assert.notNull(ids, "id列表不能为空");
        List<ApplicationResp> list = new LinkedList<>();
        for (Long id : ids) {
            try {
                list.add(stop(id));
            } catch (Exception e) {
                LOGGER.error("批量停止弹性作业失败:" + e.getMessage());
            }
        }
        return list;
    }


    @Override
    public ApplicationResp updateAndStart(ApplicationUpdateReq req) {
        String lockKey = getLockKeyById(req.getId());
        try {
            if (!lockService.lock(lockKey, null, BLOCK_TIMEOUT)) {
                throw new BizException(ResponseCode.LOCK_FAILED.getCode(), "弹性作业更新并启动失败，获取资源锁失败，请稍后再试。");
            }
            update(req);
            return start(req.getId(), req.getForceStart());
        } catch (Exception e) {
            LOGGER.error("启动失败：", e);
            throw e;
        } finally {
            lockService.unLock(lockKey);
        }
    }

    @Override
    public TbApplication getById(Long id) {
        TbApplication application = tbApplicationService.getById(id);
        if (application == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "弹性作业不存在：id=" + id);
        }
        return application;
    }

    @Override
    public List<UsageResp> usageByEntity(TbApplication entity) {
        List<UsageResp> result = new ArrayList<>();
        Long id = entity.getId();
        // 存储集群
        result.addAll(jaxRepository.storageClusterService()
                .list(new LambdaQueryWrapper<TbStorageCluster>()
                        .eq(TbStorageCluster::getAppId, id))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));

        return result;
    }

    @Override
    public ApplicationResp start(Long id, boolean forceStart) {
        TbApplication app = null;
        String lockKey = getLockKeyById(id);
        try {
            if (!lockService.lock(lockKey, null, BLOCK_TIMEOUT)) {
                throw new BizException(ResponseCode.LOCK_FAILED.getCode(), "弹性作业启动失败，获取资源锁失败，请稍后再试。appId=" + id);
            }
            app = getById(id);
            String status = app.getStatus();
            boolean isPublished = app.getIsPublished() == 1;
            if (isPublished && RunningStatusEnum.RUNNING.code().equals(status)) {
                throw new BizException(ResponseCode.NOT_ALLOW_START.getCode(),
                        "不允许启动，当前作业状态为:" + RunningStatusEnum.getMessageByCode(status));
            }

            TbApplicationDeploy deploy = tbApplicationDeployService.getByAppId(id);
            if (deploy != null) {
                if (isPublished && !ApplicationStatusEnum.canStart(deploy.getStatus())) {
                    throw new BizException(ResponseCode.NOT_ALLOW_START.getCode(),
                            "不允许启动，当前作业状态为:" + ApplicationStatusEnum.getMessageByCode(deploy.getStatus()));
                }
            }

            // 资源配额
            if (projectResourceService.checkNeedCalResource()) {
                ProjectResourceReq projectResourceReq = new ProjectResourceReq();
                projectResourceReq.setStartCheck(true);
                projectResourceReq.setApplicationId(id);
                ProjectResourceResp projectResource = projectResourceService.getProjectResource(projectResourceReq);
                ApplicationDeployResp applicationDeployResp = new ApplicationDeployResp();
                BeanUtil.copyProperties(app, applicationDeployResp);
                ApplicationResourceResp currentResource =
                        projectResourceService.getApplicationResource(applicationDeployResp);
                long currentCpu = currentResource.getCpuCount();
                long currentMem = currentResource.getMemCount();
                projectResource.checkResource(currentCpu, currentMem, forceStart);
            }

            app.setStatus(RunningStatusEnum.EXCEPTION.code());
            app.setIsPublished(1);
            tbApplicationService.updateById(app);
            LOGGER.info("状态变更: 弹性作业【" + app.getName() + "#appId=" + id + "】启动，状态从【"
                    + status + "】变更为【" + app.getStatus() + "】");
            Date now = new Date();
            String deployStatus = "";
            if (deploy == null) {
                deploy = new TbApplicationDeploy();
                BeanUtil.copyProperties(app, deploy);
                deploy.setId(null);
                deploy.setApplicationId(app.getId());
                deploy.setStartTime(now);
                deploy.setCreateTime(now);
                deploy.setStatus(ApplicationStatusEnum.WAITING_START.code());
                deploy.setUpdateTime(now);
                tbApplicationDeployService.save(deploy);
            } else {
                if (isPublished && !ApplicationStatusEnum.canStart(deploy.getStatus())) {
                    throw new BizException(ResponseCode.NOT_ALLOW_START.getCode(),
                            "不允许启动，当前作业状态为:" + ApplicationStatusEnum.getMessageByCode(deploy.getStatus()));
                }
                deployStatus = deploy.getStatus();
                Long deployId = deploy.getId();
                Date createTime = deploy.getCreateTime();
                BeanUtil.copyProperties(app, deploy);
                deploy.setId(deployId);
                deploy.setStatus(ApplicationStatusEnum.WAITING_START.code());
                deploy.setStartTime(now);
                deploy.setCreateTime(createTime);
                deploy.setUpdateTime(now);
                tbApplicationDeployService.updateById(deploy);
            }
            LOGGER.info("状态变更: 弹性作业【" + app.getName() + "#appId=" + id + "】启动deploy任务，状态从【" + deployStatus + "】变更为【"
                    + deploy.getStatus() + "】");
            return new ApplicationResp().fromEntity(app);
        } catch (Exception e) {
            LOGGER.error("启动失败：", e);
            throw e;
        } finally {
            lockService.unLock(lockKey);
        }
    }

    @Override
    public ApplicationResp stop(Long id) {
        TbApplication app = null;
        String lockKey = getLockKeyById(id);
        try {
            boolean isLocked = lockService.lock(lockKey, null, BLOCK_TIMEOUT);
            if (!isLocked) {
                throw new BizException(ResponseCode.LOCK_FAILED.getCode(), "弹性作业停止失败，获取资源锁失败，请稍后再试。appId=" + id);
            }
            app = getById(id);
            String status = app.getStatus();
            if (RunningStatusEnum.STOPPED.code().equals(status)
                    || RunningStatusEnum.UNSTART.code().equals(status)) {
                LOGGER.info("停止命令未发送，直接返回结果，当前作业状态为:" + RunningStatusEnum.getMessageByCode(status));
                throw new BizException(ResponseCode.NOT_ALLOW_STOP.getCode(),
                        "不允许停止，当前作业状态为:" + ApplicationStatusEnum.getMessageByCode(status));
            }
            TbApplicationDeploy deploy = tbApplicationDeployService.getByAppId(id);
            Date now = new Date();
            if (deploy == null) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "停止失败，弹性作业任务未找到。");
            }
            if (!ApplicationStatusEnum.canStop(deploy.getStatus())) {
                throw new BizException(ResponseCode.NOT_ALLOW_STOP.getCode(),
                        "不允许停止，当前作业状态为:" + ApplicationStatusEnum.getMessageByCode(deploy.getStatus()));
            }
            app.setStatus(RunningStatusEnum.EXCEPTION.code());
            LOGGER.info("状态变更: 弹性作业【" + app.getName() + "#appId=" + id + "】停止，状态从【" +
                    status + "】变更为【" + app.getStatus() + "】");
            app.setIsPublished(1);
            tbApplicationService.updateById(app);
            updateBusinessFlowTree(app);
            String deployStatus = deploy.getStatus();
            deploy.setStatus(ApplicationStatusEnum.WAITING_STOP.code());
            deploy.setEndTime(now);
            deploy.setUpdateTime(now);
            tbApplicationDeployService.updateById(deploy);
            LOGGER.info("状态变更: 弹性作业【" + app.getName() + "#appId=" + id + "】停止deploy任务，状态从【" + deployStatus +
                    "】变更为【" + deploy.getStatus() + "】");
            return new ApplicationResp().fromEntity(app);
        } catch (Exception e) {
            LOGGER.error("停止失败：", e);
            throw e;
        } finally {
            lockService.unLock(lockKey);
        }
    }

    @Override
    public List<ApplicationLogResp> listLog(Long id) {
        List<TbOperatorLog> list = tbOperatorLogService.list(
                new LambdaQueryWrapper<TbOperatorLog>()
                        .eq(TbOperatorLog::getObjId, id)
                        .eq(TbOperatorLog::getObjType, OpObjTypeEnum.APPLICATION.code())
                        .eq(TbOperatorLog::getLogType, OpLogTypeEnum.AUDIT.code())
                        .orderByDesc(TbOperatorLog::getId)
        );
        return list.stream()
                .map(i -> (ApplicationLogResp) new ApplicationLogResp().copyFrom(i))
                .collect(Collectors.toList());
    }

    @Override
    public Paged<ApplicationLogResp> pageConsole(Long id, Long opId, int pageIndex, int pageSize) {
        IPage<TbOperatorLog> paged = tbOperatorLogService.page(
                new Page<>(pageIndex + 1L, pageSize),
                new LambdaQueryWrapper<TbOperatorLog>()
                        .eq(TbOperatorLog::getObjId, id)
                        .eq(opId != null, TbOperatorLog::getOpId, opId)
                        .eq(TbOperatorLog::getObjType, OpObjTypeEnum.APPLICATION.code())
                        .eq(TbOperatorLog::getLogType, OpLogTypeEnum.CONSOLE.code())
                        .orderByAsc(TbOperatorLog::getId)
        );
        long total = paged.getTotal();
        return new Paged<>(total, paged.getRecords().stream()
                .map(i -> (ApplicationLogResp) new ApplicationLogResp().copyFrom(i))
                .collect(Collectors.toList())
        );
    }

    @Override
    public ApplicationDeployResp refresh(Long appId) {
        ApplicationDeployResp deployResp = new ApplicationDeployResp();
        try {
            TbApplicationDeploy tbApplicationDeploy = tbApplicationDeployService.getByAppId(appId);
            if (tbApplicationDeploy == null) {
                ApplicationResp applicationResp = get(appId);
                BeanUtil.copyProperties(applicationResp, deployResp);
                return deployResp;
            }
            deployResp = new ApplicationDeployResp().fromEntity(tbApplicationDeploy);
            marayarnApplicationManager.refreshApplicationStatus(tbApplicationDeploy);
            tbApplicationDeploy = tbApplicationDeployService.getByAppId(appId);
            deployResp = new ApplicationDeployResp().fromEntity(tbApplicationDeploy);
            return deployResp;
        } catch (LockException e) {
            return deployResp;
        } catch (Exception e) {
            throw new BizException(ResponseCode.FAILED.getCode(), "手动刷新弹性作业失败", e);
        }
    }

    @Autowired
    private AppMarayarnAccessService appMarayarnAccessService;

    @Override
    public ApplicationInfo getAppInstanceInfo(Long appId) {
        TbApplicationDeploy appDeploy = tbApplicationDeployService.getByAppId(appId);
        if (appDeploy == null || StringUtils.isEmpty(appDeploy.getYarnApplicationId())) {
            throw new BizException(ResponseCode.FAILED.getCode(), "只能获取运行中的作业实例信息");
        }
        String status = appDeploy.getStatus();
        if (!ApplicationStatusEnum.RUNNING.code().equals(status)
                && !ApplicationStatusEnum.STOP_FAILED.code().equals(status)) {
            throw new BizException(ResponseCode.FAILED.getCode(),
                    "只能获取运行中的作业实例信息，当前状态为：" + ApplicationStatusEnum.getMessageByCode(status));
        }
        try {
            MarayarnJobGetResult result = (MarayarnJobGetResult) marayarnApplicationManager.get(appDeploy);
            return appMarayarnAccessService.getApplication(result.getTrackingUrl());
        } catch (Exception e) {
            LOGGER.error("Get application info for[" + appDeploy.getYarnApplicationId() + "] error", e);
            throw new BizException(ResponseCode.FAILED.getCode(), "获取yarn应用信息失败");
        }
    }

    @Override
    public TbApplicationDeploy scaleApplication(Long appId, ApplicationScaleReq req) {
        String lockKey = getLockKeyById(appId);
        try {
            if (!lockService.lock(lockKey, null, BLOCK_TIMEOUT)) {
                throw new BizException(ResponseCode.LOCK_FAILED.getCode(), "弹性作业扩缩容失败，获取资源锁失败，请稍后再试。appId=" + appId);
            }
            TbApplicationDeploy appDeploy = tbApplicationDeployService.getByAppId(appId);
            if (appDeploy == null || StringUtils.isEmpty(appDeploy.getYarnApplicationId())) {
                throw new BizException(ResponseCode.FAILED.getCode(), "只能操作运行中的作业实例信息");
            }
            String status = appDeploy.getStatus();
            if (!ApplicationStatusEnum.RUNNING.code().equals(status)
                    && !ApplicationStatusEnum.STOP_FAILED.code().equals(status)) {
                throw new BizException(ResponseCode.FAILED.getCode(),
                        "只能操作运行中的作业实例，当前状态为：" + ApplicationStatusEnum.getMessageByCode(status));
            }

            // 资源配额
            if (projectResourceService.checkNeedCalResource()) {
                if (req.getCount() > appDeploy.getInstanceCount()) {
                    ProjectResourceReq projectResourceReq = new ProjectResourceReq();
                    projectResourceReq.setStartCheck(true);
                    projectResourceReq.setApplicationId(appDeploy.getApplicationId());
                    ProjectResourceResp projectResource = projectResourceService.getProjectResource(projectResourceReq);
                    ApplicationDeployResp applicationDeployResp = new ApplicationDeployResp();
                    BeanUtil.copyProperties(appDeploy, applicationDeployResp);
                    applicationDeployResp.setInstanceCount(req.getCount());
                    ApplicationResourceResp currentResource =
                            projectResourceService.getApplicationResource(applicationDeployResp);
                    long currentCpu = currentResource.getCpuCount();
                    long currentMem = currentResource.getMemCount();
                    projectResource.checkResource(currentCpu, currentMem, req.getForceStart());
                }

            }

            LambdaUpdateWrapper<TbApplication> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(TbApplication::getId, appId);
            wrapper.set(TbApplication::getInstanceCount, req.getCount());
            tbApplicationService.update(wrapper);
            LambdaUpdateWrapper<TbApplicationDeploy> wrapper2 = new LambdaUpdateWrapper<>();
            wrapper2.eq(TbApplicationDeploy::getId, appDeploy.getId());
            wrapper2.set(TbApplicationDeploy::getInstanceCount, req.getCount());
            tbApplicationDeployService.update(wrapper2);

            try {
                MarayarnJobGetResult result = (MarayarnJobGetResult) marayarnApplicationManager.get(appDeploy);
                ScaleRequest request = new ScaleRequest();
                request.setInstances(req.getCount());
                request.setContainerIds(req.getKillContainerIds());
                AMResponse resp = appMarayarnAccessService.scaleApplication(result.getTrackingUrl(), request);
                if (!"0".equals(resp.getCode())) {
                    LOGGER.warn("Scale application [{}][{}] failed, response code = {}, msg = {}", appId, appDeploy.getYarnApplicationId(),
                            resp.getCode(), resp.getMessage());
                    throw new BizException(ResponseCode.APP_SCALE_FAILED.getCode(), resp.getMessage());
                }
            } catch (Exception e) {
                LOGGER.error("Scale application for[" + appDeploy.getYarnApplicationId() + "] error", e);
                throw new BizException(ResponseCode.APP_SCALE_FAILED.getCode(), "弹性作业扩缩容失败：" + e.getMessage(), e);
            }
            return tbApplicationDeployService.getById(appDeploy.getId());
        } catch (Exception e) {
            throw e;
        } finally {
            lockService.unLock(lockKey);
        }

    }

    @Override
    public YarnAppLog getYarnAppLog(Long id, String applicationId) {
        TbApplicationDeploy appDeploy = tbApplicationDeployService.getByAppId(id);
        if (appDeploy == null || StringUtils.isEmpty(appDeploy.getYarnApplicationId())) {
            throw new BizException(ResponseCode.FAILED.getCode(), "弹性作业未发布");
        }
        Cluster cluster = clusterService.getCluster(appDeploy.getClusterId());
        if (!(cluster.getSetting() instanceof YarnCluster)) {
            throw new BizException(ResponseCode.INVALID_REQUEST);
        }
        YarnCluster yarnCluster = (YarnCluster) cluster.getSetting();
        YarnRestClient yarnRestClient = yarnRestClient(yarnCluster);
        try {
            return yarnRestClient.getApplicationLog(applicationId);
        } catch (Exception e) {
            throw new BizException(ResponseCode.YARN_LOG_FAILED, e);
        }
    }

    private YarnRestClient yarnRestClient(YarnCluster yarnCluster) {
        YarnRestClient yarnRestClient = new YarnRestClient(yarnCluster.getYarnWebUrl());
        if (yarnCluster.isWebKrb5Enabled()) {
            yarnRestClient.withKrb5Auth(yarnCluster.getKeytab(), yarnCluster.getPrincipal());
        }
        return yarnRestClient;
    }

    @Override
    public byte[] getYarnAppLogContent(Long id, String url) {
        TbApplicationDeploy appDeploy = tbApplicationDeployService.getByAppId(id);
        if (appDeploy == null || StringUtils.isEmpty(appDeploy.getYarnApplicationId())) {
            throw new BizException(ResponseCode.FAILED.getCode(), "弹性作业未发布");
        }
        Cluster cluster = clusterService.getCluster(appDeploy.getClusterId());
        if (!(cluster.getSetting() instanceof YarnCluster)) {
            throw new BizException(ResponseCode.INVALID_REQUEST);
        }
        YarnCluster yarnCluster = (YarnCluster) cluster.getSetting();
        YarnRestClient yarnRestClient = yarnRestClient(yarnCluster);
        try {
            return yarnRestClient.getLogContent(url);
        } catch (Exception e) {
            throw new BizException(ResponseCode.YARN_LOG_FAILED);
        }
    }

    private void createBusinessFlowTree(TbApplication entity) {
        if (entity.getBusinessFlowId() == null) {
            return;
        }
        BusinessFlowTreeCreateReq createReq = new BusinessFlowTreeCreateReq();
        createReq.setName(entity.getName());
        createReq.setStatus(businessFlowTreeService.convert2TreeStatus(entity.getStatus()));
        createReq.setIsPublished(entity.getIsPublished());
        createReq.setType(entity.getAppType());
        createReq.setReferenceType(FLOW_TREE_REFERENCE_TYPE);
        createReq.setCategory(FLOW_TREE_CATEGORY);
        createReq.setBusinessFlowId(entity.getBusinessFlowId());
        createReq.setReferenceId(entity.getId());
        createReq.setResourceType(ProjectResourceTypeEnum.TB_APPLICATION.code());
        businessFlowTreeService.create(createReq);
    }

    private void updateBusinessFlowTree(TbApplication entity) {
        if (entity.getBusinessFlowId() == null) {
            return;
        }
        BusinessFlowTreeResp treeResp = businessFlowTreeService.getOne(FLOW_TREE_REFERENCE_TYPE, entity.getId());
        BusinessFlowTreeUpdateReq updateReq = new BusinessFlowTreeUpdateReq();
        updateReq.setId(treeResp.getId());
        updateReq.setBusinessFlowId(entity.getBusinessFlowId());
        updateReq.setName(entity.getName());
        updateReq.setStatus(businessFlowTreeService.convert2TreeStatus(entity.getStatus()));
        updateReq.setIsPublished(entity.getIsPublished());
        businessFlowTreeService.update(updateReq);
    }

    private void deleteBusinessFlowTree(TbApplication entity) {
        if (entity.getBusinessFlowId() == null) {
            return;
        }
        Long referenceId = entity.getId();
        businessFlowTreeService.delete(FLOW_TREE_REFERENCE_TYPE, referenceId);
    }

    @Override
    public String sheetName() {
        return "弹性作业";
    }

    @Override
    public List<ApplicationExcelModel> example() {
        return null;
    }

    @Override
    public void verifyData(List<ApplicationExcelModel> importRows) {
        if (CollUtil.isEmpty(importRows)) {
            return;
        }

        List<String> nameList = importRows.stream().map(ApplicationExcelModel::getName).collect(Collectors.toList());
        Map<String, TbApplication> map = new LinkedHashMap<>();
        for (List<String> list : CollUtil.split(nameList, 50)) {
            List<TbApplication> tbApplicationList = tbApplicationService.list(Wrappers.<TbApplication>lambdaQuery()
                    .in(TbApplication::getName, list));
            tbApplicationList = CollUtil.defaultIfEmpty(tbApplicationList, CollUtil.newArrayList());
            tbApplicationList.forEach(it -> {
                map.put(it.getName(), it);
            });
        }

        importRows.forEach(item -> {
            TbApplication tbApplication = map.get(item.getName());
            if (Objects.isNull(tbApplication)) {
                return;
            }
            item.setCoverField("name");
            item.setId(tbApplication.getId());

            if (ContextHolder.getProjectAuthorityEnable()) {
                TbApplication authTbApplication = tbApplicationService.selectByIdWithProjectAuth(tbApplication.getId());
                if (Objects.isNull(authTbApplication) || !authTbApplication.hasWritePrivilege()) {
                    item.setFailedReason("无权限修改");
                }
            }
        });
    }

    @Override
    public void saveRows(Boolean skip, List<ApplicationExcelModel> importRows) {
        if (importRows.size() == 0) {
            return;
        }
        Map<String, Long> clusterRespMap = clusterService.all()
                .stream().collect(Collectors.toMap(item -> item.getClusterName(), x -> x.getId(), (v1, v2) -> v1));
        Map<String, Long> optsRespMap = optsService.all()
                .stream().collect(Collectors.toMap(item -> item.getOptsName(), x -> x.getId(), (v1, v2) -> v1));

        verifyData(importRows);
        importRows.forEach(item -> {
            item.setAppType(ApplicationTypeEnum.getCodeByMessage(item.getAppType()));
            if (StringUtils.isEmpty(item.getAppType())) {
                item.setAppType("");
            }
            if (StringUtils.isNotBlank(item.getClusterName())) {
                item.setClusterId(clusterRespMap.get(item.getClusterName()));
            }
            if (item.getClusterId() == null) {
                item.setClusterId(0L);
            }
            if (StringUtils.isNotBlank(item.getOptsName())) {
                item.setOptsId(optsRespMap.get(item.getOptsName()));
            }

            if (item.getOptsId() == null) {
                item.setOptsId(0L);
            }
        });
        saveRowsCustomer(skip,
                importRows,
                (item -> {
                    ApplicationCreateReq applicationCreateReq = ModelBeanUtil.copyBean(item, new ApplicationCreateReq());
                    applicationCreateReq.setSettingMap(Optional.ofNullable(((ApplicationExcelModel) item).getSetting())
                            .filter(StringUtils::isNotBlank).map(JSONUtil::parseObj).orElse(null));
                    TbApplication tbApplication = applicationCreateReq.toEntity();
                    tbApplication.setAppType(((ApplicationExcelModel) item).getAppType());
                    insert(tbApplication);
                    ((ApplicationExcelModel) item).setId(tbApplication.getId());
                    return tbApplication;
                }),
                (item -> {
                    ApplicationUpdateReq applicationUpdateReq = ModelBeanUtil.copyBean(item, new ApplicationUpdateReq());
                    applicationUpdateReq.setSettingMap(Optional.ofNullable(((ApplicationExcelModel) item).getSetting())
                            .filter(StringUtils::isNotBlank).map(JSONUtil::parseObj).orElse(null));
                    TbApplication dbApplication = getById(applicationUpdateReq.getId());
                    TbApplication tbApplication = applicationUpdateReq.toEntity(dbApplication);
                    tbApplication.setAppType(((ApplicationExcelModel) item).getAppType());
                    update(tbApplication);
                    return tbApplication;
                }));

    }

    @Override
    public List<ApplicationExcelModel> exportData(ImportExcelReq req) {
        List<ApplicationExcelModel> applicationExcelModels = new ArrayList<>();
        Map<Long, String> clusterRespMap = clusterService.all()
                .stream().collect(Collectors.toMap(item -> item.getId(), x -> x.getClusterName()));
        Map<Long, String> optsRespMap = optsService.all()
                .stream().collect(Collectors.toMap(item -> item.getId(), x -> x.getOptsName()));
        QueryWrapper wrapper = new QueryWrapper();
        buildExcelExportQueryWrapper(wrapper, req, ApplicationQueryReq.class);
        List<ApplicationResp> respList = allWithProjectAuth(wrapper);
        respList.forEach(resp -> {
            ApplicationExcelModel applicationExcelModel = new ApplicationExcelModel();
            BeanUtil.copyProperties(resp, applicationExcelModel);
            applicationExcelModel.setSetting(Optional.ofNullable(resp.getSettingMap()).filter(MapUtil::isNotEmpty)
                    .map(JSONUtil::toJsonStr).orElse(null));
            applicationExcelModel.setAppType(ApplicationTypeEnum.getMessageByCode(resp.getAppType()));
            applicationExcelModel.setClusterName(clusterRespMap.get(resp.getClusterId()));
            applicationExcelModel.setOptsName(optsRespMap.get(resp.getOptsId()));
            applicationExcelModels.add(applicationExcelModel);
        });
        return applicationExcelModels;
    }
}
