package com.eoi.jax.web.ingestion.model.ingestioninfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.integration.model.cell.agent.CellTaskInput;
import com.eoi.jax.web.core.integration.model.cell.agent.CellTaskInputParam;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
public class ArchiveSyncSetting extends AbstractSetting {


    private ArchiveInput input;

    public ArchiveSyncSetting() {
    }

    public ArchiveSyncSetting(ArchiveInput input) {
        this.input = input;
    }

    /**
     * 返回用户设置的标签
     *
     * @return 用户设置的标签
     */
    @Override
    public List<String> tags() {
        return this.input.getTagList();
    }

    /**
     * 参数校验
     *
     * @param settingInDb 数据库中保存的配置
     * @param isPublished 是否已发布
     */
    @Override
    public void valid(AbstractSetting settingInDb, Boolean isPublished) {
        Assert.isFalse(this.input.getCharset().contains("失败"), "自动推荐字符集失败，请手动选择字符集");
        if (CollUtil.isNotEmpty(this.input.getTagList())) {
            Assert.isTrue(this.input.getTagList().stream().distinct().count() == this.input.getTagList().size(),
                    "标签存在重复值");
        }
        if (CollUtil.isNotEmpty(this.input.getCustomParam())) {
            Assert.isTrue(this.input.getCustomParam().keySet().stream().distinct().count() == this.input.getCustomParam().size(),
                    "自定义参数存在重复key");
        }

        if (Objects.isNull(settingInDb) || !isPublished) {
            return;
        }
        Map<String, Pair<Object, Object>> compareResultMap = ModelBeanUtil.compareBeanFields(this.input, settingInDb.getInput(),
                "openFileSort");
        if (CollUtil.isEmpty(compareResultMap)) {
            return;
        }
        Assert.isNull(compareResultMap.get("openFileSort"), "[指定文件采集顺序]任务发布后不允许修改");

    }

    /**
     * 构建input参数
     *
     * @param convertToCellTaskInputParam 额外的转化参数
     * @return input参数JSON格式
     */
    @Override
    public String buildInputParam(ConvertToCellTaskInputParam convertToCellTaskInputParam) {
        CellTaskInput<CellTaskInputParam.ArchiveInput> cellTaskInput = new CellTaskInput<>();
        cellTaskInput.setAgentId(convertToCellTaskInputParam.getAgentId());
        cellTaskInput.setInputType(convertToCellTaskInputParam.getType().inputType());

        CellTaskInputParam.ArchiveInput archiveInputParam = ModelBeanUtil.copyBean(this.input,
                new CellTaskInputParam.ArchiveInput(), "customParam", "tagList",
                "whitelist", "blacklist", "include", "exclude");
        List<String> pathList = StrUtil.splitTrim(this.input.getPath(), ";");
        archiveInputParam.setPath(pathList.stream().map(String::trim).collect(Collectors.joining(",")));
        archiveInputParam.setId(convertToCellTaskInputParam.getTaskId().toString());
        archiveInputParam.setEnable(convertToCellTaskInputParam.getEnable());
        archiveInputParam.setMode("archive");
        if (Optional.ofNullable(this.input.getUseWhitelist()).orElse(false)) {
            archiveInputParam.setWhitelist(this.input.getWhitelist());
        }
        if (Optional.ofNullable(this.input.getUseBlacklist()).orElse(false)) {
            archiveInputParam.setBlacklist(this.input.getBlacklist());
        }
        if (Optional.ofNullable(this.input.getUseInclude()).orElse(false)) {
            archiveInputParam.setInclude(this.input.getInclude());
        }
        if (Optional.ofNullable(this.input.getUseExclude()).orElse(false)) {
            archiveInputParam.setExclude(this.input.getExclude());
        }
        if (Optional.ofNullable(this.input.getUseInnerWhiteList()).orElse(false)) {
            archiveInputParam.setInnerBlackList(this.input.getInnerWhiteList());
        }
        if (Optional.ofNullable(this.input.getUseInnerBlackList()).orElse(false)) {
            archiveInputParam.setInnerBlackList(this.input.getInnerBlackList());
        }

        Map<String, String> fieldMap = new HashMap<>(4);
        fieldMap.put("@topic", convertToCellTaskInputParam.getTopicName());
        fieldMap.put("@tags", String.join(",", Optional.ofNullable(this.input.getTagList()).orElse(CollUtil.newArrayList())));
        fieldMap.put("@ip", "{{.agent_ip}}");
        if (CollUtil.isNotEmpty(this.input.getCustomParam())) {
            for (Map.Entry<String, String> e : this.input.getCustomParam().entrySet()) {
                fieldMap.put("@" + e.getKey(), e.getValue());
            }
        }
        archiveInputParam.setFields(fieldMap);

        cellTaskInput.setInputParams(archiveInputParam);
        return JsonUtil.encode(CollUtil.newArrayList(cellTaskInput));
    }

    /**
     * 将cell返回的input参数转化成AbstractSetting
     *
     * @param inputStr                      从cell接口获取到的输入参数字符串
     * @param convertToAbstractSettingParam 额外的转化参数
     * @return AbstractSetting的子类对象
     */
    @Override
    public AbstractSetting fromCellTaskInputStr(String inputStr, ConvertToAbstractSettingParam convertToAbstractSettingParam) {
        List<CellTaskInput<CellTaskInputParam.ArchiveInput>> cellTaskInputList = JsonUtil.decode(inputStr,
                new TypeReference<List<CellTaskInput<CellTaskInputParam.ArchiveInput>>>() {
                });
        CellTaskInput<CellTaskInputParam.ArchiveInput> cellTaskInput = cellTaskInputList.get(0);
        CellTaskInputParam.ArchiveInput inputParams = cellTaskInput.getInputParams();

        ArchiveSyncSetting.ArchiveInput archiveInput = ModelBeanUtil.copyBean(inputParams, new ArchiveSyncSetting.ArchiveInput());

        Map<String, String> fields = new HashMap<>(inputParams.getFields().size());
        fields.putAll(inputParams.getFields());
        archiveInput.setTagList(Arrays.stream(fields.getOrDefault("@tags", "").split(",")).collect(Collectors.toList()));
        fields.remove("@topic");
        fields.remove("@tags");
        fields.remove("@ip");
        Map<String, String> customParam = new HashMap<>(4);
        for (Map.Entry<String, String> entry : fields.entrySet()) {
            customParam.put(entry.getKey().substring(1), entry.getValue());
        }
        archiveInput.setCustomParam(customParam);
        // 设置其它的参数

        // 根据数据库里的单位进行转换
        ArchiveSyncSetting fileSyncSettingInDb = JsonUtil.decode(convertToAbstractSettingParam.getTbIngestionJobTask().getSetting(),
                new TypeReference<ArchiveSyncSetting>() {
                });
        archiveInput.setIp(fileSyncSettingInDb.getInput().getIp());
        archiveInput.setHostname(fileSyncSettingInDb.getInput().getHostname());
        archiveInput.setUseWhitelist(fileSyncSettingInDb.getInput().getUseWhitelist());
        archiveInput.setWhitelist(fileSyncSettingInDb.getInput().getWhitelist());
        archiveInput.setUseBlacklist(fileSyncSettingInDb.getInput().getUseBlacklist());
        archiveInput.setBlacklist(fileSyncSettingInDb.getInput().getBlacklist());
        archiveInput.setUseInclude(fileSyncSettingInDb.getInput().getUseInclude());
        archiveInput.setInclude(fileSyncSettingInDb.getInput().getInclude());
        archiveInput.setUseExclude(fileSyncSettingInDb.getInput().getUseExclude());
        archiveInput.setExclude(fileSyncSettingInDb.getInput().getExclude());
        archiveInput.setUseInnerWhiteList(fileSyncSettingInDb.getInput().getUseInnerWhiteList());
        archiveInput.setInnerWhiteList(fileSyncSettingInDb.getInput().getInnerWhiteList());
        archiveInput.setUseInnerBlackList(fileSyncSettingInDb.getInput().getUseInnerBlackList());
        archiveInput.setInnerBlackList(fileSyncSettingInDb.getInput().getInnerBlackList());

        this.setInput(archiveInput);
        return this;
    }

    /**
     * Input输入对象
     *
     * @return Input输入对象
     */
    @Override
    public ArchiveInput getInput() {
        return input;
    }

    public void setInput(ArchiveInput input) {
        this.input = input;
    }

    public static class ArchiveInput extends BaseInput {
        @Schema(description = "自定义参数")
        private Map<String, String> customParam;

        @NotBlank(message = "文件路径不能为空")
        @Schema(description = "文件路径")
        private String path;
        /**
         * 仅限前端展示
         */
        @Schema(description = "标签列表")
        private List<String> tagList;

        @Schema(description = "是否包含子文件")
        private Boolean recursive;

        @NotBlank(message = "字符集不能为空")
        @Schema(description = "字符集")
        private String charset;

        @Schema(description = "多行合并")
        private String multiline;

        @NotNull(message = "是否使用文件白名单不能为空")
        @Schema(description = "是否使用文件白名单, true表示使用")
        private Boolean useWhitelist;

        @Schema(description = "文件白名单")
        private List<String> whitelist;

        @NotNull(message = "是否使用文件黑名单不能为空")
        @Schema(description = "是否使用文件黑名单, true表示使用")
        private Boolean useBlacklist;

        @Schema(description = "文件黑名单")
        private List<String> blacklist;

        @NotNull(message = "是否使用包含内容不能为空")
        @Schema(description = "是否使用包含内容, true表示使用")
        private Boolean useInclude;

        @Schema(description = "包含内容")
        private List<String> include;

        @NotNull(message = "是否使用排除文件内容不能为空")
        @Schema(description = "是否使用排除文件内容, true表示使用")
        private Boolean useExclude;

        @Schema(description = "排除文件内容")
        private List<String> exclude;

        @NotNull(message = "指定文件采集顺序不能为空")
        @Schema(description = "指定文件采集顺序")
        private String openFileSort;

        @NotNull(message = "单条消息最大长度不能为空")
        @Schema(description = "单条消息最大长度")
        private Integer msgMaxLength;

        /**
         * 这个参数不在前端展示，但是这个参数cell接口需要，固定为true
         */
        private Boolean savepos = true;

        @NotNull(message = "是否使用子文件白名单")
        @Schema(description = "是否使用子文件白名单, true表示使用")
        private Boolean useInnerWhiteList;

        /**
         * 子文件白名单
         */
        @Schema(description = "子文件白名单")
        private List<String> innerWhiteList;

        @NotNull(message = "是否使用子文件黑名单")
        @Schema(description = "是否使用子文件黑名单, true表示使用")
        private Boolean useInnerBlackList;

        /**
         * 子文件黑名单
         */
        @Schema(description = "子文件黑名单")
        private List<String> innerBlackList;

        /**
         * 网络带宽权重
         *
         * @return 权重值
         */
        @Override
        public Integer getBandwidthweight() {
            return 1;
        }

        /**
         * 单次发送最大消息数量
         *
         * @return 批次数量
         */
        @Override
        public Integer getBatch() {
            return 500;
        }

        public Map<String, String> getCustomParam() {
            return customParam;
        }

        public void setCustomParam(Map<String, String> customParam) {
            this.customParam = customParam;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public List<String> getTagList() {
            return tagList;
        }

        public void setTagList(List<String> tagList) {
            this.tagList = tagList;
        }

        public Boolean getRecursive() {
            return recursive;
        }

        public void setRecursive(Boolean recursive) {
            this.recursive = recursive;
        }

        public String getCharset() {
            return charset;
        }

        public void setCharset(String charset) {
            this.charset = charset;
        }

        public String getMultiline() {
            return multiline;
        }

        public void setMultiline(String multiline) {
            this.multiline = multiline;
        }

        public Boolean getUseWhitelist() {
            return useWhitelist;
        }

        public void setUseWhitelist(Boolean useWhitelist) {
            this.useWhitelist = useWhitelist;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }

        public Boolean getUseBlacklist() {
            return useBlacklist;
        }

        public void setUseBlacklist(Boolean useBlacklist) {
            this.useBlacklist = useBlacklist;
        }

        public List<String> getBlacklist() {
            return blacklist;
        }

        public void setBlacklist(List<String> blacklist) {
            this.blacklist = blacklist;
        }

        public Boolean getUseInclude() {
            return useInclude;
        }

        public void setUseInclude(Boolean useInclude) {
            this.useInclude = useInclude;
        }

        public List<String> getInclude() {
            return include;
        }

        public void setInclude(List<String> include) {
            this.include = include;
        }

        public Boolean getUseExclude() {
            return useExclude;
        }

        public void setUseExclude(Boolean useExclude) {
            this.useExclude = useExclude;
        }

        public List<String> getExclude() {
            return exclude;
        }

        public void setExclude(List<String> exclude) {
            this.exclude = exclude;
        }

        public String getOpenFileSort() {
            return openFileSort;
        }

        public void setOpenFileSort(String openFileSort) {
            this.openFileSort = openFileSort;
        }

        public Integer getMsgMaxLength() {
            return msgMaxLength;
        }

        public void setMsgMaxLength(Integer msgMaxLength) {
            this.msgMaxLength = msgMaxLength;
        }

        public Boolean getSavepos() {
            return savepos;
        }

        public void setSavepos(Boolean savepos) {
            this.savepos = savepos;
        }

        public Boolean getUseInnerWhiteList() {
            return useInnerWhiteList;
        }

        public void setUseInnerWhiteList(Boolean useInnerWhiteList) {
            this.useInnerWhiteList = useInnerWhiteList;
        }

        public List<String> getInnerWhiteList() {
            return innerWhiteList;
        }

        public void setInnerWhiteList(List<String> innerWhiteList) {
            this.innerWhiteList = innerWhiteList;
        }

        public Boolean getUseInnerBlackList() {
            return useInnerBlackList;
        }

        public void setUseInnerBlackList(Boolean useInnerBlackList) {
            this.useInnerBlackList = useInnerBlackList;
        }

        public List<String> getInnerBlackList() {
            return innerBlackList;
        }

        public void setInnerBlackList(List<String> innerBlackList) {
            this.innerBlackList = innerBlackList;
        }
    }


}
