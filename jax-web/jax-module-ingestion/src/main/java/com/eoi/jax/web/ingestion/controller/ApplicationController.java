package com.eoi.jax.web.ingestion.controller;

import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpParameters;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.ingestion.model.application.*;
import com.eoi.jax.web.ingestion.service.ApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 * @Desc: 弹性作业
 **/
@RestController
public class ApplicationController implements V2Controller {

    @Autowired
    private ApplicationService applicationService;

    @Operation(summary = "弹性作业运维分页查询")
    @PostMapping("ingestion/application/query")
    public Response query(@RequestBody ApplicationQueryReq req) {
        return Response.success(applicationService.getList(req));
    }

    @Operation(summary = "根据id查询详情")
    @GetMapping("ingestion/application/{id}")
    public Response get(@Parameter(description = "弹性作业id", required = true) @PathVariable("id") Long id) {
        return Response.success(applicationService.get(id));
    }

    @Operation(summary = "弹性作业创建")
    @PostMapping("ingestion/application")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.CREATE, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response create(@OpParameters @Valid @RequestBody ApplicationCreateReq req) {
        return Response.success(applicationService.create(req));
    }

    @Operation(summary = "弹性作业创建并启动")
    @PostMapping("ingestion/application/create-start")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.CREATE_START, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response createAndStart(@OpParameters @Valid @RequestBody ApplicationCreateReq req) {
        return Response.success(applicationService.createAndStart(req));
    }

    @Operation(summary = "弹性作业批量启动")
    @PostMapping("ingestion/application/batch-start")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.BATCH_START, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response batchStart(@OpPrimaryKey @RequestBody List<Long> ids,
                               @Parameter(description = "是否强制启动", required = false) Boolean forceStart) {
        return Response.success(applicationService.batchStart(ids, forceStart == null ? false : forceStart));
    }

    @Operation(summary = "弹性作业批量停止")
    @PostMapping("ingestion/application/batch-stop")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.BATCH_STOP, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response batchStop(@OpPrimaryKey @RequestBody List<Long> ids) {
        return Response.success(applicationService.batchStop(ids));
    }

    @Operation(summary = "弹性作业更新")
    @PutMapping("ingestion/application/{id}")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.UPDATE, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response update(@OpPrimaryKey(name = "id") @Parameter(description = "弹性作业主键id", required = true) @PathVariable("id") Long id,
                           @OpParameters @Valid @RequestBody ApplicationUpdateReq req) {
        req.setId(id);
        return Response.success(applicationService.update(req));
    }

    @Operation(summary = "弹性作业更新并启动")
    @PutMapping("ingestion/application/{id}/update-start")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.UPDATE_START, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response updateAndStart(@OpPrimaryKey(name = "id") @Parameter(description = "弹性作业主键id", required = true)
                                   @PathVariable("id") Long id,
                                   @OpParameters @Valid @RequestBody ApplicationUpdateReq req) {
        req.setId(id);
        return Response.success(applicationService.updateAndStart(req));
    }

    @Operation(summary = "弹性作业删除")
    @DeleteMapping("ingestion/application/{id}")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.DELETE, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response delete(@OpPrimaryKey(name = "id") @Parameter(description = "弹性作业主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(applicationService.delete(id));
    }

    @Operation(summary = "数据中心查询关联数据")
    @GetMapping("ingestion/application/{id}/usage")
    public Response usage(@Parameter(description = "弹性作业主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(applicationService.usage(id));
    }

    @Operation(summary = "启动")
    @GetMapping("ingestion/application/{id}/start")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.START, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response start(@OpPrimaryKey(name = "id") @Parameter(description = "弹性作业主键id", required = true) @PathVariable("id") Long id,
                          @Parameter(description = "是否强制启动", required = false) Boolean forceStart) {
        return Response.success(applicationService.start(id, forceStart == null ? false : forceStart));
    }

    @Operation(summary = "停止")
    @GetMapping("ingestion/application/{id}/stop")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.STOP, module = "弹性作业", function = "弹性作业管理", code = "application")
    public Response stop(@OpPrimaryKey(name = "id") @Parameter(description = "弹性作业主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(applicationService.stop(id));
    }

    @Operation(summary = "弹性作业操作日志")
    @GetMapping("ingestion/application/{id}/log")
    public Response log(@Parameter(description = "作业id", required = true) @PathVariable("id") Long id) {
        return Response.success(applicationService.listLog(id));
    }

    @Operation(summary = "弹性作业日志详情")
    @GetMapping("ingestion/application/{id}/console")
    public Response console(
            @Parameter(description = "作业id", required = true) @PathVariable("id") Long id,
            @RequestParam(value = "opId", required = false) Long opId,
            @RequestParam(value = "pageIndex", defaultValue = "0") Integer pageIndex,
            @RequestParam(value = "pageSize", defaultValue = "1000") Integer pageSize
    ) {
        return Response.success(applicationService.pageConsole(id, opId, pageIndex, pageSize));
    }

    @Operation(summary = "弹性作业刷新状态")
    @GetMapping("ingestion/application/{id}/refresh")
    public Response refresh(@Parameter(description = "作业id", required = true) @PathVariable("id") Long id) {
        return Response.success(applicationService.refresh(id));
    }

    @Operation(summary = "获取弹性作业实例信息")
    @GetMapping("ingestion/application/{id}/instance")
    public Response appInstanceInfo(@Parameter(description = "作业id", required = true) @PathVariable("id") Long id) {
        return Response.success(applicationService.getAppInstanceInfo(id));
    }

    @Operation(summary = "弹性作业扩缩容/kill容器")
    @AuditLog(category = "数据开发", opAction = OpActionEnum.SCALE, module = "弹性作业", function = "弹性作业管理", code = "application")
    @PostMapping("ingestion/application/{id}/scale")
    public Response scale(@OpPrimaryKey @Parameter(description = "作业id", required = true) @PathVariable("id") Long id,
                          @Valid @RequestBody ApplicationScaleReq req) {
        return Response.success(applicationService.scaleApplication(id, req));
    }

    @Operation(summary = "获取yarn application日志")
    @GetMapping("ingestion/application/{id}/yarn-application/{yarnApplicationId}/log")
    public Response yarnAppLog(
            @Parameter(description = "id", required = true) @PathVariable("id") Long id,
            @Parameter(description = "yarnApplicationId", required = true) @PathVariable("yarnApplicationId") String yarnApplicationId
    ) {
        return Response.success(applicationService.getYarnAppLog(id, yarnApplicationId));
    }

    @Operation(summary = "获取yarn application日志内容")
    @GetMapping("ingestion/application/{id}/yarn-application-log")
    public ResponseEntity<byte[]> yarnLogContent(
            @Parameter(description = "id", required = true) @PathVariable("id") Long id,
            @RequestParam(value = "url") String url
    ) {
        return ResponseEntity.ok()
                .header("Content-Type", "text/plain; charset=UTF-8")
                .body(applicationService.getYarnAppLogContent(id, url));
    }

    @Operation(summary = "下载yarn application日志内容")
    @GetMapping("ingestion/application/{id}/yarn-application-log/download")
    public void download(
            @Parameter(description = "id", required = true) @PathVariable("id") Long id,
            @RequestParam(value = "url") String url,
            final HttpServletResponse response
    ) throws IOException {
        byte[] bytes = applicationService.getYarnAppLogContent(id, url);
        response.reset();
        response.setContentType("application/octet-stream");
        try {
            String filename = url.substring(url.lastIndexOf("/") + 1);
            filename = filename.substring(0, filename.lastIndexOf("?"));
            response.addHeader("Content-Disposition", "filename=" + filename);
        } catch (Exception ignore) {
        }
        response.getOutputStream().write(bytes);
    }
}
