package com.eoi.jax.web.ingestion.model.ingestioninfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.integration.model.cell.agent.CellTaskInput;
import com.eoi.jax.web.core.integration.model.cell.agent.CellTaskInputParam;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class FileSyncSetting extends AbstractSetting {

    @NotNull(message = "输入参数不能为空")
    private FileInput input;

    public FileSyncSetting() {
    }

    public FileSyncSetting(FileInput input) {
        this.input = input;
    }

    @Override
    public List<String> tags() {
        return this.input.getTagList();
    }

    @Override
    public void valid(AbstractSetting settingInDb, Boolean isPublished) {
        Assert.isFalse(this.input.getCharset().contains("失败"), "自动推荐字符集失败，请手动选择字符集");
        if (CollUtil.isNotEmpty(this.input.getTagList())) {
            Assert.isTrue(this.input.getTagList().stream().distinct().count() == this.input.getTagList().size(),
                    "标签存在重复值");
        }
        if (CollUtil.isNotEmpty(this.input.getCustomParam())) {
            Assert.isTrue(this.input.getCustomParam().keySet().stream().distinct().count() == this.input.getCustomParam().size(),
                    "自定义参数存在重复key");
        }

        if (Objects.isNull(settingInDb) || !isPublished) {
            return;
        }
        Map<String, Pair<Object, Object>> compareResultMap = ModelBeanUtil.compareBeanFields(this.input, settingInDb.getInput(),
                "incremental", "backoff", "hashEnable", "openFileSort", "fileExpiredEnable");
        if (CollUtil.isEmpty(compareResultMap)) {
            return;
        }
        Assert.isNull(compareResultMap.get("incremental"), "[仅采集增量数据]任务发布后不允许修改");
        Assert.isNull(compareResultMap.get("backoff"), "[已读文件延后检查]任务发布后不允许修改");
        Assert.isNull(compareResultMap.get("hashEnable"), "[日志哈希标识]任务发布后不允许修改");
        Assert.isNull(compareResultMap.get("openFileSort"), "[指定文件采集顺序]任务发布后不允许修改");
        Assert.isNull(compareResultMap.get("fileExpiredEnable"), "[海量文件处理策略]任务发布后不允许修改");
    }

    @Override
    public String buildInputParam(ConvertToCellTaskInputParam convertToCellTaskInputParam) {
        CellTaskInput<CellTaskInputParam.FileInput> cellTaskInput = new CellTaskInput<>();
        cellTaskInput.setAgentId(convertToCellTaskInputParam.getAgentId());
        cellTaskInput.setInputType(convertToCellTaskInputParam.getType().inputType());

        CellTaskInputParam.FileInput fileInputParam = ModelBeanUtil.copyBean(this.input,
                new CellTaskInputParam.FileInput(), "customParam", "tagList", "ignoreOlderThan", "actionMinWait",
                "whitelist", "blacklist", "include", "exclude");
        List<String> pathList = StrUtil.splitTrim(this.input.getPath(), ";");
        fileInputParam.setPath(pathList.stream().map(String::trim).collect(Collectors.joining(",")));
        fileInputParam.setIgnoreOlderThan(this.input.getIgnoreOlderThan().toSeconds());
        fileInputParam.setActionMinWait(this.input.getActionMinWait().toSeconds());
        fileInputParam.setId(convertToCellTaskInputParam.getTaskId().toString());
        fileInputParam.setEnable(convertToCellTaskInputParam.getEnable());
        fileInputParam.setMode("normal");
        if (Optional.ofNullable(this.input.getUseWhitelist()).orElse(false)) {
            fileInputParam.setWhitelist(this.input.getWhitelist());
        }
        if (Optional.ofNullable(this.input.getUseBlacklist()).orElse(false)) {
            fileInputParam.setBlacklist(this.input.getBlacklist());
        }
        if (Optional.ofNullable(this.input.getUseInclude()).orElse(false)) {
            fileInputParam.setInclude(this.input.getInclude());
        }
        if (Optional.ofNullable(this.input.getUseExclude()).orElse(false)) {
            fileInputParam.setExclude(this.input.getExclude());
        }

        Map<String, String> fieldMap = new HashMap<>(4);
        fieldMap.put("@topic", convertToCellTaskInputParam.getTopicName());
        fieldMap.put("@tags", String.join(",", Optional.ofNullable(this.input.getTagList()).orElse(CollUtil.newArrayList())));
        fieldMap.put("@ip", "{{.agent_ip}}");
        if (CollUtil.isNotEmpty(this.input.getCustomParam())) {
            for (Map.Entry<String, String> e : this.input.getCustomParam().entrySet()) {
                fieldMap.put("@" + e.getKey(), e.getValue());
            }
        }
        fileInputParam.setFields(fieldMap);

        cellTaskInput.setInputParams(fileInputParam);
        return JsonUtil.encode(CollUtil.newArrayList(cellTaskInput));
    }

    public FileSyncSetting fromCellTaskInputStr(String inputStr, ConvertToAbstractSettingParam convertToAbstractSettingParam) {
        List<CellTaskInput<CellTaskInputParam.FileInput>> cellTaskInputList = JsonUtil.decode(inputStr,
                new TypeReference<List<CellTaskInput<CellTaskInputParam.FileInput>>>() {
                });
        CellTaskInput<CellTaskInputParam.FileInput> cellTaskInput = cellTaskInputList.get(0);
        CellTaskInputParam.FileInput inputParams = cellTaskInput.getInputParams();

        FileSyncSetting.FileInput fileInput = ModelBeanUtil.copyBean(inputParams, new FileSyncSetting.FileInput(),
                "ignoreOlderThan", "actionMinWait");

        Map<String, String> fields = new HashMap<>(inputParams.getFields().size());
        fields.putAll(inputParams.getFields());
        fileInput.setTagList(Arrays.stream(fields.getOrDefault("@tags", "").split(",")).collect(Collectors.toList()));
        fields.remove("@topic");
        fields.remove("@tags");
        fields.remove("@ip");
        Map<String, String> customParam = new HashMap<>(4);
        for (Map.Entry<String, String> entry : fields.entrySet()) {
            customParam.put(entry.getKey().substring(1), entry.getValue());
        }
        fileInput.setCustomParam(customParam);
        // 设置其它的参数

        // 根据数据库里的单位进行转换
        FileSyncSetting fileSyncSettingInDb = JsonUtil.decode(convertToAbstractSettingParam.getTbIngestionJobTask().getSetting(),
                new TypeReference<FileSyncSetting>() {
                });
        fileInput.setActionMinWait(TimeWrap.build(inputParams.getActionMinWait(),
                fileSyncSettingInDb.getInput().getActionMinWait().getUnit()));
        fileInput.setIgnoreOlderThan(TimeWrap.build(inputParams.getIgnoreOlderThan(),
                fileSyncSettingInDb.getInput().getIgnoreOlderThan().getUnit()));
        fileInput.setIp(fileSyncSettingInDb.getInput().getIp());
        fileInput.setHostname(fileSyncSettingInDb.getInput().getHostname());
        fileInput.setUseWhitelist(fileSyncSettingInDb.getInput().getUseWhitelist());
        fileInput.setWhitelist(fileSyncSettingInDb.getInput().getWhitelist());
        fileInput.setUseBlacklist(fileSyncSettingInDb.getInput().getUseBlacklist());
        fileInput.setBlacklist(fileSyncSettingInDb.getInput().getBlacklist());
        fileInput.setUseInclude(fileSyncSettingInDb.getInput().getUseInclude());
        fileInput.setInclude(fileSyncSettingInDb.getInput().getInclude());
        fileInput.setUseExclude(fileSyncSettingInDb.getInput().getUseExclude());
        fileInput.setExclude(fileSyncSettingInDb.getInput().getExclude());

        this.setInput(fileInput);
        return this;
    }

    @Override
    public FileInput getInput() {
        return input;
    }

    public void setInput(FileInput input) {
        this.input = input;
    }


    public static class FileInput extends BaseInput {
        @Schema(description = "自定义参数")
        private Map<String, String> customParam;

        @NotBlank(message = "文件路径不能为空")
        @Schema(description = "文件路径")
        private String path;
        /**
         * 仅限前端展示
         */
        @Schema(description = "标签列表")
        private List<String> tagList;

        @Schema(description = "是否包含子文件")
        private Boolean recursive;

        @NotBlank(message = "字符集不能为空")
        @Schema(description = "字符集")
        private String charset;

        @Schema(description = "多行合并")
        private String multiline;

        @NotNull(message = "是否使用文件白名单不能为空")
        @Schema(description = "是否使用文件白名单, true表示使用")
        private Boolean useWhitelist;

        @Schema(description = "文件白名单")
        private List<String> whitelist;

        @NotNull(message = "是否使用文件黑名单不能为空")
        @Schema(description = "是否使用文件黑名单, true表示使用")
        private Boolean useBlacklist;

        @Schema(description = "文件黑名单")
        private List<String> blacklist;

        @NotNull(message = "是否使用包含内容不能为空")
        @Schema(description = "是否使用包含内容, true表示使用")
        private Boolean useInclude;

        @Schema(description = "包含内容")
        private List<String> include;

        @NotNull(message = "是否使用排除文件内容不能为空")
        @Schema(description = "是否使用排除文件内容, true表示使用")
        private Boolean useExclude;

        @Schema(description = "排除文件内容")
        private List<String> exclude;

        @NotNull(message = "不采集此时间前的文件不能为空")
        @Schema(description = "不采集此时间前的文件")
        private TimeWrap ignoreOlderThan;

        @NotNull(message = "仅采集增量数据不能为空")
        @Schema(description = "仅采集增量数据")
        private Boolean incremental;

        @NotNull(message = "文件重命名检查不能为空")
        @Schema(description = "文件重命名检查")
        private Boolean renamecheck;

        @NotNull(message = "文件尾无结束标志时等待不能为空")
        @Schema(description = "文件尾无结束标志时等待")
        private Integer flushPartial;

        @NotNull(message = "已经打开文件检查新数据间隔不能为空")
        @Schema(description = "已经打开文件检查新数据间隔")
        private Integer pollInterval;

        @NotNull(message = "检测新文件产生的时间间隔不能为空")
        @Schema(description = "检测新文件产生的时间间隔")
        private Integer dircheckInterval;
        /**
         * backoffFactor
         * maxBackoff
         */
        @NotNull(message = "已读文件延后检查不能为空")
        @Schema(description = "已读文件延后检查")
        private Integer backoff;
        /**
         * 已读文件延后检查间隔增长因子,前端无需配置
         */
        private Integer backoffFactor = 2;
        /**
         * 已读文件延后检查间隔增长上限,前端无需配置
         */
        private Integer maxBackoff = 60;

        @NotNull(message = "文件读后处理不能为空")
        @Schema(description = "文件读后处理，NONE、RENAME、DELETE")
        private String actionWhenEof;

        @NotNull(message = "文件读后处理等待时间不能为空")
        @Schema(description = "文件读后处理等待时间")
        private TimeWrap actionMinWait;

        @NotNull(message = "日志定时采集不能为空")
        @Schema(description = "日志定时采集")
        private String workTimeInterval;

        @NotNull(message = "缓存刷新间隔不能为空")
        @Schema(description = "缓存刷新间隔")
        private Integer flushCacheInterval;

        @NotNull(message = "日志哈希标识不能为空")
        @Schema(description = "日志哈希标识")
        private Boolean hashEnable;

        @NotNull(message = "哈希计算长度不能为空")
        @Schema(description = "哈希计算长度")
        private Integer hashDataLength;

        @NotNull(message = "过滤小于“哈希计算长度”的文件不能为空")
        @Schema(description = "过滤小于“哈希计算长度”的文件")
        private Boolean hashWaitData;

        @NotNull(message = "同时采集文件数量不能为空")
        @Schema(description = "同时采集文件数量")
        private Integer openfiles;

        @NotNull(message = "单条消息最大长度不能为空")
        @Schema(description = "单条消息最大长度")
        private Integer msgMaxLength;

        @NotNull(message = "单次发送最大消息数量不能为空")
        @Schema(description = "单次发送最大消息数量")
        private Integer batch;

        @NotNull(message = "指定文件采集顺序不能为空")
        @Schema(description = "指定文件采集顺序")
        private String openFileSort;

        @NotNull(message = "忽略软链接文件不能为空")
        @Schema(description = "忽略软链接文件")
        private Boolean ignoreSymlink;

        @NotNull(message = "网络带宽权重不能为空")
        @Schema(description = "网络带宽权重")
        private Integer bandwidthweight;

        @NotNull(message = "海量文件处理策略不能为空")
        @Schema(description = "海量文件处理策略,当为true时，才展示maxAddFiles、fileExpiredTime、dirExpiredTime、expiredCheckInterval四个参数")
        private Boolean fileExpiredEnable;

        @NotNull(message = "单次扫描最多文件数不能为空")
        @Schema(description = "单次扫描最多文件数")
        private Integer maxAddFiles;

        @NotNull(message = "文件过期时间不能为空")
        @Schema(description = "文件过期时间")
        private Integer fileExpiredTime;

        @NotNull(message = "目录过期时间不能为空")
        @Schema(description = "目录过期时间")
        private Integer dirExpiredTime;

        @NotNull(message = "过期文件是否存在检查间隔不能为空")
        @Schema(description = "过期文件是否存在检查间隔")
        private Integer expiredCheckInterval;

        @NotNull(message = "多行合并等待不能为空")
        @Schema(description = "多行合并等待")
        private Boolean waitOnEof;
        /**
         * 这个参数不在前端展示，但是这个参数cell接口需要，固定为true
         */
        private Boolean savepos = true;
        /**
         *
         */
        private List<String> innerWhiteList = new ArrayList<>();
        /**
         *
         */
        private List<String> innerBlackList = new ArrayList<>();

        public Map<String, String> getCustomParam() {
            return customParam;
        }

        public void setCustomParam(Map<String, String> customParam) {
            this.customParam = customParam;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public List<String> getTagList() {
            return tagList;
        }

        public void setTagList(List<String> tagList) {
            this.tagList = tagList;
        }

        public Boolean getRecursive() {
            return recursive;
        }

        public void setRecursive(Boolean recursive) {
            this.recursive = recursive;
        }

        public String getCharset() {
            return charset;
        }

        public void setCharset(String charset) {
            this.charset = charset;
        }

        public String getMultiline() {
            return multiline;
        }

        public void setMultiline(String multiline) {
            this.multiline = multiline;
        }

        public Boolean getUseWhitelist() {
            return useWhitelist;
        }

        public void setUseWhitelist(Boolean useWhitelist) {
            this.useWhitelist = useWhitelist;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }

        public Boolean getUseBlacklist() {
            return useBlacklist;
        }

        public void setUseBlacklist(Boolean useBlacklist) {
            this.useBlacklist = useBlacklist;
        }

        public List<String> getBlacklist() {
            return blacklist;
        }

        public void setBlacklist(List<String> blacklist) {
            this.blacklist = blacklist;
        }

        public Boolean getUseInclude() {
            return useInclude;
        }

        public void setUseInclude(Boolean useInclude) {
            this.useInclude = useInclude;
        }

        public List<String> getInclude() {
            return include;
        }

        public void setInclude(List<String> include) {
            this.include = include;
        }

        public Boolean getUseExclude() {
            return useExclude;
        }

        public void setUseExclude(Boolean useExclude) {
            this.useExclude = useExclude;
        }

        public List<String> getExclude() {
            return exclude;
        }

        public void setExclude(List<String> exclude) {
            this.exclude = exclude;
        }

        public TimeWrap getIgnoreOlderThan() {
            return ignoreOlderThan;
        }

        public void setIgnoreOlderThan(TimeWrap ignoreOlderThan) {
            this.ignoreOlderThan = ignoreOlderThan;
        }

        public Boolean getIncremental() {
            return incremental;
        }

        public void setIncremental(Boolean incremental) {
            this.incremental = incremental;
        }

        public Boolean getRenamecheck() {
            return renamecheck;
        }

        public void setRenamecheck(Boolean renamecheck) {
            this.renamecheck = renamecheck;
        }

        public Integer getFlushPartial() {
            return flushPartial;
        }

        public void setFlushPartial(Integer flushPartial) {
            this.flushPartial = flushPartial;
        }

        public Integer getPollInterval() {
            return pollInterval;
        }

        public void setPollInterval(Integer pollInterval) {
            this.pollInterval = pollInterval;
        }

        public Integer getDircheckInterval() {
            return dircheckInterval;
        }

        public void setDircheckInterval(Integer dircheckInterval) {
            this.dircheckInterval = dircheckInterval;
        }

        public Integer getBackoff() {
            return backoff;
        }

        public void setBackoff(Integer backoff) {
            this.backoff = backoff;
        }

        public Integer getBackoffFactor() {
            return backoffFactor;
        }

        public void setBackoffFactor(Integer backoffFactor) {
            this.backoffFactor = backoffFactor;
        }

        public Integer getMaxBackoff() {
            return maxBackoff;
        }

        public void setMaxBackoff(Integer maxBackoff) {
            this.maxBackoff = maxBackoff;
        }

        public String getActionWhenEof() {
            return actionWhenEof;
        }

        public void setActionWhenEof(String actionWhenEof) {
            this.actionWhenEof = actionWhenEof;
        }

        public TimeWrap getActionMinWait() {
            return actionMinWait;
        }

        public void setActionMinWait(TimeWrap actionMinWait) {
            this.actionMinWait = actionMinWait;
        }

        public String getWorkTimeInterval() {
            return workTimeInterval;
        }

        public void setWorkTimeInterval(String workTimeInterval) {
            this.workTimeInterval = workTimeInterval;
        }

        public Integer getFlushCacheInterval() {
            return flushCacheInterval;
        }

        public void setFlushCacheInterval(Integer flushCacheInterval) {
            this.flushCacheInterval = flushCacheInterval;
        }

        public Boolean getHashEnable() {
            return hashEnable;
        }

        public void setHashEnable(Boolean hashEnable) {
            this.hashEnable = hashEnable;
        }

        public Integer getHashDataLength() {
            return hashDataLength;
        }

        public void setHashDataLength(Integer hashDataLength) {
            this.hashDataLength = hashDataLength;
        }

        public Boolean getHashWaitData() {
            return hashWaitData;
        }

        public void setHashWaitData(Boolean hashWaitData) {
            this.hashWaitData = hashWaitData;
        }

        public Integer getOpenfiles() {
            return openfiles;
        }

        public void setOpenfiles(Integer openfiles) {
            this.openfiles = openfiles;
        }

        public Integer getMsgMaxLength() {
            return msgMaxLength;
        }

        public void setMsgMaxLength(Integer msgMaxLength) {
            this.msgMaxLength = msgMaxLength;
        }

        @Override
        public Integer getBatch() {
            return batch;
        }

        public void setBatch(Integer batch) {
            this.batch = batch;
        }

        public String getOpenFileSort() {
            return openFileSort;
        }

        public void setOpenFileSort(String openFileSort) {
            this.openFileSort = openFileSort;
        }

        public Boolean getIgnoreSymlink() {
            return ignoreSymlink;
        }

        public void setIgnoreSymlink(Boolean ignoreSymlink) {
            this.ignoreSymlink = ignoreSymlink;
        }

        @Override
        public Integer getBandwidthweight() {
            return bandwidthweight;
        }

        public void setBandwidthweight(Integer bandwidthweight) {
            this.bandwidthweight = bandwidthweight;
        }

        public Boolean getFileExpiredEnable() {
            return fileExpiredEnable;
        }

        public void setFileExpiredEnable(Boolean fileExpiredEnable) {
            this.fileExpiredEnable = fileExpiredEnable;
        }

        public Integer getMaxAddFiles() {
            return maxAddFiles;
        }

        public void setMaxAddFiles(Integer maxAddFiles) {
            this.maxAddFiles = maxAddFiles;
        }

        public Integer getFileExpiredTime() {
            return fileExpiredTime;
        }

        public void setFileExpiredTime(Integer fileExpiredTime) {
            this.fileExpiredTime = fileExpiredTime;
        }

        public Integer getDirExpiredTime() {
            return dirExpiredTime;
        }

        public void setDirExpiredTime(Integer dirExpiredTime) {
            this.dirExpiredTime = dirExpiredTime;
        }

        public Integer getExpiredCheckInterval() {
            return expiredCheckInterval;
        }

        public void setExpiredCheckInterval(Integer expiredCheckInterval) {
            this.expiredCheckInterval = expiredCheckInterval;
        }

        public Boolean getWaitOnEof() {
            return waitOnEof;
        }

        public void setWaitOnEof(Boolean waitOnEof) {
            this.waitOnEof = waitOnEof;
        }

        public Boolean getSavepos() {
            return savepos;
        }

        public void setSavepos(Boolean savepos) {
            this.savepos = savepos;
        }

        public List<String> getInnerWhiteList() {
            return innerWhiteList;
        }

        public void setInnerWhiteList(List<String> innerWhiteList) {
            this.innerWhiteList = innerWhiteList;
        }

        public List<String> getInnerBlackList() {
            return innerBlackList;
        }

        public void setInnerBlackList(List<String> innerBlackList) {
            this.innerBlackList = innerBlackList;
        }
    }
}
