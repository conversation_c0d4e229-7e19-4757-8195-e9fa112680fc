package com.eoi.jax.web.ingestion.model.blueking.mapping;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.repository.entity.TbBluekingIntegrationMapping;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/4
 */
public class BluekingIntegrationMapping {

    /**
     * 主键
     */
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 同步配置id
     */
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "同步配置id")
    private Long configId;

    /**
     * 蓝鲸源模型id
     */
    @NotBlank(message = "蓝鲸源模型id不能为空")
    @Schema(description = "蓝鲸源模型id")
    private String sourceModel;


    /**
     * 蓝鲸源模型路径，"/"分割
     */
    @Schema(description = "蓝鲸源模型路径")
    private String sourceModelPath;

    /**
     * 蓝鲸源模型路径中文，"/"分割
     */
    @Schema(description = "蓝鲸源模型路径中文")
    private String sourceModelPathZh;

    /**
     * 目标模型id
     */
    @NotNull(message = "目标模型id不能为空")
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "目标模型id")
    private Long targetModel;

    /**
     * 目标模型表名称
     */
    @Schema(description = "目标模型表名称")
    private String targetModelTbName;

    /**
     * 目标模型路径，"/"分割
     */
    @Schema(description = "目标模型路径")
    private String targetModelPath;

    /**
     * 目标模型路径中文，"/"分割过
     */
    @Schema(description = "目标模型路径中文")
    private String targetModelPathZh;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private String filterScript;

    /**
     * 字段映射，JSON结构
     */
    @Schema(description = "字段映射")
    private List<BluekingFieldMapping> fieldMapping;

    /**
     * 关系映射，JSON结构
     */
    @Schema(description = "关系映射")
    private List<BluekingRelationMapping> relationMapping;


    public TbBluekingIntegrationMapping toEntity() {
        TbBluekingIntegrationMapping tbBluekingIntegrationMapping = ModelBeanUtil.copyBean(this, new TbBluekingIntegrationMapping());

        if (CollUtil.isNotEmpty(this.getFieldMapping())) {
            tbBluekingIntegrationMapping.setFieldMapping(JsonUtil.encode(this.getFieldMapping()));
        }
        if (CollUtil.isNotEmpty(this.getRelationMapping())) {
            tbBluekingIntegrationMapping.setRelationMapping(JsonUtil.encode(this.getRelationMapping()));
        }
        return tbBluekingIntegrationMapping;
    }


    public BluekingIntegrationMapping fromEntity(TbBluekingIntegrationMapping tbBluekingIntegrationMapping) {
        BluekingIntegrationMapping bluekingIntegrationMapping =
            ModelBeanUtil.copyBean(tbBluekingIntegrationMapping, new BluekingIntegrationMapping());

        if (StrUtil.isNotBlank(tbBluekingIntegrationMapping.getFieldMapping())) {
            bluekingIntegrationMapping.setFieldMapping(JsonUtil.decode(tbBluekingIntegrationMapping.getFieldMapping(),
                new TypeReference<List<BluekingFieldMapping>>() {
                }));
        }

        if (StrUtil.isNotBlank(tbBluekingIntegrationMapping.getRelationMapping())) {
            bluekingIntegrationMapping.setRelationMapping(JsonUtil.decode(tbBluekingIntegrationMapping.getRelationMapping(),
                new TypeReference<List<BluekingRelationMapping>>() {
                }));
        }
        return bluekingIntegrationMapping;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public String getSourceModel() {
        return sourceModel;
    }

    public void setSourceModel(String sourceModel) {
        this.sourceModel = sourceModel;
    }

    public String getSourceModelPath() {
        return sourceModelPath;
    }

    public void setSourceModelPath(String sourceModelPath) {
        this.sourceModelPath = sourceModelPath;
    }

    public String getSourceModelPathZh() {
        return sourceModelPathZh;
    }

    public void setSourceModelPathZh(String sourceModelPathZh) {
        this.sourceModelPathZh = sourceModelPathZh;
    }

    public Long getTargetModel() {
        return targetModel;
    }

    public void setTargetModel(Long targetModel) {
        this.targetModel = targetModel;
    }

    public String getTargetModelTbName() {
        return targetModelTbName;
    }

    public void setTargetModelTbName(String targetModelTbName) {
        this.targetModelTbName = targetModelTbName;
    }

    public String getTargetModelPath() {
        return targetModelPath;
    }

    public void setTargetModelPath(String targetModelPath) {
        this.targetModelPath = targetModelPath;
    }

    public String getTargetModelPathZh() {
        return targetModelPathZh;
    }

    public void setTargetModelPathZh(String targetModelPathZh) {
        this.targetModelPathZh = targetModelPathZh;
    }

    public String getFilterScript() {
        return filterScript;
    }

    public void setFilterScript(String filterScript) {
        this.filterScript = filterScript;
    }

    public List<BluekingFieldMapping> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(List<BluekingFieldMapping> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }

    public List<BluekingRelationMapping> getRelationMapping() {
        return relationMapping;
    }

    public void setRelationMapping(List<BluekingRelationMapping> relationMapping) {
        this.relationMapping = relationMapping;
    }
}
