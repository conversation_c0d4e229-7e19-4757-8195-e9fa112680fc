package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeCreateReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeListResp;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeMoveReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeQueryReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeResp;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeUpdateReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.DataDevelopmentQueryReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.FlowTreeWithOrderQuery;
import com.eoi.jax.web.ingestion.model.businessflowtree.TreeNode;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public interface BusinessFlowTreeService {

    /**
     * 树
     *
     * @param search
     * @return
     */
    List<TreeNode> tree(FlowTreeWithOrderQuery search);

    /**
     * 分页查询
     *
     * @param req
     * @return
     */
    Paged<BusinessFlowTreeResp> query(BusinessFlowTreeQueryReq req);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    BusinessFlowTreeResp get(Long id);

    /**
     * 根据作业状态获取树节点的状态
     *
     * @param status
     * @return
     */
    String convert2TreeStatus(String status);

    /**
     * 创建
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowTreeResp create(BusinessFlowTreeCreateReq req);

    /**
     * 更新
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowTreeResp update(BusinessFlowTreeUpdateReq req);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowTreeResp delete(Long id);

    /**
     * 根据业务流程获取树
     *
     * @param type
     * @param referenceId
     * @return
     */
    BusinessFlowTreeResp getOne(String type, Long referenceId);

    /**
     * 删除
     *
     * @param type
     * @param referenceId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowTreeResp delete(String type, Long referenceId);

    /**
     * 查询全部
     *
     * @param req
     * @return
     */
    List<BusinessFlowTreeResp> all(BusinessFlowTreeQueryReq req);

    /**
     * 查询关联对象
     *
     * @param referenceType
     * @param referenceId
     * @return
     */
    Object getReferenceObject(String referenceType, Long referenceId);

    /**
     * 移动
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowTreeResp move(BusinessFlowTreeMoveReq req);

    /**
     * 根据类型获取数据开发树
     *
     * @param search
     * @return
     */
    List<TreeNode> treeByNodeType(FlowTreeWithOrderQuery search);

    /**
     * 分页查询数据开发树
     *
     * @param search
     * @return
     */
    Paged<BusinessFlowTreeListResp> queryList(DataDevelopmentQueryReq search);
}
