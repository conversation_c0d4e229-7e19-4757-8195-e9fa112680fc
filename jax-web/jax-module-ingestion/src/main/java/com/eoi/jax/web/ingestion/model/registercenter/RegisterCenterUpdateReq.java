package com.eoi.jax.web.ingestion.model.registercenter;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbRegisterCenter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.Set;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class RegisterCenterUpdateReq implements IUpdateModel<TbRegisterCenter> {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "注册中心名称")
    @NotBlank(message = "注册中心名称不能为空")
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "注册中心类型：（默认）NACOS, ZK, EUREKA")
    @NotNull(message = "注册中心类型不能为空")
    @Pattern(regexp = "^NACOS|ZK|EUREKA$", message = "注册中心类型不存在")
    private String type;

    @Schema(description = "注册中心地址")
    @NotBlank(message = "注册中心地址不能为空")
    private String address;

    @Schema(description = "注册中心命名空间")
    private String namespace;

    @Schema(description = "注册中心用户名")
    private String username;

    @Schema(description = "注册中心密码")
    private String password;

    @Schema(description = "数据中心id集合")
    private Set<Long> dataCenterIds;

    public Set<Long> getDataCenterIds() {
        return dataCenterIds;
    }

    public void setDataCenterIds(Set<Long> dataCenterIds) {
        this.dataCenterIds = dataCenterIds;
    }
    @Override
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
