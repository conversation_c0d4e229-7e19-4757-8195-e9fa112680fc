package com.eoi.jax.web.ingestion.model.jar;

import com.eoi.jax.web.core.common.audit.OpPrimaryName;

/**
 * <AUTHOR>
 * @date 2023/2/28
 */
public class JarUploadReq {
    @OpPrimaryName
    private String jarName;
    private String jarType;
    private String jarVersion;
    private String jarFile;
    private String jarDescription;
    private Long clusterId;

    public String getJarName() {
        return jarName;
    }

    public void setJarName(String jarName) {
        this.jarName = jarName;
    }

    public String getJarType() {
        return jarType;
    }

    public void setJarType(String jarType) {
        this.jarType = jarType;
    }

    public String getJarVersion() {
        return jarVersion;
    }

    public void setJarVersion(String jarVersion) {
        this.jarVersion = jarVersion;
    }

    public String getJarFile() {
        return jarFile;
    }

    public void setJarFile(String jarFile) {
        this.jarFile = jarFile;
    }

    public String getJarDescription() {
        return jarDescription;
    }

    public void setJarDescription(String jarDescription) {
        this.jarDescription = jarDescription;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }
}
