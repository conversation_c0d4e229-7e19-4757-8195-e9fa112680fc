package com.eoi.jax.web.ingestion.model.storagehive;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2023/3/31
 */
public class StorageHiveIsModifiedReq extends StorageHiveUpdateReq {

    @Schema(description = "上一次用户编辑后参数md5")
    private String sqlMd5;


    public String getSqlMd5() {
        return sqlMd5;
    }

    public void setSqlMd5(String sqlMd5) {
        this.sqlMd5 = sqlMd5;
    }


}
