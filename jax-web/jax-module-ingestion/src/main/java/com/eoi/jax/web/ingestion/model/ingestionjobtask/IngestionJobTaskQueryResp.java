package com.eoi.jax.web.ingestion.model.ingestionjobtask;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2023/2/6
 */
public class IngestionJobTaskQueryResp {

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "数据集成Id")
    private Long ingestionId;
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "网关id")
    private Long cellId;

    private Agent agent;

    private Task task;

    public static class Agent {

        @JsonSerialize(using = LongStringSerializer.class)
        @Schema(description = "agentId")
        private Long agentId;
        @Schema(description = "hostname")
        private String hostname;
        @Schema(description = "操作系统")
        private String os;
        @Schema(description = "操作系统别名")
        private String osAlias;
        @Schema(description = "ip")
        private String ip;
        @Schema(description = "serverIp")
        private String serverIp;
        @Schema(description = "连接状态, 0 初始化，1 健康，2 离线")
        private Integer connection;

        public Long getAgentId() {
            return agentId;
        }

        public void setAgentId(Long agentId) {
            this.agentId = agentId;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public String getOs() {
            return os;
        }

        public void setOs(String os) {
            this.os = os;
        }

        public String getOsAlias() {
            return osAlias;
        }

        public void setOsAlias(String osAlias) {
            this.osAlias = osAlias;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getServerIp() {
            return serverIp;
        }

        public void setServerIp(String serverIp) {
            this.serverIp = serverIp;
        }

        public Integer getConnection() {
            return connection;
        }

        public void setConnection(Integer connection) {
            this.connection = connection;
        }
    }

    public static class Task {
        @JsonSerialize(using = LongStringSerializer.class)
        @Schema(description = "taskId")
        private Long taskId;
        @Schema(description = "任务状态")
        private String status;
        @Schema(description = "模块")
        private String module;
        @Schema(description = "进程名称")
        private String process;
        @Schema(description = "修改时间")
        private Long updateTime;

        public Long getTaskId() {
            return taskId;
        }

        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getModule() {
            return module;
        }

        public void setModule(String module) {
            this.module = module;
        }

        public String getProcess() {
            return process;
        }

        public void setProcess(String process) {
            this.process = process;
        }

        public Long getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(Long updateTime) {
            this.updateTime = updateTime;
        }
    }

    public Long getIngestionId() {
        return ingestionId;
    }

    public void setIngestionId(Long ingestionId) {
        this.ingestionId = ingestionId;
    }

    public Long getCellId() {
        return cellId;
    }

    public void setCellId(Long cellId) {
        this.cellId = cellId;
    }

    public Agent getAgent() {
        return agent;
    }

    public void setAgent(Agent agent) {
        this.agent = agent;
    }

    public Task getTask() {
        return task;
    }

    public void setTask(Task task) {
        this.task = task;
    }
}
