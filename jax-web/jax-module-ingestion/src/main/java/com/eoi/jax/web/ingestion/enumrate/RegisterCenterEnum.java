package com.eoi.jax.web.ingestion.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public enum RegisterCenterEnum implements ICodeEnum {

    /**
     * nacos注册中心
     */
    NACOS("NACOS", "nacos注册中心"),

    /**
     * zookeeper注册中心
     */
    ZK("ZK", "zookeeper注册中心"),

    /**
     * eureka注册中心
     */
    EUREKA("EUREKA", "eureka注册中心");

    private final String code;

    private final String message;

    RegisterCenterEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static RegisterCenterEnum fromString(String code) {
        for (RegisterCenterEnum value : RegisterCenterEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        for (RegisterCenterEnum value : RegisterCenterEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }

}
