package com.eoi.jax.web.ingestion.model.blueking;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/4
 */
public class BluekingModelTree {

    private String bkId;

    private String name;

    private String type;

    private List<BluekingModelTree> children;


    public String getBkId() {
        return bkId;
    }

    public void setBkId(String bkId) {
        this.bkId = bkId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<BluekingModelTree> getChildren() {
        return children;
    }

    public void setChildren(List<BluekingModelTree> children) {
        this.children = children;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
