package com.eoi.jax.web.ingestion.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyChartReq;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyQueryReq;
import com.eoi.jax.web.ingestion.service.PipelineCapacityDailyService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/1/12
 */
@RestController
public class PipelineCapacityDailyController implements V2Controller {

    @Resource
    private PipelineCapacityDailyService pipelineCapacityDailyService;

    @Operation(summary = "查询容量日报")
    @PostMapping("/ingestion/pipeline-capacity-daily/query")
    public Response query(@RequestBody PipelineCapacityDailyQueryReq req) {
        return Response.success(pipelineCapacityDailyService.queryPage(req));
    }

    @Operation(summary = "管线作业容量日报指标")
    @PostMapping("/ingestion/pipeline-capacity-daily/pipeline-metrics")
    public Response pipelineMetrics(@RequestBody PipelineCapacityDailyChartReq req) {
        return Response.success(pipelineCapacityDailyService.queryJobLevelRangeMetrics(req));
    }

    @Operation(summary = "手动统计容量日报")
    @GetMapping("/ingestion/pipeline-capacity-daily/manual-statistical")
    public Response manualStatistical(@RequestParam(value = "statisticalDay") String statisticalDay) {
        return Response.success(pipelineCapacityDailyService.capacityDaily(statisticalDay));
    }

    @Operation(summary = "管线作业手动统计容量日报")
    @GetMapping("/ingestion/pipeline-capacity-daily/pipeline-manual-statistical")
    public Response manualStatistical(@RequestParam("pipelineId") Long pipelineId,
                                      @RequestParam(value = "statisticalDay", required = false) String statisticalDay) {
        pipelineCapacityDailyService.pipelineCapacityDaily(pipelineId, statisticalDay);
        return Response.success(true);
    }

}
