package com.eoi.jax.web.ingestion.controller;


import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.ingestion.model.auditlog.AuditLogQueryReq;
import com.eoi.jax.web.ingestion.service.AuditLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 审计记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@RestController
public class AuditLogController implements V2Controller {

    @Autowired
    private AuditLogService auditLogService;

    @Operation(summary = "数据源分页查询")
    @PostMapping("platform/audit-log/query")
    public Response query(@RequestBody AuditLogQueryReq req) {
        return Response.success(auditLogService.queryList(req));
    }


    @Operation(summary = "根据id查询详情")
    @GetMapping("platform/audit-log/{id}")
    public Response get(@Parameter(description = "数据源id", required = true) @PathVariable("id") Long id) {
        return Response.success(auditLogService.get(id));
    }
}

