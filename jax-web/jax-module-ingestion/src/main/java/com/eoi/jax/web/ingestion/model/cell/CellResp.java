package com.eoi.jax.web.ingestion.model.cell;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.ingestion.model.datacenter.DataCenterResp;
import com.eoi.jax.web.repository.entity.TbCell;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class CellResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbCell> {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "网关名称")
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "接入模式：NACOS_AUTO，NACOS_3RD，MANUAL")
    private String connectMode;

    @Schema(description = "连接数据源详情")
    private String connectDetail;

    @Schema(description = "最后一次连接数据源的时间")
    private Date lastConnectTime;

    @Schema(description = "网关状态：NORMAL, ABNORMAL等")
    private String status;

    @Schema(description = "采集网关地址，字符串格式，逗号分割")
    private String address;

    @Schema(description = "nacos分组")
    private String groupName;

    @Schema(description = "注册中心id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long registerCenterId;

    @Schema(description = "数据源id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long dsId;

    @Schema(description = "数据中心列表")
    private List<DataCenterResp> dataCenterList = new LinkedList<>();

    @Schema(description = "cell管理页面地址")
    private String cellManagementUrl;

    /**
     * 类型
     */
    @Schema(description = "类型, 采集网关: CELL, 接入网关: ROUTER")
    private String type;

    /**
     * 是否是SSL信道
     */
    @Schema(description = "是否是SSL信道")
    private Boolean isSsl;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    @Override
    public CellResp fromEntity(TbCell table) {
        IRespModel.super.fromEntity(table);
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getConnectMode() {
        return connectMode;
    }

    public void setConnectMode(String connectMode) {
        this.connectMode = connectMode;
    }

    public String getStatus() {
        return status;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getRegisterCenterId() {
        return registerCenterId;
    }

    public void setRegisterCenterId(Long registerCenterId) {
        this.registerCenterId = registerCenterId;
    }

    public Long getDsId() {
        return dsId;
    }

    public String getCellManagementUrl() {
        return cellManagementUrl;
    }

    public void setCellManagementUrl(String cellManagementUrl) {
        this.cellManagementUrl = cellManagementUrl;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

    public List<DataCenterResp> getDataCenterList() {
        return dataCenterList;
    }

    public void setDataCenterList(List<DataCenterResp> dataCenterList) {
        this.dataCenterList = dataCenterList;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getIsSsl() {
        return isSsl;
    }

    public void setIsSsl(Boolean ssl) {
        isSsl = ssl;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getConnectDetail() {
        return connectDetail;
    }

    public void setConnectDetail(String connectDetail) {
        this.connectDetail = connectDetail;
    }

    public Date getLastConnectTime() {
        return lastConnectTime;
    }

    public void setLastConnectTime(Date lastConnectTime) {
        this.lastConnectTime = lastConnectTime;
    }

    @Override
    public String toString() {
        return "CellResp{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", connectMode='" + connectMode + '\'' +
                ", connectDetail='" + connectDetail + '\'' +
                ", lastConnectTime=" + lastConnectTime +
                ", status='" + status + '\'' +
                ", address='" + address + '\'' +
                ", groupName='" + groupName + '\'' +
                ", registerCenterId=" + registerCenterId +
                ", dsId=" + dsId +
                ", dataCenterList=" + dataCenterList +
                ", cellManagementUrl='" + cellManagementUrl + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                '}';
    }
}
