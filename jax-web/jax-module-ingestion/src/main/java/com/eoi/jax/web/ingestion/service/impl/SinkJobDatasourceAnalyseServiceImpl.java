package com.eoi.jax.web.ingestion.service.impl;

import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.ingestion.enumrate.DatasourcePlatformEnum;
import com.eoi.jax.web.ingestion.service.IOperatorDatasourceAnalyse;
import com.eoi.jax.web.ingestion.service.SinkJobDatasourceAnalyseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/12/14
 * @Desc:
 **/

@Service
public class SinkJobDatasourceAnalyseServiceImpl implements SinkJobDatasourceAnalyseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SinkJobDatasourceAnalyseService.class);
    /**
     * 算子名称和数据源分析服务类的映射
     * key:数据源类型_算子名称
     * value:数据源分析服务类
     */
    private Map<String, IOperatorDatasourceAnalyse> operatorDatasourceAnalyseMap = new HashMap<>(1);
    /**
     * 默认算子名称和数据源分析服务类的映射
     * 找不到就会调用默认处理流程
     * key:数据源类型
     * value:数据源分析服务类
     */
    private Map<String, IOperatorDatasourceAnalyse> defaultJobServiceNameMap = new HashMap<>(1);


    /**
     * 加载系统算子数据源分析服务类,
     * 如果是默认算子,则放入defaultJobServiceNameMap,否则放入operatorDatasourceAnalyseMap
     * 获取服务先根据jobName从operatorDatasourceAnalyseMap获取,
     * 如果没有则根据数据源类型从defaultJobServiceNameMap获取
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initAnalyseService() {
        try {
            Map<String, IOperatorDatasourceAnalyse> datasourceAnalyseMap = ContextHolder.getBeans(IOperatorDatasourceAnalyse.class);
            datasourceAnalyseMap.forEach((k, v) -> {
                if (v.defaultJob()) {
                    if (defaultJobServiceNameMap.containsKey(v.datasourcePlatform().code())) {
                        throw new BizException(ResponseCode.FAILED.getCode(),
                            "defaultJobServiceNameMap has duplicate key:" + v.datasourcePlatform().code());
                    }
                    defaultJobServiceNameMap.put(v.datasourcePlatform().code(), v);
                } else {
                    operatorDatasourceAnalyseMap.put(v.datasourcePlatform().code() + "_" + v.jobName(), v);
                }
            });
            LOGGER.info("initOperatorDatasourceAnalyse success {}, default: {}",
                operatorDatasourceAnalyseMap.keySet(),
                defaultJobServiceNameMap.keySet());
        } catch (Exception e) {
            LOGGER.error("initOperatorDatasourceAnalyse error", e);
        }
    }

    /**
     * 根据数据源类型和算子名称获取数据源分析服务
     *
     * @param platformEnum
     * @param jobName
     * @return
     */
    @Override
    public IOperatorDatasourceAnalyse getAnalyseService(DatasourcePlatformEnum platformEnum, String jobName) {
        if (jobName == null) {
            return null;
        }
        if (operatorDatasourceAnalyseMap.size() == 0 && defaultJobServiceNameMap.size() == 0) {
            initAnalyseService();
        }
        IOperatorDatasourceAnalyse datasourceAnalyse = operatorDatasourceAnalyseMap.get(platformEnum.code() + "_" + jobName);
        if (datasourceAnalyse != null) {
            return datasourceAnalyse;
        }
        return defaultJobServiceNameMap.get(platformEnum.code());
    }
}
