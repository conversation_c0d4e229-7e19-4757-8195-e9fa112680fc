package com.eoi.jax.web.ingestion.model.pipeline;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbPipeline;

/**
 * <AUTHOR>
 * @date 2023/2/14
 */
public class PipelineQueryReq extends BaseQueryReq<TbPipeline> {
    private PipelineFilterReq filter = new PipelineFilterReq();
    private PipelineSortReq sort = new PipelineSortReq();
    private boolean fetchConsumerLag = false;

    @Override
    public PipelineFilterReq getFilter() {
        return filter;
    }

    public void setFilter(PipelineFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public PipelineSortReq getSort() {
        return sort;
    }

    public void setSort(PipelineSortReq sort) {
        this.sort = sort;
    }

    public boolean getFetchConsumerLag() {
        return fetchConsumerLag;
    }

    public void setFetchConsumerLag(boolean fetchConsumerLag) {
        this.fetchConsumerLag = fetchConsumerLag;
    }
}
