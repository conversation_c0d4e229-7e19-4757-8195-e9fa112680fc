package com.eoi.jax.web.ingestion.model.monitor;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/3/16
 */
@Deprecated
public class TmIdListReq {

    @NotNull(message = "管线作业Id不能为空")
    @Schema(description = "管线作业Id")
    private Long id;

    @Schema(description = "开始时间（默认时区+08:00）", example = "2021-08-25T11:00:00.723+08:00")
    private String start;

    @Schema(description = "结束时间（默认时区+08:00）", example = "2021-08-25T11:10:00.723+08:00")
    private String end;

    @Schema(description = "时间间隔,例如15s,1m,1h,1d", example = "1m")
    private String step;

    @Schema(description = "是否离线")
    private Boolean isOffline;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public Boolean getOffline() {
        return isOffline;
    }

    public void setOffline(Boolean offline) {
        isOffline = offline;
    }
}
