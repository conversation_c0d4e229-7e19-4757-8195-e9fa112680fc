package com.eoi.jax.web.ingestion.model.cluster;

import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbCluster;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/21
 */
public class ClusterCreateReq implements ICreateModel<TbCluster> {
    @OpPrimaryName
    private String clusterName;
    private String clusterType;
    private String clusterDescription;
    private Boolean defaultFlinkCluster;
    private Boolean defaultSparkCluster;
    private Boolean defaultMarayarnCluster;
    private Long flinkOptsId;
    private Long sparkOptsId;
    private Long marayarnOptsId;
    private Map<String, Object> setting;
    @Schema(description = "数据中心id集合")
    private Set<Long> dataCenterIds;

    @Override
    public TbCluster toEntity() {
        return toEntity(new TbCluster());
    }

    @Override
    public TbCluster toEntity(TbCluster tbCluster) {
        TbCluster entity = ICreateModel.super.toEntity(tbCluster);
        IClusterSetting clusterSetting = ClusterSettingFactory.instance(clusterType, setting);
        entity.setSetting(JsonUtil.encode(clusterSetting));
        return entity;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getClusterType() {
        return clusterType;
    }

    public void setClusterType(String clusterType) {
        this.clusterType = clusterType;
    }

    public String getClusterDescription() {
        return clusterDescription;
    }

    public void setClusterDescription(String clusterDescription) {
        this.clusterDescription = clusterDescription;
    }

    public Boolean getDefaultFlinkCluster() {
        return defaultFlinkCluster;
    }

    public void setDefaultFlinkCluster(Boolean defaultFlinkCluster) {
        this.defaultFlinkCluster = defaultFlinkCluster;
    }

    public Boolean getDefaultSparkCluster() {
        return defaultSparkCluster;
    }

    public void setDefaultSparkCluster(Boolean defaultSparkCluster) {
        this.defaultSparkCluster = defaultSparkCluster;
    }

    public Boolean getDefaultMarayarnCluster() {
        return defaultMarayarnCluster;
    }

    public void setDefaultMarayarnCluster(Boolean defaultMarayarnCluster) {
        this.defaultMarayarnCluster = defaultMarayarnCluster;
    }

    public Long getFlinkOptsId() {
        return flinkOptsId;
    }

    public void setFlinkOptsId(Long flinkOptsId) {
        this.flinkOptsId = flinkOptsId;
    }

    public Long getSparkOptsId() {
        return sparkOptsId;
    }

    public void setSparkOptsId(Long sparkOptsId) {
        this.sparkOptsId = sparkOptsId;
    }

    public Long getMarayarnOptsId() {
        return marayarnOptsId;
    }

    public void setMarayarnOptsId(Long marayarnOptsId) {
        this.marayarnOptsId = marayarnOptsId;
    }

    public Map<String, Object> getSetting() {
        return setting;
    }

    public void setSetting(Map<String, Object> setting) {
        this.setting = setting;
    }

    public Set<Long> getDataCenterIds() {
        return dataCenterIds;
    }

    public void setDataCenterIds(Set<Long> dataCenterIds) {
        this.dataCenterIds = dataCenterIds;
    }
}
