package com.eoi.jax.web.ingestion.model.application.cksinker;

import cn.hutool.json.JSONUtil;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.ingestion.model.opts.MarayarnOpts;
import com.eoi.jax.web.repository.entity.TbApplication;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

public class AppCkSinkerUpdateRequest implements IUpdateModel<TbApplication> {
    private Long id;

    @Schema(description = "执行器实例数")
    @NotNull(message = "执行器实例数不能为空")
    @Min(value = 1, message = "执行器实例数必须大于0")
    private Integer instanceCount;

    @Schema(description = "集群id")
    @NotNull(message = "集群id不能为空")
    private Long clusterId;

    @Schema(description = "marayarn框架id")
    @NotNull(message = "marayarn框架id不能为空")
    private Long optsId;

    @Schema(description = "普通配置")
    private CommonConfig commonConfig;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "其他自定义的一些配置,比如自动生成command的必要参数")
    @NotNull(message = "自定义配置不能为空")
    private Map<String, Object> customConfig;


    @Override
    public TbApplication toEntity(TbApplication tbApplication) {
        TbApplication entity = IUpdateModel.super.toEntity(tbApplication);
        if (this.commonConfig != null) {
            entity.setSetting(JSONUtil.toJsonStr(this.commonConfig));
        }
        return entity;
    }

    public static class CommonConfig extends MarayarnOpts {

    }

    @Override
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getInstanceCount() {
        return instanceCount;
    }

    public void setInstanceCount(Integer instanceCount) {
        this.instanceCount = instanceCount;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public Long getOptsId() {
        return optsId;
    }

    public void setOptsId(Long optsId) {
        this.optsId = optsId;
    }

    public CommonConfig getCommonConfig() {
        return commonConfig;
    }

    public void setCommonConfig(CommonConfig commonConfig) {
        this.commonConfig = commonConfig;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, Object> getCustomConfig() {
        return customConfig;
    }

    public void setCustomConfig(Map<String, Object> customConfig) {
        this.customConfig = customConfig;
    }
}
