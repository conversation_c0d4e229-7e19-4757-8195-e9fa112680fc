package com.eoi.jax.web.ingestion.model.application;

import com.eoi.jax.web.core.model.IModel;
import com.eoi.jax.web.repository.entity.TbOperatorLog;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
public class ApplicationLogResp implements IModel<TbOperatorLog> {
    private Long opId;
    private String opType;
    private Date opTime;
    private String content;

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public Date getOpTime() {
        return opTime;
    }

    public void setOpTime(Date opTime) {
        this.opTime = opTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
