package com.eoi.jax.web.ingestion.model.opts;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.base.param.Parameter;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.config.AppConfig;
import com.eoi.jax.web.core.config.JaxEnvVar;
import com.eoi.jax.web.ingestion.consts.OptsScope;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22
 */
@SuppressWarnings("PMD")
public class FlinkOpts implements IOptsSetting {
    @Parameter(
            label = "Flink目录",
            description = "flink主目录，一般为${JAX_HOME}/flink"
    )
    private String home;
    @Parameter(
            label = "引导jar包",
            description = "引导jar包，一般为${JAX_HOME}/jax/jar_lib/jax-flink-entry.jar"
    )
    private String entryJar;
    private String entryClass;
    @Parameter(
            label = "算子依赖库",
            description = "算子lib目录，一般为${JAX_HOME}/jax/jar_lib/flink"
    )
    private String jobLib;
    @Parameter(
            label = "Yarn Queue",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "提交到Yarn的Queue名字"
    )
    private String yarnQueue;
    @Parameter(
            label = "并行度",
            scope = OptsScope.PIPELINE,
            description = "并行度"
    )
    private Long parallelism;
    @Parameter(
            label = "时间类型",
            scope = OptsScope.PIPELINE,
            description = "flink时间类型",
            recommendations = {"processing", "event"}
    )
    private String timeCharacteristic;
    @Parameter(
            label = "状态存储类型",
            scope = OptsScope.PIPELINE,
            description = "状态存储类型",
            recommendations = {"rocksdb", "fs", "memory"}
    )
    private String backend;
    @Parameter(
            label = "rocksDb存储路径",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "rocksDb存储路径"
    )
    private String rocksDbPath;
    @Parameter(
            label = "savepoint地址",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "savepoint地址"
    )
    private String savepointURI;
    @Parameter(
            label = "checkpoint地址",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "checkpoint地址"
    )
    private String checkpointURI;
    @Parameter(
            label = "checkpoint周期",
            scope = OptsScope.PIPELINE,
            description = "checkpoint间隔时间(毫秒)"
    )
    private Long checkpointInterval;
    @Parameter(
            label = "状态最小时间",
            scope = OptsScope.PIPELINE,
            description = "状态最小保持时间(小时)"
    )
    private Long minIdleStateRetentionTime;
    @Parameter(
            label = "状态最大时间",
            scope = OptsScope.PIPELINE,
            description = "状态最大保持时间(小时)"
    )
    private Long maxIdleStateRetentionTime;
    @Parameter(
            label = "Yarn Slot",
            scope = OptsScope.PIPELINE,
            description = "yarn内每个TaskManager的slot数量"
    )
    private Long yarnSlots;
    @Parameter(
            label = "JobManager内存",
            scope = OptsScope.PIPELINE,
            description = "JobManager的容器的内存大小(默认MB);\n" +
                    "flink1.10开始建议最少配置1024MB，否则可能启动失败"
    )
    private String yarnJobManagerMemory;
    @Parameter(
            label = "TaskManager内存",
            scope = OptsScope.PIPELINE,
            description = "TaskManager的容器的内存大小(默认MB);\n" +
                    "flink1.10开始建议最少配置2048MB，否则可能启动失败"
    )
    private String yarnTaskManagerMemory;
    @Parameter(
            label = "禁用算子链优化",
            scope = OptsScope.PIPELINE,
            description = "关闭对operator进行chain优化，默认不关闭",
            trueLabel = "禁用",
            falseLabel = "不禁用"
    )
    private Boolean disableOperatorChaining;
    @Parameter(
            label = "使用OldPlanner",
            scope = OptsScope.PIPELINE,
            description = "是否使用flink old planner（非blink）"
    )
    private Boolean useOldPlanner;
    @Parameter(
            label = "跳过失效状态",
            scope = OptsScope.PIPELINE,
            description = "允许跳过那些不能使用的savepoint"
    )
    private Boolean allowNonRestoredState;
    @Parameter(
            label = "禁用savepoint",
            scope = OptsScope.PIPELINE,
            description = "放弃savepoint启动",
            trueLabel = "禁用",
            falseLabel = "不禁用"
    )
    private Boolean discardSavepoint;
    @Parameter(
            label = "对象重用模式",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "Flink对象重用模式,ENABLE_MODE-开启对象重用、DISABLE_MODE-关闭对象重用、AUTO_OPTIMIZE_MODE-自动优化",
            recommendations = {"ENABLE_MODE", "DISABLE_MODE", "AUTO_OPTIMIZE_MODE"}
    )
    private String objectReuseMode;
    @Parameter(
            label = "使用application模式",
            scope = OptsScope.PIPELINE,
            description = "是否为application模式启动"
    )
    private Boolean applicationMode;
    @Parameter(
            label = "运行时模式",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "Flink1.12及以上运行时模式，可选【STREAMING，BATCH，AUTOMATIC】",
            recommendations = {"STREAMING", "BATCH", "AUTOMATIC"}
    )
    private String runtimeMode;
    @Parameter(
            label = "环境模式",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "Flink1.12以上环境模式，可选【STREAMING，BATCH】",
            recommendations = {"STREAMING", "BATCH"}
    )
    private String envMode;
    @Parameter(
            label = "启用高可用",
            optional = true,
            description = "是否启用高可用HA模式，默认不启用"
    )
    private Boolean haEnabled;
    @Parameter(
            label = "高可用模式",
            optional = true,
            description = "高可用模式：zookeeper模式等",
            recommendations = {"zookeeper"},
            availableCondition = "haEnabled == true",
            requireCondition = "haEnabled == true"
    )
    private String haAvailability;
    @Parameter(
            label = "高可用存储地址",
            optional = true,
            description = "高可用存储地址，hdfs需要配合hadoop配置，如：hdfs://jax/flinkha/recovery",
            availableCondition = "haEnabled == true",
            requireCondition = "haEnabled == true"
    )
    private String haStorageDir;
    @Parameter(
            label = "高可用ZK地址",
            optional = true,
            description = "高可用的zookeeper地址，如：host1:2181,host2:2181",
            availableCondition = "haEnabled == true",
            requireCondition = "haAvailability == 'zookeeper'"
    )
    private String haZkQuorum;
    @Parameter(
            label = "高可用ZK目录",
            optional = true,
            description = "高可用的zookeeper的根目录，如：/jax/flinkha",
            availableCondition = "haEnabled == true",
            requireCondition = "haAvailability == 'zookeeper'"
    )
    private String haZkPathRoot;
    @Parameter(
            label = "ZK禁用Sasl",
            optional = true,
            description = "禁用zookeeper sasl",
            availableCondition = "haEnabled == true",
            trueLabel = "禁用",
            falseLabel = "不禁用"
    )
    private Boolean zkSaslDisable;
    @Parameter(
            label = "Yarn重试次数",
            optional = true,
            description = "Yarn重试次数，上限为Yarn的最大重试次数(yarn.resourcemanager.am.max-attempts)",
            availableCondition = "haEnabled == true"
    )
    private Long yarnApplicationAttempts;

    @Parameter(
            label = "高级配置",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "flink高级配置"
    )
    private List<String> confList;
    @Parameter(
            label = "Java Options",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "Java Options"
    )
    private List<String> javaOptions;
    @Parameter(
            label = "额外启动参数",
            scope = OptsScope.PIPELINE,
            optional = true,
            description = "额外启动参数"
    )
    private List<String> otherStartArgs;
    @Parameter(
            label = "Prometheus地址",
            optional = true,
            description = "prometheus查询入口Url，例如：http://*************:9090"
    )
    private String prometheusUrl;
    @Parameter(
            label = "PushGateway地址",
            optional = true,
            description = "pushGateway地址，多个用逗号分隔，例如： *************:9091,*************:9091"
    )
    private String prometheusPushGateway;
    @Parameter(
            label = "K8s镜像",
            optional = true,
            description = "flink任务提交到k8s时，使用container镜像地址，例如：**************:5000/jax-flink:lastest"
    )
    private String kubeContainerImage;
    @Parameter(
            label = "K8s镜像拉取策略",
            optional = true,
            description = "拉取flink镜像策略：IfNotPresent, Always, Never",
            recommendations = {"IfNotPresent", "Always", "Never"}
    )
    private String kubeImagePullPolicy;
    @Parameter(
            label = "K8s名空间",
            optional = true,
            description = "任务提交到k8s的名空间"
    )
    private String kubeNameSpace;

    @Override
    public void fillValue() {
        if (StrUtil.isEmpty(home)) {
            home = "${JAX_HOME}/flink";
        }
        if (StrUtil.isEmpty(entryJar)) {
            entryJar = "${JAX_HOME}/jax/jar_lib/jax-flink-entry.jar";
        }
        if (StrUtil.isEmpty(entryClass)) {
            entryClass = "com.eoi.jax.flink_entry.FlinkMainEntry";
        }
        if (StrUtil.isEmpty(jobLib)) {
            jobLib = "${JAX_HOME}/jax/jar_lib/flink";
        }
        if (parallelism == null) {
            parallelism = 1L;
        }
        if (StrUtil.isEmpty(timeCharacteristic)) {
            timeCharacteristic = "processing";
        }
        if (StrUtil.isEmpty(backend)) {
            backend = "rocksdb";
        }
        if (StrUtil.isEmpty(rocksDbPath)) {
            rocksDbPath = "file://${JAX_HOME}/flink/rocksdb";
        }
        if (StrUtil.isEmpty(savepointURI)) {
            savepointURI = "file://${JAX_HOME}/flink/savepoint";
        }
        if (StrUtil.isEmpty(checkpointURI)) {
            checkpointURI = "file://${JAX_HOME}/flink/checkpoint";
        }
        if (checkpointInterval == null) {
            checkpointInterval = 300000L;
        }
        if (minIdleStateRetentionTime == null) {
            minIdleStateRetentionTime = 12L;
        }
        if (maxIdleStateRetentionTime == null) {
            maxIdleStateRetentionTime = 24L;
        }
        if (yarnSlots == null) {
            yarnSlots = 1L;
        }
        if (StrUtil.isEmpty(yarnJobManagerMemory)) {
            yarnJobManagerMemory = "1024";
        }
        if (StrUtil.isEmpty(yarnTaskManagerMemory)) {
            yarnTaskManagerMemory = "2048";
        }
        if (disableOperatorChaining == null) {
            disableOperatorChaining = false;
        }
        if (useOldPlanner == null) {
            useOldPlanner = false;
        }
        if (allowNonRestoredState == null) {
            allowNonRestoredState = false;
        }
        if (discardSavepoint == null) {
            discardSavepoint = false;
        }
        if (applicationMode == null) {
            applicationMode = false;
        }
        if (StrUtil.isEmpty(runtimeMode)) {
            runtimeMode = "STREAMING";
        }
        if (StrUtil.isEmpty(envMode)) {
            envMode = "STREAMING";
        }
        if (confList == null) {
            confList = new ArrayList<>();
        }
        if (javaOptions == null) {
            javaOptions = new ArrayList<>();
        }
        if (otherStartArgs == null) {
            otherStartArgs = new ArrayList<>();
        }
    }

    public String computeFlinkHomePath() {
        return JaxEnvVar.effectEnvVar(home);
    }

    public String computeFlinkBinPath() {
        return Common.pathsJoin(computeFlinkHomePath(), AppConfig.FLINK_BIN_RELATIVE);
    }

    public String computeFlinkConfPath() {
        return Common.pathsJoin(computeFlinkHomePath(), AppConfig.FLINK_CONF_RELATIVE);
    }


    public String computeFlinkYarnSessionBinPath() {
        return Common.pathsJoin(computeFlinkHomePath(), AppConfig.FLINK_YARN_SESSION_BIN_RELATIVE);
    }

    public String computeFlinkLibPath() {
        return Common.pathsJoin(computeFlinkHomePath(), AppConfig.FLINK_LIB_RELATIVE);
    }

    public String computeEntryJarPath() {
        return JaxEnvVar.effectEnvVar(entryJar);
    }

    public String computeJobLibPath() {
        return JaxEnvVar.effectEnvVar(jobLib);
    }

    @Override
    public String getHome() {
        return home;
    }

    public void setHome(String home) {
        this.home = home;
    }

    public String getEntryJar() {
        return entryJar;
    }

    public void setEntryJar(String entryJar) {
        this.entryJar = entryJar;
    }

    public String getEntryClass() {
        return entryClass;
    }

    public void setEntryClass(String entryClass) {
        this.entryClass = entryClass;
    }

    public String getJobLib() {
        return jobLib;
    }

    public void setJobLib(String jobLib) {
        this.jobLib = jobLib;
    }

    public String getYarnQueue() {
        return yarnQueue;
    }

    public void setYarnQueue(String yarnQueue) {
        this.yarnQueue = yarnQueue;
    }

    public Long getParallelism() {
        return parallelism;
    }

    public void setParallelism(Long parallelism) {
        this.parallelism = parallelism;
    }

    public String getTimeCharacteristic() {
        return timeCharacteristic;
    }

    public void setTimeCharacteristic(String timeCharacteristic) {
        this.timeCharacteristic = timeCharacteristic;
    }

    public String getBackend() {
        return backend;
    }

    public void setBackend(String backend) {
        this.backend = backend;
    }

    public String getRocksDbPath() {
        return rocksDbPath;
    }

    public void setRocksDbPath(String rocksDbPath) {
        this.rocksDbPath = rocksDbPath;
    }

    public String getSavepointURI() {
        return savepointURI;
    }

    public void setSavepointURI(String savepointURI) {
        this.savepointURI = savepointURI;
    }

    public String getCheckpointURI() {
        return checkpointURI;
    }

    public void setCheckpointURI(String checkpointURI) {
        this.checkpointURI = checkpointURI;
    }

    public Long getCheckpointInterval() {
        return checkpointInterval;
    }

    public void setCheckpointInterval(Long checkpointInterval) {
        this.checkpointInterval = checkpointInterval;
    }

    public Long getMinIdleStateRetentionTime() {
        return minIdleStateRetentionTime;
    }

    public void setMinIdleStateRetentionTime(Long minIdleStateRetentionTime) {
        this.minIdleStateRetentionTime = minIdleStateRetentionTime;
    }

    public Long getMaxIdleStateRetentionTime() {
        return maxIdleStateRetentionTime;
    }

    public void setMaxIdleStateRetentionTime(Long maxIdleStateRetentionTime) {
        this.maxIdleStateRetentionTime = maxIdleStateRetentionTime;
    }

    public Long getYarnSlots() {
        return yarnSlots;
    }

    public void setYarnSlots(Long yarnSlots) {
        this.yarnSlots = yarnSlots;
    }

    public String getYarnJobManagerMemory() {
        return yarnJobManagerMemory;
    }

    public void setYarnJobManagerMemory(String yarnJobManagerMemory) {
        this.yarnJobManagerMemory = yarnJobManagerMemory;
    }

    public String getYarnTaskManagerMemory() {
        return yarnTaskManagerMemory;
    }

    public void setYarnTaskManagerMemory(String yarnTaskManagerMemory) {
        this.yarnTaskManagerMemory = yarnTaskManagerMemory;
    }

    public Boolean getDisableOperatorChaining() {
        return disableOperatorChaining;
    }

    public void setDisableOperatorChaining(Boolean disableOperatorChaining) {
        this.disableOperatorChaining = disableOperatorChaining;
    }

    public Boolean getUseOldPlanner() {
        return useOldPlanner;
    }

    public void setUseOldPlanner(Boolean useOldPlanner) {
        this.useOldPlanner = useOldPlanner;
    }

    public Boolean getAllowNonRestoredState() {
        return allowNonRestoredState;
    }

    public void setAllowNonRestoredState(Boolean allowNonRestoredState) {
        this.allowNonRestoredState = allowNonRestoredState;
    }

    public Boolean getDiscardSavepoint() {
        return discardSavepoint;
    }

    public void setDiscardSavepoint(Boolean discardSavepoint) {
        this.discardSavepoint = discardSavepoint;
    }

    public String getObjectReuseMode() {
        return objectReuseMode;
    }

    public void setObjectReuseMode(String objectReuseMode) {
        this.objectReuseMode = objectReuseMode;
    }

    public Boolean getApplicationMode() {
        return applicationMode;
    }

    public void setApplicationMode(Boolean applicationMode) {
        this.applicationMode = applicationMode;
    }

    public String getRuntimeMode() {
        return runtimeMode;
    }

    public void setRuntimeMode(String runtimeMode) {
        this.runtimeMode = runtimeMode;
    }

    public String getEnvMode() {
        return envMode;
    }

    public void setEnvMode(String envMode) {
        this.envMode = envMode;
    }

    public Boolean getHaEnabled() {
        return haEnabled;
    }

    public void setHaEnabled(Boolean haEnabled) {
        this.haEnabled = haEnabled;
    }

    public String getHaAvailability() {
        return haAvailability;
    }

    public void setHaAvailability(String haAvailability) {
        this.haAvailability = haAvailability;
    }

    public String getHaStorageDir() {
        return haStorageDir;
    }

    public void setHaStorageDir(String haStorageDir) {
        this.haStorageDir = haStorageDir;
    }

    public String getHaZkQuorum() {
        return haZkQuorum;
    }

    public void setHaZkQuorum(String haZkQuorum) {
        this.haZkQuorum = haZkQuorum;
    }

    public String getHaZkPathRoot() {
        return haZkPathRoot;
    }

    public void setHaZkPathRoot(String haZkPathRoot) {
        this.haZkPathRoot = haZkPathRoot;
    }

    public Boolean getZkSaslDisable() {
        return zkSaslDisable;
    }

    public void setZkSaslDisable(Boolean zkSaslDisable) {
        this.zkSaslDisable = zkSaslDisable;
    }

    public Long getYarnApplicationAttempts() {
        return yarnApplicationAttempts;
    }

    public void setYarnApplicationAttempts(Long yarnApplicationAttempts) {
        this.yarnApplicationAttempts = yarnApplicationAttempts;
    }

    public List<String> getConfList() {
        return confList;
    }

    public void setConfList(List<String> confList) {
        this.confList = confList;
    }

    public List<String> getJavaOptions() {
        return javaOptions;
    }

    public void setJavaOptions(List<String> javaOptions) {
        this.javaOptions = javaOptions;
    }

    public List<String> getOtherStartArgs() {
        return otherStartArgs;
    }

    public void setOtherStartArgs(List<String> otherStartArgs) {
        this.otherStartArgs = otherStartArgs;
    }

    public String getPrometheusUrl() {
        return prometheusUrl;
    }

    public void setPrometheusUrl(String prometheusUrl) {
        this.prometheusUrl = prometheusUrl;
    }

    public String getPrometheusPushGateway() {
        return prometheusPushGateway;
    }

    public void setPrometheusPushGateway(String prometheusPushGateway) {
        this.prometheusPushGateway = prometheusPushGateway;
    }

    public String getKubeContainerImage() {
        return kubeContainerImage;
    }

    public void setKubeContainerImage(String kubeContainerImage) {
        this.kubeContainerImage = kubeContainerImage;
    }

    public String getKubeImagePullPolicy() {
        return kubeImagePullPolicy;
    }

    public void setKubeImagePullPolicy(String kubeImagePullPolicy) {
        this.kubeImagePullPolicy = kubeImagePullPolicy;
    }

    public String getKubeNameSpace() {
        return kubeNameSpace;
    }

    public void setKubeNameSpace(String kubeNameSpace) {
        this.kubeNameSpace = kubeNameSpace;
    }
}
