package com.eoi.jax.web.ingestion.model.businessflowtree;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbBusinessFlowTree;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class BusinessFlowTreeQueryReq extends BaseQueryReq<TbBusinessFlowTree> {

    @Schema(description = "查询条件")
    private BusinessFlowTreeQueryFilterReq filter = new BusinessFlowTreeQueryFilterReq();

    @Schema(description = "排序条件")
    private BusinessFlowTreeQuerySortReq sort = new BusinessFlowTreeQuerySortReq();

    @Override
    public IFilterReq<TbBusinessFlowTree> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbBusinessFlowTree> getSort() {
        return sort;
    }

    public void setFilter(BusinessFlowTreeQueryFilterReq filter) {
        this.filter = filter;
    }

    public void setSort(BusinessFlowTreeQuerySortReq sort) {
        this.sort = sort;
    }
}
