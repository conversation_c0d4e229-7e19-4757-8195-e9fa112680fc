package com.eoi.jax.web.ingestion.model.jar;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.ingestion.model.flinksqlfunc.FlinkSqlFuncResp;
import com.eoi.jax.web.repository.entity.TbJar;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
public class JarResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbJar> {
    @OpPrimaryKey
    private Long id;
    @OpPrimaryName
    private String jarName;
    private String jarUuid;
    private String jarType;
    private String jarPath;
    private String jarVersion;
    private String jarFile;
    private String jarDescription;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long clusterId;
    private String supportVersion;
    private Date createTime;
    private Date updateTime;
    private Long createUser;
    private Long updateUser;
    private Long historyCount;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long jobCount;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String clusterName;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean fileNotExists;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<FlinkSqlFuncResp> sqls;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getJarName() {
        return jarName;
    }

    public void setJarName(String jarName) {
        this.jarName = jarName;
    }

    public String getJarUuid() {
        return jarUuid;
    }

    public void setJarUuid(String jarUuid) {
        this.jarUuid = jarUuid;
    }

    public String getJarType() {
        return jarType;
    }

    public void setJarType(String jarType) {
        this.jarType = jarType;
    }

    public String getJarPath() {
        return jarPath;
    }

    public void setJarPath(String jarPath) {
        this.jarPath = jarPath;
    }

    public String getJarVersion() {
        return jarVersion;
    }

    public void setJarVersion(String jarVersion) {
        this.jarVersion = jarVersion;
    }

    public String getJarFile() {
        return jarFile;
    }

    public void setJarFile(String jarFile) {
        this.jarFile = jarFile;
    }

    public String getJarDescription() {
        return jarDescription;
    }

    public void setJarDescription(String jarDescription) {
        this.jarDescription = jarDescription;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public String getSupportVersion() {
        return supportVersion;
    }

    public void setSupportVersion(String supportVersion) {
        this.supportVersion = supportVersion;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Long getHistoryCount() {
        return historyCount;
    }

    public void setHistoryCount(Long historyCount) {
        this.historyCount = historyCount;
    }

    public Long getJobCount() {
        return jobCount;
    }

    public JarResp setJobCount(Long jobCount) {
        this.jobCount = jobCount;
        return this;
    }

    public String getClusterName() {
        return clusterName;
    }

    public JarResp setClusterName(String clusterName) {
        this.clusterName = clusterName;
        return this;
    }

    public Boolean getFileNotExists() {
        return fileNotExists;
    }

    public JarResp setFileNotExists(Boolean fileNotExists) {
        this.fileNotExists = fileNotExists;
        return this;
    }

    public List<FlinkSqlFuncResp> getSqls() {
        return sqls;
    }

    public JarResp setSqls(List<FlinkSqlFuncResp> sqls) {
        this.sqls = sqls;
        return this;
    }
}
