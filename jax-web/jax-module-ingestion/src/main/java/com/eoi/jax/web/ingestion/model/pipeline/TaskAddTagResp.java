package com.eoi.jax.web.ingestion.model.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/5/24
 * @Desc:
 **/
public class TaskAddTagResp {

    @Schema(description = "处理结果")
    private String message;

    private List<TagMatchedCheckTask> matchedCheckTaskList;

    public TaskAddTagResp(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<TagMatchedCheckTask> getMatchedCheckTaskList() {
        return matchedCheckTaskList;
    }

    public void setMatchedCheckTaskList(List<TagMatchedCheckTask> matchedCheckTaskList) {
        this.matchedCheckTaskList = matchedCheckTaskList;
    }

    public static class TagMatchedCheckTask {

        private Long tagId;

        private String tagName;

        private String matchedStatus;

        private List<PipelineCheckTaskResp.CheckTaskResp> checkTaskRespList;

        public Long getTagId() {
            return tagId;
        }

        public void setTagId(Long tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public String getMatchedStatus() {
            return matchedStatus;
        }

        public void setMatchedStatus(String matchedStatus) {
            this.matchedStatus = matchedStatus;
        }

        public List<PipelineCheckTaskResp.CheckTaskResp> getCheckTaskRespList() {
            return checkTaskRespList;
        }

        public void setCheckTaskRespList(List<PipelineCheckTaskResp.CheckTaskResp> checkTaskRespList) {
            this.checkTaskRespList = checkTaskRespList;
        }
    }

}
