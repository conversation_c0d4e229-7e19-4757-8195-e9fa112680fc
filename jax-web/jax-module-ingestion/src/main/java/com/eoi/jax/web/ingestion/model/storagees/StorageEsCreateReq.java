package com.eoi.jax.web.ingestion.model.storagees;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.enumrate.TableDeployStatusEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.core.util.ValidatorUtils;
import com.eoi.jax.web.ingestion.model.pipelineconf.SinkElasticSearchJobConfig;
import com.eoi.jax.web.ingestion.model.pipelineconf.SourceKafkaJobConfig;
import com.eoi.jax.web.ingestion.plugin.pipeline.EsModelSetting;
import com.eoi.jax.web.repository.entity.TbStorageEs;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.search.query.TableDeployCheckExistParam;
import com.eoi.jax.web.repository.service.TbTableDeployService;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.stream.Collectors;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class StorageEsCreateReq implements ICreateModel<TbStorageEs> {

    /**
     * 业务流程id
     */
    @NotNull(message = "业务流程id不能为空")
    @Schema(description = "业务流程id")
    private Long businessFlowId;

    /**
     * 存储集群名称
     */
    @NotBlank(message = "存储任务名称不能为空")
    @Schema(description = "存储任务名称")
    private String name;

    /**
     * kafka模型id
     */
    @NotNull(message = "kafka模型id不能为空")
    @Schema(description = "kafka模型id")
    private Long kafkaTbId;

    /**
     * 消费策略，全量-ALL、首次最新-NEW
     */
    @NotBlank(message = "消费策略不能为空")
    @Schema(description = "消费策略, 全量: earliest  最新: latest")
    @Pattern(regexp = "^earliest|latest$", message = "消费策略不存在")
    private String strategy;

    /**
     * 消费者分组名
     */
    @Schema(description = "消费者分组名")
    private String consumeGroup;

    /**
     * 源时间字段
     */
    @Schema(description = "源时间字段")
    private String sourceTimeField;

    /**
     * 源时间格式
     */
    @Schema(description = "源时间格式")
    private String sourceTimeFormat;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String sourceBeginTime;

    /**
     * es模型id
     */
    @NotNull(message = "es模型id不能为空")
    @Schema(description = "es模型id")
    private Long esTbId;

    /**
     * 重复数据检查
     */
    @NotNull(message = "重复数据检查不能为空")
    @Schema(description = "重复数据检查")
    private Boolean uniqueCheck;

    /**
     * 重复数据检查字段
     */
    @Schema(description = "重复数据检查字段不能为空")
    private String uniqueCheckField;

    /**
     * 字段映射，json字符串
     */
    @Schema(description = "重复数据检查字段不能为空")
    private List<StorageEsColumnMapping> schemaMappingList;

    /**
     * 集群
     */
    @Schema(description = "集群")
    private Long clusterId;

    /**
     * YarnSession 名称
     */
    @Schema(description = "YarnSession 名称")
    private Long yarnSessionId;

    /**
     * 框架
     */
    @Schema(description = "框架")
    private Long optsId;

    /**
     * 用户自定义设置
     */
    @Schema(description = "用户自定义设置")
    private Map<String, Object> customSetting;

    @Schema(description = "Kafka-Job配置")
    private SourceKafkaJobConfig kafkaJobConfig;

    @Schema(description = "Es-Job配置")
    private SinkElasticSearchJobConfig esJobConfig;


    @Schema(description = "是否强制启动")
    private Boolean forceStart = false;

    private List<String> tags;


    public Boolean getForceStart() {
        return forceStart != null && forceStart;
    }

    public void setForceStart(Boolean forceStart) {
        this.forceStart = forceStart;
    }

    public void validate() {
        ValidatorUtils.validateEntity(this);
        List<StorageEsColumnMapping> columnMappingList = this.getSchemaMappingList();
        if (CollUtil.isNotEmpty(columnMappingList)) {
            columnMappingList.forEach(StorageEsColumnMapping::validate);
        }
        if (this.getUniqueCheck()) {
            Assert.notBlank(this.getUniqueCheckField(), "您已开启重复数据检查，请选择重复数据检查字段");
            Assert.notNull(columnMappingList, "重复数据检查字段不在ES模型字段中");
            long count = columnMappingList.stream().filter(it -> it.getSinkField().equalsIgnoreCase(this.getUniqueCheckField())).count();
            Assert.isFalse(count == 0, "重复数据检查字段不在ES模型字段中");
        }
        if (StrUtil.isNotBlank(this.getSourceTimeField())) {
            Assert.notBlank(this.getSourceTimeFormat(), "源时间字段已填写，源时间格式不能为空");
            Assert.notBlank(this.getSourceBeginTime(), "源时间字段已填写，开始时间不能为空");
        }
        // 索引字段必填检测
        TableDeployCheckExistParam param = new TableDeployCheckExistParam();
        param.setTableId(this.getEsTbId());
        param.setInStatus(CollUtil.newArrayList(TableDeployStatusEnum.SUCCESS.code()));
        TbTableDeploy tbTableDeploy = ContextHolder.getBean(TbTableDeployService.class).checkIfExistAndReturn(param);
        Assert.notNull(tbTableDeploy, "无法找到对应的ES模型");
        EsModelSetting esModel = JsonUtil.decode(tbTableDeploy.getSetting(), EsModelSetting.class);
        if (!EsModelSetting.INDEX_NAME_STRATEGY_CUSTOM.equals(esModel.getIndexNameStrategy())) {
            return;
        }
        Set<String> colSet = new HashSet<>(2);
        for (String indexNamePattern : esModel.getIndexNamePatterns()) {
            Matcher dateMatcher = EsModelSetting.DATE_FIELD_FORMAT_PATTERN.matcher(indexNamePattern);
            if (dateMatcher.find()) {
                colSet.add(dateMatcher.group(1));
                continue;
            }
            Matcher fieldMatcher = EsModelSetting.FIELD_FORMAT_PATTERN.matcher(indexNamePattern);
            if (fieldMatcher.find()) {
                colSet.add(fieldMatcher.group(1));
            }
        }
        Map<Object, StorageEsColumnMapping> map = columnMappingList.stream()
                .collect(Collectors.toMap(StorageEsColumnMapping::getSinkField, it -> it));
        for (String col : colSet) {
            StorageEsColumnMapping columnMapping = map.get(col);
            Assert.notBlank(columnMapping.getSourceField(), "{}字段被ES索引名称使用，源kafka字段不能为空", col);
        }

    }

    @Override
    public TbStorageEs toEntity() {
        TbStorageEs entity = toEntity(new TbStorageEs());
        if (CollUtil.isNotEmpty(this.getSchemaMappingList())) {
            entity.setSchemaMapping(JsonUtil.encode(this.getSchemaMappingList()));
        }
        if (CollUtil.isNotEmpty(this.getCustomSetting())) {
            entity.setCustomSetting(JsonUtil.encode(this.getCustomSetting()));
        }
        if (Objects.nonNull(this.getKafkaJobConfig())) {
            entity.setKafkaJobConfig(JsonUtil.encode(this.getKafkaJobConfig()));
        }
        if (Objects.nonNull(this.getEsJobConfig())) {
            entity.setEsJobConfig(JsonUtil.encode(this.getEsJobConfig()));
        }
        return entity;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getKafkaTbId() {
        return kafkaTbId;
    }

    public void setKafkaTbId(Long kafkaTbId) {
        this.kafkaTbId = kafkaTbId;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getConsumeGroup() {
        return consumeGroup;
    }

    public void setConsumeGroup(String consumeGroup) {
        this.consumeGroup = consumeGroup;
    }

    public String getSourceTimeField() {
        return sourceTimeField;
    }

    public void setSourceTimeField(String sourceTimeField) {
        this.sourceTimeField = sourceTimeField;
    }

    public String getSourceTimeFormat() {
        return sourceTimeFormat;
    }

    public void setSourceTimeFormat(String sourceTimeFormat) {
        this.sourceTimeFormat = sourceTimeFormat;
    }

    public String getSourceBeginTime() {
        return sourceBeginTime;
    }

    public void setSourceBeginTime(String sourceBeginTime) {
        this.sourceBeginTime = sourceBeginTime;
    }

    public Long getEsTbId() {
        return esTbId;
    }

    public void setEsTbId(Long esTbId) {
        this.esTbId = esTbId;
    }

    public Boolean getUniqueCheck() {
        return uniqueCheck;
    }

    public void setUniqueCheck(Boolean uniqueCheck) {
        this.uniqueCheck = uniqueCheck;
    }

    public String getUniqueCheckField() {
        return uniqueCheckField;
    }

    public void setUniqueCheckField(String uniqueCheckField) {
        this.uniqueCheckField = uniqueCheckField;
    }

    public List<StorageEsColumnMapping> getSchemaMappingList() {
        return schemaMappingList;
    }

    public void setSchemaMappingList(List<StorageEsColumnMapping> schemaMappingList) {
        this.schemaMappingList = schemaMappingList;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public Long getYarnSessionId() {
        return yarnSessionId;
    }

    public void setYarnSessionId(Long yarnSessionId) {
        this.yarnSessionId = yarnSessionId;
    }

    public Long getOptsId() {
        return optsId;
    }

    public void setOptsId(Long optsId) {
        this.optsId = optsId;
    }

    public Map<String, Object> getCustomSetting() {
        return customSetting;
    }

    public void setCustomSetting(Map<String, Object> customSetting) {
        this.customSetting = customSetting;
    }

    public SourceKafkaJobConfig getKafkaJobConfig() {
        return kafkaJobConfig;
    }

    public void setKafkaJobConfig(SourceKafkaJobConfig kafkaJobConfig) {
        this.kafkaJobConfig = kafkaJobConfig;
    }

    public SinkElasticSearchJobConfig getEsJobConfig() {
        return esJobConfig;
    }

    public void setEsJobConfig(SinkElasticSearchJobConfig esJobConfig) {
        this.esJobConfig = esJobConfig;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return "StorageEsCreateReq{" +
                "businessFlowId=" + businessFlowId +
                ", name='" + name + '\'' +
                ", kafkaTbId=" + kafkaTbId +
                ", strategy='" + strategy + '\'' +
                ", consumeGroup='" + consumeGroup + '\'' +
                ", sourceTimeField='" + sourceTimeField + '\'' +
                ", sourceTimeFormat='" + sourceTimeFormat + '\'' +
                ", sourceBeginTime='" + sourceBeginTime + '\'' +
                ", esTbId=" + esTbId +
                ", uniqueCheck=" + uniqueCheck +
                ", uniqueCheckField='" + uniqueCheckField + '\'' +
                ", schemaMappingList=" + schemaMappingList +
                ", clusterId=" + clusterId +
                ", yarnSessionId=" + yarnSessionId +
                ", optsId=" + optsId +
                ", customSetting=" + customSetting +
                ", kafkaJobConfig=" + kafkaJobConfig +
                ", esJobConfig=" + esJobConfig +
                '}';
    }
}
