package com.eoi.jax.web.ingestion.model.job;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.repository.entity.TbJob;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
public class JobResp extends ProjectAuthRespModel implements
    IProjectAuthModel, IUserInfoExtensionModel, IRespModel<TbJob> {
    private Long id;
    private String jobName;
    private String jobDisplay;
    private String jobType;
    private String jobRole;
    private String jobCategory;
    private String jobDescription;
    private List<Map<String, Object>> inTypes;
    private List<Map<String, Object>> outTypes;
    private Map<String, Object> experimental;
    private String apiVersion;
    private Boolean internal;
    private Boolean hasIcon;
    private Boolean hasDoc;
    @JsonSerialize(using = LongStringSerializer.class)
    private Long jarId;
    private Date createTime;
    private Date updateTime;
    private Long createUser;
    private Long updateUser;

    @Override
    public JobResp fromEntity(TbJob tbJob) {
        IRespModel.super.fromEntity(tbJob);
        this.inTypes = JsonUtil.decode2ListMap(tbJob.getInTypes());
        this.outTypes = JsonUtil.decode2ListMap(tbJob.getOutTypes());
        this.experimental = JsonUtil.decode2Map(tbJob.getExperimental());
        return this;
    }

    public String getDocUrl() {
        return getHasDoc() ? String.format("/api/v2/ingestion/job-name/%s/document", jobName) : null;
    }

    public String getIconUrl() {
        return getHasIcon() ? String.format("/api/v2/ingestion/job-name/%s/icon.svg", jobName) : null;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobDisplay() {
        return jobDisplay;
    }

    public void setJobDisplay(String jobDisplay) {
        this.jobDisplay = jobDisplay;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public String getJobRole() {
        return jobRole;
    }

    public void setJobRole(String jobRole) {
        this.jobRole = jobRole;
    }

    public String getJobCategory() {
        return jobCategory;
    }

    public void setJobCategory(String jobCategory) {
        this.jobCategory = jobCategory;
    }

    public String getJobDescription() {
        return jobDescription;
    }

    public void setJobDescription(String jobDescription) {
        this.jobDescription = jobDescription;
    }

    public List<Map<String, Object>> getInTypes() {
        return inTypes;
    }

    public void setInTypes(List<Map<String, Object>> inTypes) {
        this.inTypes = inTypes;
    }

    public List<Map<String, Object>> getOutTypes() {
        return outTypes;
    }

    public void setOutTypes(List<Map<String, Object>> outTypes) {
        this.outTypes = outTypes;
    }

    public Map<String, Object> getExperimental() {
        return experimental;
    }

    public void setExperimental(Map<String, Object> experimental) {
        this.experimental = experimental;
    }

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public Boolean getInternal() {
        return internal;
    }

    public void setInternal(Boolean internal) {
        this.internal = internal;
    }

    public Boolean getHasIcon() {
        return Boolean.TRUE.equals(hasIcon);
    }

    public void setHasIcon(Boolean hasIcon) {
        this.hasIcon = hasIcon;
    }

    public Boolean getHasDoc() {
        return Boolean.TRUE.equals(hasDoc);
    }

    public void setHasDoc(Boolean hasDoc) {
        this.hasDoc = hasDoc;
    }

    public Long getJarId() {
        return jarId;
    }

    public void setJarId(Long jarId) {
        this.jarId = jarId;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
