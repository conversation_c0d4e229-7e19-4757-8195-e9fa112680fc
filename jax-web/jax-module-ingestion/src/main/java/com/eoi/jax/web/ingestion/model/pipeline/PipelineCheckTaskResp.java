package com.eoi.jax.web.ingestion.model.pipeline;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckTask;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/14
 */
public class PipelineCheckTaskResp {

    @Schema(description = "通知用户")
    private Boolean notify;

    private List<CheckTaskResp> checkTaskList;

    public PipelineCheckTaskResp() {
    }

    public PipelineCheckTaskResp(Boolean notify) {
        this.notify = notify;
    }

    public static class CheckTaskResp {


        @JsonSerialize(using = LongStringSerializer.class)
        @Schema(description = "主键id")
        private Long checkTaskId;

        /**
         * 任务名称
         */
        @Schema(description = "任务名称")
        private String name;

        /**
         * 运行模式
         */
        @Schema(description = "运行模式")
        private String runMode;

        /**
         * 作业选择方式
         */
        @Schema(description = "作业选择方式")
        private String selectMode;

        @Schema(description = "选择的pipeline任务")
        private String selectedPipelineTask;

        /**
         * 算子中文名称
         */
        @Schema(description = "算子名称")
        private String jobDisplay;

        /**
         * 算子输出挂载点
         */
        @Schema(description = "算子输出挂载点")
        private Integer jobSlot;


        @Schema(description = "检测对象")
        private String checkObj;

        @Schema(description = "匹配状态")
        private String matchedStatus;

        public CheckTaskResp() {
        }

        public CheckTaskResp(TbRealtimeCheckTask tbRealtimeCheckTask) {
            this.checkTaskId = tbRealtimeCheckTask.getId();
            this.name = tbRealtimeCheckTask.getName();
            this.runMode = tbRealtimeCheckTask.getRunMode();
            this.selectMode = tbRealtimeCheckTask.getSelectMode();
            this.selectedPipelineTask = tbRealtimeCheckTask.getSelectedPipelineTask();
            this.jobDisplay = tbRealtimeCheckTask.getJobDisplay();
            this.jobSlot = tbRealtimeCheckTask.getJobSlot();
        }

        public Long getCheckTaskId() {
            return checkTaskId;
        }

        public void setCheckTaskId(Long checkTaskId) {
            this.checkTaskId = checkTaskId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getRunMode() {
            return runMode;
        }

        public void setRunMode(String runMode) {
            this.runMode = runMode;
        }

        public String getSelectMode() {
            return selectMode;
        }

        public void setSelectMode(String selectMode) {
            this.selectMode = selectMode;
        }

        public String getSelectedPipelineTask() {
            return selectedPipelineTask;
        }

        public void setSelectedPipelineTask(String selectedPipelineTask) {
            this.selectedPipelineTask = selectedPipelineTask;
        }

        public String getJobDisplay() {
            return jobDisplay;
        }

        public void setJobDisplay(String jobDisplay) {
            this.jobDisplay = jobDisplay;
        }

        public Integer getJobSlot() {
            return jobSlot;
        }

        public void setJobSlot(Integer jobSlot) {
            this.jobSlot = jobSlot;
        }

        public String getCheckObj() {
            return checkObj;
        }

        public void setCheckObj(String checkObj) {
            this.checkObj = checkObj;
        }

        public String getMatchedStatus() {
            return matchedStatus;
        }

        public void setMatchedStatus(String matchedStatus) {
            this.matchedStatus = matchedStatus;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            CheckTaskResp that = (CheckTaskResp) o;
            return Objects.equals(checkTaskId, that.checkTaskId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(checkTaskId);
        }
    }


    public List<CheckTaskResp> getCheckTaskList() {
        return checkTaskList;
    }

    public void setCheckTaskList(List<CheckTaskResp> checkTaskList) {
        this.checkTaskList = checkTaskList;
    }

    public Boolean getNotify() {
        return notify;
    }

    public void setNotify(Boolean notify) {
        this.notify = notify;
    }
}
