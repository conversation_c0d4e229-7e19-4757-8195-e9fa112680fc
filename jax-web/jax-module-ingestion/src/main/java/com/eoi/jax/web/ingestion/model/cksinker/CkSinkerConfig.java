package com.eoi.jax.web.ingestion.model.cksinker;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zsc
 * @create 2023/2/10 18:30
 */
public class CkSinkerConfig implements Serializable {
    /**
     * clickhouse
     */
    @JsonAlias("Clickhouse")
    private Map<String, Object> clickhouse;

    /**
     * kafka
     */
    @JsonAlias("Kafka")
    private Map<String, Object> kafka;

    /**
     * task
     */
    @JsonAlias("Task")
    private Map<String, Object> task;

    /**
     * tasks
     */
    @JsonAlias("Tasks")
    private List<Map<String, Object>> tasks;

    /**
     * logLevel
     */
    @JsonAlias("LogLevel")
    private String logLevel;

    private Map<String, Object> otherProperties = new HashMap<>();

    public Map<String, Object> getClickhouse() {
        return clickhouse;
    }

    public void setClickhouse(Map<String, Object> clickhouse) {
        this.clickhouse = clickhouse;
    }

    public Map<String, Object> getKafka() {
        return kafka;
    }

    public void setKafka(Map<String, Object> kafka) {
        this.kafka = kafka;
    }

    public Map<String, Object> getTask() {
        return task;
    }

    public void setTask(Map<String, Object> task) {
        this.task = task;
    }

    public List<Map<String, Object>> getTasks() {
        return tasks;
    }

    public void setTasks(List<Map<String, Object>> tasks) {
        this.tasks = tasks;
    }

    public String getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    @JsonAnySetter
    public void setProperty(String key, Object value) {
        otherProperties.put(key, value);
    }

    @JsonAnyGetter
    public Map<String, Object> getProperties() {
        return otherProperties;
    }

    public static class Clickhouse implements Serializable {
        /**
         * cluster
         */
        private String cluster;
        /**
         * hosts
         */
        private List<List<String>> hosts;
        /**
         * port
         */
        private Integer port;
        /**
         * username
         */
        private String username;
        /**
         * password
         */
        private String password;
        /**
         * db
         */
        private String db;
        /**
         * secure
         */
        private Boolean secure;
        /**
         * insecureSkipVerify
         */
        private Boolean insecureSkipVerify;
        /**
         * retryTimes
         */
        private Integer retryTimes;
        /**
         * maxOpenConns
         */
        private Integer maxOpenConns;

        public Map<String, Object> toMap() {
            return BeanUtil.beanToMap(this);
        }

        public String getCluster() {
            return cluster;
        }

        public void setCluster(String cluster) {
            this.cluster = cluster;
        }

        public List<List<String>> getHosts() {
            return hosts;
        }

        public void setHosts(List<List<String>> hosts) {
            this.hosts = hosts;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getDb() {
            return db;
        }

        public void setDb(String db) {
            this.db = db;
        }

        public Boolean getSecure() {
            return secure;
        }

        public void setSecure(Boolean secure) {
            this.secure = secure;
        }

        public Boolean getInsecureSkipVerify() {
            return insecureSkipVerify;
        }

        public void setInsecureSkipVerify(Boolean insecureSkipVerify) {
            this.insecureSkipVerify = insecureSkipVerify;
        }

        public Integer getRetryTimes() {
            return retryTimes;
        }

        public void setRetryTimes(Integer retryTimes) {
            this.retryTimes = retryTimes;
        }

        public Integer getMaxOpenConns() {
            return maxOpenConns;
        }

        public void setMaxOpenConns(Integer maxOpenConns) {
            this.maxOpenConns = maxOpenConns;
        }
    }

    public static class Kafka implements Serializable {
        /**
         * brokers
         */
        private String brokers;
        /**
         * security
         */
        private Map<String, Object> security;
        /**
         * tls
         */
        private Map<String, Object> tls;
        /**
         * sasl
         */
        private Map<String, Object> sasl;


        public Map<String, Object> toMap() {
            return BeanUtil.beanToMap(this);
        }

        public String getBrokers() {
            return brokers;
        }

        public void setBrokers(String brokers) {
            this.brokers = brokers;
        }

        public Map<String, Object> getSecurity() {
            return security;
        }

        public void setSecurity(Map<String, Object> security) {
            this.security = security;
        }

        public Map<String, Object> getTls() {
            return tls;
        }

        public void setTls(Map<String, Object> tls) {
            this.tls = tls;
        }

        public Map<String, Object> getSasl() {
            return sasl;
        }

        public void setSasl(Map<String, Object> sasl) {
            this.sasl = sasl;
        }


        public static class Security implements Serializable {
            private String securityProtocol;
            private String saslKerberosServiceName;
            private String saslMechanism;
            private String saslJaasConfig;

            public Map<String, Object> toMap() {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("security.protocol", this.securityProtocol);
                map.put("sasl.kerberos.service.name", this.saslKerberosServiceName);
                map.put("sasl.mechanism", this.saslMechanism);
                map.put("sasl.jaas.config", this.saslJaasConfig);
                return map;
            }

            public Map<String, Object> toHumpMap() {
                return BeanUtil.beanToMap(this);
            }

            public static Map<String, Object> humpMapToPointMap(Map<String, Object> humpMap) {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("security.protocol", humpMap.get("securityProtocol"));
                map.put("sasl.kerberos.service.name", humpMap.get("saslKerberosServiceName"));
                map.put("sasl.mechanism", humpMap.get("saslMechanism"));
                map.put("sasl.jaas.config", humpMap.get("saslJaasConfig"));
                return map;
            }

            public String getSecurityProtocol() {
                return securityProtocol;
            }

            public void setSecurityProtocol(String securityProtocol) {
                this.securityProtocol = securityProtocol;
            }

            public String getSaslKerberosServiceName() {
                return saslKerberosServiceName;
            }

            public void setSaslKerberosServiceName(String saslKerberosServiceName) {
                this.saslKerberosServiceName = saslKerberosServiceName;
            }

            public String getSaslMechanism() {
                return saslMechanism;
            }

            public void setSaslMechanism(String saslMechanism) {
                this.saslMechanism = saslMechanism;
            }

            public String getSaslJaasConfig() {
                return saslJaasConfig;
            }

            public void setSaslJaasConfig(String saslJaasConfig) {
                this.saslJaasConfig = saslJaasConfig;
            }
        }

        public static class Tls implements Serializable {
            /**
             * enable
             */
            private Boolean enable;
            /**
             * caCertFiles
             */
            private String caCertFiles;
            /**
             * clientCertFile
             */
            private String clientCertFile;
            /**
             * clientKeyFile
             */
            private String clientKeyFile;

            public Map<String, Object> toMap() {
                return BeanUtil.beanToMap(this);
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }

            public String getCaCertFiles() {
                return caCertFiles;
            }

            public void setCaCertFiles(String caCertFiles) {
                this.caCertFiles = caCertFiles;
            }

            public String getClientCertFile() {
                return clientCertFile;
            }

            public void setClientCertFile(String clientCertFile) {
                this.clientCertFile = clientCertFile;
            }

            public String getClientKeyFile() {
                return clientKeyFile;
            }

            public void setClientKeyFile(String clientKeyFile) {
                this.clientKeyFile = clientKeyFile;
            }
        }

        public static class Sasl implements Serializable {
            /**
             * enable
             */
            private Boolean enable;
            /**
             * mechanism
             */
            private String mechanism;
            /**
             * username
             */
            private String username;
            /**
             * password
             */
            private String password;
            /**
             * gssapi
             */
            private Map<String, Object> gssapi;

            public Map<String, Object> toMap() {
                return BeanUtil.beanToMap(this);
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }

            public String getMechanism() {
                return mechanism;
            }

            public void setMechanism(String mechanism) {
                this.mechanism = mechanism;
            }

            public String getUsername() {
                return username;
            }

            public void setUsername(String username) {
                this.username = username;
            }

            public String getPassword() {
                return password;
            }

            public void setPassword(String password) {
                this.password = password;
            }

            public Map<String, Object> getGssapi() {
                return gssapi;
            }

            public void setGssapi(Map<String, Object> gssapi) {
                this.gssapi = gssapi;
            }

            public static class Gssapi implements Serializable {
                /**
                 * authtype
                 */
                private Integer authtype;
                /**
                 * keytabpath
                 */
                private String keytabpath;
                /**
                 * kerberosconfigpath
                 */
                private String kerberosconfigpath;
                /**
                 * servicename
                 */
                private String servicename;
                /**
                 * username
                 */
                private String username;
                /**
                 * password
                 */
                private String password;
                /**
                 * realm
                 */
                private String realm;
                /**
                 * disablepafxfast
                 */
                private Boolean disablepafxfast;

                public Map<String, Object> toMap() {
                    return BeanUtil.beanToMap(this);
                }

                public Integer getAuthtype() {
                    return authtype;
                }

                public void setAuthtype(Integer authtype) {
                    this.authtype = authtype;
                }

                public String getKeytabpath() {
                    return keytabpath;
                }

                public void setKeytabpath(String keytabpath) {
                    this.keytabpath = keytabpath;
                }

                public String getKerberosconfigpath() {
                    return kerberosconfigpath;
                }

                public void setKerberosconfigpath(String kerberosconfigpath) {
                    this.kerberosconfigpath = kerberosconfigpath;
                }

                public String getServicename() {
                    return servicename;
                }

                public void setServicename(String servicename) {
                    this.servicename = servicename;
                }

                public String getUsername() {
                    return username;
                }

                public void setUsername(String username) {
                    this.username = username;
                }

                public String getPassword() {
                    return password;
                }

                public void setPassword(String password) {
                    this.password = password;
                }

                public String getRealm() {
                    return realm;
                }

                public void setRealm(String realm) {
                    this.realm = realm;
                }

                public Boolean getDisablepafxfast() {
                    return disablepafxfast;
                }

                public void setDisablepafxfast(Boolean disablepafxfast) {
                    this.disablepafxfast = disablepafxfast;
                }
            }
        }
    }

    public static class Task implements Serializable {
        /**
         * name
         */
        private String name;
        /**
         * kafkaClient
         */
        private String kafkaClient;
        /**
         * topic
         */
        private String topic;
        /**
         * earliest
         */
        private Boolean earliest;
        /**
         * consumerGroup
         */
        private String consumerGroup;
        /**
         * parser
         */
        private String parser;
        /**
         * tableName
         */
        private String tableName;
        /**
         * seriesTableName
         */
        private String seriesTableName;
        /**
         * dims
         */
        private List<Dims> dims;
        /**
         * autoSchema
         */
        private Boolean autoSchema;
        /**
         * excludeColumns
         */
        private List<?> excludeColumns;
        /**
         * dynamicSchema
         */
        private Map<String, Object> dynamicSchema;
        /**
         * shardingKey
         */
        private String shardingKey;
        /**
         * shardingStripe
         */
        private Integer shardingStripe;
        /**
         * flushInterval
         */
        private Integer flushInterval;
        /**
         * bufferSize
         */
        private Integer bufferSize;
        /**
         * timeZone
         */
        private String timeZone;
        /**
         * timeUnit
         */
        private Double timeUnit;

        private Boolean prometheusSchema;

        private String promLabelsBlackList;

        private String delimiter;

        public Map<String, Object> toMap() {
            return BeanUtil.beanToMap(this);
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getKafkaClient() {
            return kafkaClient;
        }

        public void setKafkaClient(String kafkaClient) {
            this.kafkaClient = kafkaClient;
        }

        public Boolean getPrometheusSchema() {
            return prometheusSchema;
        }

        public void setPrometheusSchema(Boolean prometheusSchema) {
            this.prometheusSchema = prometheusSchema;
        }

        public String getPromLabelsBlackList() {
            return promLabelsBlackList;
        }

        public void setPromLabelsBlackList(String promLabelsBlackList) {
            this.promLabelsBlackList = promLabelsBlackList;
        }

        public String getDelimiter() {
            return delimiter;
        }

        public void setDelimiter(String delimiter) {
            this.delimiter = delimiter;
        }

        public String getTopic() {
            return topic;
        }

        public void setTopic(String topic) {
            this.topic = topic;
        }

        public Boolean getEarliest() {
            return earliest;
        }

        public void setEarliest(Boolean earliest) {
            this.earliest = earliest;
        }

        public String getConsumerGroup() {
            return consumerGroup;
        }

        public void setConsumerGroup(String consumerGroup) {
            this.consumerGroup = consumerGroup;
        }

        public String getParser() {
            return parser;
        }

        public void setParser(String parser) {
            this.parser = parser;
        }

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getSeriesTableName() {
            return seriesTableName;
        }

        public void setSeriesTableName(String seriesTableName) {
            this.seriesTableName = seriesTableName;
        }

        public List<Dims> getDims() {
            return dims;
        }

        public void setDims(List<Dims> dims) {
            this.dims = dims;
        }

        public Boolean getAutoSchema() {
            return autoSchema;
        }

        public void setAutoSchema(Boolean autoSchema) {
            this.autoSchema = autoSchema;
        }

        public List<?> getExcludeColumns() {
            return excludeColumns;
        }

        public void setExcludeColumns(List<?> excludeColumns) {
            this.excludeColumns = excludeColumns;
        }

        public Map<String, Object> getDynamicSchema() {
            return dynamicSchema;
        }

        public void setDynamicSchema(Map<String, Object> dynamicSchema) {
            this.dynamicSchema = dynamicSchema;
        }

        public String getShardingKey() {
            return shardingKey;
        }

        public void setShardingKey(String shardingKey) {
            this.shardingKey = shardingKey;
        }

        public Integer getShardingStripe() {
            return shardingStripe;
        }

        public void setShardingStripe(Integer shardingStripe) {
            this.shardingStripe = shardingStripe;
        }

        public Integer getFlushInterval() {
            return flushInterval;
        }

        public void setFlushInterval(Integer flushInterval) {
            this.flushInterval = flushInterval;
        }

        public Integer getBufferSize() {
            return bufferSize;
        }

        public void setBufferSize(Integer bufferSize) {
            this.bufferSize = bufferSize;
        }

        public String getTimeZone() {
            return timeZone;
        }

        public void setTimeZone(String timeZone) {
            this.timeZone = timeZone;
        }

        public Double getTimeUnit() {
            return timeUnit;
        }

        public void setTimeUnit(Double timeUnit) {
            this.timeUnit = timeUnit;
        }

        public static class DynamicSchema implements Serializable {
            /**
             * enable
             */
            private Boolean enable;
            /**
             * maxDims
             */
            private Integer maxDims;
            /**
             * whiteList
             */
            private String whiteList;
            /**
             * blackList
             */
            private String blackList;

            public Map<String, Object> toMap() {
                return BeanUtil.beanToMap(this);
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }

            public Integer getMaxDims() {
                return maxDims;
            }

            public void setMaxDims(Integer maxDims) {
                this.maxDims = maxDims;
            }

            public String getWhiteList() {
                return whiteList;
            }

            public void setWhiteList(String whiteList) {
                this.whiteList = whiteList;
            }

            public String getBlackList() {
                return blackList;
            }

            public void setBlackList(String blackList) {
                this.blackList = blackList;
            }
        }

        public static class Dims implements Serializable {
            /**
             * name
             */
            private String name;
            /**
             * type
             */
            private String type;
            /**
             * sourcename
             */
            private String sourcename;

            public Map<String, Object> toMap() {
                return BeanUtil.beanToMap(this);
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public String getSourcename() {
                return sourcename;
            }

            public void setSourcename(String sourcename) {
                this.sourcename = sourcename;
            }
        }
    }
}
