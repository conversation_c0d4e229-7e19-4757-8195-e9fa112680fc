package com.eoi.jax.web.ingestion.plugin.pipeline;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.config.config.JaxApiAuthConfig;
import com.eoi.jax.web.core.model.jaxcluster.JaxClusterNode;
import com.eoi.jax.web.core.model.jaxcluster.JaxClusterNodes;
import com.eoi.jax.web.core.service.JaxClusterService;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineJob;
import com.eoi.jax.web.ingestion.plugin.BasePipelinePlugin;
import com.eoi.jax.web.ingestion.plugin.IPipelinePlugin;
import com.eoi.jax.web.ingestion.provider.manager.PipelineCtx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/5/13
 */
@Component
public class JaxConfigPipelinePluginImpl extends BasePipelinePlugin implements IPipelinePlugin {
    public static final String DIC_ENUM_LOOK_UP_JOB = "com.eoi.jax.flink.job.process.DicEnumLookUpJob";
    private static final Logger LOGGER = LoggerFactory.getLogger(JaxConfigPipelinePluginImpl.class);

    @Override
    public void startReq(PipelineCtx pipeline) {
        process(pipeline);
    }

    @Override
    public void debugReq(PipelineCtx pipeline) {
        process(pipeline);
    }

    private void process(PipelineCtx pipeline) {
        PipelineConfig pipelineConfig = pipeline.getPipelineConfig();
        for (PipelineJob job : pipelineConfig.getJobs()) {
            String jobName = job.getJobName();
            String oldConfig = JsonUtil.encode(job.getJobConfig());
            if (DIC_ENUM_LOOK_UP_JOB.equals(jobName)) {
                processDicEnumLookUpJob(job);
            }
            String newConfig = JsonUtil.encode(job.getJobConfig());
            if (!StrUtil.equals(oldConfig, newConfig)) {
                LOGGER.info("Pipeline Plugin invoke pipeline {} config {} from {}",
                        pipeline.getPipeline().getPipelineName(),
                        newConfig,
                        oldConfig
                );
            }
        }
    }

    private void processDicEnumLookUpJob(PipelineJob job) {
        Map<String, Object> jobConfig = job.getJobConfig();
        Object jaxServers = jobConfig.get("jaxServers");
        if (jaxServers == null || CollUtil.isEmpty((List<String>) jaxServers)) {
            JaxClusterService jaxClusterService = ContextHolder.getBean(JaxClusterService.class);
            JaxClusterNodes nodes = jaxClusterService.getClusterNodes();
            JaxClusterNode local = jaxClusterService.getLocalNode();
            LinkedHashSet<String> httpServers = new LinkedHashSet<>();
            httpServers.add(local.getHttpAddress());
            for (JaxClusterNode node : nodes.getNodes()) {
                httpServers.add(node.getHttpAddress());
            }
            jobConfig.put("jaxServers", httpServers);
        }
        Object apiHeaders = jobConfig.get("apiHeaders");
        if (apiHeaders == null || CollUtil.isEmpty((Map<String, String>) apiHeaders)) {
            JaxApiAuthConfig jaxApiAuthConfig = ContextHolder.getBean(SystemConfigHolder.class).getBean(SystemConfigEnum.JAX_API_AUTH);
            JaxApiAuthConfig.AppSecret appSecret = CollUtil.isEmpty(jaxApiAuthConfig.getAppSecretList()) ?
                    null : jaxApiAuthConfig.getAppSecretList().get(0);
            if (appSecret != null) {
                jobConfig.put("apiHeaders", appSecret.genHttpHeader());
            }
        }
    }
}
