package com.eoi.jax.web.ingestion.model.jar;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
public class ScanMeta {
    private boolean success;
    private Map<String, JobMeta> jobs;
    private Map<String, AlgMeta> algs;
    private Map<String, SqlMeta> sqls;
    private String message;
    private String stackTrace;

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Map<String, JobMeta> getJobs() {
        return jobs;
    }

    public void setJobs(Map<String, JobMeta> jobs) {
        this.jobs = jobs;
    }

    public Map<String, AlgMeta> getAlgs() {
        return algs;
    }

    public void setAlgs(Map<String, AlgMeta> algs) {
        this.algs = algs;
    }

    public Map<String, SqlMeta> getSqls() {
        return sqls;
    }

    public void setSqls(Map<String, SqlMeta> sqls) {
        this.sqls = sqls;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }
}
