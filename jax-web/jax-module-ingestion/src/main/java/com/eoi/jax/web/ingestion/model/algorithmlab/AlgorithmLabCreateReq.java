package com.eoi.jax.web.ingestion.model.algorithmlab;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbAlgorithmLab;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/2/20
 */
public class AlgorithmLabCreateReq implements ICreateModel<TbAlgorithmLab> {

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Schema(description = "名称")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * CPU配置
     */
    @NotNull(message = "CPU配置不能为空")
    @Schema(description = "CPU配置")
    private Integer cpu;

    /**
     * 内存配置
     */
    @NotNull(message = "内存配置不能为空")
    @Schema(description = "内存配置")
    private Integer memory;

    @Override
    public TbAlgorithmLab toEntity() {
        TbAlgorithmLab entity = toEntity(new TbAlgorithmLab());
        entity.setStatus("STOPPED");
        return entity;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Integer getMemory() {
        return memory;
    }

    public void setMemory(Integer memory) {
        this.memory = memory;
    }


}
