package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.service.IBaseProjectAuthService;
import com.eoi.jax.web.ingestion.model.businessflow.BusinessFlowCreateReq;
import com.eoi.jax.web.ingestion.model.businessflow.BusinessFlowQueryReq;
import com.eoi.jax.web.ingestion.model.businessflow.BusinessFlowResp;
import com.eoi.jax.web.ingestion.model.businessflow.BusinessFlowUpdateReq;
import com.eoi.jax.web.repository.entity.TbBusinessFlow;
import com.eoi.jax.web.repository.service.TbBusinessFlowService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
public interface BusinessFlowService extends IBaseProjectAuthService<
        TbBusinessFlowService,
        TbBusinessFlow,
        BusinessFlowResp,
        BusinessFlowCreateReq,
        BusinessFlowUpdateReq,
        BusinessFlowQueryReq> {

    /**
     * 创建或者获取默认业务流程
     * @return
     */
    TbBusinessFlow createOrGetDefault();

    /**
     * 业务流程全量查询
     *
     * @return
     */
    @Override
    List<BusinessFlowResp> all();

    /**
     * 分页查询业务流程
     *
     * @param req
     * @return
     */
    @Override
    Paged<BusinessFlowResp> query(BusinessFlowQueryReq req);

    /**
     * 根据Id获取业务流程
     * @param id
     * @return
     */
    BusinessFlowResp get(Long id);

    /**
     * 创建业务流程
     *
     * @param req 请求对象
     * @return 业务对象实体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowResp create(BusinessFlowCreateReq req);


    /**
     * 删除业务流程
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowResp delete(Long id);


    /**
     * 更新业务流程
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    BusinessFlowResp update(BusinessFlowUpdateReq req);


}
