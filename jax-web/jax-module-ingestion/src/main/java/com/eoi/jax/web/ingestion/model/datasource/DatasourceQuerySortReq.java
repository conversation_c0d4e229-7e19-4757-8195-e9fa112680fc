package com.eoi.jax.web.ingestion.model.datasource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbDatasource;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class DatasourceQuerySortReq implements ISortReq<TbDatasource> {

    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbDatasource> order(QueryWrapper<TbDatasource> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbDatasource::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbDatasource::getUpdateTime);
        return wrapper;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
