package com.eoi.jax.web.ingestion.model;

import com.eoi.jax.web.core.excel.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: tangy
 * @Date: 2023/12/11
 * @Desc:
 **/
public class DateDeveloperExportModel implements Serializable {

    @Excel(name = "业务流程名称", no = 0)
    @Schema(description = "业务流程名称")
    private String businessFlowName;
    @Excel(name = "任务类型", no = 1)
    @Schema(description = "任务类型")
    private String categoryName;

    @Excel(name = "任务名称", no = 2)
    @Schema(description = "任务名称")
    private String name;
    @Excel(name = "任务类型", no = 3)
    @Schema(description = "类型")
    private String typeName;
    @Excel(name = "关联类型", no = 4)
    @Schema(description = "关联类型")
    private String referenceTypeName;
    @Excel(name = "任务配置", no = 5)
    @Schema(description = "任务配置")
    private Map<String, Object> taskConfigMap;

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getReferenceTypeName() {
        return referenceTypeName;
    }

    public void setReferenceTypeName(String referenceTypeName) {
        this.referenceTypeName = referenceTypeName;
    }

    public Map<String, Object> getTaskConfigMap() {
        return taskConfigMap;
    }

    public void setTaskConfigMap(Map<String, Object> taskConfigMap) {
        this.taskConfigMap = taskConfigMap;
    }
}
