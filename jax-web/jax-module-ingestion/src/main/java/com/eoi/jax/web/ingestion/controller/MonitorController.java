package com.eoi.jax.web.ingestion.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.ingestion.model.monitor.*;
import com.eoi.jax.web.ingestion.service.MonitorService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;


@RestController
public class MonitorController implements V2Controller {

    @Autowired
    private MonitorService monitorService;

    @Operation(summary = "获取所有的TaskManager的Id")
    @PostMapping("monitor/tmIdList")
    public Response<TmIdListResp> tmIdList(@Valid @RequestBody TaskManagerIdListReq req) {
        return Response.success(monitorService.allTaskManagerId(req));
    }

    @Operation(summary = "诊断概览")
    @GetMapping("monitor/overview/{id}")
    public Response<OverviewResp> overview(@PathVariable("id") Long id) {
        return Response.success(monitorService.overview(id));
    }

    @Operation(summary = "job拓扑图")
    @PostMapping("monitor/job_topology")
    public Response<JobTopologyResp> jobTopology(@Valid @RequestBody JobTopologyReq req) {
        return Response.success(monitorService.jobTopology(req));
    }

    @Operation(summary = "jobManager监控指标")
    @PostMapping("monitor/job_manager")
    public Response<JobManagerMonitorResp> jobManager(@Valid @RequestBody JobManagerMetricsReq req) {
        return Response.success(monitorService.jobManagerMonitor(req));
    }

    @Operation(summary = "taskManager监控指标")
    @PostMapping("monitor/task_manager")
    public Response<TaskManagerMonitorResp> taskManager(@Valid @RequestBody TaskManagerMetricsReq req) {
        return Response.success(monitorService.taskManagerMonitor(req));
    }

}
