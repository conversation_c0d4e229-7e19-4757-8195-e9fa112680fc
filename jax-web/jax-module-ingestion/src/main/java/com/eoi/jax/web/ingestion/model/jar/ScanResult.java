package com.eoi.jax.web.ingestion.model.jar;

import com.eoi.jax.web.repository.entity.TbAlg;
import com.eoi.jax.web.repository.entity.TbDsHandler;
import com.eoi.jax.web.repository.entity.TbFlinkSqlFunc;
import com.eoi.jax.web.repository.entity.TbJob;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
public class ScanResult {
    private List<TbJob> jobs = new ArrayList<>();
    private List<TbAlg> algs = new ArrayList<>();
    private List<TbFlinkSqlFunc> funcs = new ArrayList<>();
    private List<TbDsHandler> dsHandlers = new ArrayList<>();

    public List<TbJob> getJobs() {
        return jobs;
    }

    public void setJobs(List<TbJob> jobs) {
        this.jobs = jobs;
    }

    public List<TbAlg> getAlgs() {
        return algs;
    }

    public void setAlgs(List<TbAlg> algs) {
        this.algs = algs;
    }

    public List<TbFlinkSqlFunc> getFuncs() {
        return funcs;
    }

    public void setFuncs(List<TbFlinkSqlFunc> funcs) {
        this.funcs = funcs;
    }

    public List<TbDsHandler> getDsHandlers() {
        return dsHandlers;
    }

    public void setDsHandlers(List<TbDsHandler> dsHandlers) {
        this.dsHandlers = dsHandlers;
    }
}
