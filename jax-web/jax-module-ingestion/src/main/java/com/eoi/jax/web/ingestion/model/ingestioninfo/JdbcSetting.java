package com.eoi.jax.web.ingestion.model.ingestioninfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.field.CronField;
import com.cronutils.model.field.CronFieldName;
import com.cronutils.model.field.expression.Every;
import com.cronutils.model.field.expression.FieldExpression;
import com.cronutils.model.field.expression.QuestionMark;
import com.cronutils.parser.CronParser;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.integration.model.cell.agent.CellTaskInput;
import com.eoi.jax.web.core.integration.model.cell.agent.CellTaskInputParam;
import com.eoi.jax.web.core.util.ValidatorUtils;
import com.eoi.jax.web.ingestion.enumrate.DatasourceConnectionPlatformEnum;
import com.eoi.jax.web.ingestion.model.cellagent.CellAgentListQueryResp;
import com.eoi.jax.web.ingestion.model.datasource.DatasourceResp;
import com.eoi.jax.web.ingestion.service.DatasourceService;
import com.eoi.jax.api.dataservice.util.ConnectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/18
 */
public class JdbcSetting extends AbstractSetting {

    private static final String CUSTOM = "CUSTOM";

    @NotNull(message = "输入参数不能为空")
    private JdbcInput input;


    @Override
    public List<String> tags() {
        return input.tagList;
    }

    @Override
    public void valid(AbstractSetting settingInDb, Boolean isPublished) {
        ValidatorUtils.validateEntity(input);
        if (CUSTOM.equals(input.getType())) {
            Assert.isTrue(StrUtil.isNotBlank(input.getJdbcDriver()), "JDBC Driver不能为空");
            Assert.isTrue(StrUtil.isNotBlank(input.getJdbcUrl()), "JDBC URL不能为空");
            Assert.isTrue(StrUtil.isNotBlank(input.getUserName()), "用户名不能为空");
            Assert.isTrue(StrUtil.isNotBlank(input.getPassword()), "密码不能为空");
        } else {
            Assert.isTrue(Objects.nonNull(input.getDatasourceId()), "数据源ID不能为空");
            Assert.isTrue(StrUtil.isNotBlank(input.getDatabase()), "数据库不能为空");
        }

        if ("advanced".equals(input.getSyncMode()) || "incremental".equals(input.getSyncMode())) {
            Assert.isTrue(StrUtil.isNotBlank(input.getTrackingColumn()), "同步关键字不能为空");
            Assert.isTrue(StrUtil.isNotBlank(input.getTrackingColumnType()), "同步关键字类型不能为空");
        }

    }

    @Override
    public String buildInputParam(ConvertToCellTaskInputParam convertToCellTaskInputParam) {
        CellTaskInput<CellTaskInputParam.JdbcInput> cellTaskInput = new CellTaskInput<>();
        cellTaskInput.setAgentId(convertToCellTaskInputParam.getAgentId());
        cellTaskInput.setInputType(convertToCellTaskInputParam.getType().inputType());

        CellTaskInputParam.JdbcInput jdbcInputParam = new CellTaskInputParam.JdbcInput();
        jdbcInputParam.setId(convertToCellTaskInputParam.getTaskId().toString());
        jdbcInputParam.setEnable(convertToCellTaskInputParam.getEnable());
        if (CUSTOM.equals(input.getType())) {
            jdbcInputParam.setJdbcDriverClass(input.getJdbcDriver());
            jdbcInputParam.setJdbcConnectionString(input.getJdbcUrl());
            jdbcInputParam.setJdbcUser(input.getUserName());
            jdbcInputParam.setJdbcPassword(input.getPassword());
            jdbcInputParam.setJdbcDriverLibrary(input.getJdbcJar());
        } else {
            DatasourceService datasourceService = ContextHolder.getBean(DatasourceService.class);
            DatasourceResp datasourceResp = datasourceService.get(input.getDatasourceId());
            Map<String, Object> paramMap = datasourceResp.getParamMap();
            Assert.isTrue(MapUtil.isNotEmpty(paramMap), "数据源参数为空");
            String driverClass = ConnectionUtil.getDriverClass(input.getType());
            if (DatasourceConnectionPlatformEnum.ORACLE.code().equals(input.getType())) {
                driverClass = String.format("Java::%s", driverClass);
            }
            jdbcInputParam.setJdbcDriverClass(driverClass);
            jdbcInputParam.setJdbcConnectionString(ConnectionUtil.buildJdbcUrl(datasourceResp.getPlatform(),
                paramMap.get("address").toString(), input.getDatabase()));
            jdbcInputParam.setJdbcUser(paramMap.get("username").toString());
            jdbcInputParam.setJdbcPassword(paramMap.get("password").toString());
        }
        jdbcInputParam.setCronSchedule(covertCron(input.getCron()));

        String sqlStatement;
        if (StrUtil.isNotBlank(input.getTrackingColumn()) && "incremental".equals(input.getSyncMode())) {
            sqlStatement = String.format("select * from (%s) tmp_t", StrUtil.removeSuffix(input.getSql(), ";"));
        } else {
            sqlStatement = StrUtil.removeSuffix(input.getSql(), ";");
        }
        jdbcInputParam.setSqlStatement(sqlStatement);
        jdbcInputParam.setTrackingColumnType(input.getTrackingColumnType());
        jdbcInputParam.setTrackingColumn(input.getTrackingColumn());
        jdbcInputParam.setSyncMode(input.getSyncMode());
        jdbcInputParam.setMode("advanced".equals(input.getSyncMode()) ? 1 : 0);

        Map<String, String> fieldMap = new HashMap<>(4);
        fieldMap.put("@topic", convertToCellTaskInputParam.getTopicName());
        fieldMap.put("@tags", String.join(",", Optional.ofNullable(this.input.getTagList()).orElse(CollUtil.newArrayList())));
        fieldMap.put("@ip", "{{.agent_ip}}");
        if (StrUtil.isNotBlank(input.getTrackingColumn())) {
            fieldMap.put("@@id", convertToCellTaskInputParam.getTaskId().toString() + "-%{" + input.getTrackingColumn() + "}");
        }
        jdbcInputParam.setFields(fieldMap);

        cellTaskInput.setInputParams(jdbcInputParam);
        return JsonUtil.encode(CollUtil.newArrayList(cellTaskInput));
    }

    @Override
    public AbstractSetting fromCellTaskInputStr(String inputStr, ConvertToAbstractSettingParam convertToAbstractSettingParam) {
        List<CellTaskInput<CellTaskInputParam.JdbcInput>> cellTaskInputList = JsonUtil.decode(inputStr,
            new TypeReference<List<CellTaskInput<CellTaskInputParam.JdbcInput>>>() {
            });
        CellTaskInput<CellTaskInputParam.JdbcInput> cellTaskInput = cellTaskInputList.get(0);
        CellTaskInputParam.JdbcInput inputParams = cellTaskInput.getInputParams();

        JdbcSetting.JdbcInput jdbcInput = new JdbcSetting.JdbcInput();

        JdbcSetting jdbcSetting = JsonUtil.decode(convertToAbstractSettingParam.getTbIngestionJobTask().getSetting(),
            new TypeReference<JdbcSetting>() {
            });

        jdbcInput.setType(jdbcSetting.getInput().getType());
        if (!CUSTOM.equals(jdbcSetting.getInput().getType())) {
            jdbcInput.setDatasourceId(jdbcSetting.getInput().getDatasourceId());
            jdbcInput.setDatabase(jdbcSetting.getInput().getDatabase());
            DatasourceService datasourceService = ContextHolder.getBean(DatasourceService.class);
            Optional<DatasourceResp> datasourceRespOptional = Optional.ofNullable(datasourceService.get(jdbcInput.getDatasourceId()));
            jdbcInput.setDatasourceName(datasourceRespOptional.map(DatasourceResp::getName).orElse(null));
            jdbcInput.setDatasourceAddress(datasourceRespOptional.map(DatasourceResp::getParamMap)
                .map(it -> (String) it.get("address")).orElse(null));
        } else {
            jdbcInput.setJdbcUrl(inputParams.getJdbcConnectionString());
            jdbcInput.setJdbcDriver(inputParams.getJdbcDriverClass());
            jdbcInput.setJdbcJar(inputParams.getJdbcDriverLibrary());
            jdbcInput.setUserName(inputParams.getJdbcUser());
            jdbcInput.setPassword(inputParams.getJdbcPassword());
        }
        jdbcInput.setPreviewSql(jdbcSetting.getInput().getPreviewSql());
        jdbcInput.setSyncMode(inputParams.getSyncMode());
        jdbcInput.setTrackingColumnType(inputParams.getTrackingColumnType());
        jdbcInput.setTrackingColumn(inputParams.getTrackingColumn());
        jdbcInput.setSql(jdbcSetting.getInput().getSql());
        jdbcInput.setCron(jdbcSetting.getInput().getCron());
        jdbcInput.setTagList(Arrays.stream(Optional.ofNullable(inputParams.getFields()).orElse(MapUtil.newHashMap())
                .getOrDefault("@tags", "").split(","))
            .collect(Collectors.toList()));
        jdbcInput.setHostname(jdbcSetting.getInput().getHostname());
        jdbcInput.setIp(jdbcSetting.getInput().getIp());
        jdbcInput.setOsAlias(jdbcSetting.getInput().getOsAlias());

        this.setInput(jdbcInput);
        return this;
    }

    @Override
    public JdbcInput getInput() {
        return input;
    }

    public void setInput(JdbcInput input) {
        this.input = input;
    }


    private String covertCron(String cron) {
        try {
            CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);
            CronParser parser = new CronParser(cronDefinition);
            Cron quartzCron = parser.parse(cron);
            Map<CronFieldName, CronField> cronFieldNameCronFieldMap = quartzCron.retrieveFieldsAsMap();

            return String.format("%s %s %s %s %s",
                covertFieldExpression(cronFieldNameCronFieldMap.get(CronFieldName.MINUTE)),
                covertFieldExpression(cronFieldNameCronFieldMap.get(CronFieldName.HOUR)),
                covertFieldExpression(cronFieldNameCronFieldMap.get(CronFieldName.DAY_OF_MONTH)),
                covertFieldExpression(cronFieldNameCronFieldMap.get(CronFieldName.MONTH)),
                covertFieldExpression(cronFieldNameCronFieldMap.get(CronFieldName.DAY_OF_WEEK)));
        } catch (Exception e) {
            throw new BizException(ResponseCode.FAILED.getCode(),
                String.format("cron表达式:%s转化为logstash-cron表达式异常", cron), e);
        }
    }

    private String covertFieldExpression(CronField cronField) {
        if (Objects.isNull(cronField)) {
            return "";
        }
        FieldExpression fieldExpression = cronField.getExpression();
        if (fieldExpression instanceof Every) {
            return "*/" + ((Every) fieldExpression).getPeriod().getValue();
        } else if (fieldExpression instanceof QuestionMark) {
            return "*";
        } else {
            return fieldExpression.asString();
        }
    }

    public static class JdbcInput extends BaseInput {

        @Schema(description = "系统")
        private String os;

        @Schema(description = "系统版本")
        private String version;

        /**
         * 采集器标签
         */
        @Schema(description = "采集器标签")
        private List<CellAgentListQueryResp.Group> groupList;

        @NotBlank(message = "类型不能为空")
        @Schema(description = "类型, 自定义:CUSTOM")
        private String type;

        @JsonSerialize(using = LongStringSerializer.class)
        @Schema(description = "数据源ID")
        private Long datasourceId;

        @Schema(description = "数据源名称")
        private String datasourceName;

        @Schema(description = "地址")
        private String datasourceAddress;

        @Schema(description = "数据库名称")
        private String database;

        @Schema(description = "JDBC URL")
        private String jdbcUrl;

        @Schema(description = "JDBC Driver")
        private String jdbcDriver;

        @Schema(description = "JDBC JAR")
        private String jdbcJar;

        @Schema(description = "用户名")
        private String userName;

        @Schema(description = "密码")
        private String password;

        @Schema(description = "预览")
        private String previewSql;

//        private String columnsCharset;

        /**
         * advanced,incremental,whole
         */
        @NotBlank(message = "采集方式不能为空")
        @Schema(description = "采集方式, 全量:whole 增量:incremental 高级:advanced")
        private String syncMode;

        @Schema(description = "同步关键字类型, 数值:numeric, 时间:timestamp")
        private String trackingColumnType;

        @Schema(description = "同步关键字")
        private String trackingColumn;

        @Schema(description = "自定义采集语句")
        @NotBlank(message = "自定义采集语句不能为空")
        private String sql;

        @Schema(description = "采集周期")
        @NotBlank(message = "采集周期不能为空")
        private String cron;

        @Schema(description = "标签列表")
        private List<String> tagList;

        public String getOs() {
            return os;
        }

        public void setOs(String os) {
            this.os = os;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public List<CellAgentListQueryResp.Group> getGroupList() {
            return groupList;
        }

        public void setGroupList(List<CellAgentListQueryResp.Group> groupList) {
            this.groupList = groupList;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Long getDatasourceId() {
            return datasourceId;
        }

        public void setDatasourceId(Long datasourceId) {
            this.datasourceId = datasourceId;
        }

        public String getDatasourceName() {
            return datasourceName;
        }

        public void setDatasourceName(String datasourceName) {
            this.datasourceName = datasourceName;
        }

        public String getDatasourceAddress() {
            return datasourceAddress;
        }

        public void setDatasourceAddress(String datasourceAddress) {
            this.datasourceAddress = datasourceAddress;
        }

        public String getDatabase() {
            return database;
        }

        public void setDatabase(String database) {
            this.database = database;
        }

        public String getJdbcUrl() {
            return jdbcUrl;
        }

        public void setJdbcUrl(String jdbcUrl) {
            this.jdbcUrl = jdbcUrl;
        }

        public String getJdbcDriver() {
            return jdbcDriver;
        }

        public void setJdbcDriver(String jdbcDriver) {
            this.jdbcDriver = jdbcDriver;
        }

        public String getJdbcJar() {
            return jdbcJar;
        }

        public void setJdbcJar(String jdbcJar) {
            this.jdbcJar = jdbcJar;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getPreviewSql() {
            return previewSql;
        }

        public void setPreviewSql(String previewSql) {
            this.previewSql = previewSql;
        }

        public String getSyncMode() {
            return syncMode;
        }

        public void setSyncMode(String syncMode) {
            this.syncMode = syncMode;
        }

        public String getTrackingColumnType() {
            return trackingColumnType;
        }

        public void setTrackingColumnType(String trackingColumnType) {
            this.trackingColumnType = trackingColumnType;
        }

        public String getTrackingColumn() {
            return trackingColumn;
        }

        public void setTrackingColumn(String trackingColumn) {
            this.trackingColumn = trackingColumn;
        }

        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        public String getCron() {
            return cron;
        }

        public void setCron(String cron) {
            this.cron = cron;
        }

        public List<String> getTagList() {
            return tagList;
        }

        public void setTagList(List<String> tagList) {
            this.tagList = tagList;
        }

        @Override
        public Integer getBandwidthweight() {
            return 1;
        }

        @Override
        public Integer getBatch() {
            return 500;
        }
    }

}
