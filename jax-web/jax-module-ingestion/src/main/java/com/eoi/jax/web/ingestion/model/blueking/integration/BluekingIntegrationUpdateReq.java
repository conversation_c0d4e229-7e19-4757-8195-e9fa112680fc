package com.eoi.jax.web.ingestion.model.blueking.integration;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.ingestion.model.blueking.mapping.BluekingIntegrationMapping;
import com.eoi.jax.web.repository.entity.TbBluekingIntegration;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/4
 */
public class BluekingIntegrationUpdateReq implements IUpdateModel<TbBluekingIntegration> {

    /**
     * 主键
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 业务流程id
     */
    @NotNull(message = "业务流程id不能为空")
    @Schema(description = "业务流程id")
    private Long businessFlowId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    @Schema(description = "任务名称")
    private String name;

    /**
     * 蓝鲸数据源id
     */
    @NotNull(message = "蓝鲸数据源id不能为空")
    @Schema(description = "蓝鲸数据源id")
    private Long bkDsId;

    @NotNull(message = "映射不能为空")
    @Schema(description = "映射")
    private List<BluekingIntegrationMapping> mappingList;

    @Override
    public TbBluekingIntegration toEntity(TbBluekingIntegration tbBluekingIntegration) {
        return IUpdateModel.super.toEntity(tbBluekingIntegration);
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBkDsId() {
        return bkDsId;
    }

    public void setBkDsId(Long bkDsId) {
        this.bkDsId = bkDsId;
    }


    public List<BluekingIntegrationMapping> getMappingList() {
        return mappingList;
    }

    public void setMappingList(List<BluekingIntegrationMapping> mappingList) {
        this.mappingList = mappingList;
    }
}
