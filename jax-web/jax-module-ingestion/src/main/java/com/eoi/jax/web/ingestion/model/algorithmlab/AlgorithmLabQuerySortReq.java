package com.eoi.jax.web.ingestion.model.algorithmlab;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbAlgorithmLab;

/**
 * <AUTHOR>
 * @date 2024/2/20
 */
public class AlgorithmLabQuerySortReq implements ISortReq<TbAlgorithmLab> {

    private String updateTime;

    @Override
    public QueryWrapper<TbAlgorithmLab> order(QueryWrapper<TbAlgorithmLab> wrapper) {
        wrapper.lambda()
            .orderBy(isOrder(updateTime), isAsc(updateTime), TbAlgorithmLab::getUpdateTime);
        return wrapper;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
