package com.eoi.jax.web.ingestion.provider.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.eoi.jax.manager.api.JobStartParam;
import com.eoi.jax.manager.api.JobStartResult;
import com.eoi.jax.manager.api.JobStopParam;
import com.eoi.jax.manager.api.JobStopResult;
import com.eoi.jax.manager.client.yarn.YarnAppReport;
import com.eoi.jax.manager.yarn.YarnJobGetParam;
import com.eoi.jax.manager.yarn.YarnJobGetResult;
import com.eoi.jax.manager.yarn.YarnJobStopParam;
import com.eoi.jax.manager.yarn.YarnManager;
import com.eoi.jax.web.core.client.yarn.YarnApplication;
import com.eoi.jax.web.core.client.yarn.YarnRestClient;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.PipelineTypeEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.exception.JaxException;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.ConfigLoader;
import com.eoi.jax.web.ingestion.enumrate.OpTypeEnum;
import com.eoi.jax.web.ingestion.model.cluster.Cluster;
import com.eoi.jax.web.ingestion.model.cluster.ClusterSettingFactory;
import com.eoi.jax.web.ingestion.model.cluster.YarnCluster;
import com.eoi.jax.web.ingestion.model.event.PipelineStatusUpdateEvent;
import com.eoi.jax.web.ingestion.model.opts.FlinkOpts;
import com.eoi.jax.web.ingestion.model.opts.IOptsSetting;
import com.eoi.jax.web.ingestion.model.opts.Opts;
import com.eoi.jax.web.ingestion.model.opts.SparkOpts;
import com.eoi.jax.web.ingestion.model.pipelineconf.BasePipelineDefine;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineJob;
import com.eoi.jax.web.ingestion.plugin.PipelinePlugin;
import com.eoi.jax.web.ingestion.provider.jar.FileUrl;
import com.eoi.jax.web.ingestion.provider.jar.JarFileUrlProvider;
import com.eoi.jax.web.ingestion.service.ClusterService;
import com.eoi.jax.web.ingestion.service.OptsService;
import com.eoi.jax.web.repository.entity.TbAlg;
import com.eoi.jax.web.repository.entity.TbJar;
import com.eoi.jax.web.repository.entity.TbJob;
import com.eoi.jax.web.repository.entity.TbPipeline;
import com.eoi.jax.web.repository.service.TbAlgService;
import com.eoi.jax.web.repository.service.TbJarService;
import com.eoi.jax.web.repository.service.TbJobService;
import com.eoi.jax.web.repository.service.TbPipelineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
public abstract class BasePipelineManager extends AbstractPipelineManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(BasePipelineManager.class);

    @Lazy
    @Autowired
    private PipelinePlugin pipelinePlugin;
    @Autowired
    private TbPipelineService tbPipelineService;
    @Autowired
    private TbJarService tbJarService;
    @Autowired
    private TbJobService tbJobService;
    @Autowired
    private TbAlgService tbAlgService;
    @Autowired
    private OptsService optsService;
    @Autowired
    private ClusterService clusterService;
    @Autowired
    private JarFileUrlProvider jarFileUrlProvider;
    @Autowired
    private JaxManagerProvider jaxManagerProvider;

    @Override
    public void beforeStart(JobStartParam param, PipelineCtx pipeline) {
        pipelinePlugin.beforeStart(param, pipeline);
    }

    @Override
    public void afterStart(JobStartResult result, PipelineCtx pipeline) {
        try {
            pipelinePlugin.afterStart(result, pipeline);
        } catch (Exception e) {
            LOGGER.error("pipeline {} {} after start failed",
                    pipeline.getPipeline().getId(),
                    pipeline.getPipeline().getPipelineName(),
                    e);
        }
    }

    @Override
    public void beforeStop(JobStopParam param, PipelineCtx pipeline) {
        pipelinePlugin.beforeStop(param, pipeline);
    }

    @Override
    public void afterStop(JobStopResult result, PipelineCtx pipeline) {
        try {
            pipelinePlugin.afterStop(result, pipeline);
        } catch (Exception e) {
            LOGGER.error("pipeline {} {} after stop failed",
                    pipeline.getPipeline().getId(),
                    pipeline.getPipeline().getPipelineName(),
                    e);
        }
    }

    @Override
    public void beforeDelete(JobStopParam param, PipelineCtx pipeline) {
        pipelinePlugin.beforeDelete(param, pipeline);
    }

    @Override
    public void afterDelete(JobStopResult result, PipelineCtx pipeline) {
        try {
            pipelinePlugin.afterDelete(result, pipeline);
        } catch (Exception e) {
            LOGGER.error("pipeline {} {} after delete failed",
                    pipeline.getPipeline().getId(),
                    pipeline.getPipeline().getPipelineName(),
                    e);
        }
    }

    public boolean updatePipelineStatus(TbPipeline status, TbPipeline entity) {
        if (StrUtil.equals(status.getPipelineStatus(), entity.getPipelineStatus())
                && StrUtil.equals(status.getInternalStatus(), entity.getInternalStatus())) {
            //状态未发生变化，不需要更新
            return false;
        }
        boolean updated = tbPipelineService.update(
                new LambdaUpdateWrapper<TbPipeline>()
                        .set(TbPipeline::getPipelineStatus, status.getPipelineStatus())
                        .set(TbPipeline::getInternalStatus, status.getInternalStatus())
                        .isNull(TbPipeline::getProcessing)
                        .eq(TbPipeline::getId, entity.getId())
                        .eq(TbPipeline::getPipelineStatus, entity.getPipelineStatus())
                        .eq(TbPipeline::getUpdateTime, entity.getUpdateTime())
        );
        if (updated) {
            LOGGER.info("update pipeline {} {} status from {} - {} to {} - {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    entity.getPipelineStatus(),
                    entity.getInternalStatus(),
                    status.getPipelineStatus(),
                    status.getInternalStatus()
            );
            // 发布作业状态更新事件
            ContextHolder.publishEvent(new PipelineStatusUpdateEvent()
                    .setPipelineId(entity.getId())
                    .setPipelineStatus(status.getPipelineStatus())
                    .setProcessId(entity.getProcessId())
                    .setProcessType(entity.getProcessType())
            );
        }
        return updated;
    }

    public String getTrackUrl(PipelineCtx pipeline) {
        try {
            YarnAppReport report = getYarnApp(pipeline);
            if (report == null) {
                LOGGER.warn("track url {} {} failed",
                        pipeline.getPipeline().getId(),
                        pipeline.getPipeline().getPipelineName());
                return null;
            }
            LOGGER.info("track url {} {} {}",
                    pipeline.getPipeline().getId(),
                    pipeline.getPipeline().getPipelineName(),
                    report.getTrackingUrl());
            return report.getTrackingUrl();
        } catch (Error e) {
            LOGGER.error("track url {} {} failed",
                    pipeline.getPipeline().getId(),
                    pipeline.getPipeline().getPipelineName(),
                    e);
            return null;
        }
    }

    @Override
    public YarnAppReport getYarnApp(PipelineCtx pipeline) {
        try {
            YarnApplication yarnApplication = getYarnAppByHttp(pipeline);
            YarnAppReport yarnAppReport = new YarnAppReport();
            yarnAppReport.setApplicationId(yarnApplication.getId());
            yarnAppReport.setName(yarnApplication.getName());
            yarnAppReport.setState(yarnApplication.getState());
            yarnAppReport.setTrackingUrl(yarnApplication.getTrackingUrl());
            yarnAppReport.setQueue(yarnApplication.getQueue());
            yarnAppReport.setStartTime(yarnApplication.getStartedTime());
            yarnAppReport.setFinishTime(yarnApplication.getStartedTime());
            yarnAppReport.setFinalStatus(yarnApplication.getFinalStatus());
            return yarnAppReport;
        } catch (Exception ex) {
            LOGGER.debug("get yarn app info failed", ex);
        }
        YarnJobGetResult yarnJobGetResult = getYarnAppByManager(pipeline);
        return yarnJobGetResult.getReport();
    }

    public YarnApplication getYarnAppByHttp(PipelineCtx pipeline) {
        TbPipeline entity = pipeline.getPipeline();
        YarnCluster yarnCluster = (YarnCluster) pipeline.getCluster().getSetting();
        LOGGER.debug("get pipeline yarn app {} {} on cluster {} {}",
                entity.getId(),
                entity.getPipelineName(),
                pipeline.getCluster().getId(),
                pipeline.getCluster().getClusterName());
        YarnRestClient yarnRestClient = new YarnRestClient(yarnCluster.getYarnWebUrl());
        if (yarnCluster.isWebKrb5Enabled()) {
            yarnRestClient.withKrb5Auth(yarnCluster.getKeytab(), yarnCluster.getPrincipal());
        }
        return yarnRestClient.getApplication(pipeline.getPipeline().getYarnAppId());
    }

    public YarnJobGetResult getYarnAppByManager(PipelineCtx pipeline) {
        TbPipeline entity = pipeline.getPipeline();
        YarnCluster yarnCluster = (YarnCluster) pipeline.getCluster().getSetting();
        LOGGER.debug("get pipeline yarn app {} {} on cluster {} {}",
                entity.getId(),
                entity.getPipelineName(),
                pipeline.getCluster().getId(),
                pipeline.getCluster().getClusterName());
        YarnJobGetParam param = new YarnJobGetParam()
                .setApplicationId(entity.getYarnAppId())
                .setTrackingUrl(entity.getTrackUrl())
                .setTimeOutMs(5000)
                .setPrincipal(yarnCluster.getPrincipal())
                .setKeytab(yarnCluster.getKeytab());
        param.setHadoopConfDir(yarnCluster.computeHadoopConfPath());
        param.setUuid(new OpUUID()
                .setObjId(entity.getId())
                .setOpTime(System.currentTimeMillis())
                .setOpType(OpTypeEnum.GET));
        String paramJson = JsonUtil.encode(param);
        LOGGER.debug("get pipeline yarn app {} {} param {}", entity.getId(), entity.getPipelineName(), paramJson);
        YarnManager jaxManager = jaxManagerProvider.yarnManager(pipeline);
        YarnJobGetResult result = (YarnJobGetResult) jaxManager.get(param);
        String resultJson = JsonUtil.encode(result);
        LOGGER.debug("get pipeline yarn app {} {} result {}", entity.getId(), entity.getPipelineName(), resultJson);
        return result;
    }

    public YarnJobStopParam genYarnStopParam(PipelineCtx pipeline) {
        YarnCluster yarnCluster = (YarnCluster) pipeline.getCluster().getSetting();
        YarnJobStopParam param = new YarnJobStopParam()
                .setApplicationId(pipeline.getPipeline().getYarnAppId())
                .setTrackingUrl(pipeline.getPipeline().getTrackUrl())
                .setTimeOutMs(yarnCluster.getTimeoutMs().intValue())
                .setPrincipal(yarnCluster.getPrincipal())
                .setKeytab(yarnCluster.getKeytab());
        param.setHadoopConfDir(yarnCluster.computeHadoopConfPath());
        return param;
    }

    public FileUrl genDefineFile(BasePipelineDefine define) {
        long now = System.currentTimeMillis();
        String fileName = define.getPipelineName() + "-" + now + ".pipeline";
        String filePath = Common.pathsJoin(ConfigLoader.load().getJax().getWork(), fileName);
        try {
            FileUtil.writeString(JsonUtil.encode(define), filePath, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new JaxException(e);
        }
        return jarFileUrlProvider.genWorkUrl(filePath);
    }

    public List<FileUrl> genLoadUrls(PipelineCtx pipeline) {
        String jarLib;
        if (pipeline.getOpts().getSetting() instanceof FlinkOpts) {
            jarLib = ((FlinkOpts) pipeline.getOpts().getSetting()).computeJobLibPath();
        } else {
            jarLib = ((SparkOpts) pipeline.getOpts().getSetting()).computeJobLibPath();
        }
        List<FileUrl> urls = new ArrayList<>();
        urls.addAll(jarFileUrlProvider.getJobLibUrls(jarLib));
        urls.addAll(jarFileUrlProvider.getJobJarUrls(pipeline.getJars()));
        return urls;
    }

    public List<FileUrl> genJobPyFileUrls(PipelineCtx pipeline) {
        List<String> pyFiles = null;
        if (pipeline.getOpts().getSetting() instanceof SparkOpts) {
            SparkOpts sparkOpts = (SparkOpts) pipeline.getOpts().getSetting();
            pyFiles = sparkOpts.getPyFiles();
        }
        List<FileUrl> urls = new ArrayList<>();
        urls.addAll(jarFileUrlProvider.genPythonLibUrl(pyFiles));
        urls.addAll(jarFileUrlProvider.getJobPyFileUrls(pipeline.getJars()));
        return urls;
    }

    @Override
    public PipelineCtx pipelineCtx(TbPipeline entity) {
        PipelineCtx pipeline = new PipelineCtx(entity);
        Cluster cluster = clusterService.getCluster(entity.getClusterId());
        pipeline.setCluster(cluster);
        PipelineConfig pipelineConfig = pipeline.getPipelineConfig();
        pipelineConfig = ClusterSettingFactory.effectEnvVar(pipelineConfig, cluster.getSetting());
        pipeline.setPipelineConfig(pipelineConfig);
        Opts opts;
        if (entity.getOptsId() == null) {
            if (PipelineTypeEnum.isFlink(entity.getPipelineType())) {
                opts = optsService.getOpts(cluster.getFlinkOptsId());
            } else {
                opts = optsService.getOpts(cluster.getSparkOptsId());
            }
        } else {
            opts = optsService.getOpts(entity.getOptsId());
        }
        IOptsSetting optsSetting = opts.getSetting();
        optsSetting = ClusterSettingFactory.effectEnvVar(optsSetting, cluster.getSetting());
        opts.setSetting(optsSetting);
        pipeline.setOpts(opts);
        List<PipelineJar> jars = pipelineJars(pipeline.getPipelineConfig());
        pipeline.setJars(jars);
        return pipeline;
    }

    @Override
    public List<PipelineJar> pipelineJars(PipelineConfig pipelineConfig) {
        List<PipelineJar> list = new ArrayList<>();
        if (pipelineConfig == null) {
            return list;
        }
        PipelineConfigOpts pipelineConfigOpts = PipelineConfigOpts.fromMap(pipelineConfig.getOpts());
        Set<Long> jarIds = new HashSet<>();
        // pipeline jobs
        Set<String> jobNames = pipelineConfig.getJobs().stream()
                .map(PipelineJob::getJobName)
                .collect(Collectors.toSet());
        // ext jobs
        jobNames.addAll(pipelineConfigOpts.getExtJobs());
        // job jar id list
        if (CollUtil.isNotEmpty(jobNames)) {
            List<TbJob> jobJars = getPipelineJobJarIds(jobNames);
            jarIds.addAll(jobJars.stream().map(TbJob::getJarId).collect(Collectors.toList()));
        }
        // alg jar id list
        if (CollUtil.isNotEmpty(pipelineConfigOpts.getExtAlgs())) {
            Set<String> algClasses = new HashSet<>(pipelineConfigOpts.getExtAlgs());
            List<TbAlg> algJars = getPipelineAlgJarIds(algClasses);
            jarIds.addAll(algJars.stream().map(TbAlg::getJarId).collect(Collectors.toList()));
        }
        // ext jar id list
        if (CollUtil.isNotEmpty(pipelineConfigOpts.getExtJars())) {
            Set<String> jarNames = new HashSet<>(pipelineConfigOpts.getExtJars());
            List<TbJar> extJars = getPipelineExtJarIds(jarNames);
            jarIds.addAll(extJars.stream().map(TbJar::getId).collect(Collectors.toList()));
        }
        if (CollUtil.isEmpty(jarIds)) {
            return list;
        }
        List<TbJar> jars = tbJarService.list(new LambdaQueryWrapper<TbJar>()
                .in(TbJar::getId, jarIds));
        Map<Long, Cluster> clusters = new HashMap<>(4);
        for (TbJar jar : jars) {
            Cluster cluster = null;
            Long clusterId = jar.getClusterId();
            if (clusterId != null) {
                cluster = clusters.computeIfAbsent(clusterId, id -> clusterService.getCluster(id));
            }
            PipelineJar pipelineJar = new PipelineJar(jar, cluster);
            list.add(pipelineJar);
        }
        return list;
    }

    private List<TbJob> getPipelineJobJarIds(Set<String> jobNames) {
        List<TbJob> jobJars = tbJobService.list(new LambdaQueryWrapper<TbJob>()
                .select(TbJob::getJobName, TbJob::getJarId)
                .in(TbJob::getJobName, jobNames));
        for (String jobName : jobNames) {
            boolean found = jobJars.stream().anyMatch(i -> jobName.equals(i.getJobName()));
            if (!found) {
                throw new BizException(ResponseCode.PIPELINE_INVALID_CONF.getCode(),
                        ResponseCode.PIPELINE_INVALID_CONF.getMessage() + ":job " + jobName + " not found");
            }
        }
        return jobJars;
    }

    private List<TbAlg> getPipelineAlgJarIds(Set<String> algClasses) {
        List<TbAlg> algJars = tbAlgService.list(new LambdaQueryWrapper<TbAlg>()
                .select(TbAlg::getJarId, TbAlg::getAlgClass)
                .in(TbAlg::getAlgClass, algClasses));
        for (String algClass : algClasses) {
            boolean found = algJars.stream().anyMatch(i -> algClass.equals(i.getAlgClass()));
            if (!found) {
                throw new JaxException("alg " + algClass + " not found");
            }
        }
        return algJars;
    }

    private List<TbJar> getPipelineExtJarIds(Set<String> jarNames) {
        List<TbJar> extJars = tbJarService.list(new LambdaQueryWrapper<TbJar>()
                .select(TbJar::getId, TbJar::getJarName)
                .in(TbJar::getJarName, jarNames));
        for (String jarName : jarNames) {
            boolean found = extJars.stream().anyMatch(i -> jarName.equals(i.getJarName()));
            if (!found) {
                throw new JaxException("jar " + jarName + " not found");
            }
        }
        return extJars;
    }
}
