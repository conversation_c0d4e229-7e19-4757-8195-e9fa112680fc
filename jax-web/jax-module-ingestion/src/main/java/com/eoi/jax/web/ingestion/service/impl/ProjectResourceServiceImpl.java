package com.eoi.jax.web.ingestion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.ApplicationStatusEnum;
import com.eoi.jax.web.core.common.enumrate.PipelineStatusEnum;
import com.eoi.jax.web.core.common.enumrate.PipelineTypeEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.security.UserContext;
import com.eoi.jax.web.ingestion.enumrate.ClusterTypeEnum;
import com.eoi.jax.web.ingestion.model.application.deploy.ApplicationDeployResp;
import com.eoi.jax.web.ingestion.model.cluster.ClusterResp;
import com.eoi.jax.web.ingestion.model.flinkyarnsession.FlinkYarnSessionResp;
import com.eoi.jax.web.ingestion.model.opts.FlinkOpts;
import com.eoi.jax.web.ingestion.model.opts.MarayarnOpts;
import com.eoi.jax.web.ingestion.model.opts.OptsResp;
import com.eoi.jax.web.ingestion.model.opts.SparkOpts;
import com.eoi.jax.web.ingestion.model.pipeline.PipelineFilterReq;
import com.eoi.jax.web.ingestion.model.pipeline.PipelineQueryReq;
import com.eoi.jax.web.ingestion.model.pipeline.PipelineResp;
import com.eoi.jax.web.ingestion.model.pipeline.PipelineSortReq;
import com.eoi.jax.web.ingestion.model.process.ProcessResp;
import com.eoi.jax.web.ingestion.model.projectresource.*;
import com.eoi.jax.web.ingestion.model.storagecluster.StorageClusterResp;
import com.eoi.jax.web.ingestion.model.storagees.StorageEsResp;
import com.eoi.jax.web.ingestion.model.storagehive.StorageHiveResp;
import com.eoi.jax.web.ingestion.service.FlinkYarnSessionService;
import com.eoi.jax.web.ingestion.service.ProjectResourceService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.service.*;
import com.xxl.job.core.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.executor.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zsc
 * @create 2024/7/30 17:32
 */
@SuppressWarnings("all")
@Service
public class ProjectResourceServiceImpl implements ProjectResourceService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectResourceServiceImpl.class);
    @Resource
    private TbApplicationDeployService tbApplicationDeployService;

    @Resource
    private TbPipelineService tbPipelineService;

    @Resource
    private TbStorageEsService tbStorageEsService;

    @Resource
    private TbStorageHiveService tbStorageHiveService;

    @Resource
    private FlinkYarnSessionService flinkYarnSessionService;


    @Resource
    private TbOptsService tbOptsService;

    @Resource
    private TbClusterService tbClusterService;

    @Resource
    private TDsTaskDefinitionService taskDefinitionService;

    @Resource
    private TDsProcessTaskRelationService processTaskRelationService;

    @Resource
    private TDsProcessDefinitionService processDefinitionService;

    @Resource
    private TDsSchedulesService tdSchedulesService;


    @Resource
    private TbProcessService tbProcessService;

    @Resource
    private TbProjectService tbProjectService;

    @Override
    public boolean checkNeedCalResource() {
        if (!ContextHolder.getProjectAuthorityEnable()) {
            return false;
        }
        if (ContextHolder.getCurrentProjectId() != null) {
            TbProject project = tbProjectService.getById(ContextHolder.getCurrentProjectId());
            if (project != null) {
                return project.getEnableResourceLimit().intValue() == 1;
            }
        }
        return false;
    }

    @Override
    public ProjectResourceResp getProjectResource(ProjectResourceReq req) {


        Long projectId = req.getProjectId();
        boolean startCheck = req.getStartCheck();
        Long oldProjectId = ContextHolder.getCurrentProjectId();
        UserContext userContext = ContextHolder.getUserContext();
        if (userContext == null) {
            userContext = new UserContext();
            // 本地测试放开 userContext.setProjectAuthorityEnable(true);
            ContextHolder.setUserContext(userContext);
        }
        if (projectId != null) {
            userContext.setCurrentProjectId(projectId);
        }
        Long currentProjectId = ContextHolder.getCurrentProjectId();
        try {
            ProjectResourceResp projectResourceResp = new ProjectResourceResp();
            if (currentProjectId != null) {
                TbProject project = tbProjectService.getById(currentProjectId);
                if (project != null) {
                    projectResourceResp.setProjectName(project.getName());
                    if (startCheck && project.getEnableResourceLimit().intValue() != 1) {
                        return projectResourceResp;
                    }
                    projectResourceResp.setProjectMaxCpuCount(project.getCpu() == null ? -1 : project.getCpu());
                    projectResourceResp.setProjectMaxMemCount(project.getMemory() == null ? -1 : project.getMemory());
                }
            }

            List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
            List<ClusterResp> clusters = tbClusterService.list().stream()
                    .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
            Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
            Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));

            // 弹性作业
            countApplicationResource(clusterMap, optsMap, projectResourceResp, req.getApplicationId());

            // 实时作业
            List<Long> runningSparkIds = new ArrayList<>();
            List<PipelineResourceResp> pipelineResourceResps = countPipelineResouce(clusterMap, optsMap, req.getProcessId(), runningSparkIds);
            int pipelineCpuCount = 0;
            int pipelineMemCount = 0;
            for (PipelineResourceResp pipelineResourceResp : pipelineResourceResps) {
                pipelineCpuCount += pipelineResourceResp.getCpu();
                pipelineMemCount += pipelineResourceResp.getMemory();
            }
            projectResourceResp.setPipelineCpuCount(pipelineCpuCount);
            projectResourceResp.setPipelineMemCount(pipelineMemCount);

            // 离线作业
            List<OfflineSparkProcessResp> offlineSparkProcessResps =
                    countOfflineSparkResource(clusterMap, optsMap, null, req.getProcessDefinitionId(), runningSparkIds);
            int offlinePipelineCpuCount = 0;
            int offlinePipelineMemCount = 0;
            for (OfflineSparkProcessResp dol : offlineSparkProcessResps) {
                offlinePipelineCpuCount += dol.getCpu();
                offlinePipelineMemCount += dol.getMemory();
            }
            projectResourceResp.setOfflineSparkPipelineCpuCount(offlinePipelineCpuCount);
            projectResourceResp.setOfflineSparkPipelineMemCount(offlinePipelineMemCount);
            projectResourceResp.setOfflineSparkProcessRespList(offlineSparkProcessResps);

            return projectResourceResp;
        } catch (Exception e) {
            logger.error("获取项目资源失败", e);
            throw new BizException(ResponseCode.FAILED.getCode(), "获取项目资源失败");
        } finally {
            if (projectId != null) {
                userContext.setCurrentProjectId(oldProjectId);
            }
        }
    }

    @Override
    public ApplicationResourceResp getApplicationResource(ApplicationDeployResp app) {
        List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
        List<ClusterResp> clusters = tbClusterService.list().stream()
                .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
        Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
        Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));
        return calApplication(clusterMap, optsMap, app);
    }

    @Override
    public ApplicationResourceResp getStorageCkClusterResource(StorageClusterResp app) {
        List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
        List<ClusterResp> clusters = tbClusterService.list().stream()
                .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
        Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
        Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));
        return calStorageCkCluster(clusterMap, optsMap, app);
    }

    @Override
    public StorageEsResourceResp getStorageEsResource(StorageEsResp p) {
        List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
        List<ClusterResp> clusters = tbClusterService.list().stream()
                .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
        Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
        Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));
        return calStorageEs(clusterMap, optsMap, p);
    }

    @Override
    public StorageHiveResourceResp getStorageHiveResource(StorageHiveResp p) {
        List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
        List<ClusterResp> clusters = tbClusterService.list().stream()
                .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
        Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
        Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));
        return calStorageHive(clusterMap, optsMap, p);
    }

    @Override
    public OfflineSparkProcessResp getOfflineSparkProcessResource(Long processDefinitionId) {
        if (processDefinitionId == null) {
            throw new RuntimeException("processDefinitionId不能为空");
        }
        List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
        List<ClusterResp> clusters = tbClusterService.list().stream()
                .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
        Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
        Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));

        List<Long> runningSparkIds = new ArrayList<>();
        countPipelineResouce(clusterMap, optsMap, null, runningSparkIds);
        List<OfflineSparkProcessResp> offlineSparkProcessResps = countOfflineSparkResource(clusterMap, optsMap, processDefinitionId, null
                , runningSparkIds);

        return offlineSparkProcessResps.size() > 0 ? offlineSparkProcessResps.get(0) : new OfflineSparkProcessResp();
    }

    @Override
    public PipelineResourceResp getPipelineResource(PipelineResp p) {
        List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
        List<ClusterResp> clusters = tbClusterService.list().stream()
                .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
        Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
        Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));
        return calPiplineResource(clusterMap, optsMap, p);
    }

    @Override
    public PipelineResourceResp getProcessResource(ProcessResp p) {
        List<OptsResp> opts = tbOptsService.list().stream().map(x -> new OptsResp().fromEntity(x)).collect(Collectors.toList());
        List<ClusterResp> clusters = tbClusterService.list().stream()
                .map(x -> new ClusterResp().fromEntity(x)).collect(Collectors.toList());
        Map<Long, OptsResp> optsMap = opts.stream().collect(Collectors.toMap(OptsResp::getId, v -> v));
        Map<Long, ClusterResp> clusterMap = clusters.stream().collect(Collectors.toMap(ClusterResp::getId, v -> v));
        return calProcessResource(clusterMap, optsMap, p);
    }

    private StorageEsResourceResp calStorageEs(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                               StorageEsResp app) {
        Long optsId = app.getOptsId();
        if (optsId == null) {
            ClusterResp clusterResp = clusterMap.get(app.getClusterId());
            optsId = clusterResp.getFlinkOptsId();
        }
        OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());

        FlinkOpts optSetting = new FlinkOpts();
        if (optsResp != null) {
            optSetting = BeanUtil.mapToBean(optsResp.getSetting(), FlinkOpts.class, true);
        }
        FlinkOpts appSetting = BeanUtil.mapToBean(app.getCustomSetting(), FlinkOpts.class, true);
        long yarnSlots = appSetting.getYarnSlots() != null ? appSetting.getYarnSlots() :
                optSetting.getYarnSlots();
        long yarnTaskManagerMemory = StringUtils.isNotBlank(appSetting.getYarnTaskManagerMemory()) ?
                convertMemoryToMb(appSetting.getYarnTaskManagerMemory()) :
                convertMemoryToMb(optSetting.getYarnTaskManagerMemory());
        long yarnJobManagerMemory = StringUtils.isNotBlank(appSetting.getYarnJobManagerMemory()) ?
                convertMemoryToMb(appSetting.getYarnJobManagerMemory()) :
                convertMemoryToMb(optSetting.getYarnJobManagerMemory());
        long parallelism = appSetting.getParallelism() != null ?
                appSetting.getParallelism() :
                optSetting.getParallelism();
        // flink：
        //    flinkMemory =(并行度/slot 向上取整 ) * taskManager  + jobManager
        //    flinkCpu=1+taskManager
        long memCount = (long) (Math.ceil(1.0 * parallelism / yarnSlots) * yarnTaskManagerMemory + yarnJobManagerMemory);
        long cpuCount = 1 + (long) (Math.ceil(1.0 * parallelism / yarnSlots)) * yarnSlots;
        StorageEsResourceResp result = new StorageEsResourceResp(cpuCount, memCount);
        return result;
    }

    private StorageHiveResourceResp calStorageHive(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                                   StorageHiveResp app) {
        Long optsId = app.getOptsId();
        if (optsId == null) {
            ClusterResp clusterResp = clusterMap.get(app.getClusterId());
            optsId = clusterResp.getFlinkOptsId();
        }
        OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());

        FlinkOpts optSetting = new FlinkOpts();
        if (optsResp != null) {
            optSetting = BeanUtil.mapToBean(optsResp.getSetting(), FlinkOpts.class, true);
        }
        FlinkOpts appSetting = BeanUtil.mapToBean(app.getCustomSetting(), FlinkOpts.class, true);
        long yarnSlots = appSetting.getYarnSlots() != null ? appSetting.getYarnSlots() :
                optSetting.getYarnSlots();
        long yarnTaskManagerMemory = StringUtils.isNotBlank(appSetting.getYarnTaskManagerMemory()) ?
                convertMemoryToMb(appSetting.getYarnTaskManagerMemory()) :
                convertMemoryToMb(optSetting.getYarnTaskManagerMemory());
        long yarnJobManagerMemory = StringUtils.isNotBlank(appSetting.getYarnJobManagerMemory()) ?
                convertMemoryToMb(appSetting.getYarnJobManagerMemory()) :
                convertMemoryToMb(optSetting.getYarnJobManagerMemory());
        long parallelism = appSetting.getParallelism() != null ?
                appSetting.getParallelism() :
                optSetting.getParallelism();
        // flink：
        //    flinkMemory =(并行度/slot 向上取整 ) * taskManager  + jobManager
        //    flinkCpu=1+taskManager
        long memCount = (long) (Math.ceil(1.0 * parallelism / yarnSlots) * yarnTaskManagerMemory + yarnJobManagerMemory);
        long cpuCount = 1 + (long) (Math.ceil(1.0 * parallelism / yarnSlots)) * yarnSlots;
        StorageHiveResourceResp result = new StorageHiveResourceResp(cpuCount, memCount);
        return result;
    }

    private List<OfflineSparkProcessResp> countOfflineSparkResource(
            Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap, Long processDefinitionId,
            Long withoutProcessDefinitionId, List<Long> excludedPipelineIds
    ) {
        Map<Long, OfflineSparkProcessResp> sparkProcessRespMap = new HashMap<>(16);
        Set<Long> schedulePipelineIds = new HashSet<>();

        PipelineQueryReq req = new PipelineQueryReq();
        PipelineFilterReq filter = new PipelineFilterReq();
        PipelineSortReq sort = new PipelineSortReq();
        req.setPage(0);
        req.setSize(1000000);
        req.setFilter(filter);
        req.setSort(sort);
        filter.setPipelineType(Arrays.asList(PipelineTypeEnum.BATCH.code()));
        IPage<TbPipeline> pipelinePage = tbPipelineService.selectCustomPage(req.page(), req.query(),
                Optional.ofNullable(req.getFilter())
                        .map(it -> CollUtil.isEmpty(it.getTagIdList()) ? null : it.getTagIdList()).orElse(null));
        List<TbPipeline> tbPipelineList = pipelinePage.getRecords();
        List<PipelineResp> pipelineResps = tbPipelineList.stream().filter(x -> {
            ClusterResp clusterResp = clusterMap.get(x.getClusterId());
            if (ClusterTypeEnum.FLINK_STANDALONE.code().equals(clusterResp.getClusterType())
                    || ClusterTypeEnum.SPARK_STANDALONE.code().equals(clusterResp.getClusterType())) {
                return false;
            }
            return x.getProjectAuthIsOwner() != null && x.getProjectAuthIsOwner();
        }).map(it -> {
            return new PipelineResp().fromEntity(it);
        }).collect(Collectors.toList());

        // 查找tb_process未发布的作业
        List<TbProcess> tbProcessList = tbProcessService.selectListWithProjectAuth(new LambdaQueryWrapper<TbProcess>()
                .eq(TbProcess::getPipelineType, PipelineTypeEnum.BATCH.code())
        );
        Iterator it = tbProcessList.iterator();
        while (it.hasNext()) {
            TbProcess tbProcess = (TbProcess) it.next();
            if (tbProcess.getPipelineId() != null) {
                it.remove();
                continue;
            }
            ClusterResp clusterResp = clusterMap.get(tbProcess.getClusterId());
            if (ClusterTypeEnum.FLINK_STANDALONE.code().equals(clusterResp.getClusterType())
                    || ClusterTypeEnum.SPARK_STANDALONE.code().equals(clusterResp.getClusterType())) {
                it.remove();
            }
        }

        if ((pipelineResps != null && !pipelineResps.isEmpty())
                || (tbProcessList != null && !tbProcessList.isEmpty())) {
            // 已发布任务
            List<TDsProcessDefinition> processDefinitions = new ArrayList<>();
            if (processDefinitionId == null) {
                processDefinitions = processDefinitionService.selectListWithProjectAuth(new LambdaQueryWrapper<TDsProcessDefinition>()
                        .eq(TDsProcessDefinition::getReleaseState, 1));
            } else {
                processDefinitions = processDefinitionService.list(new LambdaQueryWrapper<TDsProcessDefinition>()
                        .eq(TDsProcessDefinition::getId, processDefinitionId));
            }

            List<Long> processDefIds = processDefinitions.stream().filter(x -> {
                        if (withoutProcessDefinitionId != null && x.getId().longValue() == withoutProcessDefinitionId) {
                            return false;
                        }
                        return true;
                    })
                    .map(TDsProcessDefinition::getCode).collect(Collectors.toList());
            Map<Long, TDsProcessDefinition> processDefinitionMap = processDefinitions.stream()
                    .collect(Collectors.toMap(TDsProcessDefinition::getCode, v -> v));
            if (processDefIds != null && !processDefIds.isEmpty()) {
                // cron
                List<TDsSchedules> schedules = tdSchedulesService.list(new LambdaQueryWrapper<TDsSchedules>()
                        .in(TDsSchedules::getProcessDefinitionCode, processDefIds));
                Map<Long, TDsSchedules> scheduleMap = schedules.stream()
                        .collect(Collectors.toMap(TDsSchedules::getProcessDefinitionCode, v -> v));

                List<TDsProcessTaskRelation> processTaskRelations = processTaskRelationService
                        .list(new LambdaQueryWrapper<TDsProcessTaskRelation>()
                                .in(TDsProcessTaskRelation::getProcessDefinitionCode, processDefIds));

                Map<Long, Long> processTaskRelationMap = new LinkedHashMap<>();
                for (TDsProcessTaskRelation v : processTaskRelations) {
                    processTaskRelationMap.put(v.getPostTaskCode(), v.getProcessDefinitionCode());
                    processTaskRelationMap.put(v.getPreTaskCode(), v.getProcessDefinitionCode());
                }

                List<TDsTaskDefinition> taskDefinitions = taskDefinitionService.list(new LambdaQueryWrapper<TDsTaskDefinition>()
                        .eq(TDsTaskDefinition::getTaskType, "SPARK_PIPELINE")
                        .in(TDsTaskDefinition::getCode, processTaskRelationMap.keySet())
                );
                // 所有spark任务
                if (taskDefinitions != null && !taskDefinitions.isEmpty()) {
                    // 拿到所有的离线作业
                    taskDefinitions.forEach(v -> {
                        Map<String, Object> taskParams = JSONUtil.toBean(v.getTaskParams(), Map.class);
                        Long processId = Long.parseLong((String) taskParams.get("processId"));
                        schedulePipelineIds.add(processId);

                        OfflineSparkProcessResp.Task task = new OfflineSparkProcessResp.Task();
                        task.setTaskCode(v.getCode());
                        task.setTaskType(v.getTaskType());
                        task.setTaskName(v.getName());
                        task.setProcessId(processId);

                        Long processDefinitionCode = processTaskRelationMap.get(v.getCode());
                        TDsProcessDefinition processDefinition = processDefinitionMap.get(processDefinitionCode);
                        OfflineSparkProcessResp offlineSparkProcessResp = sparkProcessRespMap.get(processDefinitionCode);
                        if (offlineSparkProcessResp == null) {
                            offlineSparkProcessResp = new OfflineSparkProcessResp();
                            offlineSparkProcessResp.setProcessCode(processDefinition.getCode());
                            offlineSparkProcessResp.setProcessName(processDefinition.getName());
                            String cron = scheduleMap.get(processDefinitionCode) != null ?
                                    scheduleMap.get(processDefinitionCode).getCrontab() : "";
                            if (StringUtils.isNotBlank(cron)) {
                                offlineSparkProcessResp.setCron(cron);
                                offlineSparkProcessResp.setCronToDoTime(getCronToDomain(cron));
                            }
                            sparkProcessRespMap.put(processDefinitionCode, offlineSparkProcessResp);
                        }
                        offlineSparkProcessResp.getTaskList().add(task);
                    });
                }

                // 计算子作业spark管线作业 cpu和内存
                List<PipelineResourceResp> schedulePipelineResps = new ArrayList<>();
                Map<Long, PipelineResourceResp> schedulePipelineRespMap = new HashMap<>();
                for (PipelineResp pipelineProcess : pipelineResps) {
                    if (!schedulePipelineIds.contains(pipelineProcess.getId())) {
                        continue;
                    }
                    if (excludedPipelineIds != null && excludedPipelineIds.contains(pipelineProcess.getId())) {
                        continue;
                    }
                    PipelineResourceResp schedulePipelineResp = calSparkPipeline(clusterMap, optsMap, pipelineProcess);
                    schedulePipelineResps.add(schedulePipelineResp);
                    schedulePipelineRespMap.put(pipelineProcess.getId(), schedulePipelineResp);
                }

                for (TbProcess pipelineProcess : tbProcessList) {
                    if (!schedulePipelineIds.contains(pipelineProcess.getId())) {
                        continue;
                    }
                    PipelineResourceResp schedulePipelineResp = calSparkPipeline(clusterMap, optsMap, pipelineProcess);
                    schedulePipelineResps.add(schedulePipelineResp);
                    schedulePipelineRespMap.put(pipelineProcess.getId(), schedulePipelineResp);
                }

                // 计算整个定时作业 cpu和内存
                for (OfflineSparkProcessResp offlineSparkProcessResp : sparkProcessRespMap.values()) {
                    offlineSparkProcessResp.getTaskList().forEach(task -> {
                        PipelineResourceResp pipeline = schedulePipelineRespMap.get(task.getProcessId());
                        if (pipeline != null) {
                            task.setCpu(pipeline.getCpu());
                            task.setMemory(pipeline.getMemory());
                            task.setTaskName(task.getTaskName() + "__" + pipeline.getName());
                        }
                    });
                }
            }
        }
        return sparkProcessRespMap.values().stream().collect(Collectors.toList());
    }


    private static PipelineResourceResp calSparkPipeline(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                                         PipelineResp pipelineProcess) {
        Map<String, Object> pipelineConfig = pipelineProcess.getPipelineConfig();
        if (pipelineConfig == null) {
            pipelineConfig = new HashMap<>(16);
        }
        SparkOpts appSetting = BeanUtil.toBean(pipelineConfig.getOrDefault("opts", new HashMap<>()),
                SparkOpts.class);
        Long optsId = pipelineProcess.getOptsId();
        if (optsId == null) {
            ClusterResp clusterResp = clusterMap.get(pipelineProcess.getClusterId());
            optsId = clusterResp.getSparkOptsId();
        }
        OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());
        SparkOpts optSetting = new SparkOpts();
        if (optsResp != null) {
            optSetting = BeanUtil.mapToBean(optsResp.getSetting(), SparkOpts.class, true);
        }
        long driverCore = appSetting.getDriverCores() != null ? appSetting.getDriverCores() :
                optSetting.getDriverCores();
        long driverMemory = StringUtils.isNotBlank(appSetting.getDriverMemory()) ?
                convertMemoryToMb(appSetting.getDriverMemory()) :
                convertMemoryToMb(optSetting.getDriverMemory());
        long executorCores = appSetting.getExecutorCores() != null ? appSetting.getExecutorCores() :
                optSetting.getExecutorCores();
        long executorMemory = StringUtils.isNotBlank(appSetting.getExecutorMemory()) ?
                convertMemoryToMb(appSetting.getExecutorMemory()) :
                convertMemoryToMb(optSetting.getExecutorMemory());
        long numExecutors = appSetting.getNumExecutors() != null ? appSetting.getNumExecutors() :
                optSetting.getNumExecutors();

        // spark：
        //    core=driverCore+ （execCore*实例数）
        //    memory=driverMemory + (execMemeory*实例数)
        long scheduleCpuCount = executorCores * numExecutors + driverCore;
        long scheduleMemCount = numExecutors * executorMemory + driverMemory;

        PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
        schedulePipelineResp.setCpu(scheduleCpuCount);
        schedulePipelineResp.setMemory(scheduleMemCount);
        schedulePipelineResp.setName(pipelineProcess.getPipelineAlias());
        schedulePipelineResp.setPipelineType(pipelineProcess.getPipelineType());
        schedulePipelineResp.setProcessId(pipelineProcess.getId());
        schedulePipelineResp.setStatus(pipelineProcess.getPipelineStatus());
        return schedulePipelineResp;
    }

    private static PipelineResourceResp calSparkPipeline(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                                         TbProcess tbProcess) {
        Map<String, Object> pipelineConfig = JSONUtil.toBean(tbProcess.getPipelineConfig(), HashMap.class);
        if (pipelineConfig == null) {
            pipelineConfig = new HashMap<>(16);
        }
        SparkOpts appSetting = BeanUtil.toBean(pipelineConfig.getOrDefault("opts", new HashMap<>()),
                SparkOpts.class);
        Long optsId = tbProcess.getOptsId();
        if (optsId == null) {
            ClusterResp clusterResp = clusterMap.get(tbProcess.getClusterId());
            optsId = clusterResp.getSparkOptsId();
        }
        OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());
        SparkOpts optSetting = new SparkOpts();
        if (optsResp != null) {
            optSetting = BeanUtil.mapToBean(optsResp.getSetting(), SparkOpts.class, true);
        }
        long driverCore = appSetting.getDriverCores() != null ? appSetting.getDriverCores() :
                optSetting.getDriverCores();
        long driverMemory = StringUtils.isNotBlank(appSetting.getDriverMemory()) ?
                convertMemoryToMb(appSetting.getDriverMemory()) :
                convertMemoryToMb(optSetting.getDriverMemory());
        long executorCores = appSetting.getExecutorCores() != null ? appSetting.getExecutorCores() :
                optSetting.getExecutorCores();
        long executorMemory = StringUtils.isNotBlank(appSetting.getExecutorMemory()) ?
                convertMemoryToMb(appSetting.getExecutorMemory()) :
                convertMemoryToMb(optSetting.getExecutorMemory());
        long numExecutors = appSetting.getNumExecutors() != null ? appSetting.getNumExecutors() :
                optSetting.getNumExecutors();

        // spark：
        //    core=driverCore+ （execCore*实例数）
        //    memory=driverMemory + (execMemeory*实例数)
        long scheduleCpuCount = executorCores * numExecutors + driverCore;
        long scheduleMemCount = numExecutors * executorMemory + driverMemory;

        PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
        schedulePipelineResp.setCpu(scheduleCpuCount);
        schedulePipelineResp.setMemory(scheduleMemCount);
        schedulePipelineResp.setName(tbProcess.getName());
        schedulePipelineResp.setPipelineType(tbProcess.getPipelineType());
        schedulePipelineResp.setProcessId(tbProcess.getId());
        schedulePipelineResp.setStatus(tbProcess.getPipelineStatus());
        return schedulePipelineResp;
    }

    private static PipelineResourceResp calSparkProcess(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                                        ProcessResp pipelineProcess) {
        Map<String, Object> pipelineConfig = pipelineProcess.getPipelineConfig();
        if (pipelineConfig == null) {
            pipelineConfig = new HashMap<>(16);
        }
        SparkOpts appSetting = BeanUtil.toBean(pipelineConfig.getOrDefault("opts", new HashMap<>()),
                SparkOpts.class);
        Long optsId = pipelineProcess.getOptsId();
        if (optsId == null) {
            ClusterResp clusterResp = clusterMap.get(pipelineProcess.getClusterId());
            optsId = clusterResp.getSparkOptsId();
        }
        OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());
        SparkOpts optSetting = new SparkOpts();
        if (optsResp != null) {
            optSetting = BeanUtil.mapToBean(optsResp.getSetting(), SparkOpts.class, true);
        }
        long driverCore = appSetting.getDriverCores() != null ? appSetting.getDriverCores() :
                optSetting.getDriverCores();
        long driverMemory = StringUtils.isNotBlank(appSetting.getDriverMemory()) ?
                convertMemoryToMb(appSetting.getDriverMemory()) :
                convertMemoryToMb(optSetting.getDriverMemory());
        long executorCores = appSetting.getExecutorCores() != null ? appSetting.getExecutorCores() :
                optSetting.getExecutorCores();
        long executorMemory = StringUtils.isNotBlank(appSetting.getExecutorMemory()) ?
                convertMemoryToMb(appSetting.getExecutorMemory()) :
                convertMemoryToMb(optSetting.getExecutorMemory());
        long numExecutors = appSetting.getNumExecutors() != null ? appSetting.getNumExecutors() :
                optSetting.getNumExecutors();

        // spark：
        //    core=driverCore+ （execCore*实例数）
        //    memory=driverMemory + (execMemeory*实例数)
        long scheduleCpuCount = executorCores * numExecutors + driverCore;
        long scheduleMemCount = numExecutors * executorMemory + driverMemory;

        PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
        schedulePipelineResp.setCpu(scheduleCpuCount);
        schedulePipelineResp.setMemory(scheduleMemCount);
        schedulePipelineResp.setName(pipelineProcess.getName());
        schedulePipelineResp.setPipelineType(pipelineProcess.getPipelineType());
        schedulePipelineResp.setProcessId(pipelineProcess.getId());
        schedulePipelineResp.setStatus(pipelineProcess.getPipelineStatus());
        return schedulePipelineResp;
    }

    @SuppressWarnings("all")
    private List<PipelineResourceResp> countPipelineResouce(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                                            Long processId, List<Long> runningSparkIds) {
        Map<Long, PipelineResourceResp> pipelineResourceRespMap = new HashMap<>(16);

        // 管线作业(flink + flinkSql + 自定义FLINK)、hive作业、es作业
        PipelineQueryReq req = new PipelineQueryReq();
        PipelineFilterReq filter = new PipelineFilterReq();
        PipelineSortReq sort = new PipelineSortReq();
        req.setPage(0);
        req.setSize(1000000);
        req.setFilter(filter);
        req.setSort(sort);
        filter.setPipelineStatus(Arrays.asList(
                PipelineStatusEnum.RUNNING.code(),
                PipelineStatusEnum.WAITING_START.code(),
                PipelineStatusEnum.STARTING.code(),
                PipelineStatusEnum.WAITING_STOP.code(),
                PipelineStatusEnum.STOPPING.code(),
                PipelineStatusEnum.STOP_FAILED.code(),
                PipelineStatusEnum.RESTARTING.code()
        ));
        IPage<TbPipeline> pipelinePage = tbPipelineService.selectCustomPage(req.page(), req.query(),
                Optional.ofNullable(req.getFilter())
                        .map(it -> CollUtil.isEmpty(it.getTagIdList()) ? null : it.getTagIdList()).orElse(null));
        List<TbPipeline> tbPipelineList = pipelinePage.getRecords();
        List<PipelineResp> pipelineResps = tbPipelineList.stream().filter(x -> {
            if (processId != null && processId.equals(x.getProcessId())) {
                return false;
            }
            return x.getProjectAuthIsOwner() != null && x.getProjectAuthIsOwner();
        }).map(it -> {
            return new PipelineResp().fromEntity(it);
        }).collect(Collectors.toList());

        if (pipelineResps != null && pipelineResps.size() > 0) {
            // 找出flinkSession会话
            List<PipelineResp> flinkSessionProcessList = new ArrayList<>();
            Iterator<PipelineResp> iterator = pipelineResps.iterator();
            while (iterator.hasNext()) {
                PipelineResp p = iterator.next();
                if (p.getYarnSessionId() != null) {
                    flinkSessionProcessList.add(p);
                    iterator.remove();
                }
            }
            // 不计算flinkSession会话
//            if (flinkSessionProcessList.size() > 0) {
//                // 查询所有flinkSession
//                List<FlinkYarnSessionResp> yarnSessionResps = flinkYarnSessionService
//                        .allWithProjectAuth(new LambdaQueryWrapper<TbFlinkYarnSession>()
//                                .in(TbFlinkYarnSession::getId, flinkSessionProcessList.stream()
//                                        .map(PipelineResp::getYarnSessionId).collect(Collectors.toList())));
//                Map<Long, FlinkYarnSessionResp> yarnSessionMap = yarnSessionResps.stream()
//                        .collect(Collectors.toMap(FlinkYarnSessionResp::getId, Function.identity()));
//                // 根据yarnSessionId分组
////                yarnSession1  jobManagerM  2048 , tm 1024 , slots 3
////                flink1  parall 2
////                flink2  parall 3
////                flink3  parall 5
////                flinkMemory = (10/3 = 4) * 1024  + 2048   (并行度/slot )向上取整
////                flinkCpu = 1+10
//                Map<Long, List<PipelineResp>> yarnSessionIdMap = flinkSessionProcessList.stream()
//                        .collect(Collectors.groupingBy(PipelineResp::getYarnSessionId));
//                for (Map.Entry<Long, List<PipelineResp>> entry : yarnSessionIdMap.entrySet()) {
//                    List<PipelineResp> pipelineResp = entry.getValue();
//                    FlinkYarnSessionResp yarnSessionResp = yarnSessionMap.get(entry.getKey());
//                    PipelineResourceResp flinkSessionPipelineResp =
//                            countFlinkSessionResource(clusterMap, optsMap, pipelineResps, yarnSessionResp);
//                    pipelineResourceRespMap.put(entry.getKey(), flinkSessionPipelineResp);
//                }
//            }

            // 计算非flinkSession任务
            for (PipelineResp p : pipelineResps) {
                ClusterResp clusterResp = clusterMap.get(p.getClusterId());
                if (ClusterTypeEnum.FLINK_STANDALONE.code().equals(clusterResp.getClusterType())
                        || ClusterTypeEnum.SPARK_STANDALONE.code().equals(clusterResp.getClusterType())) {
                    continue;
                }
                PipelineResourceResp schedulePipelineResp = calPiplineResource(clusterMap, optsMap, p);
                pipelineResourceRespMap.put(p.getId(), schedulePipelineResp);
                if (PipelineTypeEnum.BATCH.code().equals(p.getPipelineType())) {
                    runningSparkIds.add(p.getId());
                }
            }
        }
        return pipelineResourceRespMap.values().stream().collect(Collectors.toList());
    }

    private static PipelineResourceResp countFlinkSessionResource(Map<Long, ClusterResp> clusterMap,
                                                                  Map<Long, OptsResp> optsMap,
                                                                  List<PipelineResp> pipelineResps,
                                                                  FlinkYarnSessionResp yarnSessionResp) {
        long totalParallelism = 0;
        for (PipelineResp p : pipelineResps) {
            Map<String, Object> pipelineConfig = p.getPipelineConfig();
            FlinkOpts appSetting = BeanUtil.toBean(pipelineConfig.getOrDefault("opts", new HashMap<>()),
                    FlinkOpts.class);
            Long optsId = p.getOptsId();
            if (optsId == null) {
                ClusterResp clusterResp = clusterMap.get(p.getClusterId());
                optsId = clusterResp.getFlinkOptsId();
            }
            OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());
            FlinkOpts optSetting = new FlinkOpts();
            if (optsResp != null) {
                optSetting = BeanUtil.mapToBean(optsResp.getSetting(), FlinkOpts.class, true);
            }
            long parallelism = appSetting.getParallelism() != null ?
                    appSetting.getParallelism() :
                    optSetting.getParallelism();
            totalParallelism += parallelism;
        }
        long jobManagerMemory = yarnSessionResp.getJobManagerMemory();
        long yarnTaskManagerMemory = yarnSessionResp.getTaskManagerMemory();
        long slots = yarnSessionResp.getSlots();
        long memCount = (long) (Math.ceil(1.0 * totalParallelism / slots) * yarnTaskManagerMemory + jobManagerMemory);
        long cpuCount = 1 + (long) (Math.ceil(1.0 * totalParallelism / slots)) * slots;

        PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
        schedulePipelineResp.setCpu(cpuCount);
        schedulePipelineResp.setMemory(memCount);
        schedulePipelineResp.setName(yarnSessionResp.getYarnSessionName());
        schedulePipelineResp.setPipelineType(null);
        schedulePipelineResp.setProcessId(yarnSessionResp.getId());
        schedulePipelineResp.setStatus(null);
        return schedulePipelineResp;
    }


    private static PipelineResourceResp calPiplineResource(Map<Long, ClusterResp> clusterMap,
                                                           Map<Long, OptsResp> optsMap,
                                                           PipelineResp p) {
        try {
            if (PipelineTypeEnum.BATCH.code().equals(p.getPipelineType())) {
                return calSparkPipeline(clusterMap, optsMap, p);
            }
            if (PipelineTypeEnum.STREAMING.code().equals(p.getPipelineType())
                    || PipelineTypeEnum.FLINK_SQL.code().equals(p.getPipelineType())) {
                Map<String, Object> pipelineConfig = p.getPipelineConfig();
                FlinkOpts appSetting = BeanUtil.toBean(pipelineConfig.getOrDefault("opts", new HashMap<>()),
                        FlinkOpts.class);

                Long optsId = p.getOptsId();
                if (optsId == null) {
                    ClusterResp clusterResp = clusterMap.get(p.getClusterId());
                    optsId = clusterResp.getFlinkOptsId();
                }
                OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());
                FlinkOpts optSetting = new FlinkOpts();
                if (optsResp != null) {
                    optSetting = BeanUtil.mapToBean(optsResp.getSetting(), FlinkOpts.class, true);
                }

                long yarnSlots = appSetting.getYarnSlots() != null ? appSetting.getYarnSlots() :
                        optSetting.getYarnSlots();
                long yarnTaskManagerMemory = StringUtils.isNotBlank(appSetting.getYarnTaskManagerMemory()) ?
                        convertMemoryToMb(appSetting.getYarnTaskManagerMemory()) :
                        convertMemoryToMb(optSetting.getYarnTaskManagerMemory());
                long yarnJobManagerMemory = StringUtils.isNotBlank(appSetting.getYarnJobManagerMemory()) ?
                        convertMemoryToMb(appSetting.getYarnJobManagerMemory()) :
                        convertMemoryToMb(optSetting.getYarnJobManagerMemory());
                long parallelism = appSetting.getParallelism() != null ?
                        appSetting.getParallelism() :
                        optSetting.getParallelism();
                // flink：
                //    flinkMemory =(并行度/slot 向上取整 ) * taskManager  + jobManager
                //    flinkCpu=1+taskManager
                long memCount = (long) (Math.ceil(1.0 * parallelism / yarnSlots) * yarnTaskManagerMemory + yarnJobManagerMemory);
                long cpuCount = 1 + (long) (Math.ceil(1.0 * parallelism / yarnSlots)) * yarnSlots;

                PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
                schedulePipelineResp.setCpu(cpuCount);
                schedulePipelineResp.setMemory(memCount);
                schedulePipelineResp.setName(p.getPipelineAlias());
                schedulePipelineResp.setPipelineType(p.getPipelineType());
                schedulePipelineResp.setProcessId(p.getId());
                schedulePipelineResp.setStatus(p.getPipelineStatus());
                return schedulePipelineResp;
            }

            if (PipelineTypeEnum.FLINK_CMD.code().equals(p.getPipelineType())) {
                //自定义flink作业：
                //    优先解析自定义启动命令来获取：-  任务配置取 - 框架取
                //bin/flink run -m yarn-cluster -d -p 1 -yn 1 -ys 1 -yjm 1024 -ytm 2048 -ynm exercise1_timeZone -c com.eoi.jax.flink_entry.FlinkMainEntry ##ENTRY##
                //-p 并行度，-ys slot， -yjm jobManagerMemory， -ytm taskManagerMemory
                String command = p.getStartCmd();
                Long cmdParallelism = null;
                Long cmdYarnSlots = null;
                Long cmdJobManagerMemory = null;
                Long cmdTaskManagerMemory = null;
                if (StringUtils.isNotBlank(command)) {
                    Pattern pattern = Pattern.compile("-p\\s+(\\d+)|-yn\\s+(\\d+)|-yjm\\s+(\\d+)|-ytm\\s+(\\d+)");
                    Matcher matcher = pattern.matcher(command);
                    while (matcher.find()) {
                        if (matcher.group(1) != null) {
                            cmdParallelism = Long.parseLong(matcher.group(1) == null ? "0" : matcher.group(1));
                        }
                        if (matcher.group(2) != null) {
                            cmdYarnSlots = Long.parseLong(matcher.group(2) == null ? "0" : matcher.group(2));
                        }
                        if (matcher.group(3) != null) {
                            cmdJobManagerMemory = Long.parseLong(matcher.group(3) == null ? "0" : matcher.group(3));
                        }
                        if (matcher.group(4) != null) {
                            cmdTaskManagerMemory = Long.parseLong(matcher.group(4) == null ? "0" : matcher.group(4));
                        }
                    }
                }

                Long optsId = p.getOptsId();
                if (optsId == null) {
                    ClusterResp clusterResp = clusterMap.get(p.getClusterId());
                    optsId = clusterResp.getFlinkOptsId();
                }
                OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());

                FlinkOpts optSetting = new FlinkOpts();
                if (optsResp != null) {
                    optSetting = BeanUtil.mapToBean(optsResp.getSetting(), FlinkOpts.class, true);
                }

                long yarnSlots = cmdYarnSlots != null ? cmdYarnSlots :
                        optSetting.getYarnSlots();
                long yarnTaskManagerMemory = cmdTaskManagerMemory != null ?
                        cmdTaskManagerMemory :
                        convertMemoryToMb(optSetting.getYarnTaskManagerMemory());
                long yarnJobManagerMemory = cmdJobManagerMemory != null ?
                        cmdJobManagerMemory :
                        convertMemoryToMb(optSetting.getYarnJobManagerMemory());
                long parallelism = cmdParallelism != null ?
                        cmdParallelism :
                        optSetting.getParallelism();

                long memCount = (long) (Math.ceil(1.0 * parallelism / yarnSlots) * yarnTaskManagerMemory + yarnJobManagerMemory);
                long cpuCount = 1 + (long) (Math.ceil(1.0 * parallelism / yarnSlots)) * yarnSlots;

                PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
                schedulePipelineResp.setCpu(cpuCount);
                schedulePipelineResp.setMemory(memCount);
                schedulePipelineResp.setName(p.getPipelineAlias());
                schedulePipelineResp.setPipelineType(p.getPipelineType());
                schedulePipelineResp.setProcessId(p.getId());
                schedulePipelineResp.setStatus(p.getPipelineStatus());
                return schedulePipelineResp;
            }
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                    "不支持的pipeline类型：" + p.getPipelineType() + "_" + p.getPipelineName());
        } catch (Exception e) {
            logger.error("计算管线作业资源失败：" + JSONUtil.toJsonPrettyStr(p), e);
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                    "计算管线作业资源失败：" + JSONUtil.toJsonPrettyStr(p));
        }
    }


    private static long convertMemoryToMb(String memory) {
        if (StringUtils.isBlank(memory)) {
            return 0;
        }
        memory = memory.trim().toUpperCase();
        long memoryNum = 0;
        if (memory.contains("M")) {
            memoryNum = Long.parseLong(memory.replaceAll("\\D", ""));
        } else if (memory.contains("G")) {
            memoryNum = Long.parseLong(memory.replaceAll("\\D", "")) * 1024;
        } else {
            memoryNum = Long.parseLong(memory.replaceAll("\\D", ""));
        }

        return memoryNum;
    }


    private static PipelineResourceResp calProcessResource(Map<Long, ClusterResp> clusterMap,
                                                           Map<Long, OptsResp> optsMap,
                                                           ProcessResp p) {
        try {
            if (PipelineTypeEnum.BATCH.code().equals(p.getPipelineType())) {
                return calSparkProcess(clusterMap, optsMap, p);
            }
            if (PipelineTypeEnum.STREAMING.code().equals(p.getPipelineType())
                    || PipelineTypeEnum.FLINK_SQL.code().equals(p.getPipelineType())) {
                Map<String, Object> pipelineConfig = p.getPipelineConfig();
                FlinkOpts appSetting = BeanUtil.toBean(pipelineConfig.getOrDefault("opts", new HashMap<>()),
                        FlinkOpts.class);

                Long optsId = p.getOptsId();
                if (optsId == null) {
                    ClusterResp clusterResp = clusterMap.get(p.getClusterId());
                    optsId = clusterResp.getFlinkOptsId();
                }
                OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());
                FlinkOpts optSetting = new FlinkOpts();
                if (optsResp != null) {
                    optSetting = BeanUtil.mapToBean(optsResp.getSetting(), FlinkOpts.class, true);
                }

                long yarnSlots = appSetting.getYarnSlots() != null ? appSetting.getYarnSlots() :
                        optSetting.getYarnSlots();
                long yarnTaskManagerMemory = StringUtils.isNotBlank(appSetting.getYarnTaskManagerMemory()) ?
                        convertMemoryToMb(appSetting.getYarnTaskManagerMemory()) :
                        convertMemoryToMb(optSetting.getYarnTaskManagerMemory());
                long yarnJobManagerMemory = StringUtils.isNotBlank(appSetting.getYarnJobManagerMemory()) ?
                        convertMemoryToMb(appSetting.getYarnJobManagerMemory()) :
                        convertMemoryToMb(optSetting.getYarnJobManagerMemory());
                long parallelism = appSetting.getParallelism() != null ?
                        appSetting.getParallelism() :
                        optSetting.getParallelism();
                // flink：
                //    flinkMemory =(并行度/slot 向上取整 ) * taskManager  + jobManager
                //    flinkCpu=1+taskManager
                long memCount = (long) (Math.ceil(1.0 * parallelism / yarnSlots) * yarnTaskManagerMemory + yarnJobManagerMemory);
                long cpuCount = 1 + (long) (Math.ceil(1.0 * parallelism / yarnSlots)) * yarnSlots;

                PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
                schedulePipelineResp.setCpu(cpuCount);
                schedulePipelineResp.setMemory(memCount);
                schedulePipelineResp.setName(p.getName());
                schedulePipelineResp.setPipelineType(p.getPipelineType());
                schedulePipelineResp.setProcessId(p.getId());
                schedulePipelineResp.setStatus(p.getPipelineStatus());
                return schedulePipelineResp;
            }

            if (PipelineTypeEnum.FLINK_CMD.code().equals(p.getPipelineType())) {
                //自定义flink作业：
                //    优先解析自定义启动命令来获取：-  任务配置取 - 框架取
                //bin/flink run -m yarn-cluster -d -p 1 -yn 1 -ys 1 -yjm 1024 -ytm 2048 -ynm exercise1_timeZone -c com.eoi.jax.flink_entry.FlinkMainEntry ##ENTRY##
                //-p 并行度，-ys slot， -yjm jobManagerMemory， -ytm taskManagerMemory
                String command = p.getStartCmd();
                Long cmdParallelism = null;
                Long cmdYarnSlots = null;
                Long cmdJobManagerMemory = null;
                Long cmdTaskManagerMemory = null;
                if (StringUtils.isNotBlank(command)) {
                    Pattern pattern = Pattern.compile("-p\\s+(\\d+)|-yn\\s+(\\d+)|-yjm\\s+(\\S+)|-ytm\\s+(\\S+)");
                    Matcher matcher = pattern.matcher(command);
                    while (matcher.find()) {
                        if (matcher.group(1) != null) {
                            cmdParallelism = Long.parseLong(matcher.group(1) == null ? "0" : matcher.group(1));
                        }
                        if (matcher.group(2) != null) {
                            cmdYarnSlots = Long.parseLong(matcher.group(2) == null ? "0" : matcher.group(2));
                        }
                        if (matcher.group(3) != null) {
                            cmdJobManagerMemory = convertMemoryToMb(matcher.group(3) == null ? "0" : matcher.group(3));
                        }
                        if (matcher.group(4) != null) {
                            cmdTaskManagerMemory = convertMemoryToMb(matcher.group(4) == null ? "0" : matcher.group(4));
                        }
                    }
                }

                Long optsId = p.getOptsId();
                if (optsId == null) {
                    ClusterResp clusterResp = clusterMap.get(p.getClusterId());
                    optsId = clusterResp.getFlinkOptsId();
                }
                OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());

                FlinkOpts optSetting = new FlinkOpts();
                if (optsResp != null) {
                    optSetting = BeanUtil.mapToBean(optsResp.getSetting(), FlinkOpts.class, true);
                }

                long yarnSlots = cmdYarnSlots != null ? cmdYarnSlots :
                        optSetting.getYarnSlots();
                long yarnTaskManagerMemory = cmdTaskManagerMemory != null ?
                        cmdTaskManagerMemory :
                        convertMemoryToMb(optSetting.getYarnTaskManagerMemory());
                long yarnJobManagerMemory = cmdJobManagerMemory != null ?
                        cmdJobManagerMemory :
                        convertMemoryToMb(optSetting.getYarnJobManagerMemory());
                long parallelism = cmdParallelism != null ?
                        cmdParallelism :
                        optSetting.getParallelism();

                long memCount = (long) (Math.ceil(1.0 * parallelism / yarnSlots) * yarnTaskManagerMemory + yarnJobManagerMemory);
                long cpuCount = 1 + (long) (Math.ceil(1.0 * parallelism / yarnSlots)) * yarnSlots;

                PipelineResourceResp schedulePipelineResp = new PipelineResourceResp();
                schedulePipelineResp.setCpu(cpuCount);
                schedulePipelineResp.setMemory(memCount);
                schedulePipelineResp.setName(p.getName());
                schedulePipelineResp.setPipelineType(p.getPipelineType());
                schedulePipelineResp.setProcessId(p.getId());
                schedulePipelineResp.setStatus(p.getPipelineStatus());
                return schedulePipelineResp;
            }
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                    "不支持的pipeline类型：" + p.getPipelineType() + "_" + p.getPipelineName());
        } catch (Exception e) {
            logger.error("计算管线作业资源失败：" + JSONUtil.toJsonPrettyStr(p), e);
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                    "计算管线作业资源失败：" + JSONUtil.toJsonPrettyStr(p));
        }
    }

    @Resource
    private TbApplicationService tbApplicationService;

    private void countApplicationResource(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                          ProjectResourceResp projectResourceResp, Long applicationId) {
        List<TbApplication> tbApplications =
                tbApplicationService.selectListWithProjectAuth(new LambdaQueryWrapper<TbApplication>());
        List<Long> applicationIds = tbApplications.stream().filter(x -> {
            return x.getProjectAuthIsOwner() != null && x.getProjectAuthIsOwner();
        }).map(TbApplication::getId).collect(Collectors.toList());

        if (applicationIds != null && applicationIds.size() > 0) {
            List<TbApplicationDeploy> tbApplicationDeployList = tbApplicationDeployService.list(
                    new LambdaQueryWrapper<TbApplicationDeploy>().in(TbApplicationDeploy::getApplicationId, applicationIds));

            List<ApplicationDeployResp> applications = tbApplicationDeployList.stream().map(x -> {
                return new ApplicationDeployResp().fromEntity(x);
            }).collect(Collectors.toList());

            int appCpuCount = 0;
            int appMemCount = 0;
            for (ApplicationDeployResp app : applications) {
                try {

                    if (ApplicationStatusEnum.WAITING_START.code().equals(app.getStatus())
                            || ApplicationStatusEnum.WAITING_STOP.code().equals(app.getStatus())
                            || ApplicationStatusEnum.STARTING.code().equals(app.getStatus())
                            || ApplicationStatusEnum.STOPPING.code().equals(app.getStatus())
                            || ApplicationStatusEnum.STOP_FAILED.code().equals(app.getStatus())
                            || ApplicationStatusEnum.RUNNING.code().equals(app.getStatus())
                    ) {
                        if (applicationId != null && applicationId.longValue() == app.getApplicationId()) {
                            continue;
                        }
                        ApplicationResourceResp result = calApplication(clusterMap, optsMap, app);
                        appCpuCount += result.getCpuCount();
                        appMemCount += result.getMemCount();
                    }
                } catch (Exception e) {
                    logger.error("计算弹性作业资源失败：" + JSONUtil.toJsonPrettyStr(app), e);
                }
            }
            projectResourceResp.setAppCpuCount(appCpuCount);
            projectResourceResp.setAppMemCount(appMemCount);
        }
    }


    private ApplicationResourceResp calApplication(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                                   ApplicationDeployResp app) {
        Long optsId = app.getOptsId();
        if (optsId == null) {
            ClusterResp clusterResp = clusterMap.get(app.getClusterId());
            optsId = clusterResp.getFlinkOptsId();
        }
        OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());

        MarayarnOpts optSetting = new MarayarnOpts();
        if (optsResp != null) {
            optSetting = BeanUtil.mapToBean(optsResp.getSetting(), MarayarnOpts.class, true);
        }
        Map<String, Object> appSettingMap = app.getSettingMap();
        if (appSettingMap == null || appSettingMap.size() == 0) {
            if (StringUtils.isNotEmpty(app.getSetting())) {
                appSettingMap = JSONUtil.toBean(app.getSetting(), Map.class);
            }
        }
        MarayarnOpts appSetting = BeanUtil.mapToBean(appSettingMap, MarayarnOpts.class, true);
        int executorCpu = appSetting.getExecutorCpu() != null ? appSetting.getExecutorCpu() : optSetting.getExecutorCpu();
        int executorMemory = appSetting.getExecutorMemory() != null ? appSetting.getExecutorMemory() : optSetting.getExecutorMemory();
        int amMemory = appSetting.getAmMemory() != null ? appSetting.getAmMemory() : optSetting.getAmMemory();
        int instanceCount = app.getInstanceCount();
        // 先看任务配置，如果任务配置的为空，就取框架配置
        //    core = instanceCount * cup数量 + 1
        //    memory= instanceCount * execMemory + amMemory
        int cpuCount = instanceCount * executorCpu + 1;
        int memCount = instanceCount * executorMemory + amMemory;
        ApplicationResourceResp result = new ApplicationResourceResp(cpuCount, memCount);
        return result;
    }

    private ApplicationResourceResp calStorageCkCluster(Map<Long, ClusterResp> clusterMap, Map<Long, OptsResp> optsMap,
                                                        StorageClusterResp app) {
        Long optsId = app.getOptsId();
        if (optsId == null) {
            ClusterResp clusterResp = clusterMap.get(app.getClusterId());
            optsId = clusterResp.getFlinkOptsId();
        }
        OptsResp optsResp = optsMap.getOrDefault(optsId, new OptsResp());

        MarayarnOpts optSetting = new MarayarnOpts();
        if (optsResp != null) {
            optSetting = BeanUtil.mapToBean(optsResp.getSetting(), MarayarnOpts.class, true);
        }

        int executorCpu = app.getCpuNum() != null ? app.getCpuNum() : optSetting.getExecutorCpu();
        int executorMemory = app.getMemory() != null ? app.getMemory() : optSetting.getExecutorMemory();
        int amMemory = optSetting.getAmMemory();
        int instanceCount = app.getInstanceNum();
        // 先看任务配置，如果任务配置的为空，就取框架配置
        //    core = instanceCount * cup数量 + 1
        //    memory= instanceCount * execMemory + amMemory
        int cpuCount = instanceCount * executorCpu + 1;
        int memCount = instanceCount * executorMemory + amMemory;
        ApplicationResourceResp result = new ApplicationResourceResp(cpuCount, memCount);
        return result;
    }


    public static void main(String[] args) {
        String originalString = "abc123d ef456ghi789M ";
        String replacedString = originalString.replaceAll("\\D", "");
        System.out.println(replacedString); // 输出: 123456789

        System.out.println(Math.ceil(1.0 * 1 / 5));

        String command = "bin/flink run -m yarn-cluster -d   -yn    1 -p    2   -ys 1   -yjm 1024m   -ytm    2048g -ynm " +
                "exercise1_timeZone " +
                "-c com.eoi.jax.flink_entry.FlinkMainEntry";

        // Define the pattern for the options we're interested in  \\s+(\\S+)
        Pattern pattern = Pattern.compile("-p\\s+(\\d+)|-yn\\s+(\\d+)|-yjm\\s+(\\S+)|-ytm\\s+(\\S+)");
        Matcher matcher = pattern.matcher(command);
        while (matcher.find()) {
            if (matcher.group(1) != null) {
                System.out.println("Parallelism (-p): " + matcher.group(1));
            }
            if (matcher.group(2) != null) {
                System.out.println("TaskManager instances (-yn): " + matcher.group(2));
            }
            if (matcher.group(3) != null) {
                System.out.println("JobManager memory (-yjm): " + matcher.group(3));
            }
            if (matcher.group(4) != null) {
                System.out.println("TaskManager memory (-ytm): " + matcher.group(4));
            }
        }
    }

    private String getCronToDomain(String cron) {
        List<String> result = new ArrayList<>();
        CronExpression cronExpression = new CronExpression(cron);
        Date lastTime = new Date();
        lastTime = cronExpression.getNextValidTimeAfter(lastTime);
        return DateUtil.formatDateTime(lastTime);
    }
}
