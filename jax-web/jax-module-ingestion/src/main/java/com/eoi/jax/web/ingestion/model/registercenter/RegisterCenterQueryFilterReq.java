package com.eoi.jax.web.ingestion.model.registercenter;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbRegisterCenter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class RegisterCenterQueryFilterReq implements IFilterReq<TbRegisterCenter> {

    @Schema(description = "注册中心类型")
    private String type;

    @Schema(description = "注册中心名称")
    private String name;

    @Schema(description = "注册中心地址")
    private String address;

    @Schema(description = "注册中心命名空间")
    private String namespace;

    @Schema(description = "注册中心用户名")
    private String username;

    @Override
    public QueryWrapper<TbRegisterCenter> where(QueryWrapper<TbRegisterCenter> wrapper) {
        wrapper.lambda().like(StringUtils.isNotBlank(name), TbRegisterCenter::getName, name);
        wrapper.lambda().like(StringUtils.isNotBlank(address), TbRegisterCenter::getAddress, address);
        wrapper.lambda().like(StringUtils.isNotBlank(namespace), TbRegisterCenter::getNamespace, namespace);
        wrapper.lambda().like(StringUtils.isNotBlank(username), TbRegisterCenter::getUsername, username);
        wrapper.lambda().eq(StringUtils.isNotBlank(type), TbRegisterCenter::getType, type);
        return wrapper;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
