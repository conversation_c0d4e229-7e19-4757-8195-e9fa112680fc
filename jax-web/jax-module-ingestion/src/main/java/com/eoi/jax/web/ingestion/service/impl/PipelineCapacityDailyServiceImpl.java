package com.eoi.jax.web.ingestion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.client.common.SimpleHttpResponse;
import com.eoi.jax.web.core.client.krb5.SpnegoHttpClient;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.PipelineStatusEnum;
import com.eoi.jax.web.core.common.enumrate.PipelineTypeEnum;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.config.config.PipelineCapacityDailyConfig;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.integration.model.prometheus.MetricType;
import com.eoi.jax.web.core.integration.model.prometheus.PromRangeData;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryMetric;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryReq;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryResp;
import com.eoi.jax.web.core.integration.service.PrometheusService;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.service.ISimpleExcelService;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.ingestion.consts.FlinkMetricConstant;
import com.eoi.jax.web.ingestion.enumrate.ClusterTypeEnum;
import com.eoi.jax.web.ingestion.model.cluster.YarnCluster;
import com.eoi.jax.web.ingestion.model.flinkmetric.FlinkJobRangeMetrics;
import com.eoi.jax.web.ingestion.model.flinkmetric.PromMatrixData;
import com.eoi.jax.web.ingestion.model.flinkmetric.PromMatrixMetric;
import com.eoi.jax.web.ingestion.model.monitor.PipelineRunInfo;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyChartReq;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyExcelMode;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyQueryFilterReq;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyQueryReq;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyQuerySortReq;
import com.eoi.jax.web.ingestion.model.pipelinecapacitydaily.PipelineCapacityDailyResp;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.ingestion.provider.pipeline.KafkaJobInfo;
import com.eoi.jax.web.ingestion.provider.pipeline.PipelineJobConfigProvider;
import com.eoi.jax.web.ingestion.service.FlinkMetricService;
import com.eoi.jax.web.ingestion.service.MonitorService;
import com.eoi.jax.web.ingestion.service.PipelineCapacityDailyService;
import com.eoi.jax.web.ingestion.util.PromQueryUtils;
import com.eoi.jax.web.repository.entity.TbPipeline;
import com.eoi.jax.web.repository.entity.TbPipelineCapacityDaily;
import com.eoi.jax.web.repository.search.query.PipelineCapacityDailyParam;
import com.eoi.jax.web.repository.search.result.PipelineCapacityDailyResult;
import com.eoi.jax.web.repository.service.TbPipelineCapacityDailyService;
import com.eoi.jax.web.repository.service.TbPipelineService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/15
 */
@Service
public class PipelineCapacityDailyServiceImpl implements PipelineCapacityDailyService, ISimpleExcelService<PipelineCapacityDailyExcelMode> {

    private static final Logger LOG = LoggerFactory.getLogger(PipelineCapacityDailyServiceImpl.class);

    @Resource
    private TbPipelineService tbPipelineService;

    @Resource
    private TbPipelineCapacityDailyService tbPipelineCapacityDailyService;

    @Resource
    private FlinkMetricService flinkMetricService;

    @Resource
    private MonitorService monitorService;

    @Resource
    private PrometheusService prometheusService;

    @Resource
    private PipelineJobConfigProvider pipelineJobConfigProvider;

    @Resource
    private SystemConfigHolder systemConfigHolder;


    @Override
    public Paged<PipelineCapacityDailyResp> queryPage(PipelineCapacityDailyQueryReq req) {
        Page<TbPipelineCapacityDaily> page = new Page<>(req.getPage() + 1, req.getSize());
        page.setOrders(req.getSort().buildPageOrder());
        PipelineCapacityDailyParam param = ModelBeanUtil.copyBean(req.getFilter(), new PipelineCapacityDailyParam());
        IPage<PipelineCapacityDailyResult> resultPage = tbPipelineCapacityDailyService.selectCustomPage(page, param);

        if (CollUtil.isEmpty(resultPage.getRecords())) {
            return new Paged<>(0, CollUtil.newArrayList());
        }
        Paged<PipelineCapacityDailyResp> paged = new Paged<>();
        paged.setTotal(resultPage.getTotal());
        List<PipelineCapacityDailyResp> list = resultPage.getRecords().stream()
                .map(it -> new PipelineCapacityDailyResp().fromEntity(it)).collect(Collectors.toList());
        paged.setList(list);

        return paged;
    }


    @Override
    public FlinkJobRangeMetrics queryJobLevelRangeMetrics(PipelineCapacityDailyChartReq req) {
        TbPipelineCapacityDaily tbPipelineCapacityDaily = tbPipelineCapacityDailyService.getById(req.getId());

        PipelineRunInfo pipelineRunInfo = monitorService.getPipelineRunInfo(tbPipelineCapacityDaily.getPipelineId());

        String statisticalDay = tbPipelineCapacityDaily.getStatisticalDay();
        LocalDate baseDate = LocalDate.parse(statisticalDay, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Long startSec = LocalDateTime.of(baseDate, LocalTime.of(0, 0, 0)).toEpochSecond(ZoneOffset.of("+8"));
        Long endSec = LocalDateTime.of(baseDate.plusDays(1), LocalTime.of(0, 0, 0)).toEpochSecond(ZoneOffset.of("+8"));

        FlinkJobRangeMetrics flinkJobRangeMetrics = new FlinkJobRangeMetrics();
        Pair<Integer, Set<String>> parallelismPair = parallelism(pipelineRunInfo, endSec);
        if (Objects.isNull(parallelismPair)) {
            return flinkJobRangeMetrics;
        }
        pipelineRunInfo.setYarnAppId(String.join("|", parallelismPair.getValue()));

        flinkJobRangeMetrics.setCpuUsage(PromRangeData.create("CPU使用率", MetricType.PercentType,
                queryRangeDataByAppId(FlinkMetricConstant.JOB_METRIC_TM_CPU_USAGE_BY_APP_ID,
                        pipelineRunInfo.getYarnAppId(), pipelineRunInfo.getPrometheusUrl(), startSec, endSec, req.getWidth())));

        flinkJobRangeMetrics.setFullGcRate(PromRangeData.create("FullGC次数", MetricType.CountMinuteRate,
                queryRangeDataByAppId(FlinkMetricConstant.JOB_METRIC_TM_FULL_GC_RATE_BY_APP_ID,
                        pipelineRunInfo.getYarnAppId(), pipelineRunInfo.getPrometheusUrl(), startSec, endSec, req.getWidth())));

        flinkJobRangeMetrics.setKafkaConsumeRate(PromRangeData.create("FlinkOffset拉取速率", MetricType.ShortRate,
                queryRangeDataByAppId(FlinkMetricConstant.JOB_METRIC_KAFKA_OFFSET_RATE_BY_APP_ID,
                        pipelineRunInfo.getYarnAppId(), pipelineRunInfo.getPrometheusUrl(), startSec, endSec, req.getWidth())));

        flinkJobRangeMetrics.setKafkaLag(queryKafkaGroupLag(tbPipelineCapacityDaily, pipelineRunInfo.getYarnAppId(),
                pipelineRunInfo.getPrometheusUrl(), startSec, endSec, req.getWidth()));

        return flinkJobRangeMetrics;
    }


    private List<Object[]> queryRangeDataByAppId(String jobMetricQuery, String appId, String prometheusUrl,
                                                 Long startSec, Long endSec, Integer width) {
        String query = replaceLabel(jobMetricQuery, "app_resource", appId);
        List<Object[]> rangeArrays = flinkMetricService.queryRangeValuesAsResp(
                query, prometheusUrl, PromQueryUtils.createRangeReq(query, startSec, endSec, width));
        return rangeArrays;
    }

    private PromMatrixData queryKafkaGroupLag(TbPipelineCapacityDaily tbPipelineCapacityDaily,
                                              String appId, String prometheusUrl,
                                              Long startSec, Long endSec, Integer width) {

        if (StrUtil.isBlank(tbPipelineCapacityDaily.getKafkaJobInfo())) {
            return null;
        }
        List<KafkaJobInfo> kafkaJobs = JsonUtil.decode(tbPipelineCapacityDaily.getKafkaJobInfo(), new TypeReference<List<KafkaJobInfo>>() {
        });
        if (CollUtil.isEmpty(kafkaJobs)) {
            return null;
        }

        PromMatrixData metric = new PromMatrixData();
        metric.setDisplayName("Kafka消费积压");
        metric.setDisplayType(MetricType.ShortNum.getType());
        metric.setUnit(MetricType.ShortNum.getUnit());
        metric.setMetrics(new ArrayList<>());
        Double max = null;
        Double min = null;
        for (KafkaJobInfo kafkaJob : kafkaJobs) {
            for (String topic : kafkaJob.getTopics()) {
                String pql = replaceLabel(FlinkMetricConstant.KAFKA_EXPORTER_CONSUMERGROUP_LAG, "topic", topic);
                pql = replaceLabel(pql, "consumergroup", kafkaJob.getGroupId());
                PromRangeData flinkKafkaLag = PromRangeData.create(
                        "Lag-" + topic + "-" + kafkaJob.getGroupId(),
                        MetricType.ShortNum,
                        queryRangeDataByAppId(pql, appId, prometheusUrl, startSec, endSec, width)
                );
                if (CollUtil.isEmpty(flinkKafkaLag.getValues())) {
                    continue;
                }
                if (flinkKafkaLag.getMax() != null) {
                    if (max == null || max < flinkKafkaLag.getMax()) {
                        max = flinkKafkaLag.getMax();
                    }
                }
                if (flinkKafkaLag.getMin() != null) {
                    if (min == null || min > flinkKafkaLag.getMin()) {
                        min = flinkKafkaLag.getMin();
                    }
                }
                Map<String, String> label = new HashMap<>(8);
                label.put("consumergroup", kafkaJob.getGroupId());
                label.put("topic", topic);
                PromMatrixMetric matrixMetric = new PromMatrixMetric();
                matrixMetric.setMax(flinkKafkaLag.getMax());
                matrixMetric.setMin(flinkKafkaLag.getMin());
                matrixMetric.setValues(flinkKafkaLag.getValues());
                matrixMetric.setLabel(label);
                metric.getMetrics().add(matrixMetric);
            }
        }
        metric.setMax(max);
        metric.setMin(min);
        return metric;
    }


    /**
     * 容量日报
     */
    public Integer capacityDaily(String statisticalDay) {
        Assert.notBlank(statisticalDay, "统计日期不能为空");
        LocalDate baseDate = LocalDate.parse(statisticalDay, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (!baseDate.isBefore(LocalDate.now())) {
            throw new BizException(ResponseCode.FAILED.getCode(), "统计日期不能大于等于今天");
        }

        int count = 0;
        LambdaQueryWrapper<TbPipeline> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbPipeline::getPipelineType, PipelineTypeEnum.STREAMING.code());
        queryWrapper.eq(TbPipeline::getPipelineStatus, PipelineStatusEnum.RUNNING.code());
        Semaphore semaphore = new Semaphore(4);
        for (int i = 1; ; i++) {
            Page<TbPipeline> page = tbPipelineService.page(new Page<>(i, 100), queryWrapper);
            if (CollUtil.isEmpty(page.getRecords())) {
                break;
            }
            List<CompletableFuture<Boolean>> completableFutureList = new LinkedList<>();
            for (TbPipeline tbPipeline : page.getRecords()) {
                try {
                    semaphore.acquire();
                    completableFutureList.add(CompletableFuture.supplyAsync(() -> {
                        try {
                            return compute(tbPipeline, statisticalDay);
                        } finally {
                            semaphore.release();
                        }
                    }, ThreadPoolUtil.THREAD_POOL));
                } catch (InterruptedException e) {
                    throw new RuntimeException("中断异常", e);
                }
            }
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
            count += completableFutureList.stream().map(it -> it.getNow(false)).filter(Boolean.TRUE::equals).count();
        }

        deleteExpiredCapacityDaily();
        return count;
    }


    @Override
    public void pipelineCapacityDaily(Long pipelineId, String statisticalDay) {
        if (StrUtil.isBlank(statisticalDay)) {
            statisticalDay = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        TbPipeline tbPipeline = tbPipelineService.getById(pipelineId);
        compute(tbPipeline, statisticalDay);
    }

    /**
     * 删除过期的容量日报
     */
    private void deleteExpiredCapacityDaily() {
        PipelineCapacityDailyConfig config = systemConfigHolder.getBean(SystemConfigEnum.PIPELINE_CAPACITY_DAILY);
        if (Optional.ofNullable(config).map(PipelineCapacityDailyConfig::getTtl).orElse(0) <= 0) {
            return;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String ltStatisticalDay = LocalDate.now().minusDays(config.getTtl()).format(dateTimeFormatter);
        Long count = tbPipelineCapacityDailyService.selectByLtStatisticalDay(ltStatisticalDay);
        if (Objects.isNull(count) || count <= 0) {
            return;
        }
        LOG.debug("deleteExpiredCapacityDaily, ltStatisticalDay:{}, count:{}", ltStatisticalDay, count);
        boolean flag = tbPipelineCapacityDailyService.deleteLtStatisticalDay(ltStatisticalDay);
        if (!flag) {
            LOG.error("deleteExpiredCapacityDaily error");
        }
    }


    private boolean compute(TbPipeline tbPipeline, String statisticalDay) {
        try {
            PipelineRunInfo pipelineRunInfo = monitorService.getPipelineRunInfo(tbPipeline.getId());

            LocalDate baseDate = LocalDate.parse(statisticalDay, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 获取当天的结束时间
            long endSec = LocalDateTime.of(baseDate.plusDays(1), LocalTime.MIN).toEpochSecond(ZoneOffset.of("+8"));

            TbPipelineCapacityDaily tbPipelineCapacityDaily = new TbPipelineCapacityDaily();
            tbPipelineCapacityDaily.setPipelineId(tbPipeline.getId());
            // 获取并行度
            Pair<Integer, Set<String>> parallelismPair = parallelism(pipelineRunInfo, endSec);
            if (Objects.isNull(parallelismPair)) {
                return false;
            }
            // 重新设置yarnAppId
            pipelineRunInfo.setYarnAppId(String.join("|", parallelismPair.getValue()));
            tbPipelineCapacityDaily.setParallelism(parallelismPair.getKey());
            // CPU峰值使用率
            double maxTmCupUsageRate = maxTmCupUsageRate(pipelineRunInfo.getPrometheusUrl(), pipelineRunInfo.getYarnAppId(), endSec);
            // CPU平均使用率
            double avgTmCupUsageRate = avgTmCupUsageRate(pipelineRunInfo.getPrometheusUrl(), pipelineRunInfo.getYarnAppId(), endSec);
            tbPipelineCapacityDaily.setMaxCpuRate(Math.round(maxTmCupUsageRate * 100));
            tbPipelineCapacityDaily
                    .setMaxCpuRateQuotaRatio(tbPipelineCapacityDaily.getMaxCpuRate() / tbPipelineCapacityDaily.getParallelism());
            tbPipelineCapacityDaily.setAvgCpuRate(Math.round(avgTmCupUsageRate * 100));
            tbPipelineCapacityDaily
                    .setAvgCpuRateQuotaRatio(tbPipelineCapacityDaily.getAvgCpuRate() / tbPipelineCapacityDaily.getParallelism());
            // 获取重启次数
            tbPipelineCapacityDaily.setRestartCount(queryJobRestartNum(pipelineRunInfo.getPrometheusUrl(),
                    pipelineRunInfo.getYarnAppId(), endSec));
            // 获取插槽数
            tbPipelineCapacityDaily.setSlot(getSolt(pipelineRunInfo));
            // 获取fullGc次数
            tbPipelineCapacityDaily.setMaxFullGcCount(maxFullGcCount(pipelineRunInfo.getPrometheusUrl(),
                    pipelineRunInfo.getYarnAppId(), endSec));
            // 计算kafka消费数据量
            Pair<List<KafkaJobInfo>, Long> pair = kafkaConsumerDataCount(pipelineRunInfo.getPrometheusUrl(),
                    endSec, tbPipeline.getPipelineConfig());
            tbPipelineCapacityDaily.setRecordCount(pair.getValue());
            tbPipelineCapacityDaily.setKafkaJobInfo(Optional.ofNullable(pair.getKey()).filter(CollUtil::isNotEmpty)
                    .map(JsonUtil::encode).orElse(null));

            tbPipelineCapacityDaily.setStatisticalDay(statisticalDay);
            ModelBeanUtil.setCreateDefaultValue(tbPipelineCapacityDaily);

            savePipelineCapacityDaily(tbPipelineCapacityDaily);
            return true;
        } catch (Exception e) {
            LOG.error("统计pipelineId:{},pipelineName:{},pipelineAlias:{}", tbPipeline.getId(),
                    tbPipeline.getPipelineName(), tbPipeline.getPipelineAlias(), e);
            return false;
        }
    }


    private void savePipelineCapacityDaily(TbPipelineCapacityDaily tbPipelineCapacityDaily) {
        TbPipelineCapacityDaily oldTbPipelineCapacityDaily = tbPipelineCapacityDailyService.selectByPieplineIdAndStatisticalDay(
                tbPipelineCapacityDaily.getPipelineId(), tbPipelineCapacityDaily.getStatisticalDay());
        if (Objects.isNull(oldTbPipelineCapacityDaily)) {
            tbPipelineCapacityDailyService.save(tbPipelineCapacityDaily);
        } else {
            tbPipelineCapacityDaily.setId(oldTbPipelineCapacityDaily.getId());
            ModelBeanUtil.setUpdateDefaultValue(tbPipelineCapacityDaily);
            tbPipelineCapacityDailyService.updateById(tbPipelineCapacityDaily);
        }
    }


    /**
     * 计算TaskManage的CPU峰值
     */
    private double maxTmCupUsageRate(String promUrl, String appId, long endSec) {
        String pql = replaceLabel(FlinkMetricConstant.CPU_MAX_WITHIN_24_HOURS, "app_resource", appId);
        Object value = flinkMetricService.queryInstantValueAsResponse(pql, promUrl, endSec);
        LOG.debug("endSec:{}, maxTmCupTime-pql:{}, value:{}", endSec, pql, value);
        return PromQueryUtils.castAsDouble(value);
    }

    /**
     * 计算TaskManage的CPU平均值
     */
    private double avgTmCupUsageRate(String promUrl, String appId, long endSec) {
        String pql = replaceLabel(FlinkMetricConstant.CPU_AVG_WITHIN_24_HOURS, "app_resource", appId);
        Object value = flinkMetricService.queryInstantValueAsResponse(pql, promUrl, endSec);
        LOG.debug("endSec:{}, avgTmCupTime-pql:{}, value:{}", endSec, pql, value);
        return PromQueryUtils.castAsDouble(value);
    }

    /**
     * 计算并行度
     */
    private Pair<Integer, Set<String>> parallelism(PipelineRunInfo pipelineRunInfo, long endSec) {
        PipelineCapacityDailyConfig config = systemConfigHolder.getBean(SystemConfigEnum.PIPELINE_CAPACITY_DAILY);
        String filterField = Optional.ofNullable(config).map(PipelineCapacityDailyConfig::getFilterField).orElse("job_id");
        String taskNumRecordQuery = "flink_taskmanager_job_task_operator_numRecordsOutPerSecond{%s='%s'}";
        String pql = null;
        if ("job_id".equals(filterField)) {
            pql = String.format(taskNumRecordQuery, filterField, pipelineRunInfo.getFlinkJobId());
        } else if ("job_name".equals(filterField)) {
            pql = String.format(taskNumRecordQuery, filterField, pipelineRunInfo.getPipelineName());
        }
        PrometheusQueryResp resp = prometheusService.query(new PrometheusQueryReq(pql, endSec), pipelineRunInfo.getPrometheusUrl());

        Set<String> subtaskIndexSet = new HashSet<>();
        Set<String> appIdSet = new HashSet<>();
        List<PrometheusQueryMetric> jobMetricValues = resp.getResult();
        if (CollUtil.isNotEmpty(jobMetricValues)) {
            for (PrometheusQueryMetric taskOpData : jobMetricValues) {
                Map<String, Object> taskOpMetric = taskOpData.getMetric();
                subtaskIndexSet.add(getOrNull(taskOpMetric, "subtask_index"));
                appIdSet.add(getOrNull(taskOpMetric, "app_resource"));
            }
        }

        LOG.info("endSec:{}, cupQuota-pql:{}, subtaskIndexSet:{}", endSec, pql, JsonUtil.encode(subtaskIndexSet));
        if (CollUtil.isEmpty(subtaskIndexSet)) {
            return null;
        }
        return new Pair<>(subtaskIndexSet.size(), appIdSet);
    }


    private <T> T getOrNull(Map map, String key) {
        Object value = map.get(key);
        if (null != value) {
            return (T) value;
        }
        return null;
    }

    /**
     * 获取插槽数
     *
     * @param pipelineRunInfo
     * @return
     */
    private Integer getSolt(PipelineRunInfo pipelineRunInfo) {
        String url = String.format("%s/overview", pipelineRunInfo.getTrackingUrl());
        try {
            Map<String, Object> overviewResp = new HashMap<>(4);
            if (ClusterTypeEnum.YARN.equals(pipelineRunInfo.getClusterType()) && Optional.ofNullable(pipelineRunInfo.getCluster())
                    .map(it -> (YarnCluster) it.getSetting()).map(YarnCluster::isWebKrb5Enabled).orElse(false)) {
                YarnCluster yarnCluster = (YarnCluster) pipelineRunInfo.getCluster().getSetting();
                SpnegoHttpClient spnegoHttpClient = new SpnegoHttpClient(yarnCluster.getKeytab(), yarnCluster.getPrincipal(), true);
                HttpGet httpGet = new HttpGet(url);
                httpGet.addHeader("Content-Type", "application/json");
                httpGet.setConfig(RequestConfig.custom()
                        .setConnectTimeout(2000)
                        .setSocketTimeout(5000)
                        .setRedirectsEnabled(true)
                        .setMaxRedirects(8)
                        .build());
                SimpleHttpResponse simpleHttpResponse = spnegoHttpClient.execute((CloseableHttpClient client) -> {
                    try (CloseableHttpResponse httpResponse = client.execute(httpGet)) {
                        return new SimpleHttpResponse()
                                .setStatus(httpResponse.getStatusLine().getStatusCode())
                                .setBody(EntityUtils.toString(httpResponse.getEntity()));
                    }
                });
                if (simpleHttpResponse.getStatus() < 200 || simpleHttpResponse.getStatus() >= 300) {
                    return -1;
                }
                overviewResp = JsonUtil.decode2Map(simpleHttpResponse.getBody());
            } else {
                HttpRequest httpRequest = HttpUtil.createGet(url)
                        .contentType("application/json")
                        .setConnectionTimeout(2000)
                        .setReadTimeout(5000)
                        .setMaxRedirectCount(8);
                try (HttpResponse httpResponse = httpRequest.execute()) {
                    if (!httpResponse.isOk()) {
                        return -1;
                    }
                    overviewResp = JsonUtil.decode2Map(httpResponse.body());
                }
            }
            int slots = (int) overviewResp.getOrDefault("slots-total", 0);
            if (slots <= 0) {
                return -1;
            }
            int taskmanagers = (int) overviewResp.getOrDefault("taskmanagers", 0);
            if (taskmanagers <= 0) {
                return -1;
            }
            return slots / taskmanagers;
        } catch (Exception ex) {
            LOG.error("get slot error", ex);
            return -1;
        }
    }


    /**
     * 查询任务重启次数
     *
     * @param promUrl
     * @param appId
     * @param endSec
     * @return
     */
    public Integer queryJobRestartNum(String promUrl, String appId, long endSec) {
        String pql = replaceLabel(FlinkMetricConstant.RESTART_NUM_COUNT_WITHIN_24_HOURS, "app_resource", appId);
        Object value = flinkMetricService.queryInstantValueAsResponse(pql, promUrl, endSec);
        if (Objects.isNull(value)) {
            pql = replaceLabel(FlinkMetricConstant.FULL_RESTART_COUNT_WITHIN_24_HOURS, "app_resource", appId);
            value = flinkMetricService.queryInstantValueAsResponse(pql, promUrl, endSec);
        }
        return PromQueryUtils.castAsNumber(value).intValue();
    }

    /**
     * fullGc次数统计
     *
     * @param promUrl
     * @param appId
     * @param endSec
     */
    private int maxFullGcCount(String promUrl, String appId, long endSec) {
        String pql = replaceLabel(FlinkMetricConstant.FULL_GC_CMS_COUNT_MAX_WITHIN_24_HOURS, "app_resource", appId);
        Object value = flinkMetricService.queryInstantValueAsResponse(pql, promUrl, endSec);
        LOG.debug("endSec:{}, maxFullGcCount-pql: {}, value:{}", endSec, pql, value);
        if (Objects.nonNull(value)) {
            return PromQueryUtils.castAsNumber(value).intValue();
        }
        pql = replaceLabel(FlinkMetricConstant.FULL_GC_G1_COUNT_MAX_WITHIN_24_HOURS, "app_resource", appId);
        value = flinkMetricService.queryInstantValueAsResponse(pql, promUrl, endSec);
        return PromQueryUtils.castAsNumber(value).intValue();
    }


    private Pair<List<KafkaJobInfo>, Long> kafkaConsumerDataCount(String promUrl, long endSec, String pipelineConfigStr) {
        PipelineConfig pipelineConfig = PipelineConfig.fromString(pipelineConfigStr);
        List<KafkaJobInfo> kafkaJobs = pipelineJobConfigProvider.getKafkaSourceJobInfo(pipelineConfig);
        if (CollUtil.isEmpty(kafkaJobs)) {
            return new Pair<>(null, 0L);
        }

        long recordConut = 0L;
        for (KafkaJobInfo kafkaJob : kafkaJobs) {
            for (String topic : kafkaJob.getTopics()) {
                String kafkaLagPql = replaceLabel(FlinkMetricConstant.KAFKA_REAL_CONSUMER_RECORD_COUNT_WITHIN_24_HOURS, "topic", topic);
                kafkaLagPql = replaceLabel(kafkaLagPql, "consumergroup", kafkaJob.getGroupId());
                Object o = flinkMetricService.queryInstantValueAsResponse(kafkaLagPql, promUrl, endSec);
                LOG.debug("endSec:{}, kafkaLagPql-pql: {}, value:{}", endSec, kafkaLagPql, o);
                recordConut += PromQueryUtils.castAsNumber(o).longValue();
            }
        }

        return new Pair<>(kafkaJobs, recordConut);
    }


    private String replaceLabel(String metricQuery, String labelName, String labelValue) {
        return metricQuery.replace("$" + labelName, labelValue);
    }

    @Override
    public Class<PipelineCapacityDailyExcelMode> excelModelClass() {
        return PipelineCapacityDailyExcelMode.class;
    }

    @Override
    public String sheetName() {
        return "流作业容量日报";
    }

    @Override
    public void saveRows(Boolean skip, List<PipelineCapacityDailyExcelMode> importRows) {
        throw new RuntimeException("不支持导入");
    }

    @Override
    public List<PipelineCapacityDailyExcelMode> exportData(ImportExcelReq req) {
        List<PipelineCapacityDailyResp> respList = queryExportData(req);
        if (CollUtil.isEmpty(respList)) {
            return CollUtil.newArrayList();
        }
        return respList.stream().map(it -> {
            PipelineCapacityDailyExcelMode excelMode = new PipelineCapacityDailyExcelMode();
            ModelBeanUtil.copyBean(it, excelMode);
            return excelMode;
        }).collect(Collectors.toList());
    }


    private List<PipelineCapacityDailyResp> queryExportData(ImportExcelReq req) {
        PipelineCapacityDailyQueryReq pipelineCapacityDailyQueryReq = new PipelineCapacityDailyQueryReq();
        if (CollUtil.isNotEmpty(req.getIds())) {
            pipelineCapacityDailyQueryReq.setPage(0);
            pipelineCapacityDailyQueryReq.setSize(req.getIds().size());

            PipelineCapacityDailyQueryFilterReq filter = new PipelineCapacityDailyQueryFilterReq();
            filter.setIdList(req.getIds());
            pipelineCapacityDailyQueryReq.setFilter(filter);
            Paged<PipelineCapacityDailyResp> pipelineCapacityDailyRespPaged = queryPage(pipelineCapacityDailyQueryReq);
            return pipelineCapacityDailyRespPaged.getList();
        } else {
            if (StrUtil.isNotBlank(req.getCondition())) {
                pipelineCapacityDailyQueryReq = JsonUtil.decode(req.getCondition(), PipelineCapacityDailyQueryReq.class);
            } else {
                PipelineCapacityDailyQuerySortReq sort = new PipelineCapacityDailyQuerySortReq();
                sort.setStatisticalDay("desc");
                pipelineCapacityDailyQueryReq.setSort(sort);
            }
            pipelineCapacityDailyQueryReq.setSize(200);
            List<PipelineCapacityDailyResp> list = CollUtil.newArrayList();
            for (int i = 0; ; i++) {
                pipelineCapacityDailyQueryReq.setPage(i);
                Paged<PipelineCapacityDailyResp> pipelineCapacityDailyRespPaged = queryPage(pipelineCapacityDailyQueryReq);
                if (CollUtil.isEmpty(pipelineCapacityDailyRespPaged.getList())) {
                    break;
                }
                list.addAll(pipelineCapacityDailyRespPaged.getList());
            }
            return list;
        }
    }

}
