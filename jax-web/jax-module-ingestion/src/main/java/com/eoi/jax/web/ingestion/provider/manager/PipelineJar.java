package com.eoi.jax.web.ingestion.provider.manager;

import com.eoi.jax.web.ingestion.model.cluster.Cluster;
import com.eoi.jax.web.ingestion.model.cluster.YarnCluster;
import com.eoi.jax.web.ingestion.provider.filesystem.HdfsFileSystem;
import com.eoi.jax.web.repository.entity.TbJar;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
public class PipelineJar {
    private TbJar jar;
    private Cluster cluster;

    public PipelineJar(TbJar jar, Cluster cluster) {
        this.jar = jar;
        this.cluster = cluster;
    }

    public String touchJarFile() {
        if (cluster != null) {
            return HdfsFileSystem.touchJarFile(jar, (YarnCluster) cluster.getSetting());
        }
        return jar.getJarPath();
    }

    public TbJar getJar() {
        return jar;
    }

    public void setJar(TbJar jar) {
        this.jar = jar;
    }

    public Cluster getCluster() {
        return cluster;
    }

    public void setCluster(Cluster cluster) {
        this.cluster = cluster;
    }
}
