package com.eoi.jax.web.ingestion.model.storageck.history;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbStorageCkHistory;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class StorageCkHistoryUpdateReq implements IUpdateModel<TbStorageCkHistory> {
    @Schema(description = "id")
    private Long id;


    @Override
    public TbStorageCkHistory toEntity(TbStorageCkHistory tbStorageCkHistory) {
        TbStorageCkHistory entity = IUpdateModel.super.toEntity(tbStorageCkHistory);
        return entity;
    }

    @Override
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }


}
