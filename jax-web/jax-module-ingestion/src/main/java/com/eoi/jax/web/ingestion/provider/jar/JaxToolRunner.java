package com.eoi.jax.web.ingestion.provider.jar;

import com.eoi.jax.manager.process.IProcessRunner;
import com.eoi.jax.manager.process.ProcessRunner;
import com.eoi.jax.web.core.config.ConfigLoader;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
@Component
public class JaxToolRunner {

    public IProcessRunner jaxToolRunner(Map<String, String> environment) {
        return new ProcessRunner(
                ConfigLoader.load().getJax().getWork(),
                ConfigLoader.load().getJax().getTool().getJaxToolBin(),
                ConfigLoader.load().getJax().getTool().getJaxToolTimeoutMillis()
        ).setEnvironment(environment);
    }

}
