package com.eoi.jax.web.ingestion.debug;

/**
 * <AUTHOR>
 * @Date 2023/8/10
 */
public class WsDebugSinkerJobSlotConfig {
    private String fromJobEntry;
    private String fromJobId;
    private Integer fromJobSlot;

    public String getFromJobEntry() {
        return fromJobEntry;
    }

    public void setFromJobEntry(String fromJobEntry) {
        this.fromJobEntry = fromJobEntry;
    }

    public String getFromJobId() {
        return fromJobId;
    }

    public void setFromJobId(String fromJobId) {
        this.fromJobId = fromJobId;
    }

    public Integer getFromJobSlot() {
        return fromJobSlot;
    }

    public void setFromJobSlot(Integer fromJobSlot) {
        this.fromJobSlot = fromJobSlot;
    }
}
