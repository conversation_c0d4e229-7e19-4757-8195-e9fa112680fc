package com.eoi.jax.web.ingestion.provider.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.eoi.jax.manager.JaxManager;
import com.eoi.jax.manager.api.JobStartParam;
import com.eoi.jax.manager.api.JobStartResult;
import com.eoi.jax.manager.api.JobStopParam;
import com.eoi.jax.manager.api.JobStopResult;
import com.eoi.jax.manager.client.yarn.YarnAppReport;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.enumrate.FlinkStatusEnum;
import com.eoi.jax.web.core.common.enumrate.PipelineStatusEnum;
import com.eoi.jax.web.core.common.enumrate.SparkStatusEnum;
import com.eoi.jax.web.core.common.enumrate.YarnStateEnum;
import com.eoi.jax.web.core.common.exception.JaxException;
import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.ingestion.enumrate.OpLogTypeEnum;
import com.eoi.jax.web.ingestion.enumrate.OpObjTypeEnum;
import com.eoi.jax.web.ingestion.enumrate.OpTypeEnum;
import com.eoi.jax.web.ingestion.model.event.PipelineStatusUpdateEvent;
import com.eoi.jax.web.ingestion.model.pipelineconf.PipelineConfig;
import com.eoi.jax.web.ingestion.plugin.PipelinePlugin;
import com.eoi.jax.web.ingestion.service.PipelineService;
import com.eoi.jax.web.repository.entity.TbOperatorLog;
import com.eoi.jax.web.repository.entity.TbPipeline;
import com.eoi.jax.web.repository.entity.TbPipelineJar;
import com.eoi.jax.web.repository.service.TbOperatorLogService;
import com.eoi.jax.web.repository.service.TbPipelineJarService;
import com.eoi.jax.web.repository.service.TbPipelineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
public abstract class AbstractPipelineManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractPipelineManager.class);

    @Autowired
    private TbPipelineService tbPipelineService;
    @Autowired
    private TbPipelineJarService tbPipelineJarService;
    @Autowired
    private TbOperatorLogService tbOperatorLogService;
    @Autowired
    private PipelineService pipelineService;
    @Lazy
    @Autowired
    private PipelinePlugin pipelinePlugin;

    /**
     * 操作作业所需的内容
     *
     * @param entity
     * @return
     */
    public abstract PipelineCtx pipelineCtx(TbPipeline entity);


    /**
     * 作业依赖的扩展包
     *
     * @param pipelineConfig
     * @return
     */
    public abstract List<PipelineJar> pipelineJars(PipelineConfig pipelineConfig);

    /**
     * 获取 JaxManager
     *
     * @param pipeline
     * @param opTypeEnum
     * @return
     */
    public abstract JaxManager genJaxManager(PipelineCtx pipeline, OpTypeEnum opTypeEnum);

    /**
     * 生成启动参数
     *
     * @param pipeline
     * @return
     */
    public abstract JobStartParam genStartParam(PipelineCtx pipeline);

    /**
     * 生成停止参数
     *
     * @param pipeline
     * @return
     */
    public abstract JobStopParam genStopParam(PipelineCtx pipeline);

    /**
     * 生成删除参数
     *
     * @param pipeline
     * @return
     */
    public abstract JobStopParam genDeleteParam(PipelineCtx pipeline);

    /**
     * 准备启动
     *
     * @param param
     * @param pipeline
     */
    public abstract void beforeStart(JobStartParam param, PipelineCtx pipeline);

    /**
     * 启动结束
     *
     * @param result
     * @param pipeline
     */
    public abstract void afterStart(JobStartResult result, PipelineCtx pipeline);

    /**
     * 准备停止
     *
     * @param param
     * @param pipeline
     */
    public abstract void beforeStop(JobStopParam param, PipelineCtx pipeline);

    /**
     * 停止结束
     *
     * @param result
     * @param pipeline
     */
    public abstract void afterStop(JobStopResult result, PipelineCtx pipeline);

    /**
     * 准备删除
     *
     * @param param
     * @param pipeline
     */
    public abstract void beforeDelete(JobStopParam param, PipelineCtx pipeline);

    /**
     * 删除结束
     *
     * @param result
     * @param pipeline
     */
    public abstract void afterDelete(JobStopResult result, PipelineCtx pipeline);

    /**
     * 获取yarn application信息
     *
     * @param pipeline
     * @return
     */
    public abstract YarnAppReport getYarnApp(PipelineCtx pipeline);

    /**
     * 同步作业状态
     *
     * @param entities
     */
    public abstract void status(List<TbPipeline> entities);

    public void execute(TbPipeline pipeline) {
        if (PipelineStatusEnum.WAITING_START.equals(pipeline.getPipelineStatus())) {
            start(pipeline);
        } else if (PipelineStatusEnum.WAITING_STOP.equals(pipeline.getPipelineStatus())) {
            stop(pipeline);
        } else if (PipelineStatusEnum.WAITING_DELETE.equals(pipeline.getPipelineStatus())) {
            delete(pipeline);
        } else if (PipelineStatusEnum.RESTARTING.equals(pipeline.getPipelineStatus())) {
            restart(pipeline);
        }
    }

    public boolean start(TbPipeline entity) {
        if (!lockPipeline(PipelineStatusEnum.STARTING.code(), entity)) {
            return false;
        }
        boolean success = false;
        String message;
        OpUUID uuid = new OpUUID()
                .setObjType(OpObjTypeEnum.PIPELINE)
                .setObjId(entity.getId())
                .setOpId(IdUtil.genId())
                .setOpType(OpTypeEnum.START)
                .setOpTime(System.currentTimeMillis());
        try {
            PipelineCtx pipeline = pipelineCtx(entity);
            checkPipelineState(pipeline);
            savePipelineJars(entity, pipeline.getJars());
            pipelinePlugin.startReq(pipeline);
            LOGGER.info("start pipeline {} {} on cluster {} {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    entity.getClusterId(),
                    pipeline.getCluster().getClusterName());
            JobStartParam param = genStartParam(pipeline);
            param.setUuid(uuid);
            String paramJson = JsonUtil.encode(param);
            beforeStart(param, pipeline);
            LOGGER.info("start pipeline {} {} param {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    paramJson);
            JobStartResult result = genJaxManager(pipeline, OpTypeEnum.START).start(param);
            String resultJson = JsonUtil.encode(result);
            LOGGER.info("start pipeline {} {} result {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    resultJson);
            success = result.isSuccess();
            message = resultJson;
            pipeline = pipelineCtx(tbPipelineService.getById(entity.getId()));
            afterStart(result, pipeline);
        } catch (Throwable e) {
            LOGGER.error("start pipeline {} {} failed",
                    entity.getId(),
                    entity.getPipelineName(),
                    e);
            success = false;
            message = ExceptionUtil.stacktraceToString(e);
        } finally {
            PipelineStatusEnum pipelineStatus = success ? PipelineStatusEnum.STARTING : PipelineStatusEnum.START_FAILED;
            unlockPipeline(pipelineStatus.code(), entity);
        }
        savePipelineLog(uuid, message, entity);
        return success;
    }

    public boolean stop(TbPipeline entity) {
        if (!lockPipeline(PipelineStatusEnum.STOPPING.code(), entity)) {
            return false;
        }
        boolean success = false;
        String message;
        OpUUID uuid = new OpUUID()
                .setObjType(OpObjTypeEnum.PIPELINE)
                .setObjId(entity.getId())
                .setOpId(IdUtil.genId())
                .setOpType(OpTypeEnum.STOP)
                .setOpTime(System.currentTimeMillis());
        try {
            PipelineCtx pipeline = pipelineCtx(entity);
            pipelinePlugin.stopReq(pipeline);
            LOGGER.info("stop pipeline {} {} on cluster {} {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    entity.getClusterId(),
                    pipeline.getCluster().getClusterName());
            JobStopParam param = genStopParam(pipeline);
            param.setUuid(uuid);
            String paramJson = JsonUtil.encode(param);
            beforeStop(param, pipeline);
            LOGGER.info("stop pipeline {} {} param {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    paramJson);
            JobStopResult result = genJaxManager(pipeline, OpTypeEnum.STOP).stop(param);
            String resultJson = JsonUtil.encode(result);
            LOGGER.info("stop pipeline {} {} result {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    resultJson);
            success = result.isSuccess();
            message = resultJson;
            pipeline = pipelineCtx(tbPipelineService.getById(entity.getId()));
            afterStop(result, pipeline);
        } catch (Throwable e) {
            LOGGER.error("stop pipeline {} {} failed",
                    entity.getId(),
                    entity.getPipelineName(),
                    e);
            success = false;
            message = ExceptionUtil.stacktraceToString(e);
        } finally {
            PipelineStatusEnum pipelineStatus = success ? PipelineStatusEnum.STOPPING : PipelineStatusEnum.STOP_FAILED;
            unlockPipeline(pipelineStatus.code(), entity);
        }
        savePipelineLog(uuid, message, entity);
        return success;
    }

    public boolean delete(TbPipeline entity) {
        if (!lockPipeline(PipelineStatusEnum.DELETING.code(), entity)) {
            return false;
        }
        boolean success = false;
        String message = "";
        OpUUID uuid = new OpUUID()
                .setObjType(OpObjTypeEnum.PIPELINE)
                .setObjId(entity.getId())
                .setOpId(IdUtil.genId())
                .setOpType(OpTypeEnum.DELETE)
                .setOpTime(System.currentTimeMillis());
        try {
            PipelineCtx pipeline = pipelineCtx(entity);
            pipelinePlugin.deleteReq(pipeline);
            LOGGER.info("delete pipeline {} {} on cluster {} {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    entity.getClusterId(),
                    pipeline.getCluster().getClusterName());
            JobStopParam param = genDeleteParam(pipeline);
            param.setUuid(uuid);
            String paramJson = JsonUtil.encode(param);
            beforeDelete(param, pipeline);
            LOGGER.info("delete pipeline {} {} param {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    paramJson);
            JobStopResult result = genJaxManager(pipeline, OpTypeEnum.DELETE).stop(param);
            String resultJson = JsonUtil.encode(result);
            LOGGER.info("delete pipeline {} {} result {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    resultJson);
            success = result.isSuccess();
            message = resultJson;
            pipeline = pipelineCtx(tbPipelineService.getById(entity.getId()));
            afterDelete(result, pipeline);
        } catch (Throwable e) {
            LOGGER.error("delete pipeline {} {} failed",
                    entity.getId(),
                    entity.getPipelineName(),
                    e);
            success = false;
            message = ExceptionUtil.stacktraceToString(e);
        } finally {
            PipelineStatusEnum pipelineStatus = success ? PipelineStatusEnum.DELETING : PipelineStatusEnum.DELETE_FAILED;
            unlockPipeline(pipelineStatus.code(), entity);
        }
        savePipelineLog(uuid, message, entity);
        pipelineService.clear(entity.getId());
        return success;
    }

    public boolean restart(TbPipeline entity) {
        LOGGER.info("restart pipeline {} {}",
                entity.getId(),
                entity.getPipelineName());
        boolean success = true;
        if (FlinkStatusEnum.isRunning(entity.getInternalStatus())
                || SparkStatusEnum.isRunning(entity.getInternalStatus())) {
            success = stop(entity);
            entity = tbPipelineService.getById(entity.getId());
        }
        if (success) {
            success = start(entity);
        }
        return success;
    }

    public void checkPipelineState(PipelineCtx pipelineCtx) {
        if (StrUtil.isNotEmpty(pipelineCtx.getPipeline().getYarnAppId())
                && pipelineCtx.getPipeline().getYarnSessionId() == null) {
            // yarn集群，非session模式，检查yarn app状态
            checkYarnAppState(pipelineCtx);
        }
    }

    public void checkYarnAppState(PipelineCtx pipelineCtx) {
        YarnAppReport yarnApp = null;
        // 轮询检查yarn app状态，等待yarn app退出（最多5s）
        long startMs = System.currentTimeMillis();
        for (int i = 0; i < 5; i++) {
            try {
                yarnApp = getYarnApp(pipelineCtx);
            } catch (Exception e) {
                LOGGER.warn("check pipeline {} {} {} yarn app failed",
                        pipelineCtx.getPipeline().getId(),
                        pipelineCtx.getPipeline().getPipelineName(),
                        pipelineCtx.getPipeline().getYarnAppId(),
                        e);
            }
            if (yarnApp == null || YarnStateEnum.isExit(yarnApp.getState())) {
                // yarn app 已经退出
                LOGGER.info("yarn app {} of pipeline {} {} has exited",
                        pipelineCtx.getPipeline().getYarnAppId(),
                        pipelineCtx.getPipeline().getId(),
                        pipelineCtx.getPipeline().getPipelineName());
                return;
            }
            if (System.currentTimeMillis() - startMs >= 5000) {
                // 最多等5s
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (Exception ignore) {
            }
            LOGGER.info("waiting for yarn app {} of pipeline {} {} to exit",
                    pipelineCtx.getPipeline().getYarnAppId(),
                    pipelineCtx.getPipeline().getId(),
                    pipelineCtx.getPipeline().getPipelineName());
        }
        if (yarnApp == null) {
            return;
        }
        LOGGER.info("got yarn app info for pipeline {} {} {} on yarn {} {} {}",
                pipelineCtx.getPipeline().getId(),
                pipelineCtx.getPipeline().getPipelineName(),
                pipelineCtx.getPipeline().getClusterId(),
                pipelineCtx.getCluster().getClusterName(),
                pipelineCtx.getPipeline().getYarnAppId(),
                JsonUtil.encode(yarnApp));
        if (!YarnStateEnum.isExit(yarnApp.getState())) {
            String error = "can not start pipeline "
                    + pipelineCtx.getPipeline().getId() + " "
                    + pipelineCtx.getPipeline().getPipelineName() +
                    " because of it is " + yarnApp.getState() +
                    " on yarn " + pipelineCtx.getPipeline().getClusterId() + " "
                    + pipelineCtx.getCluster().getClusterName() + " "
                    + pipelineCtx.getPipeline().getYarnAppId() + " "
                    + pipelineCtx.getPipeline().getTrackUrl();
            LOGGER.error(error);
            throw new JaxException(error);
        }
    }

    private void savePipelineJars(TbPipeline entity, List<PipelineJar> jars) {
        tbPipelineJarService.remove(
                new LambdaQueryWrapper<TbPipelineJar>().eq(TbPipelineJar::getPipelineId, entity.getId())
        );
        if (CollUtil.isEmpty(jars)) {
            return;
        }
        Date now = new Date();
        List<TbPipelineJar> list = new ArrayList<>();
        for (PipelineJar jar : jars) {
            TbPipelineJar item = new TbPipelineJar();
            item.setId(IdUtil.genId());
            item.setPipelineId(entity.getId());
            item.setJarName(jar.getJar().getJarName());
            item.setJarUuid(jar.getJar().getJarUuid());
            item.setCreateTime(now);
            item.setUpdateTime(now);
            item.setCreateUser(entity.getUpdateUser());
            item.setUpdateUser(entity.getUpdateUser());
            list.add(item);
        }
        tbPipelineJarService.saveBatch(list);
    }

    private void savePipelineLog(OpUUID uuid, String content, TbPipeline entity) {
        Date now = new Date();
        TbOperatorLog pipelineLog = new TbOperatorLog();
        pipelineLog.setId(IdUtil.genId());
        pipelineLog.setLogType(OpLogTypeEnum.AUDIT.code());
        pipelineLog.setObjType(uuid.getObjType().code());
        pipelineLog.setObjId(entity.getId());
        pipelineLog.setOpId(uuid.getOpId());
        pipelineLog.setOpType(uuid.getOpType().code());
        pipelineLog.setOpTime(new Date(uuid.getOpTime()));
        pipelineLog.setCreateTime(now);
        pipelineLog.setUpdateTime(now);
        pipelineLog.setCreateUser(entity.getUpdateUser());
        pipelineLog.setUpdateUser(entity.getUpdateUser());
        pipelineLog.setContent(content);
        tbOperatorLogService.save(pipelineLog);
    }

    private boolean lockPipeline(String toStatus, TbPipeline entity) {
        Date now = new Date();
        boolean updated = tbPipelineService.update(
                new LambdaUpdateWrapper<TbPipeline>()
                        // 清空内部状态
                        .set(TbPipeline::getInternalStatus, null)
                        // 状态
                        .set(TbPipeline::getPipelineStatus, toStatus)
                        // 锁定
                        .set(TbPipeline::getProcessing, 1)
                        // 锁定时间
                        .set(TbPipeline::getProcessTime, now)
                        // 未锁定
                        .isNull(TbPipeline::getProcessing)
                        // 作业Id
                        .eq(TbPipeline::getId, entity.getId())
                        // 未发生变化
                        .eq(TbPipeline::getPipelineStatus, entity.getPipelineStatus())
                        // 未发生变化
                        .eq(TbPipeline::getUpdateTime, entity.getUpdateTime())
        );
        if (updated) {
            LOGGER.info("lock and update {} {} status from {} to {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    entity.getPipelineStatus(),
                    toStatus
            );
        }
        return updated;
    }

    private boolean unlockPipeline(String toStatus, TbPipeline entity) {
        boolean updated = tbPipelineService.update(
                new LambdaUpdateWrapper<TbPipeline>()
                        // 状态
                        .set(TbPipeline::getPipelineStatus, toStatus)
                        // 释放锁
                        .set(TbPipeline::getProcessing, null)
                        // 作业Id
                        .eq(TbPipeline::getId, entity.getId())
        );
        if (updated) {
            LOGGER.info("unlock and update {} {} status from {} to {}",
                    entity.getId(),
                    entity.getPipelineName(),
                    entity.getPipelineStatus(),
                    toStatus
            );
            // 发布作业状态更新事件
            ContextHolder.publishEvent(new PipelineStatusUpdateEvent()
                    .setPipelineId(entity.getId())
                    .setPipelineStatus(toStatus)
                    .setProcessId(entity.getProcessId())
                    .setProcessType(entity.getProcessType())
            );
        }
        return updated;
    }
}
