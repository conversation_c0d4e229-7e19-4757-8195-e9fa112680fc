package com.eoi.jax.web.ingestion.model.jaxclusterfile;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbJaxClusterFile;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class JaxClusterFileQueryReq extends BaseQueryReq<TbJaxClusterFile> {

    private JaxClusterFileQueryFilterReq filter = new JaxClusterFileQueryFilterReq();
    private JaxClusterFileQuerySortReq sort = new JaxClusterFileQuerySortReq();

    @Override
    public IFilterReq<TbJaxClusterFile> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbJaxClusterFile> getSort() {
        return sort;
    }
}
