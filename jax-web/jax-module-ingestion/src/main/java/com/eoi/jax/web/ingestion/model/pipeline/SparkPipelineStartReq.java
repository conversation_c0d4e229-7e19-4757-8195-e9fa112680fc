package com.eoi.jax.web.ingestion.model.pipeline;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/24
 */
public class SparkPipelineStartReq {

    private Long processId;

    private List<VarProperty> varPool;

    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public List<VarProperty> getVarPool() {
        return varPool;
    }

    public void setVarPool(List<VarProperty> varPool) {
        this.varPool = varPool;
    }
}
