package com.eoi.jax.web.ingestion.model.jobgroup;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbJobGroup;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-18
 */

public class JobGroupCreateReq implements ICreateModel<TbJobGroup> {

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;


    /**
     * 分组描述
     */
    @Schema(description = "分组描述")
    private String description;


    /**
     * 分组作业类型(streaming,batch)
     */
    @Schema(description = "分组作业类型(streaming,batch)")
    @Pattern(regexp = "^streaming|batch$", message = "分组作业类型不存在")
    @NotNull(message = "作业类型不能为空")
    private String jobType;


    /**
     * 作业配置
     */
    @Schema(description = "作业配置")
    @NotNull(message = "作业配置不能为空")
    private Map<String, Object> jobConfig;

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public Map<String, Object> getJobConfig() {
        return jobConfig;
    }

    public void setJobConfig(Map<String, Object> jobConfig) {
        this.jobConfig = jobConfig;
    }

    @Override
    public String toString() {
        return "JobGroupCreateReq{" +
            ", groupName='" + groupName + '\'' +
            ", description='" + description + '\'' +
            ", jobType='" + jobType + '\'' +
            ", jobConfig=" + jobConfig +
            '}';
    }

    @Override
    public TbJobGroup toEntity() {
        TbJobGroup entity = ICreateModel.super.toEntity(new TbJobGroup());
        entity.setJobConfig(JsonUtil.encode(jobConfig));
        return entity;
    }
}
