package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.ingestion.model.monitor.JobManagerMonitorReq;
import com.eoi.jax.web.ingestion.model.monitor.JobManagerMonitorResp;
import com.eoi.jax.web.ingestion.model.monitor.PipelineRunInfo;

/**
 * <AUTHOR>
 * @date 2023/3/16
 */
public interface JobManagerMonitorService {

    /**
     * 读取Jobmanager的指标-从Prometheus
     * @param resp
     * @param url
     * @param exportedJob
     */
    void readJmMetricsFromExportedPrometheus(JobManagerMonitorResp resp, String url, String exportedJob);

    /**
     * 读取Jobmanager的历史指标
     * @param pipelineRunInfo
     * @param req
     * @param resp
     */
    void readJmHistory(PipelineRunInfo pipelineRunInfo, JobManagerMonitorReq req, JobManagerMonitorResp resp);

    /**
     * 读取Jobmanager的指标-从VictoriaMetrics
     * @param pipelineRunInfo
     * @param req
     * @param resp
     * @param url
     * @param jobName
     */
    void readJmMetricsFromVm(PipelineRunInfo pipelineRunInfo, JobManagerMonitorReq req,
                             JobManagerMonitorResp resp, String url, String jobName);

    /**
     * 实时抓取Jobmanager的指标
     * @param pipelineRunInfo
     * @param resp
     */
    void fetchJobmanagerMetrics(PipelineRunInfo pipelineRunInfo, JobManagerMonitorResp resp);
}
