package com.eoi.jax.web.ingestion.model.flinkmetric;

import com.eoi.jax.web.core.integration.model.prometheus.PromRangeData;

/**
 * @projectName: jax-super
 * @className: FlinkTmRangeMetrics
 * @description: com.eoi.jax.web.ingestion.model.flinkmetric.FlinkTmRangeMetrics
 * @author: jiaqing.he
 * @date: 2023/4/23 13:10
 * @version: 1.0
 */
public class FlinkJobRangeMetrics {

    private PromRangeData sourceRate;
    private PromRangeData sinkRate;

    private PromRangeData kafkaInputRate;
    private PromRangeData kafkaConsumeRate;
    private PromRangeData flinkKafkaOffsetRate;

    private PromMatrixData kafkaLag;
    private PromRangeData flinkKafkaLag;

    private PromRangeData backPressured;
    private PromRangeData inPoolUsage;
    private PromRangeData outPoolUsage;

    private PromRangeData cpuUsage;
    private PromRangeData cpuLoad;

    private PromRangeData fullGcRate;
    private PromRangeData youngGcRate;
    private PromRangeData gcThroughout;

    private PromRangeData heapUsed;
    private PromRangeData heapCommitted;
    private PromRangeData heapMax;

    private PromRangeData nonHeapUsed;
    private PromRangeData nonHeapCommitted;
    private PromRangeData nonHeapMax;

    private PromRangeData metaspaceUsed;
    private PromRangeData metaspaceCommitted;
    private PromRangeData metaspaceMax;

    private PromRangeData directUsed;
    private PromRangeData directTotal;

    private PromRangeData networkUsed;
    private PromRangeData networkTotal;

    public FlinkJobRangeMetrics() { }


    public FlinkJobRangeMetrics(
            PromRangeData sourceRate, PromRangeData sinkRate, PromRangeData kafkaInputRate,
            PromRangeData kafkaConsumeRate, PromRangeData flinkKafkaOffsetRate, PromMatrixData kafkaLag,
            PromRangeData flinkKafkaLag, PromRangeData backPressured, PromRangeData inPoolUsage, PromRangeData outPoolUsage,
            PromRangeData cpuUsage, PromRangeData cpuLoad, PromRangeData fullGcRate, PromRangeData youngGcRate,
            PromRangeData gcThroughout, PromRangeData heapUsed, PromRangeData heapCommitted, PromRangeData heapMax,
            PromRangeData nonHeapUsed, PromRangeData nonHeapCommitted, PromRangeData nonHeapMax,
            PromRangeData metaspaceUsed, PromRangeData metaspaceCommitted, PromRangeData metaspaceMax,
            PromRangeData directUsed, PromRangeData directTotal, PromRangeData networkUsed, PromRangeData networkTotal) {
        this.sourceRate = sourceRate;
        this.sinkRate = sinkRate;
        this.kafkaInputRate = kafkaInputRate;
        this.kafkaConsumeRate = kafkaConsumeRate;
        this.flinkKafkaOffsetRate = flinkKafkaOffsetRate;
        this.kafkaLag = kafkaLag;
        this.flinkKafkaLag = flinkKafkaLag;
        this.backPressured = backPressured;
        this.inPoolUsage = inPoolUsage;
        this.outPoolUsage = outPoolUsage;

        this.cpuUsage = cpuUsage;
        this.cpuLoad = cpuLoad;
        this.fullGcRate = fullGcRate;
        this.youngGcRate = youngGcRate;
        this.gcThroughout = gcThroughout;
        this.heapUsed = heapUsed;
        this.heapCommitted = heapCommitted;
        this.heapMax = heapMax;
        this.nonHeapUsed = nonHeapUsed;
        this.nonHeapCommitted = nonHeapCommitted;
        this.nonHeapMax = nonHeapMax;
        this.metaspaceUsed = metaspaceUsed;
        this.metaspaceCommitted = metaspaceCommitted;
        this.metaspaceMax = metaspaceMax;
        this.directUsed = directUsed;
        this.directTotal = directTotal;
        this.networkUsed = networkUsed;
        this.networkTotal = networkTotal;
    }

    public PromRangeData getCpuUsage() {
        return cpuUsage;
    }

    public void setCpuUsage(PromRangeData cpuUsage) {
        this.cpuUsage = cpuUsage;
    }

    public PromRangeData getCpuLoad() {
        return cpuLoad;
    }

    public void setCpuLoad(PromRangeData cpuLoad) {
        this.cpuLoad = cpuLoad;
    }

    public PromRangeData getSourceRate() {
        return sourceRate;
    }

    public void setSourceRate(PromRangeData sourceRate) {
        this.sourceRate = sourceRate;
    }

    public PromRangeData getSinkRate() {
        return sinkRate;
    }

    public void setSinkRate(PromRangeData sinkRate) {
        this.sinkRate = sinkRate;
    }

    public PromRangeData getKafkaInputRate() {
        return kafkaInputRate;
    }

    public void setKafkaInputRate(PromRangeData kafkaInputRate) {
        this.kafkaInputRate = kafkaInputRate;
    }

    public PromRangeData getKafkaConsumeRate() {
        return kafkaConsumeRate;
    }

    public void setKafkaConsumeRate(PromRangeData kafkaConsumeRate) {
        this.kafkaConsumeRate = kafkaConsumeRate;
    }

    public PromRangeData getFlinkKafkaOffsetRate() {
        return flinkKafkaOffsetRate;
    }

    public void setFlinkKafkaOffsetRate(PromRangeData flinkKafkaOffsetRate) {
        this.flinkKafkaOffsetRate = flinkKafkaOffsetRate;
    }

    public PromMatrixData getKafkaLag() {
        return kafkaLag;
    }

    public void setKafkaLag(PromMatrixData kafkaLag) {
        this.kafkaLag = kafkaLag;
    }

    public PromRangeData getFlinkKafkaLag() {
        return flinkKafkaLag;
    }

    public void setFlinkKafkaLag(PromRangeData flinkKafkaLag) {
        this.flinkKafkaLag = flinkKafkaLag;
    }

    public PromRangeData getBackPressured() {
        return backPressured;
    }

    public void setBackPressured(PromRangeData backPressured) {
        this.backPressured = backPressured;
    }

    public PromRangeData getInPoolUsage() {
        return inPoolUsage;
    }

    public void setInPoolUsage(PromRangeData inPoolUsage) {
        this.inPoolUsage = inPoolUsage;
    }

    public PromRangeData getOutPoolUsage() {
        return outPoolUsage;
    }

    public void setOutPoolUsage(PromRangeData outPoolUsage) {
        this.outPoolUsage = outPoolUsage;
    }

    public PromRangeData getFullGcRate() {
        return fullGcRate;
    }

    public void setFullGcRate(PromRangeData fullGcRate) {
        this.fullGcRate = fullGcRate;
    }

    public PromRangeData getYoungGcRate() {
        return youngGcRate;
    }

    public void setYoungGcRate(PromRangeData youngGcRate) {
        this.youngGcRate = youngGcRate;
    }

    public PromRangeData getGcThroughout() {
        return gcThroughout;
    }

    public void setGcThroughout(PromRangeData gcThroughout) {
        this.gcThroughout = gcThroughout;
    }

    public PromRangeData getHeapUsed() {
        return heapUsed;
    }

    public void setHeapUsed(PromRangeData heapUsed) {
        this.heapUsed = heapUsed;
    }

    public PromRangeData getHeapCommitted() {
        return heapCommitted;
    }

    public void setHeapCommitted(PromRangeData heapCommitted) {
        this.heapCommitted = heapCommitted;
    }

    public PromRangeData getHeapMax() {
        return heapMax;
    }

    public void setHeapMax(PromRangeData heapMax) {
        this.heapMax = heapMax;
    }

    public PromRangeData getNonHeapUsed() {
        return nonHeapUsed;
    }

    public void setNonHeapUsed(PromRangeData nonHeapUsed) {
        this.nonHeapUsed = nonHeapUsed;
    }

    public PromRangeData getNonHeapCommitted() {
        return nonHeapCommitted;
    }

    public void setNonHeapCommitted(PromRangeData nonHeapCommitted) {
        this.nonHeapCommitted = nonHeapCommitted;
    }

    public PromRangeData getNonHeapMax() {
        return nonHeapMax;
    }

    public void setNonHeapMax(PromRangeData nonHeapMax) {
        this.nonHeapMax = nonHeapMax;
    }

    public PromRangeData getMetaspaceUsed() {
        return metaspaceUsed;
    }

    public void setMetaspaceUsed(PromRangeData metaspaceUsed) {
        this.metaspaceUsed = metaspaceUsed;
    }

    public PromRangeData getMetaspaceCommitted() {
        return metaspaceCommitted;
    }

    public void setMetaspaceCommitted(PromRangeData metaspaceCommitted) {
        this.metaspaceCommitted = metaspaceCommitted;
    }

    public PromRangeData getMetaspaceMax() {
        return metaspaceMax;
    }

    public void setMetaspaceMax(PromRangeData metaspaceMax) {
        this.metaspaceMax = metaspaceMax;
    }

    public PromRangeData getDirectUsed() {
        return directUsed;
    }

    public void setDirectUsed(PromRangeData directUsed) {
        this.directUsed = directUsed;
    }

    public PromRangeData getDirectTotal() {
        return directTotal;
    }

    public void setDirectTotal(PromRangeData directTotal) {
        this.directTotal = directTotal;
    }

    public PromRangeData getNetworkUsed() {
        return networkUsed;
    }

    public void setNetworkUsed(PromRangeData networkUsed) {
        this.networkUsed = networkUsed;
    }

    public PromRangeData getNetworkTotal() {
        return networkTotal;
    }

    public void setNetworkTotal(PromRangeData networkTotal) {
        this.networkTotal = networkTotal;
    }
}
