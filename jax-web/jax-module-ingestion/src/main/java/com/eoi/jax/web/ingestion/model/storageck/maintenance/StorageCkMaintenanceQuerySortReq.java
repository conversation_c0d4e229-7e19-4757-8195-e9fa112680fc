package com.eoi.jax.web.ingestion.model.storageck.maintenance;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbStorageCk;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class StorageCkMaintenanceQuerySortReq implements ISortReq<TbStorageCk> {

    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbStorageCk> order(QueryWrapper<TbStorageCk> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbStorageCk::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbStorageCk::getUpdateTime);
        return wrapper;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
