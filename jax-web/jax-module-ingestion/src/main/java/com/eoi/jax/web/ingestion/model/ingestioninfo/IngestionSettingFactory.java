package com.eoi.jax.web.ingestion.model.ingestioninfo;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.enumrate.IngestionTypeEnum;
import com.fasterxml.jackson.core.type.TypeReference;

/**
 * <AUTHOR>
 * @date 2023/2/14
 */
public final class IngestionSettingFactory {

    private IngestionSettingFactory() {

    }

    /**
     * 根据类型获取setting
     *
     * @param ingestionType IngestionTypeEnum
     * @return
     */
    @SuppressWarnings("checkstyle:ReturnCount")
    public static AbstractSetting getSettingByType(String ingestionType) {
        if (IngestionTypeEnum.FILE.code().equalsIgnoreCase(ingestionType)) {
            return new FileSyncSetting();
        } else if (IngestionTypeEnum.TCP_OR_UDP.code().equalsIgnoreCase(ingestionType)) {
            return new TcpUdpSyncSetting();
        } else if (IngestionTypeEnum.SYSLOG.code().equalsIgnoreCase(ingestionType)) {
            return new SyslogSyncSetting();
        } else if (IngestionTypeEnum.KAFKA.code().equals(ingestionType)) {
            return new KafkaSetting();
        } else if (IngestionTypeEnum.ARCHIVE.code().equals(ingestionType)) {
            return new ArchiveSyncSetting();
        } else if (IngestionTypeEnum.WIN_EVENT_LOG.code().equals(ingestionType)) {
            return new WinEventLogSetting();
        } else if (IngestionTypeEnum.JDBC.code().equals(ingestionType)) {
            return new JdbcSetting();
        } else if (IngestionTypeEnum.LOGSTASH.code().equals(ingestionType)) {
            return new LogstashSetting();
        }
        throw new IllegalArgumentException("不支持的ingestionType");
    }

    /**
     * 根据类型获取setting
     *
     * @param ingestionType IngestionTypeEnum
     * @param setting       json字符串
     * @return AbstractSetting
     */
    @SuppressWarnings("checkstyle:ReturnCount")
    public static AbstractSetting getSettingByType(String ingestionType, String setting) {
        if (IngestionTypeEnum.FILE.code().equalsIgnoreCase(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<FileSyncSetting>() {
            });
        } else if (IngestionTypeEnum.TCP_OR_UDP.code().equalsIgnoreCase(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<TcpUdpSyncSetting>() {
            });
        } else if (IngestionTypeEnum.SYSLOG.code().equalsIgnoreCase(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<SyslogSyncSetting>() {
            });
        } else if (IngestionTypeEnum.KAFKA.code().equals(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<KafkaSetting>() {
            });
        } else if (IngestionTypeEnum.ARCHIVE.code().equals(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<ArchiveSyncSetting>() {
            });
        } else if (IngestionTypeEnum.WIN_EVENT_LOG.code().equals(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<WinEventLogSetting>() {
            });
        } else if (IngestionTypeEnum.JDBC.code().equals(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<JdbcSetting>() {
            });
        } else if (IngestionTypeEnum.LOGSTASH.code().equals(ingestionType)) {
            return JsonUtil.decode(setting, new TypeReference<LogstashSetting>() {
            });
        }
        throw new IllegalArgumentException("不支持的ingestionType");
    }

}
