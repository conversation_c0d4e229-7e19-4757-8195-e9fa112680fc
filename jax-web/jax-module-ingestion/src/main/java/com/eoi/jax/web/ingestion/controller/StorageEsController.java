package com.eoi.jax.web.ingestion.controller;


import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpParameters;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.ingestion.model.storagees.StorageEsCreateReq;
import com.eoi.jax.web.ingestion.model.storagees.StorageEsQueryReq;
import com.eoi.jax.web.ingestion.model.storagees.StorageEsUpdateReq;
import com.eoi.jax.web.ingestion.service.StorageEsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * ES存储作业 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2023-03-07
 */
@RestController
public class StorageEsController implements V2Controller {

    @Autowired
    private StorageEsService storageEsService;

    @Operation(summary = "es存储作业全部查询")
    @GetMapping("ingestion/storage-es/list")
    public Response list() {
        return Response.success(storageEsService.all());
    }

    @Operation(summary = "es存储作业分页查询")
    @PostMapping("ingestion/storage-es/query")
    public Response query(@Valid @RequestBody StorageEsQueryReq req) {
        return Response.success(storageEsService.query(req));
    }

    @Operation(summary = "根据id查询详情")
    @GetMapping("ingestion/storage-es/{id}")
    public Response get(@Parameter(description = "es存储作业id", required = true) @PathVariable("id") Long id) {
        return Response.success(storageEsService.get(id));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.CREATE, module = "数据开发", function = "实时开发", code = "storage-es")
    @Operation(summary = "es存储作业创建")
    @PostMapping("ingestion/storage-es")
    public Response create(@OpPrimaryName(name = "name") @OpParameters
                           @Valid @RequestBody StorageEsCreateReq req) {
        return Response.success(storageEsService.create(req));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.UPDATE, module = "数据开发", function = "实时开发", code = "storage-es")
    @Operation(summary = "es存储作业更新")
    @PutMapping("ingestion/storage-es/{id}")
    public Response update(
            @OpPrimaryKey @Parameter(description = "es存储作业主键id", required = true) @PathVariable("id") Long id,
            @OpParameters @Valid @RequestBody StorageEsUpdateReq req) {
        req.setId(id);
        return Response.success(storageEsService.update(req));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.DELETE, module = "数据开发", function = "实时开发", code = "storage-es")
    @Operation(summary = "es存储作业删除")
    @DeleteMapping("ingestion/storage-es/{id}")
    public Response delete(
            @OpPrimaryKey(name = "id")
            @Parameter(description = "es存储作业主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(storageEsService.delete(id));
    }

    @Operation(summary = "预览匹配的检测任务")
    @PostMapping("ingestion/storage-es/preview-matched-check-task")
    public Response previewMatchedCheckTask(@Valid @RequestBody StorageEsUpdateReq req) {
        return Response.success(storageEsService.previewMatchedCheckTask(req));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.PUBLISH, module = "数据开发", function = "实时开发", code = "storage-es")
    @Operation(summary = "es存储作业创建并发布")
    @PostMapping("ingestion/storage-es/publish")
    public Response createPublish(
            @OpPrimaryName(name = "name") @OpParameters
            @Valid @RequestBody StorageEsCreateReq req) {
        return Response.success(storageEsService.publish(req));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.PUBLISH, module = "数据开发", function = "实时开发", code = "storage-es")
    @Operation(summary = "es存储作业更新并发布")
    @PutMapping("ingestion/storage-es/{id}/publish")
    public Response updatePublish(
            @OpPrimaryKey(name = "id")
            @Parameter(description = "作业id", required = true) @PathVariable("id") Long id,
            @OpParameters @Valid @RequestBody StorageEsUpdateReq req) {
        req.setId(id);
        return Response.success(storageEsService.publish(req));
    }

    @Operation(summary = "kafka作业配置")
    @GetMapping("ingestion/storage-es/kafka-job-config")
    public Response getSourceKafkaJobConfig() {
        return Response.success(storageEsService.getSourceKafkaJobConfig());
    }

    @Operation(summary = "es作业配置")
    @GetMapping("ingestion/storage-es/es-job-config")
    public Response getSinkElasticSearchJobConfig() {
        return Response.success(storageEsService.getSinkElasticSearchJobConfig());
    }
}

