package com.eoi.jax.web.ingestion.model.storageck.maintenance;

import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class StorageCkMetricReq {

    @Schema(description = "ck存储作业id")
    private Long storageCkId;

    @Schema(description = "开始时间,格式：yyyy-MM-dd'T'HH:mm:ss.SSSZZZ")
    @NotBlank(message = "开始时间不能为空")
    private String beginTime;

    @Schema(description = "结束时间,格式：yyyy-MM-dd'T'HH:mm:ss.SSSZZZ")
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    @Schema(description = "图表组件宽度，单位：px")
    @NotNull(message = "图表组件宽度不能为空")
    @Min(value = 1, message = "图表组件宽度必须大于等于1")
    private Integer width;

    public Long getStorageCkId() {
        return storageCkId;
    }

    public void setStorageCkId(Long storageCkId) {
        this.storageCkId = storageCkId;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }
}
