package com.eoi.jax.web.ingestion.model.ingestioninfo;

import cn.hutool.core.lang.Assert;
import com.eoi.jax.web.core.util.ValidatorUtils;
import com.eoi.jax.web.core.common.enumrate.IngestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * @param <S> Setting对象
 * <AUTHOR>
 * @date 2023/1/13
 */
public class IngestionInfoCreateReq<S extends AbstractSetting> {
    @NotNull(message = "业务流程id不能为空")
    @Schema(description = "业务流程id")
    private Long businessFlowId;
    @NotNull(message = "网关id不能为空")
    @Schema(description = "网关id")
    private Long cellId;
    @Schema(description = "接入网关id")
    private Long routerId;
    @NotBlank(message = "数据集成名称不能为空")
    @Schema(description = "数据集成名称")
    private String name;
    @Schema(description = "数据集成类型")
    @NotBlank(message = "数据集成类型不能为空")
    private String ingestionType;
    /**
     * 模型表Id
     */
    @Schema(description = "模型表Id")
    private Long tbId;

    @Schema(description = "配置")
    @NotNull(message = "配置不能为空")
    private S setting;

    @Schema(description = "agent配置")
    private List<AgentSetting<S>> agentSettingList;

    public void valid() {
        ValidatorUtils.validateEntity(this);
        if (!IngestionTypeEnum.LOGSTASH.code().equals(this.getIngestionType())) {
            Assert.notNull(this.getTbId(), "输出模型不能为空");
        }
        Assert.notEmpty(this.getAgentSettingList(), "agent配置不能为空");
        ValidatorUtils.validateEntity(this.getSetting().getInput());
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public Long getCellId() {
        return cellId;
    }

    public void setCellId(Long cellId) {
        this.cellId = cellId;
    }

    public Long getRouterId() {
        return routerId;
    }

    public void setRouterId(Long routerId) {
        this.routerId = routerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIngestionType() {
        return ingestionType;
    }

    public void setIngestionType(String ingestionType) {
        this.ingestionType = ingestionType;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public S getSetting() {
        return setting;
    }

    public void setSetting(S setting) {
        this.setting = setting;
    }

    public List<AgentSetting<S>> getAgentSettingList() {
        return agentSettingList;
    }

    public void setAgentSettingList(List<AgentSetting<S>> agentSettingList) {
        this.agentSettingList = agentSettingList;
    }

    @Override
    public String toString() {
        return "IngestionInfoCreateReq{" +
                "businessFlowId=" + businessFlowId +
                ", cellId=" + cellId +
                ", name='" + name + '\'' +
                ", ingestionType='" + ingestionType + '\'' +
                ", tbId=" + tbId +
                ", setting=" + setting +
                ", agentSettingList=" + agentSettingList +
                '}';
    }
}
