package com.eoi.jax.web.ingestion.model.datacenter.mapping;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbDataCenterMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class DataCenterMappingUpdateReq implements IUpdateModel<TbDataCenterMapping> {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "关联表类型")
    @NotBlank(message = "关联表类型不能为空")
    private String type;

    @Schema(description = "关联表id")
    @NotBlank(message = "关联表id不能为空")
    private Long mappingId;

    @Schema(description = "数据中心id")
    @NotBlank(message = "数据中心id不能为空")
    private Long dataCenterId;

    @Override
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getMappingId() {
        return mappingId;
    }

    public void setMappingId(Long mappingId) {
        this.mappingId = mappingId;
    }

    public Long getDataCenterId() {
        return dataCenterId;
    }

    public void setDataCenterId(Long dataCenterId) {
        this.dataCenterId = dataCenterId;
    }
}
