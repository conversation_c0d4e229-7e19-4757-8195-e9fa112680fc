package com.eoi.jax.web.ingestion.util;

import cn.hutool.core.lang.Assert;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;

import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/4/3
 */
public final class VelocityUtil {

    private static final VelocityEngine VE;

    static {
        try {
            final Properties properties = new Properties();
            // 在classPath目录下获取模板文件
            properties.setProperty(Velocity.RESOURCE_LOADERS, Velocity.RESOURCE_LOADER_CLASS);
            properties.setProperty("resource.loader.class.class",
                    org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader.class.getName());
            properties.setProperty(Velocity.FILE_RESOURCE_LOADER_CACHE, Boolean.FALSE.toString());
            properties.setProperty(Velocity.INPUT_ENCODING, StandardCharsets.UTF_8.name());
            VE = new VelocityEngine(properties);
            VE.init();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private VelocityUtil() {


    }

    /**
     * @param templateFileName
     * @param context
     * @return
     * @throws Exception
     */
    public static String getContent(String templateFileName, VelocityContext context) throws Exception {
        Assert.notBlank(templateFileName, "模板文件不能为空");
        Assert.notNull(context, "VelocityContext不能为空");
        //实例化一个StringWriter
        StringWriter writer = new StringWriter();
        VE.mergeTemplate(templateFileName, StandardCharsets.UTF_8.name(), context, writer);
        return writer.toString();
    }


}
