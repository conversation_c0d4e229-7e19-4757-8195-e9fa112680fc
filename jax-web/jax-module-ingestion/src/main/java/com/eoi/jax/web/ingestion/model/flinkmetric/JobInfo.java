package com.eoi.jax.web.ingestion.model.flinkmetric;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @projectName: jax-super
 * @className: JobEntry
 * @description: com.eoi.jax.web.ingestion.model.flinkmetric.JobEntry
 * @author: jiaqing.he
 * @date: 2023/4/7 16:23
 * @version: 1.0
 */
public class JobInfo {

    private String jobId;
    private String prometheusUrl;

    private String jobName;
    private String appResource;
    private String jmJvmPid;

    private int parallelism;
    private int tmSize;
    private int taskSize;
    private int operatorSize;

    private Set<String> tmIdSet = new HashSet<String>();
    private Set<String> taskIdSet = new HashSet<String>();
    private Set<String> operatorIdSet = new HashSet<String>();
    private Set<String> subtaskIndexSet = new HashSet<String>();

    private Map<String, TmCpuMemory> tmCpuMemoryMap;

    public void addTmId(String key) {
        this.tmIdSet.add(key);
    }

    public void addTaskId(String taskId) {
        this.taskIdSet.add(taskId);
    }

    public void addOperatorId(String key) {
        this.operatorIdSet.add(key);
    }

    public void addSubtaskIndex(String key) {
        this.subtaskIndexSet.add(key);
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getPrometheusUrl() {
        return prometheusUrl;
    }

    public void setPrometheusUrl(String prometheusUrl) {
        this.prometheusUrl = prometheusUrl;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getAppResource() {
        return appResource;
    }

    public void setAppResource(String appResource) {
        this.appResource = appResource;
    }

    public String getJmJvmPid() {
        return jmJvmPid;
    }

    public void setJmJvmPid(String jmJvmPid) {
        this.jmJvmPid = jmJvmPid;
    }

    public int getParallelism() {
        return parallelism;
    }

    public void setParallelism(int parallelism) {
        this.parallelism = parallelism;
    }

    public int getTmSize() {
        return tmSize;
    }

    public void setTmSize(int tmSize) {
        this.tmSize = tmSize;
    }

    public int getTaskSize() {
        return taskSize;
    }

    public void setTaskSize(int taskSize) {
        this.taskSize = taskSize;
    }

    public int getOperatorSize() {
        return operatorSize;
    }

    public void setOperatorSize(int operatorSize) {
        this.operatorSize = operatorSize;
    }

    public Set<String> getTmIdSet() {
        return tmIdSet;
    }

    public void setTmIdSet(Set<String> tmIdSet) {
        this.tmIdSet = tmIdSet;
    }

    public Set<String> getTaskIdSet() {
        return taskIdSet;
    }

    public void setTaskIdSet(Set<String> taskIdSet) {
        this.taskIdSet = taskIdSet;
    }

    public Set<String> getOperatorIdSet() {
        return operatorIdSet;
    }

    public void setOperatorIdSet(Set<String> operatorIdSet) {
        this.operatorIdSet = operatorIdSet;
    }

    public Set<String> getSubtaskIndexSet() {
        return subtaskIndexSet;
    }

    public void setSubtaskIndexSet(Set<String> subtaskIndexSet) {
        this.subtaskIndexSet = subtaskIndexSet;
    }

    public Map<String, TmCpuMemory> getTmCpuMemoryMap() {
        return tmCpuMemoryMap;
    }

    public void setTmCpuMemoryMap(Map<String, TmCpuMemory> tmCpuMemoryMap) {
        this.tmCpuMemoryMap = tmCpuMemoryMap;
    }
}
