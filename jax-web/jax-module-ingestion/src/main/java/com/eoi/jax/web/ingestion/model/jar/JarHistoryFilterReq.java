package com.eoi.jax.web.ingestion.model.jar;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbJarHistory;

/**
 * <AUTHOR>
 * @Date 2024/1/18
 */
public class JarHistoryFilterReq implements IFilterReq<TbJarHistory> {
    private String search;
    private Long jarId;
    private String jarName;
    private String jarType;
    private String jarVersion;

    @Override
    public QueryWrapper<TbJarHistory> where(QueryWrapper<TbJarHistory> wrapper) {
        wrapper.lambda()
                .and(StrUtil.isNotBlank(search), q -> q
                        .like(TbJarHistory::getJarName, search)
                        .or().like(TbJarHistory::getJarType, search)
                        .or().like(TbJarHistory::getJarFile, search)
                        .or().like(TbJarHistory::getJarDescription, search))
                .eq(jarId != null, TbJarHistory::getJarId, jarId)
                .like(StrUtil.isNotBlank(jarName), TbJarHistory::getJarName, jarName)
                .like(StrUtil.isNotBlank(jarType), TbJarHistory::getJarType, jarType)
                .like(StrUtil.isNotBlank(jarVersion), TbJarHistory::getJarVersion, jarVersion);
        return wrapper;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Long getJarId() {
        return jarId;
    }

    public void setJarId(Long jarId) {
        this.jarId = jarId;
    }

    public String getJarName() {
        return jarName;
    }

    public void setJarName(String jarName) {
        this.jarName = jarName;
    }

    public String getJarType() {
        return jarType;
    }

    public void setJarType(String jarType) {
        this.jarType = jarType;
    }

    public String getJarVersion() {
        return jarVersion;
    }

    public void setJarVersion(String jarVersion) {
        this.jarVersion = jarVersion;
    }
}
