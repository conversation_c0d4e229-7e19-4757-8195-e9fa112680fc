package com.eoi.jax.web.ingestion.model.cellagent;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
public class CellAgentListQueryFilterReq {

    @NotNull(message = "采集网关id不能为空")
    @Schema(description = "采集网关")
    private Long cellId;

    @Schema(description = "采集类型:file、archive、tcp、udp、mseventlog、kafka、jdbc、syslog、script")
    private String capability;

    @Schema(description = "主机名，或IP")
    private String hostnameOrIp;

    @Schema(description = "操作系统：Linux、Windows、AIX、HPUX")
    private String osAlias;

    @Schema(description = "排除agent列表")
    private List<Long> excludeIds;

    @Schema(description = "标签id")
    private List<Long> groups;

    @Schema(description = "采集器状态")
    private Integer connection;

    public Long getCellId() {
        return cellId;
    }

    public void setCellId(Long cellId) {
        this.cellId = cellId;
    }

    public String getCapability() {
        return capability;
    }

    public void setCapability(String capability) {
        this.capability = capability;
    }

    public String getHostnameOrIp() {
        return hostnameOrIp;
    }

    public void setHostnameOrIp(String hostnameOrIp) {
        this.hostnameOrIp = hostnameOrIp;
    }

    public String getOsAlias() {
        return osAlias;
    }

    public void setOsAlias(String osAlias) {
        this.osAlias = osAlias;
    }

    public List<Long> getExcludeIds() {
        return excludeIds;
    }

    public void setExcludeIds(List<Long> excludeIds) {
        this.excludeIds = excludeIds;
    }

    public List<Long> getGroups() {
        return groups;
    }

    public void setGroups(List<Long> groups) {
        this.groups = groups;
    }

    public Integer getConnection() {
        return connection;
    }

    public void setConnection(Integer connection) {
        this.connection = connection;
    }
}
