package com.eoi.jax.web.ingestion.provider.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.manager.api.UUID;
import com.eoi.jax.manager.process.LineHandler;
import com.eoi.jax.web.ingestion.enumrate.OpLogTypeEnum;
import com.eoi.jax.web.repository.entity.TbOperatorLog;
import com.eoi.jax.web.repository.service.TbOperatorLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/15
 */
@Component
public class OpLogLineQueue implements LineHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpLogLineQueue.class);
    private static final BlockingQueue<OpLogLine> QUEUE = new LinkedBlockingQueue<>(100000);
    private static final Long RETENTION_MS = 7 * 24 * 3600 * 1000L;

    @Autowired
    @Lazy
    private TbOperatorLogService tbOperatorLogService;

    @Override
    public void handleLine(UUID uuid, String line) {
        try {
            OpLogLine log = new OpLogLine();
            if (uuid instanceof OpUUID) {
                log.setUuid((OpUUID) uuid);
                log.setContent(line);
                put(log);
            }
        } catch (Exception e) {
            LOGGER.warn("console log error", e);
        }
    }

    public void retention() {
        Date expired = new Date(System.currentTimeMillis() - RETENTION_MS);
        tbOperatorLogService.remove(
                new LambdaQueryWrapper<TbOperatorLog>()
                        .eq(TbOperatorLog::getLogType, OpLogTypeEnum.CONSOLE.code())
                        .lt(TbOperatorLog::getOpTime, expired)
        );
    }

    public void persist() {
        try {
            while (!QUEUE.isEmpty()) {
                List<OpLogLine> batch = new ArrayList<>();
                while (!QUEUE.isEmpty() && batch.size() <= 1000) {
                    OpLogLine console = take();
                    if (console == null) {
                        break;
                    }
                    batch.add(console);
                }
                if (CollUtil.isNotEmpty(batch)) {
                    saveBatch(batch);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("persist console log error", e);
        }
    }

    private void saveBatch(List<OpLogLine> batch) {
        List<TbOperatorLog> list = batch.stream().map(i -> toEntity(i)).collect(Collectors.toList());
        tbOperatorLogService.saveBatch(list);
    }

    private TbOperatorLog toEntity(OpLogLine log) {
        Date now = new Date();
        OpUUID uuid = log.getUuid();
        TbOperatorLog entity = new TbOperatorLog();
        entity.setLogType(OpLogTypeEnum.CONSOLE.code());
        entity.setObjType(uuid.getObjType().code());
        entity.setObjId(uuid.getObjId());
        entity.setOpId(uuid.getOpId());
        entity.setOpType(uuid.getOpType().code());
        entity.setOpTime(new Date(uuid.getOpTime()));
        entity.setContent(log.getContent());
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        return entity;
    }

    private OpLogLine take() throws Exception {
        //尽量有值的情况下取值，没有值直接返回null
        if (QUEUE.isEmpty()) {
            return null;
        }
        return QUEUE.poll(1, TimeUnit.SECONDS);
    }

    private void put(OpLogLine log) throws Exception {
        //尽量在有容的情况下插入，没容量直接丢弃
        if (QUEUE.remainingCapacity() > 0) {
            QUEUE.offer(log, 1, TimeUnit.SECONDS);
        }
    }
}
