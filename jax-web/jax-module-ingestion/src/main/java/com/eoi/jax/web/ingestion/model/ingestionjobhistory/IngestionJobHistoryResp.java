package com.eoi.jax.web.ingestion.model.ingestionjobhistory;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.repository.entity.TbIngestionJobHistory;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
public class IngestionJobHistoryResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbIngestionJobHistory> {

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "id")
    private Long id;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "数据集成jobId")
    private Long jobId;

    /**
     * 数据集成id
     */
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "数据集成id")
    private Long ingestionId;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String name;

    /**
     * 数据集成类型
     */
    @Schema(description = "数据集成类型")
    private String ingestionType;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 采集网关id
     */
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "采集网关id")
    private Long cellId;

    /**
     * 配置
     */
    @Schema(description = "配置")
    private String setting;

    /**
     * 模型表Id
     */
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "模型表Id")
    @NotNull(message = "模型表Id不能为空")
    private Long tbId;

    /**
     * 发布版本
     */
    @Schema(description = "发布版本")
    private Long publishVersion;

    /**
     * 操作
     */
    @Schema(description = "操作")
    private String operation;

    /**
     * 运行状态
     */
    @Schema(description = "运行状态")
    private String status;

    /**
     * 任务状态统计
     */
    @Schema(description = "任务状态统计")
    private String taskStatusStatistics;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    @Schema(description = "网关名称")
    private String cellName;

    @Schema(description = "业务流程名称")
    private String businessFlowName;


    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getIngestionId() {
        return ingestionId;
    }

    public void setIngestionId(Long ingestionId) {
        this.ingestionId = ingestionId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIngestionType() {
        return ingestionType;
    }

    public void setIngestionType(String ingestionType) {
        this.ingestionType = ingestionType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCellId() {
        return cellId;
    }

    public void setCellId(Long cellId) {
        this.cellId = cellId;
    }

    public String getSetting() {
        return setting;
    }

    public void setSetting(String setting) {
        this.setting = setting;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public Long getPublishVersion() {
        return publishVersion;
    }

    public void setPublishVersion(Long publishVersion) {
        this.publishVersion = publishVersion;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaskStatusStatistics() {
        return taskStatusStatistics;
    }

    public void setTaskStatusStatistics(String taskStatusStatistics) {
        this.taskStatusStatistics = taskStatusStatistics;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    @Override
    public String toString() {
        return "IngestionJobResp{" +
                "id=" + id +
                ", ingestionId=" + ingestionId +
                ", name='" + name + '\'' +
                ", ingestionType='" + ingestionType + '\'' +
                ", description='" + description + '\'' +
                ", cellId=" + cellId +
                ", setting='" + setting + '\'' +
                ", tbId=" + tbId +
                ", publishVersion=" + publishVersion +
                ", operation='" + operation + '\'' +
                ", status='" + status + '\'' +
                ", taskStatusStatistics='" + taskStatusStatistics + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                ", cellName='" + cellName + '\'' +
                ", businessFlowName='" + businessFlowName + '\'' +
                '}';
    }
}
