package com.eoi.jax.web.ingestion.model.blueking;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/4
 */
public class BluekingBaseReq {

    @Schema(description = "蓝鲸数据源id")
    @NotNull(message = "蓝鲸数据源id不能为空")
    private Long dsId;

    public Long getDsId() {
        return dsId;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

}
