package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.ingestion.model.taskchain.TaskChainQueryReq;
import com.eoi.jax.web.ingestion.model.taskchain.TaskChainQueryResp;

/**
 * @Author: tangy
 * @Date: 2023/4/20
 * @Desc:
 **/
public interface PipelineTaskChainService {

    /**
     * 根据任务id获取调用链路
     *
     * @param id
     * @return
     */
    TaskChainQueryResp taskChain(TaskChainQueryReq id);
}
