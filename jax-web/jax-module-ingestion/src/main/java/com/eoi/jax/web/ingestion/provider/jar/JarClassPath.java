package com.eoi.jax.web.ingestion.provider.jar;

import cn.hutool.core.util.StrUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/28
 */
public class JarClassPath {
    private String name;
    private List<String> paths;

    public JarClassPath(String name, List<String> paths) {
        this.name = name;
        this.paths = paths;
    }

    public String getName() {
        return name;
    }

    public List<String> getPaths() {
        return paths;
    }

    public String genClassPath() {
        return StrUtil.join(":", getPaths().stream().sorted().collect(Collectors.toList()));
    }

    /**
     * 只关注paths字段
     */
    @Override
    public String toString() {
        return genClassPath();
    }

    /**
     * 只关注paths字段
     */
    @Override
    public int hashCode() {
        return (genClassPath()).hashCode();
    }

    /**
     * 只关注paths字段
     */
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof JarClassPath)) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        return this.hashCode() == obj.hashCode();
    }
}
