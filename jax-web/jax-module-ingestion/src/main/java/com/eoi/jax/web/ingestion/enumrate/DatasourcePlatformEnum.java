package com.eoi.jax.web.ingestion.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public enum DatasourcePlatformEnum implements ICodeEnum {

    KAFKA("KAFKA", "kafka数据源"),

    ELASTICSEARCH("ELASTICSEARCH", "elasticsearch数据源"),

    CLICKHOUSE("CLICKHOUSE", "clickhouse数据源"),

    MYSQL("MYSQL", "mysql数据源"),

    CUSTOM("CUSTOM", "自定义数据源"),

    HIVE("HIVE", "hive数据源"),

    DB2("DB2", "DB2数据源"),

    SQLSERVER("SQLSERVER", "SQLSERVER数据源"),

    ORACLE("ORACLE", "ORACLE数据源"),

    POSTGRESQL("POSTGRESQL", "POSTGRESQL数据源"),

    SYBASE("SYBASE", "SYBASE数据源"),

    DAMENG("DAMENG", "达梦数据源");

    private final String code;

    private final String message;

    DatasourcePlatformEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static DatasourcePlatformEnum fromString(String code) {
        for (DatasourcePlatformEnum value : DatasourcePlatformEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        for (DatasourcePlatformEnum value : DatasourcePlatformEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }

}
