package com.eoi.jax.web.ingestion.model.tag;

import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.Excel;
import com.eoi.jax.web.ingestion.enumrate.TagTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2024/4/25
 */
public class TagExcelModel extends AbstractExcelRowResp {

    /**
     * 标签名称
     */
    @Excel(name = "标签名称*", no = 0)
    @Schema(description = "标签名称")
    private String name;

    /**
     * 标签类型
     */
    @Excel(name = "标签类型*", no = 1, selectionEnum = TagTypeEnum.class)
    @Schema(description = "标签类型")
    private String tagType;

    public TagExcelModel() {
    }

    public TagExcelModel(String name, String tagType) {
        this.name = name;
        this.tagType = tagType;
    }




    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTagType() {
        return tagType;
    }

    public void setTagType(String tagType) {
        this.tagType = tagType;
    }
}
