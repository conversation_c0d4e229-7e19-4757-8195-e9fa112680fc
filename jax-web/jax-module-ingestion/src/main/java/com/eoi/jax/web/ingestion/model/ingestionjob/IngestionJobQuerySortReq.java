package com.eoi.jax.web.ingestion.model.ingestionjob;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbIngestionJob;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
public class IngestionJobQuerySortReq implements ISortReq<TbIngestionJob> {

    @Schema(description = "修改时间排序")
    private String updateTime;

    @Override
    public QueryWrapper<TbIngestionJob> order(QueryWrapper<TbIngestionJob> wrapper) {
        return wrapper;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

}
