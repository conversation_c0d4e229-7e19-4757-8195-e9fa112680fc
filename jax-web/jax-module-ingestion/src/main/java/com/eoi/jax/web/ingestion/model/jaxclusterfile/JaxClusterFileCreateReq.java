package com.eoi.jax.web.ingestion.model.jaxclusterfile;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbJaxClusterFile;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class JaxClusterFileCreateReq implements ICreateModel<TbJaxClusterFile> {

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空")
    private String type;

    /**
     * 类型对应的记录id
     */
    private Long recordId;

    /**
     * 文件路径
     */
    @NotBlank(message = "文件路径不能为空")
    private String filePath;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * value
     */
    @NotBlank(message = "value不能为空")
    private String value;

    /**
     * value的md5值
     */
    @NotBlank(message = "valueMd5不能为空")
    private String valueMd5;

    /**
     * version版本号
     */
    @NotNull(message = "version不能为空")
    private Integer version;

    @Override
    public TbJaxClusterFile toEntity() {
        TbJaxClusterFile entity = toEntity(new TbJaxClusterFile());

        return entity;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueMd5() {
        return valueMd5;
    }

    public void setValueMd5(String valueMd5) {
        this.valueMd5 = valueMd5;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "JaxClusterFileCreateReq{" +
                "type='" + type + '\'' +
                ", recordId=" + recordId +
                ", filePath='" + filePath + '\'' +
                ", name='" + name + '\'' +
                ", value='" + value + '\'' +
                ", valueMd5='" + valueMd5 + '\'' +
                ", version=" + version +
                '}';
    }
}
