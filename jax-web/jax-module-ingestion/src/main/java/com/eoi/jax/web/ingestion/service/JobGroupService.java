package com.eoi.jax.web.ingestion.service;

import com.eoi.jax.web.core.service.IBaseProjectAuthService;
import com.eoi.jax.web.ingestion.model.jobgroup.JobGroupCreateReq;
import com.eoi.jax.web.ingestion.model.jobgroup.JobGroupQueryReq;
import com.eoi.jax.web.ingestion.model.jobgroup.JobGroupResp;
import com.eoi.jax.web.ingestion.model.jobgroup.JobGroupUpdateReq;
import com.eoi.jax.web.repository.entity.TbJobGroup;
import com.eoi.jax.web.repository.service.TbJobGroupService;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-18
 */
public interface JobGroupService extends IBaseProjectAuthService<
    TbJobGroupService,
    TbJobGroup,
    JobGroupResp,
    JobGroupCreateReq,
    JobGroupUpdateReq,
    JobGroupQueryReq> {

    /**
     * 根据jobType查询
     * @param jobType
     * @return
     */
    List<JobGroupResp> queryByJobType(String jobType);
}
