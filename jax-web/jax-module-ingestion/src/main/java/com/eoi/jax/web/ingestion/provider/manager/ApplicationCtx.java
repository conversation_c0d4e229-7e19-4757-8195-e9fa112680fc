package com.eoi.jax.web.ingestion.provider.manager;

import com.eoi.jax.web.ingestion.model.application.deploy.ApplicationDeploy;
import com.eoi.jax.web.ingestion.model.cluster.Cluster;
import com.eoi.jax.web.ingestion.model.opts.Opts;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
public class ApplicationCtx {
    private ApplicationDeploy applicationDeploy;
    private Cluster cluster;
    private Opts opts;

    public ApplicationCtx(ApplicationDeploy applicationDeploy) {
        this.applicationDeploy = applicationDeploy;
        this.cluster = applicationDeploy.getCluster();
        this.opts = applicationDeploy.getOpts();
    }

    public ApplicationDeploy getApplicationDeploy() {
        return applicationDeploy;
    }

    public void setApplicationDeploy(ApplicationDeploy applicationDeploy) {
        this.applicationDeploy = applicationDeploy;
    }

    public Cluster getCluster() {
        return cluster;
    }

    public void setCluster(Cluster cluster) {
        this.cluster = cluster;
    }

    public Opts getOpts() {
        return opts;
    }

    public void setOpts(Opts opts) {
        this.opts = opts;
    }
}
