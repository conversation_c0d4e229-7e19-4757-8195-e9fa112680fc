package com.eoi.jax.web.ingestion.model.algorithmlab;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.repository.entity.TbAlgorithmLab;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/2/20
 */
@OpPrimaryKey(name = "id")
@OpPrimaryName(name = "name")
public class AlgorithmLabResp extends ProjectAuthRespModel
    implements IRespModel<TbAlgorithmLab>, IProjectAuthModel, IUserInfoExtensionModel {

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * CPU配置
     */
    @Schema(description = "CPU配置")
    private Integer cpu;

    /**
     * 内存配置
     */
    @Schema(description = "内存配置")
    private Integer memory;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Long createUser;

    /**
     * 更新人id
     */
    @Schema(description = "更新人id")
    private Long updateUser;

    @Override
    public AlgorithmLabResp fromEntity(TbAlgorithmLab tbAlgorithmLab) {
        AlgorithmLabResp resp = IRespModel.super.fromEntity(tbAlgorithmLab);
        return resp;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Integer getMemory() {
        return memory;
    }

    public void setMemory(Integer memory) {
        this.memory = memory;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
