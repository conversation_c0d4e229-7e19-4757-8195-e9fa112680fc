package com.eoi.jax.web.ingestion.model.ingestioninfo;

import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/2/16
 */
public class TimeWrap {

    @Schema(description = "具体时间")
    private Long num;

    @Schema(description = "单位: hour,day")
    private String unit;

    public TimeWrap() {
    }

    public TimeWrap(Long num, String unit) {
        this.num = num;
        this.unit = unit;
    }

    /**
     * 转化为秒
     *
     * @return
     */
    public Long toSeconds() {
        if ("day".equalsIgnoreCase(unit)) {
            return TimeUnit.DAYS.toSeconds(num);
        } else if ("hour".equalsIgnoreCase(unit)) {
            return TimeUnit.HOURS.toSeconds(num);
        }
        throw new BizException(ResponseCode.CODE_EXIST, String.format("不支持的时间参数:%s", unit));
    }

    public static TimeWrap build(Long num, String unit) {
        if ("day".equalsIgnoreCase(unit)) {
            return new TimeWrap(TimeUnit.SECONDS.toDays(num), unit);
        } else if ("hour".equalsIgnoreCase(unit)) {
            return new TimeWrap(TimeUnit.SECONDS.toHours(num), unit);
        }
        throw new BizException(ResponseCode.FAILED, String.format("不支持的单位:%s", unit));
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
