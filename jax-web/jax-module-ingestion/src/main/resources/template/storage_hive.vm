-- 创建flink-hive-CATALOG
CREATE CATALOG ${hiveDatasourceName} WITH (
    'type' = 'hive',
    'hive-conf-dir' = '$!{hiveConfDir}',
#if(${req.hiveVersion})
    'hive-version' = '${req.hiveVersion}',
#end
    'default-database' = '${hiveDatabase}'
);
USE CATALOG ${hiveDatasourceName};


-- 设置Hive文件提交策略
SET table.sql-dialect=hive;
ALTER TABLE ${hiveTableName} SET TBLPROPERTIES (
    'sink.rolling-policy.file-size'='${req.fileSystemSetting.sinkRollingPolicyFileSize}MB',
    'sink.rolling-policy.rollover-interval'='${req.fileSystemSetting.sinkRollingPolicyRolloverInterval} min',
    'sink.rolling-policy.check-interval'='${req.fileSystemSetting.sinkRollingPolicyCheckInterval} min',
    'auto-compaction'='${req.fileSystemSetting.autoCompaction}',
    'compaction.file-size'='${req.fileSystemSetting.compactionFileSize}MB',
    'sink.partition-commit.trigger'='${req.fileSystemSetting.sinkPartitionCommitTrigger}',
#if($req.fileSystemSetting.partitionTimeExtractorTimestampPattern && $req.fileSystemSetting.sinkPartitionCommitTrigger == 'partition-time')
    'partition.time-extractor.timestamp-pattern'='${req.fileSystemSetting.partitionTimeExtractorTimestampPattern}',
#end
#if($req.fileSystemSetting.sinkPartitionCommitPolicyKind)
    'sink.partition-commit.policy.kind'='${req.fileSystemSetting.buildSinkPartitionCommitPolicyKind()}',
#end
    'sink.partition-commit.delay'='${req.fileSystemSetting.sinkPartitionCommitDelay} h'
);

-- 创建kafka表
SET table.sql-dialect=default;

-- 删除kafka表
DROP TABLE IF EXISTS ${kafkaTableName};

CREATE TABLE IF NOT EXISTS ${kafkaTableName} (
#foreach($item in $req.schemaMappingList)
`${item.sourceField}` ${item.sourceTypeStr}#if($foreach.hasNext),#end
#end
#if($req.fileSystemSetting.sinkPartitionCommitTrigger == 'partition-time')
-- 采用'分区时间'触发策略,需设置WATERMARK
    ,WATERMARK FOR ? AS ? - INTERVAL '5' SECOND
#end
) WITH (
  'connector' = 'kafka',
  'topic' = '${kafkaTopicName}',
  'scan.startup.mode' = '${req.strategy}-offset',
  'properties.bootstrap.servers' = '${kafkaServers}',
#if("$!{req.consumeGroup}" != "")
  'properties.group.id' = '${req.consumeGroup}',
#else
  -- 消费组ID将在实际运行时自动生成
  'properties.group.id' = '${consumeGroup}',
#end
  'format' = '${req.dataFormat.toLowerCase()}',
  'json.ignore-parse-errors' = '${req.ignoreParseError}',
  #if(${req.ignoreMissingField} == true)
  'json.fail-on-missing-field' = 'false'
  #else
  'json.fail-on-missing-field' = 'true'
  #end

);


-- 将kafka数据插入到hive
INSERT INTO ${hiveTableName}
(
#foreach($item in $req.schemaMappingList)
`${item.sinkField}`#if($foreach.hasNext),#end
#end
)
SELECT
#foreach($item in $req.schemaMappingList)
#if($item.customSql)
 ${item.customSql} AS `${item.sourceField}`#if($foreach.hasNext),#end
#else
`${item.sourceField}`#if($foreach.hasNext),#end
#end
#end
FROM ${kafkaTableName};