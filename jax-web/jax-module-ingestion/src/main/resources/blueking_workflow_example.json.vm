{"businessFlowName": "业务流程", "categoryName": "离线数据处理", "name": "${configName}工作流", "typeName": "离线工作流", "referenceTypeName": "离线工作流", "taskConfigMap": {"taskDefinitionList": [{"failRetryTimes": 0, "code": 15004110761216, "flag": "YES", "taskParamList": [], "taskPriority": "MEDIUM", "description": "", "memoryMax": -1, "taskParams": {"sqlType": 0, "postStatements": ["alter table ${cmdbOdsTable.cmdbOdsTable}_local on cluster ${cluster} delete where source = ${source} and date = ${loadDate} and snapshot = 0", "alter table ${cmdbOdsTable.cmdbOdsRelationTable}_local on cluster ${cluster} delete where source = ${source} and date = ${loadDate} and snapshot = 0"], "datasource": "${cmdbOdsTable.dsId}", "displayRows": 1, "type": "CLICKHOUSE", "localParams": [], "sql": "select (select count(1) from ${cmdbOdsTable.cmdbOdsTable} where source = ${source} and date = ${loadDate} and snapshot = 0) as delete_object_count, (select count(1) from ${cmdbOdsTable.cmdbOdsRelationTable} where source = ${source} and date = ${loadDate} and snapshot = 0) as delete_relation_count", "preStatements": [], "resourceList": []}, "version": 0, "cpuQuota": -1, "taskType": "SQL", "projectCode": -1, "failRetryInterval": 1, "name": "幂等删除", "delayTime": 0, "resourceParams": null, "id": 862, "workerGroup": null, "resourceIds": []}, {"failRetryTimes": 0, "code": 15004110761345, "flag": "YES", "taskParamList": [], "taskPriority": "MEDIUM", "description": "", "memoryMax": -1, "taskParams": {"sqlType": 1, "datasource": "${cmdbOdsTable.dsId}", "segmentSeparator": ";", "type": "CLICKHOUSE", "localParams": [], "sql": "insert  into ${cmdbOdsTable.cmdbOdsTable} select date,source,tag,vid,properties ,1 from ${cmdbOdsTable.cmdbOdsTable} where source = ${source} and date = ${loadDate} and snapshot = 0;\n\ninsert into ${cmdbOdsTable.cmdbOdsRelationTable} select date,source,svid,dvid,type,1 from ${cmdbOdsTable.cmdbOdsRelationTable} where source = ${source} and date = ${loadDate} and snapshot = 0;", "resourceList": []}, "version": 0, "cpuQuota": -1, "taskType": "SQL", "projectCode": -1, "failRetryInterval": 0, "name": "数据快照", "delayTime": 1, "resourceParams": null, "id": 865, "workerGroup": null, "resourceIds": []}, {"failRetryTimes": 0, "code": 15004110761346, "flag": "YES", "taskParamList": [], "taskPriority": "MEDIUM", "description": "", "memoryMax": -1, "taskParams": {"rawScript": "java -cp jax-integration.jar com.eoi.jax.integration.syncdata.SyncObjectData -loadDate ${loadDate}", "localParams": [], "resourceList": [{"id": 100024}]}, "version": 0, "cpuQuota": -1, "taskType": "SHELL", "projectCode": -1, "failRetryInterval": 1, "name": "同步到宽表和图空间", "delayTime": 1, "resourceParams": [{"fullName": "/dolphinscheduler/1858846107829248/resources/jax-integration.jar", "type": 0}], "id": 866, "workerGroup": null, "resourceIds": [100024]}, {"failRetryTimes": 0, "code": 15004111330400, "flag": "YES", "taskParamList": null, "taskPriority": "MEDIUM", "description": "", "memoryMax": -1, "taskParams": {"varPool": null, "configId": "${configId}", "localParams": [], "loadDate": "${loadDate}", "cmdbType": "BLUEKING", "resourceList": [{"id": 100024}]}, "version": 0, "cpuQuota": -1, "taskType": "CMDB_INTEGRATION", "projectCode": -1, "failRetryInterval": 1, "name": "蓝鲸数据同步", "delayTime": 1, "resourceParams": [{"fullName": "/dolphinscheduler/1858846107829248/resources/jax-integration.jar", "type": 0}], "id": 867, "workerGroup": null, "resourceIds": [100024]}, {"failRetryTimes": 0, "code": 15004110761344, "flag": "YES", "taskParamList": [], "taskPriority": "MEDIUM", "description": "", "memoryMax": -1, "taskParams": {"sqlType": 0, "postStatements": ["alter table ${cmdbOdsTable.cmdbOdsTable}_local on cluster ${cluster} delete where source = ${source} and snapshot = 1", "alter table ${cmdbOdsTable.cmdbOdsRelationTable}_local on cluster ${cluster}  delete where source =  ${source} and snapshot=1"], "datasource": "${cmdbOdsTable.dsId}", "displayRows": 1, "type": "CLICKHOUSE", "localParams": [], "sql": "select (select count(1) from ${cmdbOdsTable.cmdbOdsTable} where source = ${source} and date = ${loadDate} and snapshot = 1) as delete_object_count, (select count(1) from ${cmdbOdsTable.cmdbOdsRelationTable} where source = ${source} and date = ${loadDate} and snapshot = 1) as delete_relation_count", "preStatements": [], "resourceList": []}, "version": 0, "cpuQuota": -1, "taskType": "SQL", "projectCode": -1, "failRetryInterval": 0, "name": "删除数据快照", "delayTime": 0, "resourceParams": null, "id": 864, "workerGroup": null, "resourceIds": []}], "taskRelationList": [{"postTaskCode": 15004110761345, "postTaskCodeName": "数据快照", "preTaskCode": 15004110761344, "postTaskVersion": 0, "preTaskCodeName": "删除数据快照", "preTaskVersion": 0}, {"postTaskCode": 15004110761346, "postTaskCodeName": "同步到宽表和图空间", "preTaskCode": 15004110761345, "postTaskVersion": 0, "preTaskCodeName": "数据快照", "preTaskVersion": 0}, {"postTaskCode": 15004111330400, "postTaskCodeName": "蓝鲸数据同步", "preTaskCode": 15004110761216, "postTaskVersion": 0, "preTaskCodeName": "幂等删除", "preTaskVersion": 0}, {"postTaskCode": 15004110761344, "postTaskCodeName": "删除数据快照", "preTaskCode": 15004111330400, "postTaskVersion": 0, "preTaskCodeName": "蓝鲸数据同步", "preTaskVersion": 0}], "code": "15004111197760", "flag": "YES", "executionType": "PARALLEL", "description": null, "globalParams": [{"prop": "loadDate", "direct": "IN", "type": "VARCHAR", "value": "$[yyyy-MM-dd-1]"}, {"prop": "cluster", "direct": "IN", "type": "VARCHAR", "value": "${cmdbOdsTable.ckClusterName}"}, {"prop": "source", "direct": "IN", "type": "VARCHAR", "value": "blueking-${configId}"}], "version": 0, "timeout": 0, "tags": [], "schedule": {"failureStrategy": "CONTINUE", "crontab": "0 15 0 * * ?", "timezoneId": "Asia/Shanghai", "startTime": "2024-05-13 00:00:00", "id": 203, "endTime": "2124-05-13 00:00:00", "workerGroup": "default", "processInstancePriority": "MEDIUM"}, "projectCode": -1, "locations": [{"taskCode": 15004110761216, "x": 40, "y": 40}, {"taskCode": 15004110761345, "x": 40, "y": 340}, {"taskCode": 15004110761346, "x": 40, "y": 440}, {"taskCode": 15004111330400, "x": 40, "y": 130}, {"taskCode": 15004110761344, "x": 40, "y": 240}], "releaseState": "OFFLINE"}}