:plugin: split
:type: filter

///////////////////////////////////////////
START - GENERATED VARIABLES, DO NOT EDIT!
///////////////////////////////////////////
:version: %VERSION%
:release_date: %RELEASE_DATE%
:changelog_url: %CHANGELOG_URL%
:include_path: ../../../../logstash/docs/include
///////////////////////////////////////////
END - GENERATED VARIABLES, DO NOT EDIT!
///////////////////////////////////////////

[id="plugins-{type}s-{plugin}"]

=== Split filter plugin

include::{include_path}/plugin_header.asciidoc[]

==== Description

The split filter clones an event by splitting one of its fields and
placing each value resulting from the split into a clone of the original
event. The field being split can either be a string or an array.

An example use case of this filter is for taking output from the
{logstash-ref}/plugins-inputs-exec.html[exec input plugin] which emits one event for
the whole output of a command and splitting that output by newline -
making each line an event.

Split filter can also be used to split array fields in events into individual events.
A very common pattern in JSON & XML is to make use of lists to group data together.

For example, a json structure like this:

[source,js]
----------------------------------
{ field1: ...,
 results: [
   { result ... },
   { result ... },
   { result ... },
   ...
] }
----------------------------------

The split filter can be used on the above data to create separate events for each value of `results` field

[source,js]
----------------------------------
filter {
 split {
   field => "results"
 }
}
----------------------------------

The end result of each split is a complete copy of the event
with only the current split section of the given field changed.

[id="plugins-{type}s-{plugin}-options"]
==== Split Filter Configuration Options

This plugin supports the following configuration options plus the <<plugins-{type}s-{plugin}-common-options>> described later.

[cols="<,<,<",options="header",]
|=======================================================================
|Setting |Input type|Required
| <<plugins-{type}s-{plugin}-field>> |<<string,string>>|No
| <<plugins-{type}s-{plugin}-target>> |<<string,string>>|No
| <<plugins-{type}s-{plugin}-terminator>> |<<string,string>>|No
|=======================================================================

Also see <<plugins-{type}s-{plugin}-common-options>> for a list of options supported by all
filter plugins.

&nbsp;

[id="plugins-{type}s-{plugin}-field"]
===== `field` 

  * Value type is <<string,string>>
  * Default value is `"message"`

The field which value is split by the terminator.  
Can be a multiline message or the ID of an array.  
Nested arrays are referenced like: "[object_id][array_id]"

[id="plugins-{type}s-{plugin}-target"]
===== `target` 

  * Value type is <<string,string>>
  * There is no default value for this setting.

The field within the new event which the value is split into.
If not set, the target field defaults to split field name.

[id="plugins-{type}s-{plugin}-terminator"]
===== `terminator` 

  * Value type is <<string,string>>
  * Default value is `"\n"`

The string to split on. This is usually a line terminator, but can be any
string. If you are splitting a JSON array into multiple events, you can ignore this field.



[id="plugins-{type}s-{plugin}-common-options"]
include::{include_path}/{type}.asciidoc[]
