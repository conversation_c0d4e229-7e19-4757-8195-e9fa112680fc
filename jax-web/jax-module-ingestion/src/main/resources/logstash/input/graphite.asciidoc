:plugin: graphite
:type: input
:default_codec: plain

///////////////////////////////////////////
START - GENERATED VARIABLES, DO NOT EDIT!
///////////////////////////////////////////
:version: %VERSION%
:release_date: %RELEASE_DATE%
:changelog_url: %CHANGELOG_URL%
:include_path: ../../../../logstash/docs/include
///////////////////////////////////////////
END - GENERATED VARIABLES, DO NOT EDIT!
///////////////////////////////////////////

[id="plugins-{type}s-{plugin}"]

=== Graphite input plugin

include::{include_path}/plugin_header.asciidoc[]

==== Description

Receive graphite metrics. This plugin understands the text-based graphite
carbon protocol. Both `N` and `specific-timestamp` forms are supported, example:
[source,ruby]
    mysql.slow_query.count 204 N
    haproxy.live_backends 7 1364608909

`N` means `now` for a timestamp. This plugin also supports having the time
specified in the metric payload:

For every metric received from a client, a single event will be emitted with
the metric name as the field (like `mysql.slow_query.count`) and the metric
value as the field's value.

[id="plugins-{type}s-{plugin}-options"]
==== Graphite Input Configuration Options

This plugin supports the following configuration options plus the <<plugins-{type}s-{plugin}-common-options>> described later.

[cols="<,<,<",options="header",]
|=======================================================================
|Setting |Input type|Required
| <<plugins-{type}s-{plugin}-host>> |<<string,string>>|No
| <<plugins-{type}s-{plugin}-mode>> |<<string,string>>, one of `["server", "client"]`|No
| <<plugins-{type}s-{plugin}-port>> |<<number,number>>|Yes
| <<plugins-{type}s-{plugin}-proxy_protocol>> |<<boolean,boolean>>|No
| <<plugins-{type}s-{plugin}-ssl_cert>> |a valid filesystem path|No
| <<plugins-{type}s-{plugin}-ssl_enable>> |<<boolean,boolean>>|No
| <<plugins-{type}s-{plugin}-ssl_extra_chain_certs>> |<<array,array>>|No
| <<plugins-{type}s-{plugin}-ssl_key>> |a valid filesystem path|No
| <<plugins-{type}s-{plugin}-ssl_key_passphrase>> |<<password,password>>|No
| <<plugins-{type}s-{plugin}-ssl_verify>> |<<boolean,boolean>>|No
|=======================================================================

Also see <<plugins-{type}s-{plugin}-common-options>> for a list of options supported by all
input plugins.

&nbsp;

[id="plugins-{type}s-{plugin}-data_timeout"]
===== `data_timeout`  (DEPRECATED)

  * DEPRECATED WARNING: This configuration item is deprecated and may not be available in future versions.
  * Value type is <<number,number>>
  * Default value is `-1`



[id="plugins-{type}s-{plugin}-host"]
===== `host` 

  * Value type is <<string,string>>
  * Default value is `"0.0.0.0"`

Read events over a TCP socket.

Like stdin and file inputs, each event is assumed to be one line of text.

Can either accept connections from clients or connect to a server,
depending on `mode`.
When mode is `server`, the address to listen on.
When mode is `client`, the address to connect to.

[id="plugins-{type}s-{plugin}-mode"]
===== `mode` 

  * Value can be any of: `server`, `client`
  * Default value is `"server"`

Mode to operate in. `server` listens for client connections,
`client` connects to a server.

[id="plugins-{type}s-{plugin}-port"]
===== `port` 

  * This is a required setting.
  * Value type is <<number,number>>
  * There is no default value for this setting.

When mode is `server`, the port to listen on.
When mode is `client`, the port to connect to.

[id="plugins-{type}s-{plugin}-proxy_protocol"]
===== `proxy_protocol` 

  * Value type is <<boolean,boolean>>
  * Default value is `false`

Proxy protocol support, only v1 is supported at this time
http://www.haproxy.org/download/1.5/doc/proxy-protocol.txt

[id="plugins-{type}s-{plugin}-ssl_cacert"]
===== `ssl_cacert`  (DEPRECATED)

  * DEPRECATED WARNING: This configuration item is deprecated and may not be available in future versions.
  * Value type is <<path,path>>
  * There is no default value for this setting.

The SSL CA certificate, chainfile or CA path. The system CA path is automatically included.

[id="plugins-{type}s-{plugin}-ssl_cert"]
===== `ssl_cert` 

  * Value type is <<path,path>>
  * There is no default value for this setting.

SSL certificate path

[id="plugins-{type}s-{plugin}-ssl_enable"]
===== `ssl_enable` 

  * Value type is <<boolean,boolean>>
  * Default value is `false`

Enable SSL (must be set for other `ssl_` options to take effect).

[id="plugins-{type}s-{plugin}-ssl_extra_chain_certs"]
===== `ssl_extra_chain_certs` 

  * Value type is <<array,array>>
  * Default value is `[]`

An Array of extra X509 certificates to be added to the certificate chain.
Useful when the CA chain is not necessary in the system store.

[id="plugins-{type}s-{plugin}-ssl_key"]
===== `ssl_key` 

  * Value type is <<path,path>>
  * There is no default value for this setting.

SSL key path

[id="plugins-{type}s-{plugin}-ssl_key_passphrase"]
===== `ssl_key_passphrase` 

  * Value type is <<password,password>>
  * Default value is `nil`

SSL key passphrase

[id="plugins-{type}s-{plugin}-ssl_verify"]
===== `ssl_verify` 

  * Value type is <<boolean,boolean>>
  * Default value is `true`

Verify the identity of the other end of the SSL connection against the CA.
For input, sets the field `sslsubject` to that of the client certificate.



[id="plugins-{type}s-{plugin}-common-options"]
include::{include_path}/{type}.asciidoc[]

:default_codec!: