output {
## https://www.elastic.co/guide/en/logstash-versioned-plugins/current/v10.1.0-plugins-outputs-elasticsearch.html
    elasticsearch {
## 远程实例的主机
        hosts => [#foreach($node in $elasticsearchConnection.address.split(","))"$node"#if($foreach.hasNext), #end#end]
## 索引名称,默认值为"logstash-%{+YYYY.MM.dd}"
        index => "$!indexNamePattern"
        #if($elasticsearchConnection.authType == "HTTP_BASIC")
## 用于向安全 Elasticsearch 集群进行身份验证的用户名,没有默认值
        user => "$elasticsearchConnection.username"
## 用于向安全 Elasticsearch 集群进行身份验证的密码,没有默认值
        password => "$elasticsearchConnection.password"
        #end
## 设置网络操作和发送 Elasticsearch 请求的超时（以秒为单位）。如果发生超时，将重试请求, 默认值为60
        timeout => 60
        #if($lifecyclePolicy)
## 默认值为auto
            ilm_enabled => "true"
## 自定义索引生命周期管理策略
            ilm_policy => "$lifecyclePolicy"
        #end
    }
}