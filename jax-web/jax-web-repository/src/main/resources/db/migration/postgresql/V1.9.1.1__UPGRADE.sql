UPDATE tb_system_config
SET setting = '{"hooks":[{"hook":"debugReq","lang":"groovy","script":"if (ctx.pipelineConfig.jobs != null) {\n    for (job in ctx.pipelineConfig.jobs) {\n        if (job.jobName.contains(\"KafkaSourceJob\")\n            || job.jobName.contains(\"KafkaByteSourceJob\")\n            || job.jobName.contains(\"KafkaByteSourceByModelJob\")) {\n                if (job.jobConfig && job.jobConfig.groupId) {\n                    job.jobConfig.groupId = job.jobConfig.groupId + \"_debug_\";\n                }\n            }\n    }\n}","hookEnable":true}]}'
WHERE id = 10;

UPDATE tb_system_config
SET setting_schema = '[{"requireCondition":"","defaultValue":"30","description":"用于控制流作业容量日报保存多少天，过期的数据会被删除","range":"","optional":false,"label":"流作业容量日报TTL","type":["INT"],"recommendations":[],"availableCondition":"","candidates":[],"regex":"^[1-9]\\d*$","apiVersion":2,"scope":"","name":"ttl","inputType":"TEXT","placeholder":""}]'
WHERE id = 11;
