UPDATE `tb_permission` SET `resource_type`= NULL WHERE `id`=2167962332826124;

INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2277937868179456', 'AI实验室-jupyterlab', NULL, NULL, '/algorithm-lab/jupyterlab', 'M1279', 'P003005001', 'AI实验室-jupyterlab', NULL, '2127368951759872', '32/2127368951759872/', '0', '0', NULL, 'menu', '0', NULL, '0', '0', '2024-04-16 14:19:07', '2024-06-03 13:37:50', NULL, NULL);
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2413755151060994', '1765194435396608', 'P003005000');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2413756320256000', '1765192536458240', 'P003005000');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2413755151060992', '1765194435396608', 'P003005001');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2413756320256002', '1765192536458240', 'P003005001');

INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2402962426956800,'hit','hit','hit','CACHE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403018382608384,'miss','miss','miss','CACHE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403018750233600,'eviction','eviction','eviction','CACHE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403019191911424,'set','set','set','CACHE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403019637457920,'get','get','get','CACHE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403020826903552,'core','core','core','CPU',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403021373868032,'cursor','cursor','cursor','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403022850688000,'index','index','index','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403023287192576,'table','table','table','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403023644036096,'transaction','transaction','transaction','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403023961129984,'query','query','query','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403024298050560,'row','row','row','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038041606144,'key','key','key','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038042916864,'command','command','command','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038043113472,'offset','offset','offset','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038044194816,'record','record','record','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038045833216,'object','object','object','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038046586880,'lock','lock','lock','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038047012864,'scan','scan','scan','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038047373312,'assertion','assertion','assertion','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038047766528,'shard','shard','shard','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038048389120,'question','question','question','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038048946176,'flush','flush','flush','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038050158592,'merge','merge','merge','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038051010560,'refresh','refresh','refresh','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038051534848,'fetch','fetch','fetch','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038052026368,'column','column','column','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038053959680,'commit','commit','commit','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038054549504,'document','document','document','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038055598080,'ticket','ticket','ticket','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038055991296,'wait','wait','wait','DB',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038056482816,'file','file','file','DISK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038057007104,'sector','sector','sector','DISK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038058252288,'inode','inode','inode','DISK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038059202560,'block','block','block','DISK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038059497472,'gigahert','gigahert','gigahert','FREQUENCY',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038059792384,'megahertz','megahertz','megahertz','FREQUENCY',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038060316672,'hertz','hertz','hertz','FREQUENCY',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038061004800,'kilohertz','kilohertz','kilohertz','FREQUENCY',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038061856768,'exception','exception','exception','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038062381056,'prediction','prediction','prediction','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038063134720,'build','build','build','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038063593472,'success','success','success','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038064216064,'user','user','user','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038064773120,'invocation','invocation','invocation','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038065428480,'throttle','throttle','throttle','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038066116608,'execution','execution','execution','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038066706432,'container','container','container','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038067132416,'job','job','job','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038067951616,'device','device','device','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038068377600,'update','update','update','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038068967424,'attempt','attempt','attempt','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038069360640,'method','method','method','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038070016000,'location','location','location','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038070409216,'buffer','buffer','buffer','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038070802432,'error','error','error','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038071195648,'check','check','check','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038071851008,'write','write','write','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038072211456,'occurrence','occurrence','occurrence','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038072604672,'event','event','event','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038073030656,'times','times','times','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038073751552,'unit','unit','unit','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038074308608,'operation','operation','operation','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038074832896,'read','read','read','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038075324416,'task','task','task','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038075947008,'worker','worker','worker','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038076667904,'resource','resource','resource','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038077159424,'garbage collection','garbage collection','garbage collection','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038077552640,'email','email','email','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038077913088,'sample','sample','sample','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038078273536,'stage','stage','stage','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038078732288,'monitor','monitor','monitor','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038078928896,'item','item','item','GENERAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038079518720,'split','split','split','MEMORY',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038080141312,'page','page','page','MEMORY',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038080468992,'session','session','session','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038081255424,'timeout','timeout','timeout','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038081812480,'payload','payload','payload','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038082533376,'message','message','message','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038082861056,'response','response','response','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038083254272,'segment','segment','segment','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038083581952,'packet','packet','packet','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038083844096,'request','request','request','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038084368384,'connection','connection','connection','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038084696064,'hop','hop','hop','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038084859904,'route','route','route','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038085154816,'datagram','datagram','datagram','NETWORK',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038085384192,'volt','volt','volt','POTENTIAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038085875712,'millivolt','millivolt','millivolt','POTENTIAL',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038086400000,'watt','watt','watt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038086891520,'terrawatt','terrawatt','terrawatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038087120896,'gigawatt','gigawatt','gigawatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038087415808,'milliwatt','milliwatt','milliwatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038087710720,'microwatt','microwatt','microwatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038088005632,'nanowatt','nanowatt','nanowatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038088169472,'megawatt','megawatt','megawatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038088398848,'deciwatt','deciwatt','deciwatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038088595456,'kilowatt','kilowatt','kilowatt','POWER',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038089119744,'process','process','process','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038089381888,'node','node','node','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038089840640,'host','host','host','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038090397696,'fault','fault','fault','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038091479040,'service','service','service','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038091806720,'instance','instance','instance','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038092560384,'cpu','cpu','cpu','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038092953600,'thread','thread','thread','SYSTEM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038093215744,'decidegree celsius','decidegree celsius','decidegree celsius','TEMPERATURE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038093445120,'degree fahrenheit','degree fahrenheit','degree fahrenheit','TEMPERATURE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403038093707264,'degree celsius','degree celsius','degree celsius','TEMPERATURE',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_measure_unit`(`id`, `code`, `name`, `name_en`, `catalog`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2403052052679680,'span','span','span','APM',0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);

INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '32 0/2 * * * ?', '用于弹性作业指标收集', '2024-05-30 17:11:56', '2024-05-30 18:08:42', NULL, NULL, 'CONSISTENT_HASH', 'jaxApplicationJobCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-05-30 17:11:56', NULL, '1', '0', '0', '作业级-弹性作业指标收集', '0');

INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('20', '算法服务配置', '算法服务配置', '数据服务', 'jax.aiservice.config', NULL, NULL, '[{\"requireCondition\":\"\",\"falseLabel\":\"\",\"defaultValue\":\"\",\"objectTitle\":false,\"description\":\"算法服务地址\",\"range\":\"\",\"optional\":true,\"label\":\"算法服务地址\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"trueLabel\":\"\",\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"server\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"falseLabel\":\"\",\"defaultValue\":\"\",\"objectTitle\":false,\"description\":\"超时时间\",\"range\":\"\",\"optional\":true,\"label\":\"超时时间\",\"type\":[\"LONG\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"trueLabel\":\"\",\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"httpTimeout\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"falseLabel\":\"\",\"defaultValue\":\"\",\"objectTitle\":false,\"description\":\"密钥\",\"range\":\"\",\"optional\":true,\"label\":\"密钥\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"trueLabel\":\"\",\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"secretKey\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', '{}', 0, '2024-06-05 00:00:00', '2024-06-05 00:00:00', NULL, NULL);
INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('21', '二维表导入配置', '二维表导入配置', '导入导出', 'jax.import.export', NULL, NULL, '[{\"requireCondition\":\"\",\"falseLabel\":\"\",\"defaultValue\":\"10000\",\"objectTitle\":false,\"description\":\"二维表导入数据量限制, 单位为行\",\"range\":\"\",\"optional\":false,\"label\":\"二维表导入数据量限制\",\"type\":[\"INT\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"trueLabel\":\"\",\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"modelImportLimit\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', '{\"modelImportLimit\":10000}', '0', '2024-06-05 14:35:28', '2024-06-05 14:47:22', NULL, NULL);
UPDATE `tb_system_config` SET `setting_schema`='[{\"requireCondition\":\"\",\"falseLabel\":\"\",\"defaultValue\":\"\",\"objectTitle\":false,\"description\":\"Jupyterhub地址,例如:http://127.0.0.1:8000/jupyterhub\",\"range\":\"\",\"optional\":false,\"label\":\"Jupyterhub地址\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"trueLabel\":\"\",\"regex\":\"^https?://[^:/]*(:[0-9]{1,5})?(/[^?#]*)?(\\\\?[^#]*)?(#.*|)?$\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"url\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"falseLabel\":\"\",\"defaultValue\":\"\",\"objectTitle\":false,\"description\":\"使用管理员账号登录Jupyterhub,生成一个token,将该token填写到此处\",\"range\":\"\",\"optional\":false,\"label\":\"Jupyterhub访问token\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"trueLabel\":\"\",\"regex\":\"^\\\\w+$\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"token\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]' WHERE (`id`='12');

INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2220796423113730, 'application', '弹性作业', NULL, 2220795316734976, '2220795316734976	2220795316734976/', 2220795316734976, 1, 0, '2024-03-27 09:54:55', '2024-03-27 09:54:55', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(58, 2220796423113730, 'APPLICATION_JOB', '作业可用性', 'jax_application_job_failed', '弹性作业运行失败时(包含Clickhouse存储集群运行失败)，产生告警', 1, 'jax_application_job_failed', '["clusterId","clusterName"]', 40, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(58, 58, '[{"metricName":"value","value":"运行失败"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"name"}}', 'LIVENESS', '任务状态告警,无需设置阈值', '<#if storageClusterName??>存储集群【${storageClusterName}】<#else>弹性作业【${objectName}】</#if>运行失败', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-06-06 09:53:42', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2416196531881984, 58, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-06-04 10:21:06', '2024-06-04 10:21:06', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(40, '在数据开发/弹性作业中，通过运行日志排查问题', '弹性作业', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);

alter table tb_import_detail MODIFY  row_detail MEDIUMTEXT NOT NULL COMMENT '行内容';

INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES (2413756320256003,1765192536458240,'job_group:write');

TRUNCATE TABLE `tb_permission`;
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (11,'数据建模',NULL,'iconfont  icon-application-line',NULL,'M0011','P001000000','数据建模','full',0,'',0,0,NULL,'menu',10,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (12,'数仓规划',NULL,'iconfont  icon-columns-line text-sm',NULL,'M0012','P001001000','数仓规划','full',11,'11/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (13,'业务分类',NULL,'iconfont  icon-widgets-fill','/data-modeling/warehouse-plan/business-category','M0013','P001001001','业务分类','full',12,'11/12/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (14,'数仓分层',NULL,'iconfont  icon-readermode-line','/data-modeling/warehouse-plan/warehouse-layer/index','M0014','P001001002','数仓分层','full',12,'11/12/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (15,'公共层',NULL,'iconfont  icon-scatter_plot-line',NULL,'M0015','P001001003','公共层','full',12,'11/12/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (16,'应用层',NULL,'iconfont  icon-object-fill',NULL,'M0016','P001001004','应用层','full',12,'11/12/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (17,'数据标准',NULL,'iconfont  icon-text-line tex-sm',NULL,'M0017','P001002000','数据标准','full',11,'11/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (18,'字段标准',NULL,'iconfont  icon-text-line','/data-modeling/data-standard/field','M0018','P001002001','字段标准','full',17,'11/17/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (19,'数据字典',NULL,'iconfont  icon-codemode-line','/data-modeling/data-standard/enum','M0019','P001002002','数据字典','full',17,'11/17/',0,0,NULL,'menu',2,NULL,1,0,'2023-06-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (20,'度量单位',NULL,'iconfont icon-ruler-line','/data-modeling/data-standard/measure','M0020','P001002003','度量单位','full',17,'11/17/',0,0,NULL,'menu',3,NULL,1,0,'2023-06-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (21,'命名词典',NULL,'iconfont icon-class-line','/data-modeling/data-standard/name','M0021','P001002004','命名词典','full',17,'11/17/',0,0,NULL,'menu',4,NULL,1,0,'2023-06-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (22,'维度建模',NULL,'iconfont icon-dimensionwd-line text-sm',NULL,'M0022','P001003000','维度建模','full',11,'11/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (23,'维度建模',NULL,'iconfont icon-dimensionwd-line','/data-modeling/dimension-modeling/modeling/build','M0023','P001003001','维度建模','full',22,'11/22/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (24,'数据指标',NULL,'iconfont icon-sort-line text-sm','/data-modeling/data-indicator','M0024','P001004000','数据指标','full',11,'11/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (25,'派生指标',NULL,'iconfont icon-charts-line','/data-modeling/data-indicator/derivativeIndicator/list','MDerivativeIndicator','P001004001','派生指标','full',24,'11/24/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (26,'原子指标',NULL,'iconfont icon-scatter_plot-line','/data-modeling/data-indicator/atomicIndicator/list','MAtomicIndicator','P001004002','原子指标','full',24,'11/24/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (27,'修饰词',NULL,'iconfont icon-palette-line','/data-modeling/data-indicator/adjunct/list','MAdjunct','P001004003','修饰词','full',24,'11/24/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (28,'时间周期',NULL,'iconfont icon-calendar-line','/data-modeling/data-indicator/timePeriod/list','MTimePeriod','P001004004','时间周期','full',24,'11/24/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (29,'导入导出',NULL,'iconfont icon-save_alt-line',NULL,'M0029','P002000000','导入导出',NULL,0,'',0,0,NULL,'menu',40,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (30,'导入',NULL,'iconfont icon-save_alt-line text-sm',NULL,'M0030','P002001000','导入',NULL,29,'29/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (31,'导入数据',NULL,'iconfont icon-save_alt-line','/import/management/list','M0031','P002001001','导入数据',NULL,30,'29/30/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (32,'数据开发',NULL,'iconfont icon-codeone-line',NULL,'M0032','P003000000','数据开发',NULL,0,'',0,0,NULL,'menu',30,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (33,'数据开发',NULL,'iconfont icon-codemode-line text-sm',NULL,'M0033','P003001000','数据开发',NULL,32,'32/',0,0,NULL,'menu',1,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (34,'数据开发',NULL,'iconfont icon-codemode-line','/data-dev/dev/management','M0034','P003001001','数据开发',NULL,33,'32/33/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (35,'弹性作业',NULL,'iconfont icon-timebox-line',NULL,'M0035','P003002000','弹性作业',NULL,32,'32/',0,0,NULL,'menu',2,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (36,'弹性作业',NULL,'iconfont icon-timebox-line','/flexible-application/ops','M0036','P003002001','弹性作业',NULL,35,'32/35/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (37,'运维中心',NULL,'iconfont icon-operation-line',NULL,'M0037','P004000000','运维中心',NULL,0,'',0,0,NULL,'menu',50,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (38,'采集任务运维',NULL,'iconfont icon-collect-line text-sm',NULL,'M0038','P004001000','采集任务运维','rtdatacollection',37,'37/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (39,'采集任务运维',NULL,'iconfont icon-collect-line','/ops/ingestion/cell-agent','M0039','P004001001','采集任务运维','rtdatacollection',38,'37/38/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (41,'实时任务运维',NULL,'iconfont icon-timetask-line',NULL,'M0041','P004002000','实时任务运维',NULL,37,'37/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (42,'实时任务运维',NULL,'iconfont icon-timetask-line',NULL,'M0042','P004002001','实时任务运维',NULL,41,'37/41/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2024-01-20 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (43,'存储任务运维',NULL,'iconfont icon-dns-line','/ops/realtime/storage','M0043','P004002002','存储任务运维',NULL,41,'37/41/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (46,'平台管理',NULL,'iconfont icon-settings-line',NULL,'M0046','P005000000','平台管理',NULL,0,'',0,0,NULL,'menu',20,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (47,'中心管理',NULL,'iconfont icon-dns-line text-sm',NULL,'M0047','P005001000','中心管理',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (48,'中心管理',NULL,'iconfont icon-dns-line','/platform-management/data-center/management','M0048','P005001001','中心管理',NULL,47,'46/47/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (49,'地址管理',NULL,'iconfont icon-pin_drop-line',NULL,'M0049','P005002000','地址管理',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (50,'数据源管理',NULL,'iconfont icon-data-line','/platform-management/address/data-source/list','M0050','P005002001','数据源管理',NULL,49,'46/49/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (51,'采集网关管理',NULL,'iconfont icon-map-line','/platform-management/address/gateway','M0051','P005002002','采集网关管理','rtdatacollection',49,'46/49/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (52,'注册中心管理',NULL,'iconfont icon-nacos','/platform-management/address/registry','M0052','P005002003','注册中心管理',NULL,49,'46/49/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (53,'集群管理',NULL,'iconfont icon-cmdb-line',NULL,'M0053','P005003000','集群管理',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (54,'集群管理',NULL,'iconfont icon-cmdb-line','/platform-management/cluster/management/list','M0054','P005003001','集群管理',NULL,53,'46/53/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (55,'存储集群管理(CK)',NULL,'iconfont icon-save-fill','/platform-management/cluster/storage-management-ck/list','M0055','P005003002','存储集群管理(CK)',NULL,53,'46/53/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (56,'框架管理',NULL,'iconfont icon-web-line text-sm',NULL,'M0056','P005004000','框架管理',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (57,'框架管理',NULL,'iconfont icon-web-line','/platform-management/opts/list','M0057','P005004001','框架管理',NULL,56,'46/56/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (58,'扩展包管理',NULL,'iconfont icon-puzzle-line text-sm',NULL,'M0058','P005005000','扩展包管理',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (59,'扩展包管理',NULL,'iconfont icon-puzzle-line','/platform-management/extension-package/list','M0059','P005005001','扩展包管理',NULL,58,'46/58/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (60,'审计日志',NULL,'iconfont icon-chat-line text-sm',NULL,'M0060','P005006000','审计日志',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (61,'审计日志',NULL,'iconfont icon-chat-line','/platform-management/audit-log/index','M0061','P005006001','审计日志',NULL,60,'46/60/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (62,'数据域',NULL,NULL,'/data-modeling/warehouse-plan/wh-layer/data-domain/index','M0062','P001001003001','数据域','full',15,'11/12/15/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (63,'业务过程',NULL,NULL,'/data-modeling/warehouse-plan/wh-layer/business-process/index','M0063','P001001003002','业务过程','full',15,'11/12/15/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (64,'数据集市',NULL,NULL,'/data-modeling/warehouse-plan/app-layer/data-mart','M0064','P001001004001','数据集市','full',16,'11/12/16/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (65,'主题域',NULL,NULL,'/data-modeling/warehouse-plan/app-layer/mart-subject','M0065','P001001004002','主题域','full',16,'11/12/16/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307046510167040,'业务过程详情',NULL,NULL,'/data-modeling/warehouse-plan/wh-layer/business-process/detail/:id','M1001','P001001003002','业务过程',NULL,63,'11/12/15/63/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307051331912704,'数据域详情',NULL,NULL,'/data-modeling/warehouse-plan/wh-layer/data-domain/detail/:id','M1002','P001001003001','数据域',NULL,62,'11/12/15/62/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307063554475008,'数仓分层详情',NULL,NULL,'/data-modeling/warehouse-plan/warehouse-layer/detail/:id','M1003','P001001002','数仓分层',NULL,14,'11/12/14/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307110164530176,'数据集市详情',NULL,NULL,'/data-modeling/warehouse-plan/app-layer/data-mart/:id','M1004','P001001004001','数据集市',NULL,64,'11/12/16/64/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307112590181376,'主题域详情',NULL,NULL,'/data-modeling/warehouse-plan/app-layer/mart-subject/:id','M1005','P001001004002','主题域',NULL,65,'11/12/16/65/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307124938736640,'业务分类详情',NULL,NULL,'/data-modeling/warehouse-plan/business-category/:id','M1007','P001001001','业务分类',NULL,13,'11/12/13/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307166298801152,'字段标准列表',NULL,NULL,'/data-modeling/data-standard/field/list/:id','M1008','P001002001','字段标准',NULL,18,'11/17/18/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307167129207808,'字段标准详情',NULL,NULL,'/data-modeling/data-standard/field/detail/:id','M1009','P001002001','字段标准',NULL,18,'11/17/18/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307169830437888,'数据字典列表',NULL,NULL,'/data-modeling/data-standard/enum/list/:id','M1010','P001002002','数据字典',NULL,19,'11/17/19/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307170930525184,'数据字典详情',NULL,NULL,'/data-modeling/data-standard/enum/detail/:id','M1011','P001002002','数据字典',NULL,19,'11/17/19/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307173890982912,'度量单位列表',NULL,NULL,'/data-modeling/data-standard/measure/list/:id','M1012','P001002003','度量单位',NULL,20,'11/17/20/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307174753371136,'度量单位详情',NULL,NULL,'/data-modeling/data-standard/measure/detail/:id','M1013','P001002003','度量单位',NULL,20,'11/17/20/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307313820369920,'Kafka创建',NULL,NULL,'/platform-management/address/data-source/kafka','M1026','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307314638947328,'Kafka编辑',NULL,NULL,'/platform-management/address/data-source/kafka/:id','M1027','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307315590759424,'Mysql创建',NULL,NULL,'/platform-management/address/data-source/mysql','M1028','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307316335182848,'Mysql编辑',NULL,NULL,'/platform-management/address/data-source/mysql/:id','M1029','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307317903655936,'ElasticSearch创建',NULL,NULL,'/platform-management/address/data-source/es','M1030','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307318751036416,'ElasticSearch编辑',NULL,NULL,'/platform-management/address/data-source/es/:id','M1031','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307319874323456,'Clickhouse创建',NULL,NULL,'/platform-management/address/data-source/ck','M1032','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307320519033856,'Clickhouse编辑',NULL,NULL,'/platform-management/address/data-source/ck/:id','M1033','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307321327780864,'Hive创建',NULL,NULL,'/platform-management/address/data-source/hive','M1034','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307321963414528,'Hive编辑',NULL,NULL,'/platform-management/address/data-source/hive/:id','M1035','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307368452326400,'创建集群',NULL,NULL,'/platform-management/cluster/management/create','M1036','P005003001','集群管理',NULL,54,'46/53/54/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307369760457728,'编辑集群',NULL,NULL,'/platform-management/cluster/management/edit/:id','M1037','P005003001','集群管理',NULL,54,'46/53/54/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307372307481600,'克隆集群',NULL,NULL,'/platform-management/cluster/management/clone/:cloneId','M1038','P005003001','集群管理',NULL,54,'46/53/54/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307381086848000,'创建存储集群',NULL,NULL,'/platform-management/cluster/storage-management-ck/create','M1039','P005003002','存储集群管理(CK)',NULL,55,'46/53/55/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307382258074624,'编辑存储集群',NULL,NULL,'/platform-management/cluster/storage-management-ck/edit/:id','M1040','P005003002','存储集群管理(CK)',NULL,55,'46/53/55/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307384665310208,'创建框架',NULL,NULL,'/platform-management/opts/create','M1041','P005004001','框架管理',NULL,57,'46/56/57/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307385630655488,'编辑框架',NULL,NULL,'/platform-management/opts/edit/:id','M1042','P005004001','框架管理',NULL,57,'46/56/57/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1307386868532224,'克隆框架',NULL,NULL,'/platform-management/opts/clone/:cloneId','M1043','P005004001','框架管理',NULL,57,'46/56/57/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309137253303296,'算子列表',NULL,NULL,'/platform-management/extension-package/job/list','M1044','P005005001','扩展包管理',NULL,59,'46/58/59/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309141488337920,'审计日志详情',NULL,NULL,'/platform-management/audit-log/detail/:id','M1045','P005006001','审计日志',NULL,61,'46/60/61/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309151048500224,'创建数据集成',NULL,NULL,'/data-dev/dev/data-ingestion/online/create/:businessFlowId','M1046','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309154588591104,'文件同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/file','M1047','P003001001','数据开发','rtdatacollection',1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309156844536832,'TCP/UDP同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/tcp-udp','M1048','P003001001','数据开发','rtdatacollection',1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309160059470848,'Kafka接入',NULL,NULL,'/data-dev/dev/data-ingestion/online/kafka','M1049','P003001001','数据开发','rtdatacollection',1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309161616180224,'SysLog同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/syslog','M1050','P003001001','数据开发','rtdatacollection',1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309172713260032,'实时开发',NULL,NULL,NULL,'M1051','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309180212020224,'编辑文件同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/file/:id','M1052','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309181667247104,'编辑TCP/UDP同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/tcp-udp/:id','M1053','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309183780422656,'编辑kafka同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/kafka/:id','M1054','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309185103660032,'编辑Syslog同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/syslog/:id','M1055','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309188023813120,'创建数据处理',NULL,NULL,'/data-dev/dev/data-process/online/index','M1056','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309189665588224,'创建管线作业',NULL,NULL,'/data-dev/dev/data-process/online/pipeline/create/:businessFlowId','M1057','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309191154205696,'创建SQL作业',NULL,NULL,'/data-dev/dev/data-process/online/flink-sql/create/:businessFlowId','M1058','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309195853169664,'创建Clickhouse存储作业',NULL,NULL,'/data-dev/dev/data-process/online/storage/ck','M1059','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309197317309440,'创建ElasticSearch存储作业',NULL,NULL,'/data-dev/dev/data-process/online/storage/es','M1060','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309198905672704,'创建Hive存储作业',NULL,NULL,'/data-dev/dev/data-process/online/hive/create/:businessFlowId','M1061','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309201079305216,'编辑管线作业',NULL,NULL,'/data-dev/dev/data-process/online/pipeline/edit/:id','M1062','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309202820432896,'克隆管线作业',NULL,NULL,'/data-dev/dev/data-process/online/pipeline/clone/:cloneId','M1063','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309204136330240,'编辑SQL作业',NULL,NULL,'/data-dev/dev/data-process/online/flink-sql/edit/:id','M1064','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309205502428160,'克隆SQL作业',NULL,NULL,'/data-dev/dev/data-process/online/flink-sql/clone/:cloneId','M1065','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309208037098496,'编辑Hive存储作业',NULL,NULL,'/data-dev/dev/data-process/online/hive/edit/:id','M1066','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309209121391616,'克隆Hive存储作业',NULL,NULL,'/data-dev/dev/data-process/online/hive/clone/:cloneId','M1067','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309211389101056,'编辑Clickhouse存储作业',NULL,NULL,'/data-dev/dev/data-process/online/storage/ck/:id','M1068','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309222309266432,'编辑ElasticSearch存储作业',NULL,NULL,'/data-dev/dev/data-process/online/storage/es/:id','M1069','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309231574483968,'克隆弹性作业',NULL,NULL,'/flexible-application/clone/:cloneId','M1070','P003002001','弹性作业',NULL,36,'32/35/36/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309232690889728,'创建弹性作业',NULL,NULL,'/flexible-application/create','M1071','P003002001','弹性作业',NULL,36,'32/35/36/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309233485415424,'编辑弹性作业',NULL,NULL,'/flexible-application/edit/:id','M1072','P003002001','弹性作业',NULL,36,'32/35/36/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309235554255872,'弹性作业诊断',NULL,NULL,'/flexible-application/:id/diagnosis','M1073','P003002001','弹性作业',NULL,36,'32/35/36/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309241396495360,'导入列表',NULL,NULL,'/import/management/list','M1074','P002001001','导入数据',NULL,31,'29/30/31/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309270114501632,'导入详情',NULL,NULL,'/import/management/detail/:id','M1075','P002001001','导入数据',NULL,31,'29/30/31/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309273219236864,'任务详情',NULL,NULL,'/ops/ingestion/cell-agent/detail/:id','M1076','P004001001','采集任务运维',NULL,39,'37/38/39/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309281323942912,'主机资源使用情况',NULL,NULL,'/ops/ingestion/cell-agent/task-performance-monitor/:cellId/:agentId/:process','M1077','P004001001','采集任务运维',NULL,39,'37/38/39/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309283666494464,'性能监控',NULL,NULL,'/ops/ingestion/cell-agent/job-performance-monitor/:id','M1078','P004001001','采集任务运维',NULL,39,'37/38/39/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309320061715456,'实时任务诊断',NULL,NULL,'/ops/realtime/pipeline/diagnose/:id','M1079','P004002001','实时任务运维',NULL,42,'37/41/42/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1309321233236992,'存储任务诊断',NULL,NULL,'/ops/realtime/storage/:type/performance-monitor/:id','M1080','P004002002','存储任务运维',NULL,43,'37/41/43/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312181422621696,'创建派生指标',NULL,NULL,'/data-modeling/data-indicator/derivativeIndicator/create','M1081','P001004001','派生指标',NULL,25,'11/24/25/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312182374335488,'编辑派生指标',NULL,NULL,'/data-modeling/data-indicator/derivativeIndicator/edit/:id','M1082','P001004001','派生指标',NULL,25,'11/24/25/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312183349806080,'克隆派生指标',NULL,NULL,'/data-modeling/data-indicator/derivativeIndicator/clone/:cloneId','M1083','P001004001','派生指标',NULL,25,'11/24/25/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312184753849344,'创建原子指标',NULL,NULL,'/data-modeling/data-indicator/atomicIndicator/create','M1084','P001004002','原子指标',NULL,26,'11/24/26/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312186769802240,'编辑原子指标',NULL,NULL,'/data-modeling/data-indicator/atomicIndicator/edit/:id','M1085','P001004002','原子指标',NULL,26,'11/24/26/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312188074460160,'创建修饰词',NULL,NULL,'/data-modeling/data-indicator/adjunct/create','M1086','P001004003','修饰词',NULL,27,'11/24/27/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312188927869952,'编辑修饰词',NULL,NULL,'/data-modeling/data-indicator/adjunct/edit/:id','M1087','P001004003','修饰词',NULL,27,'11/24/27/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312189939418112,'创建时间周期',NULL,NULL,'/data-modeling/data-indicator/timePeriod/create','M1088','P001004004','时间周期',NULL,28,'11/24/28/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312190854300672,'编辑时间周期',NULL,NULL,'/data-modeling/data-indicator/timePeriod/edit/:id','M1090','P001004004','时间周期',NULL,28,'11/24/28/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312435521881088,'时间周期详情',NULL,NULL,'/data-modeling/data-indicator/timePeriod/detail/:id','M1091','P001004004','时间周期',NULL,28,'11/24/28/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312437183284224,'修饰词详情',NULL,NULL,'/data-modeling/data-indicator/adjunct/detail/:id','M1092','P001004003','修饰词',NULL,27,'11/24/27/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312438581199872,'原子指标详情',NULL,NULL,'/data-modeling/data-indicator/atomicIndicator/detail/:id','M1093','P001004002','原子指标',NULL,26,'11/24/26/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312440069915648,'派生指标详情',NULL,NULL,'/data-modeling/data-indicator/derivativeIndicator/detail/:id','M1094','P001004001','派生指标',NULL,25,'11/24/25/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312877385516032,'菜单配置',NULL,NULL,'/auth','M1096',NULL,NULL,NULL,0,'',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312894479696896,'派生指标列表',NULL,NULL,'/data-modeling/data-indicator/derivativeIndicator/list','M1097','P001004001','派生指标',NULL,25,'11/24/25/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312896264897536,'原子指标列表',NULL,NULL,'/data-modeling/data-indicator/atomicIndicator/list','M1098','P001004002','原子指标',NULL,26,'11/24/26/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312897065452544,'修饰词列表',NULL,NULL,'/data-modeling/data-indicator/adjunct/list','M1099','P001004003','修饰词',NULL,27,'11/24/27/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1312897852277760,'时间周期列表',NULL,NULL,'/data-modeling/data-indicator/timePeriod/list','M1100','P001004004','时间周期',NULL,28,'11/24/28/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1323400428717056,'版本对比',NULL,NULL,'/version/compare/:type/:id','M1101','P001003001','维度建模',NULL,23,'11/22/23/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1323941644895232,'维度建模',NULL,NULL,NULL,'M1102','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1323941644895233,'Kafka',NULL,NULL,'/data-dev/dev/management/kafka/:id','M1103','P003001001','数据开发',NULL,1323941644895232,'32/33/34/1323941644895232/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1323947443717120,'Hive',NULL,NULL,'/data-dev/dev/management/hive/:id','M1104','P003001001','数据开发',NULL,1323941644895232,'32/33/34/1323941644895232/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1323948567725056,'ElasticSearch',NULL,NULL,'/data-dev/dev/management/es/:id','M1105','P003001001','数据开发',NULL,1323941644895232,'32/33/34/1323941644895232/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1323949723321344,'Clickhouse',NULL,NULL,'/data-dev/dev/management/ck/:id','M1106','P003001001','数据开发',NULL,1323941644895232,'32/33/34/1323941644895232/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1323950867547136,'Mysql',NULL,NULL,'/data-dev/dev/management/mysql/:id','M1107','P003001001','数据开发',NULL,1323941644895232,'32/33/34/1323941644895232/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1329170676646912,'依赖关系',NULL,NULL,'/data-modeling/dependencies','M1108',NULL,NULL,NULL,0,'',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1329784043308032,'创建导入',NULL,NULL,'/import/management/create','M1109','P002001001','导入数据',NULL,31,'29/30/31/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1329892801250304,'克隆原子指标',NULL,NULL,'/data-modeling/data-indicator/atomicIndicator/clone/:cloneId','M1110','P001004002','原子指标',NULL,26,'11/24/26/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1329894660801536,'克隆修饰词',NULL,NULL,'/data-modeling/data-indicator/adjunct/clone/:cloneId','M1111','P001004003','修饰词',NULL,27,'11/24/27/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1329895402636288,'克隆时间周期',NULL,NULL,'/data-modeling/data-indicator/timePeriod/clone/:cloneId','M1112','P001004004','时间周期',NULL,28,'11/24/28/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1329909506409472,'导出',NULL,NULL,'/file/export/:id','M1113',NULL,NULL,NULL,0,'',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1335764428817408,'数据质量',NULL,'iconfont icon-datasafe-quality',NULL,'M1114','P006000000','数据质量','full',0,'',0,0,NULL,'menu',31,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1335768147330048,'实时数据质量',NULL,'iconfont icon-restore-line',NULL,'M1116','P006001000','实时数据质量','full',1335764428817408,'1335764428817408/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1335773932422144,'质量概览',NULL,'iconfont icon-view-line','/data-quality/realtime/overview','M1117','P006001001','质量概览','full',1335768147330048,'1335764428817408/1335768147330048/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1335775211324416,'检测任务',NULL,'iconfont icon-monitor-line','/data-quality/realtime/task/list','M1118','P006001002','检测任务','full',1335768147330048,'1335764428817408/1335768147330048/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1335776653542400,'规则模板',NULL,'iconfont icon-rulemodel-line','/data-quality/realtime/rule-template/list','M1119','P006001003','规则模板','full',1335768147330048,'1335764428817408/1335768147330048/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1343203322659840,'创建检测任务',NULL,NULL,'/data-quality/realtime/task/create','M1121','P006001002','检测任务',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1343204559127552,'编辑检测任务',NULL,NULL,'/data-quality/realtime/task/edit/:id','M1122','P006001002','检测任务',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1343205561140224,'检测任务详情',NULL,NULL,'/data-quality/realtime/task/detail/:id','M1123','P006001002','检测任务',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1343208026899456,'创建规则模板',NULL,NULL,'/data-quality/realtime/rule-template/create','M1124','P006001003','规则模板',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1343209184789504,'编辑规则模板',NULL,NULL,'/data-quality/realtime/rule-template/edit/:id','M1125','P006001003','规则模板',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1343210428564480,'克隆规则模板',NULL,NULL,'/data-quality/realtime/rule-template/clone/:cloneId','M1126','P006001003','规则模板',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1343212270519296,'克隆检测任务',NULL,NULL,'/data-quality/realtime/task/clone/:cloneId','M1127','P006001002','检测任务',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1351636365411328,'定时作业',NULL,'iconfont icon-restore-line',NULL,'M1128','P003003000','定时作业',NULL,32,'32/',0,0,NULL,'menu',3,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1351754569057280,'调度日志',NULL,'iconfont icon-dislog-line','/job-timing/dispatch-log','M1129','P003003002','调度日志',NULL,1351636365411328,'32/1351636365411328/',0,0,NULL,'menu',2,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1351768449942528,'作业管理',NULL,'iconfont icon-task-line','/job-timing/job-management','M1130','P003003001','作业管理',NULL,1351636365411328,'32/1351636365411328/',0,0,NULL,'menu',1,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1351771934786560,'执行器管理',NULL,'iconfont icon-execute-line','/job-timing/actuator','M1131','P003003003','执行器管理',NULL,1351636365411328,'32/1351636365411328/',0,0,NULL,'menu',3,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1351839912788992,'查看规则模板',NULL,NULL,'/data-quality/realtime/rule-template/detail/:detailId','M1132','P006001003','规则模板',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1355323670692864,'创建定时作业',NULL,NULL,'/job-timing/job-management/create','M1133','P003003001','作业管理',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1365920063292416,'编辑定时作业',NULL,NULL,'/job-timing/job-management/edit/:id','M1134','P003003001','作业管理',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1366015818204160,'克隆定时作业',NULL,NULL,'/job-timing/job-management/clone/:cloneId','M1135','P003003001','作业管理',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1368687722529792,'查看调度明细',NULL,NULL,'/job-timing/dispatch-log/detail/:logId','M1136','P003003002','调度日志',NULL,1351754569057280,'32/1351636365411328/1351754569057280/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1369173689500672,'表创建',NULL,NULL,'/data-modeling/dimension-modeling/modeling/table/create','M1137','P001003001','维度建模',NULL,23,'11/22/23/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1369221036344320,'表编辑',NULL,NULL,'/data-modeling/dimension-modeling/modeling/table/edit/:id','M1138','P001003001','维度建模',NULL,23,'11/22/23/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1369222594823168,'表克隆',NULL,NULL,'/data-modeling/dimension-modeling/modeling/table/clone/:cloneId','M1139','P001003001','维度建模',NULL,23,'11/22/23/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1369484668208128,'维度创建',NULL,NULL,'/data-modeling/dimension-modeling/modeling/dimension/create','M1140','P001003001','维度建模',NULL,23,'11/22/23/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1369486902854656,'维度编辑',NULL,NULL,'/data-modeling/dimension-modeling/modeling/dimension/edit/:id','M1141','P001003001','维度建模',NULL,23,'11/22/23/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1369487733883904,'维度克隆',NULL,NULL,'/data-modeling/dimension-modeling/modeling/dimension/clone/:cloneId','M1142','P001003001','维度建模',NULL,23,'11/22/23/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1382903756915712,'离线开发',NULL,NULL,NULL,'M1143','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1382907679835136,'创建数据处理',NULL,NULL,'/data-dev/dev/data-process/offline/index','M1144','P003001001','数据开发',NULL,1382903756915712,'32/33/34/1382903756915712/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1383529044149248,'数据集成',NULL,NULL,NULL,'M1145','P003001001','数据开发',NULL,1382903756915712,'32/33/34/1382903756915712/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1383529695020032,'数据处理',NULL,NULL,NULL,'M1146','P003001001','数据开发',NULL,1382903756915712,'32/33/34/1382903756915712/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1383531504600064,'创建管线作业',NULL,NULL,'/data-dev/dev/data-process/batch-process/create','M1147','P003001001','数据开发',NULL,1383529695020032,'32/33/34/1382903756915712/1383529695020032/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1383532695520256,'编辑管线作业',NULL,NULL,'/data-dev/dev/data-process/batch-process/edit/:id','M1148','P003001001','数据开发',NULL,1383529695020032,'32/33/34/1382903756915712/1383529695020032/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1383533697795072,'克隆管线作业',NULL,NULL,'/data-dev/dev/data-process/batch-process/clone/:cloneId','M1149','P003001001','数据开发',NULL,1383529695020032,'32/33/34/1382903756915712/1383529695020032/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1383694132806656,'离线任务运维',NULL,'iconfont icon-wifi_off-fill text-sm',NULL,'M1152','P004003000','离线任务运维',NULL,37,'37/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1383697661953024,'离线任务运维',NULL,'iconfont icon-wifi_off-fill text-sm','/ops/offline/pipeline','M1153','P004003001','离线任务运维',NULL,1383694132806656,'37/1383694132806656/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1385730201322496,'离线任务诊断',NULL,NULL,'/ops/offline/pipeline/diagnose/:id','M1154','P004003001','离线任务运维',NULL,1383697661953024,'37/1383694132806656/1383697661953024/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1385935391327232,'管线作业运行日志',NULL,NULL,'/pipeline/running-log','M1155','P004000000','运维中心',NULL,37,'37/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1385937446372352,'运行日志',NULL,NULL,'/flexible-application/running-log','M1156','P003002001','弹性作业',NULL,36,'32/35/36/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1388900177806336,'Flink会话管理',NULL,'iconfont icon-chat-line text-sm','/platform-management/cluster/flink-yarn-session/list','M1157','P005003003','Flink会话管理',NULL,53,'46/53/',0,0,NULL,'menu',0,NULL,1,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1388901568644096,'创建Flink会话',NULL,NULL,'/platform-management/cluster/flink-yarn-session/create','M1158','P005003003','Flink会话管理',NULL,1388900177806336,'46/53/1388900177806336/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1392251713684480,'任务日志诊断',NULL,NULL,'/platform-management/cluster/flink-yarn-session/diagnose/:id','M1159','P005003003','Flink会话管理',NULL,1388900177806336,'46/53/1388900177806336/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1395109433345024,'编辑Flink会话',NULL,NULL,'/platform-management/cluster/flink-yarn-session/edit/:id','M1160','P005003003','Flink会话管理',NULL,1388900177806336,'46/53/1388900177806336/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1403182154253312,'查看储存集群',NULL,NULL,'/platform-management/cluster/storage-management-ck/detail/:id','M1161','P005003002','存储集群管理(CK)',NULL,55,'46/53/55/',0,0,NULL,'menu',0,NULL,0,0,'2023-06-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1522171449934848,'克隆文件同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/clone/file/:cloneId','M1162','P003001001','数据开发',NULL,1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1522181958370304,'克隆TCP',NULL,NULL,'/data-dev/dev/data-ingestion/online/clone/tcp-udp/:cloneId','M1163','P003001001','数据开发',NULL,1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1522183978615808,'克隆kafka',NULL,NULL,'/data-dev/dev/data-ingestion/online/clone/kafka/:cloneId','M1164','P003001001','数据开发',NULL,1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1522185899475968,'克隆SysLog',NULL,NULL,'/data-dev/dev/data-ingestion/online/clone/syslog/:cloneId','M1165','P003001001','数据开发',NULL,1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1527195427013632,'克隆CK作业',NULL,NULL,'/data-dev/dev/data-process/online/storage/clone/ck/:cloneId','M1175','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1527199354127360,'克隆ES作业',NULL,NULL,'/data-dev/dev/data-process/online/storage/clone/es/:cloneId','M1176','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1530708952679424,'数据服务',NULL,'iconfont icon-application-line','/data-service','M1166','P003004000','数据服务',NULL,32,'32/',0,0,NULL,'menu',1,NULL,1,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1530715876164608,'服务开发',NULL,'iconfont icon-datacode-line','/data-service/dev','M1167','P003004001','服务开发',NULL,1530708952679424,'32/1530708952679424/',0,0,NULL,'menu',0,NULL,1,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1530725044618240,'服务管理',NULL,'iconfont icon-dataservice-line','/data-service/management','M1168','P003004002','服务管理',NULL,1530708952679424,'32/1530708952679424/',0,0,NULL,'menu',0,NULL,1,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1530727473284096,'授权管理',NULL,'iconfont icon-empower-line','/data-service/auth','M1169','P003004003','授权管理',NULL,1530708952679424,'32/1530708952679424/',0,0,NULL,'menu',0,NULL,1,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1552782625309696,'查看授权管理',NULL,NULL,'/data-service/auth/detail/:detailId','M1174','P003004003','授权管理',NULL,1530727473284096,'32/1530708952679424/1530727473284096/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1561629237380096,'新建服务',NULL,NULL,'/data-service/dev/create','M1170','P003004001','服务开发',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1562087218840576,'服务详情',NULL,NULL,'/data-service/dev/detail/:apiId','M1171','P003004001','服务开发',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1562094208156672,'编辑服务',NULL,NULL,'/data-service/dev/edit/:editId','M1172','P003004001','服务开发',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1584610487862272,'版本对比',NULL,NULL,'/version/compare/data-service/dev','M1173','P003004001','服务开发',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1607220851344384,'系统管理',NULL,'iconfont icon-dns-line text-sm',NULL,'M1178','P005007000','系统管理',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1607224288904192,'Redis创建',NULL,NULL,'/platform-management/address/data-source/redis','M1180','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1607224909628416,'系统管理',NULL,'iconfont icon-dns-line text-sm','/system/config','M1179','P005007001','系统管理',NULL,1607220851344384,'46/1607220851344384/',0,0,NULL,'menu',0,NULL,1,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1607225617384448,'Redis编辑',NULL,NULL,'/platform-management/address/data-source/redis/:id','M1181','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1609257690366976,'VictoriaMetrics创建',NULL,NULL,'/platform-management/address/data-source/victoria-metrics','M1182','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1609259212145664,'VictoriaMetrics编辑',NULL,NULL,'/platform-management/address/data-source/victoria-metrics/:id','M1183','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1609810320950272,'Nebula创建',NULL,NULL,'/platform-management/address/data-source/nebula','M1184','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1609811353109504,'Nebula编辑',NULL,NULL,'/platform-management/address/data-source/nebula/:id','M1185','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1624725425816576,'Nebula',NULL,NULL,'/data-dev/dev/management/nebula/:id','M1186','P003001001','数据开发',NULL,1323941644895232,'32/33/34/1323941644895232/',0,0,NULL,'menu',0,NULL,0,0,'2023-08-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640434404819968,'对象模型',NULL,'iconfont icon-application-line text-sm','/object/modeling/search','M1188','P001003002001','对象模型','full',1647057048667136,'11/22/1647057048667136/',0,0,NULL,'menu',0,NULL,1,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640436000523264,'指标模型',NULL,'iconfont icon-readermode-line','/metric/modeling/search','M1198','P001003003001','指标模型','full',1647064256578560,'11/22/1647064256578560/',0,0,NULL,'menu',0,NULL,1,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640437689189376,'创建',NULL,'iconfont icon-application-line text-primary text-sm','/object/modeling/create','M1189','P001003002001','对象模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640438776693760,'编辑',NULL,'iconfont icon-application-line text-primary text-sm','/object/modeling/edit/:editId','M1190','P001003002001','对象模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640439816061952,'克隆',NULL,'iconfont icon-application-line text-primary text-sm','/object/modeling/clone/:cloneId','M1191','P001003002001','对象模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640440901239808,'列表',NULL,'iconfont icon-application-line text-primary text-sm','/object/modeling/search','M1192','P001003002001','对象模型','full',1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640488989426688,'关系模型详情',NULL,NULL,'/object/relation/detail/:id','M1195','P001003002002','关系模型',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640549978637312,'详情',NULL,'iconfont  icon-readermode-line','/metric/modeling/detail/:id','M1200','P001003003001','指标模型',NULL,1640436000523264,'11/22/1647064256578560/1640436000523264/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640550860751872,'列表',NULL,'iconfont icon-view_table-line','/metric/modeling/search','M1201','P001003003001','指标模型','full',1640436000523264,'11/22/1647064256578560/1640436000523264/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1640552688124928,'详情',NULL,'iconfont icon-application-line text-primary text-sm','/object/modeling/detail/:id','M1193','P001003002001','对象模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1646992046720000,'指标来源',NULL,'iconfont icon-sticky_note-line','/metric/source/list','M1199','P001003003002','指标来源','full',1647064256578560,'11/22/1647064256578560/',0,0,NULL,'menu',0,NULL,1,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1647057048667136,'对象建模',NULL,'iconfont icon-object-fill','/object/modeling','M1187','P001003002','对象建模','full',22,'11/22/',0,0,NULL,'menu',0,NULL,1,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1647061341668352,'关系模型',NULL,'iconfont icon-relationshipo-line text-sm','/object/relation/list','M1194','P001003002002','关系模型','full',1647057048667136,'11/22/1647057048667136/',0,0,NULL,'menu',0,NULL,1,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1647064256578560,'指标建模',NULL,'iconfont icon-sort-line','/metric/modeling','M1197','P001003003','指标建模','full',22,'11/22/',0,0,NULL,'menu',0,NULL,1,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1647104782730240,'关系模型列表',NULL,NULL,'/object/relation/list','M1196','P001003002002','关系模型','full',1647061341668352,'11/22/1647057048667136/1647061341668352/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1647124960445440,'列表',NULL,NULL,'/metric/source/list','M1203','P001003003002','指标来源','full',1646992046720000,'11/22/1647064256578560/1646992046720000/',0,0,NULL,'menu',0,NULL,0,0,'2023-09-15 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1734435321480192,'权限管理',NULL,'iconfont icon-icon-empowerpeople',NULL,'M1204','P005008000','权限管理',NULL,46,'46/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1734440718697472,'用户管理',NULL,'iconfont icon-icon-setpeople','/rights-management/user','M1207','P005008001001','用户管理',NULL,1748607326094336,'46/1734435321480192/1748607326094336/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1734449730782208,'项目管理',NULL,'iconfont icon-icon-archivefiles','/rights-management/project','M1205','P005008002','项目管理','projectmanagement',1734435321480192,'46/1734435321480192/',0,0,NULL,'menu',1,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1748600261936128,'用户组管理',NULL,'iconfont icon-icon-Usergroup','/rights-management/user-group','M1208','P005008001002','用户组管理',NULL,1748607326094336,'46/1734435321480192/1748607326094336/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1748602778158080,'角色管理',NULL,'iconfont icon-icon-Rolemanagement','/rights-management/role','M1209','P005008001003','角色管理',NULL,1748607326094336,'46/1734435321480192/1748607326094336/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1748607326094336,'权限管理',NULL,'iconfont icon-icon-empowerpeople','/rights-management','M1206','P005008001','权限管理',NULL,1734435321480192,'46/1734435321480192/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1748613384700928,'权限管理',NULL,'iconfont icon-icon-Authoritymanagement','/rights-management/rights','M1210','P005008001004','权限管理',NULL,1748607326094336,'46/1734435321480192/1748607326094336/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1748962019902464,'创建|编辑|删除',NULL,NULL,NULL,'B001','business_category:write','创建|编辑|删除',NULL,13,'11/12/13/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1748967609992192,'创建','post',NULL,'/api/v2/data/modeling/business-category','A001','business_category:write','创建|编辑|删除',NULL,13,'11/12/13/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1749003877581824,'更新','put',NULL,'/api/v2/data/modeling/business-category/{id}','A002','business_category:write','创建|编辑|删除',NULL,13,'11/12/13/',1,0,'tb_business_category','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1749006100988928,'删除','delete',NULL,'/api/v2/data/modeling/business-category/{id}','A003','business_category:write','创建|编辑|删除',NULL,13,'11/12/13/',1,0,'tb_business_category','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1751835732214784,'创建工作流',NULL,NULL,'/data-dev/dev/data-process/bpm-process/create','M1211','P003001001','数据开发',NULL,1383529695020032,'32/33/34/1382903756915712/1383529695020032/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1751838661379072,'编辑工作流',NULL,NULL,'/data-dev/dev/data-process/bpm-process/edit/:id','M1212','P003001001','数据开发',NULL,1383529695020032,'32/33/34/1382903756915712/1383529695020032/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1751841355793408,'克隆工作流',NULL,NULL,'/data-dev/dev/data-process/bpm-process/clone/:cloneId','M1213','P003001001','数据开发',NULL,1383529695020032,'32/33/34/1382903756915712/1383529695020032/',0,0,NULL,'menu',0,NULL,1,0,'2023-11-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759787257463808,'创建|编辑|删除',NULL,NULL,NULL,'B002','warehouse_layer:write','创建|编辑|删除',NULL,14,'11/12/14/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759789807272960,' 创建','post',NULL,'/api/v2/data/modeling/warehouse-layer','A004','warehouse_layer:write','创建|编辑|删除',NULL,14,'11/12/14/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759801892373504,'更新','put',NULL,'/api/v2/data/modeling/warehouse-layer/{id}','A005','warehouse_layer:write','创建|编辑|删除',NULL,14,'11/12/14/',1,0,'tb_warehouse_layer','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759803241464832,'删除','delete',NULL,'/api/v2/data/modeling/warehouse-layer/{id}','A006','warehouse_layer:write','创建|编辑|删除',NULL,14,'11/12/14/',1,0,'tb_warehouse_layer','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759817761686528,'创建|编辑|删除',NULL,NULL,NULL,'B003','data_domain:write','创建|编辑|删除',NULL,62,'11/12/15/62/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759828775207936,'创建','post',NULL,'/api/v2/data/modeling/domain','A007','data_domain:write','创建|编辑|删除',NULL,62,'11/12/15/62/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759831073358848,'更新','put',NULL,'/api/v2/data/modeling/domain/{id}','A008','data_domain:write','创建|编辑|删除',NULL,62,'11/12/15/62/',1,0,'tb_data_domain','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759833515000832,'删除','delete',NULL,'/api/v2/data/modeling/domain/{id}','A009','data_domain:write','创建|编辑|删除',NULL,62,'11/12/15/62/',1,0,'tb_data_domain','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759851675026432,'创建|编辑|删除',NULL,NULL,NULL,'B004','business_process:write','创建|编辑|删除',NULL,63,'11/12/15/63/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759854607467520,'创建','post',NULL,'/api/v2/data/modeling/bus-process','A010','business_process:write','创建|编辑|删除',NULL,63,'11/12/15/63/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759857667376128,'更新','put',NULL,'/api/v2/data/modeling/bus-process/{id}','A011','business_process:write','创建|编辑|删除',NULL,63,'11/12/15/63/',1,0,'tb_business_process','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759864137876480,'删除','delete',NULL,'/api/v2/data/modeling/bus-process/{id}','A012','business_process:write','创建|编辑|删除',NULL,63,'11/12/15/63/',1,0,'tb_business_process','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759869831775232,'创建|编辑|删除',NULL,NULL,NULL,'B005','data_mart:write','创建|编辑|删除',NULL,64,'11/12/16/64/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759878715835392,'创建','post',NULL,'/api/v2/data/modeling/mart','A013','data_mart:write','创建|编辑|删除',NULL,64,'11/12/16/64/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759881794946048,'更新','put',NULL,'/api/v2/data/modeling/mart/{id}','A014','data_mart:write','创建|编辑|删除',NULL,64,'11/12/16/64/',1,0,'tb_data_mart','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759883286447104,'删除','delete',NULL,'/api/v2/data/modeling/mart/{id}','A015','data_mart:write','创建|编辑|删除',NULL,64,'11/12/16/64/',1,0,'tb_data_mart','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759888572122112,'创建|编辑|删除',NULL,NULL,NULL,'B006','data_subject:write','创建|编辑|删除',NULL,65,'11/12/16/65/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759905396884480,'创建','post',NULL,'/api/v2/data/modeling/subject','A016','data_subject:write','创建|编辑|删除',NULL,65,'11/12/16/65/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759906951070720,'更新','put',NULL,'/api/v2/data/modeling/subject/{id}','A017','data_subject:write','创建|编辑|删除',NULL,65,'11/12/16/65/',1,0,'tb_data_subject','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1759923231163392,'删除','delete',NULL,'/api/v2/data/modeling/subject/{id}','A018','data_subject:write','创建|编辑|删除',NULL,65,'11/12/16/65/',1,0,'tb_data_subject','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760006789432320,'创建|编辑|删除',NULL,NULL,NULL,'B007','column_dict:write','创建|编辑|删除',NULL,18,'11/17/18/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760010503717888,'创建','post',NULL,'/api/v2/data/modeling/data-standard/dict/column','A019','column_dict:write','创建|编辑|删除',NULL,18,'11/17/18/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760012882445312,'更新','put',NULL,'/api/v2/data/modeling/data-standard/column/{id}','A020','column_dict:write','创建|编辑|删除',NULL,18,'11/17/18/',1,0,'tb_column_dict','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760014390756352,'删除','delete',NULL,'/api/v2/data/modeling/data-standard/column/{id}','A021','column_dict:write','创建|编辑|删除',NULL,18,'11/17/18/',1,0,'tb_column_dict','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760021191001088,'创建|编辑|删除',NULL,NULL,NULL,'B008','dict_enum:write','创建|编辑|删除',NULL,19,'11/17/19/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760023186637824,'创建','post',NULL,'/api/v2/data/modeling/data-standard/enum','A022','dict_enum:write','创建|编辑|删除',NULL,19,'11/17/19/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760025671730176,'更新','put',NULL,'/api/v2/data/modeling/data-standard/enum/{id}','A023','dict_enum:write','创建|编辑|删除',NULL,19,'11/17/19/',1,0,'tb_dict_enum','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760030222451712,'删除','delete',NULL,'/api/v2/data/modeling/data-standard/enum/{id}','A024','dict_enum:write','创建|编辑|删除',NULL,19,'11/17/19/',1,0,'tb_dict_enum','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760033724335104,'创建|编辑|删除',NULL,NULL,NULL,'B009','measure_unit:write','创建|编辑|删除',NULL,20,'11/17/20/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760035133326336,'创建','post',NULL,'/api/v2/data/modeling/data-standard/measure','A025','measure_unit:write','创建|编辑|删除',NULL,20,'11/17/20/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760036268934144,'更新','put',NULL,'/api/v2/data/modeling/data-standard/measure/{id}','A026','measure_unit:write','创建|编辑|删除',NULL,20,'11/17/20/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760039220544512,'删除','delete',NULL,'/api/v2/data/modeling/data-standard/measure/{id}','A027','measure_unit:write','创建|编辑|删除',NULL,20,'11/17/20/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760046113588224,'创建|编辑|删除',NULL,NULL,NULL,'B010','name_dict:write','创建|编辑|删除',NULL,21,'11/17/21/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760048617751552,'创建','post',NULL,'/api/v2/data/modeling/data-standard/name','A028','name_dict:write','创建|编辑|删除',NULL,21,'11/17/21/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760050857346048,'更新','put',NULL,'/api/v2/data/modeling/data-standard/name/{id}','A029','name_dict:write','创建|编辑|删除',NULL,21,'11/17/21/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760052529169408,'删除','delete',NULL,'/api/v2/data/modeling/data-standard/name/{id}','A030','name_dict:write','创建|编辑|删除',NULL,21,'11/17/21/',1,0,'','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760061792945152,'创建|编辑|删除',NULL,NULL,NULL,'B011','data_model:write','创建|编辑|删除',NULL,23,'11/22/23/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760062986257408,'发布',NULL,NULL,NULL,'B012','data_model:deploy','发布',NULL,23,'11/22/23/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760074449912832,'创建','post',NULL,'/api/v2/data/modeling/table','A031','data_model:write','创建|编辑|删除',NULL,23,'11/22/23/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760075557143552,'更新','put',NULL,'/api/v2/data/modeling/table/{id}','A032','data_model:write','创建|编辑|删除',NULL,23,'11/22/23/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760077702628352,'删除','delete',NULL,'/api/v2/data/modeling/table/{id}','A033','data_model:write','创建|编辑|删除',NULL,23,'11/22/23/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760083698025472,'发布','post',NULL,'/api/v2/data/modeling/table-deploy','A034','data_model:deploy','发布',NULL,23,'11/22/23/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760088160633856,'下线','get',NULL,'/api/v2/data/modeling/table/offline/{id}','A035','data_model:deploy','发布',NULL,23,'11/22/23/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760166667551744,'创建|编辑|删除模型',NULL,NULL,NULL,'B013','object_model:write','创建|编辑|删除模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760167856735232,'发布',NULL,NULL,NULL,'B014','object_model:deploy','发布',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760172294767616,'创建','post',NULL,'/api/v2/data-modeling/object-table','A036','object_model:write','创建|编辑|删除模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760174543406080,'更新','put',NULL,'/api/v2/data-modeling/object-table/{id}','A037','object_model:write','创建|编辑|删除模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760187436073984,'删除','delete',NULL,'/api/v2/data-modeling/object-table/{id}','A038','object_model:write','创建|编辑|删除模型',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760195898672128,'发布','post',NULL,'/api/v2/data/modeling/table-deploy','A039','object_model:deploy','发布',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760201417229312,'下线','get',NULL,'/api/v2/data-modeling/object-table/offline/{id}','A040','object_model:deploy','发布',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760217533121536,'创建|编辑|删除分类',NULL,NULL,NULL,'B015','object_category:write','创建|编辑|删除分类',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760222046094336,'创建','post',NULL,'/api/v2/data-modeling/object-category','A041','object_category:write','创建|编辑|删除分类',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760223891817472,'更新','put',NULL,'/api/v2/data-modeling/object-category/{id}','A042','object_category:write','创建|编辑|删除分类',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,'tb_object_category','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760227823977472,'删除','delete',NULL,'/api/v2/data-modeling/object-category/{id}','A043','object_category:write','创建|编辑|删除分类',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',1,0,'tb_object_category','api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760243523388416,'创建|编辑|删除关系路径',NULL,NULL,NULL,'B016','relation_path:write','创建|编辑|删除关系路径',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760244483195904,'创建|编辑|删除关系类型',NULL,NULL,NULL,'B017','relation_type:write','创建|编辑|删除关系类型',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760245369177088,'创建|编辑|删除关系约束',NULL,NULL,NULL,'B018','object_relation:write','创建|编辑|删除关系约束',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760253300933632,'创建关系类型','post',NULL,'/api/v2/data-modeling/object-relation-type','A044','relation_type:write','创建|编辑|删除关系类型',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760255322063872,'更新关系类型','put',NULL,'/api/v2/data-modeling/object-relation-type/{id}','A045','relation_type:write','创建|编辑|删除关系类型',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760257282737152,'删除关系类型','delete',NULL,'/api/v2/data-modeling/object-relation-type/{id}','A046','relation_type:write','创建|编辑|删除关系类型',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760259613623296,'创建关系路径','post',NULL,'/api/v2/data-modeling/object-relation-path','A047','relation_path:write','创建|编辑|删除关系路径',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760261772116992,'更新关系路径','put',NULL,'/api/v2/data-modeling/object-relation-path/{id}','A048','relation_path:write','创建|编辑|删除关系路径',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760263173178368,'删除关系路径','delete',NULL,'/api/v2/data-modeling/object-relation-path/{id}','A049','relation_path:write','创建|编辑|删除关系路径',NULL,1647061341668352,'11/22/1647057048667136/1647061341668352/',1,0,NULL,'api',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760278840017920,'创建|编辑|删除指标集',NULL,NULL,NULL,'B019','metric_set:write','创建|编辑|删除指标集',NULL,1640436000523264,'11/22/1647064256578560/1640436000523264/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760362194568192,'创建|编辑|删除指标项',NULL,NULL,NULL,'B020','metric_item:write','创建|编辑|删除指标项',NULL,1640436000523264,'11/22/1647064256578560/1640436000523264/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1760366709933056,'绑定指标来源',NULL,NULL,NULL,'B021','metric_item:source','绑定指标来源',NULL,1640436000523264,'11/22/1647064256578560/1640436000523264/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762187704534016,'创建|编辑|删除',NULL,NULL,NULL,'B022','metric_source:write','创建|编辑|删除',NULL,1646992046720000,'11/22/1647064256578560/1646992046720000/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762193134552064,'创建|编辑|删除',NULL,NULL,NULL,'B023','derivative_indicator:write','创建|编辑|删除',NULL,25,'11/24/25/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762196896678912,'创建|编辑|删除',NULL,NULL,NULL,'B024','atomic_indicator:write','创建|编辑|删除',NULL,26,'11/24/26/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762233557058560,'创建|编辑|删除',NULL,NULL,NULL,'B025','data_adjunct:write','创建|编辑|删除',NULL,27,'11/24/27/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762236206482432,'创建|编辑|删除',NULL,NULL,NULL,'B026','time_period:write','创建|编辑|删除',NULL,28,'11/24/28/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762277803492352,'创建|编辑|删除|调试|发布',NULL,NULL,NULL,'B027','data_develop:write','创建|编辑|删除|调试|发布|试跑|上下线',NULL,34,'32/33/34/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762292373128192,'创建|编辑|删除|发布',NULL,NULL,NULL,'B028','application:write','创建|编辑|删除|发布',NULL,36,'32/35/36/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762293748106240,'启动|停止|扩缩容',NULL,NULL,NULL,'B029','application:operate','启动|停止|扩缩容',NULL,36,'32/35/36/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762310896616448,'创建|编辑|删除',NULL,NULL,NULL,'B030','xxl_job:write','创建|编辑|删除',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762313851175936,'启动|停止|执行',NULL,NULL,NULL,'B031','xxl_job:operate','启动|停止|手工执行',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762314610082816,'创建|编辑|删除',NULL,NULL,NULL,'B032','xxl_group:write','创建|编辑|删除',NULL,1351771934786560,'32/1351636365411328/1351771934786560/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762319875408896,'创建|编辑|删除|调试|发布',NULL,NULL,NULL,'B033','api_service:write','创建|编辑|删除|调试|发布',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762322478826496,'上线|下线',NULL,NULL,NULL,'B034','api_service:operate','上线|下线',NULL,1530725044618240,'32/1530708952679424/1530725044618240/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762327436493824,'创建|编辑|删除|授权',NULL,NULL,NULL,'B035','api_client:write','创建|编辑|删除|授权',NULL,1530727473284096,'32/1530708952679424/1530727473284096/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762346250994688,'启动|停止',NULL,NULL,NULL,'B036','collection_job:operate','启动|停止',NULL,39,'37/38/39/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762351509832704,'启动|停止',NULL,NULL,NULL,'B037','pipeline_streaming:operate','启动|停止',NULL,42,'37/41/42/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762354024317952,'启动|停止',NULL,NULL,NULL,'B038','storage_job:operate','启动|停止',NULL,43,'37/41/43/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762359453385728,'启动|停止',NULL,NULL,NULL,'B039','pipeline_batch:operate','启动|停止',NULL,1383697661953024,'37/1383694132806656/1383697661953024/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762555182646272,'创建|编辑|删除',NULL,NULL,NULL,'B040','realtime_check_task:write','创建|编辑|删除',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1762560754418688,'创建|编辑|删除',NULL,NULL,NULL,'B041','realtime_check_rule:write','创建|编辑|删除',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1765748629603328,'归档文件采集',NULL,NULL,'/data-dev/dev/data-ingestion/online/archive','M1214','P003001001','数据开发','rtdatacollection',1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1765751129048064,'克隆归档文件采集',NULL,NULL,'/data-dev/dev/data-ingestion/online/clone/archive/:cloneId','M1215','P003001001','数据开发',NULL,1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1765753300812800,'编辑归档文件采集',NULL,NULL,'/data-dev/dev/data-ingestion/online/archive/:id','M1216','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779213196297216,'Kafka详情',NULL,NULL,'/platform-management/address/data-source/kafka/detail/:detailId','M1217','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779216279077888,'Mysql详情',NULL,NULL,'/platform-management/address/data-source/mysql/detail/:detailId','M1218','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779218584962048,'ElasticSearch详情',NULL,NULL,'/platform-management/address/data-source/es/detail/:detailId','M1219','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779220848215040,'Clickhouse详情',NULL,NULL,'/platform-management/address/data-source/ck/detail/:detailId','M1220','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779223103800320,'Hive详情',NULL,NULL,'/platform-management/address/data-source/hive/detail/:detailId','M1221','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779224461509632,'Redis详情',NULL,NULL,'/platform-management/address/data-source/redis/detail/:detailId','M1222','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779225790710784,'VictoriaMetrics详情',NULL,NULL,'/platform-management/address/data-source/victoria-metrics/detail/:detailId','M1223','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779227795293184,'Nebula详情',NULL,NULL,'/platform-management/address/data-source/nebula/detail/:detailId','M1224','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1779298788279296,'数据权限管理',NULL,NULL,'/rights-management/project/data-rights/:id','M1225','P005008002','项目管理','projectmanagement',1734449730782208,'46/1734435321480192/1734449730782208/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1782550288565248,'成员管理',NULL,NULL,'/rights-management/project/member-management/:id','M1226','P005008002','项目管理','projectmanagement',1734449730782208,'46/1734435321480192/1734449730782208/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1782940712305664,'共享设置',NULL,NULL,NULL,'B042','project_resource:share','共享设置',NULL,1734449730782208,'46/1734435321480192/1734449730782208/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1782941647143936,'权限设置',NULL,NULL,NULL,'B043','project_resource:permission','权限设置',NULL,1734449730782208,'46/1734435321480192/1734449730782208/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1784837788894208,'编辑|删除',NULL,NULL,NULL,'R001','business_category:write','编辑|删除',NULL,13,'11/12/13/',0,1,'tb_business_category','resource',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1784840012170240,'编辑|删除',NULL,NULL,NULL,'R002','warehouse_layer:write','编辑|删除',NULL,14,'11/12/14/',0,1,'tb_warehouse_layer','resource',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785353412314112,'编辑|删除',NULL,NULL,NULL,'R003','data_domain:write','编辑|删除',NULL,62,'11/12/15/62/',0,1,'tb_data_domain','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785374300832768,'编辑|删除',NULL,NULL,NULL,'R004','business_process:write','编辑|删除',NULL,63,'11/12/15/63/',0,1,'tb_business_process','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785384723678208,'编辑|删除',NULL,NULL,NULL,'R005','data_mart:write','编辑|删除',NULL,64,'11/12/16/64/',0,1,'tb_data_mart','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785390515028992,'编辑|删除',NULL,NULL,NULL,'R006','data_subject:write','编辑|删除',NULL,65,'11/12/16/65/',0,1,'tb_data_subject','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785405895345152,'编辑|删除',NULL,NULL,NULL,'R007','column_dict:write','编辑|删除',NULL,18,'11/17/18/',0,1,'tb_column_dict','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785632986399744,'编辑|删除',NULL,NULL,NULL,'R008','dict_enum:write','编辑|删除',NULL,19,'11/17/19/',0,1,'tb_dict_enum','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785639466370048,'编辑|删除',NULL,NULL,NULL,'R013','data_model:write','编辑|删除',NULL,23,'11/22/23/',0,1,'tb_table','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785640943617024,'发布',NULL,NULL,NULL,'R014','data_model:deploy','发布',NULL,23,'11/22/23/',0,1,'tb_table','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1785778321753088,'编辑|删除',NULL,NULL,NULL,'R015','object_category:write','编辑|删除',NULL,1640434404819968,'11/22/1647057048667136/1640434404819968/',0,1,'tb_object_category','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1788139537925120,'编辑|删除',NULL,NULL,NULL,'R016','derivative_indicator:write','编辑|删除',NULL,25,'11/24/25/',0,1,'tb_derivative_indicator','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1788141984449536,'编辑|删除',NULL,NULL,NULL,'R017','atomic_indicator:write','编辑|删除',NULL,26,'11/24/26/',0,1,'tb_atomic_indicator','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1790934601729024,'编辑|删除|调试|发布',NULL,NULL,NULL,'R018','api_service:write','编辑|删除|调试|发布',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',0,1,'tb_api','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1790936196809728,'上线|下线',NULL,NULL,NULL,'R019','api_service:operate','上线|下线',NULL,1530725044618240,'32/1530708952679424/1530725044618240/',0,1,'tb_api','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1790939752924160,'编辑|删除|授权',NULL,NULL,NULL,'R020','api_client:write','编辑|删除|授权',NULL,1530727473284096,'32/1530708952679424/1530727473284096/',0,1,'tb_client','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1790946837955584,'编辑|删除',NULL,NULL,NULL,'R021','realtime_check_task:write','编辑|删除',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',0,1,'tb_realtime_check_task','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1790948035560448,'编辑|删除',NULL,NULL,NULL,'R022','realtime_check_rule:write','编辑|删除',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',0,1,'tb_realtime_check_rule_template','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1791076828021760,'编辑|删除|发布',NULL,NULL,NULL,'R023','application:write','编辑|删除|发布',NULL,36,'32/35/36/',0,1,'tb_application','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1791077884396544,'启动|停止|扩缩容',NULL,NULL,NULL,'R024','application:operate','启动|停止|扩缩容',NULL,36,'32/35/36/',0,1,'tb_application','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1791098385040384,'业务流程编辑|删除',NULL,NULL,NULL,'R025','data_develop:write','编辑|删除',NULL,34,'32/33/34/',0,1,'tb_business_flow','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1791245340345344,'编辑|删除',NULL,NULL,NULL,'R026','xxl_job:write','编辑|删除',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,1,'xxl_job_info','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1791247093433344,'启动|停止|执行',NULL,NULL,NULL,'R027','xxl_job:operate','启动|停止|执行',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,1,'xxl_job_info','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1791249337615360,'编辑|删除',NULL,NULL,NULL,'R028','xxl_group:write','编辑|删除',NULL,1351771934786560,'32/1351636365411328/1351771934786560/',0,1,'xxl_job_group','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802266411140096,'数据集成编辑|删除|发布',NULL,NULL,NULL,'R029','data_develop:write','编辑|删除|发布',NULL,34,'32/33/34/',0,1,'tb_ingestion','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802269544842240,'数据集成启动|停止',NULL,NULL,NULL,'R030','collection_job:operate','启动|停止',NULL,34,'32/33/34/',0,1,'tb_ingestion','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802271072781312,'数据处理编辑|删除|发布',NULL,NULL,NULL,'R031','data_develop:write','编辑|删除|发布',NULL,34,'32/33/34/',0,1,'tb_process','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802272769344512,'数据处理启动|停止',NULL,NULL,NULL,'R032','pipeline_streaming:operate','启动|停止',NULL,34,'32/33/34/',0,1,'tb_process','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802304724141056,'CK存储编辑|删除|发布',NULL,NULL,NULL,'R033','data_develop:write','编辑|删除|发布',NULL,34,'32/33/34/',0,1,'tb_storage_ck','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802306395243520,'CK存储启动|停止',NULL,NULL,NULL,'R034','storage_job:operate','启动|停止',NULL,34,'32/33/34/',0,1,'tb_storage_ck','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802309216896000,'Es存储编辑|删除|发布',NULL,NULL,NULL,'R035','data_develop:write','编辑|删除|发布',NULL,34,'32/33/34/',0,1,'tb_storage_es','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802309274076160,'共享设置',NULL,NULL,'/rights-management/project/data-share/:id','M1227','P005008002','共享设置','projectmanagement',1734449730782208,'46/1734435321480192/1734449730782208/',0,0,NULL,'menu',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802310875448320,'Es存储启动|停止',NULL,NULL,NULL,'R036','pipeline_streaming:operate','启动|停止',NULL,34,'32/33/34/',0,1,'tb_storage_es','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802312889828352,'Hive存储编辑|删除|发布',NULL,NULL,NULL,'R037','data_develop:write','编辑|删除|发布',NULL,34,'32/33/34/',0,1,'tb_storage_hive','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1802342172165120,'Hive存储启动|停止',NULL,NULL,NULL,'R038','pipeline_streaming:operate','启动|停止',NULL,34,'32/33/34/',0,1,'tb_storage_hive','resource',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1805635202089984,'创建派生指标','post',NULL,'/api/v2/data/modeling/derivative-indicator','A051','derivative_indicator:write','创建|编辑|删除关系类型',NULL,25,'11/24/25/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1807550656906240,'修改派生指标','put',NULL,'/api/v2/data/modeling/derivative-indicator/{id}','A052','derivative_indicator:write','创建|编辑|删除派生指标',NULL,25,'11/24/25/',1,0,'tb_derivative_indicator','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1807562891789312,'删除派生指标','delete',NULL,'/api/v2/data/modeling/derivative-indicator/{id}','A053','derivative_indicator:write','创建|编辑|删除派生指标',NULL,25,'11/24/25/',1,0,'tb_derivative_indicator','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1807826874139648,'创建原子指标','post',NULL,'/api/v2/data/modeling/atomic-indicator','A054','atomic_indicator:write','创建|编辑|删除原子指标',NULL,26,'11/24/26/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1807827523470336,'删除原子指标','delete',NULL,'/api/v2/data/modeling/atomic-indicator/{id}','A055','atomic_indicator:write','创建|编辑|删除原子指标',NULL,26,'11/24/26/',1,0,'tb_atomic_indicator','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1807827861701632,'修改原子指标','put',NULL,'/api/v2/data/modeling/atomic-indicator/{id}','A056','atomic_indicator:write','创建|编辑|删除原子指标',NULL,26,'11/24/26/',1,0,'tb_atomic_indicator','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808033560986624,'创建文件同步','post',NULL,'/api/v2/ingestion/info/file','A057','data_develop:write','创建|编辑|删除文件同步',NULL,1309154588591104,'32/33/34/1309151048500224/1309154588591104/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808044824364032,'修改文件同步','put',NULL,'/api/v2/ingestion/info/file','A058','data_develop:write','创建|编辑|删除文件同步',NULL,1309154588591104,'32/33/34/1309151048500224/1309154588591104/',1,0,'tb_ingestion','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808065663042560,'删除文件同步','delete',NULL,'/api/v2/ingestion/manager/{id}','A059','data_develop:write','创建|编辑|删除文件同步',NULL,1309154588591104,'32/33/34/1309151048500224/1309154588591104/',1,0,'tb_ingestion','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808118623962112,'创建归档文件','post',NULL,'/api/v2/ingestion/info/archive','A060','data_develop:write','创建|编辑|删除归档文件',NULL,1765748629603328,'32/33/34/1309151048500224/1765748629603328/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808123121501184,'修改归档文件','put',NULL,'/api/v2/ingestion/info/archive','A061','data_develop:write','创建|编辑|删除归档文件',NULL,1765748629603328,'32/33/34/1309151048500224/1765748629603328/',1,0,'tb_ingestion','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808136896054272,'创建TCP/UDP同步','post',NULL,'/api/v2/ingestion/info/tcp-udp','A062','data_develop:write','创建|编辑TCP/UDP同步',NULL,1309156844536832,'32/33/34/1309151048500224/1309156844536832/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808138142680064,'编辑TCP/UDP同步','put',NULL,'/api/v2/ingestion/info/tcp-udp','A063','data_develop:write','创建|编辑TCP/UDP同步',NULL,1309156844536832,'32/33/34/1309151048500224/1309156844536832/',1,0,'tb_ingestion','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808145743086592,'创建kafka接入','post',NULL,'/api/v2/ingestion/info/kafka','A064','data_develop:write','创建|编辑kafka接入',NULL,1309160059470848,'32/33/34/1309151048500224/1309160059470848/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808147097060352,'编辑kafka接入','put',NULL,'/api/v2/ingestion/info/kafka','A065','data_develop:write','创建|编辑kafka接入',NULL,1309160059470848,'32/33/34/1309151048500224/1309160059470848/',1,0,'tb_ingestion','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808193833239552,'创建管线作业','post',NULL,'/api/v2/ingestion/process','A066','data_develop:write','创建|编辑|删除管线作业',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808196296147968,'编辑管线作业','put',NULL,'/api/v2/ingestion/process/{id}','A067','data_develop:write','编辑|删除|发布管线作业',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,'tb_process','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808230637765632,'创建并发布管线作业','post',NULL,'/api/v2/ingestion/process/publish','A068','data_develop:write','创建|编辑|删除管线作业',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808233514632192,'更新并发布管线作业','put',NULL,'/api/v2/ingestion/process/{id}/publish','A069','data_develop:write','创建|编辑|删除管线作业',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,'tb_process','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808252408464384,'删除管线作业','delete',NULL,'/api/v2/ingestion/process/{id}','A070','data_develop:write','创建|编辑|删除|发布管线作业',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,'tb_process','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808264214610944,'创建ck存储作业','post',NULL,'/api/v2/ingestion/storage-ck','A071','data_develop:write','创建|编辑|删除|发布ck存储作业',NULL,1309195853169664,'32/33/34/1309188023813120/1309195853169664/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808267596792832,'编辑ck存储作业','put',NULL,'/api/v2/ingestion/storage-ck/{id}','A072','data_develop:write','创建|编辑|删除|发布ck存储作业',NULL,1309195853169664,'32/33/34/1309188023813120/1309195853169664/',1,0,'tb_storage_ck','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808269639648256,'更新并发布ck存储作业','put',NULL,'/api/v2/ingestion/storage-ck/{id}/update-start','A073','data_develop:write','创建|编辑|删除|发布ck存储作业',NULL,1309195853169664,'32/33/34/1309188023813120/1309195853169664/',1,0,'tb_storage_ck','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808271635055616,'创建并发布ck存储作业','post',NULL,'ingestion/storage-ck/create-start','A074','data_develop:write','创建|编辑|删除|发布ck存储作业',NULL,1309195853169664,'32/33/34/1309188023813120/1309195853169664/',1,0,'','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808273279747072,'删除ck存储作业','delete',NULL,'/api/v2/ingestion/storage-ck/{id}','A075','data_develop:write','创建|编辑|删除|发布ck存储作业',NULL,1309195853169664,'32/33/34/1309188023813120/1309195853169664/',1,0,'tb_storage_ck','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808354928067584,'启动存储任务','get',NULL,'/api/v2/ingestion/storage-ck/{id}/start','A076','storage_job:operate','启动|停止ck存储作业',NULL,43,'37/41/43/',1,0,'tb_storage_ck','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808354928067585,'停止存储任务','get',NULL,'/api/v2/ingestion/storage-ck/{id}/stop','A077','storage_job:operate','启动|停止ck存储作业',NULL,43,'37/41/43/',1,0,'tb_storage_ck','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808383943738368,'创建es存储作业','post',NULL,'/api/v2/ingestion/storage-es','A078','data_develop:write','创建|编辑|删除|发布es存储作业',NULL,1309197317309440,'32/33/34/1309188023813120/1309197317309440/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808386519139328,'修改es存储作业','put',NULL,'/api/v2/ingestion/storage-es/{id}','A079','data_develop:write','创建|编辑|删除|发布es存储作业',NULL,1309197317309440,'32/33/34/1309188023813120/1309197317309440/',1,0,'tb_storage_es','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808387553461248,'删除es存储作业','delete',NULL,'/api/v2/ingestion/storage-es/{id}','A081','data_develop:write','创建|编辑|删除|发布es存储作业',NULL,1309197317309440,'32/33/34/1309188023813120/1309197317309440/',1,0,'tb_storage_es','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808388839310336,'创建并发布es存储作业','post',NULL,'/api/v2/ingestion/storage-es/publish','A082','data_develop:write','创建|编辑|删除|发布es存储作业',NULL,1309197317309440,'32/33/34/1309188023813120/1309197317309440/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1808391786693632,'更新并发布es存储作业','put',NULL,'/api/v2/ingestion/storage-es/{id}/publish','A083','data_develop:write','创建|编辑|删除|发布es存储作业',NULL,1309197317309440,'32/33/34/1309188023813120/1309197317309440/',1,0,'tb_storage_es','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809928148747264,'创建hive存储作业','post',NULL,'/api/v2/ingestion/storage-hive','A084','data_develop:write','创建|编辑|删除|发布hive存储作业',NULL,1309198905672704,'32/33/34/1309188023813120/1309198905672704/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809929737470976,'编辑hive存储作业','put',NULL,'/api/v2/ingestion/storage-hive/{id}','A085','data_develop:write','创建|编辑|删除|发布hive存储作业',NULL,1309198905672704,'32/33/34/1309188023813120/1309198905672704/',1,0,'tb_storage_hive','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809931227235328,'删除hive存储作业','delete',NULL,'/api/v2/ingestion/storage-hive/{id}','A086','data_develop:write','创建|编辑|删除|发布hive存储作业',NULL,1309198905672704,'32/33/34/1309188023813120/1309198905672704/',1,0,'tb_storage_hive','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809933180699648,'创建并发布hive存储作业','post',NULL,'/api/v2/ingestion/storage-hive/publish','A087','data_develop:write','创建|编辑|删除|发布hive存储作业',NULL,1309198905672704,'32/33/34/1309188023813120/1309198905672704/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809934843905024,'编辑并发布hive存储作业','put',NULL,'/api/v2/ingestion/storage-hive/{id}/publish','A088','data_develop:write','创建|编辑|删除|发布hive存储作业',NULL,1309198905672704,'32/33/34/1309188023813120/1309198905672704/',1,0,'tb_storage_hive','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809948252439552,'创建弹性作业','post',NULL,'/api/v2/ingestion/application','A089','application:write','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809950822073344,'创建并|启动弹性作业','post',NULL,'/api/v2/ingestion/application/create-start','A090','data_develop:write','创建|编辑|删除弹性作业',NULL,36,'32/35/36/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809953119339520,'批量启动弹性作业','post',NULL,'/api/v2/ingestion/application/batch-start','A091','application:operate','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809955571663872,'批量停止弹性作业','post',NULL,'/api/v2/ingestion/application/batch-stop','A092','application:operate','启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809961545696256,'编辑并启动弹性作业','put',NULL,'/api/v2/ingestion/application/{id}/update-start','A093','application:operate','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809964003558400,'删除弹性作业','delete',NULL,'/api/v2/ingestion/application/{id}','A094','application:write','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809966749549568,'启动弹性作业','get',NULL,'/api/v2/ingestion/application/{id}/start','A095','application:operate','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809968050603008,'停止弹性作业','get',NULL,'/api/v2/ingestion/application/{id}/stop','A096','application:operate','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809973115388928,'扩缩容弹性作业','post',NULL,'/api/v2/ingestion/application/{id}/scale','A097','application:operate','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809980946088960,'创建执行器','post',NULL,'/api/v2/api/v2/xxl-job/group','A098','xxl_group:write','创建|编辑|删除执行器',NULL,1351771934786560,'32/1351636365411328/1351771934786560/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809984463242240,'编辑执行器','put',NULL,'/api/v2/xxl-job/group/{id}','A099','xxl_group:write','创建|编辑|删除执行器',NULL,1351771934786560,'32/1351636365411328/1351771934786560/',1,0,'xxl_job_group','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1809986244936704,'删除执行器','delete',NULL,'/api/v2/xxl-job/group/{id}','A100','xxl_group:write','创建|编辑|删除执行器',NULL,1351771934786560,'32/1351636365411328/1351771934786560/',1,0,'xxl_job_group','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810002350048256,'创建定时作业','post',NULL,'/api/v2/xxl-job/job','A102','xxl_job:write','编辑|删除|启动|停止|执行定时作业',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810004539868160,'编辑定时作业','put',NULL,'/api/v2/xxl-job/job/{id}','A103','xxl_job:write','编辑|删除|启动|停止|执行定时作业',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',1,0,'xxl_job_info','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810006806397952,'删除定时作业','delete',NULL,'/api/v2/xxl-job/job/{id}','A104','xxl_job:write','编辑|删除|启动|停止|执行定时作业',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',1,0,'xxl_job_info','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810008617780224,'停止定时作业','put',NULL,'/api/v2/xxl-job/job/{id}/pause','A105','xxl_job:operate','启动|停止|执行定时作业',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',1,0,'xxl_job_info','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810011618149376,'启动定时作业','put',NULL,'/api/v2/xxl-job/job/{id}/start','A106','xxl_job:operate','编辑|删除|启动|停止|执行定时作业',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',1,0,'xxl_job_info','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810018221917184,'创建授权管理','post',NULL,'/api/v2/data-service/client','A107','api_client:write','创建|编辑|删除授权管理',NULL,1530727473284096,'32/1530708952679424/1530727473284096/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810019991225344,'编辑授权管理','put',NULL,'/api/v2/data-service/client/{id}','A108','api_client:write','创建|编辑|删除授权管理',NULL,1530727473284096,'32/1530708952679424/1530727473284096/',1,0,'tb_client','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810021369709568,'删除授权管理','delete',NULL,'/api/v2/data-service/client/{id}','A109','api_client:write','创建|编辑|删除授权管理',NULL,1530727473284096,'32/1530708952679424/1530727473284096/',1,0,'tb_client','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810026139616256,'新建数据服务','post',NULL,'/api/v2/data-service/api','A110','api_service:write','编辑|删除|调试|发布数据服务',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810027935728640,'删除数据服务','delete',NULL,'/api/v2/data-service/api/{id}','A111','api_service:write','编辑|删除|调试|发布数据服务',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',1,0,'tb_api','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810029666042880,'编辑数据服务','put',NULL,'/api/v2/data-service/api/{id}','A112','api_service:write','编辑|删除|调试|发布数据服务',NULL,1530715876164608,'32/1530708952679424/1530715876164608/',1,0,'tb_api','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810049452672000,'新建规则模板','post',NULL,'/api/v2/realtime/check-rule-template','A113','realtime_check_rule:write','编辑|删除|规则模板',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810050749170688,'编辑规则模板','put',NULL,'/api/v2/realtime/check-rule-template/{id}','A114','realtime_check_rule:write','编辑|删除|规则模板',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',1,0,'tb_realtime_check_rule_template','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810052171203584,'删除规则模板','delete',NULL,'/api/v2/realtime/check-rule-template/{id}','A115','realtime_check_rule:write','编辑|删除|规则模板',NULL,1335776653542400,'1335764428817408/1335768147330048/1335776653542400/',1,0,'tb_realtime_check_rule_template','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810056181941248,'新建检测任务','post',NULL,'/api/v2/realtime/check-task','A116','realtime_check_task:write','编辑|删除检测任务',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',1,0,NULL,'api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810057618916352,'编辑检测任务','put',NULL,'/api/v2/realtime/check-task/{id}','A117','realtime_check_task:write','编辑|删除检测任务',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',1,0,'tb_realtime_check_task','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810058949067776,'删除检测任务','delete',NULL,'/api/v2/realtime/check-task/{id}','A118','realtime_check_task:write','删除检测任务',NULL,1335775211324416,'1335764428817408/1335768147330048/1335775211324416/',1,0,'tb_realtime_check_task','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810271611126784,'编辑弹性作业','put',NULL,'/api/v2/ingestion/application/{id}','A119','application:write','创建|编辑|删除|启动|停止|扩缩容弹性作业',NULL,36,'32/35/36/',1,0,'tb_application','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1810685928080384,'执行定时作业','put',NULL,'/api/v2/xxl-job/job/{id}/trigger','A120','xxl_job:operate','编辑|删除|启动|停止|执行定时作业',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',1,0,'xxl_job_info','api',0,NULL,0,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1831046365283328,'创建|编辑|删除',NULL,NULL,NULL,'B044','user_group:write','创建|编辑|删除',NULL,1748600261936128,'46/1734435321480192/1748607326094336/1748600261936128/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1831049218982912,'创建|编辑|删除',NULL,NULL,NULL,'B045','role:write','创建|编辑|删除',NULL,1748602778158080,'46/1734435321480192/1748607326094336/1748602778158080/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1831051415127040,'创建|编辑|删除',NULL,NULL,NULL,'B046','project:write','创建|编辑|删除',NULL,1734449730782208,'46/1734435321480192/1734449730782208/',0,0,NULL,'button',0,NULL,1,0,'2023-11-01 00:00:00','2023-11-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1858350457717760,'工作流定义',NULL,'iconfont icon-vertical_split-line text-sm','/ops/offline/workflow','M1231','P004003002','工作流定义',NULL,1383694132806656,'37/1383694132806656/',0,0,NULL,'menu',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1858352194552832,'工作流实例',NULL,'iconfont icon-detail-line text-sm','/ops/offline/processInstance','M1232','P004003003','工作流实例',NULL,1383694132806656,'37/1383694132806656/',0,0,NULL,'menu',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1858355439174656,'试跑工作流',NULL,NULL,'/data-dev/dev/data-process/bpm-process/test/:instanceId','M1228','P003001001','数据开发',NULL,1383529695020032,'32/33/34/1382903756915712/1383529695020032/',0,0,NULL,'menu',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1858365376857088,'执行节点',NULL,'iconfont icon-transitpoint-line text-sm','/worker-group/management','M1230','P003001002','执行节点',NULL,33,'32/33/',0,0,NULL,'menu',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1859263342314496,'资源程序包',NULL,'iconfont icon-zip-line text-sm','/data-dev/dev/data-process/resource/:id','M1229','P003001001','数据开发',NULL,1382903756915712,'32/33/34/1382903756915712/',0,0,NULL,'menu',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1859402419438592,'创建|编辑|删除',NULL,NULL,NULL,'B047','ds_workgroup:write','创建|编辑|删除',NULL,1858365376857088,'32/33/1858365376857088/',0,0,NULL,'button',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1859414697149440,'上线|下线|补数',NULL,NULL,NULL,'B048','workflow:operate','上线|下线|补数',NULL,1858350457717760,'37/1383694132806656/1858350457717760/',0,0,NULL,'button',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1859416278172672,'停止|重跑',NULL,NULL,NULL,'B049','workflow_instance:operate','停止|重跑',NULL,1858352194552832,'37/1383694132806656/1858352194552832/',0,0,NULL,'button',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1864502910387200,'工作流编辑|删除',NULL,NULL,NULL,'R039','data_develop:write','编辑|删除',NULL,34,'32/33/34/',0,1,'t_ds_process_definition','resource',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1864528898360320,'工作流上下线补数',NULL,NULL,NULL,'R040','workflow:operate','发布|上线|下线|补数',NULL,34,'32/33/34/',0,1,'t_ds_process_definition','resource',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1864536379982848,'工作流实例停止|重跑',NULL,NULL,NULL,'R041','workflow_instance:operate','停止|重跑',NULL,34,'32/33/34/',0,1,'t_ds_process_definition','resource',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1864540255749120,'离线资源包编辑删除',NULL,NULL,NULL,'R042','data_develop:write','编辑|删除',NULL,34,'32/33/34/',0,1,'t_ds_resources','resource',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1864551212778496,'编辑|删除',NULL,NULL,NULL,'R043','ds_workgroup:write','编辑|删除',NULL,1858365376857088,'32/33/1858365376857088/',0,1,'t_ds_worker_group','resource',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1870406422529024,'创建自定义flink作业',NULL,NULL,'/data-dev/dev/data-process/online/flink-custom/create/:businessFlowId','M1236','P003001001','数据开发',NULL,1309188023813120,'32/33/34/1309188023813120/',0,0,NULL,'menu',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1870409496593408,'编辑自定义flink作业',NULL,NULL,'/data-dev/dev/data-process/online/flink-custom/edit/:id','M1234','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1870411574903808,'克隆自定义flink作业',NULL,NULL,'/data-dev/dev/data-process/online/flink-custom/clone/:cloneId','M235','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1890471429932032,'字段模板',NULL,'iconfont  icon-dislog-line','/data-modeling/data-standard/field-template','M1237','P001002005','字段模板','full',17,'11/17/',0,0,NULL,'menu',1,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1890512447636480,'生产',NULL,NULL,NULL,'R050','data_model:produce','生产',NULL,23,'11/22/23/',0,1,'tb_table','resource',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1901410344109056,'编辑|删除',NULL,NULL,NULL,'R045','column_template:write','编辑|删除',NULL,1890471429932032,'11/17/1890471429932032/',0,1,'tb_column_template','resource',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1901417601041408,'更新','put',NULL,'/api/v2/data/modeling/data-standard/column-template/{id}','A121','column_template:write','创建|编辑|删除',NULL,1890471429932032,'11/17/1890471429932032/',1,0,'tb_column_template','api',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1901419967284224,'删除','delete',NULL,'/api/v2/data/modeling/data-standard/column-template/{id}','A122','column_template:write','创建|编辑|删除',NULL,1890471429932032,'11/17/1890471429932032/',1,0,'tb_column_template','api',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1901422015349760,'批量删除','delete',NULL,'/api/v2/data/modeling/data-standard/column-template/bulk','A123','column_template:write','创建|编辑|删除',NULL,1890471429932032,'11/17/1890471429932032/',1,0,'tb_column_template','api',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1904437477934080,'创建|编辑|删除',NULL,NULL,NULL,'B050','column_template:write','创建|编辑|删除',NULL,1890471429932032,'11/17/1890471429932032/',0,0,NULL,'button',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1910316206425088,'离线数据处理启动|停止',NULL,NULL,NULL,'R051','pipeline_batch:operate','启动|停止',NULL,34,'32/33/34/',0,1,'tb_process_batch','resource',0,NULL,1,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1910354231428096,'离线数据处理编辑|删除|发布',NULL,NULL,NULL,'R052','data_develop:write','编辑|删除|发布',NULL,32,'32/',0,1,'tb_process_batch','resource',0,NULL,0,0,'2023-12-01 00:00:00','2023-12-01 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1938094085866496,'历史诊断',NULL,NULL,'/ops/realtime/pipeline/historical-diagnosis/:id','M1241','P004002001','历史诊断',NULL,42,'37/41/42/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1941037294191616,'运行实例',NULL,NULL,'/ops/realtime/pipeline/historical-running-instances/:id','M1242','P004002001','运行实例',NULL,42,'37/41/42/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1944149910651904,'创建算子分组','post',NULL,'/api/v2/ingestion/job-group','A124','job_group:write','创建|编辑|删除|算子分组',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,NULL,'api',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1944149910651905,'编辑算子分组','put',NULL,'/api/v2/ingestion/job-group/{id}','A125','job_group:write','创建|编辑|删除|算子分组',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,'tb_job_group','api',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1944149910651906,'删除算子分组','delete',NULL,'/api/v2/ingestion/job-group/{id}','A126','job_group:write','创建|编辑|删除|算子分组',NULL,1309189665588224,'32/33/34/1309188023813120/1309189665588224/',1,0,'tb_job_group','api',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1947176626783232,'数据库同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/jdbc','M1243','P003001001','数据开发','rtdatacollection',1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1947180622152704,'克隆数据库同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/clone/jdbc/:cloneId','M1244','P003001001','数据开发',NULL,1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1947183375057920,'编辑数据库同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/jdbc/:id','M1245','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1958421567144960,'创建数据源',NULL,NULL,'/platform-management/address/data-source/create','M1238','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1958423528047616,'编辑数据源',NULL,NULL,'/platform-management/address/data-source/edit/:editId','M1239','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (1958424983897088,'数据源详情',NULL,NULL,'/platform-management/address/data-source/detail/:id','M1240','P005002001','数据源管理',NULL,50,'46/49/50/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2017541925962752,'维度标签',NULL,'iconfont icon-dimension-label','/metric/dimension/list','M1246','P001003003001','指标模型','full',1647064256578560,'11/22/1647064256578560/',0,0,NULL,'menu',0,NULL,1,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2017543910261760,'列表',NULL,NULL,'/metric/dimension/list','M1247','P001003003001','指标模型','full',2017541925962752,'11/22/1647064256578560/2017541925962752/',0,0,NULL,'menu',0,NULL,1,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2017755037303808,'详情',NULL,NULL,'/metric/dimension/detail/:id','M1248','P001003003001','指标模型',NULL,2017541925962752,'11/22/1647064256578560/2017541925962752/',0,0,NULL,'menu',0,NULL,1,0,'2024-01-10 00:00:00','2024-01-10 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2025523064964096,'容量日报',NULL,NULL,'/ops/realtime/pipeline/capacity-daily','M1250','P004002001','实时任务运维',NULL,42,'37/41/42/',0,0,NULL,'menu',1,NULL,1,0,'2024-01-20 00:00:00','2024-01-20 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2025854518264832,'任务列表',NULL,NULL,'/ops/realtime/pipeline/devOps','M1249','P004002001','实时任务运维',NULL,42,'37/41/42/',0,0,NULL,'menu',0,NULL,1,0,'2024-01-20 00:00:00','2024-01-20 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2025941823751168,'容量日报详情',NULL,NULL,'/ops/realtime/pipeline/capacity-daily-detail/:id','M1251','P004002001','实时任务运维',NULL,2025523064964096,'37/41/42/2025523064964096/',0,0,NULL,'menu',0,NULL,0,0,'2024-01-20 00:00:00','2024-01-20 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2079820645860352,'文件列表',NULL,NULL,'/platform-management/cluster/management/files/:id','M1252','P005003001','集群管理',NULL,54,'46/53/54/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127368951759872,'AI实验室',NULL,'iconfont  icon-robot-angry-outline',NULL,'M1253','P003005000','AI实验室','jupyter',32,'32/',0,0,NULL,'menu',4,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127375238923264,'AI实验室',NULL,'iconfont  icon-robot-angry-outline','/algorithm-lab/management','M1254','P003005001','AI实验室','jupyter',2127368951759872,'32/2127368951759872/',0,0,NULL,'menu',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127501591072768,'创建|编辑|删除|进入',NULL,NULL,NULL,'B051','algorithm_lab:write','创建|编辑|删除|进入',NULL,2127375238923264,'32/2127368951759872/2127375238923264/',0,0,NULL,'button',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127502429979648,'AI算法实验室创建|编辑|删除',NULL,NULL,NULL,'R053','algorithm_lab:write','创建|编辑|删除',NULL,2127375238923264,'32/2127368951759872/2127375238923264/',0,1,'tb_algorithm_lab','resource',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127521482966016,'AI算法实验室进入',NULL,NULL,NULL,'R054','algorithm_lab:access','进入',NULL,2127375238923264,'32/2127368951759872/2127375238923264/',0,1,'tb_algorithm_lab','resource',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2158620955085824,'框架详情',NULL,NULL,'/platform-management/opts/detail/:id','M1255','P005004001','框架管理',NULL,57,'46/56/57/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2158670880179200,'查看定时作业',NULL,NULL,'/job-timing/job-management/detail/:id','M1256','P003003001','作业管理',NULL,1351768449942528,'32/1351636365411328/1351768449942528/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2161316163685376,'查看弹性作业',NULL,NULL,'/flexible-application/detail/:id','M1257','P003002001','弹性作业',NULL,36,'32/35/36/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2167958639805440,'Logstash同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/logstash','M1258','P003001001','数据开发','rtdatacollection',1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2167960322870272,'克隆Logstash同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/clone/logstash/:cloneId','M1259','P003001001','数据开发',NULL,1309151048500224,'32/33/34/1309151048500224/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2167960322871272,'编辑Logstash同步',NULL,NULL,'/data-dev/dev/data-ingestion/online/logstash/:id','M1260','P003001001','数据开发',NULL,34,'32/33/34/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2167962332826124,'创建Logstash同步','post',NULL,'/api/v2/ingestion/info/logstash','A127','data_develop:write','编辑|删除|发布',NULL,2167958639805440,'32/33/34/1309151048500224/2167958639805440/',1,0,NULL,'api',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2167962332826624,'编辑Logstash同步','put',NULL,'/api/v2/ingestion/info/logstash','A128','data_develop:write','编辑|删除|发布',NULL,2167958639805440,'32/33/34/1309151048500224/2167958639805440/',1,0,'tb_ingestion','api',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2167962332829867,'logstash插件文档',NULL,NULL,'/data-dev/logstash-document','M1261','P003001001','数据开发',NULL,2167958639805440,'32/33/34/1309151048500224/2167958639805440/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2195368430699520,'运维排障',NULL,'iconfont icon-hammer-screwdriver',NULL,'M1271','P004004000','运维排障',NULL,37,'37/',0,0,NULL,'menu',1,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2195376427534336,'告警中心',NULL,'iconfont icon-alarm-line1',NULL,'M1272','P004004001','告警中心',NULL,2195368430699520,'37/2195368430699520/',0,0,NULL,'menu',2,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2195467065821184,'告警列表',NULL,NULL,'/trouble-shooting/alert-center/list','M1273','P004004001001','告警列表',NULL,2195376427534336,'37/2195368430699520/2195376427534336/',0,0,NULL,'menu',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2195471335393280,'规则管理',NULL,NULL,'/trouble-shooting/alert-center/rule','M1274','P004004001002','规则管理',NULL,2195376427534336,'37/2195368430699520/2195376427534336/',0,0,NULL,'menu',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2196005348082688,'日志',NULL,'iconfont icon-description-line','/trouble-shooting/log','M1275','P004004002','日志',NULL,2195368430699520,'37/2195368430699520/',0,0,NULL,'menu',3,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2198640813901824,'巡检仪表板',NULL,'iconfont icon-monitor-dashboard','/trouble-shooting/panel','M1276','P004004003','巡检仪表板',NULL,2195368430699520,'37/2195368430699520/',0,0,NULL,'menu',1,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2201404335818752,'健康检查',NULL,'iconfont icon-operation-line','/trouble-shooting/inspect','M1277','P004004004','健康检查',NULL,2195368430699520,'37/2195368430699520/',0,0,NULL,'menu',4,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2207397452385280,'日志在线查看',NULL,NULL,'/trouble-shooting/log/view','M1278','P004004002001','日志在线查看',NULL,2196005348082688,'37/2195368430699520/2196005348082688/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2277937868179456,'AI实验室-jupyterlab',NULL,NULL,'/algorithm-lab/jupyterlab','M1279','P003005001','AI实验室-jupyterlab','jupyter',2127368951759872,'32/2127368951759872/',0,0,NULL,'menu',0,NULL,0,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2348953349097472,'数据资产',NULL,'iconfont icon-data-line',NULL,'M1290','P007000000','数据资产','full',0,'',0,0,NULL,'menu',51,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2354662124651520,'资产目录',NULL,'iconfont icon-a-datalistbeifen2',NULL,'M1291','P007001000','资产目录','full',2348953349097472,'2348953349097472/',0,0,NULL,'menu',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2354667197793280,'对象资产',NULL,'iconfont icon-object-fill','/data-property/object/management','M1292','P007001001','对象资产','full',2354662124651520,'2348953349097472/2354662124651520/',0,0,NULL,'menu',0,NULL,1,0,'2024-05-30 00:00:00','2024-05-30 00:00:00',NULL,NULL);

UPDATE tb_self_monitor_alarm_rule SET base_metrics='[{"metricName":"value","value":"执行失败"}]', object_def='{"objectMode":"MID_REF_MODE","midRefMode":{"objectRefMode":"WORKFLOW_INSTANCE"}}', content_template='离线工作流【${objectName}】执行失败' WHERE id=65;
UPDATE tb_self_monitor_alarm_rule SET base_metrics='[{"metricName":"value","value":"集群不可用"}]' WHERE id=45;
UPDATE tb_system_config SET setting_schema='[{"requireCondition":"","falseLabel":"","defaultValue":"true","objectTitle":false,"description":"是否启用自监控","range":"","optional":false,"label":"消费告警启用开关","type":["BOOL"],"recommendations":[],"availableCondition":"","candidates":["true","false"],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"enable","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"jax_self_monitor_alarm","objectTitle":false,"description":"自监控告警topic,自监控开启会默认创建topic，默认1分区、1副本","range":"","optional":false,"label":"topic名称","type":["STRING"],"recommendations":[],"availableCondition":"","candidates":[],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"topic","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"true","objectTitle":false,"description":"是否自动创建topic,创建的topic分区为1.副本为1","range":"","optional":false,"label":"是否自动创建topic","type":["BOOL"],"recommendations":[],"availableCondition":"","candidates":["true","false"],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"autoCreateTopic","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"group_jax_self_monitor_alarm","objectTitle":false,"description":"自监控告警topic消费组","range":"","optional":false,"label":"消费组","type":["STRING"],"recommendations":[],"availableCondition":"","candidates":[],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"group","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"1","objectTitle":false,"description":"消费线程数量","range":"","optional":false,"label":"消费线程数量","type":["INT"],"recommendations":[],"availableCondition":"","candidates":[],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"maxThreadSize","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"1","objectTitle":false,"description":"线程容量","range":"","optional":false,"label":"消费线程容量","type":["INT"],"recommendations":[],"availableCondition":"","candidates":[],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"queueCapacity","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"true","objectTitle":false,"description":"数据质量告警是否接入到自监控告警","range":"","optional":false,"label":"数据质量告警是否接入到自监控告警","type":["BOOL"],"recommendations":[],"availableCondition":"","candidates":["true","false"],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"dataQualityAlarm","inputType":"TEXT","placeholder":""}]' WHERE id=14;
UPDATE tb_system_config SET setting_schema='[{"requireCondition":"","falseLabel":"","defaultValue":"false","objectTitle":false,"description":"是否启用告警推送","range":"","optional":false,"label":"自监控告警推送启用开关","type":["BOOL"],"recommendations":[],"availableCondition":"","candidates":["true","false"],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"enable","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"jax_self_monitor_alarm_notify","objectTitle":false,"description":"自监控告警推送topic","range":"","optional":false,"label":"topic名称","type":["STRING"],"recommendations":[],"availableCondition":"","candidates":[],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"topic","inputType":"TEXT","placeholder":""},{"requireCondition":"","falseLabel":"","defaultValue":"true","objectTitle":false,"description":"是否自动创建topic,创建的topic分区为1.副本为1","range":"","optional":false,"label":"是否自动创建topic","type":["BOOL"],"recommendations":[],"availableCondition":"","candidates":["true","false"],"trueLabel":"","regex":"","apiVersion":2,"scope":"","name":"autoCreateTopic","inputType":"TEXT","placeholder":""}]' WHERE id=15;

alter table tb_process MODIFY pipeline_config LONGTEXT DEFAULT NULL COMMENT '作业配置';
alter table tb_pipeline MODIFY pipeline_config LONGTEXT DEFAULT NULL COMMENT '作业配置';
alter table tb_process_history MODIFY pipeline_config LONGTEXT DEFAULT NULL COMMENT '作业配置';
