CREATE TABLE `tb_algorithm_lab` (
    `id` bigint(20) NOT NULL,
    `name` varchar(255) NOT NULL COMMENT '名称',
    `description` varchar(512) DEFAULT NULL COMMENT '描述',
    `cpu` int(11) DEFAULT NULL COMMENT 'CPU配置',
    `memory` int(11) DEFAULT NULL COMMENT '内存配置',
    `status` varchar(255) DEFAULT NULL COMMENT '状态',
    `is_deleted` int(1) NOT NULL COMMENT '逻辑删除',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算法实验室';

INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('12', 'Jupyterhub配置', '算法实验室功能依赖Jupyterhub，配置Jupyterhub相关信息后方可使用', '算法实验室', 'jax.algorithm.lab.jupyterhub', NULL, NULL, '[{\"requireCondition\":\"\",\"defaultValue\":\"\",\"description\":\"Jupyterhub地址,例如:http://127.0.0.1:8000\",\"range\":\"\",\"optional\":false,\"label\":\"Jupyterhub地址\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"^https?://[^:/]*(:[0-9]{1,5})?(/[^?#]*)?(\\\\?[^#]*)?(#.*|)?$\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"url\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"\",\"description\":\"使用管理员账号登录Jupyterhub,生成一个token,将该token填写到此处\",\"range\":\"\",\"optional\":false,\"label\":\"Jupyterhub访问token\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"^\\\\w+$\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"token\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', '{\"url\":\"\",\"token\":\"f3a6edc8992141a394bd9a8fbc1ee416\"}', '0', '2024-02-20 17:16:24', '2024-03-04 13:44:36', NULL, NULL);

INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`) VALUES ('3', '0 0/5 * * * ?', '用于同步Jupyterhub的Server状态到中台', '2024-02-28 14:38:09', '2024-02-28 14:39:24', NULL, NULL, 'FIRST', 'syncAllJupyterhubServerStatus', '-1', 'DISCARD_LATER', '41', '0', 'BEAN', '', '初始化代码', '2024-02-28 14:38:09', NULL, '1', '0', '0', 'Jupyterhub-Server状态同步');


CREATE TABLE `tb_jax_cluster_file`
(
    `id`          bigint(20) NOT NULL COMMENT 'id',
    `type`        varchar(255) NOT NULL COMMENT '类型',
    `record_id`   bigint(20) COMMENT '类型对应的记录id',
    `file_path`   varchar(255) NOT NULL COMMENT '文件路径',
    `name`        varchar(255) NOT NULL COMMENT '名称',
    `value_md5`   varchar(255) NOT NULL COMMENT 'value的md5值',
    `version`     int(11) NOT NULL COMMENT 'version版本号',
    `value`       longtext     NOT NULL COMMENT 'value',
    `is_deleted`  int(1) NOT NULL COMMENT '是否已删除',
    `create_time` datetime     NOT NULL COMMENT '创建时间',
    `update_time` datetime     NOT NULL COMMENT '更新时间',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
    PRIMARY KEY (`id`),
    KEY           `idx_type_version` (`type`,`record_id`,`name`,`version`),
    KEY           `idx_type_path` (`type`, `file_path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='中台集群配置变更记录表';



CREATE TABLE `tb_jax_cluster_file_node`
(
    `id`          bigint(20) NOT NULL COMMENT 'id',
    `jcf_id`      bigint(20) NOT NULL,
    `server_id`   varchar(255) NOT NULL COMMENT '服务id',
    `file_path`   varchar(255) NOT NULL COMMENT '实际文件路径',
    `ip`          varchar(255) NOT NULL COMMENT 'ip',
    `port`        int(11) NOT NULL COMMENT 'value',
    `status`      varchar(255) NOT NULL COMMENT '状态',
    `message`     text COMMENT '异常信息',
    `create_time` datetime     NOT NULL COMMENT '创建时间',
    `update_time` datetime     NOT NULL COMMENT '更新时间',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `udx_server_id` (`server_id`,`jcf_id`),
    KEY           `idx_jcfId` (`jcf_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='中台集群配置变更记录集群表';

INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127368951759872, 'AI实验室', NULL, 'iconfont  icon-robot-angry-outline', NULL, 'M1253', 'P003005000', 'AI实验室', NULL, '32', '32/', '0', '0', NULL, 'menu', '4', NULL, '1', '0', '2024-02-23 09:55:48', '2024-02-26 16:25:24', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127375238923264, 'AI实验室', NULL, 'iconfont  icon-robot-angry-outline', '/algorithm-lab/management', 'M1254', 'P003005001', 'AI实验室', NULL, '2127368951759872', '32/2127368951759872/', '0', '0', NULL, 'menu', '0', NULL, '1', '0', '2024-02-23 09:59:00', '2024-02-26 16:25:29', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127501591072768, '创建|编辑|删除|进入', NULL, NULL, NULL, 'B051', 'algorithm_lab:write', '创建|编辑|删除|进入', NULL, '2127375238923264', '32/2127368951759872/2127375238923264/', '0', '0', NULL, 'button', '0', NULL, '1', '0', '2024-02-23 11:12:25', '2024-02-23 13:15:04', '20200321', '20200321');
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127502429979648, 'AI算法实验室创建|编辑|删除', NULL, NULL, NULL, 'R053', 'algorithm_lab:write', '创建|编辑|删除', NULL, '2127375238923264', '32/2127368951759872/2127375238923264/', '0', '1', 'tb_algorithm_lab', 'resource', '0', NULL, '1', '0', '2024-02-23 11:03:42', '2024-02-23 11:19:38', '20200321', '20200321');
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2127521482966016, 'AI算法实验室进入', NULL, NULL, NULL, 'R054', 'algorithm_lab:access', '进入', NULL, '2127375238923264', '32/2127368951759872/2127375238923264/', '0', '1', 'tb_algorithm_lab', 'resource', '0', NULL, '1', '0', '2024-02-23 11:13:23', '2024-02-23 13:14:41', '20200321', '20200321');

INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2136242047747075, 1765192536458240, 'algorithm_lab:write');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2158993976758275, 1765194435396608, 'algorithm_lab:write');


CREATE TABLE `tb_health_check` (
    `id` bigint(20) NOT NULL,
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `status` varchar(255) NOT NULL COMMENT '状态',
    `run_type` varchar(255) NOT NULL COMMENT '运行类型',
    `check_item_num` bigint(20) DEFAULT '0' COMMENT '检查项计数',
    `instance_num` bigint(20) DEFAULT '0' COMMENT '实例数',
    `normal_num` bigint(20) DEFAULT '0' COMMENT '正常计数',
    `suggestion_num` bigint(20) DEFAULT '0' COMMENT '建议计数',
    `exception_num` bigint(20) DEFAULT '0' COMMENT '异常计数',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康检查';


CREATE TABLE `tb_health_check_item` (
    `id` bigint(20) NOT NULL,
    `check_id` bigint(20) NOT NULL COMMENT '检查id',
    `category` varchar(255) NOT NULL COMMENT '类别',
    `component` varchar(255) NOT NULL COMMENT '组件',
    `check_item` varchar(255) NOT NULL COMMENT '检查项',
    `instance_num` bigint(20) DEFAULT NULL COMMENT '实例数',
    `normal_num` bigint(20) DEFAULT NULL COMMENT '正常计数',
    `suggestion_num` bigint(20) DEFAULT NULL COMMENT '建议计数',
    `exception_num` bigint(20) DEFAULT NULL COMMENT '异常计数',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
    PRIMARY KEY (`id`),
    KEY `idx_check_item_check_id` (`check_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康检查项';


CREATE TABLE `tb_health_check_instance` (
    `id` bigint(20) NOT NULL,
    `check_id` bigint(20) NOT NULL COMMENT '检查id',
    `item_id` bigint(20) DEFAULT NULL COMMENT '检查项id',
    `instance_id` bigint(20) DEFAULT NULL COMMENT '实例Id',
    `instance_name` varchar(255) DEFAULT NULL COMMENT '实例名称',
    `instance_info` text COMMENT '实例信息',
    `instance_type` varchar(255) DEFAULT NULL COMMENT '实例类型',
    `status` varchar(255) NOT NULL COMMENT '状态',
    `content` varchar(1024) DEFAULT NULL COMMENT '告警内容',
    `solution` varchar(1024) DEFAULT NULL COMMENT '解决方案',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL COMMENT '更新时间',
    `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
    PRIMARY KEY (`id`),
    KEY `idx_check_instance_item_id` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康检查实例信息';

ALTER TABLE xxl_job_info ADD COLUMN is_user_created tinyint(1) DEFAULT 1 NOT NULL COMMENT '用户创建或内置';


INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('13', '指标收集Kafka配置', '运维排障功能收集到的指标，通过Kafka中转，由kafka-victoriametrics-synchronizer保存到VictoriaMetrics，此处需要配置kafka-victoriametrics-synchronizer监听的的kafka、topic及其所使用的消费组', '运维排障', 'jax.monitor.collect.kafka', 'KAFKA', null, '[{\"requireCondition\":\"\",\"defaultValue\":\"true\",\"description\":\"是否启用运维排障指标收集\",\"range\":\"\",\"optional\":false,\"label\":\"启用开关\",\"type\":[\"BOOL\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[\"true\",\"false\"],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"enable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"flink-metrics\",\"description\":\"kafka-victoriametrics-synchronizer服务监听的topic\",\"range\":\"\",\"optional\":false,\"label\":\"topic名称\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"topic\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"group-flink-metrics\",\"description\":\"kafka-victoriametrics-synchronizer使用的消费组\",\"range\":\"\",\"optional\":false,\"label\":\"消费组\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"group\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', NULL, '0', '2024-03-14 17:12:48', '2024-03-22 17:54:20', NULL, NULL);
INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('14', '自监控配置', '自监控告警配置', '运维排障', 'jax.monitor.alarm', 'KAFKA', NULL, '[{\"requireCondition\":\"\",\"defaultValue\":\"true\",\"description\":\"是否启用自监控\",\"range\":\"\",\"optional\":false,\"label\":\"消费告警启用开关\",\"type\":[\"BOOL\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[\"true\",\"false\"],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"enable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"jax_self_monitor_alarm\",\"description\":\"自监控告警topic\",\"range\":\"\",\"optional\":false,\"label\":\"topic名称\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"topic\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"group_jax_self_monitor_alarm\",\"description\":\"自监控告警topic消费组\",\"range\":\"\",\"optional\":false,\"label\":\"消费组\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"group\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"1\",\"description\":\"消费线程数量\",\"range\":\"\",\"optional\":false,\"label\":\"消费线程数量\",\"type\":[\"INT\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"maxThreadSize\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"1\",\"description\":\"线程容量\",\"range\":\"\",\"optional\":false,\"label\":\"消费线程容量\",\"type\":[\"INT\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"queueCapacity\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"true\",\"description\":\"数据质量告警是否接入到自监控告警\",\"range\":\"\",\"optional\":false,\"label\":\"数据质量告警是否接入到自监控告警\",\"type\":[\"BOOL\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"dataQualityAlarm\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', NULL, '0', '2024-03-14 17:12:48', '2024-04-10 10:03:10', NULL, NULL);
INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('15', '自监控推送配置', '自监控告警推送配置,推送自监控告警及告警恢复至指定topic', '运维排障', 'jax.monitor.notify', 'KAFKA', NULL, '[{\"requireCondition\":\"\",\"defaultValue\":\"false\",\"description\":\"是否启用告警推送\",\"range\":\"\",\"optional\":false,\"label\":\"自监控告警推送启用开关\",\"type\":[\"BOOL\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[\"true\",\"false\"],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"enable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"jax_self_monitor_alarm_notify\",\"description\":\"自监控告警topic\",\"range\":\"\",\"optional\":false,\"label\":\"topic名称\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"topic\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', NULL, '0', '2024-03-14 17:12:48', '2024-04-09 17:19:07', NULL, NULL);
INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('16', '中台节点列表', '该配置在中台自监控中用于判断中台已有节点是否正常，如果中台节点失去连接，则会触发告警, 该配置通常由中台自动维护', '运维排障', 'jax.monitor.jax.web.nodes', NULL, NULL, '[{\"requireCondition\":\"\",\"defaultValue\":\"\",\"description\":\"该配置在中台自监控中用于判断中台已有节点是否正常，如果中台节点失去连接，则会触发告警, 该配置通常由中台自动维护\",\"range\":\"\",\"optional\":true,\"label\":\"中台节点列表\",\"type\":[\"LIST\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"nodes\",\"inputType\":\"TEXT\",\"placeholder\":\"\",\"listParameter\":{\"apiVersion\":2,\"optional\":false,\"objectParameters\":[],\"type\":[\"STRING\"]}}]', NULL, '0', '2024-04-07 21:59:43', '2024-04-09 09:40:50', NULL, NULL);
INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('17', 'Datart集成', '集成Datart数据可视化开放平台。目前支持将中台的JDBC类型的数据源同步到Datart', '三方集成', 'jax.datart.connection', NULL, NULL, '[{"requireCondition":"","defaultValue":"","description":"格式为：http://<ip>:<port>","range":"","optional":false,"label":"datart地址","type":["STRING"],"recommendations":[],"availableCondition":"","candidates":[],"regex":"","apiVersion":2,"scope":"","name":"address","inputType":"TEXT","placeholder":""},{"requireCondition":"","defaultValue":"","description":"登录datart的账号","range":"","optional":false,"label":"登录账号","type":["STRING"],"recommendations":[],"availableCondition":"","candidates":[],"regex":"","apiVersion":2,"scope":"","name":"user","inputType":"TEXT","placeholder":""},{"requireCondition":"","defaultValue":"","description":"登录datart的密码","range":"","optional":false,"label":"登录密码","type":["STRING"],"recommendations":[],"availableCondition":"","candidates":[],"regex":"","apiVersion":2,"scope":"","name":"password","inputType":"PASSWORD","placeholder":""},{"requireCondition":"","defaultValue":"","description":"同步数据到datart时，归属的租户，填名字即可","range":"","optional":true,"label":"归属租户","type":["STRING"],"recommendations":[],"availableCondition":"","candidates":[],"regex":"","apiVersion":2,"scope":"","name":"org","inputType":"TEXT","placeholder":""}]', NULL, '0', '2024-04-23 21:59:43', '2024-04-23 11:58:36', NULL, NULL);


INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '5 0/2 * * * ?', '用于采集JaxWeb存活指标', '2024-03-25 09:26:50', '2024-03-25 10:57:27', NULL, NULL, 'CONSISTENT_HASH', 'jaxNodeLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 09:26:50', NULL, '1', '0', '0', '内部组件-JaxWeb存活指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '10 0/2 * * * ?', '用于采集XXlJob存活指标', '2024-03-25 09:28:07', '2024-03-25 10:57:39', NULL, NULL, 'CONSISTENT_HASH', 'xxlJobExecutorLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 09:28:07', NULL, '1', '0', '0', '内部组件-XxlJob存活指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '15 0/2 * * * ?', '用于采集海豚存活和资源指标', '2024-03-25 09:29:23', '2024-03-25 10:57:51', NULL, NULL, 'CONSISTENT_HASH', 'dolphinschedulerLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 09:29:23', NULL, '1', '0', '0', '内部组件-海豚存活和资源指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '20 0/2 * * * ?', '用于采集KafkaVictoriaMetricsSync服务存活指标', '2024-03-25 09:30:35', '2024-03-25 10:58:00', NULL, NULL, 'CONSISTENT_HASH', 'kafkaVmSyncLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 09:30:35', NULL, '1', '0', '0', ' 内部组件-KafkaVictoriaMetricsSync服务存活指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '25 0/2 * * * ?', '用于采集中台数据库线程指标', '2024-03-25 09:31:49', '2024-03-25 10:58:10', NULL, NULL, 'CONSISTENT_HASH', 'jaxDbMetricCollectHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 09:31:49', NULL, '1', '0', '0', '内部组件-中台数据库线程指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '30 0/2 * * * ?', '用于Cell采集网关存活指标', '2024-03-25 09:34:30', '2024-03-25 10:58:19', NULL, NULL, 'CONSISTENT_HASH', 'jaxCellLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 09:34:30', NULL, '1', '0', '0', '内部组件-Cell采集网关存活指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '35 0/2 * * * ?', '用于采集Zookeeper存活指标', '2024-03-25 09:36:36', '2024-03-25 10:58:43', NULL, NULL, 'CONSISTENT_HASH', 'zookeeperServiceLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 09:36:36', NULL, '1', '0', '0', '外部组件-Zookeeper存活指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '40 0/2 * * * ?', '用于收集Kafka存活指标', '2024-03-25 10:16:20', '2024-03-25 10:58:55', NULL, NULL, 'CONSISTENT_HASH', 'kafkaBrokerLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:16:20', NULL, '1', '0', '0', '外部组件-Kafka存活指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '45 0/2 * * * ?', '用于收集Kafka的Topic指标', '2024-03-25 10:17:23', '2024-03-25 11:26:43', NULL, NULL, 'CONSISTENT_HASH', 'kafkaTopicMetricCollectHandler', '-1', 'SERIAL_EXECUTION', '120', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:17:23', NULL, '1', '1712717565000', '1712717685000', '外部组件-Kafka的Topic指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '50 0/2 * * * ?', '用于收集Elasticsearch存活指标', '2024-03-25 10:18:26', '2024-03-25 10:59:36', NULL, NULL, 'CONSISTENT_HASH', 'elasticsearchLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:18:26', NULL, '1', '0', '0', '外部组件-Elasticsearch存活指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '55 0/2 * * * ?', '用于收集Clickhouse存活指标', '2024-03-25 10:19:22', '2024-03-25 10:59:49', NULL, NULL, 'CONSISTENT_HASH', 'clickhouseLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:19:22', NULL, '1', '0', '0', '外部组件-Clickhouse存活指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '0 0/30 * * * ?', '用于收集Clickhouse磁盘占用指标', '2024-03-25 10:20:27', '2024-03-25 10:33:53', NULL, NULL, 'CONSISTENT_HASH', 'clickhouseDiskMetricCheckHandler', '-1', 'SERIAL_EXECUTION', '120', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:20:27', NULL, '1', '0', '0', '外部组件-Clickhouse磁盘占用指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '5 0/2 * * * ?', '用于收集Hadoop存活指标', '2024-03-25 10:21:12', '2024-03-25 11:00:14', NULL, NULL, 'CONSISTENT_HASH', 'hadoopNodeLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:21:12', NULL, '1', '0', '0', '外部组件-Hadoop存活指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '15 0/2 * * * ?', '用于收集Yarn的CPU和内存指标', '2024-03-25 10:22:16', '2024-03-25 11:00:47', NULL, NULL, 'CONSISTENT_HASH', 'hadoopCpuMemoryCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:22:16', NULL, '1', '0', '0', '外部组件-Yarn的CPU和内存指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '0 0/30 * * * ?', '用于收集HDFS磁盘占用指标', '2024-03-25 10:23:13', '2024-03-25 10:33:12', NULL, NULL, 'CONSISTENT_HASH', 'hadoopDiskCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:23:13', NULL, '1', '0', '0', '外部组件-HDFS磁盘占用指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '10 0/2 * * * ?', '用于收集Redis存活指标', '2024-03-25 10:24:00', '2024-03-25 11:00:28', NULL, NULL, 'CONSISTENT_HASH', 'redisServiceLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:24:00', NULL, '1', '0', '0', '外部组件-Redis存活指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '25 0/2 * * * ?', '用于收集VictoriaMetrics存活指标', '2024-03-25 10:24:41', '2024-03-25 11:01:14', NULL, NULL, 'CONSISTENT_HASH', 'victoriaMetricsLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:24:41', NULL, '1', '0', '0', '外部组件-VictoriaMetrics存活指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '20 0/2 * * * ?', '用于收集Nebula存活指标', '2024-03-25 10:25:29', '2024-03-25 11:01:01', NULL, NULL, 'CONSISTENT_HASH', 'nebulaNodeLivedCheckHandler', '-1', 'SERIAL_EXECUTION', '84', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:25:29', NULL, '1', '0', '0', '外部组件-Nebula存活指标收集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '5 0/2 * * * ?', '用于采集实时任务指标', '2024-03-25 10:27:00', '2024-03-25 11:01:27', NULL, NULL, 'CONSISTENT_HASH', 'jaxRealtimePipelineCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:27:00', NULL, '1', '0', '0', '作业级-实时管线任务指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '10 0/2 * * * ?', '用于采集离线管线作业指标', '2024-03-25 10:27:57', '2024-03-25 11:01:52', NULL, NULL, 'CONSISTENT_HASH', 'jaxOfflinePipelineCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:27:57', NULL, '1', '0', '0', '作业级-离线管线作业指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '15 0/2 * * * ?', '用于采集CK存储任务指标', '2024-03-25 10:29:00', '2024-03-25 11:02:01', NULL, NULL, 'CONSISTENT_HASH', 'jaxStorageCkCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:29:00', NULL, '1', '0', '0', '作业级-CK存储任务指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '20 0/2 * * * ?', '用于采集离线工作流任务指标', '2024-03-25 10:30:07', '2024-03-25 11:02:08', NULL, NULL, 'CONSISTENT_HASH', 'jaxWorkflowCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:30:07', NULL, '1', '0', '0', '作业级-离线工作流任务指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '25 0/2 * * * ?', '用于采集定时任务指标', '2024-03-25 10:30:59', '2024-03-25 11:02:14', NULL, NULL, 'CONSISTENT_HASH', 'jaxXxlJobCheckHandler', '-1', 'SERIAL_EXECUTION', '90', '0', 'BEAN', '', '初始化代码', '2024-03-25 10:30:59', NULL, '1', '0', '0', '作业级-定时任务指标采集', '0');
INSERT INTO `xxl_job_info` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`, `job_name`, `is_user_created`) VALUES ('3', '0 0/1 * * * ?', 'Datart集成', '2024-04-23 12:01:14', '2024-04-23 12:01:17', NULL, NULL, 'ROUND', 'sourceSyncToDatart', '-1', 'SERIAL_EXECUTION', '60', '0', 'BEAN', '', '初始化代码', '2024-04-23 12:01:14', NULL, '1', '0', '0', 'Datart集成', '1');


UPDATE `xxl_job_info` SET `is_user_created`='0' WHERE `job_group`='3' AND `executor_handler`='syncAllJupyterhubServerStatus';
UPDATE `xxl_job_info` SET `is_user_created`='0' WHERE `job_group`='2' AND `executor_handler`='ruleJobHandler';
CREATE TABLE `tb_self_monitor_category` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '英文缩写（唯一）',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `description` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注信息',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父id，空表示树根节点',
  `parent_path` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父路径',
  `root_id` bigint(20) NOT NULL COMMENT '根结点id',
  `is_leaf` tinyint(1) NOT NULL COMMENT '是否为叶子结点',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自监控分类';

CREATE TABLE `tb_self_monitor_item` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `category_id` bigint(20) NOT NULL COMMENT '监控项分类id',
  `object_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对象类型',
  `metric_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '指标名称',
  `metric_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '指标code',
  `description` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `is_atomic` tinyint(1) NOT NULL COMMENT '是否是原子指标',
  `sample_metric` text COLLATE utf8mb4_unicode_ci COMMENT '衍生指标pql',
  `tag_all` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '监控对象所有标签',
  `solution_id` bigint(20) DEFAULT NULL COMMENT '解决方案id',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自监控指标';

CREATE TABLE `tb_self_monitor_threshold` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `rule_id` bigint(20) NOT NULL COMMENT '规则id',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '监控策略名称',
  `start_time` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '监控时间段',
  `end_time` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '监控时间段',
  `alarm_level` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警等级',
  `threshold` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '阈值设置',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自监控阈值设置';

CREATE TABLE `tb_self_monitor_alarm_rule` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `item_id` bigint(20) NOT NULL COMMENT '监控项分类id',
  `base_metrics` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始指标列表',
  `monitor_interval` int(11) NOT NULL COMMENT '采样周期（s）',
  `is_enabled` tinyint(4) NOT NULL COMMENT '告警监控是否启用',
  `object_def` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警对象定义',
  `threshold_method` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '阈值设置方案',
  `threshold_remark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '阈值设置提示',
  `content_template` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警内容模板',
  `auto_close` tinyint(1) NOT NULL COMMENT '告警是否自动恢复',
  `xxl_group_id` bigint(20) DEFAULT NULL COMMENT 'xxl执行器id',
  `xxl_job_id` bigint(20) DEFAULT NULL COMMENT 'xxljobid',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tb_self_monitor_alarm_rule_unique` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自监控告警规则';

CREATE TABLE `tb_self_monitor_event` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `alarm_id` bigint(20) NOT NULL COMMENT '压缩后告警id',
  `origin_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始告警id,可以是文本',
  `active_time` datetime DEFAULT NULL COMMENT '触发时间',
  `resolved_time` datetime DEFAULT NULL COMMENT '处理时间',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态',
  `severity` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警等级',
  `object_id` bigint(20) DEFAULT NULL COMMENT '告警对象id',
  `object_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警对象名称',
  `object_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警对象类型',
  `instance_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警来源',
  `metric_id` bigint(20) DEFAULT NULL COMMENT '指标id',
  `metric_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指标名称',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '规则id',
  `rule_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规则名称',
  `value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警值',
  `source` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警来源',
  `solution_id` bigint(20) DEFAULT NULL COMMENT '解决方案id',
  `content` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警内容',
  `addition` text COLLATE utf8mb4_unicode_ci COMMENT '扩展信息',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`id`),
  KEY `idx_alarm_id` (`alarm_id`) USING BTREE,
  KEY `idx_rule_id` (`rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警原始事件表';

CREATE TABLE `tb_self_monitor_alarm` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `uid` bigint(20) NOT NULL COMMENT 'uid:用于告警压缩',
  `occur_time` datetime NOT NULL COMMENT '首次发生事件',
  `last_time` datetime NOT NULL COMMENT '最近告警时间',
  `close_time` datetime DEFAULT NULL COMMENT '关闭时间',
  `occur_count` bigint(20) NOT NULL COMMENT '告警次数（重复次数/压缩次数）',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警状态，OPEN 开启，CLOSED 关闭',
  `close_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关闭类型，MANUAL 手动关闭，AUTO 自动恢复',
  `close_reason` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关闭原因描述，MANUAL用户输入信息，AUTO 自动恢复',
  `severity` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警等级',
  `object_id` bigint(20) DEFAULT NULL COMMENT '告警对象id',
  `object_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警对象名称',
  `object_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警对象类型',
  `instance_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警来源',
  `metric_id` bigint(20) DEFAULT NULL COMMENT '指标id',
  `metric_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指标名称',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '规则id',
  `rule_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规则名称',
  `value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警值',
  `source` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警来源',
  `solution_id` bigint(20) DEFAULT NULL COMMENT '解决方案id',
  `content` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警内容',
  `addition` text COLLATE utf8mb4_unicode_ci COMMENT '扩展信息',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
  `resource_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源类型',
  PRIMARY KEY (`id`),
  KEY `idx_self_alarm_rule_id` (`rule_id`) USING BTREE,
  KEY `idx_object_id` (`object_id`) USING BTREE,
  KEY `idx_content` (`content`(512)) USING BTREE,
  KEY `idx_severity` (`severity`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_last_time` (`last_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警表';


CREATE TABLE `tb_task_topic_chain` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `task_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '作业名称',
  `task_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '作业名称',
  `parent_task_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父级作业名称,如ck集群名称',
  `task_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '作业类型',
  `topic` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'topic',
  `consumer_group` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消费组',
  `tb_id` bigint(20) DEFAULT NULL COMMENT '模型id',
  `tb_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型名称',
  `role_type` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消费|生产',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='kafkatopic与任务关联表';

CREATE TABLE `tb_solution` (
  `id` bigint NOT NULL COMMENT '主键',
  `solution` text NOT NULL COMMENT '处置建议',
  `remark` varchar(255) NOT NULL COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处置建议';

INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195924546192384, 'system', '系统级', NULL, NULL, NULL, 2195924546192384, 0, 0, '2024-03-18 15:04:59', '2024-03-18 15:04:59', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195930664698880, 'inner_component', '内部组件', NULL, 2195924546192384, '2195924546192384/', 2195924546192384, 0, 0, '2024-03-18 15:08:06', '2024-03-18 15:08:06', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195933290890240, 'web_component', 'web组件', NULL, 2195930664698880, '2195924546192384/2195930664698880/', 2195924546192384, 1, 0, '2024-03-18 15:09:26', '2024-03-18 15:09:26', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195934965695488, 'xxl_job_group', '定时作业调度器', NULL, 2195930664698880, '2195924546192384/2195930664698880/', 2195924546192384, 1, 0, '2024-03-18 15:10:17', '2024-03-18 15:10:17', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195935665030144, 'offline_executor', '工作流调度器', NULL, 2195930664698880, '2195924546192384/2195930664698880/', 2195924546192384, 1, 0, '2024-03-18 15:10:38', '2024-03-18 15:10:38', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195936151143424, 'filnk-metric-sync', '指标转发器', NULL, 2195930664698880, '2195924546192384/2195930664698880/', 2195924546192384, 1, 0, '2024-03-18 15:10:53', '2024-03-18 15:10:53', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195936835634176, 'main_database', '主数据库', NULL, 2195930664698880, '2195924546192384/2195930664698880/', 2195924546192384, 1, 0, '2024-03-18 15:11:14', '2024-03-18 15:11:14', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195936835634177, 'main_kafka', '告警kafka', NULL, 2195930664698880, '2195924546192384/2195930664698880/', 2195924546192384, 1, 0, '2024-03-18 15:11:14', '2024-03-18 15:11:14', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195941344969728, 'extra_component', '外部组件', NULL, 2195924546192384, '2195924546192384/', 2195924546192384, 0, 0, '2024-03-18 15:13:32', '2024-03-18 15:13:32', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195942229476352, 'Kafka', 'Kafka', NULL, 2195941344969728, '2195924546192384/2195941344969728/', 2195924546192384, 1, 0, '2024-03-18 15:13:59', '2024-03-18 15:13:59', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195942549357568, 'Elasticsearch', 'Elasticsearch', NULL, 2195941344969728, '2195924546192384/2195941344969728/', 2195924546192384, 1, 0, '2024-03-18 15:14:08', '2024-03-18 15:14:08', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195942721946624, 'Clickhouse', 'Clickhouse', NULL, 2195941344969728, '2195924546192384/2195941344969728/', 2195924546192384, 1, 0, '2024-03-18 15:14:14', '2024-03-18 15:14:14', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2195943042221056, 'Hadoop', 'Hadoop', NULL, 2195941344969728, '2195924546192384/2195941344969728/', 2195924546192384, 1, 0, '2024-03-18 15:14:23', '2024-03-18 15:14:23', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2220795316734976, 'task', '作业级', NULL, NULL, NULL, 2220795316734976, 0, 0, '2024-03-27 09:54:55', '2024-03-27 09:54:55', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2220796423113728, 'flink', 'Flink作业', NULL, 2220795316734976, '2220795316734976/', 2220795316734976, 1, 0, '2024-03-27 09:55:28', '2024-03-27 09:55:28', NULL, NULL);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2225280234914816, 'ck', 'CK存储任务', NULL, 2220795316734976, '2220795316734976/', 2220795316734976, 1, 0, '2024-03-28 23:56:03', '2024-03-28 23:56:03', 1858838275228672, 1858838275228672);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2225280234914817, 'workflow', '工作流', NULL, 2220795316734976, '2220795316734976/', 2220795316734976, 1, 0, '2024-03-28 23:56:03', '2024-03-28 23:56:03', 1858838275228672, 1858838275228672);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2225280234914818, 'xxl_job', '定时任务', NULL, 2220795316734976, '2220795316734976/', 2220795316734976, 1, 0, '2024-03-28 23:56:03', '2024-03-28 23:56:03', 1858838275228672, 1858838275228672);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2225283460695040, 'data-service', '数据服务', NULL, 2220795316734976, '2220795316734976/', 2220795316734976, 0, 0, '2024-03-28 23:57:42', '2024-03-28 23:57:42', 1858838275228672, 1858838275228672);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2225979498759168, 'Redis', 'Redis', NULL, 2195941344969728, '2195924546192384/2195941344969728/', 2195924546192384, 0, 0, '2024-03-29 05:51:43', '2024-03-29 05:51:43', 1858838275228672, 1858838275228672);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2225979839382528, 'VictoriaMetrics', 'VictoriaMetrics', NULL, 2195941344969728, '2195924546192384/2195941344969728/', 2195924546192384, 0, 0, '2024-03-29 05:51:54', '2024-03-29 05:51:54', 1858838275228672, 1858838275228672);
INSERT INTO tb_self_monitor_category (id, code, name, description, parent_id, parent_path, root_id, is_leaf, is_deleted, create_time, update_time, create_user, update_user) VALUES(2225979839382529, 'Nebula', 'Nebula', NULL, 2195941344969728, '2195924546192384/2195941344969728/', 2195924546192384, 0, 0, '2024-03-29 05:51:54', '2024-03-29 05:51:54', 1858838275228672, 1858838275228672);

INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(1, '建议根据实际情况在启动脚本适当增加Xmx和Xms', '应用内存', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(2, '重启不可用的中台服务', '中台服务', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(3, '建议将中台部署到压力较小的主机上', '中台服务-所在主机资源', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(4, '建议清理logs目录下的日志文件释放使用空间；或者对磁盘扩容', '日志清理', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(5, '启动定时作业所需要的执行器程序', '定时作业调度器', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(6, '启动海豚调度的MasterServer和WorkerServer', '工作流调度器-执行器可用性', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(7, '建议将MasterServer和WorkerServer部署到压力较小的主机上', '工作流调度器-所在主机资源', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(8, '修复zookeeper后，在平台管理/系统管理/数据开发/离线工作流中重新配置正确的zookeeper地址', '工作流调度器-zk', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(9, '根据安装部署手册的Flink作业监控章节，部署kafka-victoriametrics-synchronize进程，并在平台管理-系统管理-运维配置中配置正确的topic和消费组', '指标转发器', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(10, '建议增加指标topic分区,并部署多个kafka-victoriametrics-synchronizer服务提高处理速度', '指标转发器', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(11, '建议适当增加数据库连接数上限', '数据库连接数', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(12, '修复Kafka集群，在平台管理/地址管理/数据源管理中重新维护Kafka集群的broker列表', 'kafka', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(13, '修复主分区所在的broker，清理无法修复数据的topic', 'kafka', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(14, '修复分区副本所在的broker，清理无法修复数据的topic', 'kafka', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(15, '建议为topic设置合理的数据过期时间（retention.ms单位毫秒）', 'kafka', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(16, '修复Elasticsearch集群，在平台管理/地址管理/数据源管理中重新维护Elasticsearch集群的节点列表', 'es', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(17, '修复丢失shard的index所在的Elasticsearch节点，清理无法修复数据的index', 'es', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(18, '修复Clickhouse集群，在平台管理/地址管理/数据源管理中重新维护Clickhouse集群的节点列表', 'ck', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(19, '在平台管理/地址管理/数据源管理中设置正确的Clickhouse JDBC连接信息', 'ck', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(20, '修复的Hadoop集群，在平台管理/集群管理重新配置Hadoop集群', 'hadoop', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(21, '停止Yarn上的重复旧任务', 'hadoop', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(22, '建议扩容Yarn集群', 'hadoop', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(23, '修复的Redis服务，在平台管理/系统管理/数据服务中配置正确的Resis地址', 'redis', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(24, '修复VictoriaMetrics，在平台管理/系统管理/基础配置中配置正确的VictoriaMetrics地址', 'vm', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(25, '在平台管理/地址管理/数据源管理中设置正确的Nebula服务地址', 'nebula', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(26, '修复Nebula集群，在平台管理/地址管理/数据源管理中重新维护Nebula集群的节点列表', 'nebula', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(27, '在运维中心/实时任务运维中，通过任务指标和运行日志排查问题', 'flink', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(28, '在运维中心/实时任务运维中，通过任务指标和运行日志排查问题。资源不足时，增加作业并行度提高处理速度；数据倾斜时，调整数据分区策略', 'flink', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(29, '增加作业并行度提高处理速度', 'flink', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(30, '适当增加作业的内存和并行度，缓解单并行度压力', 'flink', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(31, '增加作业并行度。缓解单并行度压力', 'flink', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(32, '在运维中心/实时存储任务运维中，通过任务指标和运行日志排查问题', 'flink', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(33, '优化数据服务中的计算逻辑，开启数据服务缓存功能', '数据服务', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(34, '优化数据服务中的计算逻辑', '数据服务', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(35, '在定时作业/调度日志中查看任务日志，排查问题', '定时作业', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(36, '在运维中心/离线任务运维/工作流实例中查看任务日志，排查问题', '工作流', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);
INSERT INTO tb_solution (id, solution, remark, is_deleted, create_time, update_time, create_user, update_user) VALUES(37, '在平台管理/系统管理/运维排障-自监控配置中开启告警消费，并适当增加消费线程数量', '自监控告警处理', 0, '2024-03-27 12:10:49', '2024-03-27 12:10:49', NULL, NULL);


INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(1, 2195933290890240, 'JAX_WEB', ' 组件可用性', 'jax_web_node_lived', '监控中台集群节点的可用性', 1, NULL, '["serverAddress"]', 2, 0, '2024-03-25 15:46:07', '2024-04-01 21:42:41', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(2, 2195933290890240, 'JAX_WEB', 'JVM 内存使用率', 'jax_web_heap_usage', '中台进程的JVM内存使用率，即内存使用量和可用量的比值。通常中台管理的数据（数据模型、数据任务，尤其是数据服务）越多，所需要的内存就越多。当系统监测到使用率超过阈值时产生告警；当使用率低于阈值时，告警自动恢复。', 0, 'jax_web_node_jvm_head_used/jax_web_node_jvm_head_committed * 100', '["serverAddress"]', 1, 0, '2024-03-27 09:41:00', '2024-03-29 06:42:58', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(3, 2195933290890240, 'JAX_WEB', 'JVM FullGC频率', 'jax_web_node_jvm_gc_avg', '中台进程的JVM FullGC频率，即每分钟FullGC的次数。当内存使用一直维持在高峰时，FullGC频率会上升，导致系统响应异常。当系统监测到频率超过阈值时产生告警；当频率低于阈值时，告警自动恢复。', 0, 'delta(jax_web_node_jvm_gc_PS_Scavenge_count[2m])/2', '["serverAddress"]', 1, 0, '2024-03-27 09:41:00', '2024-03-28 18:09:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(4, 2195933290890240, 'JAX_WEB', '所在主机的CPU使用率', 'jax_web_node_cpu_usage', '中台进程所在主机的CPU使用率，注意监控的是主机的整体使用率，并非中台进程的CPU使用率。当系统监测到使用率超过阈值时产生告警；当使用率低于阈值时，告警自动恢复。', 1, NULL, '["serverAddress"]', 3, 0, '2024-03-27 09:41:00', '2024-03-28 18:09:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(5, 2195933290890240, 'JAX_WEB', '所在主机的内存使用率', 'jax_web_node_memory_usage', '中台进程所在主机的内存使用率，注意监控的是内存的整体使用率，并非中台进程的进程使用率。当系统监测到频率超过阈值时产生告警；当使用率低于阈值时，告警自动恢复。', 1, NULL, '["serverAddress"]', 3, 0, '2024-03-27 09:41:34', '2024-04-01 22:22:32', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(6, 2195933290890240, 'JAX_WEB', '所在主机的文件系统使用率', 'jax_web_node_disk_usage', '中台进程所在主机的文件系统的使用率。系统运行的时间越长，产生的文件越多，文件系统使用率越高。当系统监测到频率超过阈值时产生告警；当使用率低于阈值时，告警自动恢复。', 1, NULL, '["serverAddress"]', 4, 0, '2024-03-27 09:41:00', '2024-04-01 22:22:37', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(7, 2195934965695488, 'EXECUTOR_APP', '执行器可用性', 'jax_xxl_job_executor_lived', '中台定时作业中注册的执行器的可用性。定时作业的作业需要执行器执行，当执行器组中没有发现可用的执行器时，产生告警；执行器成功注册后，告警自动恢复。', 1, NULL, '["appName","id"]', 5, 0, '2024-03-27 09:41:00', '2024-03-29 08:11:48', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(8, 2195935665030144, 'WORKFLOW_WORKER', '执行器可用性', 'jax_dolphinscheduler_worker_lived', '中台工作流作业的执行节点的可用性。工作流作业的作业需要执行节点执行，当执行节点不可用时，产生告警；执行节点启动后，告警自动恢复。', 1, NULL, '["appName","id"]', 6, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(9, 2195935665030144, 'WORKFLOW_WORKER', '执行器所在主机的CPU使用率', 'jax_dolphinscheduler_worker_cpu_usage', '工作流执行器所在主机的CPU使用率，注意监控的是主机的整体使用率，并非工作流执行器进程的CPU使用率。当系统监测到使用率超过阈值时产生告警；当使用率低于阈值时，告警自动恢复。', 1, NULL, '["id","address"]', 7, 0, '2024-03-27 09:41:00', '2024-03-29 11:06:14', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(10, 2195935665030144, 'WORKFLOW_WORKER', '执行器所在主机的内存使用率', 'jax_dolphinscheduler_worker_memory_usage', '工作流执行器所在主机的内存使用率，注意监控的是主机的整体使用率，并非工作流执行器进程的内存使用率。当系统监测到使用率超过阈值时产生告警；当使用率低于阈值时，告警自动恢复。', 1, NULL, '["id","address"]', 7, 0, '2024-03-27 09:41:00', '2024-03-29 11:13:18', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(11, 2195935665030144, 'WORKFLOW_WORKER', ' 执行器所在主机的文件系统使用率', 'jax_dolphinscheduler_worker_disk_usage', '工作流执行器所在主机的文件系统的使用率。系统运行的时间越长，产生的文件越多，文件系统使用率越高。当系统监测到频率超过阈值时产生告警；当使用率低于阈值时，告警自动恢复。', 1, NULL, '["id","address"]', 4, 0, '2024-03-27 09:41:00', '2024-03-29 11:13:14', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(12, 2195935665030144, 'WORKFLOW_WORKER', 'zk可用性', 'jax_zookeeper_cluster_lived', '工作流依赖zookeeper实现分布式调度功能。当系统监测到zookeeper不可用时产生告警；zookeeper恢复后，告警自动恢复。', 1, NULL, '["zookeeperCluster"]', 8, 0, '2024-03-27 09:41:00', '2024-03-29 10:54:35', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(13, 2195936151143424, 'KAFKA_VM_SYNC', '组件可用性', 'jax_kafka_vm_synch_lived', 'kafka-victoriametrics-synchronizer将flink任务产生的指标（在kafka中）写入victoriametrics。当系统监测到组件不可用时产生告警；组件恢复后，告警自动恢复。', 1, NULL, '["consumerGroupId"]', 9, 0, '2024-03-27 09:41:00', '2024-03-29 10:54:35', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(14, 2195936151143424, 'KAFKA_VM_SYNC', '消费积压', 'jax_kafka_vm_synch_lag', 'kafka-victoriametrics-synchronizer将flink导出到kafka的任务指标写入victoriametrics。当系统监测到消费积压时，并且积压超过5分钟kafka数据生产量，产生告警；消费积压恢复后，告警自动恢复。', 1, NULL, '["consumerGroupId"]', 10, 0, '2024-03-27 09:41:00', '2024-03-29 10:54:35', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(15, 2195936835634176, 'MAIN_DB', '连接数', 'jax_db_threads_available_connections', '中台服务使用的主数据库的剩余可用连接数。当系统监测到连接数低于阈值时产生告警；当连接数超过阈值时，告警自动恢复。', 1, NULL, '[]', 11, 0, '2024-03-27 09:41:00', '2024-03-29 07:29:14', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(16, 2195936835634177, 'ALARM_CONSUMER', '自监控消费积压', 'jax_alarm_consumer_lived', '自监控大部分告警通过kafka消费产生。当系统监测到消费积压时，并且积压超过5分钟kafka数据生产量，产生告警；消费积压恢复后，告警自动恢复。', 1, NULL, '["consumerGroupId"]', 37, 0, '2024-03-27 09:41:00', '2024-03-29 10:54:35', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(17, 2195942229476352, 'KAFKA', '集群可用性', 'jax_kafka_cluster_lived', '监控Kafka集群的可用性，集群不可用时产生告警；集群可用后，告警自动恢复。', 1, NULL, '["dsName","dsId"]', 12, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(18, 2195942229476352, 'KAFKA', '节点可用性', 'jax_kafka_broker_lived', '监控Kafka集群broker的可用性。集群的broker丢失后产生告警；broker恢复后，告警自动恢复。', 1, NULL, '["dsName","dsId","server"]', 12, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(20, 2195942229476352, 'KAFKA', '分区可用性', 'jax_kafka_topic_partition_available', '监控Kafka的Topic主分区的可用性。发现有Topic主分区不可用（主分区所在的broker丢失）时，产生告警；修复后，告警自动恢复。', 1, NULL, '["dsName","dsId","topic","partition"]', 13, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(21, 2195934965695488, 'EXECUTOR_APP', '自监控执行器可用性', 'jax_xxl_job_executor_lived', '规则引擎执行器的可用性。自监控检测需要规则引擎执行器执行，当执行器组中没有发现可用的执行器时，产生告警；执行器成功注册后，告警自动恢复。', 1, NULL, '["appName","id"]', 5, 0, '2024-03-27 09:41:00', '2024-03-29 08:11:48', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(22, 2195942229476352, 'KAFKA', '分区副本可用性', 'jax_kafka_topic_partition_replicas_available', '监控Kafka的Topic分区副本的可用性。发现有Topic分区副本不可用（副本所在的broker丢失）时，产生告警；修复后，告警自动恢复。', 1, NULL, '["dsName","dsId","topic","partition"]', 14, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(24, 2195942229476352, 'KAFKA', '大TOPIC', 'jax_kafka_topic_bytes', '监控Kafka的Topic的使用情况，当TOPIC内的数据量超过阈值时，产生告警；低于阈值时，告警自动恢复。', 1, NULL, '["dsName","dsId","topic"]', 15, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(26, 2195942549357568, 'ELASTICSEARCH', '集群健康', 'jax_elasticsearch_cluster_health', '监控Elasticsearch集群的健康度。当健康度不是green时告警；健康度为green后，告警自动恢复。', 1, NULL, '["dsName","dsId"]', 16, 0, '2024-03-27 09:41:00', '2024-03-29 07:47:42', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(27, 2195942549357568, 'ELASTICSEARCH', '节点可用性', 'jax_elasticsearch_node_lived', '监控Elasticsearch集群的节点的可用性。集群的节点丢失后产生告警；节点恢复后，告警自动恢复。', 1, NULL, '["dsName","dsId","node"]', 17, 0, '2024-03-27 09:41:00', '2024-03-29 07:47:42', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(28, 2195942721946624, 'CLICKHOUSE', '集群健康', 'jax_clickhouse_cluster_lived', '监控Clickhouse集群的可用性，集群不可用时产生告警；集群可用后，告警自动恢复。', 1, NULL, '["dsName","dsId"]', 18, 0, '2024-03-27 09:41:00', '2024-03-29 07:52:53', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(30, 2195942721946624, 'CLICKHOUSE', '节点可用性', 'jax_clickhouse_node_lived', '监控Clickhouse集群的节点的可用性。集群的节点丢失后产生告警；节点恢复后，告警自动恢复。', 1, NULL, '["dsName","dsId","serverNode"]', 19, 0, '2024-03-27 09:41:00', '2024-03-29 07:52:53', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(32, 2195943042221056, 'HADOOP', 'hdfs可用性', 'jax_hadoop_hdfs_lived', '监控Hadoop集群中的Hdfs是否可用。NameNode不可用时产生告警；NameNode恢复后，告警自动恢复。', 1, NULL, '["clusterName","id"]', 20, 0, '2024-03-27 09:41:00', '2024-03-29 07:57:17', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(34, 2195943042221056, 'HADOOP', 'yarn可用性', 'jax_hadoop_yarn_lived', '监控Hadoop集群中的Yarn是否可用。ResourceManager不可用时产生告警；ResourceManager恢复后，告警自动恢复。', 1, NULL, '["clusterName","id"]', 20, 0, '2024-03-27 09:41:00', '2024-03-29 07:57:17', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(36, 2195943042221056, 'HADOOP', 'CPU资源使用率', 'jax_hadoop_yarn_cpu_usage', '监控Yarn集群CPU使用情况，当CPU使用量超过阈值时产生告警，当使用量低于阈值时，告警自动恢复。', 1, NULL, '["clusterName","id"]', 22, 0, '2024-03-27 09:41:00', '2024-03-28 18:09:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(38, 2195943042221056, 'HADOOP', '内存资源使用率', 'jax_hadoop_yarn_memory_usage', '监控Yarn集群内存使用情况，当内存使用量超过阈值时产生告警，当使用量低于阈值时，告警自动恢复。', 1, NULL, '["clusterName","id"]', 22, 0, '2024-03-27 09:41:00', '2024-03-28 18:09:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(40, 2195943042221056, 'HADOOP', '重复作业', 'jax_hadoop_yarn_app_repeat_run', '监控Yarn上是否存在同名任务，当发现同名任务时产生告警；同名任务不存在时，告警自动恢复。', 1, NULL, '["clusterName","id"]', 21, 0, '2024-03-27 09:41:00', '2024-03-28 18:09:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(42, 2225979498759168, 'REDIS', '集群可用性', 'jax_redis_cluster_lived', '监控数据服务缓存限流功能使用的Redis服务。Redis不可用时产生告警；Redis恢复后，告警自动恢复。', 1, NULL, '["dsName","dsId","nodeAddress"]', 23, 0, '2024-03-27 09:41:00', '2024-03-29 07:57:17', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(45, 2225979839382528, 'VICTORIA_METRICS', '集群可用性', 'jax_main_victoria_metrics_lived', '中台的监控功能（任务监控、数据质量、平台监控，数据服务）使用VictoriaMetrics作为监控指标数据存储服务。VictoriaMetrics不可用时产生告警；VictoriaMetrics恢复后，告警自动恢复。', 1, '', '["dsName","dsId","address"]', 24, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(47, 2225979839382529, 'NEBULA', '集群可用性', 'jax_nebula_cluster_lived', '监控Nebula节点是否可用，节点不可用时产生告警；节点可用后，告警自动恢复。', 1, NULL, '["dsName","dsId","address"]', 25, 0, '2024-03-27 09:41:00', '2024-03-29 07:57:17', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(48, 2225979839382529, 'NEBULA', '节点可用性', 'jax_nebula_node_lived', '监控Nebula集群是否可用，集群不可用时产生告警；集群可用后，告警自动恢复。', 1, NULL, '["dsName","dsId","address"]', 26, 0, '2024-03-27 09:41:00', '2024-03-29 07:57:17', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(50, 2220796423113728, 'FLINK_TASK', '作业可用性', 'jax_realtime_pipeline_job_failed', '监控实时作业，作业运行失败后告警；作业正常运行后，告警自动恢复。', 1, NULL, '["app_resource","job_name"]', 27, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(51, 2220796423113728, 'FLINK_TASK', '并行度可用性', 'jax_realtime_pipeline_job_restart', '监控实时作业并行度，tm运行失败、重启会导致可用的并行度减少，产生告警', 0, 'delta(flink_jobmanager_job_fullRestarts[1m])', '["app_resource","job_name"]', 27, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(53, 2220796423113728, 'FLINK_TASK', '作业背压率', 'jax_realtime_pipeline_backpressure', '监控实时作业的背压，当算子无法及时处理数据（资源不足、数据倾斜等）的背压率升高，超过阈值时，产生告警', 0, 'max by (app_resource,job_name) (avg by (app_resource,task_name,job_name)(avg_over_time(flink_taskmanager_job_task_isBackPressured[5m])))', '["app_resource","job_name"]', 28, 0, '2024-03-27 09:41:00', '2024-03-29 11:13:14', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(54, 2220796423113728, 'FLINK_TASK', '消费积压', 'jax_realtime_pipeline_lag', '监控实时作业消费kafka数据的速率，当作业消费速度达不到生产速度时，消费积压超过阈值比例（消费速度/生产速度），产生告警', 0, 'sum by(topic,consumergroup)(kafka_consumergroup_lag) - ignoring(consumergroup) group_left sum by (topic) (delta(kafka_topic_partition_current_offset[${threshold}m])) ', '["topic","consumergroup"]', 29, 0, '2024-03-27 10:00:25', '2024-03-28 23:24:52', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(56, 2220796423113728, 'FLINK_TASK', 'FullGC频率', 'jax_realtime_pipeline_fullGc', 'Flink作业的JVM FullGC频率，即每分钟FullGC的次数。当内存使用一直维持在高峰时，FullGC频率会上升，导致作业异常。当系统监测到频率超过阈值时产生告警；当频率低于阈值时，告警自动恢复。', 0, 'sum by (app_resource,job_name)(delta(flink_taskmanager_Status_JVM_GarbageCollector_PS_MarkSweep_Count[2m])/2) or sum by (app_resource,job_name)(delta(flink_taskmanager_Status_JVM_GarbageCollector_G1_Old_Generation_Count[2m])/2)', '["app_resource","job_name"]', 30, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(57, 2220796423113728, 'FLINK_TASK', '作业CPU使用率', 'jax_realtime_pipeline_cpuUsage', 'Flink作业的CPU使用率。当作业压力大时，CPU使用率升高，产生告警', 0, 'avg by (app_resource,job_name)(rate(flink_taskmanager_Status_JVM_CPU_Time[2m])/1000000000) * 100', '["app_resource","job_name"]', 31, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(60, 2225280234914816, 'CK_STORAGE', '作业可用性', 'jax_storage_ck_job_failed', 'Clickhouse存储作业运行失败时，产生告警', 1, NULL, '["storageCkId","storageCkName"]', 32, 0, '2024-03-27 09:41:00', '2024-03-29 08:06:04', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(61, 2225280234914816, 'CK_STORAGE', '消费积压', 'jax_storage_ck_job_lag', '监控Clickhouse存储作业消费kafka数据的速率，当作业消费速度达不到生产速度时，消费积压超过阈值比例（消费速度/生产速度），产生告警', 0, 'sum by(topic,consumergroup)(kafka_consumergroup_lag) - ignoring(consumergroup) group_left sum by (topic) (delta(kafka_topic_partition_current_offset[${threshold}m]))', '["topic","consumergroup"]', 29, 0, '2024-03-27 09:41:00', '2024-03-29 08:06:04', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(64, 2225280234914818, 'XXL_JOB', '任务调度/执行失败', 'jax_xxl_job_failed', '定时任务调度或执行失败时，产生告警', 1, NULL, '["jobId","jobName"]', 35, 0, '2024-03-27 09:41:00', '2024-03-29 08:06:04', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(65, 2225280234914817, 'WORKFLOW_JOB', '任务调度/执行失败', 'jax_workflow_job_failed', '工作流任务调度或执行失败时，产生告警', 1, NULL, '["id","name","processDefinitionCode","processDefinitionName","scheduleTime"]', 36, 0, '2024-03-27 09:41:00', '2024-03-27 09:41:00', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(66, 2225283460695040, 'DATA_SERVICE', '服务接口平均耗时', 'jax_data_service_api_duration_avg', '监控数据服务平均耗时，当系统监测到耗时超过阈值时产生告警；当耗时低于阈值时，告警自动恢复。', 0, '(sum by(apiId) (delta(jax_data_service_api_sum[2m]))) /((sum by(apiId) (delta(jax_data_service_api_count[2m])))*1000)', '["apiId"]', 33, 0, '2024-03-27 09:41:00', '2024-03-29 08:06:04', NULL, NULL);
INSERT INTO tb_self_monitor_item (id, category_id, object_type, metric_name, metric_code, description, is_atomic, sample_metric, tag_all, solution_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(68, 2225283460695040, 'DATA_SERVICE', '服务接口平均成功率', 'jax_data_service_api_success_avg', '监控数据服务平均成功率，当系统监测到成功率低于阈值时产生告警；当成功率高于阈值时，告警自动恢复。', 0, '((sum by(apiId) (delta(jax_data_service_api_count{retCode=''0000''}[2m]))*100) /(sum by(apiId) (delta(jax_data_service_api_count[2m])))) or ( (sum by(apiId) (delta(jax_data_service_api_count-jax_data_service_api_count{retCode!=''0000''}[2m]))*100) /(sum by(apiId) (delta(jax_data_service_api_count[2m]))) )', '["apiId"]', 34, 0, '2024-03-27 09:41:00', '2024-03-29 08:06:04', NULL, NULL);

INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(1, 1, '[{"metricName":"value","value":"服务不可用"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"中台服务"},"instance":{"tags":["serverAddress"],"objectNameRef":"${serverAddress}"}}', 'LIVENESS', '应用状态告警,无需设置阈值', '中台服务${instanceName}不可用', 1, 2, NULL, 0, '2024-03-25 15:46:07', '2024-04-09 17:12:07', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(2, 2, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"中台服务"},"instance":{"tags":["serverAddress"],"objectNameRef":"${serverAddress}"}}', 'NORMAL', 'JVM内存使用率(百分制)', '中台服务${instanceName}JVM内存使用率超过${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-09 17:12:09', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(3, 3, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"中台服务"},"instance":{"tags":["serverAddress"],"objectNameRef":"${serverAddress}"}}', 'NORMAL', '每分钟fullGc次数', '中台服务${instanceName}fullGc超过${threshold}次', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-09 17:12:11', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(4, 4, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"中台服务"},"instance":{"tags":["serverAddress"],"objectNameRef":"${serverAddress}"}}', 'NORMAL', 'CPU使用率(百分制)', '中台服务${instanceName}cpu使用率超过${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-09 17:12:13', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(5, 5, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"中台服务"},"instance":{"tags":["serverAddress"],"objectNameRef":"${serverAddress}"}}', 'NORMAL', '内存使用率(百分制)', '中台服务${instanceName}内存使用率超过${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:34', '2024-04-09 17:12:15', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(6, 6, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"中台服务"},"instance":{"tags":["serverAddress"],"objectNameRef":"${serverAddress}"}}', 'NORMAL', '文件系统使用率(百分制)', '中台节点${instanceName}文件系统使用率超过${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-09 17:12:18', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(7, 7, '[{"metricName":"value","value":"0"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"executorName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '定时作业调度器【${objectName}】无可用节点', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 13:25:43', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(8, 8, '[{"metricName":"value","value":"0"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"工作流调度器"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '工作流调度器无可用节点', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 14:33:20', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(9, 9, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"工作流调度器"},"instance":{"tags":["address"],"objectNameRef":"${address}"}}', 'NORMAL', 'CPU使用率(百分制)', '工作流执行器所在主机【${instanceName}】CPU使用率过高，超过了${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 15:02:02', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(10, 10, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"工作流调度器"},"instance":{"tags":["address"],"objectNameRef":"${address}"}}', 'NORMAL', '内存使用率(百分制)', '工作流执行器所在主机【${instanceName}】内存使用率过高，超过了${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 15:02:04', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(11, 11, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"工作流调度器"},"instance":{"tags":["address"],"objectNameRef":"${address}"}}', 'NORMAL', '磁盘使用率(百分制)', '工作流执行器所在主机【${instanceName}】磁盘使用率过高，超过了${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 15:02:07', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(12, 12, '[{"metricName":"value","value":"集群不可用"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"工作流调度器-zk集群"},"instance":{"tags":["address"],"objectNameRef":"${zookeeperCluster}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '工作流调度器-zookeeper不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 13:59:33', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(13, 13, '[{"metricName":"value","value":"组件不可用"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"指标转发器"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '指标转发器不可用，消费者组【${consumerGroupId}】无活跃成员', 1, 3, NULL, 0, '2024-03-27 09:41:00', '2024-04-18 13:10:18', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(14, 14, '[{"metricName":"value","metricPql":"sample","metricLabel":"kafka_consumergroup_lag"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"指标转发器"},"instance":{"tags":["consumerGroupId"],"objectNameRef":"${consumerGroupId}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '指标转发器存在消费者【${consumerGroupId}】处理延时超过5分钟', 1, 3, NULL, 0, '2024-03-27 09:41:00', '2024-04-10 14:38:05', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(15, 15, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"主数据库"}}', 'NORMAL', '主数据库可用连接数', '主数据库可用连接数少于${threshold}', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 15:09:23', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(16, 16, '[{"metricName":"value","value":"告警处理延迟"}]', 120, 1, '{"objectMode":"TAG_MODE","tagMode":{"tags":[""],"objectNameRef":"告警处理延迟"}}', 'LIVENESS', '处理程序状态告警,无需设置阈值', '自监控告警处理异常：处理延迟超过5分钟', 1, 3, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 04:37:14', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(17, 17, '[{"metricName":"value","value":"服务不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'kafka集群【${objectName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 16:08:48', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(18, 18, '[{"metricName":"value","value":"节点不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"},"instance":{"tags":["server"],"objectNameRef":"${server}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'kafka集群【${objectName}】节点【${instanceName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 16:08:50', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(20, 20, '[{"metricName":"value","value":"分区不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"},"instance":{"tags":["topic","partition"],"objectNameRef":"${topic}-${partition}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'kafka集群【${objectName}】分区【${instanceName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 05:47:16', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(21, 21, '[{"metricName":"value","value":"0"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"executorName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '定时作业调度器【${objectName}】无可用节点', 1, 3, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 13:25:43', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(22, 22, '[{"metricName":"value","value":"分区副本不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"},"instance":{"tags":["topic","partition"],"objectNameRef":"${topic}-${partition}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'kafka集群【${objectName}】分区【${instanceName}】分区副本不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 04:39:32', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(24, 24, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"},"instance":{"tags":["topic"],"objectNameRef":"${topic}"}}', 'NORMAL', 'TOPIC大小单位为字节', 'TOPIC【${instanceName}】大小已超过阈值【${thresholdName}】', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 17:37:34', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(26, 26, '[{"metricName":"value","value":"集群不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"}}', 'NORMAL', '组件状态分三种green(0)/yellow(1)/red(2)', 'elasticsearch集群【${objectName}】${thresholdName}', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 17:47:23', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(27, 27, '[{"metricName":"value","value":"节点不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"},"instance":{"tags":["node"],"objectNameRef":"${node}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'elasticsearch【${objectName}】集群【${instanceName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 04:52:43', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(28, 28, '[{"metricName":"value","value":"集群不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'clickhouse集群【${dsName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 04:51:16', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(30, 30, '[{"metricName":"value","value":"节点不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"},"instance":{"tags":["serverNode"],"objectNameRef":"${serverNode}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'clickhouse集群【${objectName}】节点【${instanceName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-15 18:04:06', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(32, 32, '[{"metricName":"value","value":"集群不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"clusterName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '【${clusterName}】集群NameNode不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 14:46:27', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(34, 34, '[{"metricName":"value","value":"集群不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"clusterName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '【${clusterName}】集群ResourceManager不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 14:46:29', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(36, 36, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"clusterName"}}', 'NORMAL', 'CPU使用率(百分制)', '【${clusterName}】集群CPU资源已使用超过${threshold}', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 14:46:33', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(38, 38, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"clusterName"}}', 'NORMAL', '内存使用率(百分制)', '【${clusterName}】集群内存资源已使用超过${threshold}', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 14:46:35', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(40, 40, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"id","objectNameRef":"clusterName"},"instance":{"tags":["appName"],"objectNameRef":"${appName}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', '【${clusterName}】存在重复作业:【${appName}】', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 14:46:37', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(42, 42, '[{"metricName":"value","value":"集群不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'redis集群【${dsName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-09 18:34:03', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(45, 45, '[{"metricName":"value","value":"节点不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'VictoriaMetrics【${dsName}】不可用', 1, 3, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 05:15:47', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(47, 47, '[{"metricName":"value","value":"集群不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'nebula集群【${dsName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-16 06:20:43', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(48, 48, '[{"metricName":"value","value":"节点不可用"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"dsId","objectNameRef":"dsName"},"instance":{"tags":["address"],"objectNameRef":"${address}"}}', 'LIVENESS', '组件状态告警,无需设置阈值', 'nebula集群【${dsName}】${serviceType}节点【${instanceName}】不可用', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-16 06:20:45', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(50, 50, '[{"metricName":"value","value":"运行失败"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"processId","objectNameRef":"pipelineAlias"}}', 'LIVENESS', '任务状态告警,无需设置阈值', 'flink运行任务【${objectName}】运行失败', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-18 14:53:14', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(51, 51, '[{"metricName":"value","metricPql":"sample"}]', 60, 1, '{"objectMode":"MID_REF_MODE","midRefMode":{"objectRefMode":"FLINK_TASK"}}', 'NORMAL', '分钟重启次数阈值设置', 'flink运行任务【${objectName}】发生重启', 0, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-03 18:03:56', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(53, 53, '[{"metricName":"value","metricPql":"sample"}]', 300, 1, '{"objectMode":"MID_REF_MODE","midRefMode":{"objectRefMode":"FLINK_TASK"}}', 'NORMAL', '作业背压阈值设置', 'flink运行任务【${objectName}】作业背压超过了${threshold}', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 05:25:32', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(54, 54, '[{"metricName":"value","metricPql":"sum by(topic,consumergroup)(kafka_consumergroup_lag)"}]', 120, 1, '{"objectMode":"MID_REF_MODE","midRefMode":{"objectRefMode":"KAFKA_LAG"}}', 'KAFKA_LAG_TOLERANCE', '消费积压和生产速率相比较计算延时', '实时作业【${objectName}】消费积压超过${threshold}分钟', 1, 2, NULL, 0, '2024-03-27 10:00:25', '2024-04-16 07:44:23', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(56, 56, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"MID_REF_MODE","midRefMode":{"objectRefMode":"FLINK_TASK"}}', 'NORMAL', '每分钟fullGc次数', 'flink运行任务【${objectName}】fullGc频率超过了${threshold}次/分钟', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 16:07:02', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(57, 57, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"MID_REF_MODE","midRefMode":{"objectRefMode":"FLINK_TASK"}}', 'NORMAL', '两分钟内并行度的平均cpu使用率(百分制)', 'flink运行任务【${objectName}】cpu使用率超过了${threshold}', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-16 06:45:27', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(60, 60, '[{"metricName":"value","value":"任务运行失败"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"storageCkId","objectNameRef":"storageCkName"}}', 'LIVENESS', '任务状态告警,无需设置阈值', 'ck存储任务【${objectName}】运行失败', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 11:03:09', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(61, 61, '[{"metricName":"value","metricPql":"sum by(topic,consumergroup)(kafka_consumergroup_lag)"}]', 120, 1, '{"objectMode":"MID_REF_MODE","midRefMode":{"objectRefMode":"STORAGE_CK_CONSUMER"}}', 'KAFKA_LAG_TOLERANCE', '消费积压和生产速率相比较计算延时', 'ck存储作业【${objectName}】消费积压超过${threshold}分钟', 1, 2, NULL, 0, '2024-03-27 10:00:25', '2024-04-16 09:16:08', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(64, 64, '[{"metricName":"value","value":"运行失败"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"jobId","objectNameRef":"jobName"}}', 'LIVENESS', '任务状态告警,无需设置阈值', '定时任务【${objectName}】运行失败', 0, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 14:36:56', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(65, 65, '[{"metricName":"value","value":"运行失败"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"processDefinitionCode","objectNameRef":"processDefinitionName"}}', 'LIVENESS', '任务状态告警,无需设置阈值', '离线工作【${objectName}】运行失败', 0, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 15:08:43', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(66, 66, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"apiId"}}', 'NORMAL', '接口平均耗时(s)', '数据服务接口【${objectName}】平均耗时超时超过${threshold}s', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-07 10:32:56', NULL, NULL);
INSERT INTO tb_self_monitor_alarm_rule (id, item_id, base_metrics, monitor_interval, is_enabled, object_def, threshold_method, threshold_remark, content_template, auto_close, xxl_group_id, xxl_job_id, is_deleted, create_time, update_time, create_user, update_user) VALUES(68, 68, '[{"metricName":"value","metricPql":"sample"}]', 120, 1, '{"objectMode":"OBJECT_REF_MODE","objectRefMode":{"objectIdRef":"apiId"}}', 'NORMAL', '接口成功率(百分制)', '数据服务接口【${objectName}】成功率低于${threshold}%', 1, 2, NULL, 0, '2024-03-27 09:41:00', '2024-04-08 05:38:04', NULL, NULL);

INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2235881242690560, 1, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-01 17:48:01', '2024-04-09 06:11:42', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2235883462427648, 6, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"90","operator":">="}', 0, '2024-04-01 17:49:08', '2024-04-08 04:02:26', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2236353377076224, 5, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"90","operator":">="}', 0, '2024-04-01 21:48:09', '2024-04-08 04:02:06', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2250233759499264, 54, 'default', '00:00', '23:59', 'warning', '{"threshold":5}', 0, '2024-04-06 19:28:05', '2024-04-18 14:05:23', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252012746245120, 66, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"10","operator":">="}', 0, '2024-04-07 10:32:55', '2024-04-07 10:32:55', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252013910328320, 68, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"50","operator":"<="}', 0, '2024-04-07 10:33:30', '2024-04-16 07:06:29', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252017058317312, 65, 'default', '00:00', '23:59', 'warning', '{}', 0, '2024-04-07 10:35:07', '2024-04-07 10:35:07', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252017983980544, 60, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-07 10:35:35', '2024-04-07 10:35:35', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252054181020672, 4, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"90","operator":">="}', 0, '2024-04-07 10:53:59', '2024-04-08 04:01:56', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252055361684480, 7, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-07 10:54:35', '2024-04-07 10:54:35', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252056185078784, 8, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-07 10:55:01', '2024-04-07 10:55:01', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252338566530048, 61, 'default', '00:00', '23:59', 'warning', '{"threshold":5,"operator":">="}', 0, '2024-04-07 13:18:38', '2024-04-08 05:32:55', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252416110232576, 9, 'default', '00:00', '23:59', 'warning', '{"threshold":"95","operator":">="}', 0, '2024-04-07 13:58:05', '2024-04-07 13:58:05', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252417100809216, 10, 'default', '00:00', '23:59', 'warning', '{"threshold":"95","operator":">="}', 0, '2024-04-07 13:58:35', '2024-04-07 13:58:35', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252418032731136, 11, 'default', '00:00', '23:59', 'warning', '{"threshold":"95","operator":">="}', 0, '2024-04-07 13:59:03', '2024-04-07 13:59:03', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252418913469440, 12, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-07 13:59:30', '2024-04-07 13:59:30', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252420944430080, 15, 'default', '00:00', '23:59', 'warning', '{"threshold":"80","operator":"<="}', 0, '2024-04-07 14:00:32', '2024-04-07 14:00:46', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252492343608320, 64, 'default', '00:00', '23:59', 'warning', '{}', 0, '2024-04-07 14:36:51', '2024-04-07 14:36:51', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252669577167872, 56, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"5","operator":">="}', 0, '2024-04-07 16:07:00', '2024-04-07 16:07:13', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252671938462720, 53, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"0.9","operator":">="}', 0, '2024-04-07 16:08:12', '2024-04-07 16:08:12', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252920518837248, 30, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-07 18:14:38', '2024-04-07 18:14:38', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2252930576712704, 31, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-07 18:19:45', '2024-04-07 18:19:45', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254074083247104, 2, 'default', '00:00', '23:59', 'warning', '{"threshold":"90","operator":">="}', 0, '2024-04-08 04:01:22', '2024-04-08 17:29:26', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254074863191040, 3, 'default', '00:00', '23:59', 'warning', '{"threshold":"5","operator":">="}', 0, '2024-04-08 04:01:46', '2024-04-08 04:01:46', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254124553864192, 13, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 04:27:02', '2024-04-09 14:19:50', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254144205915136, 16, 'default', '00:00', '23:59', 'critical', '{"threshold":"0","operator":">="}', 0, '2024-04-08 04:37:02', '2024-04-08 04:37:02', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254146624357376, 18, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 04:38:16', '2024-04-08 04:38:16', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254147309274112, 20, 'default', '00:00', '23:59', 'warning', '{}', 0, '2024-04-08 04:38:37', '2024-04-08 04:38:37', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254149068129280, 22, 'default', '00:00', '23:59', 'ordinary', '{}', 0, '2024-04-08 04:39:30', '2024-04-08 04:39:30', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254154359505920, 24, '1.5Tb阈值', '00:00', '23:59', 'ordinary', '{"threshold":"1649267441664","operator":">="}', 0, '2024-04-08 04:42:12', '2024-04-08 04:44:26', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254156705334272, 24, '500Gb阈值', '00:00', '23:59', 'ordinary', '{"threshold":"536870912000","operator":">="}', 0, '2024-04-08 04:43:23', '2024-04-08 04:43:36', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254165852259328, 26, '集群不可用', '00:00', '23:59', 'critical', '{"threshold":"2","operator":">="}', 0, '2024-04-08 04:48:03', '2024-04-15 17:47:01', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254169642697728, 27, 'default', '00:00', '23:59', 'warning', '{}', 0, '2024-04-08 04:49:58', '2024-04-08 04:49:58', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254172160132096, 28, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 04:51:15', '2024-04-08 04:51:15', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254183399851008, 32, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 04:56:58', '2024-04-18 13:47:40', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254183862305792, 34, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 04:57:12', '2024-04-18 13:47:45', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254184848524288, 36, 'default', '00:00', '23:59', 'warning', '{"threshold":"90","operator":">="}', 0, '2024-04-08 04:57:42', '2024-04-08 04:57:42', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254185474622464, 38, 'default', '00:00', '23:59', 'warning', '{"threshold":"90","operator":">="}', 0, '2024-04-08 04:58:01', '2024-04-08 04:58:01', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254190536426496, 40, 'default', '00:00', '23:59', 'warning', '{}', 0, '2024-04-08 05:00:36', '2024-04-08 05:00:36', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254191277540352, 42, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 05:00:58', '2024-04-08 05:00:58', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254220351570944, 45, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 05:15:46', '2024-04-08 05:15:46', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254233555338240, 47, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 05:22:29', '2024-04-08 05:22:29', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254234035946496, 48, 'default', '00:00', '23:59', 'warning', '{}', 0, '2024-04-08 05:22:43', '2024-04-08 05:22:43', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254236957672448, 50, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-08 05:24:12', '2024-04-08 05:24:12', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2254251657200640, 51, 'default', '00:00', '23:59', 'warning', '{"threshold":"2","operator":">="}', 0, '2024-04-08 05:31:41', '2024-04-16 06:35:24', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2260492206310400, 14, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-10 10:25:48', '2024-04-10 14:38:10', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2275351892298752, 17, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-15 16:23:49', '2024-04-15 16:23:49', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2277040430091264, 57, 'default', '00:00', '23:59', 'ordinary', '{"threshold":"150","operator":">="}', 0, '2024-04-16 06:42:39', '2024-04-16 06:46:59', NULL, NULL);
INSERT INTO tb_self_monitor_threshold (id, rule_id, name, start_time, end_time, alarm_level, threshold, is_deleted, create_time, update_time, create_user, update_user) VALUES(2283549265200128, 21, 'default', '00:00', '23:59', 'critical', '{}', 0, '2024-04-18 13:53:13', '2024-04-18 13:53:13', NULL, NULL);

INSERT INTO tb_project_resource (id, project_id, resource_id, resource_type, privilege, is_owner, create_time, update_time, create_user, update_user) VALUES(811410000000048, 0, -1, 'self_monitor_object', '*', 0, '2023-10-23 14:20:36', '2023-10-23 14:20:39', NULL, NULL);

INSERT INTO xxl_job_info (job_group, job_cron, job_desc, add_time, update_time, author, alarm_email, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time, job_name, is_user_created) VALUES( 3, '35 0/5 * * * ?', 'kafka链路串联', '2024-03-27 20:23:20', '2024-03-28 18:27:57', NULL, NULL, 'RANDOM', 'syncTaskTopicChain', '-1', 'DISCARD_LATER', 120, 0, 'BEAN', '', '初始化代码', '2024-03-27 20:23:20', NULL, 1, 1712726910000, 1712726940000, 'kafka链路串联', 0);

INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2079820645860352', '文件列表', NULL, NULL, '/platform-management/cluster/management/files/:id', 'M1252', 'P005003001', '集群管理', NULL, '54', '46/53/54/', '0', '0', NULL, 'menu', '0', NULL, '0', '0', '2024-02-06 14:51:29', '2024-03-27 15:54:25', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2158620955085824', '框架详情', NULL, NULL, '/platform-management/opts/detail/:id', 'M1255', 'P005004001', '框架管理', NULL, '57', '46/56/57/', '0', '0', NULL, 'menu', '0', NULL, '0', '0', '2024-03-05 10:51:24', '2024-03-05 10:51:24', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2158670880179200', '查看定时作业', NULL, NULL, '/job-timing/job-management/detail/:id', 'M1256', 'P003003001', '作业管理', NULL, '1351768449942528', '32/1351636365411328/1351768449942528/', '0', '0', NULL, 'menu', '0', NULL, '0', '0', '2024-03-05 11:16:47', '2024-03-05 11:16:47', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2161316163685376', '查看弹性作业', NULL, NULL, '/flexible-application/detail/:id', 'M1257', 'P003002001', '弹性作业', NULL, '36', '32/35/36/', '0', '0', NULL, 'menu', '0', NULL, '0', '0', '2024-03-06 09:42:15', '2024-03-06 09:42:15', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2195368430699520', '运维排障', NULL, 'iconfont icon-hammer-screwdriver', NULL, 'M1271', 'P004004000', '运维排障', NULL, '37', '37/', '0', '0', NULL, 'menu', '1', NULL, '1', '0', '2024-03-18 10:22:08', '2024-03-27 10:00:33', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2195376427534336', '告警中心', NULL, 'iconfont icon-alarm-line1', '', 'M1272', 'P004004001', '告警中心', NULL, '2195368430699520', '37/2195368430699520/', '0', '0', NULL, 'menu', '2', NULL, '1', '0', '2024-03-18 10:26:12', '2024-04-08 18:00:10', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2195467065821184', '告警列表', NULL, NULL, '/trouble-shooting/alert-center/list', 'M1273', 'P004004001001', '告警列表', NULL, '2195376427534336', '37/2195368430699520/2195376427534336/', '0', '0', NULL, 'menu', '0', NULL, '1', '0', '2024-03-18 11:12:18', '2024-04-11 15:45:30', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2195471335393280', '规则管理', NULL, NULL, '/trouble-shooting/alert-center/rule', 'M1274', 'P004004001002', '规则管理', NULL, '2195376427534336', '37/2195368430699520/2195376427534336/', '0', '0', NULL, 'menu', '0', NULL, '1', '0', '2024-03-18 11:14:28', '2024-04-11 15:45:39', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2196005348082688', '日志', NULL, 'iconfont icon-description-line', '/trouble-shooting/log', 'M1275', 'P004004002', '日志', NULL, '2195368430699520', '37/2195368430699520/', '0', '0', NULL, 'menu', '3', NULL, '1', '0', '2024-03-18 15:46:05', '2024-04-08 18:01:20', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2198640813901824', '巡检仪表板', NULL, 'iconfont icon-monitor-dashboard', '/trouble-shooting/panel', 'M1276', 'P004004003', '巡检仪表板', NULL, '2195368430699520', '37/2195368430699520/', '0', '0', NULL, 'menu', '1', NULL, '1', '0', '2024-03-19 14:06:33', '2024-04-08 17:59:51', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2201404335818752', '健康检查', NULL, 'iconfont icon-operation-line', '/trouble-shooting/inspect', 'M1277', 'P004004004', '健康检查', NULL, '2195368430699520', '37/2195368430699520/', '0', '0', NULL, 'menu', '4', NULL, '1', '0', '2024-03-20 13:32:09', '2024-04-08 18:00:42', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('2207397452385280', '日志在线查看', NULL, NULL, '/trouble-shooting/log/view', 'M1278', 'P004004002001', '日志在线查看', NULL, '2196005348082688', '37/2195368430699520/2196005348082688/', '0', '0', NULL, 'menu', '0', NULL, '0', '0', '2024-03-22 16:20:24', '2024-04-11 15:56:47', NULL, NULL);


INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914304, 1765192536458240, 'P004004001001');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914306, 1765192536458240, 'P004004000');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914307, 1765192536458240, 'P004004001');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914308, 1765192536458240, 'P004004001002');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914309, 1765192536458240, 'P004004002001');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914310, 1765192536458240, 'P004004002');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914312, 1765192536458240, 'P004004003');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263978850914313, 1765192536458240, 'P004004004');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263984487239680, 1765195138499584, 'P004004001');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263984487239682, 1765195138499584, 'P004004000');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES (2263984487239683, 1765195138499584, 'P004004001001');

-- 修改对象分类表，增加type字段
ALTER TABLE `tb_object_category`
    ADD COLUMN `type`  varchar(255) NOT NULL COMMENT '类型' AFTER `code`;
update tb_object_category set type='basic';

ALTER TABLE `tb_metric_set`
    ADD COLUMN `category_id`  bigint(20) NULL COMMENT '对象分类id' AFTER `obj_id`;

ALTER TABLE `tb_metric_set`
    MODIFY COLUMN `obj_id`  bigint(20) NULL COMMENT '对象模型id' AFTER `description`;

CREATE TABLE `tb_zabbix_extract_templates` (
  `rule_id` bigint(20) NOT NULL COMMENT 'id',
  `template` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板字符串',
  `tag_rule` varchar(1024) COLLATE utf8mb4_unicode_ci NULL COMMENT '提取tags规则',
  KEY `idx_zabbix_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='zabbix模板表';

CREATE TABLE `tb_zabbix_extract_rule` (
  `id` bigint(20) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则名称',
  `metric_source_id` bigint(20) NOT NULL COMMENT '指标来源id',
  `delimiters` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分隔符',
  `ds_scan` tinyint(1) NOT NULL COMMENT '是否使用zabbix数据源扫描',
  `ds_id` bigint(20) NULL COMMENT '扫描的zabbix数据库地址',
  `replace_pattern` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '速配替换',
  `item_scan_sql` text NULL COLLATE utf8mb4_unicode_ci COMMENT 'item扫描替换',
  `is_deleted` tinyint(1) NOT NULL COMMENT '逻辑删除标志位',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='zabbix指标模板提取规则';

alter table tb_import_history add column schema_config MEDIUMTEXT NULL comment 'excelSchema' AFTER `failed_row`;


update tb_permission set icon = 'iconfont icon-datasafe-quality' where id = 1335764428817408;
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2348953349097472, '数据资产', NULL, 'iconfont icon-data-line', NULL, 'M1290', 'P007000000', '数据资产', NULL, '0', '', '0', '0', NULL, 'menu', '51', NULL, '1', '0', '2024-05-11 16:19:27', '2024-05-11 16:19:27', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2354662124651520, '资产目录', NULL, 'iconfont icon-a-datalistbeifen2', NULL, 'M1291', 'P007001000', '资产目录', NULL, '2348953349097472', '2348953349097472/', '0', '0', NULL, 'menu', '0', NULL, '1', '0', '2024-05-13 16:43:05', '2024-05-16 16:33:43', NULL, NULL);
INSERT INTO `tb_permission` (`id`, `name`, `http_method`, `icon`, `uri`, `permission`, `code`, `code_name`, `specific_code`, `parent_id`, `parent_path`, `is_api`, `is_resource`, `resource_type`, `type`, `sort`, `description`, `is_display`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES (2354667197793280, '对象资产', NULL, 'iconfont icon-object-fill', '/data-property/object/management', 'M1292', 'P007001001', '对象资产', NULL, '2354662124651520', '2348953349097472/2354662124651520/', '0', '0', NULL, 'menu', '0', NULL, '1', '0', '2024-05-13 16:45:40', '2024-05-16 16:35:43', NULL, NULL);
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2354669750325248', '1765192536458240', 'P007000000');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2354669750325249', '1765192536458240', 'P007001000');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2354669750325250', '1765192536458240', 'P007001001');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2365668726575104', '1765193990341632', 'P007000000');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2365668726575105', '1765193990341632', 'P007001000');
INSERT INTO `tb_role_permission` (`id`, `role_id`, `permission_code`) VALUES ('2365668726575106', '1765193990341632', 'P007001001');

INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('18', '对象实例CK宽表配置', 'cmdb对象实例Clickhouse宽表配置信息，配置后才可正常使用对象资产相关功能', '维度建模', 'jax.modeling.object.instance.wide.table', 'CLICKHOUSE', NULL, '[{\"requireCondition\":\"\",\"defaultValue\":\"default.ods_cmdb_object\",\"description\":\"cmdb对象实例Clickhouse中间表\",\"range\":\"\",\"optional\":false,\"label\":\"cmdb对象实例CK中间表\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"cmdbOdsTable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"default.ods_cmdb_object_relation\",\"description\":\"cmdb对象实例关系Clickhouse中间表\",\"range\":\"\",\"optional\":false,\"label\":\"cmdb对象实例关系CK中间表\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"cmdbOdsRelationTable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"default.ods_cmdb_excel_import\",\"description\":\"excel实例导入中间表\",\"range\":\"\",\"optional\":false,\"label\":\"excel实例导入中间表\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"cmdbExcelMiddleTable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"default.dwd_cmdb_object_all\",\"description\":\"cmdb对象实例Clickhouse宽表\",\"range\":\"\",\"optional\":false,\"label\":\"cmdb对象实例CK宽表\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"cmdbWideTable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"default.dwd_cmdb_object_relation_all\",\"description\":\"cmdb对象实例关系宽表\",\"range\":\"\",\"optional\":false,\"label\":\"cmdb对象实例关系CK宽表\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"cmdbRelationWideTable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"\",\"description\":\"使用的分区, yyyyMMddHHmmss格式\",\"range\":\"\",\"optional\":true,\"label\":\"使用的分区\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"usedPartition\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"default.dwd_cmdb_object_import_history\",\"description\":\"cmdb对象实例导入日志表，用与记录导入日志\",\"range\":\"\",\"optional\":false,\"label\":\"cmdb对象实例导入日志表\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"importLogTable\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', NULL, '0', '2024-05-16 21:59:43', '2024-05-16 14:49:45', NULL, NULL);
INSERT INTO `tb_system_config` (`id`, `name`, `description`, `use_module`, `prefix_key`, `platform`, `ds_id`, `setting_schema`, `setting`, `is_deleted`, `create_time`, `update_time`, `create_user`, `update_user`) VALUES ('19', '对象实例Nebula配置', 'cmdb对象实例Nebula配置信息', '维度建模', 'jax.modeling.object.instance.nebula', 'NEBULA', NULL, '[{\"requireCondition\":\"\",\"defaultValue\":\"cmdb\",\"description\":\"对象模型Nebula图空间\",\"range\":\"\",\"optional\":false,\"label\":\"对象模型Nebula图空间\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"baseSpace\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"cmdb\",\"description\":\"对象模型Nebula镜像图空间，该空间为对象模型Nebula图空间镜像空间，用于存储对象模型的实际数据\",\"range\":\"\",\"optional\":true,\"label\":\"对象模型Nebula镜像图空间\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"imageSpace\",\"inputType\":\"TEXT\",\"placeholder\":\"\"},{\"requireCondition\":\"\",\"defaultValue\":\"cmdb_object_relation\",\"description\":\"cmdb对象实例关系Edge名称\",\"range\":\"\",\"optional\":false,\"label\":\"cmdb对象实例关系Edge名称\",\"type\":[\"STRING\"],\"recommendations\":[],\"availableCondition\":\"\",\"candidates\":[],\"regex\":\"\",\"apiVersion\":2,\"scope\":\"\",\"name\":\"relationEdge\",\"inputType\":\"TEXT\",\"placeholder\":\"\"}]', NULL, '0', '2024-04-07 21:59:43', '2024-05-16 17:00:44', NULL, NULL);
