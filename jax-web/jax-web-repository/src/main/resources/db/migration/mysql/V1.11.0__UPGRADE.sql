ALTER TABLE tb_project
    ADD COLUMN cpu  int(4) NULL AFTER description,
    ADD COLUMN memory  int(11) NULL AFTER cpu;
ALTER TABLE tb_project
    ADD COLUMN enable_resource_limit  int(1)  not null default 0  AFTER description;


-- 删除默认共享框架
DELETE FROM tb_project_resource WHERE resource_type = 'tb_opts' AND project_id = 0 AND resource_id IN (1,2,3,4,5,6,7,8,9,10,11,12);

INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2617194784687104, 'windows事件日志同步', NULL, NULL, '/data-dev/dev/data-ingestion/online/win-event-log', 'M1265', 'win_event_log', 'windows事件日志同步', NULL, 1309151048500224, '32/33/34/1309172713260032/1309151048500224/', 0, 0, NULL, 'menu', 0, NULL, 0, 0, '2024-08-14 10:14:06', '2024-08-14 11:00:30', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2617217695351808, '编辑windows事件日志同步', NULL, NULL, '/data-dev/dev/data-ingestion/online/win-event-log/:id', 'M1266', 'win_event_log', 'windows事件日志同步', NULL, 2617194784687104, '32/33/34/1309172713260032/1309151048500224/2617194784687104/', 0, 0, NULL, 'menu', 0, NULL, 1, 0, '2024-08-14 10:25:45', '2024-08-14 10:41:23', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2617222404572160, '克隆windows事件日志同步', NULL, NULL, '/data-dev/dev/data-ingestion/online/win-event-log/:cloneId', 'M1267', 'win_event_log', 'windows事件日志同步', NULL, 2617194784687104, '32/33/34/1309172713260032/1309151048500224/2617194784687104/', 0, 0, NULL, 'menu', 0, NULL, 0, 0, '2024-08-14 10:28:09', '2024-08-14 10:41:27', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2617279231427584, '发布|上下线', NULL, NULL, NULL, 'B052', 'data_develop:deploy', '发布|上下线', NULL, 34, '32/33/34/', 0, 0, NULL, 'button', 0, NULL, 1, 0, '2024-08-14 10:57:03', '2024-08-14 10:57:03', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2622902153118720, '数据集成发布', NULL, NULL, NULL, 'R060', 'data_develop:deploy', '发布', NULL, 1309151048500224, '32/33/34/1309172713260032/1309151048500224/', 0, 1, 'tb_ingestion', 'resource', 1, NULL, 0, 0, '2024-08-16 10:37:01', '2024-08-16 10:37:01', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2622902153456720, '数据处理发布', NULL, NULL, NULL, 'R061', 'data_develop:deploy', '发布', NULL, 1309188023813120, '32/33/34/1309172713260032/1309188023813120/', 0, 1, 'tb_process', 'resource', 1, NULL, 0, 0, '2024-08-16 13:16:29', '2024-08-16 13:16:29', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2622911504122880, 'CK存储发布', NULL, NULL, NULL, 'R062', 'data_develop:deploy', '发布', NULL, 1309195853169664, '32/33/34/1309172713260032/1309188023813120/1309195853169664/', 0, 1, 'tb_storage_ck', 'resource', 1, NULL, 0, 0, '2024-08-16 10:41:47', '2024-08-16 10:41:47', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2622918282576896, 'Es存储发布', NULL, NULL, NULL, 'R063', 'data_develop:deploy', '发布', NULL, 1309197317309440, '32/33/34/1309172713260032/1309188023813120/1309197317309440/', 0, 1, 'tb_storage_es', 'resource', 1, NULL, 0, 0, '2024-08-16 10:45:14', '2024-08-16 10:45:14', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2622926869627904, 'Hive存储发布', NULL, NULL, NULL, 'R064', 'data_develop:deploy', '发布', NULL, 1309198905672704, '32/33/34/1309172713260032/1309188023813120/1309198905672704/', 0, 1, 'tb_storage_hive', 'resource', 1, NULL, 0, 0, '2024-08-16 10:49:36', '2024-08-16 10:49:36', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2622942582342656, '工作流发布|上线', NULL, NULL, NULL, 'R065', 'data_develop:deploy', '发布|下线', NULL, 1751835732214784, '32/33/34/1382903756915712/1382907679835136/1751835732214784/', 0, 1, 't_ds_process_definition', 'resource', 1, NULL, 0, 0, '2024-08-16 10:57:35', '2024-08-16 10:57:35', NULL, NULL);
INSERT INTO tb_permission (id, name, http_method, icon, uri, permission, code, code_name, specific_code, parent_id, parent_path, is_api, is_resource, resource_type, type, sort, description, is_display, is_deleted, create_time, update_time, create_user, update_user) VALUES (2622984500872192, '离线数据处理发布', NULL, NULL, NULL, 'R066', 'data_develop:deploy', '发布', NULL, 1383531504600064, '32/33/34/1382903756915712/1382907679835136/1383531504600064/', 0, 1, 'tb_process_batch', 'resource', 1, NULL, 0, 0, '2024-08-16 11:18:54', '2024-08-16 11:20:49', NULL, NULL);
UPDATE tb_permission SET name = '创建数据集成', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/create/:businessFlowId', permission = 'M1046', code = 'real_time_data_ingestion', code_name = '数据集成', specific_code = NULL, parent_id = 1309172713260032, parent_path = '32/33/34/1309172713260032/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:58:39', create_user = NULL, update_user = NULL WHERE id = 1309151048500224;
UPDATE tb_permission SET name = '文件同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/file', permission = 'M1047', code = 'file', code_name = '文件同步', specific_code = 'rtdatacollection', parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 13:45:04', create_user = NULL, update_user = NULL WHERE id = 1309154588591104;
UPDATE tb_permission SET name = 'TCP/UDP同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/tcp-udp', permission = 'M1048', code = 'tcp_udp', code_name = 'TCP/UDP同步', specific_code = 'rtdatacollection', parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:59:01', create_user = NULL, update_user = NULL WHERE id = 1309156844536832;
UPDATE tb_permission SET name = 'Kafka接入', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/kafka', permission = 'M1049', code = 'kafka', code_name = 'Kafka接入', specific_code = 'rtdatacollection', parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:59:30', create_user = NULL, update_user = NULL WHERE id = 1309160059470848;
UPDATE tb_permission SET name = 'SysLog同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/syslog', permission = 'M1050', code = 'syslog', code_name = 'SysLog同步', specific_code = 'rtdatacollection', parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:59:37', create_user = NULL, update_user = NULL WHERE id = 1309161616180224;
UPDATE tb_permission SET name = '实时开发', http_method = NULL, icon = NULL, uri = NULL, permission = 'M1051', code = 'realtime', code_name = '实时开发', specific_code = NULL, parent_id = 34, parent_path = '32/33/34/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:58:19', create_user = NULL, update_user = NULL WHERE id = 1309172713260032;
UPDATE tb_permission SET name = '编辑文件同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/file/:id', permission = 'M1052', code = 'file', code_name = '文件同步', specific_code = NULL, parent_id = 1309154588591104, parent_path = '32/33/34/1309172713260032/1309151048500224/1309154588591104/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:39:02', create_user = NULL, update_user = NULL WHERE id = 1309180212020224;
UPDATE tb_permission SET name = '编辑TCP/UDP同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/tcp-udp/:id', permission = 'M1053', code = 'tcp_udp', code_name = 'TCP/UDP同步', specific_code = NULL, parent_id = 1309156844536832, parent_path = '32/33/34/1309172713260032/1309151048500224/1309156844536832/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:39:24', create_user = NULL, update_user = NULL WHERE id = 1309181667247104;
UPDATE tb_permission SET name = '编辑kafka同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/kafka/:id', permission = 'M1054', code = 'kafka', code_name = 'Kafka接入', specific_code = NULL, parent_id = 1309160059470848, parent_path = '32/33/34/1309172713260032/1309151048500224/1309160059470848/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:39:43', create_user = NULL, update_user = NULL WHERE id = 1309183780422656;
UPDATE tb_permission SET name = '编辑Syslog同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/syslog/:id', permission = 'M1055', code = 'syslog', code_name = 'SysLog同步', specific_code = NULL, parent_id = 1309161616180224, parent_path = '32/33/34/1309172713260032/1309151048500224/1309161616180224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:40:03', create_user = NULL, update_user = NULL WHERE id = 1309185103660032;
UPDATE tb_permission SET name = '创建数据处理', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/index', permission = 'M1056', code = 'real_time_data_processing', code_name = '数据处理', specific_code = NULL, parent_id = 1309172713260032, parent_path = '32/33/34/1309172713260032/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:00:42', create_user = NULL, update_user = NULL WHERE id = 1309188023813120;
UPDATE tb_permission SET name = '创建管线作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/pipeline/create/:businessFlowId', permission = 'M1057', code = 'streaming', code_name = '管线作业', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:00:49', create_user = NULL, update_user = NULL WHERE id = 1309189665588224;
UPDATE tb_permission SET name = '创建SQL作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/flink-sql/create/:businessFlowId', permission = 'M1058', code = 'flink_sql', code_name = 'SQL作业', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:00:58', create_user = NULL, update_user = NULL WHERE id = 1309191154205696;
UPDATE tb_permission SET name = '创建Clickhouse存储作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/storage/ck', permission = 'M1059', code = 'storage_ck', code_name = 'Clickhouse存储作业', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:01:09', create_user = NULL, update_user = NULL WHERE id = 1309195853169664;
UPDATE tb_permission SET name = '创建ElasticSearch存储作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/storage/es', permission = 'M1060', code = 'storage_es', code_name = 'ElasticSearch存储作业', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:01:16', create_user = NULL, update_user = NULL WHERE id = 1309197317309440;
UPDATE tb_permission SET name = '创建Hive存储作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/hive/create/:businessFlowId', permission = 'M1061', code = 'storage_hive', code_name = 'Hive存储作业', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:01:26', create_user = NULL, update_user = NULL WHERE id = 1309198905672704;
UPDATE tb_permission SET name = '编辑管线作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/pipeline/edit/:id', permission = 'M1062', code = 'streaming', code_name = '管线作业', specific_code = NULL, parent_id = 1309189665588224, parent_path = '32/33/34/1309172713260032/1309188023813120/1309189665588224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:41:54', create_user = NULL, update_user = NULL WHERE id = 1309201079305216;
UPDATE tb_permission SET name = '克隆管线作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/pipeline/clone/:cloneId', permission = 'M1063', code = 'streaming', code_name = '管线作业', specific_code = NULL, parent_id = 1309189665588224, parent_path = '32/33/34/1309172713260032/1309188023813120/1309189665588224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:41:58', create_user = NULL, update_user = NULL WHERE id = 1309202820432896;
UPDATE tb_permission SET name = '编辑SQL作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/flink-sql/edit/:id', permission = 'M1064', code = 'flink_sql', code_name = 'SQL作业', specific_code = NULL, parent_id = 1309191154205696, parent_path = '32/33/34/1309172713260032/1309188023813120/1309191154205696/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:42:15', create_user = NULL, update_user = NULL WHERE id = 1309204136330240;
UPDATE tb_permission SET name = '克隆SQL作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/flink-sql/clone/:cloneId', permission = 'M1065', code = 'flink_sql', code_name = 'SQL作业', specific_code = NULL, parent_id = 1309191154205696, parent_path = '32/33/34/1309172713260032/1309188023813120/1309191154205696/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:42:20', create_user = NULL, update_user = NULL WHERE id = 1309205502428160;
UPDATE tb_permission SET name = '编辑Hive存储作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/hive/edit/:id', permission = 'M1066', code = 'storage_hive', code_name = 'Hive存储作业', specific_code = NULL, parent_id = 1309198905672704, parent_path = '32/33/34/1309172713260032/1309188023813120/1309198905672704/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:43:59', create_user = NULL, update_user = NULL WHERE id = 1309208037098496;
UPDATE tb_permission SET name = '克隆Hive存储作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/hive/clone/:cloneId', permission = 'M1067', code = 'storage_hive', code_name = 'Hive存储作业', specific_code = NULL, parent_id = 1309198905672704, parent_path = '32/33/34/1309172713260032/1309188023813120/1309198905672704/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:44:04', create_user = NULL, update_user = NULL WHERE id = 1309209121391616;
UPDATE tb_permission SET name = '编辑Clickhouse存储作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/storage/ck/:id', permission = 'M1068', code = 'storage_ck', code_name = 'Clickhouse存储作业', specific_code = NULL, parent_id = 1309195853169664, parent_path = '32/33/34/1309172713260032/1309188023813120/1309195853169664/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:42:33', create_user = NULL, update_user = NULL WHERE id = 1309211389101056;
UPDATE tb_permission SET name = '编辑ElasticSearch存储作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/storage/es/:id', permission = 'M1069', code = 'storage_es', code_name = 'ElasticSearch存储作业', specific_code = NULL, parent_id = 1309197317309440, parent_path = '32/33/34/1309172713260032/1309188023813120/1309197317309440/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:42:58', create_user = NULL, update_user = NULL WHERE id = 1309222309266432;
UPDATE tb_permission SET name = '维度建模', http_method = NULL, icon = NULL, uri = NULL, permission = 'M1102', code = 'P001003000', code_name = '维度建模', specific_code = NULL, parent_id = 34, parent_path = '32/33/34/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-19 13:27:14', create_user = NULL, update_user = 20200321 WHERE id = 1323941644895232;
UPDATE tb_permission SET name = 'Kafka', http_method = NULL, icon = NULL, uri = '/data-dev/dev/management/kafka/:id', permission = 'M1103', code = 'P001003000', code_name = '维度建模', specific_code = NULL, parent_id = 1323941644895232, parent_path = '32/33/34/1323941644895232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-19 13:21:47', create_user = NULL, update_user = 20200321 WHERE id = 1323941644895233;
UPDATE tb_permission SET name = 'Hive', http_method = NULL, icon = NULL, uri = '/data-dev/dev/management/hive/:id', permission = 'M1104', code = 'P001003000', code_name = '维度建模', specific_code = NULL, parent_id = 1323941644895232, parent_path = '32/33/34/1323941644895232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-19 13:22:13', create_user = NULL, update_user = 20200321 WHERE id = 1323947443717120;
UPDATE tb_permission SET name = 'ElasticSearch', http_method = NULL, icon = NULL, uri = '/data-dev/dev/management/es/:id', permission = 'M1105', code = 'P001003000', code_name = '维度建模', specific_code = NULL, parent_id = 1323941644895232, parent_path = '32/33/34/1323941644895232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-19 13:22:18', create_user = NULL, update_user = 20200321 WHERE id = 1323948567725056;
UPDATE tb_permission SET name = 'Clickhouse', http_method = NULL, icon = NULL, uri = '/data-dev/dev/management/ck/:id', permission = 'M1106', code = 'P001003000', code_name = '维度建模', specific_code = NULL, parent_id = 1323941644895232, parent_path = '32/33/34/1323941644895232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-19 13:22:23', create_user = NULL, update_user = 20200321 WHERE id = 1323949723321344;
UPDATE tb_permission SET name = 'Mysql', http_method = NULL, icon = NULL, uri = '/data-dev/dev/management/mysql/:id', permission = 'M1107', code = 'P001003000', code_name = '维度建模', specific_code = NULL, parent_id = 1323941644895232, parent_path = '32/33/34/1323941644895232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-19 13:22:28', create_user = NULL, update_user = 20200321 WHERE id = 1323950867547136;
UPDATE tb_permission SET name = '离线开发', http_method = NULL, icon = NULL, uri = NULL, permission = 'M1143', code = 'offline', code_name = '离线开发', specific_code = NULL, parent_id = 34, parent_path = '32/33/34/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:03:15', create_user = NULL, update_user = NULL WHERE id = 1382903756915712;
UPDATE tb_permission SET name = '创建数据处理', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/offline/index', permission = 'M1144', code = 'offline_data_processing', code_name = '数据处理', specific_code = NULL, parent_id = 1382903756915712, parent_path = '32/33/34/1382903756915712/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:03:10', create_user = NULL, update_user = NULL WHERE id = 1382907679835136;
UPDATE tb_permission SET name = '数据集成', http_method = NULL, icon = NULL, uri = NULL, permission = 'M1145', code = 'offline_data_ingestion', code_name = '数据集成', specific_code = NULL, parent_id = 1382903756915712, parent_path = '32/33/34/1382903756915712/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:03:48', create_user = NULL, update_user = NULL WHERE id = 1383529044149248;
UPDATE tb_permission SET name = '创建管线作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/batch-process/create', permission = 'M1147', code = 'batch', code_name = '管线作业', specific_code = NULL, parent_id = 1382907679835136, parent_path = '32/33/34/1382903756915712/1382907679835136/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 11:03:32', create_user = NULL, update_user = NULL WHERE id = 1383531504600064;
UPDATE tb_permission SET name = '编辑管线作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/batch-process/edit/:id', permission = 'M1148', code = 'batch', code_name = '管线作业', specific_code = NULL, parent_id = 1383531504600064, parent_path = '32/33/34/1382903756915712/1382907679835136/1383531504600064/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:45:21', create_user = NULL, update_user = NULL WHERE id = 1383532695520256;
UPDATE tb_permission SET name = '克隆管线作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/batch-process/clone/:cloneId', permission = 'M1149', code = 'batch', code_name = '管线作业', specific_code = NULL, parent_id = 1383531504600064, parent_path = '32/33/34/1382903756915712/1382907679835136/1383531504600064/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-06-01 00:00:00', update_time = '2024-08-14 10:45:26', create_user = NULL, update_user = NULL WHERE id = 1383533697795072;
UPDATE tb_permission SET name = '克隆文件同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/clone/file/:cloneId', permission = 'M1162', code = 'file', code_name = '文件同步', specific_code = NULL, parent_id = 1309154588591104, parent_path = '32/33/34/1309172713260032/1309151048500224/1309154588591104/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-08-01 00:00:00', update_time = '2024-08-14 10:39:07', create_user = NULL, update_user = NULL WHERE id = 1522171449934848;
UPDATE tb_permission SET name = '克隆TCP', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/clone/tcp-udp/:cloneId', permission = 'M1163', code = 'tcp_udp', code_name = 'TCP/UDP同步', specific_code = NULL, parent_id = 1309156844536832, parent_path = '32/33/34/1309172713260032/1309151048500224/1309156844536832/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-08-01 00:00:00', update_time = '2024-08-14 10:39:30', create_user = NULL, update_user = NULL WHERE id = 1522181958370304;
UPDATE tb_permission SET name = '克隆kafka', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/clone/kafka/:cloneId', permission = 'M1164', code = 'kafka', code_name = 'Kafka接入', specific_code = NULL, parent_id = 1309160059470848, parent_path = '32/33/34/1309172713260032/1309151048500224/1309160059470848/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-08-01 00:00:00', update_time = '2024-08-14 10:39:50', create_user = NULL, update_user = NULL WHERE id = 1522183978615808;
UPDATE tb_permission SET name = '克隆SysLog', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/clone/syslog/:cloneId', permission = 'M1165', code = 'syslog', code_name = 'SysLog同步', specific_code = NULL, parent_id = 1309161616180224, parent_path = '32/33/34/1309172713260032/1309151048500224/1309161616180224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-08-01 00:00:00', update_time = '2024-08-14 10:40:12', create_user = NULL, update_user = NULL WHERE id = 1522185899475968;
UPDATE tb_permission SET name = '克隆CK作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/storage/clone/ck/:cloneId', permission = 'M1175', code = 'storage_ck', code_name = 'Clickhouse存储作业', specific_code = NULL, parent_id = 1309195853169664, parent_path = '32/33/34/1309172713260032/1309188023813120/1309195853169664/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-08-01 00:00:00', update_time = '2024-08-14 10:42:39', create_user = NULL, update_user = NULL WHERE id = 1527195427013632;
UPDATE tb_permission SET name = '克隆ES作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/storage/clone/es/:cloneId', permission = 'M1176', code = 'storage_es', code_name = 'ElasticSearch存储作业', specific_code = NULL, parent_id = 1309197317309440, parent_path = '32/33/34/1309172713260032/1309188023813120/1309197317309440/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-08-01 00:00:00', update_time = '2024-08-14 10:43:05', create_user = NULL, update_user = NULL WHERE id = 1527199354127360;
UPDATE tb_permission SET name = 'Nebula', http_method = NULL, icon = NULL, uri = '/data-dev/dev/management/nebula/:id', permission = 'M1186', code = 'P001003000', code_name = '维度建模', specific_code = NULL, parent_id = 1323941644895232, parent_path = '32/33/34/1323941644895232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-08-01 00:00:00', update_time = '2024-08-19 13:22:32', create_user = NULL, update_user = 20200321 WHERE id = 1624725425816576;
UPDATE tb_permission SET name = '创建工作流', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/bpm-process/create', permission = 'M1211', code = 'offline_workflow', code_name = '工作流', specific_code = NULL, parent_id = 1382907679835136, parent_path = '32/33/34/1382903756915712/1382907679835136/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 11:03:40', create_user = NULL, update_user = NULL WHERE id = 1751835732214784;
UPDATE tb_permission SET name = '编辑工作流', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/bpm-process/edit/:id', permission = 'M1212', code = 'offline_workflow', code_name = '数据开发', specific_code = NULL, parent_id = 1751835732214784, parent_path = '32/33/34/1382903756915712/1382907679835136/1751835732214784/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 10:46:14', create_user = NULL, update_user = NULL WHERE id = 1751838661379072;
UPDATE tb_permission SET name = '克隆工作流', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/bpm-process/clone/:cloneId', permission = 'M1213', code = 'offline_workflow', code_name = '数据开发', specific_code = NULL, parent_id = 1751835732214784, parent_path = '32/33/34/1382903756915712/1382907679835136/1751835732214784/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 10:46:19', create_user = NULL, update_user = NULL WHERE id = 1751841355793408;
UPDATE tb_permission SET name = '创建|编辑|删除|调试|试跑', http_method = NULL, icon = NULL, uri = NULL, permission = 'B027', code = 'data_develop:write', code_name = '创建|编辑|删除|调试|试跑', specific_code = NULL, parent_id = 34, parent_path = '32/33/34/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'button', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 10:56:05', create_user = NULL, update_user = NULL WHERE id = 1762277803492352;
UPDATE tb_permission SET name = '归档文件同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/archive', permission = 'M1214', code = 'archive', code_name = '归档文件同步', specific_code = 'rtdatacollection', parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 10:59:59', create_user = NULL, update_user = NULL WHERE id = 1765748629603328;
UPDATE tb_permission SET name = '克隆归档文件采集', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/clone/archive/:cloneId', permission = 'M1215', code = 'archive', code_name = '归档文件同步', specific_code = NULL, parent_id = 1765748629603328, parent_path = '32/33/34/1309172713260032/1309151048500224/1765748629603328/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 10:40:25', create_user = NULL, update_user = NULL WHERE id = 1765751129048064;
UPDATE tb_permission SET name = '编辑归档文件采集', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/archive/:id', permission = 'M1216', code = 'archive', code_name = '归档文件同步', specific_code = NULL, parent_id = 1765748629603328, parent_path = '32/33/34/1309172713260032/1309151048500224/1765748629603328/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 10:40:29', create_user = NULL, update_user = NULL WHERE id = 1765753300812800;
UPDATE tb_permission SET name = '数据集成编辑|删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R029', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 1, resource_type = 'tb_ingestion', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-16 10:37:23', create_user = NULL, update_user = NULL WHERE id = 1802266411140096;
UPDATE tb_permission SET name = '数据集成启动|停止', http_method = NULL, icon = NULL, uri = NULL, permission = 'R030', code = 'collection_job:operate', code_name = '启动|停止', specific_code = NULL, parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 1, resource_type = 'tb_ingestion', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 10:01:39', create_user = NULL, update_user = NULL WHERE id = 1802269544842240;
UPDATE tb_permission SET name = '数据处理编辑|删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R031', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 1, resource_type = 'tb_process', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-16 11:19:29', create_user = NULL, update_user = NULL WHERE id = 1802271072781312;
UPDATE tb_permission SET name = '数据处理启动|停止', http_method = NULL, icon = NULL, uri = NULL, permission = 'R032', code = 'pipeline_streaming:operate', code_name = '启动|停止', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 1, resource_type = 'tb_process', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-14 09:56:59', create_user = NULL, update_user = NULL WHERE id = 1802272769344512;
UPDATE tb_permission SET name = 'CK存储编辑|删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R033', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1309195853169664, parent_path = '32/33/34/1309172713260032/1309188023813120/1309195853169664/', is_api = 0, is_resource = 1, resource_type = 'tb_storage_ck', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-16 10:42:00', create_user = NULL, update_user = NULL WHERE id = 1802304724141056;
UPDATE tb_permission SET name = 'CK存储启动|停止', http_method = NULL, icon = NULL, uri = NULL, permission = 'R034', code = 'storage_job:operate', code_name = '启动|停止', specific_code = NULL, parent_id = 1309195853169664, parent_path = '32/33/34/1309172713260032/2612057153274880/1309195853169664/', is_api = 0, is_resource = 1, resource_type = 'tb_storage_ck', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-12 15:31:44', create_user = NULL, update_user = NULL WHERE id = 1802306395243520;
UPDATE tb_permission SET name = 'Es存储编辑|删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R035', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1309197317309440, parent_path = '32/33/34/1309172713260032/1309188023813120/1309197317309440/', is_api = 0, is_resource = 1, resource_type = 'tb_storage_es', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-16 10:45:29', create_user = NULL, update_user = NULL WHERE id = 1802309216896000;
UPDATE tb_permission SET name = 'Es存储启动|停止', http_method = NULL, icon = NULL, uri = NULL, permission = 'R036', code = 'pipeline_streaming:operate', code_name = '启动|停止', specific_code = NULL, parent_id = 1309197317309440, parent_path = '32/33/34/1309172713260032/2612057153274880/1309197317309440/', is_api = 0, is_resource = 1, resource_type = 'tb_storage_es', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-12 15:36:19', create_user = NULL, update_user = NULL WHERE id = 1802310875448320;
UPDATE tb_permission SET name = 'Hive存储编辑|删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R037', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1309198905672704, parent_path = '32/33/34/1309172713260032/1309188023813120/1309198905672704/', is_api = 0, is_resource = 1, resource_type = 'tb_storage_hive', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-16 10:49:51', create_user = NULL, update_user = NULL WHERE id = 1802312889828352;
UPDATE tb_permission SET name = 'Hive存储启动|停止', http_method = NULL, icon = NULL, uri = NULL, permission = 'R038', code = 'pipeline_streaming:operate', code_name = '启动|停止', specific_code = NULL, parent_id = 1309198905672704, parent_path = '32/33/34/1309172713260032/2612057153274880/1309198905672704/', is_api = 0, is_resource = 1, resource_type = 'tb_storage_hive', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-11-01 00:00:00', update_time = '2024-08-12 15:36:38', create_user = NULL, update_user = NULL WHERE id = 1802342172165120;
UPDATE tb_permission SET name = '试跑工作流', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/bpm-process/test/:instanceId', permission = 'M1228', code = 'offline_workflow', code_name = '数据开发', specific_code = NULL, parent_id = 1751835732214784, parent_path = '32/33/34/1382903756915712/1382907679835136/1751835732214784/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-14 10:46:24', create_user = NULL, update_user = NULL WHERE id = 1858355439174656;
UPDATE tb_permission SET name = '资源程序包', http_method = NULL, icon = 'iconfont icon-zip-line text-sm', uri = '/data-dev/dev/data-process/resource/:id', permission = 'M1229', code = 'offline_resource_file', code_name = '资源程序包', specific_code = NULL, parent_id = 1382903756915712, parent_path = '32/33/34/1382903756915712/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-15 13:45:01', create_user = NULL, update_user = NULL WHERE id = 1859263342314496;
UPDATE tb_permission SET name = '工作流编辑|删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R039', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1751835732214784, parent_path = '32/33/34/1382903756915712/1383529695020032/1751835732214784/', is_api = 0, is_resource = 1, resource_type = 't_ds_process_definition', type = 'resource', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-12 15:40:19', create_user = NULL, update_user = NULL WHERE id = 1864502910387200;
UPDATE tb_permission SET name = '工作流上下线补数', http_method = NULL, icon = NULL, uri = NULL, permission = 'R040', code = 'workflow:operate', code_name = '上线|下线|补数', specific_code = NULL, parent_id = 1751835732214784, parent_path = '32/33/34/1382903756915712/1383529695020032/1751835732214784/', is_api = 0, is_resource = 1, resource_type = 't_ds_process_definition', type = 'resource', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-12 15:40:39', create_user = NULL, update_user = NULL WHERE id = 1864528898360320;
UPDATE tb_permission SET name = '工作流实例停止|重跑', http_method = NULL, icon = NULL, uri = NULL, permission = 'R041', code = 'workflow_instance:operate', code_name = '停止|重跑', specific_code = NULL, parent_id = 1751835732214784, parent_path = '32/33/34/1382903756915712/1383529695020032/1751835732214784/', is_api = 0, is_resource = 1, resource_type = 't_ds_process_definition', type = 'resource', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-12 15:41:11', create_user = NULL, update_user = NULL WHERE id = 1864536379982848;
UPDATE tb_permission SET name = '离线资源包编辑删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R042', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1859263342314496, parent_path = '32/33/34/1382903756915712/1859263342314496/', is_api = 0, is_resource = 1, resource_type = 't_ds_resources', type = 'resource', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-12 15:41:49', create_user = NULL, update_user = NULL WHERE id = 1864540255749120;
UPDATE tb_permission SET name = '创建自定义flink作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/flink-custom/create/:businessFlowId', permission = 'M1236', code = 'flink_cmd', code_name = '自定义flink作业', specific_code = NULL, parent_id = 1309188023813120, parent_path = '32/33/34/1309172713260032/1309188023813120/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-14 11:02:07', create_user = NULL, update_user = NULL WHERE id = 1870406422529024;
UPDATE tb_permission SET name = '编辑自定义flink作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/flink-custom/edit/:id', permission = 'M1234', code = 'flink_cmd', code_name = '自定义flink作业', specific_code = NULL, parent_id = 1870406422529024, parent_path = '32/33/34/1309172713260032/1309188023813120/1870406422529024/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-14 10:44:19', create_user = NULL, update_user = NULL WHERE id = 1870409496593408;
UPDATE tb_permission SET name = '克隆自定义flink作业', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-process/online/flink-custom/clone/:cloneId', permission = 'M235', code = 'flink_cmd', code_name = '自定义flink作业', specific_code = NULL, parent_id = 1870406422529024, parent_path = '32/33/34/1309172713260032/1309188023813120/1870406422529024/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-14 10:44:23', create_user = NULL, update_user = NULL WHERE id = 1870411574903808;
UPDATE tb_permission SET name = '离线数据处理启动|停止', http_method = NULL, icon = NULL, uri = NULL, permission = 'R051', code = 'pipeline_batch:operate', code_name = '启动|停止', specific_code = NULL, parent_id = 1383531504600064, parent_path = '32/33/34/1382903756915712/1382907679835136/1383531504600064/', is_api = 0, is_resource = 1, resource_type = 'tb_process_batch', type = 'resource', sort = 0, description = NULL, is_display = 1, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-15 14:06:48', create_user = NULL, update_user = NULL WHERE id = 1910316206425088;
UPDATE tb_permission SET name = '离线数据处理编辑|删除', http_method = NULL, icon = NULL, uri = NULL, permission = 'R052', code = 'data_develop:write', code_name = '编辑|删除', specific_code = NULL, parent_id = 1383531504600064, parent_path = '32/33/34/1382903756915712/1382907679835136/1383531504600064/', is_api = 0, is_resource = 1, resource_type = 'tb_process_batch', type = 'resource', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2023-12-01 00:00:00', update_time = '2024-08-16 11:20:12', create_user = NULL, update_user = NULL WHERE id = 1910354231428096;
UPDATE tb_permission SET name = '数据库同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/jdbc', permission = 'M1243', code = 'jdbc', code_name = '数据库同步', specific_code = 'rtdatacollection', parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2024-01-10 00:00:00', update_time = '2024-08-14 11:00:10', create_user = NULL, update_user = NULL WHERE id = 1947176626783232;
UPDATE tb_permission SET name = '克隆数据库同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/clone/jdbc/:cloneId', permission = 'M1244', code = 'jdbc', code_name = '数据库同步', specific_code = NULL, parent_id = 1947176626783232, parent_path = '32/33/34/1309172713260032/1309151048500224/1947176626783232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2024-01-10 00:00:00', update_time = '2024-08-14 10:40:51', create_user = NULL, update_user = NULL WHERE id = 1947180622152704;
UPDATE tb_permission SET name = '编辑数据库同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/jdbc/:id', permission = 'M1245', code = 'jdbc', code_name = '数据库同步', specific_code = NULL, parent_id = 1947176626783232, parent_path = '32/33/34/1309172713260032/1309151048500224/1947176626783232/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2024-01-10 00:00:00', update_time = '2024-08-14 10:40:56', create_user = NULL, update_user = NULL WHERE id = 1947183375057920;
UPDATE tb_permission SET name = 'Logstash同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/logstash', permission = 'M1258', code = 'logstash', code_name = 'Logstash同步', specific_code = 'rtdatacollection', parent_id = 1309151048500224, parent_path = '32/33/34/1309172713260032/1309151048500224/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2024-05-30 00:00:00', update_time = '2024-08-14 11:00:16', create_user = NULL, update_user = NULL WHERE id = 2167958639805440;
UPDATE tb_permission SET name = '克隆Logstash同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/clone/logstash/:cloneId', permission = 'M1259', code = 'logstash', code_name = 'Logstash同步', specific_code = NULL, parent_id = 2167958639805440, parent_path = '32/33/34/1309172713260032/1309151048500224/2167958639805440/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2024-05-30 00:00:00', update_time = '2024-08-14 10:41:06', create_user = NULL, update_user = NULL WHERE id = 2167960322870272;
UPDATE tb_permission SET name = '编辑Logstash同步', http_method = NULL, icon = NULL, uri = '/data-dev/dev/data-ingestion/online/logstash/:id', permission = 'M1260', code = 'logstash', code_name = 'Logstash同步', specific_code = NULL, parent_id = 2167958639805440, parent_path = '32/33/34/1309172713260032/1309151048500224/2167958639805440/', is_api = 0, is_resource = 0, resource_type = NULL, type = 'menu', sort = 0, description = NULL, is_display = 0, is_deleted = 0, create_time = '2024-05-30 00:00:00', update_time = '2024-08-14 10:41:11', create_user = NULL, update_user = NULL WHERE id = 2167960322871272;

INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379906', '1765192536458240', 'realtime');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379907', '1765192536458240', 'real_time_data_ingestion');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379908', '1765192536458240', 'file');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379909', '1765192536458240', 'tcp_udp');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379910', '1765192536458240', 'kafka');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379911', '1765192536458240', 'syslog');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379912', '1765192536458240', 'archive');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379913', '1765192536458240', 'jdbc');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379914', '1765192536458240', 'logstash');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379915', '1765192536458240', 'win_event_log');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379916', '1765192536458240', 'real_time_data_processing');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379917', '1765192536458240', 'streaming');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379919', '1765192536458240', 'flink_sql');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379920', '1765192536458240', 'storage_ck');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379921', '1765192536458240', 'storage_es');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379922', '1765192536458240', 'storage_hive');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379923', '1765192536458240', 'flink_cmd');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379924', '1765192536458240', 'offline');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379925', '1765192536458240', 'offline_data_processing');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379926', '1765192536458240', 'batch');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379927', '1765192536458240', 'offline_workflow');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379928', '1765192536458240', 'offline_data_ingestion');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379929', '1765192536458240', 'offline_resource_file');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631759328379931', '1765192536458240', 'data_develop:deploy');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235712', '1765194435396608', 'data_develop:deploy');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235716', '1765194435396608', 'offline');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235717', '1765194435396608', 'offline_data_processing');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235718', '1765194435396608', 'batch');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235719', '1765194435396608', 'offline_workflow');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235720', '1765194435396608', 'offline_data_ingestion');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235721', '1765194435396608', 'offline_resource_file');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235722', '1765194435396608', 'real_time_data_processing');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235723', '1765194435396608', 'realtime');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235724', '1765194435396608', 'streaming');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235725', '1765194435396608', 'job_group:write');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235726', '1765194435396608', 'flink_sql');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235727', '1765194435396608', 'storage_ck');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235728', '1765194435396608', 'storage_es');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235729', '1765194435396608', 'storage_hive');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235730', '1765194435396608', 'flink_cmd');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235731', '1765194435396608', 'real_time_data_ingestion');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235732', '1765194435396608', 'file');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235733', '1765194435396608', 'tcp_udp');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235734', '1765194435396608', 'kafka');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235735', '1765194435396608', 'syslog');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235736', '1765194435396608', 'archive');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235737', '1765194435396608', 'jdbc');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235738', '1765194435396608', 'logstash');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631762565235739', '1765194435396608', 'win_event_log');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477760', '1765195138499584', 'data_develop:deploy');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477764', '1765195138499584', 'data_develop:write');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477765', '1765195138499584', 'offline');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477766', '1765195138499584', 'offline_data_processing');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477767', '1765195138499584', 'batch');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477768', '1765195138499584', 'offline_workflow');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477769', '1765195138499584', 'offline_data_ingestion');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477770', '1765195138499584', 'offline_resource_file');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477771', '1765195138499584', 'realtime');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477772', '1765195138499584', 'real_time_data_ingestion');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477773', '1765195138499584', 'file');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477774', '1765195138499584', 'tcp_udp');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477775', '1765195138499584', 'kafka');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477776', '1765195138499584', 'syslog');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477777', '1765195138499584', 'archive');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477778', '1765195138499584', 'jdbc');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477779', '1765195138499584', 'logstash');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477780', '1765195138499584', 'win_event_log');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477781', '1765195138499584', 'real_time_data_processing');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477782', '1765195138499584', 'streaming');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477783', '1765195138499584', 'job_group:write');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477784', '1765195138499584', 'flink_sql');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477785', '1765195138499584', 'storage_ck');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477786', '1765195138499584', 'storage_es');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477787', '1765195138499584', 'storage_hive');
INSERT INTO tb_role_permission (id, role_id, permission_code) VALUES ('2631765615477788', '1765195138499584', 'flink_cmd');

alter table tb_storage_cluster add column `enable_task_rebalanced` tinyint default 1 comment '集群模式：1-集群,0-非集群模式' after task_limit;
alter table tb_storage_cluster_history add column `enable_task_rebalanced` tinyint default 1 comment '集群模式：1-集群,0-非集群模式' after task_limit;
