<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbTagRelationMapper">

    <select id="selectRecordInfo" resultType="java.util.Map">
        SELECT * FROM ${recordType}
        <where>
            <if test="useLogicDelete == true">
                is_deleted = 0
            </if>
            AND id IN
            <foreach collection="recordIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>


</mapper>
