<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbProjectMapper">

    <select id="getByUserId" resultType="com.eoi.jax.web.repository.entity.TbProject">
        select p.*
        from tb_project p
        where p.is_deleted = 0
          and exists (select *
                      from tb_project_user_group up
                      inner join tb_user_group_relation ur on ur.group_id = up.group_id
                          and ur.user_id = #{userId}
                      where up.project_id = p.id)
    </select>

</mapper>
