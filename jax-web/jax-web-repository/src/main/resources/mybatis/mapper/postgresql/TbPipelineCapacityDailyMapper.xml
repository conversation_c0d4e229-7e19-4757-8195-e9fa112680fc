<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbPipelineCapacityDailyMapper">


    <select id="selectCustomPage" resultType="com.eoi.jax.web.repository.search.result.PipelineCapacityDailyResult">
        SELECT
            tb_pipeline.process_id,
            tb_pipeline.pipeline_name,
            tb_pipeline.pipeline_alias,
            tb_pipeline.resource_type,
            tb_pipeline_capacity_daily.*
        FROM
            tb_pipeline as tb_pipeline
                INNER JOIN tb_pipeline_capacity_daily ON tb_pipeline.id = tb_pipeline_capacity_daily.pipeline_id
        WHERE
            1 = 1
            <if test="param.idList != null and param.idList.size() > 0">
                AND tb_pipeline_capacity_daily.id IN
                <foreach collection="param.idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
          <if test="param.search != null and param.search != ''">
            AND (
              tb_pipeline.pipeline_name LIKE CONCAT('%', #{param.search}, '%')
              OR tb_pipeline.pipeline_alias LIKE CONCAT('%', #{param.search}, '%')
              OR tb_pipeline.pipeline_source LIKE CONCAT('%', #{param.search}, '%')
              OR tb_pipeline.pipeline_description LIKE CONCAT('%', #{param.search}, '%')
            )
          </if>
            <if test="param.statisticalDay != null and param.statisticalDay != ''">
                AND tb_pipeline_capacity_daily.statistical_day = #{param.statisticalDay}
            </if>
          <if test="param.startDate != null and param.startDate != ''">
            AND tb_pipeline_capacity_daily.statistical_day >= #{param.startDate}
          </if>
           <if test="param.endDate != null and param.endDate != ''">
            AND tb_pipeline_capacity_daily.statistical_day >= #{param.endDate}
           </if>
    </select>

</mapper>
