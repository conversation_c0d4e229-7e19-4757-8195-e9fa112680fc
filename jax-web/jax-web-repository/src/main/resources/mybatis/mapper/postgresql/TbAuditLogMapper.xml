<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbAuditLogMapper">
    <select id="queryList" parameterType="com.eoi.jax.web.repository.search.query.AuditLogParam"
            resultType="com.eoi.jax.web.repository.entity.TbAuditLog">
        select
        a.id,
        a.op_api,
        a.op_application,
        a.op_category,
        a.op_module,
        a.op_func,
        a.op_code,
        a.op_user,
        a.op_action,
        a.op_status,
        a.op_error_msg,
        a.op_ip,
        a.op_time,
        a.op_primary_key,
        a.op_primary_name,
        a.op_parameters,
        a.op_end_time
        from
        tb_audit_log a
        <if test="ew.opUser != null and ew.opUser != ''">
            join tb_user u on a.op_user =u.id  and u.name like  CONCAT('%', #{ew.opUser}, '%')
        </if>
        where 1=1
        <if test="ew.opCode != null and ew.opCode != ''">
            and a.op_code like CONCAT('%', #{ew.opCode}, '%')
        </if>
        <if test="ew.opPrimaryName != null and ew.opPrimaryName != ''">
            and (a.op_primary_name like CONCAT('%', #{ew.opPrimaryName}, '%')
             <if test="ew.opPrimaryKey != null ">
                 or a.op_primary_key = #{ew.opPrimaryKey}
             </if>
            )
        </if>
        <if test="ew.opAction != null and ew.opAction != ''">
            and a.op_action = #{ew.opAction}
        </if>
        <if test="ew.opStatus != null and ew.opStatus != ''">
            and a.op_status = #{ew.opStatus}
        </if>
        <if test="ew.startTime != null and ew.endTime != null ">
            and a.op_time BETWEEN #{ew.startTime} AND #{ew.endTime}
        </if>


    </select>
</mapper>
