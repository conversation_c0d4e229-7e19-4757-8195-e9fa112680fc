<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbPipelineMapper">

    <sql id="CommonSelectCustomPage">
        SELECT
            a.*
        FROM
                (SELECT * FROM tb_pipeline
                    <where>
                        <trim prefixOverrides="where">
                            ${ew.getExpression().getNormal().getSqlSegment()}
                        </trim>
                    </where>
                ) a
                <if test="tagIdList != null">
                    INNER JOIN (
                    SELECT DISTINCT tb_tag_relation.record_id
                    FROM
                    tb_tag
                    LEFT JOIN tb_tag_relation ON tb_tag.id = tb_tag_relation.tag_id
                    WHERE
                        tb_tag_relation.record_type = 'tb_pipeline'
                        AND tb_tag.id IN
                        <foreach collection="tagIdList" item="it" open="(" separator="," close=")">
                            #{it}
                        </foreach>
                    ) b ON a.id = b.record_id
                </if>
        ${ew.getExpression().getOrderBy().getSqlSegment()}
    </sql>


    <select id="selectCustomPage" resultType="com.eoi.jax.web.repository.entity.TbPipeline">
        <include refid="CommonSelectCustomPage"></include>
    </select>

    <select id="selectCustomPageNoneAuth" resultType="com.eoi.jax.web.repository.entity.TbPipeline">
        <include refid="CommonSelectCustomPage"></include>
    </select>

</mapper>
