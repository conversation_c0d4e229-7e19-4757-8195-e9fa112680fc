<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbMetricSetMapper">
    <select id="queryTreeList" parameterType="com.eoi.jax.web.repository.search.query.MetricSetTreeParam"
            resultType="com.eoi.jax.web.repository.search.result.MetricSetTreeResult">
        select
        tb_object_category.id as categoryId,
        tb_object_category.name as categoryName,
        tb_object_category.code as categoryCode,
        tb_object_category.parent_id as categoryParentId,
        tb_object_category.parent_path as categoryParentPath,
        tb_object_category.type as categoryType,

        tb_object_table.id as objId,
        tb_object_table.name as objName,
        tb_object_table.type_code as objTypeCode,
        tb_object_table.category_id as objCategoryId,
        tb_object_table.category_path as objCategoryPath,

        tb_metric_set.id as setId,
        tb_metric_set.name as setName,
        tb_metric_set.code as setCode,
        tb_metric_set.obj_id as setObjId

        from tb_metric_set as tb_metric_set
        LEFT JOIN tb_object_table ON tb_object_table.id = tb_metric_set.obj_id and tb_object_table.is_deleted = 0

        LEFT JOIN tb_object_category as tb_object_category ON (tb_object_category.id = tb_object_table.category_id
        or tb_object_category.id = tb_metric_set.category_id) and tb_object_category.is_deleted = 0

        where tb_metric_set.is_deleted =0

        <if test="param.objId != null">
            and tb_object_table.id = #{param.objId}
        </if>
        <if test="param.categoryId != null">
            and (
            tb_object_category.parent_path like CONCAT('%', #{param.categoryId}, '/%')
            or
            tb_object_category.id = #{param.categoryId}
            )
        </if>
        <if test="param.setName != null and param.setName != ''">
            and tb_metric_set.name like CONCAT('%', #{param.setName}, '%')
        </if>


    </select>

    <delete id="physicalDeleteById">
        DELETE FROM tb_metric_set WHERE id = #{id}
    </delete>

    <select id="selectCustomPage" resultType="com.eoi.jax.web.repository.search.result.MetricSetList">
        SELECT
            ms.*,
            CASE WHEN sub.itemCount IS NULL THEN 0 ELSE sub.itemCount END AS itemCount,
            ot.name as objName,
            ot.name_en as objNameEn,
            ot.tb_name as objTbName,
            ot.type_code as objTypeCode
        FROM
            tb_metric_set ms
                LEFT JOIN (
                SELECT
                    mset_id,
                    COUNT(*) AS itemCount
                FROM
                    tb_metric_item
                GROUP BY
                    mset_id
            ) sub ON ms.id = sub.mset_id
            LEFT JOIN tb_object_table ot ON ms.obj_id = ot.id
        <where>
            ms.is_deleted = 0
            <if test="ew.getExpression().getNormal() != null">
                <trim prefixOverrides="where" prefix="AND">
                    ${ew.getExpression().getNormal().getSqlSegment()}
                </trim>
            </if>
        </where>
        ${ew.getExpression().getOrderBy().getSqlSegment()}
    </select>

</mapper>
