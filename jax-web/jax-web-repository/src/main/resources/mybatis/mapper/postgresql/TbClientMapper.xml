<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbClientMapper">


    <select id="pageQuery" resultType="com.eoi.jax.web.repository.search.result.VTbClient">
        select
        c.id,
        c.name as name,
        c.api_key as api_key,
        c.secret_key as secret_key,
        c.description as description,
        c.verify_secret as verify_secret,
        c.is_deleted as is_deleted,
        c.create_time as create_time,
        c.update_time as update_time,
        c.create_user as create_user,
        c.update_user as update_user,
        ca.apiNum as apiNum
        from
        tb_client c
        LEFT JOIN (
            SELECT client_id,
            COUNT(*) AS apiNum
            FROM tb_client_api
            WHERE is_deleted = 0
            GROUP BY client_id
        ) ca ON c.id = ca.client_id
        where c.is_deleted=0
        <if test="ew != null">
            <if test="ew.name != null and ew.name != '' ">
                and c.name like #{ew.name}
            </if>
            <if test="ew.apiKey != null and ew.apiKey != '' ">
                and c.api_key like #{ew.apiKey}
            </if>
            <if test="ew.secretKey != null and ew.secretKey != '' ">
                and c.secret_key like #{ew.secretKey}
            </if>
            <if test="ew.description != null and ew.description != '' ">
                and c.description like #{ew.description}
            </if>
            <if test="ew.verifySecret ">
                and c.verify_secret = 1
            </if>
        </if>
        order by c.update_time desc
    </select>

    <select id="apiPageQuery" resultType="com.eoi.jax.web.repository.search.result.VTbApiSimple"
            parameterType="com.eoi.jax.web.repository.search.query.ClientApiParam">
        select
        a.id,
        a.name,
        a.consume_mode,
        a.status,
        a.request_type,
        a.api_mode,
        a.api_path,
        a.description,
        a.extra_setting as api_extra_setting,
        ap.id as clientApiId
        from
        tb_api a
        left join tb_client_api ap on ap.api_id = a.id and ap.is_deleted = 0 and ap.client_id = #{ew.clientId}
        where
        a.is_deleted = 0
        and a.status not in('DRAFT')
        <if test="ew != null">
            <if test="ew.apiName != null and ew.apiName != '' ">
                and a.name like #{ew.apiName}
            </if>
            <if test="ew.hiddenAuth">
                and not EXISTS (
                SELECT 1 from tb_client_api ca
                where ca.client_id= #{ew.clientId} and ca.is_deleted = 0
                and ca.api_id=a.id
                )
            </if>

        </if>
        order by a.update_time desc

    </select>

    <select id="authApiPageQuery" resultType="com.eoi.jax.web.repository.search.result.VTbApiSimple"
            parameterType="com.eoi.jax.web.repository.search.query.ClientApiParam">
        select a.id,
        a.name,
        a.consume_mode,
        a.status,
        a.request_type,
        a.api_mode,
        a.api_path,
        a.description,
        a.extra_setting as api_extra_setting,
        ap.extra_setting as client_api_extra_setting,
        ap.id as client_api_id
        from tb_api a
        join tb_client_api ap on ap.api_id = a.id and ap.is_deleted=0 and ap.client_id = #{ew.clientId}
        where a.is_deleted = 0
        <if test="ew != null">
            <if test="ew.apiName != null and ew.apiName != '' ">
                and a.name like #{ew.apiName}
            </if>
        </if>
        order by a.update_time desc
    </select>
</mapper>
