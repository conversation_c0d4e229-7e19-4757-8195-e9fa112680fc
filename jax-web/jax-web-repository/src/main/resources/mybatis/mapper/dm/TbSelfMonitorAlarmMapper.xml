<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbSelfMonitorAlarmMapper">

    <select id="selectCustomPage" resultType="com.eoi.jax.web.repository.entity.TbSelfMonitorAlarm">
        select
        alarm.*
        from
        (select
                (CASE WHEN resource_type != 'self_monitor_object' THEN object_id ELSE -1 END) AS resource_id,
                (CASE severity when 'severe' THEN  5 when 'critical' then 4 when 'warning' then 3 when 'ordinary' then 2 when 'info' then 1 else -1 end) as severityNumber,
                tb_self_monitor_alarm.*
        from tb_self_monitor_alarm
        <where>
            <trim prefixOverrides="where">
                ${ew.getExpression().getNormal().getSqlSegment()}
            </trim>
        </where>
        ) as alarm
        ${ew.getExpression().getOrderBy().getSqlSegment()}
    </select>

    <select id="countByCondition" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
        (select
                (CASE WHEN resource_type != 'self_monitor_object' THEN object_id ELSE -1 END) AS resource_id,
                tb_self_monitor_alarm.*
        from tb_self_monitor_alarm
        <where>
            <trim prefixOverrides="where">
                ${ew.getExpression().getNormal().getSqlSegment()}
            </trim>
        </where>
        ) as alarm
    </select>

    <select id="activeAlarmStatistics" resultType="map">
        select
        alarm.severity as severity,
        count(alarm.id) as severityCount
        from
        (select (CASE WHEN resource_type != 'self_monitor_object' THEN object_id ELSE -1 END) AS resource_id,tb_self_monitor_alarm.* from
        tb_self_monitor_alarm where status = 'open') as alarm
        group by alarm.severity
    </select>


    <select id="objectStatistics" resultType="com.eoi.jax.web.repository.entity.TbSelfMonitorAlarm">
        select
        alarm.object_id, alarm.object_type,alarm.object_name
        from
        (select (CASE WHEN resource_type != 'self_monitor_object' THEN object_id ELSE -1 END) AS resource_id,tb_self_monitor_alarm.* from
        tb_self_monitor_alarm
        <where>
            <trim prefixOverrides="where">
                ${ew.getExpression().getNormal().getSqlSegment()}
            </trim>
            and object_type is not null
        </where>
        ) as alarm
        group by alarm.object_id, alarm.object_type,alarm.object_name
    </select>
</mapper>
