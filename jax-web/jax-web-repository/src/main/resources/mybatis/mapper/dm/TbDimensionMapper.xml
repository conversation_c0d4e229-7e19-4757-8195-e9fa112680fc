<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbDimensionMapper">

    <select id="queryByCondition" resultType="com.eoi.jax.web.repository.search.result.TableQueryResult">
        select t.*,
            l.name    as layerName,
            tbp.name  as bizName,
            tdd.name  as domName,
            tdm.name  as martName,
            tds.name  as subjName
        FROM
            (SELECT * FROM tb_dimension
            <where>
                is_deleted = 0
                <trim prefixOverrides="where" prefix="AND">
                    ${ew.getExpression().getNormal().getSqlSegment()}
                </trim>

            </where>
            ) t
        left join tb_warehouse_layer l on t.layer_id = l.id and l.is_deleted = 0
        left join tb_business_category tbp on t.biz_id = tbp.id and tbp.is_deleted = 0
        left join tb_data_domain tdd on t.dom_id = tdd.id and tdd.is_deleted = 0
        left join tb_data_mart tdm on t.mart_id = tdm.id and tdm.is_deleted = 0
        left join tb_data_subject tds on t.subj_id = tds.id and tds.is_deleted = 0
        <if test="tagIdList != null and tagIdList.size() > 0">
            INNER JOIN (
            SELECT DISTINCT tb_tag_relation.record_id
            FROM
            tb_tag
            LEFT JOIN tb_tag_relation ON tb_tag.id = tb_tag_relation.tag_id
            WHERE
            tb_tag_relation.record_type = 'tb_table'
            AND tb_tag.id IN
            <foreach collection="tagIdList" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
            ) b ON t.id = b.record_id
        </if>
        ${ew.getExpression().getOrderBy().getSqlSegment()}
    </select>
</mapper>

