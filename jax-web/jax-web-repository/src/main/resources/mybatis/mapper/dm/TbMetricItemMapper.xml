<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbMetricItemMapper">

    <select id="queryTreeList" parameterType="com.eoi.jax.web.repository.search.query.MetricItemTreeParam"
            resultType="com.eoi.jax.web.repository.search.result.MetricItemTreeResult">
        select
        tb_object_category.id as categoryId,
        tb_object_category.name as categoryName,
        tb_object_category.code as categoryCode,
        tb_object_category.parent_id as categoryParentId,
        tb_object_category.parent_path as categoryParentPath,
        tb_object_category.type as categoryType,

        tb_object_table.id as objId,
        tb_object_table.name as objName,
        tb_object_table.type_code as objTypeCode,
        tb_object_table.category_id as objCategoryId,
        tb_object_table.category_path as objCategoryPath,

        tb_metric_set.id as setId,
        tb_metric_set.name as setName,
        tb_metric_set.code as setCode,
        tb_metric_set.obj_id as setObjId,
        tb_metric_set.category_id as setCategoryId,

        tb_metric_item.id as itemId,
        tb_metric_item.name as itemName,
        tb_metric_item.name_en as itemNameEn,
        tb_metric_item.mset_id as itemSetId
        from tb_metric_item as tb_metric_item
        LEFT JOIN tb_metric_set ON tb_metric_item.mset_id = tb_metric_set.id and tb_metric_set.is_deleted = 0
        LEFT JOIN tb_object_table ON tb_object_table.id = tb_metric_set.obj_id and tb_object_table.is_deleted = 0

        LEFT JOIN tb_object_category as tb_object_category ON (tb_object_category.id = tb_object_table.category_id
        or tb_object_category.id = tb_metric_set.category_id) and tb_object_category.is_deleted = 0

        where tb_metric_item.is_deleted = 0
        <if test="param.objId != null">
            and tb_object_table.id = #{param.objId}
        </if>
        <if test="param.categoryId != null">
            and (
            tb_object_category.parent_path like CONCAT('%', #{param.categoryId}, '/%')
            or
            tb_object_category.id = #{param.categoryId}
            )
        </if>
        <if test="param.categoryType != null and param.categoryType != ''">
            and tb_object_category.type = #{param.categoryType}
        </if>
        <if test="param.itemName != null and param.itemName != ''">
            and tb_metric_item.name like CONCAT('%', #{param.itemName}, '%')
        </if>
        <if test="param.msetId != null">
            and tb_metric_set.id = #{param.msetId}
        </if>
        <if test="param.setName != null and param.setName != ''">
            and tb_metric_set.name like CONCAT('%', #{param.setName}, '%')
        </if>

        <if test="param.setNameEn != null and param.setNameEn != ''">
            and tb_metric_item.name_en like CONCAT('%', #{param.setNameEn}, '%')
        </if>

        <if test="param.objName != null and param.objName != ''">
            and tb_object_table.name like CONCAT('%', #{param.objName}, '%')
        </if>


    </select>

    <select id="queryItemList" parameterType="com.eoi.jax.web.repository.search.query.MetricItemListParam"
            resultType="com.eoi.jax.web.repository.search.result.MetricItemListResult">

        select
        tb_object_category.id  as categoryId,
        tb_object_category.name  as categoryName,
        tb_object_category.code as categoryCode,
        tb_object_category.parent_id  as categoryParentId,
        tb_object_category.parent_path  as categoryParentPath,
        tb_object_category.type  as categoryType,

        tb_object_table.id as objId,
        tb_object_table.name as objName,
        tb_object_table.type_code as objTypeCode,
        tb_object_table.category_path as objCategoryPath,

        tb_metric_set.id as setId,
        tb_metric_set.name as setName,
        tb_metric_set.code as setCode,
        tb_metric_set.obj_id as setObjId,

        tb_measure_unit.name as unitName,

        tb_metric_item.*

        from tb_metric_item as tb_metric_item
        <if test="param.tagIdList != null and param.tagIdList.size() > 0">
            INNER JOIN (
            SELECT DISTINCT tb_tag_relation.record_id
            FROM
            tb_tag
            LEFT JOIN tb_tag_relation ON tb_tag.id = tb_tag_relation.tag_id
            WHERE
            tb_tag_relation.record_type = 'tb_metric_item'
            AND tb_tag.id IN
            <foreach collection="param.tagIdList" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
            ) b ON tb_metric_item.id = b.record_id
        </if>
        left join tb_metric_set on tb_metric_item.mset_id = tb_metric_set.id
        left join tb_object_table on tb_object_table.id = tb_metric_set.obj_id

        LEFT JOIN tb_object_category as tb_object_category ON (tb_object_category.id = tb_object_table.category_id
        or tb_object_category.id = tb_metric_set.category_id) and tb_object_category.is_deleted = 0

        left join tb_measure_unit on tb_metric_item.unit_id = tb_measure_unit.id
        where tb_metric_item.is_deleted =0
        <if test="param.objId != null">
            and tb_object_table.id = #{param.objId}
        </if>
        <if test="param.categoryType != null and param.categoryType != ''">
            and tb_object_category.type = #{param.categoryType}
        </if>
        <if test="param.categoryId != null">
            and (
            tb_object_category.parent_path like CONCAT('%', #{param.categoryId}, '/%')
            or
            tb_object_category.id = #{param.categoryId}
            )
        </if>
        <if test="param.itemName != null and param.itemName != ''">
            and tb_metric_item.name like CONCAT('%', #{param.itemName}, '%')
        </if>
        <if test="param.msetId != null">
            and tb_metric_set.id = #{param.msetId}
        </if>
        <if test="param.msetIdList != null and param.msetIdList.size>0 ">
            and tb_metric_set.id in
            <foreach collection="param.msetIdList" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.msetCodeList != null and param.msetCodeList.size>0 ">
            and tb_metric_set.code in
            <foreach collection="param.msetCodeList" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.setName != null and param.setName != ''">
            and tb_metric_set.name like CONCAT('%', #{param.setName}, '%')
        </if>
        <if test="param.dimensionId != null">
            and exists(select 1 from tb_metric_item_dimension dim
            where dim.metric_id = tb_metric_item.id and dim.metric_dimension_id = #{param.dimensionId} )
        </if>
        <if test="param.unitId != null">
            and tb_metric_item.unit_id = #{param.unitId}
        </if>
        <if test="param.level != null and param.level != ''">
            and tb_metric_item.level = #{param.level}
        </if>
        <if test="param.sourcePlatformCode != null and param.sourcePlatformCode != ''">
            and tb_metric_item.sourcePlatformCode = #{param.sourcePlatformCode}
        </if>

        <if test="param.itemNameEn != null and param.itemNameEn != ''">
            and tb_metric_item.name_en like CONCAT('%', #{param.itemNameEn}, '%')
        </if>
        <if test="param.itemIds != null and param.itemIds.size>0 ">
            and tb_metric_item.id in
            <foreach collection="param.itemIds" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.unItemIds != null and param.unItemIds.size>0 ">
            and tb_metric_item.id not in
            <foreach collection="param.unItemIds" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.objName != null and param.objName != ''">
            and tb_object_table.name like CONCAT('%', #{param.objName}, '%')
        </if>
        <if test="param.objTypeCodeList != null and param.objTypeCodeList.size>0">
            and tb_object_table.type_code in
            <foreach collection="param.objTypeCodeList" item="it" open="(" close=")" separator=",">
                #{it}
            </foreach>
        </if>

        <if test="param.itemNameEnList != null and param.itemNameEnList.size() > 0">
            and tb_metric_item.name_en in
            <foreach collection="param.itemNameEnList" item="it" open="(" close=")" separator=",">
                #{it}
            </foreach>
        </if>
    </select>

    <select id="queryAllItemList" parameterType="com.eoi.jax.web.repository.search.query.MetricItemListParam"
            resultType="com.eoi.jax.web.repository.search.result.MetricItemListResult">

        select
        tb_object_category.id  as categoryId,
        tb_object_category.name  as categoryName,
        tb_object_category.code as categoryCode,
        tb_object_category.parent_id  as categoryParentId,
        tb_object_category.parent_path  as categoryParentPath,
        tb_object_category.type  as categoryType,

        tb_object_table.id as objId,
        tb_object_table.name as objName,
        tb_object_table.type_code as objTypeCode,
        tb_object_table.category_path as objCategoryPath,

        tb_metric_set.id as setId,
        tb_metric_set.name as setName,
        tb_metric_set.code as setCode,
        tb_metric_set.obj_id as setObjId,

        tb_measure_unit.name as unitName,

        tb_metric_item.*

        from tb_metric_item as tb_metric_item
        left join tb_metric_set on tb_metric_item.mset_id = tb_metric_set.id
        left join tb_object_table on tb_object_table.id = tb_metric_set.obj_id
        left join tb_measure_unit on tb_metric_item.unit_id = tb_measure_unit.id

        LEFT JOIN tb_object_category as tb_object_category ON (tb_object_category.id = tb_object_table.category_id
        or tb_object_category.id = tb_metric_set.category_id) and tb_object_category.is_deleted = 0

        where tb_metric_item.is_deleted =0
        <if test="param.msetCategoryId != null">
            and tb_metric_set.category_id = #{param.msetCategoryId}
        </if>
        <if test="param.objId != null">
            and tb_object_table.id = #{param.objId}
        </if>
        <if test="param.categoryType != null and param.categoryType != ''">
            and tb_object_category.type = #{param.categoryType}
        </if>
        <if test="param.categoryId != null">
            and (
            tb_object_category.parent_path like CONCAT('%', #{param.categoryId}, '/%')
            or
            tb_object_category.id = #{param.categoryId}
            )
        </if>
        <if test="param.itemName != null and param.itemName != ''">
            and tb_metric_item.name like CONCAT('%', #{param.itemName}, '%')
        </if>
        <if test="param.msetId != null">
            and tb_metric_set.id = #{param.msetId}
        </if>
        <if test="param.setName != null and param.setName != ''">
            and tb_metric_set.name like CONCAT('%', #{param.setName}, '%')
        </if>
        <if test="param.unitId != null">
            and tb_metric_item.unitId = #{param.unitId}
        </if>
        <if test="param.level != null and param.level != ''">
            and tb_metric_item.level = #{param.level}
        </if>
        <if test="param.sourcePlatformCode != null and param.sourcePlatformCode != ''">
            and tb_metric_item.sourcePlatformCode = #{param.sourcePlatformCode}
        </if>

        <if test="param.itemNameEn != null and param.itemNameEn != ''">
            and tb_metric_item.name_en like CONCAT('%', #{param.itemNameEn}, '%')
        </if>
        <if test="param.itemIds != null and param.itemIds.size>0 ">
            and tb_metric_item.id in
            <foreach collection="param.itemIds" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>

        <if test="param.objName != null and param.objName != ''">
            and tb_object_table.name like CONCAT('%', #{param.objName}, '%')
        </if>
    </select>

    <select id="queryExportItem" parameterType="com.eoi.jax.web.repository.search.query.MetricItemExportParam"
            resultType="com.eoi.jax.web.repository.search.result.MetricItemExportResult">
        select
        max(tb_object_category.id)  as categoryId,
        max(tb_object_category.name)  as categoryName,
        max(tb_object_category.code) as categoryCode,
        max(tb_object_category.parent_id)  as categoryParentId,
        max(tb_object_category.parent_path)  as categoryParentPath,
        max(tb_object_category.type)  as categoryType,

        tb_object_table.id as objId,
        tb_object_table.name as objName,
        tb_object_table.type_code as objTypeCode,
        tb_object_table.category_path as objCategoryPath,

        tb_metric_set.id as setId,
        tb_metric_set.name as setName,
        tb_metric_set.code as setCode,
        tb_metric_set.obj_id as setObjId,

        tb_measure_unit.name as unitName,
        tb_table.tb_name as idxTbName,
        tb_table.tb_alias as idxTbAlias,
        tb_column.col_name as idxColumnName,

        group_concat(tb_tag_relation.tag_id SEPARATOR ',') as tagIds,
        group_concat(tb_tag.name SEPARATOR ',') as tagNames,
        group_concat(tb_metric_item_dimension.metric_id SEPARATOR ',') as dimTagIds,
        group_concat(tb_metric_dimension.code SEPARATOR ',') as dimTagNames,

        tb_metric_item.*

        from tb_metric_item as tb_metric_item
        <if test="param.tagIdList != null and param.tagIdList.size() > 0">
            INNER JOIN (
            SELECT DISTINCT tb_tag_relation.record_id
            FROM
            tb_tag
            LEFT JOIN tb_tag_relation ON tb_tag.id = tb_tag_relation.tag_id
            WHERE
            tb_tag_relation.record_type = 'tb_metric_item'
            AND tb_tag.id IN
            <foreach collection="param.tagIdList" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
            ) b ON tb_metric_item.id = b.record_id
        </if>
        left join tb_metric_set on tb_metric_item.mset_id = tb_metric_set.id
        left join tb_object_table on tb_object_table.id = tb_metric_set.obj_id
        left join tb_measure_unit on tb_metric_item.unit_id = tb_measure_unit.id
        left join tb_table on tb_table.id = tb_metric_item.idx_id
        left join tb_column on tb_column.id = tb_metric_item.idx_column_id
        left join tb_tag_relation on tb_tag_relation.record_id = tb_metric_item.id
        left join tb_tag on tb_tag.id = tb_tag_relation.tag_id
        left join tb_metric_item_dimension on tb_metric_item_dimension.metric_id = tb_metric_item.id
        left join tb_metric_dimension on tb_metric_item_dimension.metric_dimension_id = tb_metric_dimension.id

        LEFT JOIN tb_object_category as tb_object_category ON (tb_object_category.id = tb_object_table.category_id
        or tb_object_category.id = tb_metric_set.category_id) and tb_object_category.is_deleted = 0
        where tb_metric_item.is_deleted =0
        <if test="param.objId != null">
            and tb_object_table.id = #{param.objId}
        </if>
        <if test="param.categoryType != null and param.categoryType != ''">
            and  tb_object_category.type = #{param.categoryType}
        </if>
        <if test="param.categoryId != null">
            and (
            tb_object_category.parent_path like CONCAT('%', #{param.categoryId}, '/%')
            or
            tb_object_category.id = #{param.categoryId}
            )
        </if>
        <if test="param.itemName != null and param.itemName != ''">
            and tb_metric_item.name like CONCAT('%', #{param.itemName}, '%')
        </if>
        <if test="param.msetId != null">
            and tb_metric_set.id = #{param.msetId}
        </if>
        <if test="param.itemIds != null and  param.itemIds.size != 0 ">
            AND tb_metric_item.id in
            <foreach collection="param.itemIds" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.setIds != null and  param.setIds.size != 0 ">
            AND tb_metric_item.mset_id in
            <foreach collection="param.setIds" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="param.setName != null and param.setName != ''">
            and tb_metric_set.name like CONCAT('%', #{param.setName}, '%')
        </if>

        group by tb_metric_item.id
        <if test="param.itemOrderUpdateTime != null and param.itemOrderUpdateTime != '' and param.setOrderUpdateTime == null">
            order by tb_metric_item.update_time ${param.itemOrderUpdateTime}
        </if>

        <if test="param.setOrderUpdateTime != null and param.setOrderUpdateTime != '' and param.itemOrderUpdateTime == null">
            order by tb_metric_set.update_time ${param.setOrderUpdateTime}
        </if>

    </select>

    <select id="getByMetricDimensionId" resultType="com.eoi.jax.web.repository.entity.TbMetricItem" parameterType="java.lang.Long">
        select tb_metric_item.*
        from tb_metric_item as tb_metric_item
                 right join tb_metric_item_dimension on tb_metric_item_dimension.metric_id = tb_metric_item.id
        where tb_metric_item_dimension.metric_dimension_id = #{metricDimensionId}
    </select>

    <select id="countBySetId" resultType="com.eoi.jax.web.repository.search.result.CommonCountResult">
        SELECT
            mset_id AS resultKey,
            count(1) AS resultCount
        FROM
            tb_metric_item AS tb_metric_item
        WHERE
            tb_metric_item.is_deleted = 0
          AND tb_metric_item.mset_id IN
              <foreach collection="setIdList" item="it" open="(" separator="," close=")">
                  #{it}
              </foreach>
        GROUP BY
            tb_metric_item.mset_id
    </select>
</mapper>
