<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbJaxClusterFileMapper">


    <select id="getNodeNotSyncFileList"
            resultType="com.eoi.jax.web.repository.entity.TbJaxClusterFile"
            parameterType="com.eoi.jax.web.repository.search.query.JaxClusterFileNotSyncParam">
        select
        id,
        type,
        record_id,
        file_path,
        name,
        value_md5,
        version,
        <if test="includeValueColumn">
            value,
        </if>
        is_deleted,
        create_time,
        update_time,
        create_user,
        update_user
        from tb_jax_cluster_file
        where id in (
            select max(id)
            from tb_jax_cluster_file
            where is_deleted=0
            <if test="type !=null and type != ''">
                and type= #{type}
            </if>
            group by type,record_id,name
        )
        and id not in (
            select
            DISTINCT jcf_id
            from tb_jax_cluster_file_node
            where server_id = #{serverId} and status='SUCCESS'
        )
    </select>

    <select id="getLastVersionList"
            resultType="com.eoi.jax.web.repository.entity.TbJaxClusterFile"
            parameterType="com.eoi.jax.web.repository.search.query.JaxClusterFileLastVersionParam">
        select
        id,
        type,
        record_id,
        file_path,
        name,
        value_md5,
        version,
        <if test="includeValueColumn">
            value,
        </if>
        is_deleted,
        create_time,
        update_time,
        create_user,
        update_user
        from tb_jax_cluster_file
        where id in (
        select max(id)
        from tb_jax_cluster_file
        where is_deleted=0
        <if test="type !=null and type != ''">
            and type= #{type}
        </if>
        <if test="clusterId !=null">
            and record_id= #{clusterId}
        </if>
        group by type,record_id,name
        )
    </select>


</mapper>
