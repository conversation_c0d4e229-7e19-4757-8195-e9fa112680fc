<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbAtomicIndicatorMapper">

    <select id="queryByCondition" resultType="com.eoi.jax.web.repository.search.result.VAtomicIndicator">
        select i.*,
        tbc.name as biz_name,
        tdd.name as dom_name,
        tbp.name as proc_name,
        tt.tb_name,
        tc.col_name
        from tb_atomic_indicator i
        left join tb_business_category tbc on i.biz_id = tbc.id
        left join tb_data_domain tdd on i.dom_id = tdd.id
        left join tb_business_process tbp on i.proc_id = tbp.id
        left join tb_table tt on i.tb_id = tt.id
        left join tb_column tc on i.col_id = tc.id
        <where>
            i.is_deleted = 0
            <if test="param.domId != null and param.domId != 0 ">
                and i.dom_id = #{param.domId}
            </if>
            <if test="param.procId != null and param.procId != 0 ">
                and i.proc_id = #{param.procId}
            </if>
            <if test="param.searchKeyword != null and param.searchKeyword != '' ">
                and (
                i.code like concat('%',#{param.searchKeyword},'%')
                or i.name like concat('%',#{param.searchKeyword},'%')
                or i.name_en like concat('%',#{param.searchKeyword},'%')
                )
            </if>
            <choose>
                <when test="param.sortItems != null  and  param.sortItems.size != 0 ">
                    order by
                    <foreach collection="param.sortItems" item="sortItem" separator=",">
                        ${sortItem.key} ${sortItem.order}
                    </foreach>
                </when>
                <otherwise>
                    order by i.update_time desc
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="queryByIds" resultType="com.eoi.jax.web.repository.search.result.VAtomicIndicator">
        select i.*,
        tbc.name as biz_name,
        tdd.name as dom_name,
        tbp.name as proc_name,
        tt.tb_name,
        tc.col_name
        from tb_atomic_indicator i
        left join tb_business_category tbc on i.biz_id = tbc.id
        left join tb_data_domain tdd on i.dom_id = tdd.id
        left join tb_business_process tbp on i.proc_id = tbp.id
        left join tb_table tt on i.tb_id = tt.id
        left join tb_column tc on i.col_id = tc.id
        <trim prefix="WHERE" prefixOverrides="AND | OR" >
            i.is_deleted = 0
            <if test="ids != null">
                AND <foreach collection="ids" item="id" open="i.id in (" close=") " separator=",">
                #{id}
            </foreach>
            </if>
        </trim>
        order by i.update_time desc
    </select>


</mapper>

