<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbRealtimeCheckTaskMapper">


    <select id="selectCustomPage" resultType="com.eoi.jax.web.repository.entity.TbRealtimeCheckTask">
        SELECT
            a.*
        FROM
            (SELECT * FROM tb_realtime_check_task WHERE
                is_deleted = 0
                <trim prefixOverrides="where" prefix="AND">
                    ${ew.getExpression().getNormal().getSqlSegment()}
                </trim>
            ) a
        <if test="templateId != null">
            INNER JOIN (
                SELECT DISTINCT task_id
                FROM
                    tb_realtime_check_rule
                WHERE
                    is_deleted = 0 AND template_id = #{templateId}
            ) b ON a.id = b.task_id
        </if>
        ${ew.getExpression().getOrderBy().getSqlSegment()}
    </select>

</mapper>
