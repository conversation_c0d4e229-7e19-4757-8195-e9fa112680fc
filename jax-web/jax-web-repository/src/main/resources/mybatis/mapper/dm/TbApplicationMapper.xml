<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbApplicationMapper">

    <select id="getList" resultType="com.eoi.jax.web.repository.search.result.ApplicationMaintenanceResult">
        SELECT
            ad.status as deployStatus,
            ad.yarn_application_id,
            ad.track_url,
            ad.start_time,
            ad.end_time,
            c.cluster_name as clusterName,
            c.setting as clusterSetting,
            o.opts_name as optsName,
            o.setting as optsSetting,
            o.opts_type as optsType,
            a.*
        FROM tb_application a
        left join tb_application_deploy ad on a.id=ad.application_id
        left join tb_cluster c on a.cluster_id=c.id
        left join tb_opts o on a.opts_id=o.id
        WHERE
            a.is_deleted = 0
            <if test="param.status!=null and param.status!=''">
                and ( ad.status=#{param.status} or a.status=#{param.status} )
            </if>
            <if test="param.name!=null and param.name!=''">
                and a.name like concat('%',#{param.name},'%')
            </if>
            <if test="param.appType!=null and param.appType!=''">
                and a.app_type=#{param.appType}
            </if>
            <if test="param.search!=null and param.search!=''">
                and (
                    a.name like concat('%',#{param.search},'%')
                    or
                    a.description like concat('%',#{param.search},'%')
                )
            </if>
            <if test="param.id!=null">
                and a.id=#{param.id}
            </if>
    </select>


</mapper>
