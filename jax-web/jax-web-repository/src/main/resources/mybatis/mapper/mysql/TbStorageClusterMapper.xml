<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbStorageClusterMapper">


    <select id="getClusterList" parameterType="com.eoi.jax.web.repository.search.query.StorageClusterParam"
            resultType="com.eoi.jax.web.repository.search.result.StorageClusterResult">
        select
        tc.cluster_name as clusterName,
        kad.name as kafkaDsName,
        ckd.name as ckDsName,
        rc.name as nacosName,
        sc.*
        from tb_storage_cluster sc
        left join tb_cluster tc on tc.id=sc.cluster_id
        left join tb_datasource kad on sc.kafka_ds_id=kad.id
        left join tb_datasource ckd on sc.ck_ds_id=ckd.id
        left join tb_register_center rc on sc.register_center_id=rc.id

        where sc.is_deleted=0
        <if test="param.status!=null and param.status!=''">
            AND sc.status = #{param.status}
        </if>
        <if test="param.name!=null and param.name!=''">
            AND sc.name like concat('%',#{param.name},'%')
        </if>
        <if test="param.kafkaDsId!=null">
            AND sc.kafka_ds_id = #{param.kafkaDsId}
        </if>
        <if test="param.ckDsId!=null">
            AND sc.ck_ds_id = #{param.ckDsId}
        </if>
    </select>

    <select id="getClusterByStorageCkIds" parameterType="list"
            resultType="com.eoi.jax.web.repository.entity.TbStorageCluster">
        select  sc.*
        from tb_storage_cluster sc
        where sc.is_deleted=0 and sc.id in
            (   select storage_cluster_id
                from tb_storage_ck sck
                where
                <foreach collection="storageCkIds" item="id" open="sck.id in (" close=") " separator=",">
                    #{id}
                </foreach>
            )
    </select>
</mapper>
