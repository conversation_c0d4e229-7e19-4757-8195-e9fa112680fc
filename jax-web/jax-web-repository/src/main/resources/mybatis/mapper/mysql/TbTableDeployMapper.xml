<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbTableDeployMapper">

    <select id="getLastByTableId" resultType="com.eoi.jax.web.repository.entity.TbTableDeploy">
        select *
        from tb_table_deploy
        where tb_id=#{tableId}
        <if test="includeDeleted==false">
            AND is_deleted = 0
        </if>
        order by create_time desc
        limit 1
    </select>

    <select id="selectLatestTableDeployStatus" resultType="com.eoi.jax.web.repository.entity.TbTableDeploy">
        SELECT tb_id ,
               CASE max(statusNum) WHEN  0 THEN  'SUCCESS' WHEN 1 THEN  'DEPLOYING'
                                   WHEN 2 THEN 'FAILURE'  WHEN 3 THEN  'UNDEPLOYED' END as status
        from
            (select tb_id,
                    CASE status WHEN  'SUCCESS' THEN  0 WHEN 'DEPLOYING' THEN  1
                                  WHEN 'FAILURE' THEN  2 WHEN 'UNDEPLOYED' THEN  3 END as statusNum
             from tb_table_deploy where is_deleted = 0
	        <if test="platform !=null and platform != ''">
	            AND platform = #{platform}
	        </if>
            ) t
        group by tb_id
    </select>

    <select id="getLastDeployList" resultType="com.eoi.jax.web.repository.entity.TbTableDeploy">

        SELECT
            td.*
        FROM
            tb_table_deploy td
            INNER JOIN (
                SELECT
                    max(id) id2
                FROM
                    tb_table_deploy b
                WHERE
                    is_deleted = 0
                    AND tb_type != 'OBJ'
                    <if test="platform!=null and platform!=''">
                        AND platform = #{platform}
                    </if>
                    <if test="status!=null and status!=''">
                        AND status = #{status}
                    </if>
                    <if test="tbIdList != null">
                        AND tb_id in
                        <foreach collection="tbIdList" item="it" open="(" separator="," close=")">
                            #{it}
                        </foreach>
                    </if>
                    <if test="tbNameList != null">
                        AND LOWER(tb_name) in
                        <foreach collection="tbNameList" item="it" open="(" separator="," close=")">
                            #{it}
                        </foreach>
                    </if>
                    <if test="dsId != null">
                        AND ds_id = #{dsId}
                    </if>
                    <if test="isOnline != null">
                        AND is_online = #{isOnline}
                    </if>
                GROUP BY
                    tb_id
            ) md  on td.id = md.id2
        WHERE
           1=1
            <if test="dimTbId != null">
                AND td.dim_tb_id = #{dimTbId}
            </if>
    </select>

    <select id="selectLastByQueryWrapper" resultType="com.eoi.jax.web.repository.entity.TbTableDeploy">
        SELECT
            td.*
        FROM
            tb_table_deploy td
        INNER JOIN (
            SELECT
                max(tb_table_deploy.id) max_id,tb_table.resource_id, tb_table.resource_type
            FROM
                tb_table_deploy
            INNER JOIN (
                <choose>
                    <when test="resourceType == 'tb_object_table'">
                        SELECT id,is_online,is_deleted,category_id as resource_id, 'tb_object_category' as resource_type from tb_object_table
                    </when>
                    <otherwise>
                        SELECT id,is_online,is_deleted,id AS resource_id,'tb_table' AS resource_type FROM tb_table
                    </otherwise>
                </choose>
            ) AS tb_table ON tb_table.id = tb_table_deploy.tb_id
            <where>
                tb_table.is_deleted = 0
                AND tb_table_deploy.is_deleted = 0
                <if test="isOnline != null">
                    AND tb_table.is_online = #{isOnline}
                    AND tb_table_deploy.is_online = #{isOnline}
                </if>
                <choose>
                    <when test="resourceType == 'tb_object_table'">
                        AND tb_type = 'OBJ'
                    </when>
                    <otherwise>
                        AND tb_type != 'OBJ'
                    </otherwise>
                </choose>
                <trim prefixOverrides="where" prefix="AND">
                    ${ew.getExpression().getNormal().getSqlSegment()}
                </trim>
            </where>
            GROUP BY
                tb_table_deploy.tb_id,
                tb_table.resource_id,
                tb_table.resource_type
        ) t ON t.max_id = td.id
        ${ew.getExpression().getOrderBy().getSqlSegment()}
    </select>


    <select id="getDeployedObjectTables" resultType="com.eoi.jax.web.repository.entity.TbTableDeploy">
        SELECT
            td.*
        FROM
            tb_table_deploy td
        INNER JOIN (
            SELECT
                max(id) id2
            FROM
            tb_table_deploy b
            WHERE
                is_deleted = 0
            AND tb_type = 'OBJ'
            AND is_online = 1
            AND status = 'SUCCESS'
            <if test="platform!=null and platform!=''">
                AND platform = #{platform}
            </if>
            GROUP BY tb_id
        ) md  on td.id = md.id2
        <where>
            <if test="dsId != null">
                AND td.ds_id = #{dsId}
            </if>
        </where>
    </select>
</mapper>
