<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.VIngestionJobMapper">

    <sql id="commonWhere">
        <where>
            a.is_deleted = 0
            AND b.is_deleted = 0
            AND c.is_deleted = 0
            AND d.is_deleted = 0
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.name != null">
                AND a.name LIKE CONCAT('%', #{param.name}, '%')
            </if>
            <if test="param.cellId != null">
                AND a.cell_id = #{param.cellId}
            </if>
            <if test="param.ingestionType != null">
                AND a.ingestion_type = #{param.ingestionType}
            </if>
            <if test="param.status != null">
                AND a.status = #{param.status}
            </if>
            <if test="param.statusList != null and param.statusList.size() != 0">
                AND a.status in
                <foreach collection="param.statusList" item="it" open="(" close=")" separator=",">
                    #{it}
                </foreach>
            </if>
            <if test="param.businessFlowId != null">
                AND b.business_flow_id = #{param.businessFlowId}
            </if>
        </where>
    </sql>

    <select id="selectByCondition" resultType="com.eoi.jax.web.repository.search.result.VIngestionJobResult">
        SELECT
            a.id,
            a.ingestion_id,
            a.name,
            a.ingestion_type,
            a.description,
            a.cell_id,
            a.tb_id,
            a.setting,
            a.publish_version,
            a.operation,
            a.status,
            a.task_status_statistics,
            a.is_deleted,
            a.create_time,
            a.update_time,
            a.create_user,
            a.update_user,
            c.name as cell_name,
            d.name as business_flow_name
        FROM
            tb_ingestion_job AS a
                LEFT JOIN tb_ingestion AS b ON a.ingestion_id = b.id
                LEFT JOIN tb_cell AS c ON a.cell_id = c.id
                LEFT JOIN tb_business_flow AS d ON b.business_flow_id = d.id
        <include refid="commonWhere"/>
    </select>


    <select id="countByCondition" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            tb_ingestion_job AS a
                LEFT JOIN tb_ingestion AS b ON a.ingestion_id = b.id
                LEFT JOIN tb_cell AS c ON a.cell_id = c.id
                LEFT JOIN tb_business_flow AS d ON b.business_flow_id = d.id
        <include refid="commonWhere"/>
    </select>

</mapper>