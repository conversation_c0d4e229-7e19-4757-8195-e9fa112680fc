<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbLifecycleBatchJobMapper">

    <select id="selectQueuedJobs" resultType="com.eoi.jax.web.repository.search.result.TbLifecycleQueueJob">
        select j.*,
               (select status from tb_lifecycle_batch_job where id = j.parent_job_id and is_deleted = 0) as parentJobStatus
        from tb_lifecycle_batch_job j
        where status = 'QUEUE'
          and is_deleted = 0
        order by id asc
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findMidStatusJobIds" resultType="com.eoi.jax.web.repository.entity.TbLifecycleBatchJob">
        select t.*
        from tb_lifecycle_batch_job as t
        where status in ('RUNNING', 'STOPPING')
          and t.trigger_time <![CDATA[ <= ]]> #{losedTime}
          and t.is_deleted = 0
    </select>

</mapper>