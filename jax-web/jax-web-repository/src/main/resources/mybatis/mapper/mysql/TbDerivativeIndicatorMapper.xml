<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eoi.jax.web.repository.mapper.TbDerivativeIndicatorMapper">

    <select id="queryByCondition" resultType="com.eoi.jax.web.repository.search.result.VDerivativeIndicator">
        select i.*,
        ati.name as business_atomic_name,
        mit.name as monitor_atomic_name,
        tip.name as period_name,
        twl.name as layer_name,
        ntcr.name as nameRuleName,
        ctcr.name as codeRuleName,
        tbc.name as biz_name,
        tdd.name as dom_name,
        tbp.name as proc_name,
        tdm.name as mart_name,
        tds.name as subj_name,
        twl.catalog
        from tb_derivative_indicator i
        left join tb_atomic_indicator ati on i.atomic_id = ati.id and ati.is_deleted = 0
        left join tb_metric_item mit on i.atomic_id = mit.id and mit.is_deleted = 0
        left join tb_time_period tip on i.period_id = tip.id and tip.is_deleted = 0
        left join tb_warehouse_layer twl on i.layer_id = twl.id and twl.is_deleted = 0
        left join tb_check_rule ntcr on i.name_rule_id = ntcr.id and ntcr.is_deleted = 0
        left join tb_check_rule ctcr on i.code_rule_id = ctcr.id and ctcr.is_deleted = 0
        left join tb_business_category tbc on i.biz_id = tbc.id and tbc.is_deleted = 0
        left join tb_data_domain tdd on i.dom_id = tdd.id and tdd.is_deleted = 0
        left join tb_business_process tbp on i.proc_id = tbp.id and tbp.is_deleted = 0
        left join tb_data_mart tdm on i.mart_id = tdm.id and tdm.is_deleted = 0
        left join tb_data_subject tds on i.subj_id = tds.id and tds.is_deleted = 0
        <where>
            i.is_deleted = 0
            <if test="param.catalog != null and param.catalog != '' ">
                and twl.catalog = #{param.catalog}
            </if>
            <if test="param.layerId != null and param.layerId != 0 ">
                and i.layer_id = #{param.layerId}
            </if>
            <if test="param.domId != null and param.domId != 0 ">
                and i.dom_id = #{param.domId}
            </if>
            <if test="param.procId != null and param.procId != 0 ">
                and i.proc_id = #{param.procId}
            </if>
            <if test="param.martId != null and param.martId != 0 ">
                and i.mart_id = #{param.martId}
            </if>
            <if test="param.subjId != null and param.subjId != 0 ">
                and i.subj_id = #{param.subjId}
            </if>
            <if test="param.atomicId != null and param.atomicId != 0 ">
                and i.atomic_id = #{param.atomicId}
            </if>
            <if test="param.searchKeyword != null and param.searchKeyword != '' ">
                and (
                i.code like concat('%',#{param.searchKeyword},'%')
                or i.name like concat('%',#{param.searchKeyword},'%')
                or i.name_en like concat('%',#{param.searchKeyword},'%')
                )
            </if>
            <choose>
                <when test="param.sortItems != null  and  param.sortItems.size != 0 ">
                    order by
                    <foreach collection="param.sortItems" item="sortItem" separator=",">
                        ${sortItem.key} ${sortItem.order}
                    </foreach>
                </when>
                <otherwise>
                    order by i.update_time desc
                </otherwise>
            </choose>
        </where>
    </select>


    <select id="queryByIds" resultType="com.eoi.jax.web.repository.search.result.VDerivativeIndicator">
        select i.*,
        ati.name as business_atomic_name,
        mit.name as monitor_atomic_name,
        tip.name as period_name,
        twl.name as layer_name,
        ntcr.name as nameRuleName,
        ctcr.name as codeRuleName,
        tbc.name as biz_name,
        tdd.name as dom_name,
        tbp.name as proc_name,
        tdm.name as mart_name,
        tds.name as subj_name,
        twl.catalog
        from tb_derivative_indicator i
        left join tb_atomic_indicator ati on i.atomic_id = ati.id and ati.is_deleted = 0
        left join tb_metric_item mit on i.atomic_id = mit.id and mit.is_deleted = 0
        left join tb_time_period tip on i.period_id = tip.id and tip.is_deleted = 0
        left join tb_warehouse_layer twl on i.layer_id = twl.id and twl.is_deleted = 0
        left join tb_check_rule ntcr on i.name_rule_id = ntcr.id and ntcr.is_deleted = 0
        left join tb_check_rule ctcr on i.code_rule_id = ctcr.id and ctcr.is_deleted = 0
        left join tb_business_category tbc on i.biz_id = tbc.id and tbc.is_deleted = 0
        left join tb_data_domain tdd on i.dom_id = tdd.id and tdd.is_deleted = 0
        left join tb_business_process tbp on i.proc_id = tbp.id and tbp.is_deleted = 0
        left join tb_data_mart tdm on i.mart_id = tdm.id and tdm.is_deleted = 0
        left join tb_data_subject tds on i.subj_id = tds.id and tds.is_deleted = 0
        <trim prefix="WHERE" prefixOverrides="AND | OR" >
            i.is_deleted = 0
            <if test="ids != null">
                AND <foreach collection="ids" item="id" open="i.id in (" close=") " separator=",">
                #{id}
            </foreach>
            </if>
        </trim>
        order by i.update_time desc
    </select>

</mapper>

