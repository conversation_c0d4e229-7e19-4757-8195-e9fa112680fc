package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.eoi.jax.web.repository.base.ILogicDeleteEntity;

import java.util.Date;

/**
 * <p>
 * 告警表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public class TbAlert implements ILogicDeleteEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 告警key
     */
    private String alertKey;

    /**
     * 规则id
     */
    private Long ruleId;

    /**
     * 告警等级
     */
    private String level;

    /**
     * 告警主题
     */
    private String subject;

    /**
     * 告警内容
     */
    private String content;

    /**
     * 标签
     */
    private String labels;

    /**
     * 附加信息
     */
    private String annotations;

    /**
     * 状态
     */
    private String status;

    /**
     * 作业id
     */
    private Long pipelineId;

    /**
     * 触发时间
     */
    private Long activeTime;

    /**
     * 激活时间
     */
    private Long firedTime;

    /**
     * 处理时间
     */
    private Long resolvedTime;

    /**
     * 最后发送时间
     */
    private Long lastSendTime;

    /**
     * 触发条件
     */
    private String triggerCondition;

    /**
     * 基础值
     */
    private Double base;

    /**
     * 采样值
     */
    private Double sample;

    /**
     * 结果值
     */
    private Double value;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 模型数量
     */
    private Integer tableNum;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAlertKey() {
        return alertKey;
    }

    public void setAlertKey(String alertKey) {
        this.alertKey = alertKey;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getSubject() {
        return subject;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getLabels() {
        return labels;
    }

    public void setLabels(String labels) {
        this.labels = labels;
    }

    public String getAnnotations() {
        return annotations;
    }

    public void setAnnotations(String annotations) {
        this.annotations = annotations;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Long activeTime) {
        this.activeTime = activeTime;
    }

    public Long getFiredTime() {
        return firedTime;
    }

    public void setFiredTime(Long firedTime) {
        this.firedTime = firedTime;
    }

    public Long getResolvedTime() {
        return resolvedTime;
    }

    public void setResolvedTime(Long resolvedTime) {
        this.resolvedTime = resolvedTime;
    }

    public Long getLastSendTime() {
        return lastSendTime;
    }

    public void setLastSendTime(Long lastSendTime) {
        this.lastSendTime = lastSendTime;
    }

    public Double getBase() {
        return base;
    }

    public void setBase(Double base) {
        this.base = base;
    }

    public Double getSample() {
        return sample;
    }

    public void setSample(Double sample) {
        this.sample = sample;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public String getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(String triggerCondition) {
        this.triggerCondition = triggerCondition;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return null;
    }

    @Override
    public void setCreateUser(Long createUser) {

    }

    @Override
    public Long getUpdateUser() {
        return null;
    }

    @Override
    public void setUpdateUser(Long updateUser) {

    }

    public Integer getTableNum() {
        return tableNum;
    }

    @Override
    public String toString() {
        return "TbAlert{" +
                "id=" + id +
                ", alertKey='" + alertKey + '\'' +
                ", ruleId=" + ruleId +
                ", level='" + level + '\'' +
                ", subject='" + subject + '\'' +
                ", content='" + content + '\'' +
                ", labels='" + labels + '\'' +
                ", annotations='" + annotations + '\'' +
                ", status='" + status + '\'' +
                ", pipelineId=" + pipelineId +
                ", activeTime=" + activeTime +
                ", firedTime=" + firedTime +
                ", resolvedTime=" + resolvedTime +
                ", lastSendTime=" + lastSendTime +
                ", triggerCondition='" + triggerCondition + '\'' +
                ", base=" + base +
                ", sample=" + sample +
                ", value=" + value +
                ", remark='" + remark + '\'' +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", tableNum=" + tableNum +
                '}';
    }

    public void setTableNum(Integer tableNum) {
        this.tableNum = tableNum;
    }

}
