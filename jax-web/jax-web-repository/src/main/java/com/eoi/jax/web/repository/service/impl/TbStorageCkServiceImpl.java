package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbStorageCk;
import com.eoi.jax.web.repository.mapper.TbStorageCkMapper;
import com.eoi.jax.web.repository.search.query.StorageCkMaintenanceParam;
import com.eoi.jax.web.repository.search.query.StorageCkQueryParam;
import com.eoi.jax.web.repository.search.query.StorageCkUpdateBatchParam;
import com.eoi.jax.web.repository.search.result.StorageCkMaintenanceResult;
import com.eoi.jax.web.repository.service.TbStorageCkService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 存储作业表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class TbStorageCkServiceImpl extends ProjectAuthServiceImpl<TbStorageCkMapper, TbStorageCk> implements TbStorageCkService {

    @Override
    public List<TbStorageCk> getList(StorageCkQueryParam param) {
        QueryWrapper<TbStorageCk> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(param.getStorageCkId() != null, TbStorageCk::getId, param.getStorageCkId());
        wrapper.lambda().eq(param.getStorageClusterId() != null, TbStorageCk::getStorageClusterId, param.getStorageClusterId());
        wrapper.lambda().eq(param.getCkTbId() != null, TbStorageCk::getCkTbId, param.getCkTbId());
        wrapper.lambda().eq(param.getDimTbId() != null, TbStorageCk::getDimTbId, param.getDimTbId());
        wrapper.lambda().eq(param.getIdxTbId() != null, TbStorageCk::getIdxTbId, param.getIdxTbId());
        wrapper.lambda().eq(param.getKafkaTbId() != null, TbStorageCk::getKafkaTbId, param.getKafkaTbId());
        wrapper.lambda().eq(StringUtils.hasLength(param.getConsumeGroup()), TbStorageCk::getConsumeGroup, param.getConsumeGroup());
        wrapper.lambda().eq(param.getBusinessFlowId() != null, TbStorageCk::getBusinessFlowId, param.getBusinessFlowId());
        wrapper.lambda().eq(StringUtils.hasLength(param.getStatus()), TbStorageCk::getStatus, param.getStatus());
        wrapper.lambda().in(param.getIncludeStatus() != null && param.getIncludeStatus().size() > 0,
                TbStorageCk::getStatus, param.getIncludeStatus());
        wrapper.lambda().like(StringUtils.hasLength(param.getName()), TbStorageCk::getName, param.getName());

        return baseMapper.selectList(wrapper);
    }

    @Override
    public void updateStatusBatch(StorageCkUpdateBatchParam param) {
        Assert.isTrue(param.getIds() != null && param.getIds().size() > 0, "ck存储作业ids不能为空");
        baseMapper.updateStatusBatch(param);
    }

    @Override
    public Page<StorageCkMaintenanceResult> getMaintenanceList(Page<StorageCkMaintenanceResult> page, StorageCkMaintenanceParam param) {
        return baseMapper.getMaintenanceList(page, param);
    }

}
