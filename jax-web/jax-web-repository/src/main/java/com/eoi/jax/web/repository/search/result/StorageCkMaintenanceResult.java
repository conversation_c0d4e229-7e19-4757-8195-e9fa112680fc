package com.eoi.jax.web.repository.search.result;

import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.ProjectAuthorityEntity;

import java.util.Date;

/**
 * <AUTHOR> zsc
 * @create 2023/1/3 17:05
 */
public class StorageCkMaintenanceResult extends ProjectAuthorityEntity  implements IUserInfoExtensionEntity, IProjectAuthEntity {
    private Long storageCkId;
    private String storageCkName;
    private String businessFlowName;
    private Long businessFlowId;
    private Long storageClusterId;
    private String storageClusterName;
    private Long appId;
    private String appName;
    private String appStatus;
    private Long kafkaDsId;
    private Long ckDsId;
    private Long registerCenterId;
    private String nacosGroupName;
    private String nacosDataId;
    private Long clusterId;
    private Long optsId;
    private String storageClusterStatus;
    private Integer storageClusterPublished;
    private String storageClusterScript;

    private Long kafkaTbId;
    private Long kafkaTdId;
    private String strategy;
    private String consumeGroup;
    private Long ckTbId;
    private Long dimTbId;
    private Long idxTbId;
    private Long ckTdId;
    private Long dimTdId;
    private Long idxTdId;
    private String storageCkStatus;
    private Integer storageCkStatusPublished;
    private Date lastStartTime;
    private Date createTime;
    private Date updateTime;
    private Long createUser;
    private Long updateUser;

    public Long getStorageCkId() {
        return storageCkId;
    }

    public void setStorageCkId(Long storageCkId) {
        this.storageCkId = storageCkId;
    }

    public String getStorageCkName() {
        return storageCkName;
    }

    public void setStorageCkName(String storageCkName) {
        this.storageCkName = storageCkName;
    }

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public Long getStorageClusterId() {
        return storageClusterId;
    }

    public void setStorageClusterId(Long storageClusterId) {
        this.storageClusterId = storageClusterId;
    }

    public String getStorageClusterName() {
        return storageClusterName;
    }

    public void setStorageClusterName(String storageClusterName) {
        this.storageClusterName = storageClusterName;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(String appStatus) {
        this.appStatus = appStatus;
    }

    public Long getKafkaDsId() {
        return kafkaDsId;
    }

    public void setKafkaDsId(Long kafkaDsId) {
        this.kafkaDsId = kafkaDsId;
    }

    public Long getCkDsId() {
        return ckDsId;
    }

    public void setCkDsId(Long ckDsId) {
        this.ckDsId = ckDsId;
    }

    public Long getRegisterCenterId() {
        return registerCenterId;
    }

    public void setRegisterCenterId(Long registerCenterId) {
        this.registerCenterId = registerCenterId;
    }

    public String getNacosGroupName() {
        return nacosGroupName;
    }

    public void setNacosGroupName(String nacosGroupName) {
        this.nacosGroupName = nacosGroupName;
    }

    public String getNacosDataId() {
        return nacosDataId;
    }

    public void setNacosDataId(String nacosDataId) {
        this.nacosDataId = nacosDataId;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public Long getOptsId() {
        return optsId;
    }

    public void setOptsId(Long optsId) {
        this.optsId = optsId;
    }

    public String getStorageClusterStatus() {
        return storageClusterStatus;
    }

    public void setStorageClusterStatus(String storageClusterStatus) {
        this.storageClusterStatus = storageClusterStatus;
    }

    public Integer getStorageClusterPublished() {
        return storageClusterPublished;
    }

    public void setStorageClusterPublished(Integer storageClusterPublished) {
        this.storageClusterPublished = storageClusterPublished;
    }

    public String getStorageClusterScript() {
        return storageClusterScript;
    }

    public void setStorageClusterScript(String storageClusterScript) {
        this.storageClusterScript = storageClusterScript;
    }

    public Long getKafkaTbId() {
        return kafkaTbId;
    }

    public void setKafkaTbId(Long kafkaTbId) {
        this.kafkaTbId = kafkaTbId;
    }

    public Long getKafkaTdId() {
        return kafkaTdId;
    }

    public void setKafkaTdId(Long kafkaTdId) {
        this.kafkaTdId = kafkaTdId;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getConsumeGroup() {
        return consumeGroup;
    }

    public void setConsumeGroup(String consumeGroup) {
        this.consumeGroup = consumeGroup;
    }

    public Long getCkTbId() {
        return ckTbId;
    }

    public void setCkTbId(Long ckTbId) {
        this.ckTbId = ckTbId;
    }

    public Long getDimTbId() {
        return dimTbId;
    }

    public void setDimTbId(Long dimTbId) {
        this.dimTbId = dimTbId;
    }

    public Long getIdxTbId() {
        return idxTbId;
    }

    public void setIdxTbId(Long idxTbId) {
        this.idxTbId = idxTbId;
    }

    public Long getCkTdId() {
        return ckTdId;
    }

    public void setCkTdId(Long ckTdId) {
        this.ckTdId = ckTdId;
    }

    public Long getDimTdId() {
        return dimTdId;
    }

    public void setDimTdId(Long dimTdId) {
        this.dimTdId = dimTdId;
    }

    public Long getIdxTdId() {
        return idxTdId;
    }

    public void setIdxTdId(Long idxTdId) {
        this.idxTdId = idxTdId;
    }

    public String getStorageCkStatus() {
        return storageCkStatus;
    }

    public void setStorageCkStatus(String storageCkStatus) {
        this.storageCkStatus = storageCkStatus;
    }

    public Integer getStorageCkStatusPublished() {
        return storageCkStatusPublished;
    }

    public void setStorageCkStatusPublished(Integer storageCkStatusPublished) {
        this.storageCkStatusPublished = storageCkStatusPublished;
    }

    public Date getLastStartTime() {
        return lastStartTime;
    }

    public void setLastStartTime(Date lastStartTime) {
        this.lastStartTime = lastStartTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public Long getId() {
        return null;
    }

    @Override
    public void setId(Long id) {

    }
}
