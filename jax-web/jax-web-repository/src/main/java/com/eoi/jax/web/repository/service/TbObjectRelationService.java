package com.eoi.jax.web.repository.service;

import com.eoi.jax.web.repository.entity.TbObjectRelation;
import com.eoi.jax.web.repository.search.query.ObjectRelationBatchParam;
import com.eoi.jax.web.repository.search.query.ObjectRelationParam;
import com.eoi.jax.web.repository.search.query.TableObjectExport;
import com.eoi.jax.web.repository.search.result.ObjectTableRelationGroupResult;
import com.eoi.jax.web.repository.search.result.ObjectTableRelationRespResult;

import java.util.List;

/**
 * <p>
 * 对象关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface TbObjectRelationService extends IDaoService<TbObjectRelation> {

    List<ObjectTableRelationRespResult> relations(ObjectRelationParam param);
    List<ObjectTableRelationRespResult> relations(ObjectRelationBatchParam param);

    List<ObjectTableRelationGroupResult> groupByTypeId(List<Long> collect);
    List<ObjectTableRelationRespResult> exportData(TableObjectExport parm);
}
