package com.eoi.jax.web.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.annotation.*;
import com.eoi.jax.web.repository.entity.TbPipeline;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-13 16:59:41
 */

@Mapper
@DisableInsertProjectResource
public interface TbPipelineMapper extends UserInfoExtensionMapper<TbPipeline> {


    @ProjectAuthAnnotation(resourceIdColumn = "process_id", resourceTypeColumn = "resource_type")
    @UserInfoExtensionAnnotation
    IPage<TbPipeline> selectCustomPage(Page<TbPipeline> page,
                                       @Param(Constants.WRAPPER) QueryWrapper<TbPipeline> wrapper,
                                       @Param("tagIdList") List<Long> tagIdList);


    IPage<TbPipeline> selectCustomPageNoneAuth(Page<TbPipeline> page,
                                               @Param(Constants.WRAPPER) QueryWrapper<TbPipeline> wrapper,
                                               @Param("tagIdList") List<Long> tagIdList);

    @ProjectAuthAnnotation(resourceIdColumn = "process_id", returnProjectAuthField = false, resourceTypeColumn = "resource_type")
    @Override
    Long selectCount(@Param(Constants.WRAPPER) Wrapper<TbPipeline> queryWrapper);

    @ProjectAuthAnnotation(resourceIdColumn = "process_id", returnProjectAuthField = false, resourceTypeColumn = "resource_type")
    @UserInfoExtensionAnnotation
    @Override
    List<TbPipeline> selectList(@Param(Constants.WRAPPER) Wrapper<TbPipeline> queryWrapper);
}
