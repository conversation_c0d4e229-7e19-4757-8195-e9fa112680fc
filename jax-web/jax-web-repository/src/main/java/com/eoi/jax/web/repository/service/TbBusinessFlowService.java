package com.eoi.jax.web.repository.service;

import com.eoi.jax.web.repository.entity.TbBusinessFlow;

import java.util.List;

/**
 * <p>
 * 业务流程 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-28
 */
public interface TbBusinessFlowService extends IProjectAuthService<TbBusinessFlow> {

    /**
     * 根据id列表查询TbBusinessFlow
     *
     * @param idList
     * @return
     */
    List<TbBusinessFlow> selectByIdList(List<Long> idList);

}
