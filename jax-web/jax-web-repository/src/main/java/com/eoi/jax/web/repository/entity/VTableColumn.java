package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;

/**
 * @Author: <PERSON>
 * @Date: 2022/12/3
 **/
public class VTableColumn extends TbTable{

    @TableField
    private Long tbId;

    @TableField
    private Long colNo;

    @TableField
    private String tbType;

    @TableField
    private String colType;

    @TableField
    private String colName;

    @TableField
    private String colDisplay;

    @TableField
    private String description;

    @TableField
    private Integer isPrimaryKey;

    @TableField
    private Integer isNotNull;

    @TableField
    private String colCatalog;

    @TableField
    private Long unitId;

    @TableField
    private Long dicId;

    @TableField
    private Long enumId;

    @TableField
    private String indicatorType;

    @TableField
    private Long periodId;

    @TableField
    private Long adjId;

    @TableField
    private Long atomicId;

    @TableField
    private Long dervId;

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public Long getColNo() {
        return colNo;
    }

    public void setColNo(Long colNo) {
        this.colNo = colNo;
    }

    @Override
    public String getTbType() {
        return tbType;
    }

    @Override
    public void setTbType(String tbType) {
        this.tbType = tbType;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsPrimaryKey() {
        return isPrimaryKey;
    }

    public void setIsPrimaryKey(Integer isPrimaryKey) {
        this.isPrimaryKey = isPrimaryKey;
    }

    public Integer getIsNotNull() {
        return isNotNull;
    }

    public void setIsNotNull(Integer isNotNull) {
        this.isNotNull = isNotNull;
    }

    public String getColCatalog() {
        return colCatalog;
    }

    public void setColCatalog(String colCatalog) {
        this.colCatalog = colCatalog;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public Long getEnumId() {
        return enumId;
    }

    public void setEnumId(Long enumId) {
        this.enumId = enumId;
    }

    public String getIndicatorType() {
        return indicatorType;
    }

    public void setIndicatorType(String indicatorType) {
        this.indicatorType = indicatorType;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getAdjId() {
        return adjId;
    }

    public void setAdjId(Long adjId) {
        this.adjId = adjId;
    }

    public Long getAtomicId() {
        return atomicId;
    }

    public void setAtomicId(Long atomicId) {
        this.atomicId = atomicId;
    }

    public Long getDervId() {
        return dervId;
    }

    public void setDervId(Long dervId) {
        this.dervId = dervId;
    }
}
