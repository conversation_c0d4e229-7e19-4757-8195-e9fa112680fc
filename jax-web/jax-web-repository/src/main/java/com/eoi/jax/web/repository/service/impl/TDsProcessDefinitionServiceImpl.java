package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TDsProcessDefinition;
import com.eoi.jax.web.repository.mapper.TDsProcessDefinitionMapper;
import com.eoi.jax.web.repository.service.TDsProcessDefinitionService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@Service
public class TDsProcessDefinitionServiceImpl extends ProjectAuthServiceImpl<TDsProcessDefinitionMapper, TDsProcessDefinition>
        implements TDsProcessDefinitionService {


    @Override
    public IPage<TDsProcessDefinition> selectCustomPage(Page<TDsProcessDefinition> page,
                                                        QueryWrapper<TDsProcessDefinition> wrapper,
                                                        List<Long> tagIdList) {
        return baseMapper.selectCustomPage(page, wrapper, tagIdList);
    }

    @Override
    public List<TDsProcessDefinition> listByConditionAndProjectId(QueryWrapper<TDsProcessDefinition> wrapper,
                                                                  Long projectId) {
        return baseMapper.listByConditionAndProjectId(wrapper, projectId);
    }

    @Override
    public TDsProcessDefinition getByProjectCodeAndName(Long projectCode, String name) {
        LambdaQueryWrapper<TDsProcessDefinition> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(projectCode), TDsProcessDefinition::getProjectCode, projectCode);
        wrapper.eq(TDsProcessDefinition::getName, name);
        return getOne(wrapper);
    }


    @Override
    public TDsProcessDefinition getByCode(Long code) {
        LambdaQueryWrapper<TDsProcessDefinition> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TDsProcessDefinition::getCode, code);
        return getOne(wrapper);
    }


    @Override
    public List<TDsProcessDefinition> listByCode(List<Long> codeList) {
        LambdaQueryWrapper<TDsProcessDefinition> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TDsProcessDefinition::getCode, codeList);
        return list(wrapper);
    }

}
