package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbIngestionJobTask;
import com.eoi.jax.web.repository.mapper.TbIngestionJobTaskMapper;
import com.eoi.jax.web.repository.service.TbIngestionJobTaskService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service
public class TbIngestionJobTaskServiceImpl extends ServiceImpl<TbIngestionJobTaskMapper, TbIngestionJobTask>
        implements TbIngestionJobTaskService {

    @Override
    public TbIngestionJobTask selectByIngestionIdAndAgentId(Long ingestionId, Long agentId) {
        LambdaQueryWrapper<TbIngestionJobTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbIngestionJobTask::getIngestionId, ingestionId);
        queryWrapper.eq(TbIngestionJobTask::getAgentId, agentId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<TbIngestionJobTask> selectByIngestionJobId(Long ingestionJobId) {
        LambdaQueryWrapper<TbIngestionJobTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbIngestionJobTask::getIngestionJobId, ingestionJobId);
        return baseMapper.selectList(queryWrapper);
    }




}
