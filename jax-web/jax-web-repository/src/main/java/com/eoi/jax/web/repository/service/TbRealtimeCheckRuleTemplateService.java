package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbRealtimeCheckRuleTemplate;
import com.eoi.jax.web.repository.search.result.CommonCountResult;

import java.util.List;

/**
 * <p>
 * 实时检测规则模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
public interface TbRealtimeCheckRuleTemplateService extends IProjectAuthService<TbRealtimeCheckRuleTemplate> {

    /**
     * 根据类别统计条数
     *
     * @return
     */
    List<CommonCountResult> countByCategory(List<String> categoryList);

    /**
     * 自定义分页查询
     * @param page
     * @param wrapper
     * @param taskId
     * @return
     */
    IPage<TbRealtimeCheckRuleTemplate> selectCustomPage(Page<TbRealtimeCheckRuleTemplate> page,
                                                        QueryWrapper<TbRealtimeCheckRuleTemplate> wrapper,
                                                        Long taskId);

    /**
     * 根据Id列表查询模板
     * @param idList
     * @return
     */
    List<TbRealtimeCheckRuleTemplate> selectById(List<Long> idList);
}
