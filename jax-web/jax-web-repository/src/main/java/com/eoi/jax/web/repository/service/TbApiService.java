package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbApi;
import com.eoi.jax.web.repository.entity.VApiUsage;
import com.eoi.jax.web.repository.search.query.ApiQueryParam;
import com.eoi.jax.web.repository.search.result.CommonCountResult;

import java.util.List;

/**
 * <p>
 * 接口定义表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
public interface TbApiService extends IProjectAuthService<TbApi>, IDaoService<TbApi> {
    /**
     * 根据api路径查询api
     *
     * @param apiPath
     * @return
     */
    TbApi getApiByUri(String apiPath);

    /**
     * 根据自定义类查询
     *
     * @param handlerClassList
     * @param status
     * @return
     */
    List<TbApi> getByHandlerClass(List<String> handlerClassList, String status);

    List<TbApi> getByApiPathList(List<String> apiPathList);

    List<TbApi> getApiByGroupId(List<Long> groupIds);

    List<VApiUsage> usage(Long apiId);

    List<CommonCountResult> countGroupBy(String groupBy);

    /**
     * 自定义分页查询
     *
     * @param page
     * @param queryParam
     * @return
     */
    Page<TbApi> pageQuery(Page<TbApi> page, ApiQueryParam queryParam);

    /**
     *  api 上线未发布，要判断publishId 是否引用该数据源
     *  api 未上线，取当前数据即可
     * @param id
     * @return
     */
    List<TbApi> queryDsUsage(Long id);

    Boolean checkApiPathExists(Long id, String apiPath);
}
