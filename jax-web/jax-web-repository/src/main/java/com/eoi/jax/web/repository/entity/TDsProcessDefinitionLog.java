package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
public class TDsProcessDefinitionLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * self-increasing id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * encoding
     */
    private Long code;

    /**
     * process definition name
     */
    private String name;

    /**
     * process definition version
     */
    private Integer version;

    /**
     * description
     */
    private String description;

    /**
     * project code
     */
    private Long projectCode;

    /**
     * process definition release state：0:offline,1:online
     */
    private Integer releaseState;

    /**
     * process definition creator id
     */
    private Integer userId;

    /**
     * global parameters
     */
    private String globalParams;

    /**
     * 0 not available, 1 available
     */
    private Integer flag;

    /**
     * Node location information
     */
    private String locations;

    /**
     * alert group id
     */
    private Integer warningGroupId;

    /**
     * time out,unit: minute
     */
    private Integer timeout;

    /**
     * tenant id
     */
    private Integer tenantId;

    /**
     * execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority
     */
    private Integer executionType;

    /**
     * operator user id
     */
    private Integer operator;

    /**
     * operate time
     */
    private Date operateTime;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 业务流程id
     */
    private Long businessFlowId;

    /**
     * 创建人id
     */
    private Long createUser;

    /**
     * 更新人id
     */
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(Long projectCode) {
        this.projectCode = projectCode;
    }

    public Integer getReleaseState() {
        return releaseState;
    }

    public void setReleaseState(Integer releaseState) {
        this.releaseState = releaseState;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getGlobalParams() {
        return globalParams;
    }

    public void setGlobalParams(String globalParams) {
        this.globalParams = globalParams;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getLocations() {
        return locations;
    }

    public void setLocations(String locations) {
        this.locations = locations;
    }

    public Integer getWarningGroupId() {
        return warningGroupId;
    }

    public void setWarningGroupId(Integer warningGroupId) {
        this.warningGroupId = warningGroupId;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getExecutionType() {
        return executionType;
    }

    public void setExecutionType(Integer executionType) {
        this.executionType = executionType;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "TDsProcessDefinitionLog{" +
                "id=" + id +
                ", code=" + code +
                ", name='" + name + '\'' +
                ", version=" + version +
                ", description='" + description + '\'' +
                ", projectCode=" + projectCode +
                ", releaseState=" + releaseState +
                ", userId=" + userId +
                ", globalParams='" + globalParams + '\'' +
                ", flag=" + flag +
                ", locations='" + locations + '\'' +
                ", warningGroupId=" + warningGroupId +
                ", timeout=" + timeout +
                ", tenantId=" + tenantId +
                ", executionType=" + executionType +
                ", operator=" + operator +
                ", operateTime=" + operateTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", businessFlowId=" + businessFlowId +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                '}';
    }
}
