package com.eoi.jax.web.repository.service;

import com.eoi.jax.web.repository.entity.TbObjectRelationType;
import com.eoi.jax.web.repository.search.query.TableObjectExport;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 对象模型关系类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface TbObjectRelationTypeService extends IDaoService<TbObjectRelationType> {

    List<TbObjectRelationType> exportData(TableObjectExport parm);

    List<TbObjectRelationType> listByCode(Set<String> codeList);

}
