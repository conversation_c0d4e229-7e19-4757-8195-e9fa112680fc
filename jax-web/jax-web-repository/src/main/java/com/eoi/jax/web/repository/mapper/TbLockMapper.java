package com.eoi.jax.web.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eoi.jax.web.repository.entity.TbLock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 分布式锁 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Mapper
public interface TbLockMapper extends BaseMapper<TbLock> {
    @Update("update tb_lock set count=count+1 where id=#{id}")
    boolean incCount(Long id);

    @Update("update tb_lock set count=count-1 where id=#{id}")
    boolean decCount(Long id);
}
