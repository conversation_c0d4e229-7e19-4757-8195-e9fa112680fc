package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbAuditLog;
import com.eoi.jax.web.repository.search.query.AuditLogParam;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface TbAuditLogService extends  IDaoService<TbAuditLog>  {

    Page<TbAuditLog> queryList(Page<TbAuditLog> page, AuditLogParam param);
}
