package com.eoi.jax.web.repository.service;

import com.eoi.jax.web.repository.entity.TDsTaskInstance;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
public interface TDsTaskInstanceService extends IDaoService<TDsTaskInstance> {

    List<TDsTaskInstance> listByProcessInstanceId(Integer processInstanceId, Integer runFlag);


    List<TDsTaskInstance> findValidTaskListByProcessId(Integer processInstanceId);

}
