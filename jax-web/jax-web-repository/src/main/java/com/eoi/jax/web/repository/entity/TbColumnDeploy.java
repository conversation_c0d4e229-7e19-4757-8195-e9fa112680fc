package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;

/**
 * <p>
 * 数据模型字段发布表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-30
 */
public class TbColumnDeploy extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, ILogicDeleteEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 数据模型发布表id
     */
    private Long tableDeployId;

    /**
     * 字段id
     */
    private Long colId;

    /**
     * 字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN
     */
    private String colType;

    /**
     * 字段名
     */
    private String colName;

    /**
     * 显示名
     */
    private String colDisplay;

    /**
     * 表单类型
     */
    private String formType;

    /**
     * 字段描述
     */
    private String description;

    /**
     * 是否主键
     */
    private Integer isPrimaryKey;

    private String colUsage;

    /**
     * 是否不为空
     */
    private Integer isNotNull;

    /**
     * 字段标准id
     */
    private Long dicId;

    /**
     * 字段标准配置，json字符串
     */
    private String dicSetting;

    @TableLogic
    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;

    private Long createUser;

    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTableDeployId() {
        return tableDeployId;
    }

    public void setTableDeployId(Long tableDeployId) {
        this.tableDeployId = tableDeployId;
    }

    public Long getColId() {
        return colId;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColUsage() {
        return colUsage;
    }

    public void setColUsage(String colUsage) {
        this.colUsage = colUsage;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsPrimaryKey() {
        return isPrimaryKey;
    }

    public void setIsPrimaryKey(Integer isPrimaryKey) {
        this.isPrimaryKey = isPrimaryKey;
    }

    public Integer getIsNotNull() {
        return isNotNull;
    }

    public void setIsNotNull(Integer isNotNull) {
        this.isNotNull = isNotNull;
    }

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public String getDicSetting() {
        return dicSetting;
    }

    public void setDicSetting(String dicSetting) {
        this.dicSetting = dicSetting;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "TbColumnDeploy{" +
        "id=" + id +
        ", tableDeployId=" + tableDeployId +
        ", colId=" + colId +
        ", colType=" + colType +
        ", colName=" + colName +
        ", colDisplay=" + colDisplay +
        ", description=" + description +
        ", isPrimaryKey=" + isPrimaryKey +
        ", isNotNull=" + isNotNull +
        ", dicId=" + dicId +
        ", dicSetting=" + dicSetting +
        ", isDeleted=" + isDeleted +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
