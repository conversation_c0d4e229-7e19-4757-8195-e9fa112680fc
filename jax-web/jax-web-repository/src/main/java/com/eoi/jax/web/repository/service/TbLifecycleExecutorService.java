package com.eoi.jax.web.repository.service;

import com.eoi.jax.web.repository.entity.TbLifecycleExecutor;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 生命周期执行器表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface TbLifecycleExecutorService extends IDaoService<TbLifecycleExecutor> {

    /**
     * 失效的执行器
     *
     * @param deadTimeoutInMills
     * @param date
     * @return
     */
    List<Long> findDead(int deadTimeoutInMills, Date date);

    /**
     *
     *
     * @param aliveTimeInMills
     * @param date
     * @return
     */
    List<String> findAlive(int aliveTimeInMills, Date date);

    /**
     * 可用执行器列表
     *
     * @return
     */
    List<TbLifecycleExecutor> listAliveExecutor();
}
