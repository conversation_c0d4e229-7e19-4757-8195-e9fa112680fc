package com.eoi.jax.web.repository.entity;

import com.eoi.jax.web.repository.base.IBaseEntity;

import java.util.Date;

/**
 * <p>
 * 中台集群配置变更记录集群表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
public class TbJaxClusterFileNode implements IBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    private Long jcfId;

    private String serverId;

    private String filePath;

    /**
     * 名称
     */
    private String ip;

    /**
     * value
     */
    private Integer port;

    /**
     * 状态
     */
    private String status;

    /**
     * 异常信息
     */
    private String message;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人id
     */
    private Long createUser;

    /**
     * 更新人id
     */
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJcfId() {
        return jcfId;
    }

    public void setJcfId(Long jcfId) {
        this.jcfId = jcfId;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public String toString() {
        return "TbJaxClusterFileNode{" +
                "id=" + id +
                ", jcfId=" + jcfId +
                ", filePath=" + filePath +
                ", serverid=" + serverId +
                ", ip=" + ip +
                ", port=" + port +
                ", status=" + status +
                ", message=" + message +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                "}";
    }
}
