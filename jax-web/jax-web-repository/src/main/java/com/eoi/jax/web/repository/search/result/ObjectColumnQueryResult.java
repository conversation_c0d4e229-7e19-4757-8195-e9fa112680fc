package com.eoi.jax.web.repository.search.result;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.io.Serializable;

/**
 * @Author: tangy
 * @Date: 2023/9/14
 * @Desc:
 **/
public class ObjectColumnQueryResult  extends UserInfoExtensionEntity implements Serializable {
    @TableField
    private Long id;
    /**
     * 列序号
     */
    @TableField
    private String tbName;


    /**
     * 对象模型中文名
     */
    @TableField
    private String objectName;
    /**
     * 列序号
     */
    @TableField
    private Integer colNo;

    /**
     * 字段名
     */
    @TableField
    private String colName;
    /**
     * 字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN
     */
    @TableField
    private String colType;


    /**
     * 显示名
     */
    @TableField
    private String colDisplay;


    /**
     * 备注信息
     */
    @TableField
    private String description;


    /**
     * 是否为主键
     */
    @TableField
    private Boolean isPrimaryKey;


    /**
     * 非空
     */
    @TableField
    private Boolean isNotNull;


    /**
     * 度量单位
     */
    @TableField
    private String unitName;


    /**
     * 字段类别，PROPERTY属性、DIMENSION维度、UNIT度量
     */
    @TableField
    private String colCatalog;


    /**
     * 来源字段标准
     */
    @TableField
    private String dicName;


    /**
     * 关联标准代码
     */
    @TableField
    private String enumName;


    /**
     * 关键字段
     */
    @TableField
    private Boolean isKeyField;


    /**
     * 标识字段
     */
    @TableField
    private Boolean isIdentificationField;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public Integer getColNo() {
        return colNo;
    }

    public void setColNo(Integer colNo) {
        this.colNo = colNo;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getPrimaryKey() {
        return isPrimaryKey;
    }

    public void setPrimaryKey(Boolean primaryKey) {
        isPrimaryKey = primaryKey;
    }

    public Boolean getNotNull() {
        return isNotNull;
    }

    public void setNotNull(Boolean notNull) {
        isNotNull = notNull;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getColCatalog() {
        return colCatalog;
    }

    public void setColCatalog(String colCatalog) {
        this.colCatalog = colCatalog;
    }

    public String getDicName() {
        return dicName;
    }

    public void setDicName(String dicName) {
        this.dicName = dicName;
    }

    public String getEnumName() {
        return enumName;
    }

    public void setEnumName(String enumName) {
        this.enumName = enumName;
    }

    public Boolean getKeyField() {
        return isKeyField;
    }

    public void setKeyField(Boolean keyField) {
        isKeyField = keyField;
    }

    public Boolean getIdentificationField() {
        return isIdentificationField;
    }

    public void setIdentificationField(Boolean identificationField) {
        isIdentificationField = identificationField;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }
}
