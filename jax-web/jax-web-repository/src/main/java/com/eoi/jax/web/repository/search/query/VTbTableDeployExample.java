package com.eoi.jax.web.repository.search.query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/2
 */
public class VTbTableDeployExample {

    private Long cellId;

    private Long layerId;

    private List<String> deployPlatformList;

    private String deployStatus;

    private String layerCatalog;

    private String searchKeyword;

    private List<String> neTbTypeList;

    private List<String> deployTypeList;

    public Long getCellId() {
        return cellId;
    }

    public void setCellId(Long cellId) {
        this.cellId = cellId;
    }

    public Long getLayerId() {
        return layerId;
    }

    public void setLayerId(Long layerId) {
        this.layerId = layerId;
    }

    public List<String> getDeployPlatformList() {
        return deployPlatformList;
    }

    public void setDeployPlatformList(List<String> deployPlatformList) {
        this.deployPlatformList = deployPlatformList;
    }

    public String getDeployStatus() {
        return deployStatus;
    }

    public void setDeployStatus(String deployStatus) {
        this.deployStatus = deployStatus;
    }

    public String getLayerCatalog() {
        return layerCatalog;
    }

    public void setLayerCatalog(String layerCatalog) {
        this.layerCatalog = layerCatalog;
    }

    public String getSearchKeyword() {
        return searchKeyword;
    }

    public void setSearchKeyword(String searchKeyword) {
        this.searchKeyword = searchKeyword;
    }

    public List<String> getNeTbTypeList() {
        return neTbTypeList;
    }

    public void setNeTbTypeList(List<String> neTbTypeList) {
        this.neTbTypeList = neTbTypeList;
    }

    public List<String> getDeployTypeList() {
        return deployTypeList;
    }

    public void setDeployTypeList(List<String> deployTypeList) {
        this.deployTypeList = deployTypeList;
    }
}
