package com.eoi.jax.web.repository.search.result;

import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.ProjectAuthorityEntity;

import java.util.Date;

/**
 * @Author: tangy
 * @Date: 2023/7/24
 * @Desc:
 **/
public class VTbApiSimple extends ProjectAuthorityEntity implements IUserInfoExtensionEntity, IProjectAuthEntity {
    /**
     * id
     */
    private Long id;

    /**
     * 接口名
     */
    private String name;


    /**
     * 消费模式，HTTP-http请求，KAFKA-kafka消费
     */
    private String consumeMode;

    /**
     * api路径
     */
    private String apiPath;

    /**
     * 请求方式，GET-get请求，POST-post请求
     */
    private String requestType;

    /**
     * api模式，standard-标准模式，sql-基础，uq - uq模式，custom-自定义模式
     */
    private String apiMode;

    /**
     * 状态：ONLINE 上线,OFFLINE:下线,DRAFT:草稿
     */
    private String status;


    /**
     * 描述信息
     */
    private String description;


    private String apiExtraSetting;

    private String clientApiExtraSetting;
    /**
     * id
     */
    private Long clientApiId;

    private String createUserName;
    private String updateUserName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public Date getCreateTime() {
        return null;
    }

    @Override
    public void setCreateTime(Date createTime) {

    }

    @Override
    public Date getUpdateTime() {
        return null;
    }

    @Override
    public void setUpdateTime(Date updateTime) {

    }

    @Override
    public Long getCreateUser() {
        return null;
    }

    @Override
    public void setCreateUser(Long createUser) {

    }

    @Override
    public Long getUpdateUser() {
        return null;
    }

    @Override
    public void setUpdateUser(Long updateUser) {

    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getApiPath() {
        return apiPath;
    }

    public void setApiPath(String apiPath) {
        this.apiPath = apiPath;
    }

    public String getApiExtraSetting() {
        return apiExtraSetting;
    }

    public void setApiExtraSetting(String apiExtraSetting) {
        this.apiExtraSetting = apiExtraSetting;
    }

    public String getClientApiExtraSetting() {
        return clientApiExtraSetting;
    }

    public void setClientApiExtraSetting(String clientApiExtraSetting) {
        this.clientApiExtraSetting = clientApiExtraSetting;
    }

    public Long getClientApiId() {
        return clientApiId;
    }

    public void setClientApiId(Long clientApiId) {
        this.clientApiId = clientApiId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public String getConsumeMode() {
        return consumeMode;
    }

    public void setConsumeMode(String consumeMode) {
        this.consumeMode = consumeMode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getApiMode() {
        return apiMode;
    }

    public void setApiMode(String apiMode) {
        this.apiMode = apiMode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    @Override
    public String toString() {
        return "VTbApiSimple{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", consumeMode='" + consumeMode + '\'' +
                ", apiPath='" + apiPath + '\'' +
                ", requestType='" + requestType + '\'' +
                ", apiMode='" + apiMode + '\'' +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                ", apiExtraSetting='" + apiExtraSetting + '\'' +
                ", clientApiExtraSetting='" + clientApiExtraSetting + '\'' +
                ", clientApiId=" + clientApiId +
                '}';
    }
}
