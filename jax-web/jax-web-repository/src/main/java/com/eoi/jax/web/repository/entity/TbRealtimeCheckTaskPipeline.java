package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.eoi.jax.web.repository.base.IBaseEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;

/**
 * <p>
 * 实时数据质量检测任务和管线作业关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@TableName
public class TbRealtimeCheckTaskPipeline extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, IBaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long taskId;

    private Long pipelineId;

    private String status;

    private String reason;

    private Date createTime;

    private Date updateTime;

    private Long createUser;

    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "TbRealtimeCheckTaskPipeline{" +
                "id=" + id +
                ", taskId=" + taskId +
                ", pipelineId=" + pipelineId +
                ", status='" + status + '\'' +
                ", reason='" + reason + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                '}';
    }
}
