package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TDsSchedules;
import com.eoi.jax.web.repository.mapper.TDsSchedulesMapper;
import com.eoi.jax.web.repository.service.TDsSchedulesService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@Service
public class TDsSchedulesServiceImpl extends ServiceImpl<TDsSchedulesMapper, TDsSchedules> implements TDsSchedulesService {

    @Override
    public boolean deleteByProcessDefineCode(Long processDefineCode) {
        LambdaQueryWrapper<TDsSchedules> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TDsSchedules::getProcessDefinitionCode, processDefineCode);
        return remove(queryWrapper);
    }

    @Override
    public TDsSchedules queryByProcessDefineCode(Long processDefineCode) {
        LambdaQueryWrapper<TDsSchedules> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TDsSchedules::getProcessDefinitionCode, processDefineCode);
        return getOne(queryWrapper);
    }

    @Override
    public List<TDsSchedules> querySchedulesByProcessDefinitionCodes(List<Long> processDefinitionCodeList) {
        LambdaQueryWrapper<TDsSchedules> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TDsSchedules::getProcessDefinitionCode, processDefinitionCodeList);
        return list(queryWrapper);
    }

    @Override
    public List<TDsSchedules> querySchedulesByWorkerGroup(String workerGroup) {
        LambdaQueryWrapper<TDsSchedules> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TDsSchedules::getWorkerGroup, workerGroup);
        return list(queryWrapper);
    }
}
