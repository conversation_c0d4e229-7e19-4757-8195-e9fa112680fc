package com.eoi.jax.web.repository.entity;

import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.ProjectAuthorityEntity;

import java.util.Date;

/**
 * <p>
 * 业务流程查询树表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
public class TbBusinessFlowTree extends ProjectAuthorityEntity implements IUserInfoExtensionEntity, IProjectAuthEntity, ILogicDeleteEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 业务流程id
     */
    private Long businessFlowId;

    /**
     * 名称
     */
    private String name;

    /**
     * 树节点类型
     */
    private String category;

    /**
     * 类型
     */
    private String type;

    /**
     * 关联数据类型
     */
    private String referenceType;

    /**
     * 关联数据id
     */
    private Long referenceId;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 父级ids，用/分割
     */
    private String parentPath;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否已发布
     */
    private Integer isPublished;

    /**
     * 不启用这个注解@TableLogic，需要唯一性约束
     */
    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;

    private Long createUser;

    private Long updateUser;
    /**
     * 项目资源类型
     */
    private String resourceType;

    public String getReferenceType() {
        return referenceType;
    }

    public void setReferenceType(String referenceType) {
        this.referenceType = referenceType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(Long referenceId) {
        this.referenceId = referenceId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentPath() {
        return parentPath;
    }

    public void setParentPath(String parentPath) {
        this.parentPath = parentPath;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Integer isPublished) {
        this.isPublished = isPublished;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    @Override
    public String toString() {
        return "TbBusinessFlowTree{" +
                "id=" + id +
                ", businessFlowId=" + businessFlowId +
                ", name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", type='" + type + '\'' +
                ", referenceType='" + referenceType + '\'' +
                ", referenceId=" + referenceId +
                ", parentId=" + parentId +
                ", parentPath='" + parentPath + '\'' +
                ", status='" + status + '\'' +
                ", isPublished=" + isPublished +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                ", resourceType='" + resourceType + '\'' +
                '}';
    }
}
