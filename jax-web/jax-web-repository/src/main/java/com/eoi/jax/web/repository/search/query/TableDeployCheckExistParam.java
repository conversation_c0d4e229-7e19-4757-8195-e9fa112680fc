package com.eoi.jax.web.repository.search.query;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/1/3 17:05
 */
public class TableDeployCheckExistParam {

    private long tableId;

    private List<String> inStatus;

    private String tableName;

    public long getTableId() {
        return tableId;
    }

    public void setTableId(long tableId) {
        this.tableId = tableId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getInStatus() {
        return inStatus;
    }

    public void setInStatus(List<String> inStatus) {
        this.inStatus = inStatus;
    }
}
