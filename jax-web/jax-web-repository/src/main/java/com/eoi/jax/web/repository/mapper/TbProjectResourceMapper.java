package com.eoi.jax.web.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eoi.jax.web.repository.annotation.UserInfoExtensionMapper;
import com.eoi.jax.web.repository.entity.TbProjectResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资源权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Mapper
public interface TbProjectResourceMapper extends UserInfoExtensionMapper<TbProjectResource> {
    void removeBatchByIdsAndType(@Param("ids") List<Long> ids, @Param("type") String resourceType);
}
