package com.eoi.jax.web.repository.search.query;

/**
 * <AUTHOR> zsc
 * @create 2023/1/3 17:05
 */
public class JaxClusterFileLastVersionParam {
    private String type;
    private Long clusterId;
    private boolean includeValueColumn = false;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public boolean isIncludeValueColumn() {
        return includeValueColumn;
    }

    public void setIncludeValueColumn(boolean includeValueColumn) {
        this.includeValueColumn = includeValueColumn;
    }
}
