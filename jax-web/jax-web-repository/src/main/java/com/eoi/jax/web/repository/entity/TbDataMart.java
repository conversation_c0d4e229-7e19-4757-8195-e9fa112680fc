package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eoi.jax.web.repository.base.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:52
 */

@TableName
public class TbDataMart extends ProjectAuthorityEntity
        implements ITreeNodeEntity, IUniqueCodeEntity, IUserInfoExtensionEntity, IProjectAuthEntity, ILogicDeleteEntity {

    @TableId
    private Long id;

    @TableField
    private Long bizId;

    @TableField
    private String code;

    @TableField
    private String name;

    @TableField
    private String martType;

    @TableField
    private String description;

    @TableField
    private Long parentId;

    @TableField
    private String parentPath;

    @TableField
    private Long rootId;

    @TableField
    private Integer isLeaf;

    @TableField
    @TableLogic
    private Integer isDeleted;

    @TableField
    private Date createTime;

    @TableField
    private Date updateTime;

    @TableField
    private Long createUser;

    @TableField
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMartType() {
        return martType;
    }

    public void setMartType(String martType) {
        this.martType = martType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Long getParentId() {
        return parentId;
    }

    @Override
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Override
    public String getParentPath() {
        return parentPath;
    }

    @Override
    public void setParentPath(String parentPath) {
        this.parentPath = parentPath;
    }

    @Override
    public Long getRootId() {
        return rootId;
    }

    @Override
    public void setRootId(Long rootId) {
        this.rootId = rootId;
    }

    @Override
    public Integer getIsLeaf() {
        return isLeaf;
    }

    @Override
    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    @Override
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Override
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
