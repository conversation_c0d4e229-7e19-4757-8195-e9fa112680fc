package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.repository.entity.TbBusinessFlow;
import com.eoi.jax.web.repository.mapper.TbBusinessFlowMapper;
import com.eoi.jax.web.repository.service.TbBusinessFlowService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业务流程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-28
 */
@Service
public class TbBusinessFlowServiceImpl extends ProjectAuthServiceImpl<TbBusinessFlowMapper, TbBusinessFlow> implements TbBusinessFlowService {

    @Override
    public List<TbBusinessFlow> selectByIdList(List<Long> idList) {
        LambdaQueryWrapper<TbBusinessFlow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TbBusinessFlow::getId, idList);
        return baseMapper.selectList(queryWrapper);
    }

}
