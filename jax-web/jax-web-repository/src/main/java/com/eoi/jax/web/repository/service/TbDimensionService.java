package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbDimension;
import com.eoi.jax.web.repository.search.query.VTableQueryParam;
import com.eoi.jax.web.repository.search.result.TableQueryResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:53
 */

public interface TbDimensionService extends IProjectAuthService<TbDimension>, IDaoService<TbDimension> {

    /**
     * 分页查询
     * @param page
     * @param wrapper
     * @param tagIdList
     * @return
     */
    Page<TableQueryResult> queryByCondition(Page<TbDimension> page,
                                            QueryWrapper<TbDimension> wrapper,
                                            List<Long> tagIdList);
}
