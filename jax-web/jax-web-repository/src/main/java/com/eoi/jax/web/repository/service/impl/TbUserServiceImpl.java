package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbUser;
import com.eoi.jax.web.repository.mapper.TbUserMapper;
import com.eoi.jax.web.repository.search.query.UserQueryParam;
import com.eoi.jax.web.repository.service.TbUserService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Service
public class TbUserServiceImpl extends ServiceImpl<TbUserMapper, TbUser> implements TbUserService {

    @Override
    public TbUser getByAccount(String account) {
        LambdaQueryWrapper<TbUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbUser::getAccount, account);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public TbUser getByGuid(String guid) {
        LambdaQueryWrapper<TbUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbUser::getGuid, guid);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public Set<String> getAllGuid() {
        LambdaQueryWrapper<TbUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TbUser::getGuid);
        return baseMapper.selectList(wrapper).stream().map(u -> u.getGuid()).collect(Collectors.toSet());
    }

    @Override
    public List<TbUser> list(UserQueryParam param) {
        return baseMapper.selectByCondition(param);
    }

    @Override
    public Page<TbUser> page(Page<TbUser> page, UserQueryParam param) {
        return baseMapper.pageByCondition(page, param);
    }
}
