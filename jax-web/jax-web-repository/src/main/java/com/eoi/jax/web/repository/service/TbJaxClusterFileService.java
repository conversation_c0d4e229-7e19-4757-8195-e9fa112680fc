package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eoi.jax.web.repository.entity.TbJaxClusterFile;
import com.eoi.jax.web.repository.search.query.JaxClusterFileLastVersionParam;
import com.eoi.jax.web.repository.search.query.JaxClusterFileNotSyncParam;

import java.util.List;

/**
 * <p>
 * 中台集群配置变更记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
public interface TbJaxClusterFileService extends IService<TbJaxClusterFile>, IDaoService<TbJaxClusterFile> {

    /**
     * 查找最后一个版本记录
     *
     * @param type
     * @param recordId
     * @param fileName
     * @return
     */
    TbJaxClusterFile getLastVersionByTypeAndRecordId(String type, Long recordId, String fileName);

    /**
     * 查询某节点所有未同步的文件
     *
     * @param param
     * @return
     */
    List<TbJaxClusterFile> getNodeNotSyncFileList(JaxClusterFileNotSyncParam param);

    /**
     * 查询所有版本
     *
     * @param type
     * @param recordId
     * @param fileName
     * @return
     */
    List<TbJaxClusterFile> getAllVersions(String type, Long recordId, String fileName);

    /**
     * 查询最新版本
     *
     * @param param
     * @return
     */
    List<TbJaxClusterFile> getLastVersionList(JaxClusterFileLastVersionParam param);
}
