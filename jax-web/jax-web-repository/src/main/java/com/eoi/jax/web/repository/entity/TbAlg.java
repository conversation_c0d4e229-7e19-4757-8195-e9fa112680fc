package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eoi.jax.web.repository.annotation.EntityNameField;
import com.eoi.jax.web.repository.base.IBaseEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-02-28 10:19:18
 */

@TableName
public class TbAlg extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, IBaseEntity {

    @TableId
    private Long id;

    @TableField
    private String algClass;

    @TableField
    @EntityNameField
    private String algName;

    @TableField
    private String algAlias;

    @TableField
    private String algVersion;

    @TableField
    private String algDescription;

    @TableField
    private String algDetectType;

    @TableField
    private String algProcessType;

    @TableField
    private String algDataType;

    @TableField
    private String algTrainJob;

    @TableField
    private String algTrainType;

    @TableField
    private String algTrainDataLength;

    @TableField
    private String algParameters;

    @TableField
    private Long jarId;

    @TableField
    private Integer supportMetricStreamingJob;

    @TableField
    private Integer supportMetricBatchJob;

    @TableField
    private Date createTime;

    @TableField
    private Date updateTime;

    @TableField
    private Long createUser;

    @TableField
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getAlgClass() {
        return algClass;
    }

    public void setAlgClass(String algClass) {
        this.algClass = algClass;
    }

    public String getAlgName() {
        return algName;
    }

    public void setAlgName(String algName) {
        this.algName = algName;
    }

    public String getAlgAlias() {
        return algAlias;
    }

    public void setAlgAlias(String algAlias) {
        this.algAlias = algAlias;
    }

    public String getAlgVersion() {
        return algVersion;
    }

    public void setAlgVersion(String algVersion) {
        this.algVersion = algVersion;
    }

    public String getAlgDescription() {
        return algDescription;
    }

    public void setAlgDescription(String algDescription) {
        this.algDescription = algDescription;
    }

    public String getAlgDetectType() {
        return algDetectType;
    }

    public void setAlgDetectType(String algDetectType) {
        this.algDetectType = algDetectType;
    }

    public String getAlgProcessType() {
        return algProcessType;
    }

    public void setAlgProcessType(String algProcessType) {
        this.algProcessType = algProcessType;
    }

    public String getAlgDataType() {
        return algDataType;
    }

    public void setAlgDataType(String algDataType) {
        this.algDataType = algDataType;
    }

    public String getAlgTrainJob() {
        return algTrainJob;
    }

    public void setAlgTrainJob(String algTrainJob) {
        this.algTrainJob = algTrainJob;
    }

    public String getAlgTrainType() {
        return algTrainType;
    }

    public void setAlgTrainType(String algTrainType) {
        this.algTrainType = algTrainType;
    }

    public String getAlgTrainDataLength() {
        return algTrainDataLength;
    }

    public void setAlgTrainDataLength(String algTrainDataLength) {
        this.algTrainDataLength = algTrainDataLength;
    }

    public String getAlgParameters() {
        return algParameters;
    }

    public void setAlgParameters(String algParameters) {
        this.algParameters = algParameters;
    }

    public Long getJarId() {
        return jarId;
    }

    public void setJarId(Long jarId) {
        this.jarId = jarId;
    }

    public Integer getSupportMetricStreamingJob() {
        return supportMetricStreamingJob;
    }

    public void setSupportMetricStreamingJob(Integer supportMetricStreamingJob) {
        this.supportMetricStreamingJob = supportMetricStreamingJob;
    }

    public Integer getSupportMetricBatchJob() {
        return supportMetricBatchJob;
    }

    public void setSupportMetricBatchJob(Integer supportMetricBatchJob) {
        this.supportMetricBatchJob = supportMetricBatchJob;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
