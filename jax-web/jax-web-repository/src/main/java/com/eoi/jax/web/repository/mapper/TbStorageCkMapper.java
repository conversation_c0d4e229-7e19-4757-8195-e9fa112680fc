package com.eoi.jax.web.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.annotation.ProjectAuthAnnotation;
import com.eoi.jax.web.repository.annotation.ProjectAuthMapper;
import com.eoi.jax.web.repository.annotation.UserInfoExtensionMapper;
import com.eoi.jax.web.repository.entity.TbStorageCk;
import com.eoi.jax.web.repository.search.query.StorageCkMaintenanceParam;
import com.eoi.jax.web.repository.search.query.StorageCkUpdateBatchParam;
import com.eoi.jax.web.repository.search.result.StorageCkMaintenanceResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 存储作业表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Mapper
public interface TbStorageCkMapper extends ProjectAuthMapper<TbStorageCk>, UserInfoExtensionMapper<TbStorageCk> {
    /**
     * 批量更新状态
     *
     * @param param
     */
    void updateStatusBatch(StorageCkUpdateBatchParam param);

    /**
     * 查询作业运维信息
     *
     * @param param
     * @return
     */
    @ProjectAuthAnnotation()
    Page<StorageCkMaintenanceResult> getMaintenanceList(Page<StorageCkMaintenanceResult> page,
                                                        @Param("param") StorageCkMaintenanceParam param);

    @ProjectAuthAnnotation(returnProjectAuthField = false)
    @Override
    Long selectCount(@Param(Constants.WRAPPER) Wrapper<TbStorageCk> queryWrapper);

}
