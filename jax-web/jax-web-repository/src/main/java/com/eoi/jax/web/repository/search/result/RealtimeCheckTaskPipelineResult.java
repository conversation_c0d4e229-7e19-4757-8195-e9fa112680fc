package com.eoi.jax.web.repository.search.result;

import com.eoi.jax.web.repository.entity.TbRealtimeCheckTaskPipeline;

/**
 * <AUTHOR>
 * @date 2024/10/10
 */
public class RealtimeCheckTaskPipelineResult extends TbRealtimeCheckTaskPipeline {

    private Long processId;

    private String pipelineName;

    private String processType;

    private String pipelineType;

    private String pipelineAlias;

    private String pipelineDescription;

    private String pipelineStatus;

    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public String getProcessType() {
        return processType;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public String getPipelineType() {
        return pipelineType;
    }

    public void setPipelineType(String pipelineType) {
        this.pipelineType = pipelineType;
    }

    public String getPipelineAlias() {
        return pipelineAlias;
    }

    public void setPipelineAlias(String pipelineAlias) {
        this.pipelineAlias = pipelineAlias;
    }

    public String getPipelineDescription() {
        return pipelineDescription;
    }

    public void setPipelineDescription(String pipelineDescription) {
        this.pipelineDescription = pipelineDescription;
    }

    public String getPipelineStatus() {
        return pipelineStatus;
    }

    public void setPipelineStatus(String pipelineStatus) {
        this.pipelineStatus = pipelineStatus;
    }
}
