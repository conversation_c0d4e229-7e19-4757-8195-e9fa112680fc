package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.search.query.TableDeployCheckExistParam;

import java.util.List;

/**
 * <p>
 * 数据模型发布表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-30
 */
public interface TbTableDeployService extends IService<TbTableDeploy>, IDaoService<TbTableDeploy> {

    /**
     * 获取最新一条记录（包括已删除的记录）
     *
     * @param tableId
     * @return
     */
    TbTableDeploy getLastByTableId(Long tableId);

    /**
     * 检查是否存在符合条件的记录
     *
     * @param param
     * @return
     */
    boolean checkIfExist(TableDeployCheckExistParam param);

    /**
     * 检查是否存在符合条件的记录并返回最后一条数据
     *
     * @param param
     * @return
     */
    TbTableDeploy checkIfExistAndReturn(TableDeployCheckExistParam param);

    /**
     * 根据业务流程统计id统计调速
     */
    Long selectCountByBusinessFlowId(Long businessFlowId);

    /**
     * 获取表的最新发布状态
     *
     * @return
     */
    List<TbTableDeploy> selectLatestTableDeployStatus();

    /**
     * 根据id获取
     *
     * @param id
     * @return
     */
    TbTableDeploy getById(Long id);

    /**
     * 获取最新发布成功的模型列表
     *
     * @param platform
     * @return
     */
    List<TbTableDeploy> getLastSuccessList(String platform);


    /**
     * 获取关联维度表的最后发布成功记录
     *
     * @param dimTbId
     * @return
     */
    List<TbTableDeploy> getLastSuccessRelateDimTableDeploy(Long dimTbId);

    /**
     * 获取最后一条发布记录列表
     *
     * @param platform
     * @param status
     * @param tbIdList
     * @param tbNameList
     * @return
     */
    List<TbTableDeploy> getLastDeployList(String platform, String status,
                                          List<Long> tbIdList,
                                          List<String> tbNameList,
                                          Long dsId,
                                          Boolean isOnline);


    /**
     * 获取最后一条发布成功的记录
     *
     * @param tbId
     * @return
     */
    TbTableDeploy getLastSuccessTableDeploy(long tbId);

    /**
     * 获取最后一条发布成功的记录
     *
     * @param tbName
     * @return
     */
    TbTableDeploy getLastSuccessTableDeploy(String tbName);

    /**
     * 获取表的最新发布状态
     *
     * @return
     */
    List<TbTableDeploy> selectLatestTableDeployStatusByPlatform(String platform);

    /**
     * 根据tbId删除所有发布记录
     *
     * @param tableId
     * @return
     */
    List<TbTableDeploy> deleteByTableId(Long tableId);

    /**
     * 检查模型是否已下线
     * @param tableId
     * @param tableType
     * @return
     */
    Boolean checkTableIsOffline(Long tableId, String tableType);

    /**
     * 根据条件查询最后一条记录
     * @param type
     * @param queryWrapper
     * @return
     */
    List<TbTableDeploy> selectLastByQueryWrapper(String type, Integer isOnline, QueryWrapper<TbTableDeploy> queryWrapper);

    /**
     * 已发布的对象模型
     *
     * @param platform
     * @param dsId
     * @return
     */
    List<TbTableDeploy> getDeployedObjectTables(String platform, Long dsId);
}

