package com.eoi.jax.web.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eoi.jax.web.repository.annotation.UserInfoExtensionAnnotation;
import com.eoi.jax.web.repository.annotation.UserInfoExtensionMapper;
import com.eoi.jax.web.repository.entity.TbObjectRelationType;
import com.eoi.jax.web.repository.search.query.TableObjectExport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 对象模型关系类型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Mapper
public interface TbObjectRelationTypeMapper extends UserInfoExtensionMapper<TbObjectRelationType> {

    @UserInfoExtensionAnnotation
    List<TbObjectRelationType> exportData(@Param("param") TableObjectExport parm);
}
