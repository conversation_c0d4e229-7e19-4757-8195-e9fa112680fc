package com.eoi.jax.web.repository.entity;

import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 健康检查实例信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
public class TbHealthCheckInstance extends UserInfoExtensionEntity implements IUserInfoExtensionEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 检查id
     */
    private Long checkId;

    /**
     * 检查项id
     */
    private Long itemId;

    /**
     * 实例Id
     */
    private Long instanceId;

    /**
     * 实例名称
     */
    private String instanceName;

    /**
     * 实例信息
     */
    private String instanceInfo;

    /**
     * 实例类型
     */
    private String instanceType;

    /**
     * 状态
     */
    private String status;

    /**
     * 异常内容
     */
    private String content;

    /**
     * 解决方案
     */
    private String solution;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人id
     */
    private Long createUser;

    /**
     * 更新人id
     */
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getInstanceInfo() {
        return instanceInfo;
    }

    public void setInstanceInfo(String instanceInfo) {
        this.instanceInfo = instanceInfo;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "TbHealthCheckInstance{" +
            "id=" + id +
            ", checkId=" + checkId +
            ", itemId=" + itemId +
            ", instanceId=" + instanceId +
            ", instanceName='" + instanceName + '\'' +
            ", instanceInfo='" + instanceInfo + '\'' +
            ", instanceType='" + instanceType + '\'' +
            ", status='" + status + '\'' +
            ", content='" + content + '\'' +
            ", solution='" + solution + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", createUser=" + createUser +
            ", updateUser=" + updateUser +
            '}';
    }
}
