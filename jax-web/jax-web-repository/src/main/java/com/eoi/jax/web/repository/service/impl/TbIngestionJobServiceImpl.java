package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbIngestionJob;
import com.eoi.jax.web.repository.mapper.TbIngestionJobMapper;
import com.eoi.jax.web.repository.service.TbIngestionJobService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 数据集成job 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service
public class TbIngestionJobServiceImpl extends ServiceImpl<TbIngestionJobMapper, TbIngestionJob> implements TbIngestionJobService {


    @Override
    public TbIngestionJob selectByIngestionId(Long ingestionId) {
        LambdaQueryWrapper<TbIngestionJob> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbIngestionJob::getIngestionId, ingestionId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<TbIngestionJob> selectByIngestionIdList(Long cellId, List<Long> idList) {
        LambdaQueryWrapper<TbIngestionJob> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbIngestionJob::getCellId, cellId);
        queryWrapper.in(TbIngestionJob::getId, idList);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public boolean updateOperation(TbIngestionJob tbIngestionJob) {
        LambdaUpdateWrapper<TbIngestionJob> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.set(TbIngestionJob::getOperation, tbIngestionJob.getOperation());
        queryWrapper.set(Objects.nonNull(tbIngestionJob.getUpdateTime()), TbIngestionJob::getUpdateTime, tbIngestionJob.getUpdateTime());
        queryWrapper.set(Objects.nonNull(tbIngestionJob.getUpdateUser()), TbIngestionJob::getUpdateUser, tbIngestionJob.getUpdateUser());
        queryWrapper.eq(TbIngestionJob::getId, tbIngestionJob.getId());
        return update(queryWrapper);
    }

    @Override
    public boolean updateStatus(TbIngestionJob tbIngestionJob) {
        LambdaUpdateWrapper<TbIngestionJob> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.set(TbIngestionJob::getStatus, tbIngestionJob.getStatus());
        queryWrapper.set(TbIngestionJob::getTaskStatusStatistics, tbIngestionJob.getTaskStatusStatistics());
        queryWrapper.set(Objects.nonNull(tbIngestionJob.getUpdateTime()), TbIngestionJob::getUpdateTime, tbIngestionJob.getUpdateTime());
        queryWrapper.set(Objects.nonNull(tbIngestionJob.getUpdateUser()), TbIngestionJob::getUpdateUser, tbIngestionJob.getUpdateUser());
        queryWrapper.eq(TbIngestionJob::getId, tbIngestionJob.getId());
        return update(queryWrapper);
    }
}
