package com.eoi.jax.web.repository.search.result;

import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.entity.TbTableDeploy;

/**
 * @Author: yaru.ma
 * @Date: 2023/12/1
 */
public class TableDeployWithAuthResult extends TbTableDeploy implements IProjectAuthEntity {

    protected Boolean projectAuthIsOwner;

    protected Long projectAuthProjectId;

    protected String projectAuthPrivilege;

    protected String projectAuthOwnerProjectName;

    protected Long projectAuthOwnerProjectId;

    protected String projectAuthResourceType;

    public Boolean getProjectAuthIsOwner() {
        return projectAuthIsOwner;
    }

    public void setProjectAuthIsOwner(Boolean projectAuthIsOwner) {
        this.projectAuthIsOwner = projectAuthIsOwner;
    }

    public Long getProjectAuthProjectId() {
        return projectAuthProjectId;
    }

    public void setProjectAuthProjectId(Long projectAuthProjectId) {
        this.projectAuthProjectId = projectAuthProjectId;
    }

    public String getProjectAuthPrivilege() {
        return projectAuthPrivilege;
    }

    public void setProjectAuthPrivilege(String projectAuthPrivilege) {
        this.projectAuthPrivilege = projectAuthPrivilege;
    }

    public String getProjectAuthOwnerProjectName() {
        return projectAuthOwnerProjectName;
    }

    public void setProjectAuthOwnerProjectName(String projectAuthOwnerProjectName) {
        this.projectAuthOwnerProjectName = projectAuthOwnerProjectName;
    }

    public Long getProjectAuthOwnerProjectId() {
        return projectAuthOwnerProjectId;
    }

    public void setProjectAuthOwnerProjectId(Long projectAuthOwnerProjectId) {
        this.projectAuthOwnerProjectId = projectAuthOwnerProjectId;
    }

    public String getProjectAuthResourceType() {
        return projectAuthResourceType;
    }

    public void setProjectAuthResourceType(String projectAuthResourceType) {
        this.projectAuthResourceType = projectAuthResourceType;
    }
}
