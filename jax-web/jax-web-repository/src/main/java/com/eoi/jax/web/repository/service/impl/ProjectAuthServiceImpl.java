package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.annotation.ProjectAuthMapper;
import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.service.IProjectAuthService;

/**
 * @Author: tangy
 * @Date: 2023/10/13
 * @Desc:
 **/
public class ProjectAuthServiceImpl<M extends ProjectAuthMapper<T>, T extends IProjectAuthEntity> extends ServiceImpl<M, T>
        implements IProjectAuthService<T> {

}
