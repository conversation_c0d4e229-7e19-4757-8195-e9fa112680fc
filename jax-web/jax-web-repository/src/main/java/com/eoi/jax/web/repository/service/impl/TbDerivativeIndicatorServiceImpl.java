package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbDerivativeIndicator;
import com.eoi.jax.web.repository.search.result.VDerivativeIndicator;
import com.eoi.jax.web.repository.mapper.TbDerivativeIndicatorMapper;
import com.eoi.jax.web.repository.mapper.VDerivativeIndicatorExample;
import com.eoi.jax.web.repository.service.TbDerivativeIndicatorService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:53
 */

@Service
public class TbDerivativeIndicatorServiceImpl extends ProjectAuthServiceImpl<TbDerivativeIndicatorMapper, TbDerivativeIndicator> implements TbDerivativeIndicatorService {

    @Override
    public Page<VDerivativeIndicator> queryByCondition(Page<VDerivativeIndicator> objectPage, VDerivativeIndicatorExample vDerivativeIndicatorExample) {
        return baseMapper.queryByCondition(objectPage,vDerivativeIndicatorExample);
    }

    @Override
    public List<VDerivativeIndicator> queryByIds(List<Long> ids) {
        return baseMapper.queryByIds(ids);
    }

    @Override
    public List<VDerivativeIndicator> queryByCondition(VDerivativeIndicatorExample vDerivativeIndicatorExample) {
        return baseMapper.queryByCondition(vDerivativeIndicatorExample);
    }
}
