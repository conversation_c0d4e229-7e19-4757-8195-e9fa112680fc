package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbTable;
import com.eoi.jax.web.repository.search.query.VTableQueryParam;
import com.eoi.jax.web.repository.search.result.TableQueryResult;
import com.eoi.jax.web.repository.search.result.VTable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:53
 */

public interface TbTableService extends IProjectAuthService<TbTable>, IDaoService<TbTable> {

    /**
     * 根据表名查找模型
     *
     * @param tableName
     * @return
     */
    List<TbTable> getByTableName(String tableName);

    /**
     * 根据id查询表
     *
     * @return
     */
    VTable queryTableById(Long tbId);

    /**
     * 根据id查询表
     *
     * @return
     */
    List<VTable> queryTables(Long ruleId);

    /**
     * 分页查询
     *
     * @param objectPage
     * @param queryParam
     * @return
     */
    Page<TableQueryResult> queryByCondition(Page<TableQueryResult> objectPage, VTableQueryParam queryParam);
}
