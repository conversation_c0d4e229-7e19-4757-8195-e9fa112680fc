package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.eoi.jax.web.repository.base.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:52
 */

@TableName
public class TbColumnDict extends ProjectAuthorityEntity
        implements IUniqueCodeEntity, ILogicDeleteEntity, IUserInfoExtensionEntity, IProjectAuthEntity {

    @TableId
    private Long id;

    @TableField
    private Long groupId;

    @TableField
    private String no;

    @TableField
    private String code;

    @TableField
    private String name;

    @TableField
    private String nameEn;

    @TableField
    private String colType;

    @TableField
    private String colLength;

    @TableField
    private String numPrecision;

    @TableField
    private String defaultValue;

    @TableField
    private String bizDef;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long enumId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long measureUnitId;

    @TableField
    @TableLogic
    private Integer isDeleted;

    @TableField
    private Date createTime;

    @TableField
    private Date updateTime;

    @TableField
    private Long createUser;

    @TableField
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColLength() {
        return colLength;
    }

    public void setColLength(String colLength) {
        this.colLength = colLength;
    }

    public String getNumPrecision() {
        return numPrecision;
    }

    public void setNumPrecision(String numPrecision) {
        this.numPrecision = numPrecision;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getBizDef() {
        return bizDef;
    }

    public void setBizDef(String bizDef) {
        this.bizDef = bizDef;
    }

    public Long getEnumId() {
        return enumId;
    }

    public void setEnumId(Long enumId) {
        this.enumId = enumId;
    }

    @Override
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Override
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Long getMeasureUnitId() {
        return measureUnitId;
    }

    public void setMeasureUnitId(Long measureUnitId) {
        this.measureUnitId = measureUnitId;
    }
}
