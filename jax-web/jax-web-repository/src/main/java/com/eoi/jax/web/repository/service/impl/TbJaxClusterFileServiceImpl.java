package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbJaxClusterFile;
import com.eoi.jax.web.repository.mapper.TbJaxClusterFileMapper;
import com.eoi.jax.web.repository.search.query.JaxClusterFileLastVersionParam;
import com.eoi.jax.web.repository.search.query.JaxClusterFileNotSyncParam;
import com.eoi.jax.web.repository.service.TbJaxClusterFileService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 中台集群配置变更记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Service
public class TbJaxClusterFileServiceImpl extends ServiceImpl<TbJaxClusterFileMapper, TbJaxClusterFile> implements TbJaxClusterFileService {

    @Override
    public TbJaxClusterFile getLastVersionByTypeAndRecordId(String type, Long recordId, String fileName) {
        LambdaQueryWrapper<TbJaxClusterFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbJaxClusterFile::getType, type);
        queryWrapper.eq(TbJaxClusterFile::getRecordId, recordId);
        queryWrapper.eq(TbJaxClusterFile::getName, fileName);
        queryWrapper.orderByDesc(TbJaxClusterFile::getVersion);
        queryWrapper.last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<TbJaxClusterFile> getNodeNotSyncFileList(JaxClusterFileNotSyncParam param) {
        return baseMapper.getNodeNotSyncFileList(param);
    }

    @Override
    public List<TbJaxClusterFile> getAllVersions(String type, Long recordId, String fileName) {
        LambdaQueryWrapper<TbJaxClusterFile> wrapperUser = new LambdaQueryWrapper<>();
        wrapperUser.select(TbJaxClusterFile::getId,
                TbJaxClusterFile::getName, TbJaxClusterFile::getVersion,
                TbJaxClusterFile::getCreateTime, TbJaxClusterFile::getUpdateTime,
                TbJaxClusterFile::getCreateUser, TbJaxClusterFile::getUpdateUser,
                TbJaxClusterFile::getRecordId, TbJaxClusterFile::getType,
                TbJaxClusterFile::getFilePath, TbJaxClusterFile::getValueMd5
        );
        wrapperUser.eq(TbJaxClusterFile::getType, type);
        wrapperUser.eq(TbJaxClusterFile::getRecordId, recordId);
        wrapperUser.eq(TbJaxClusterFile::getName, fileName);
        wrapperUser.orderByDesc(TbJaxClusterFile::getVersion);
        return baseMapper.selectList(wrapperUser);
    }

    @Override
    public List<TbJaxClusterFile> getLastVersionList(JaxClusterFileLastVersionParam param) {
        return baseMapper.getLastVersionList(param);
    }
}
