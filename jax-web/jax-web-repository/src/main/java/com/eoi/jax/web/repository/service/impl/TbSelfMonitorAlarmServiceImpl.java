package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbSelfMonitorAlarm;
import com.eoi.jax.web.repository.mapper.TbSelfMonitorAlarmMapper;
import com.eoi.jax.web.repository.service.TbSelfMonitorAlarmService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 告警表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Service
public class TbSelfMonitorAlarmServiceImpl
        extends ServiceImpl<TbSelfMonitorAlarmMapper, TbSelfMonitorAlarm>
        implements TbSelfMonitorAlarmService {

    @Override
    public TbSelfMonitorAlarm getActiveAlarmByObjectRule(Long objectId, Long ruleId, String instanceName, String status) {
        return getOne(new LambdaQueryWrapper<TbSelfMonitorAlarm>()
                .eq(TbSelfMonitorAlarm::getObjectId, objectId)
                .eq(instanceName != null, TbSelfMonitorAlarm::getInstanceName, instanceName)
                .eq(TbSelfMonitorAlarm::getRuleId, ruleId)
                .eq(TbSelfMonitorAlarm::getStatus, status)
                .last("limit 1"));
    }

    @Override
    public TbSelfMonitorAlarm getActiveAlarmByUid(Long alarmId, String status) {
        return getOne(new LambdaQueryWrapper<TbSelfMonitorAlarm>()
                .eq(TbSelfMonitorAlarm::getUid, alarmId).eq(TbSelfMonitorAlarm::getStatus, status)
                .last("limit 1"));
    }

    @Override
    public IPage<TbSelfMonitorAlarm> selectCustomPage(Page<TbSelfMonitorAlarm> page,
                                                      QueryWrapper<TbSelfMonitorAlarm> wrapper) {
        return baseMapper.selectCustomPage(page, wrapper);
    }


    public long countByCondition(QueryWrapper<TbSelfMonitorAlarm> wrapper) {
        return baseMapper.countByCondition(wrapper);
    }

    @Override
    public Map<String, Object> activeAlarmStatistics() {
        List<Map<String, Object>> statistics = baseMapper.activeAlarmStatistics();
        return statistics.stream().collect(Collectors.toMap(x -> x.get("severity").toString(), x -> x.get("severityCount")));
    }

    @Override
    public List<TbSelfMonitorAlarm> objectStatistics(QueryWrapper<TbSelfMonitorAlarm> wrapper) {
        return baseMapper.objectStatistics(wrapper);
    }
}
