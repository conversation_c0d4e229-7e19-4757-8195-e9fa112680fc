package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:53
 */

@TableName
public class TbEnumValue extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, ILogicDeleteEntity {

    @TableId
    private Long id;

    @TableField
    private Long enumId;

    @TableField
    private String enumValue;

    @TableField
    private String name;

    @TableField
    private String nameEn;

    @TableField
    private String description;

    @TableField
    @TableLogic
    private Integer isDeleted;

    @TableField
    private Date createTime;

    @TableField
    private Date updateTime;

    @TableField
    private Long createUser;

    @TableField
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnumId() {
        return enumId;
    }

    public void setEnumId(Long enumId) {
        this.enumId = enumId;
    }

    public String getEnumValue() {
        return enumValue;
    }

    public void setEnumValue(String enumValue) {
        this.enumValue = enumValue;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Override
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
