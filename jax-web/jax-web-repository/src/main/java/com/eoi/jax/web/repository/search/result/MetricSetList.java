package com.eoi.jax.web.repository.search.result;

import com.eoi.jax.web.repository.entity.TbMetricSet;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
public class MetricSetList extends TbMetricSet {

    private Long itemCount;

    private String objName;

    private String objNameEn;

    private String objTbName;

    private String objTypeCode;

    public Long getItemCount() {
        return itemCount;
    }

    public void setItemCount(Long itemCount) {
        this.itemCount = itemCount;
    }

    public String getObjName() {
        return objName;
    }

    public void setObjName(String objName) {
        this.objName = objName;
    }

    public String getObjNameEn() {
        return objNameEn;
    }

    public void setObjNameEn(String objNameEn) {
        this.objNameEn = objNameEn;
    }

    public String getObjTbName() {
        return objTbName;
    }

    public void setObjTbName(String objTbName) {
        this.objTbName = objTbName;
    }

    public String getObjTypeCode() {
        return objTypeCode;
    }

    public void setObjTypeCode(String objTypeCode) {
        this.objTypeCode = objTypeCode;
    }
}
