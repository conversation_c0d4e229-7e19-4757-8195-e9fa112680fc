package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eoi.jax.web.repository.entity.TbTableDeployHistory;

/**
 * <p>
 * 数据模型发布历史表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-02
 */
public interface TbTableDeployHistoryService extends IService<TbTableDeployHistory>, IDaoService<TbTableDeployHistory> {

    /**
     * 根据tbId删除所有发布记录
     *
     * @param tableId
     */
    void deleteByTableId(Long tableId);


    /**
     * 根据模型发布Id删除所有发布记录
     *
     * @param tableDeployId
     */
    void deleteByTableDeployId(Long tableDeployId);


}
