package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eoi.jax.web.repository.annotation.EntityNameField;
import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.ProjectAuthorityEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-05-29 15:49:05
 */
@TableName
public class TbFlinkYarnSession extends ProjectAuthorityEntity implements IUserInfoExtensionEntity, IProjectAuthEntity {

    @TableId
    private Long id;

    @TableField
    @EntityNameField
    private String yarnSessionName;

    @TableField
    private String yarnSessionAlias;

    @TableField("jobmanager_memory")
    private Integer jobManagerMemory;

    @TableField("taskmanager_memory")
    private Integer taskManagerMemory;

    @TableField
    private Integer slots;

    @TableField
    private Integer maxMemory;

    @TableField
    private String confList;

    @TableField
    private String status;

    @TableField
    private String description;

    @TableField
    private String yarnAppId;

    @TableField
    private String yarnQueue;

    @TableField
    private String trackUrl;

    @TableField
    private Long clusterId;

    @TableField
    private Long optsId;

    @TableField
    private Integer processing;

    @TableField
    private Date processTime;

    @TableField
    private Integer totalYarnMemory;

    @TableField
    private Integer maxSlots;

    @TableField
    private Integer totalSlots;

    @TableField
    private Integer usedSlots;

    @TableField
    private Integer runningJob;

    @TableField
    private Integer finishedJob;

    @TableField
    private Integer failedJob;

    @TableField
    private Integer canceledJob;

    @TableField
    private Date createTime;

    @TableField
    private Date updateTime;

    @TableField
    private Long createUser;

    @TableField
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getYarnSessionName() {
        return yarnSessionName;
    }

    public void setYarnSessionName(String yarnSessionName) {
        this.yarnSessionName = yarnSessionName;
    }

    public String getYarnSessionAlias() {
        return yarnSessionAlias;
    }

    public void setYarnSessionAlias(String yarnSessionAlias) {
        this.yarnSessionAlias = yarnSessionAlias;
    }

    public Integer getJobManagerMemory() {
        return jobManagerMemory;
    }

    public void setJobManagerMemory(Integer jobManagerMemory) {
        this.jobManagerMemory = jobManagerMemory;
    }

    public Integer getTaskManagerMemory() {
        return taskManagerMemory;
    }

    public void setTaskManagerMemory(Integer taskManagerMemory) {
        this.taskManagerMemory = taskManagerMemory;
    }

    public Integer getSlots() {
        return slots;
    }

    public void setSlots(Integer slots) {
        this.slots = slots;
    }

    public Integer getMaxMemory() {
        return maxMemory;
    }

    public void setMaxMemory(Integer maxMemory) {
        this.maxMemory = maxMemory;
    }

    public String getConfList() {
        return confList;
    }

    public void setConfList(String confList) {
        this.confList = confList;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getYarnAppId() {
        return yarnAppId;
    }

    public void setYarnAppId(String yarnAppId) {
        this.yarnAppId = yarnAppId;
    }

    public String getYarnQueue() {
        return yarnQueue;
    }

    public void setYarnQueue(String yarnQueue) {
        this.yarnQueue = yarnQueue;
    }

    public String getTrackUrl() {
        return trackUrl;
    }

    public void setTrackUrl(String trackUrl) {
        this.trackUrl = trackUrl;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public Long getOptsId() {
        return optsId;
    }

    public void setOptsId(Long optsId) {
        this.optsId = optsId;
    }

    public Integer getProcessing() {
        return processing;
    }

    public void setProcessing(Integer processing) {
        this.processing = processing;
    }

    public Date getProcessTime() {
        return processTime;
    }

    public void setProcessTime(Date processTime) {
        this.processTime = processTime;
    }

    public Integer getTotalYarnMemory() {
        return totalYarnMemory;
    }

    public void setTotalYarnMemory(Integer totalYarnMemory) {
        this.totalYarnMemory = totalYarnMemory;
    }

    public Integer getMaxSlots() {
        return maxSlots;
    }

    public void setMaxSlots(Integer maxSlots) {
        this.maxSlots = maxSlots;
    }

    public Integer getTotalSlots() {
        return totalSlots;
    }

    public void setTotalSlots(Integer totalSlots) {
        this.totalSlots = totalSlots;
    }

    public Integer getUsedSlots() {
        return usedSlots;
    }

    public void setUsedSlots(Integer usedSlots) {
        this.usedSlots = usedSlots;
    }

    public Integer getRunningJob() {
        return runningJob;
    }

    public void setRunningJob(Integer runningJob) {
        this.runningJob = runningJob;
    }

    public Integer getFinishedJob() {
        return finishedJob;
    }

    public void setFinishedJob(Integer finishedJob) {
        this.finishedJob = finishedJob;
    }

    public Integer getFailedJob() {
        return failedJob;
    }

    public void setFailedJob(Integer failedJob) {
        this.failedJob = failedJob;
    }

    public Integer getCanceledJob() {
        return canceledJob;
    }

    public void setCanceledJob(Integer canceledJob) {
        this.canceledJob = canceledJob;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}

