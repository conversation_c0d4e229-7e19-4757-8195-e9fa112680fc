package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbUserGroupRelation;
import com.eoi.jax.web.repository.mapper.TbUserGroupRelationMapper;
import com.eoi.jax.web.repository.service.TbUserGroupRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户组关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Service
public class TbUserGroupRelationServiceImpl extends ServiceImpl<TbUserGroupRelationMapper, TbUserGroupRelation> implements TbUserGroupRelationService {
}
