package com.eoi.jax.web.repository.search.query;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/1/3 17:05
 */
public class StorageCkMaintenanceParam {
    /**
     * ck存储作业名称
     */
    private String name;

    /**
     * 状态
     */
    private List<String> statusList;

    /**
     * 存储集群id
     */
    private Long storageClusterId;

    public StorageCkMaintenanceParam() {
    }

    public StorageCkMaintenanceParam(String name, List<String> statusList, Long storageClusterId) {
        this.name = name;
        this.statusList = statusList;
        this.storageClusterId = storageClusterId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public Long getStorageClusterId() {
        return storageClusterId;
    }

    public void setStorageClusterId(Long storageClusterId) {
        this.storageClusterId = storageClusterId;
    }

}
