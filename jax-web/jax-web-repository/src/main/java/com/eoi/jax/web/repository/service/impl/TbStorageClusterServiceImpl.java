package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbStorageCluster;
import com.eoi.jax.web.repository.mapper.TbStorageClusterMapper;
import com.eoi.jax.web.repository.search.query.StorageClusterParam;
import com.eoi.jax.web.repository.search.result.StorageClusterResult;
import com.eoi.jax.web.repository.service.TbStorageClusterService;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 存储集群表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class TbStorageClusterServiceImpl extends ProjectAuthServiceImpl<TbStorageClusterMapper, TbStorageCluster> implements TbStorageClusterService {

    @Override
    public List<TbStorageCluster> all() {
        QueryWrapper<TbStorageCluster> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(TbStorageCluster::getIsDeleted, 0);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Page<StorageClusterResult> getClusterList(Page<StorageClusterResult> page, StorageClusterParam param) {
        return baseMapper.getClusterList(page, param);
    }

    @Override
    public List<TbStorageCluster> getClusterByStorageCkIds(List<Long> storageCkIds) {
        if (storageCkIds == null || storageCkIds.size() < 1) {
            return new LinkedList<>();
        }
        return baseMapper.getClusterByStorageCkIds(storageCkIds);
    }
}
