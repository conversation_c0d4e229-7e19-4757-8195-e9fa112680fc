package com.eoi.jax.web.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.annotation.UserInfoExtensionAnnotation;
import com.eoi.jax.web.repository.entity.TbAuditLog;
import com.eoi.jax.web.repository.search.query.AuditLogParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Mapper
public interface TbAuditLogMapper extends BaseMapper<TbAuditLog> {

    @UserInfoExtensionAnnotation(createUserColumName = "op_user", ignoreUpdateUser = true)
    TbAuditLog selectById(Serializable id);

    @UserInfoExtensionAnnotation(createUserColumName = "op_user", ignoreUpdateUser = true)
    <P extends IPage<TbAuditLog>> P selectPage(P page, @Param(Constants.WRAPPER) Wrapper<TbAuditLog> queryWrapper);

    @UserInfoExtensionAnnotation(createUserColumName = "op_user", ignoreUpdateUser = true)
    Page<TbAuditLog> queryList(Page page,@Param(Constants.WRAPPER)  AuditLogParam param);
}
