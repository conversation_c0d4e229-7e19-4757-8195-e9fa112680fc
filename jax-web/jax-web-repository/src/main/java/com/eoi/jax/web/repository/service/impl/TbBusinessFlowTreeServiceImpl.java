package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbBusinessFlowTree;
import com.eoi.jax.web.repository.mapper.TbBusinessFlowTreeMapper;
import com.eoi.jax.web.repository.search.query.BusinessFlowTreeUpdateBatchStatusParam;
import com.eoi.jax.web.repository.service.TbBusinessFlowTreeService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 业务流程查询树表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class TbBusinessFlowTreeServiceImpl extends ServiceImpl<TbBusinessFlowTreeMapper, TbBusinessFlowTree> implements TbBusinessFlowTreeService {

    @Resource
    private TbBusinessFlowTreeMapper tbBusinessFlowTreeMapper;

    @Override
    public void updateStatusBatch(BusinessFlowTreeUpdateBatchStatusParam param) {
        Assert.notNull(param, "请求对象不能为空");
        Assert.isTrue(param.getReferenceIds() != null && param.getReferenceIds().size() > 0, "关联对象id列表不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getReferencesType()), "关联对象类型不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getStatus()), "关联对象状态不能为空");
        baseMapper.updateStatusBatch(param);
    }

    @Override
    public IPage<TbBusinessFlowTree> selectListWithProjectAuth(Page<TbBusinessFlowTree> page, Wrapper<TbBusinessFlowTree> queryWrapper) {
        return tbBusinessFlowTreeMapper.selectListWithProjectAuth(page, queryWrapper);
    }

    @Override
    public List<TbBusinessFlowTree> selectObjectDeployWithProjectAuth(String search, String sort, Boolean isAsc) {
        return tbBusinessFlowTreeMapper.selectObjectDeployWithProjectAuth(search, sort, isAsc ? "asc" : "desc");
    }

    /**
     * 判断统一业务流程下是否存在相同名称的节点
     *
     * @param referenceId
     * @param name
     * @param category
     * @param businessFlowId
     * @return
     */
    @Override
    public List<TbBusinessFlowTree> duplicateBusinessNameList(Long referenceId,
                                                                        String name,
                                                                        String category,
                                                                        Long businessFlowId) {
        LambdaQueryWrapper<TbBusinessFlowTree> queryWrapper = Wrappers.<TbBusinessFlowTree>lambdaQuery();
        queryWrapper.eq(TbBusinessFlowTree::getName, name)
            .eq(TbBusinessFlowTree::getCategory, category)
            .eq(TbBusinessFlowTree::getBusinessFlowId, businessFlowId)
            .ne(referenceId != null, TbBusinessFlowTree::getReferenceId, referenceId);

        List<TbBusinessFlowTree> tbBusinessFlowTrees = tbBusinessFlowTreeMapper.selectList(queryWrapper);

        return tbBusinessFlowTrees;
    }


    @Override
    public TbBusinessFlowTree selectByReferenceIdAndResourceType(Long referenceId, String resourceType) {
        LambdaQueryWrapper<TbBusinessFlowTree> queryWrapper = Wrappers.<TbBusinessFlowTree>lambdaQuery();
        queryWrapper.eq(TbBusinessFlowTree::getReferenceId, referenceId)
                .eq(TbBusinessFlowTree::getResourceType, resourceType);
        return getOne(queryWrapper);
    }
}
