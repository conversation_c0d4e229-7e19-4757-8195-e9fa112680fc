package com.eoi.jax.web.repository.search.result;

import java.io.Serializable;

/**
 * @Author: tangy
 * @Date: 2023/5/30
 * @Desc:
 **/
public class AlertTheTopCountResult implements Serializable {
   private String typeId;
    private Integer totalNum;
    private String typeAlias;
    private String typeName;

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public String getTypeAlias() {
        return typeAlias;
    }

    public void setTypeAlias(String typeAlias) {
        this.typeAlias = typeAlias;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
