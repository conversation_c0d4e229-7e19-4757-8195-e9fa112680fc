package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbTableDeployHistory;
import com.eoi.jax.web.repository.mapper.TbTableDeployHistoryMapper;
import com.eoi.jax.web.repository.service.TbTableDeployHistoryService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据模型发布历史表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-02
 */
@Service
public class TbTableDeployHistoryServiceImpl extends ServiceImpl<TbTableDeployHistoryMapper, TbTableDeployHistory> implements TbTableDeployHistoryService {

    @Override
    public void deleteByTableId(Long tableId) {
        baseMapper.delete(new LambdaQueryWrapper<TbTableDeployHistory>().eq(TbTableDeployHistory::getTbId, tableId));
    }

    @Override
    public void deleteByTableDeployId(Long tableDeployId) {
        baseMapper.delete(new LambdaQueryWrapper<TbTableDeployHistory>().eq(TbTableDeployHistory::getTableDeployId, tableDeployId));
    }
}
