package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
public class TDsTaskDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * self-increasing id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * encoding
     */
    private Long code;

    /**
     * task definition name
     */
    private String name;

    /**
     * task definition version
     */
    private Integer version;

    /**
     * description
     */
    private String description;

    /**
     * project code
     */
    private Long projectCode;

    /**
     * task definition creator id
     */
    private Integer userId;

    /**
     * task type
     */
    private String taskType;

    /**
     * task execute type: 0-batch, 1-stream
     */
    private Integer taskExecuteType;

    /**
     * job custom parameters
     */
    private String taskParams;

    /**
     * 0 not available, 1 available
     */
    private Integer flag;

    /**
     * job priority
     */
    private Integer taskPriority;

    /**
     * worker grouping
     */
    private String workerGroup;

    /**
     * environment code
     */
    private Long environmentCode;

    /**
     * number of failed retries
     */
    private Integer failRetryTimes;

    /**
     * failed retry interval
     */
    private Integer failRetryInterval;

    /**
     * timeout flag:0 close, 1 open
     */
    private Integer timeoutFlag;

    /**
     * timeout notification policy: 0 warning, 1 fail
     */
    private Integer timeoutNotifyStrategy;

    /**
     * timeout length,unit: minute
     */
    private Integer timeout;

    /**
     * delay execution time,unit: minute
     */
    private Integer delayTime;

    /**
     * resource id, separated by comma
     */
    private String resourceIds;

    /**
     * task group id
     */
    private Integer taskGroupId;

    /**
     * task group priority
     */
    private Integer taskGroupPriority;

    /**
     * cpuQuota(%): -1:Infinity
     */
    private Integer cpuQuota;

    /**
     * MemoryMax(MB): -1:Infinity
     */
    private Integer memoryMax;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 创建人id
     */
    private Long createUser;

    /**
     * 更新人id
     */
    private Long updateUser;

    private String displayType;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(Long projectCode) {
        this.projectCode = projectCode;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Integer getTaskExecuteType() {
        return taskExecuteType;
    }

    public void setTaskExecuteType(Integer taskExecuteType) {
        this.taskExecuteType = taskExecuteType;
    }

    public String getTaskParams() {
        return taskParams;
    }

    public void setTaskParams(String taskParams) {
        this.taskParams = taskParams;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Integer getTaskPriority() {
        return taskPriority;
    }

    public void setTaskPriority(Integer taskPriority) {
        this.taskPriority = taskPriority;
    }

    public String getWorkerGroup() {
        return workerGroup;
    }

    public void setWorkerGroup(String workerGroup) {
        this.workerGroup = workerGroup;
    }

    public Long getEnvironmentCode() {
        return environmentCode;
    }

    public void setEnvironmentCode(Long environmentCode) {
        this.environmentCode = environmentCode;
    }

    public Integer getFailRetryTimes() {
        return failRetryTimes;
    }

    public void setFailRetryTimes(Integer failRetryTimes) {
        this.failRetryTimes = failRetryTimes;
    }

    public Integer getFailRetryInterval() {
        return failRetryInterval;
    }

    public void setFailRetryInterval(Integer failRetryInterval) {
        this.failRetryInterval = failRetryInterval;
    }

    public Integer getTimeoutFlag() {
        return timeoutFlag;
    }

    public void setTimeoutFlag(Integer timeoutFlag) {
        this.timeoutFlag = timeoutFlag;
    }

    public Integer getTimeoutNotifyStrategy() {
        return timeoutNotifyStrategy;
    }

    public void setTimeoutNotifyStrategy(Integer timeoutNotifyStrategy) {
        this.timeoutNotifyStrategy = timeoutNotifyStrategy;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(Integer delayTime) {
        this.delayTime = delayTime;
    }

    public String getResourceIds() {
        return resourceIds;
    }

    public void setResourceIds(String resourceIds) {
        this.resourceIds = resourceIds;
    }

    public Integer getTaskGroupId() {
        return taskGroupId;
    }

    public void setTaskGroupId(Integer taskGroupId) {
        this.taskGroupId = taskGroupId;
    }

    public Integer getTaskGroupPriority() {
        return taskGroupPriority;
    }

    public void setTaskGroupPriority(Integer taskGroupPriority) {
        this.taskGroupPriority = taskGroupPriority;
    }

    public Integer getCpuQuota() {
        return cpuQuota;
    }

    public void setCpuQuota(Integer cpuQuota) {
        this.cpuQuota = cpuQuota;
    }

    public Integer getMemoryMax() {
        return memoryMax;
    }

    public void setMemoryMax(Integer memoryMax) {
        this.memoryMax = memoryMax;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getDisplayType() {
        return displayType;
    }

    public void setDisplayType(String displayType) {
        this.displayType = displayType;
    }

    @Override
    public String toString() {
        return "TDsTaskDefinition{" +
            "id=" + id +
            ", code=" + code +
            ", name='" + name + '\'' +
            ", version=" + version +
            ", description='" + description + '\'' +
            ", projectCode=" + projectCode +
            ", userId=" + userId +
            ", taskType='" + taskType + '\'' +
            ", taskExecuteType=" + taskExecuteType +
            ", taskParams='" + taskParams + '\'' +
            ", flag=" + flag +
            ", taskPriority=" + taskPriority +
            ", workerGroup='" + workerGroup + '\'' +
            ", environmentCode=" + environmentCode +
            ", failRetryTimes=" + failRetryTimes +
            ", failRetryInterval=" + failRetryInterval +
            ", timeoutFlag=" + timeoutFlag +
            ", timeoutNotifyStrategy=" + timeoutNotifyStrategy +
            ", timeout=" + timeout +
            ", delayTime=" + delayTime +
            ", resourceIds='" + resourceIds + '\'' +
            ", taskGroupId=" + taskGroupId +
            ", taskGroupPriority=" + taskGroupPriority +
            ", cpuQuota=" + cpuQuota +
            ", memoryMax=" + memoryMax +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", createUser=" + createUser +
            ", updateUser=" + updateUser +
            ", displayType='" + displayType + '\'' +
            '}';
    }
}
