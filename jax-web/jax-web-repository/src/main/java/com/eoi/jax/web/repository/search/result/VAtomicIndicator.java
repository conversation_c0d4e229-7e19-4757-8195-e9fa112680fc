package com.eoi.jax.web.repository.search.result;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eoi.jax.web.repository.entity.TbAtomicIndicator;

/**
 * @Author: <PERSON>
 * @Date: 2022/12/7
 **/
public class VAtomicIndicator extends TbAtomicIndicator {

    @TableField
    private String bizName;

    @TableField
    private String domName;

    @TableField
    private String procName;

    @TableField
    private String tbName;

    @TableField
    private String colName;

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getDomName() {
        return domName;
    }

    public void setDomName(String domName) {
        this.domName = domName;
    }

    public String getProcName() {
        return procName;
    }

    public void setProcName(String procName) {
        this.procName = procName;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }
}
