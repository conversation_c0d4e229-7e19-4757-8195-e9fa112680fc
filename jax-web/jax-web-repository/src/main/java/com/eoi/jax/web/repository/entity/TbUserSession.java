package com.eoi.jax.web.repository.entity;

import com.eoi.jax.web.repository.base.IEntity;

import java.util.Date;

/**
 * <p>
 * 用户登录session表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
public class TbUserSession implements IEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名字
     */
    private String name;

    /**
     * guid
     */
    private String guid;

    /**
     * 登录账号
     */
    private String account;

    /**
     * session token
     */
    private String sessionToken;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 登录过期时间
     */
    private Date sessionExpire;

    /**
     * session token刷新时间
     */
    private Date refreshTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    public Date getSessionExpire() {
        return sessionExpire;
    }

    public void setSessionExpire(Date sessionExpire) {
        this.sessionExpire = sessionExpire;
    }

    public Date getRefreshTime() {
        return refreshTime;
    }

    public void setRefreshTime(Date refreshTime) {
        this.refreshTime = refreshTime;
    }

    @Override
    public String toString() {
        return "TbUserSession{" +
        "id=" + id +
        ", userId=" + userId +
        ", name=" + name +
        ", guid=" + guid +
        ", account=" + account +
        ", sessionToken=" + sessionToken +
        ", loginTime=" + loginTime +
        ", sessionExpire=" + sessionExpire +
        ", refreshTime=" + refreshTime +
        "}";
    }
}
