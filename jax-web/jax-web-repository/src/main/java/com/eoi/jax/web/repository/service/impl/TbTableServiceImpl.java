package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbTable;
import com.eoi.jax.web.repository.search.query.VTableQueryParam;
import com.eoi.jax.web.repository.search.result.TableQueryResult;
import com.eoi.jax.web.repository.search.result.VTable;
import com.eoi.jax.web.repository.mapper.TbTableMapper;
import com.eoi.jax.web.repository.service.TbTableService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:53
 */

@Service
public class TbTableServiceImpl extends ProjectAuthServiceImpl<TbTableMapper, TbTable> implements TbTableService {

    @Override
    public List<TbTable> getByTableName(String tableName) {
        LambdaQueryWrapper<TbTable> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbTable::getTbName, tableName);
        queryWrapper.or().eq(TbTable::getDimTbName, tableName);
        queryWrapper.or().eq(TbTable::getIdxTbName, tableName);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public VTable queryTableById(Long tbId) {
        return baseMapper.queryTable(tbId);
    }

    @Override
    public List<VTable> queryTables(Long ruleId) {
        return baseMapper.queryTables(ruleId);
    }

    @Override
    public Page<TableQueryResult> queryByCondition(Page<TableQueryResult> objectPage, VTableQueryParam queryParam) {
        return baseMapper.queryByCondition(objectPage, queryParam);
    }
}
