package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eoi.jax.web.repository.annotation.EntityNameField;
import com.eoi.jax.web.repository.base.IBaseEntity;
import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.ProjectAuthorityEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-03-02 10:46:14
 */

@TableName
public class TbJob extends ProjectAuthorityEntity implements IUserInfoExtensionEntity, IProjectAuthEntity, IBaseEntity {

    @TableId
    private Long id;

    @TableField
    @EntityNameField
    private String jobName;

    @TableField
    private String jobDisplay;

    @TableField
    private String jobType;

    @TableField
    private String jobRole;

    @TableField
    private String jobCategory;

    @TableField
    private String jobDescription;

    @TableField
    private String inTypes;

    @TableField
    private String outTypes;

    @TableField
    private String experimental;

    @TableField
    private String apiVersion;

    @TableField
    private Integer internal;

    private String parameters;

    @TableField
    private Integer hasIcon;

    @TableField
    private Integer hasDoc;

    @TableField(select = false)
    private byte[] icon;

    @TableField(select = false)
    private byte[] doc;

    @TableField
    private Long jarId;

    @TableField
    private Date createTime;

    @TableField
    private Date updateTime;

    @TableField
    private Long createUser;

    @TableField
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobDisplay() {
        return jobDisplay;
    }

    public void setJobDisplay(String jobDisplay) {
        this.jobDisplay = jobDisplay;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public String getJobRole() {
        return jobRole;
    }

    public void setJobRole(String jobRole) {
        this.jobRole = jobRole;
    }

    public String getJobCategory() {
        return jobCategory;
    }

    public void setJobCategory(String jobCategory) {
        this.jobCategory = jobCategory;
    }

    public String getJobDescription() {
        return jobDescription;
    }

    public void setJobDescription(String jobDescription) {
        this.jobDescription = jobDescription;
    }

    public String getInTypes() {
        return inTypes;
    }

    public void setInTypes(String inTypes) {
        this.inTypes = inTypes;
    }

    public String getOutTypes() {
        return outTypes;
    }

    public void setOutTypes(String outTypes) {
        this.outTypes = outTypes;
    }

    public String getExperimental() {
        return experimental;
    }

    public void setExperimental(String experimental) {
        this.experimental = experimental;
    }

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public Integer getInternal() {
        return internal;
    }

    public void setInternal(Integer internal) {
        this.internal = internal;
    }

    public String getParameters() {
        return parameters;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    public Integer getHasIcon() {
        return hasIcon;
    }

    public void setHasIcon(Integer hasIcon) {
        this.hasIcon = hasIcon;
    }

    public Integer getHasDoc() {
        return hasDoc;
    }

    public void setHasDoc(Integer hasDoc) {
        this.hasDoc = hasDoc;
    }

    public byte[] getIcon() {
        return icon;
    }

    public void setIcon(byte[] icon) {
        this.icon = icon;
    }

    public byte[] getDoc() {
        return doc;
    }

    public void setDoc(byte[] doc) {
        this.doc = doc;
    }

    public Long getJarId() {
        return jarId;
    }

    public void setJarId(Long jarId) {
        this.jarId = jarId;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
