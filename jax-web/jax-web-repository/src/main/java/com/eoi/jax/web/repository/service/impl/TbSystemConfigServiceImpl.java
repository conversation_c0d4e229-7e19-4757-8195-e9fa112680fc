package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbSystemConfig;
import com.eoi.jax.web.repository.mapper.TbSystemConfigMapper;
import com.eoi.jax.web.repository.service.TbSystemConfigService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 系统配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
@Service
public class TbSystemConfigServiceImpl extends ServiceImpl<TbSystemConfigMapper, TbSystemConfig> implements TbSystemConfigService {

    @Override
    public List<TbSystemConfig> listByDsIdAndPlatform(Long dsId, String platform) {
        LambdaQueryWrapper<TbSystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(dsId), TbSystemConfig::getDsId, dsId);
        queryWrapper.eq(Objects.nonNull(platform), TbSystemConfig::getPlatform, platform);
        return list(queryWrapper);
    }

    @Override
    public TbSystemConfig getByPrefixKey(String prefixKey) {
        LambdaQueryWrapper<TbSystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbSystemConfig::getPrefixKey, prefixKey);
        return getOne(queryWrapper);
    }

    @Override
    public boolean updateSettingById(Long id, String setting) {
        LambdaUpdateWrapper<TbSystemConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(TbSystemConfig::getSetting, setting);
        updateWrapper.eq(TbSystemConfig::getId, id);
        return update(updateWrapper);
    }


}
