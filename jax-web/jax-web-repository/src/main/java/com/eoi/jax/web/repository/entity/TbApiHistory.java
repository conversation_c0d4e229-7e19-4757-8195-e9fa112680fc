package com.eoi.jax.web.repository.entity;

import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.ProjectAuthorityEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;

/**
 * <p>
 * 接口定义历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
public class TbApiHistory extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, ILogicDeleteEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * api主键
     */
    private Long apiId;

    /**
     * 分组名
     */
    private String name;

    /**
     * api分组id
     */
    private Long apiGroupId;

    /**
     * 消费模式，HTTP-http请求，KAFKA-kafka消费
     */
    private String consumeMode;

    /**
     * api路径
     */
    private String apiPath;

    /**
     * 请求方式，GET-get请求，POST-post请求
     */
    private String requestType;

    /**
     * api模式，standard-标准模式，sql-基础，uq - uq模式，custom-自定义模式
     */
    private String apiMode;

    /**
     * 数据源类型KAFKA, ELASTICSEARCH, CLICKHOUSE, MYSQL
     */
    private String dsType;

    /**
     * 数据源id
     */
    private Long dsId;

    /**
     * kafka主题
     */
    private String topic;

    /**
     * 数据库
     */
    private String databaseName;

    /**
     * 数据表
     */
    private String dataTable;
    /**
     * 自定义模式数据源id集合
     */
    private String customDsId;
    /**
     * 版本
     */
    private Integer version;

    /**
     * 状态：ONLINE 上线,OFFLINE:下线,DRAFT:草稿
     */
    private String status;
    /**
     * 配置信息，api不同模式有不同配置
     */
    private String setting;

    /**
     * 高级配置
     */
    private String extraSetting;

    /**
     * 自定义处理的全限定类名
     */
    private String handleClass;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 是否逻辑删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人Id
     */
    private Long createUser;

    /**
     * 更新人Id
     */
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApiId() {
        return apiId;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getApiGroupId() {
        return apiGroupId;
    }

    public void setApiGroupId(Long apiGroupId) {
        this.apiGroupId = apiGroupId;
    }

    public String getApiPath() {
        return apiPath;
    }

    public void setApiPath(String apiPath) {
        this.apiPath = apiPath;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public Long getDsId() {
        return dsId;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getDataTable() {
        return dataTable;
    }

    public void setDataTable(String dataTable) {
        this.dataTable = dataTable;
    }

    public String getCustomDsId() {
        return customDsId;
    }

    public void setCustomDsId(String customDsId) {
        this.customDsId = customDsId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSetting() {
        return setting;
    }

    public void setSetting(String setting) {
        this.setting = setting;
    }

    public String getExtraSetting() {
        return extraSetting;
    }

    public void setExtraSetting(String extraSetting) {
        this.extraSetting = extraSetting;
    }

    public String getHandleClass() {
        return handleClass;
    }

    public void setHandleClass(String handleClass) {
        this.handleClass = handleClass;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getConsumeMode() {
        return consumeMode;
    }

    public void setConsumeMode(String consumeMode) {
        this.consumeMode = consumeMode;
    }

    public String getApiMode() {
        return apiMode;
    }

    public void setApiMode(String apiMode) {
        this.apiMode = apiMode;
    }

    public String getDsType() {
        return dsType;
    }

    public void setDsType(String dsType) {
        this.dsType = dsType;
    }

    @Override
    public String toString() {
        return "TbApiHistory{" +
                "id=" + id +
                ", apiId=" + apiId +
                ", name='" + name + '\'' +
                ", apiGroupId=" + apiGroupId +
                ", consumeMode='" + consumeMode + '\'' +
                ", apiPath='" + apiPath + '\'' +
                ", requestType='" + requestType + '\'' +
                ", apiMode='" + apiMode + '\'' +
                ", dsType='" + dsType + '\'' +
                ", dsId=" + dsId +
                ", topic='" + topic + '\'' +
                ", databaseName='" + databaseName + '\'' +
                ", dataTable='" + dataTable + '\'' +
                ", customDsId='" + customDsId + '\'' +
                ", version=" + version +
                ", status='" + status + '\'' +
                ", setting='" + setting + '\'' +
                ", extraSetting='" + extraSetting + '\'' +
                ", handleClass='" + handleClass + '\'' +
                ", description='" + description + '\'' +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                '}';
    }
}
