package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.repository.entity.TbPipeline;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-13 16:59:41
 */

public interface TbPipelineService extends IDaoService<TbPipeline> {

    List<TbPipeline> selectByIdList(List<Long> idList);


    IPage<TbPipeline> selectCustomPage(Page<TbPipeline> page,
                                       QueryWrapper<TbPipeline> wrapper,
                                       List<Long> tagIdList);

    IPage<TbPipeline> selectCustomPageNoneAuth(Page<TbPipeline> page,
                                       QueryWrapper<TbPipeline> wrapper,
                                       List<Long> tagIdList);

    TbPipeline selectByProcessId(Long processId);

}
