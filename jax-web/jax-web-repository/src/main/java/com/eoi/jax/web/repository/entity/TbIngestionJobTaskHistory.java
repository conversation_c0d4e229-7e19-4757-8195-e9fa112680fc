package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 数据集成jobTask历史表
 * </p>
 *
 * <AUTHOR> @since 2023-03-06
 */
public class TbIngestionJobTaskHistory extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, Serializable, ILogicDeleteEntity {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long jobTaskId;

    /**
     * 数据集成id
     */
    private Long ingestionId;

    /**
     * 数据集成agentId
     */
    private Long ingestionAgentId;

    /**
     * 数据集成job_id
     */
    private Long ingestionJobId;

    /**
     * 代理节点id
     */
    private Long agentId;

    /**
     * 配置信息，JSON格式
     */
    private String setting;

    /**
     * 发布版本
     */
    private Long publishVersion;

    @TableLogic
    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;

    private Long createUser;

    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobTaskId() {
        return jobTaskId;
    }

    public void setJobTaskId(Long jobTaskId) {
        this.jobTaskId = jobTaskId;
    }

    public Long getIngestionId() {
        return ingestionId;
    }

    public void setIngestionId(Long ingestionId) {
        this.ingestionId = ingestionId;
    }

    public Long getIngestionAgentId() {
        return ingestionAgentId;
    }

    public void setIngestionAgentId(Long ingestionAgentId) {
        this.ingestionAgentId = ingestionAgentId;
    }

    public Long getIngestionJobId() {
        return ingestionJobId;
    }

    public void setIngestionJobId(Long ingestionJobId) {
        this.ingestionJobId = ingestionJobId;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public String getSetting() {
        return setting;
    }

    public void setSetting(String setting) {
        this.setting = setting;
    }

    public Long getPublishVersion() {
        return publishVersion;
    }

    public void setPublishVersion(Long publishVersion) {
        this.publishVersion = publishVersion;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "TbIngestionJobTaskHistory{" +
                "id=" + id +
                ", jobTaskId=" + jobTaskId +
                ", ingestionId=" + ingestionId +
                ", ingestionAgentId=" + ingestionAgentId +
                ", ingestionJobId=" + ingestionJobId +
                ", agentId=" + agentId +
                ", setting=" + setting +
                ", publishVersion=" + publishVersion +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                "}";
    }
}
