package com.eoi.jax.web.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eoi.jax.web.repository.entity.TbStorageHiveHistory;

import java.util.List;

/**
 * <p>
 * Hive存储作业 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
public interface TbStorageHiveHistoryService extends IService<TbStorageHiveHistory> {

    /**
     * 查询最后一个已发布的hive存储作业
     *
     * @param storageHiveId
     * @return
     */
    TbStorageHiveHistory getLatestStorageHiveHistory(Long storageHiveId);

    /**
     * 最新发布的配置
     *
     * @return
     */
    List<TbStorageHiveHistory> selectLastDeployList();
}
