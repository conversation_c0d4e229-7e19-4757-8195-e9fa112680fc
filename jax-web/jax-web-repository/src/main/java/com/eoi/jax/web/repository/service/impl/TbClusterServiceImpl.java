package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbCluster;
import com.eoi.jax.web.repository.mapper.TbClusterMapper;
import com.eoi.jax.web.repository.service.TbClusterService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-21 13:38:21
 */

@Service
public class TbClusterServiceImpl extends ProjectAuthServiceImpl<TbClusterMapper, TbCluster> implements TbClusterService {

    @Override
    public TbCluster getDefaultCluster() {
        QueryWrapper<TbCluster> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TbCluster::getDefaultMarayarnCluster, 1);
        List<TbCluster> clusters = baseMapper.selectList(queryWrapper);
        if (clusters != null && clusters.size() > 0) {
            return clusters.get(0);
        }
        return null;
    }

    @Override
    public TbCluster getByClusterName(String clusterName) {
        QueryWrapper<TbCluster> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TbCluster::getClusterName, clusterName);
        List<TbCluster> clusters = baseMapper.selectList(queryWrapper);
        if (clusters != null && clusters.size() > 0) {
            return clusters.get(0);
        }
        return null;
    }
}
