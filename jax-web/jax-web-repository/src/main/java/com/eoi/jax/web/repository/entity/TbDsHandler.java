package com.eoi.jax.web.repository.entity;

import com.eoi.jax.web.repository.base.IBaseEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;

/**
 * <p>
 * 数据服务自定义实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
public class TbDsHandler extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, IBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 接口名
     */
    private String name;

    /**
     * 类名
     */
    private String className;

    /**
     * 是否开启分页
     */
    private Integer enablePaged;

    /**
     * tb_jar包id
     */
    private Long jarId;

    /**
     * 数据源配置
     */
    private String dsSetting;

    /**
     * 请求参数配置
     */
    private String reqSetting;

    /**
     * 返回值配置
     */
    private String respSetting;

    /**
     * 服务参数配置
     */
    private String serviceSetting;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人Id
     */
    private Long createUser;

    /**
     * 更新人Id
     */
    private Long updateUser;
    /**
     * 是否要访问jax元数据
     *
     * @return
     */
    private Integer accessJaxMeta;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Long getJarId() {
        return jarId;
    }

    public void setJarId(Long jarId) {
        this.jarId = jarId;
    }

    public String getDsSetting() {
        return dsSetting;
    }

    public void setDsSetting(String dsSetting) {
        this.dsSetting = dsSetting;
    }

    public String getReqSetting() {
        return reqSetting;
    }

    public void setReqSetting(String reqSetting) {
        this.reqSetting = reqSetting;
    }

    public String getRespSetting() {
        return respSetting;
    }

    public void setRespSetting(String respSetting) {
        this.respSetting = respSetting;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getEnablePaged() {
        return enablePaged;
    }

    public void setEnablePaged(Integer enablePaged) {
        this.enablePaged = enablePaged;
    }

    public Integer getAccessJaxMeta() {
        return accessJaxMeta == null ? 0 : accessJaxMeta;
    }

    public void setAccessJaxMeta(Integer accessJaxMeta) {
        this.accessJaxMeta = accessJaxMeta;
    }

    public String getServiceSetting() {
        return serviceSetting;
    }

    public void setServiceSetting(String serviceSetting) {
        this.serviceSetting = serviceSetting;
    }

    @Override
    public String toString() {
        return "TbDsHandler{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", className='" + className + '\'' +
                ", enablePaged=" + enablePaged +
                ", jarId=" + jarId +
                ", dsSetting='" + dsSetting + '\'' +
                ", reqSetting='" + reqSetting + '\'' +
                ", respSetting='" + respSetting + '\'' +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                '}';
    }
}
