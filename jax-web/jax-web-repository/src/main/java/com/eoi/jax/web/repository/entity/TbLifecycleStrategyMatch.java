package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.UserInfoExtensionEntity;

import java.util.Date;

/**
 * <p>
 * 生命周期模型匹配情况
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@TableName
public class TbLifecycleStrategyMatch extends UserInfoExtensionEntity implements IUserInfoExtensionEntity, ILogicDeleteEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 批次id
     */
    @TableField
    private Long batchId;

    @TableField
    private Long strategyId;

    /**
     * 模型id
     */
    @TableField
    private Long tbId;

    /**
     * 模型名
     */
    @TableField
    private String tbName;

    /**
     * 模型中文名
     */
    @TableField
    private String tbAlias;

    /**
     * 策略是否应用
     */
    @TableField
    private String status;

    /**
     * 模型未使用该策略原因
     */
    @TableField
    private String reason;

    /**
     * 逻辑删除标志位
     */
    @TableField
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField
    private Date updateTime;

    /**
     * 创建人id
     */
    @TableField
    private Long createUser;

    /**
     * 更新人id
     */
    @TableField
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getTbAlias() {
        return tbAlias;
    }

    public void setTbAlias(String tbAlias) {
        this.tbAlias = tbAlias;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "TbLifecycleStrategyMatch{" +
                "id=" + id +
                ", batchId=" + batchId +
                ", tbId=" + tbId +
                ", tbName=" + tbName +
                ", status=" + status +
                ", reason=" + reason +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                "}";
    }
}
