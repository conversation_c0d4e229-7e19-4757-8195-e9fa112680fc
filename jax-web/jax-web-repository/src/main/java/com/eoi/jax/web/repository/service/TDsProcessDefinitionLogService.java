package com.eoi.jax.web.repository.service;

import com.eoi.jax.web.repository.entity.TDsProcessDefinitionLog;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
public interface TDsProcessDefinitionLogService extends IDaoService<TDsProcessDefinitionLog> {

    /**
     *
     * @param code
     * @return
     */
    Integer selectMaxVersionForDefinition(Long code);

    /**
     *
     * @return
     */
    TDsProcessDefinitionLog selectByCodeAndVersion(Long code, Integer version);

    /**
     * 根据编码和版本更新
     * @param tDsProcessDefinitionLog
     * @return
     */
    Boolean updateByByCodeAndVersion(TDsProcessDefinitionLog tDsProcessDefinitionLog);


    List<TDsProcessDefinitionLog> selectByCode(Long code);

}
