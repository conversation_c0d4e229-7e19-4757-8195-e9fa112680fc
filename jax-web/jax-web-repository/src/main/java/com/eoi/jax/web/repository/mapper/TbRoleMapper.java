package com.eoi.jax.web.repository.mapper;

import com.eoi.jax.web.repository.annotation.UserInfoExtensionMapper;
import com.eoi.jax.web.repository.entity.TbRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Mapper
public interface TbRoleMapper extends UserInfoExtensionMapper<TbRole> {

    /**
     * 根据用户id查询角色
     *
     * @param userId
     * @return
     */
    List<TbRole> getByUserId(@Param("userId") Long userId);
}
