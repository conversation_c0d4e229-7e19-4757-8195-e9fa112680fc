package com.eoi.jax.web.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eoi.jax.web.repository.entity.TbMetricSet;
import com.eoi.jax.web.repository.mapper.TbMetricSetMapper;
import com.eoi.jax.web.repository.search.query.MetricSetTreeParam;
import com.eoi.jax.web.repository.search.result.MetricSetList;
import com.eoi.jax.web.repository.search.result.MetricSetTreeResult;
import com.eoi.jax.web.repository.service.TbMetricSetService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 指标集 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Service
public class TbMetricSetServiceImpl extends ServiceImpl<TbMetricSetMapper, TbMetricSet> implements TbMetricSetService {

    @Override
    public List<MetricSetTreeResult> queryTreeList(MetricSetTreeParam param) {
        if (param == null) {
            param = new MetricSetTreeParam();
        }
        return baseMapper.queryTreeList(param);
    }

    @Override
    public int physicalDeleteById(Long id) {
        return baseMapper.physicalDeleteById(id);
    }

    @Override
    public List<TbMetricSet> getByCategoryId(Long categoryId) {
        LambdaQueryWrapper<TbMetricSet> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TbMetricSet::getCategoryId, categoryId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<MetricSetList> selectCustomPage(Page<TbMetricSet> page,
                                                 QueryWrapper<TbMetricSet> wrapper) {
        return baseMapper.selectCustomPage(page, wrapper);
    }

}
