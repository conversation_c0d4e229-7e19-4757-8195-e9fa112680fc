package com.eoi.jax.web.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.eoi.jax.web.repository.annotation.EntityNameField;
import com.eoi.jax.web.repository.base.ILogicDeleteEntity;
import com.eoi.jax.web.repository.base.IProjectAuthEntity;
import com.eoi.jax.web.repository.base.IUserInfoExtensionEntity;
import com.eoi.jax.web.repository.base.ProjectAuthorityEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-11-19 16:06:53
 */

@TableName
public class TbTable extends ProjectAuthorityEntity
        implements IUserInfoExtensionEntity, IProjectAuthEntity, ILogicDeleteEntity {

    @TableId
    private Long id;

    @TableField
    private String tbType;

    @TableField
    private Integer isOnline;

    @TableField
    private String storageMode;

    @TableField
    private String idxTbName;

    @TableField
    private String dimTbName;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long dimTbId;

    @TableField
    private Integer relateDim;

    @TableField
    private String layerCatalog;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long layerId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long bizId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long dimId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long domId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long procId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long martId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long subjId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String storagePolicy;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long ruleId;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String periodIds;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String adjIds;

    @TableField
    @EntityNameField
    private String tbName;

    @TableField
    private String tbAlias;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long lifecycle;

    @TableField
    private String description;

    @TableField
    @TableLogic
    private Integer isDeleted;

    @TableField
    private Date createTime;

    @TableField
    private Date updateTime;

    @TableField
    private Long createUser;

    @TableField
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getTbType() {
        return tbType;
    }

    public void setTbType(String tbType) {
        this.tbType = tbType;
    }

    public Long getLayerId() {
        return layerId;
    }

    public Integer getRelateDim() {
        return relateDim;
    }

    public void setRelateDim(Integer relateDim) {
        this.relateDim = relateDim;
    }

    public void setLayerId(Long layerId) {
        this.layerId = layerId;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Long getDimId() {
        return dimId;
    }

    public void setDimId(Long dimId) {
        this.dimId = dimId;
    }

    public Long getDomId() {
        return domId;
    }

    public void setDomId(Long domId) {
        this.domId = domId;
    }

    public String getStorageMode() {
        return storageMode;
    }

    public void setStorageMode(String storageMode) {
        this.storageMode = storageMode;
    }

    public String getIdxTbName() {
        return idxTbName;
    }

    public void setIdxTbName(String idxTbName) {
        this.idxTbName = idxTbName;
    }

    public String getDimTbName() {
        return dimTbName;
    }

    public void setDimTbName(String dimTbName) {
        this.dimTbName = dimTbName;
    }

    public Long getDimTbId() {
        return dimTbId;
    }

    public void setDimTbId(Long dimTbId) {
        this.dimTbId = dimTbId;
    }

    public Long getProcId() {
        return procId;
    }

    public void setProcId(Long procId) {
        this.procId = procId;
    }

    public Long getMartId() {
        return martId;
    }

    public void setMartId(Long martId) {
        this.martId = martId;
    }

    public Long getSubjId() {
        return subjId;
    }

    public void setSubjId(Long subjId) {
        this.subjId = subjId;
    }

    public String getStoragePolicy() {
        return storagePolicy;
    }

    public void setStoragePolicy(String storagePolicy) {
        this.storagePolicy = storagePolicy;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getTbAlias() {
        return tbAlias;
    }

    public void setTbAlias(String tbAlias) {
        this.tbAlias = tbAlias;
    }

    public Long getLifecycle() {
        return lifecycle;
    }

    public void setLifecycle(Long lifecycle) {
        this.lifecycle = lifecycle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Override
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getLayerCatalog() {
        return layerCatalog;
    }

    public void setLayerCatalog(String layerCatalog) {
        this.layerCatalog = layerCatalog;
    }

    public String getPeriodIds() {
        return periodIds;
    }

    public void setPeriodIds(String periodIds) {
        this.periodIds = periodIds;
    }

    public String getAdjIds() {
        return adjIds;
    }

    public void setAdjIds(String adjIds) {
        this.adjIds = adjIds;
    }

    public Integer getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(Integer isOnline) {
        this.isOnline = isOnline;
    }
}
