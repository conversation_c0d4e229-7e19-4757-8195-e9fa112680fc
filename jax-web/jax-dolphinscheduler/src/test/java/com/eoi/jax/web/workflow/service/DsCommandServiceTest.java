package com.eoi.jax.web.workflow.service;

import com.eoi.jax.web.repository.entity.TDsCommand;
import com.eoi.jax.web.repository.service.TDsCommandService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class DsCommandServiceTest {

    @Autowired
    private TDsCommandService tDsCommandService;

    @Test
    public void test() {
        TDsCommand tDsCommand = new TDsCommand();
        tDsCommand.setCommandType(0);
        tDsCommand.setProcessDefinitionCode(1L);
        tDsCommand.setProcessDefinitionVersion(0);
        tDsCommand.setProcessInstanceId(0);
        tDsCommand.setCommandParam("");
        tDsCommand.setTaskDependType(0);
        tDsCommand.setFailureStrategy(0);
        tDsCommand.setWarningType(0);
        tDsCommand.setWarningGroupId(0);
        tDsCommand.setScheduleTime(new Date());
        tDsCommand.setStartTime(new Date());
        tDsCommand.setExecutorId(0);
        tDsCommand.setUpdateTime(new Date());
        tDsCommand.setProcessInstancePriority(0);
        tDsCommand.setWorkerGroup("default");
        tDsCommand.setEnvironmentCode(0L);
        tDsCommand.setDryRun(0);
        // 测试插入
        boolean save = tDsCommandService.save(tDsCommand);
        Assert.assertTrue(save);

        // 测试查询
        TDsCommand tDsCommand1 = tDsCommandService.getById(tDsCommand.getId());
        Assert.assertNotNull(tDsCommand1);
        Assert.assertEquals(new Integer(0), tDsCommand1.getCommandType());
        Assert.assertEquals(new Long(1), tDsCommand1.getProcessDefinitionCode());
        Assert.assertEquals(new Integer(0), tDsCommand1.getProcessDefinitionVersion());

        // 测试更新
        tDsCommand.setCommandType(1);
        tDsCommand.setCommandParam("test");
        boolean update = tDsCommandService.updateById(tDsCommand);
        Assert.assertTrue(update);
        TDsCommand tDsCommand2 = tDsCommandService.getById(tDsCommand.getId());
        Assert.assertNotNull(tDsCommand2);
        Assert.assertEquals(new Integer(1), tDsCommand2.getCommandType());
        Assert.assertEquals("test", tDsCommand2.getCommandParam());


        // 测试删除
        boolean remove = tDsCommandService.removeById(tDsCommand.getId());
        Assert.assertTrue(remove);
        TDsCommand tDsCommand3 = tDsCommandService.getById(tDsCommand.getId());
        Assert.assertNull(tDsCommand3);
    }


}
