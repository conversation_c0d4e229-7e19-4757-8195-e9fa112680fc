package com.eoi.jax.web.workflow.service;

import com.eoi.jax.web.repository.entity.TDsTaskDefinitionLog;
import com.eoi.jax.web.repository.service.TDsTaskDefinitionLogService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class DsTaskDefinitionServiceLogTest {

    @Autowired
    private TDsTaskDefinitionLogService tDsTaskDefinitionLogService;

    /**
     * 测试插入，查询，更新，删除
     */
    @Test
    public void test() {
        TDsTaskDefinitionLog tDsTaskDefinitionLog = new TDsTaskDefinitionLog();
        tDsTaskDefinitionLog.setCode(1L);
        tDsTaskDefinitionLog.setName("shell");
        tDsTaskDefinitionLog.setVersion(0);
        tDsTaskDefinitionLog.setDescription("shell");
        tDsTaskDefinitionLog.setProjectCode(-1L);
        tDsTaskDefinitionLog.setUserId(-1);
        tDsTaskDefinitionLog.setTaskType("SHELL");
        tDsTaskDefinitionLog.setTaskExecuteType(0);
        tDsTaskDefinitionLog.setTaskParams("");
        tDsTaskDefinitionLog.setFlag(0);
        tDsTaskDefinitionLog.setTaskPriority(0);
        tDsTaskDefinitionLog.setWorkerGroup("");
        tDsTaskDefinitionLog.setEnvironmentCode(0L);
        tDsTaskDefinitionLog.setFailRetryTimes(0);
        tDsTaskDefinitionLog.setFailRetryInterval(0);
        tDsTaskDefinitionLog.setTimeoutFlag(0);
        tDsTaskDefinitionLog.setTimeoutNotifyStrategy(0);
        tDsTaskDefinitionLog.setTimeout(0);
        tDsTaskDefinitionLog.setDelayTime(0);
        tDsTaskDefinitionLog.setResourceIds("");
        tDsTaskDefinitionLog.setTaskGroupId(0);
        tDsTaskDefinitionLog.setTaskGroupPriority(0);
        tDsTaskDefinitionLog.setCpuQuota(0);
        tDsTaskDefinitionLog.setMemoryMax(0);
        tDsTaskDefinitionLog.setCreateTime(new Date());
        tDsTaskDefinitionLog.setUpdateTime(new Date());
        tDsTaskDefinitionLog.setUpdateUser(0L);
        tDsTaskDefinitionLog.setOperator(0);
        tDsTaskDefinitionLog.setOperateTime(new Date());

        // 测试插入
        boolean save = tDsTaskDefinitionLogService.save(tDsTaskDefinitionLog);
        Assert.assertTrue(save);

        TDsTaskDefinitionLog tDsTaskDefinitionLog1 = tDsTaskDefinitionLogService.getById(tDsTaskDefinitionLog.getId());
        Assert.assertNotNull(tDsTaskDefinitionLog1);
        Assert.assertEquals(new Long(1L), tDsTaskDefinitionLog1.getCode());
        Assert.assertEquals("shell", tDsTaskDefinitionLog1.getName());
        Assert.assertEquals("SHELL", tDsTaskDefinitionLog1.getTaskType());

        // 测试更新
        tDsTaskDefinitionLog.setName("SQL-test");
        tDsTaskDefinitionLog.setTaskType("SQL");
        boolean update = tDsTaskDefinitionLogService.updateById(tDsTaskDefinitionLog);
        Assert.assertTrue(update);
        TDsTaskDefinitionLog tDsTaskDefinitionLog2 = tDsTaskDefinitionLogService.getById(tDsTaskDefinitionLog.getId());
        Assert.assertEquals("SQL-test", tDsTaskDefinitionLog2.getName());
        Assert.assertEquals("SQL", tDsTaskDefinitionLog2.getTaskType());

        // 测试删除
        boolean remove = tDsTaskDefinitionLogService.removeById(tDsTaskDefinitionLog.getId());
        Assert.assertTrue(remove);
        TDsTaskDefinitionLog tDsTaskDefinitionLog3 = tDsTaskDefinitionLogService.getById(tDsTaskDefinitionLog.getId());
        Assert.assertNull(tDsTaskDefinitionLog3);
        

    }
}
