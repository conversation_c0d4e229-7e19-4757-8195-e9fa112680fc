package com.eoi.jax.web.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.repository.entity.TDsProcessDefinitionLog;
import com.eoi.jax.web.repository.service.TDsProcessDefinitionLogService;
import com.eoi.jax.web.workflow.model.processdefinition.ProcessDefinitionResp;
import com.eoi.jax.web.workflow.model.processdefinition.Schedule;
import com.eoi.jax.web.workflow.model.processdefinitionlog.ProcessDefinitionLogQueryFilterReq;
import com.eoi.jax.web.workflow.model.processdefinitionlog.ProcessDefinitionLogQueryReq;
import com.eoi.jax.web.workflow.model.processdefinitionlog.ProcessDefinitionLogQuerySortReq;
import com.eoi.jax.web.workflow.model.taskdefinition.TaskDefinition;
import com.eoi.jax.web.workflow.service.ProcessTaskRelationService;
import com.eoi.jax.web.workflow.service.SchedulesService;
import com.eoi.jax.web.workflow.service.TaskDefinitionService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProcessDefinitionLogServiceImplTest {

    @Mock
    private TDsProcessDefinitionLogService mockTDsProcessDefinitionLogService;
    @Mock
    private SchedulesService mockSchedulesService;
    @Mock
    private TaskDefinitionService mockTaskDefinitionService;
    @Mock
    private ProcessTaskRelationService mockProcessTaskRelationService;

    @InjectMocks
    private ProcessDefinitionLogServiceImpl processDefinitionLogServiceImplUnderTest;

    @Test
    public void testQuery() {
        // Setup
        final ProcessDefinitionLogQueryReq req = new ProcessDefinitionLogQueryReq();
        final ProcessDefinitionLogQueryFilterReq filter = new ProcessDefinitionLogQueryFilterReq();
        filter.setCode(0L);
        req.setFilter(filter);
        final ProcessDefinitionLogQuerySortReq sort = new ProcessDefinitionLogQuerySortReq();
        sort.setUpdateTime("updateTime");
        req.setSort(sort);

        Page<TDsProcessDefinitionLog> objectPage = new Page<>(1L, 10L, 1L, false);
        TDsProcessDefinitionLog tDsProcessDefinitionLog = new TDsProcessDefinitionLog();
        tDsProcessDefinitionLog.setId(0L);
        tDsProcessDefinitionLog.setCode(0L);
        tDsProcessDefinitionLog.setReleaseState(1);
        tDsProcessDefinitionLog.setFlag(1);
        tDsProcessDefinitionLog.setExecutionType(1);
        objectPage.setRecords(CollUtil.newArrayList(tDsProcessDefinitionLog));
        when(mockTDsProcessDefinitionLogService.page(any(Page.class), any(QueryWrapper.class)))
            .thenReturn(objectPage);

        // Run the test
        final Paged<ProcessDefinitionResp> result = processDefinitionLogServiceImplUnderTest.query(req);

        // Verify the results
        Assert.assertEquals(1, result.getTotal());
        Assert.assertEquals(1, result.getList().size());
        Assert.assertNotNull(result.getList().get(0));
        Assert.assertEquals(0L, result.getList().get(0).getCode().longValue());
    }

    @Test
    public void testGet() {
        // Setup
        // Configure TDsProcessDefinitionLogService.getById(...).
        final TDsProcessDefinitionLog tDsProcessDefinitionLog = new TDsProcessDefinitionLog();
        tDsProcessDefinitionLog.setId(0L);
        tDsProcessDefinitionLog.setCode(0L);
        tDsProcessDefinitionLog.setName("name");
        tDsProcessDefinitionLog.setVersion(0);
        tDsProcessDefinitionLog.setDescription("description");
        tDsProcessDefinitionLog.setReleaseState(1);
        tDsProcessDefinitionLog.setFlag(1);
        tDsProcessDefinitionLog.setExecutionType(1);
        when(mockTDsProcessDefinitionLogService.getById(0)).thenReturn(tDsProcessDefinitionLog);

        // Configure SchedulesService.get(...).
        final Schedule schedule = new Schedule();
        schedule.setId(0L);
        schedule.setStartTime(null);
        schedule.setEndTime(null);
        schedule.setCrontab("0 0/1 * * * ?");
        schedule.setTimezoneId(null);
        when(mockSchedulesService.get(0L)).thenReturn(schedule);


        // Configure TaskDefinitionService.queryLogByProcessDefinitionCode(...).
        final TaskDefinition taskDefinition = new TaskDefinition();
        taskDefinition.setId(0);
        taskDefinition.setCode(0L);
        taskDefinition.setName("name");
        taskDefinition.setVersion(0L);
        taskDefinition.setDescription("description");
        final List<TaskDefinition> taskDefinitions = Arrays.asList(taskDefinition);
        when(mockTaskDefinitionService.queryLogByProcessDefinitionCode(0L, 0)).thenReturn(taskDefinitions);

        // Run the test
        final ProcessDefinitionResp result = processDefinitionLogServiceImplUnderTest.get(0);

        // Verify the results
        Assert.assertEquals(0L, result.getCode().longValue());
        Assert.assertEquals("name", result.getName());
    }

}
