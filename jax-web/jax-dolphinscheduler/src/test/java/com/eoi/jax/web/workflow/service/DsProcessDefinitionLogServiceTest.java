package com.eoi.jax.web.workflow.service;

import com.eoi.jax.web.repository.entity.TDsProcessDefinitionLog;
import com.eoi.jax.web.repository.service.TDsProcessDefinitionLogService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class DsProcessDefinitionLogServiceTest {

    @Autowired
    private TDsProcessDefinitionLogService tDsProcessDefinitionLogService;

    /**
     * 测试插入，查询，更新，删除
     */
    @Test
    public void test() {
        TDsProcessDefinitionLog tDsProcessDefinitionLog = new TDsProcessDefinitionLog();
        tDsProcessDefinitionLog.setCode(11L);
        tDsProcessDefinitionLog.setName("test");
        tDsProcessDefinitionLog.setVersion(0);
        tDsProcessDefinitionLog.setDescription("test");
        tDsProcessDefinitionLog.setProjectCode(-1L);
        tDsProcessDefinitionLog.setReleaseState(0);
        tDsProcessDefinitionLog.setUserId(-1);
        tDsProcessDefinitionLog.setGlobalParams(null);
        tDsProcessDefinitionLog.setFlag(0);
        tDsProcessDefinitionLog.setLocations("");
        tDsProcessDefinitionLog.setWarningGroupId(0);
        tDsProcessDefinitionLog.setTimeout(0);
        tDsProcessDefinitionLog.setTenantId(0);
        tDsProcessDefinitionLog.setExecutionType(0);
        tDsProcessDefinitionLog.setCreateTime(new Date());
        tDsProcessDefinitionLog.setUpdateTime(new Date());
        tDsProcessDefinitionLog.setBusinessFlowId(0L);
        tDsProcessDefinitionLog.setCreateUser(0L);
        tDsProcessDefinitionLog.setUpdateUser(0L);
        tDsProcessDefinitionLog.setOperator(-1);
        tDsProcessDefinitionLog.setOperateTime(new Date());
        // 测试插入
        boolean save = tDsProcessDefinitionLogService.save(tDsProcessDefinitionLog);
        Assert.assertTrue(save);

        // 测试查询
        TDsProcessDefinitionLog tDsProcessDefinitionLog1 = tDsProcessDefinitionLogService.getById(tDsProcessDefinitionLog.getId());
        Assert.assertNotNull(tDsProcessDefinitionLog1);
        Assert.assertEquals("test", tDsProcessDefinitionLog1.getName());
        Assert.assertEquals(new Long(11L), tDsProcessDefinitionLog1.getCode());
        Assert.assertEquals(new Integer(0), tDsProcessDefinitionLog1.getVersion());

        // 测试更新
        tDsProcessDefinitionLog.setName("test1");
        tDsProcessDefinitionLog.setVersion(1);
        boolean update = tDsProcessDefinitionLogService.updateById(tDsProcessDefinitionLog);
        Assert.assertTrue(update);
        TDsProcessDefinitionLog tDsProcessDefinitionLog2 = tDsProcessDefinitionLogService.getById(tDsProcessDefinitionLog.getId());
        Assert.assertNotNull(tDsProcessDefinitionLog2);
        Assert.assertEquals("test1", tDsProcessDefinitionLog2.getName());
        Assert.assertEquals(new Long(11L), tDsProcessDefinitionLog2.getCode());

        // 测试删除
        boolean remove = tDsProcessDefinitionLogService.removeById(tDsProcessDefinitionLog.getId());
        Assert.assertTrue(remove);
        TDsProcessDefinitionLog tDsProcessDefinitionLog3 = tDsProcessDefinitionLogService.getById(tDsProcessDefinitionLog.getId());
        Assert.assertNull(tDsProcessDefinitionLog3);


    }


}
