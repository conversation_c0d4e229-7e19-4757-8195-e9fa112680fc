package com.eoi.jax.web.workflow.service;

import com.eoi.jax.web.repository.entity.TDsProcessDefinition;
import com.eoi.jax.web.repository.service.TDsProcessDefinitionService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class DsProcessDefinitionServiceTest {

    @Autowired
    private TDsProcessDefinitionService tDsProcessDefinitionService;

    /**
     * 测试插入，查询，更新，删除
     */
    @Test
    public void test() {
        TDsProcessDefinition tDsProcessDefinition = new TDsProcessDefinition();
        tDsProcessDefinition.setCode(11L);
        tDsProcessDefinition.setName("test");
        tDsProcessDefinition.setVersion(0);
        tDsProcessDefinition.setDescription("test");
        tDsProcessDefinition.setProjectCode(-1L);
        tDsProcessDefinition.setReleaseState(0);
        tDsProcessDefinition.setUserId(-1);
        tDsProcessDefinition.setGlobalParams(null);
        tDsProcessDefinition.setFlag(0);
        tDsProcessDefinition.setLocations("");
        tDsProcessDefinition.setWarningGroupId(0);
        tDsProcessDefinition.setTimeout(0);
        tDsProcessDefinition.setTenantId(0);
        tDsProcessDefinition.setExecutionType(0);
        tDsProcessDefinition.setCreateTime(new Date());
        tDsProcessDefinition.setUpdateTime(new Date());
        tDsProcessDefinition.setBusinessFlowId(0L);
        tDsProcessDefinition.setCreateUser(0L);
        tDsProcessDefinition.setUpdateUser(0L);
        // 测试插入
        boolean save = tDsProcessDefinitionService.save(tDsProcessDefinition);
        Assert.assertTrue(save);

        // 测试查询
        TDsProcessDefinition tDsProcessDefinition1 = tDsProcessDefinitionService.getById(tDsProcessDefinition.getId());
        Assert.assertNotNull(tDsProcessDefinition1);
        Assert.assertEquals("test", tDsProcessDefinition1.getName());
        Assert.assertEquals(new Long(11L), tDsProcessDefinition1.getCode());
        Assert.assertEquals(new Integer(0), tDsProcessDefinition1.getVersion());

        // 测试更新
        tDsProcessDefinition.setName("test1");
        tDsProcessDefinition.setVersion(1);
        boolean update = tDsProcessDefinitionService.updateById(tDsProcessDefinition);
        Assert.assertTrue(update);
        TDsProcessDefinition tDsProcessDefinition2 = tDsProcessDefinitionService.getById(tDsProcessDefinition.getId());
        Assert.assertNotNull(tDsProcessDefinition2);
        Assert.assertEquals("test1", tDsProcessDefinition2.getName());
        Assert.assertEquals(new Long(11L), tDsProcessDefinition2.getCode());

        // 测试删除
        boolean remove = tDsProcessDefinitionService.removeById(tDsProcessDefinition.getId());
        Assert.assertTrue(remove);
        TDsProcessDefinition tDsProcessDefinition3 = tDsProcessDefinitionService.getById(tDsProcessDefinition.getId());
        Assert.assertNull(tDsProcessDefinition3);


    }


}
