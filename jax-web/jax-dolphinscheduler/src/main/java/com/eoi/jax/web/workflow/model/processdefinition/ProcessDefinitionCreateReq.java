package com.eoi.jax.web.workflow.model.processdefinition;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TDsProcessDefinition;
import com.eoi.jax.web.workflow.model.taskdefinition.TaskDefinition;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.dolphinscheduler.common.enums.Flag;
import org.apache.dolphinscheduler.common.enums.ProcessExecutionTypeEnum;
import org.apache.dolphinscheduler.common.enums.ReleaseState;
import org.apache.dolphinscheduler.common.utils.CodeGenerateUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/12
 */
public class ProcessDefinitionCreateReq implements ICreateModel<TDsProcessDefinition> {

    /**
     * 业务流程id
     */
    @NotNull(message = "业务流程id不能为空")
    @Schema(description = "业务流程id")
    private Long businessFlowId;

    /**
     * process definition name
     */
    @Schema(description = "工作流名称")
    @NotBlank(message = "工作流名称不能为空")
    private String name;

    /**
     * process definition version
     */
    @Schema(description = "工作流版本")
    private Integer version;

    /**
     * description
     */
    @Schema(description = "工作流描述")
    @Length(max = 255)
    private String description;

    /**
     * project code
     */
    @Schema(description = "项目编码")
    private Long projectCode;


    /**
     * global parameters
     */
    @Schema(description = "全局参数")
    private List<ParamProperty> globalParams;


    /**
     * time out, unit: minute
     */
    @Schema(description = "超时时间，单位分钟")
    private Integer timeout;

    /**
     * execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority
     */
    @Schema(description = "执行类型, 并行，串行")
    private String executionType;

    @Schema(description = "调度信息")
    private Schedule schedule;

    @Schema(description = "task定义")
    private List<TaskDefinition> taskDefinitionList;

    @Schema(description = "task之间的依赖关系")
    private List<TaskRelation> taskRelationList;

    /**
     * Node location information
     */
    @Schema(description = "算子画布位置")
    private List<TaskLocation> locations;

    /**
     * 标签信息
     */
    @Schema(description = "标签信息")
    private List<String> tags;

    @Schema(description = "是否强制启动")
    private Boolean forceStart = false;

    public Boolean getForceStart() {
        return forceStart != null && forceStart;
    }

    public void setForceStart(Boolean forceStart) {
        this.forceStart = forceStart;
    }

    /**
     * request to entity
     *
     * @return
     */
    @Override
    public TDsProcessDefinition toEntity() {
        TDsProcessDefinition tDsProcessDefinition = ModelBeanUtil.copyBean(this, new TDsProcessDefinition());
        tDsProcessDefinition.setCode(CodeGenerateUtils.getInstance().genCode());
        tDsProcessDefinition.setLocations(JsonUtil.encode(this.getLocations()));
        tDsProcessDefinition.setGlobalParams(JsonUtil.encode(this.getGlobalParams()));
        tDsProcessDefinition.setReleaseState(ReleaseState.OFFLINE.getCode());
        tDsProcessDefinition.setFlag(Flag.YES.getCode());
        tDsProcessDefinition.setExecutionType(ProcessExecutionTypeEnum.valueOf(this.getExecutionType()).getCode());
        ModelBeanUtil.setCreateDefaultValue(tDsProcessDefinition);
        tDsProcessDefinition.setId(null);
        return tDsProcessDefinition;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(Long projectCode) {
        this.projectCode = projectCode;
    }

    public List<ParamProperty> getGlobalParams() {
        return globalParams;
    }

    public void setGlobalParams(List<ParamProperty> globalParams) {
        this.globalParams = globalParams;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getExecutionType() {
        return executionType;
    }

    public void setExecutionType(String executionType) {
        this.executionType = executionType;
    }

    public Schedule getSchedule() {
        return schedule;
    }

    public void setSchedule(Schedule schedule) {
        this.schedule = schedule;
    }

    public List<TaskDefinition> getTaskDefinitionList() {
        return taskDefinitionList;
    }

    public void setTaskDefinitionList(List<TaskDefinition> taskDefinitionList) {
        this.taskDefinitionList = taskDefinitionList;
    }

    public List<TaskRelation> getTaskRelationList() {
        return taskRelationList;
    }

    public void setTaskRelationList(List<TaskRelation> taskRelationList) {
        this.taskRelationList = taskRelationList;
    }

    public List<TaskLocation> getLocations() {
        return locations;
    }

    public void setLocations(List<TaskLocation> locations) {
        this.locations = locations;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }
}
