/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.eoi.jax.web.workflow.model.taskdefinition;

import java.util.List;
import java.util.Objects;

public class ConditionsParameters extends AbstractParameters {

    // dependent task list
    private DependentObject dependence;

    private ConditionResult conditionResult;

    @Override
    public boolean checkParameters() {
        return Objects.nonNull(dependence) && Objects.nonNull(conditionResult);
    }

    public DependentObject getDependence() {
        return dependence;
    }

    public void setDependence(DependentObject dependence) {
        this.dependence = dependence;
    }

    public ConditionResult getConditionResult() {
        return conditionResult;
    }

    public void setConditionResult(ConditionResult conditionResult) {
        this.conditionResult = conditionResult;
    }

    public static class ConditionResult {

        // node list to run when success
        private List<String> successNode;

        // node list to run when failed
        private List<String> failedNode;

        public List<String> getSuccessNode() {
            return successNode;
        }

        public void setSuccessNode(List<String> successNode) {
            this.successNode = successNode;
        }

        public List<String> getFailedNode() {
            return failedNode;
        }

        public void setFailedNode(List<String> failedNode) {
            this.failedNode = failedNode;
        }
    }


}
