package com.eoi.jax.web.workflow.model.taskdefinition;

import com.eoi.jax.web.workflow.model.processdefinition.ParamProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */
public class SqlTaskQueryReq {

    private Integer page;

    private Integer size;

    private SqlTaskQueryFilter filter;

    public static class SqlTaskQueryFilter {

        @Schema(description = "模拟执行时间")
        private String scheduleTime;

        /**
         * global parameters
         */
        @Schema(description = "全局参数")
        private List<ParamProperty> globalParams;

        @Schema(description = "任务自定义参数")
        private SqlParameters taskParams;

        @Schema(description = "任务自定义参数")
        private List<ParamProperty> taskParamList;

        public String getScheduleTime() {
            return scheduleTime;
        }

        public void setScheduleTime(String scheduleTime) {
            this.scheduleTime = scheduleTime;
        }

        public List<ParamProperty> getGlobalParams() {
            return globalParams;
        }

        public void setGlobalParams(List<ParamProperty> globalParams) {
            this.globalParams = globalParams;
        }

        public SqlParameters getTaskParams() {
            return taskParams;
        }

        public void setTaskParams(SqlParameters taskParams) {
            this.taskParams = taskParams;
        }

        public List<ParamProperty> getTaskParamList() {
            return taskParamList;
        }

        public void setTaskParamList(List<ParamProperty> taskParamList) {
            this.taskParamList = taskParamList;
        }
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public SqlTaskQueryFilter getFilter() {
        return filter;
    }

    public void setFilter(SqlTaskQueryFilter filter) {
        this.filter = filter;
    }
}
