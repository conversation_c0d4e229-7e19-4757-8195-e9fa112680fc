package com.eoi.jax.web.workflow.remote.command;

import org.apache.dolphinscheduler.common.utils.JSONUtils;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
public class ScheduleResponseCommand {

    private Boolean success = true;

    private String msg;

    public ScheduleResponseCommand() {
    }

    public ScheduleResponseCommand(Boolean success, String msg) {
        this.success = success;
        this.msg = msg;
    }

    public Command convert2Command() {
        Command command = new Command();
        command.setType(CommandType.SCHEDULE_RESPONSE);
        byte[] body = JSONUtils.toJsonByteArray(this);
        command.setBody(body);
        return command;
    }


    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "ScheduleResponseCommand{" +
                "success=" + success +
                ", msg='" + msg + '\'' +
                '}';
    }
}
