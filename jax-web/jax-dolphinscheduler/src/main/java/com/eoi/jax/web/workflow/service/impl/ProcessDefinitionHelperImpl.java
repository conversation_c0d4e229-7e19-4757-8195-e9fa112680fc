package com.eoi.jax.web.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.repository.entity.TDsProcessDefinition;
import com.eoi.jax.web.repository.entity.TDsTaskDefinitionLog;
import com.eoi.jax.web.repository.service.TDsProcessDefinitionService;
import com.eoi.jax.web.workflow.model.taskdefinition.SubProcessParameters;
import com.eoi.jax.web.workflow.service.ProcessDefinitionHelper;
import com.eoi.jax.web.workflow.service.TaskDefinitionService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.dolphinscheduler.common.enums.ReleaseState;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Service
public class ProcessDefinitionHelperImpl implements ProcessDefinitionHelper {


    @Resource
    private TDsProcessDefinitionService tDsProcessDefinitionService;

    @Resource
    private TaskDefinitionService taskDefinitionService;

    /**
     * 检查子工作流是否上线
     */
    public void checkSubProcessIsOnline(Long parentProcessDefinitionCode) {
        // 子工作流上线检查
        List<Long> subProcessDefineCodes = new ArrayList<>();
        recurseFindSubProcess(parentProcessDefinitionCode, subProcessDefineCodes);
        if (CollUtil.isNotEmpty(subProcessDefineCodes)) {
            List<TDsProcessDefinition> subProcessDefinitionList = tDsProcessDefinitionService.listByCode(subProcessDefineCodes);
            List<TDsProcessDefinition> offlineSubProcessDefinitionList = subProcessDefinitionList.stream()
                .filter(it -> !it.getReleaseState().equals(ReleaseState.ONLINE.getCode()))
                .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(offlineSubProcessDefinitionList)) {
                throw new BizException(ResponseCode.FAILED.getCode(), String.format("请先上线子工作流:[%s]",
                    offlineSubProcessDefinitionList.stream().map(TDsProcessDefinition::getName).collect(Collectors.joining(","))));
            }
        }
    }


    /**
     * 递归查找子工作流
     *
     * @param parentCode
     * @param ids
     */
    private void recurseFindSubProcess(Long parentCode, List<Long> ids) {
        TDsProcessDefinition tDsProcessDefinition = tDsProcessDefinitionService.getByCode(parentCode);
        List<TDsTaskDefinitionLog> tDsTaskDefinitionLogList =
            taskDefinitionService.selectByProcessDefinitionCodeAndVersion(tDsProcessDefinition.getCode(), 0);
        if (CollUtil.isEmpty(tDsTaskDefinitionLogList)) {
            return;
        }
        for (TDsTaskDefinitionLog tDsTaskDefinitionLog : tDsTaskDefinitionLogList) {
            Map<String, Object> map = JsonUtil.decode2Map(tDsTaskDefinitionLog.getTaskParams());
            if (map.containsKey("processDefinitionCode")) {
                SubProcessParameters subProcessParameter = JsonUtil.decode(tDsTaskDefinitionLog.getTaskParams(),
                    new TypeReference<SubProcessParameters>() {
                    });
                ids.add(subProcessParameter.getProcessDefinitionCode());
                recurseFindSubProcess(subProcessParameter.getProcessDefinitionCode(), ids);
            }
        }
    }

}
