package com.eoi.jax.web.workflow.model.taskdefinition;

import com.eoi.jax.web.workflow.enumrate.DependentRelation;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25
 */

public class DependentObject {

    private List<DependentTaskModel> dependTaskList;
    private DependentRelation relation;

    public List<DependentTaskModel> getDependTaskList() {
        return dependTaskList;
    }

    public void setDependTaskList(List<DependentTaskModel> dependTaskList) {
        this.dependTaskList = dependTaskList;
    }

    public DependentRelation getRelation() {
        return relation;
    }

    public void setRelation(DependentRelation relation) {
        this.relation = relation;
    }
}
