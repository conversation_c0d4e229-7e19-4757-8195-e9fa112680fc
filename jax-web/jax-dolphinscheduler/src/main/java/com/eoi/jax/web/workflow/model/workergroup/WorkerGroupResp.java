package com.eoi.jax.web.workflow.model.workergroup;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.repository.entity.TDsWorkerGroup;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/12
 */
public class WorkerGroupResp extends ProjectAuthRespModel implements
        IRespModel<TDsWorkerGroup>, IProjectAuthModel, IUserInfoExtensionModel {

    /**
     * id
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * worker group name
     */
    @Schema(description = "工作组名称")
    private String name;

    /**
     * worker addr list. split by [,]
     */
    @Schema(description = "工作组地址")
    private List<String> addrList;

    /**
     * create time
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * update time
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * description
     */
    @Schema(description = "描述")
    private String description;

    @Schema(description = "系统默认")
    private boolean systemDefault = false;

    /**
     * 创建人id
     */
    private Long createUser;

    /**
     * 更新人id
     */
    private Long updateUser;


    /**
     * response from entity
     *
     * @param tDsWorkerGroup
     * @return
     */
    @Override
    public WorkerGroupResp fromEntity(TDsWorkerGroup tDsWorkerGroup) {
        IRespModel.super.fromEntity(tDsWorkerGroup);
        if (StrUtil.isNotBlank(tDsWorkerGroup.getAddrList())) {
            this.setAddrList(StrUtil.splitTrim(tDsWorkerGroup.getAddrList(), ","));
        }
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getAddrList() {
        return addrList;
    }

    public void setAddrList(List<String> addrList) {
        this.addrList = addrList;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isSystemDefault() {
        return systemDefault;
    }

    public void setSystemDefault(boolean systemDefault) {
        this.systemDefault = systemDefault;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
