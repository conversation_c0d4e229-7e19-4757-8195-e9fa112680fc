package com.eoi.jax.web.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.BusinessFlowTreeCategoryEnum;
import com.eoi.jax.web.core.common.enumrate.BusinessFlowTreeReferenceTypeEnum;
import com.eoi.jax.web.core.common.enumrate.ProjectResourceTypeEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.excel.BaseAuthExcelService;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.ingestion.enumrate.TagRelationRecordTypeEnum;
import com.eoi.jax.web.ingestion.enumrate.TagTypeEnum;
import com.eoi.jax.web.ingestion.model.DataDeveloperExcelModel;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeCreateReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeResp;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeUpdateReq;
import com.eoi.jax.web.ingestion.model.pipeline.TaskAddTagReq;
import com.eoi.jax.web.ingestion.model.pipeline.TaskAddTagResp;
import com.eoi.jax.web.ingestion.model.projectresource.OfflineSparkProcessResp;
import com.eoi.jax.web.ingestion.model.projectresource.ProjectResourceReq;
import com.eoi.jax.web.ingestion.model.projectresource.ProjectResourceResp;
import com.eoi.jax.web.ingestion.service.BusinessFlowTreeService;
import com.eoi.jax.web.ingestion.service.ProjectResourceService;
import com.eoi.jax.web.ingestion.service.TagRelationService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.search.result.TaskMainInfo;
import com.eoi.jax.web.repository.service.IDaoService;
import com.eoi.jax.web.repository.service.TDsProcessDefinitionLogService;
import com.eoi.jax.web.repository.service.TDsProcessDefinitionService;
import com.eoi.jax.web.repository.service.TDsResourcesService;
import com.eoi.jax.web.workflow.model.OfflineWorkFlowExcelExportModel;
import com.eoi.jax.web.workflow.model.processdefinition.*;
import com.eoi.jax.web.workflow.model.processinstance.ProcessInstanceResp;
import com.eoi.jax.web.workflow.model.taskdefinition.*;
import com.eoi.jax.web.workflow.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dolphinscheduler.common.enums.ConditionType;
import org.apache.dolphinscheduler.common.enums.Flag;
import org.apache.dolphinscheduler.common.enums.Priority;
import org.apache.dolphinscheduler.common.enums.ReleaseState;
import org.apache.dolphinscheduler.common.graph.DAG;
import org.apache.dolphinscheduler.common.utils.CodeGenerateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/12
 */
@Service
public class ProcessDefinitionServiceImpl extends BaseAuthExcelService<
        TDsProcessDefinitionService,
        TDsProcessDefinition,
        ProcessDefinitionResp,
        ProcessDefinitionCreateReq,
        ProcessDefinitionUpdateReq,
        ProcessDefinitionQueryReq,
        DataDeveloperExcelModel> implements ProcessDefinitionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessDefinitionServiceImpl.class);


    @Autowired
    private TDsProcessDefinitionService tDsProcessDefinitionService;

    @Autowired
    private TDsProcessDefinitionLogService tDsProcessDefinitionLogService;

    @Autowired
    private TaskDefinitionService taskDefinitionService;

    @Autowired
    private ProcessTaskRelationService processTaskRelationService;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Autowired
    private SchedulesService schedulesService;

    @Autowired
    private BusinessFlowTreeService businessFlowTreeService;

    @Resource
    private ProjectResourceService projectResourceService;
    @Resource
    private TagRelationService tagRelationService;
    @Autowired
    private TDsResourcesService tDsResourcesService;
    @Resource
    private ProcessDefinitionHelper processDefinitionHelper;


    public ProcessDefinitionServiceImpl(IDaoService<TDsProcessDefinition> dao) {
        super(dao);
    }


    /**
     * 分页查询
     *
     * @param req
     * @return
     */
    @Override
    public Paged<ProcessDefinitionResp> query(ProcessDefinitionQueryReq req) {
        IPage<TDsProcessDefinition> customPage = tDsProcessDefinitionService.selectCustomPage(req.page(),
                req.query(), Optional.ofNullable(req.getFilter())
                        .map(it -> CollUtil.isEmpty(it.getTagIdList()) ? null : it.getTagIdList()).orElse(null));
        if (CollUtil.isEmpty(customPage.getRecords())) {
            return new Paged<>(0L, CollUtil.newArrayList());
        }
        Paged<ProcessDefinitionResp> paged = new Paged<>(customPage.getTotal(), marshallingRespListFrom(customPage.getRecords()));
        Map<Long, ProcessInstanceResp> processInstanceRespMap = processInstanceService.lastProcessInstance(
                paged.getList().stream().map(ProcessDefinitionResp::getCode).collect(Collectors.toList()));
        for (ProcessDefinitionResp resp : paged.getList()) {
            resp.setLastInstance(processInstanceRespMap.get(resp.getCode()));
            resp.setSchedule(schedulesService.get(resp.getCode()));
            resp.setTags(tagRelationService.getTagByRecordIdAndRecordType(resp.getId(), TagRelationRecordTypeEnum.DS_PROCESS_DEFINITION));
        }
        return paged;
    }

    /**
     * 所有数据
     *
     * @return
     */
    @Override
    public List<ProcessDefinitionResp> list(ProcessDefinitionQueryFilterReq req) {
        QueryWrapper<TDsProcessDefinition> queryWrapper = req.where(new QueryWrapper<>());
        List<TDsProcessDefinition> tDsProcessDefinitionList =
                tDsProcessDefinitionService.listByConditionAndProjectId(queryWrapper, req.getProjectId());
        if (CollUtil.isEmpty(tDsProcessDefinitionList)) {
            return CollUtil.newArrayList();
        }

        return tDsProcessDefinitionList.stream().map(it -> {
            ProcessDefinitionResp resp = new ProcessDefinitionResp().fromEntity(it);
            resp.setTaskDefinitionList(taskDefinitionService.queryLogByProcessDefinitionCode(resp.getCode(), resp.getVersion()));
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public ProcessDefinitionResp get(Long id) {
        TDsProcessDefinition tDsProcessDefinition = tDsProcessDefinitionService.selectByIdWithProjectAuth(id,
                new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "数据不存在或没有访问权限"));
        if (Objects.isNull(tDsProcessDefinition)) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS);
        }

        ProcessDefinitionResp resp = new ProcessDefinitionResp().fromEntity(tDsProcessDefinition);

        resp.setSchedule(schedulesService.get(resp.getCode()));
        resp.setTaskRelationList(processTaskRelationService.queryLogByProcessDefinitionCode(resp.getCode(), 0));
        resp.setTaskDefinitionList(taskDefinitionService.queryLogByProcessDefinitionCode(resp.getCode(), 0));
        resp.setTags(tagRelationService.getTagByRecordIdAndRecordType(resp.getId(), TagRelationRecordTypeEnum.DS_PROCESS_DEFINITION));
        return resp;
    }

    @Override
    public ProcessDefinitionResp cloneById(Long id) {
        ProcessDefinitionResp resp = get(id);

        Map<Long, Long> newCodeMap = new HashMap<>(8);
        List<TaskDefinition> taskDefinitionList = resp.getTaskDefinitionList();
        if (CollUtil.isNotEmpty(taskDefinitionList)) {
            for (TaskDefinition taskDefinition : taskDefinitionList) {
                Long newCode = Long.parseLong(genTaskCode(1).get(0));
                newCodeMap.put(taskDefinition.getCode(), newCode);
                taskDefinition.setId(null);
                taskDefinition.setCode(newCode);
            }
        }

        List<TaskRelation> taskRelationList = resp.getTaskRelationList();
        if (CollUtil.isNotEmpty(taskRelationList)) {
            for (TaskRelation taskRelation : taskRelationList) {
                taskRelation.setPreTaskCode(newCodeMap.getOrDefault(taskRelation.getPreTaskCode(), taskRelation.getPreTaskCode()));
                taskRelation.setPostTaskCode(newCodeMap.getOrDefault(taskRelation.getPostTaskCode(), taskRelation.getPostTaskCode()));
            }
        }

        return resp;
    }

    /**
     * 新增
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessDefinitionResp create(ProcessDefinitionCreateReq req) {
        if (Objects.isNull(req.getProjectCode())) {
            req.setProjectCode(-1L);
        }
        // 名称项目下唯一
        TDsProcessDefinition tDsProcessDefinition =
                tDsProcessDefinitionService.getByProjectCodeAndName(req.getProjectCode(), req.getName());
        if (tDsProcessDefinition != null) {
            throw new BizException(ResponseCode.FOUND_MULTIPLE_NAME);
        }
        //
        List<TDsTaskDefinitionLog> taskDefinitionLogList = generateTaskDefinitionList(req.getTaskDefinitionList());
        List<TDsProcessTaskRelationLog> taskRelationList = generateTaskRelationList(req.getTaskRelationList(), taskDefinitionLogList);

        TDsProcessDefinition processDefinition = req.toEntity();

        // 保存工作流定义
        this.saveProcessDefine(processDefinition);

        // 保存任务定义
        List<TDsTaskDefinition> tDsTaskDefinitionList = taskDefinitionService.saveTaskDefine(
                processDefinition.getProjectCode(), processDefinition.getCode(), taskDefinitionLogList);

        // 保存工作流和任务之间的关系
        processTaskRelationService.saveTaskRelation(
                processDefinition.getProjectCode(), processDefinition.getCode(), processDefinition.getVersion(),
                taskRelationList, tDsTaskDefinitionList);


        // 创建调度
        schedulesService.create(processDefinition.getCode(), req.getSchedule());

        // 保存标签
        saveOrUpdateTag(processDefinition.getId(), req.getTags());

        // 创建业务流程树
        createBusinessFlowTree(processDefinition);

        return get(processDefinition.getId());
    }

    /**
     * 更新
     *
     * @param req
     * @return
     */
    @Override
    public ProcessDefinitionResp update(ProcessDefinitionUpdateReq req) {
        if (Objects.isNull(req.getProjectCode())) {
            req.setProjectCode(-1L);
        }
        TDsProcessDefinition processDefinition = tDsProcessDefinitionService.existId(req.getId(),
                new BizException(ResponseCode.ID_NOT_EXISTS));

        List<TDsTaskDefinitionLog> tDsTaskDefinitionLogList = generateTaskDefinitionList(req.getTaskDefinitionList());
        List<TDsProcessTaskRelationLog> tDsProcessTaskRelationLogList =
                generateTaskRelationList(req.getTaskRelationList(), tDsTaskDefinitionLogList);

        if (processDefinition.getReleaseState() == ReleaseState.ONLINE.getCode()) {
            // online can not permit edit
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流定义:%s已上线不允许修改", req.getName()));
        }
        if (!req.getName().equals(processDefinition.getName())) {
            // check whether the new process define name exist
            TDsProcessDefinition tDsProcessDefinition =
                    tDsProcessDefinitionService.getByProjectCodeAndName(req.getProjectCode(), req.getName());
            if (tDsProcessDefinition != null && !tDsProcessDefinition.getId().equals(req.getId())) {
                throw new BizException(ResponseCode.FOUND_MULTIPLE_NAME);
            }
        }

        taskUsedInOtherTaskValid(processDefinition, tDsProcessTaskRelationLogList);

        TDsProcessDefinition processDefinitionDeepCopy = req.toEntity(processDefinition);
        // 保存工作流定义，0号版本
        updateProcessDefine(processDefinitionDeepCopy);

        // 保存任务定义，0号版本
        List<TDsTaskDefinition> tDsTaskDefinitionList = taskDefinitionService.updateTaskDefine(processDefinitionDeepCopy.getProjectCode(),
                processDefinitionDeepCopy.getCode(), tDsTaskDefinitionLogList);

        // 保存工作流和任务关系，0号版本
        processTaskRelationService.updateTaskRelation(processDefinitionDeepCopy.getProjectCode(), processDefinitionDeepCopy.getCode(),
                processDefinitionDeepCopy.getVersion(), tDsProcessTaskRelationLogList, tDsTaskDefinitionList);

        // 更新调度配置
        schedulesService.update(processDefinitionDeepCopy.getCode(), req.getSchedule());

        // 保存标签
        saveOrUpdateTag(processDefinition.getId(), req.getTags());

        updateBusinessFlowTree(processDefinitionDeepCopy);

        return get(processDefinitionDeepCopy.getId());
    }

    /**
     * 创建发布
     *
     * @param req
     * @return
     */
    @Override
    public ProcessDefinitionResp createPublish(ProcessDefinitionCreateReq req) {
        ProcessDefinitionResp resp = this.create(req);
        online(resp.getId(), req.getForceStart());
        return resp;
    }

    /**
     * 更新发布
     *
     * @param req
     * @return
     */
    @Override
    public ProcessDefinitionResp updatePublish(ProcessDefinitionUpdateReq req) {
        ProcessDefinitionResp resp = this.update(req);
        online(resp.getId(), req.getForceStart());
        return resp;
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public ProcessDefinitionResp delete(Long id) {
        TDsProcessDefinition processDefinition = tDsProcessDefinitionService.getById(id);
        if (Objects.isNull(processDefinition)) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS);
        }
        if (processDefinition.getReleaseState() == ReleaseState.ONLINE.getCode()) {
            // online can not permit edit
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流定义:%s已上线不允许删除", processDefinition.getName()));
        }

        processDefinitionUsedInOtherTaskValid(processDefinition);

        // 删除调度配置
        schedulesService.deleteByProcessDefineCode(processDefinition.getProjectCode(), processDefinition.getCode());

        List<TDsProcessTaskRelation> processTaskRelations = processTaskRelationService.queryByProcessCode(processDefinition.getCode(),
                processDefinition.getVersion());
        if (CollectionUtils.isNotEmpty(processTaskRelations)) {
            Set<Long> taskCodeList = new HashSet<>(processTaskRelations.size() * 2);
            for (TDsProcessTaskRelation processTaskRelation : processTaskRelations) {
                if (processTaskRelation.getPreTaskCode() != 0) {
                    taskCodeList.add(processTaskRelation.getPreTaskCode());
                }
                if (processTaskRelation.getPostTaskCode() != 0) {
                    taskCodeList.add(processTaskRelation.getPostTaskCode());
                }
            }
            if (CollectionUtils.isNotEmpty(taskCodeList)) {
                boolean deleteResult = taskDefinitionService.deleteByBatchCodes(new ArrayList<>(taskCodeList));
                if (!deleteResult) {
                    throw new BizException(ResponseCode.FAILED.getCode(), "删除任务定义错误");
                }
            }
        }

        boolean deleteResult = tDsProcessDefinitionService.removeById(processDefinition.getId());
        if (!deleteResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "删除工作流定义错误");
        }


        deleteResult = processTaskRelationService.deleteByCode(processDefinition.getCode());

        if (!deleteResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "删除任务关系失败");
        }

        deleteBusinessFlowTree(processDefinition.getId());

        deleteResult = processInstanceService.deleteByProcessDefinitionCode(processDefinition.getCode());
        if (!deleteResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "删除工作流实例失败");
        }

        return new ProcessDefinitionResp().fromEntity(processDefinition);
    }


    /**
     * 工作流上线
     *
     * @param id
     * @return
     */
    @Override
    public ProcessDefinitionResp online(Long id, boolean forceStart) {
        TDsProcessDefinition tDsProcessDefinition = tDsProcessDefinitionService.getById(id);
        if (Objects.isNull(tDsProcessDefinition)) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流定义%s不存在", id));
        }
        // 依赖工作流上线检查
        checkDependentProcessIsOnline(tDsProcessDefinition.getCode());

        // 子工作流上线检查
        processDefinitionHelper.checkSubProcessIsOnline(tDsProcessDefinition.getCode());

        // 资源配额
        if (projectResourceService.checkNeedCalResource()) {
            OfflineSparkProcessResp currentResource =
                    projectResourceService.getOfflineSparkProcessResource(id);
            long currentCpu = currentResource.getCpu();
            long currentMem = currentResource.getMemory();
            if (currentCpu != 0 || currentMem != 0) {
                ProjectResourceReq projectResourceReq = new ProjectResourceReq();
                projectResourceReq.setStartCheck(true);
                projectResourceReq.setProcessDefinitionId(id);
                ProjectResourceResp projectResource = projectResourceService.getProjectResource(projectResourceReq);
                projectResource.checkResource(currentCpu, currentMem, forceStart);
            }
        }


        // 工作流定义0号版本，发布上线
        TDsProcessDefinitionLog tDsProcessDefinitionLog = this.publishProcessDefine(tDsProcessDefinition.getCode());

        // 任务定义0号版本，发布上线
        taskDefinitionService.publishTaskDefine(tDsProcessDefinitionLog.getCode(), tDsProcessDefinitionLog.getVersion());

        // 工作流定义和任务版本0号版本上线
        processTaskRelationService.publishTaskRelation(tDsProcessDefinitionLog.getCode(), tDsProcessDefinitionLog.getVersion());

        // 上线调度
        schedulesService.release(tDsProcessDefinitionLog.getProjectCode(), tDsProcessDefinition.getCode(), ReleaseState.ONLINE);

        // 更新业务流程树
        updateBusinessFlowTree(tDsProcessDefinitionService.getByCode(tDsProcessDefinition.getCode()));

        return get(id);
    }


    /**
     * 检查工作流定义是否被其他任务使用
     */
    private void checkDependentProcessIsOnline(Long code) {
        List<TDsTaskDefinitionLog> tDsTaskDefinitionLogList =
                taskDefinitionService.selectByProcessDefinitionCodeAndVersion(code, 0);
        if (CollUtil.isEmpty(tDsTaskDefinitionLogList)) {
            return;
        }
        List<TDsTaskDefinitionLog> dependentTaskList = tDsTaskDefinitionLogList.stream()
                .filter(it -> "DEPENDENT".equals(it.getTaskType())).collect(Collectors.toList());
        if (CollUtil.isEmpty(dependentTaskList)) {
            return;
        }
        Set<Long> dependentProcessSet = new HashSet<>();
        for (TDsTaskDefinitionLog tDsTaskDefinitionLog : dependentTaskList) {
            DependentParameters dependenceParameters = JsonUtil.decode(tDsTaskDefinitionLog.getTaskParams(), DependentParameters.class);
            if (Objects.isNull(dependenceParameters.getDependence()) ||
                    CollUtil.isEmpty(dependenceParameters.getDependence().getDependTaskList())) {
                continue;
            }
            List<DependentTaskModel> dependTaskList = dependenceParameters.getDependence().getDependTaskList();
            for (DependentTaskModel dependentTaskModel : dependTaskList) {
                List<DependentItem> dependItemList = dependentTaskModel.getDependItemList();
                if (CollUtil.isNotEmpty(dependItemList)) {
                    dependItemList.stream().map(DependentItem::getDefinitionCode).distinct().forEach(dependentProcessSet::add);
                }
            }
        }

        if (CollUtil.isNotEmpty(dependentProcessSet)) {
            // 依赖工作流定义
            List<TDsProcessDefinition> tDsProcessDefinitionList = tDsProcessDefinitionService
                    .listByCode(new ArrayList<>(dependentProcessSet));

            for (TDsProcessDefinition tDsProcessDefinition : tDsProcessDefinitionList) {
                if (tDsProcessDefinition.getReleaseState() != ReleaseState.ONLINE.getCode()) {
                    throw new BizException(ResponseCode.FAILED.getCode(), String.format("DEPENDENT任务的工作流'%s'不是上线状态, 请先上线该工作流",
                            tDsProcessDefinition.getName()));
                }
            }
        }

    }


    /**
     * 工作流下线
     *
     * @param id
     * @return
     */
    @Override
    public ProcessDefinitionResp offline(Long id) {
        TDsProcessDefinition tDsProcessDefinition = tDsProcessDefinitionService.getById(id);
        if (Objects.isNull(tDsProcessDefinition)) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流定义%s不存在", id));
        }
        // 验证上游工作流（将本工作作为子流程）是否下线
        Set<TaskMainInfo> subProcessSet = taskDefinitionService.queryTaskDepOnProcess(
            tDsProcessDefinition.getProjectCode(), tDsProcessDefinition.getCode());
        if (CollUtil.isNotEmpty(subProcessSet)) {
            List<TDsProcessDefinition> upstreamProcessDefinitionList = tDsProcessDefinitionService.listByCode(
                subProcessSet.stream().map(TaskMainInfo::getProcessDefinitionCode).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(upstreamProcessDefinitionList) &&
                upstreamProcessDefinitionList.stream().anyMatch(it -> it.getReleaseState() == ReleaseState.ONLINE.getCode())) {
                throw new BizException(ResponseCode.FAILED.getCode(), String.format("当前工作流被作为依赖或子流程者使用, 请先下线工作流:[%s]",
                    upstreamProcessDefinitionList.stream().filter(it -> it.getReleaseState() == ReleaseState.ONLINE.getCode())
                        .map(TDsProcessDefinition::getName).collect(Collectors.joining(","))));
            }
        }
        // 下线调度
        schedulesService.release(tDsProcessDefinition.getProjectCode(), tDsProcessDefinition.getCode(), ReleaseState.OFFLINE);
        // 下线工作流定义log
        TDsProcessDefinitionLog tDsProcessDefinitionLog =
                tDsProcessDefinitionLogService.selectByCodeAndVersion(tDsProcessDefinition.getCode(), tDsProcessDefinition.getVersion());
        tDsProcessDefinitionLog.setReleaseState(ReleaseState.OFFLINE.getCode());
        tDsProcessDefinitionLogService.updateByByCodeAndVersion(tDsProcessDefinitionLog);

        tDsProcessDefinition.setReleaseState(ReleaseState.OFFLINE.getCode());
        ModelBeanUtil.setUpdateDefaultValue(tDsProcessDefinition);
        boolean flag = tDsProcessDefinitionService.updateById(tDsProcessDefinition);

        // 更新业务流程树
        updateBusinessFlowTree(tDsProcessDefinition);

        return get(id);
    }


    @Override
    public List<String> genTaskCode(Integer genNum) {
        if (Objects.isNull(genNum) || genNum <= 0) {
            genNum = 1;
        }
        Assert.isTrue(genNum < 100, "生成任务定义数量不能超过100个");
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < genNum; i++) {
            resultList.add(String.valueOf(CodeGenerateUtils.getInstance().genCode()));
        }
        return resultList;
    }


    @Override
    public TaskAddTagResp batchSaveOrUpdateTag(TaskAddTagReq taskAddTagReq) {
        if (taskAddTagReq.getTags() == null || CollUtil.isEmpty(taskAddTagReq.getRecodeIds())) {
            return new TaskAddTagResp("无请求参数");
        }

        if (CollUtil.isEmpty(taskAddTagReq.getTags())) {
            for (Long recodeId : taskAddTagReq.getRecodeIds()) {
                tagRelationService.removeByRecordIdAndRecordType(recodeId, TagRelationRecordTypeEnum.DS_PROCESS_DEFINITION);
            }
        }
        // 保存标签
        List<TbTagRelation> tagRelationList = tagRelationService.batchSaveTagAndTagRelation(TagTypeEnum.OFFLINE_WORKFLOW,
                new ArrayList<>(taskAddTagReq.getTags()), TagRelationRecordTypeEnum.DS_PROCESS_DEFINITION,
                new ArrayList<>(taskAddTagReq.getRecodeIds()), taskAddTagReq.getTagOverwrite());

        return new TaskAddTagResp("更新成功");
    }

    /**
     * 校验task是否被其他工作流依赖
     */
    private void taskUsedInOtherTaskValid(TDsProcessDefinition processDefinition,
                                          List<TDsProcessTaskRelationLog> taskRelationList) {
        List<TDsProcessTaskRelation> oldProcessTaskRelationList =
                processTaskRelationService.queryByProcessCode(processDefinition.getCode(), processDefinition.getVersion());
        StringBuilder sb = new StringBuilder();
        for (TDsProcessTaskRelation oldProcessTaskRelation : oldProcessTaskRelationList) {
            boolean oldTaskExists = taskRelationList.stream()
                    .anyMatch(relation -> oldProcessTaskRelation.getPostTaskCode().equals(relation.getPostTaskCode()));
            if (!oldTaskExists) {
                Optional<String> taskDepMsg = taskDefinitionService.taskDepOnTaskMsg(
                        processDefinition.getProjectCode(), oldProcessTaskRelation.getProcessDefinitionCode(),
                        oldProcessTaskRelation.getPostTaskCode());
                taskDepMsg.ifPresent(sb::append);
            }

            if (StrUtil.isNotBlank(sb)) {
                throw new BizException(ResponseCode.FAILED.getCode(), sb.toString());
            }
        }
    }

    private void saveProcessDefine(TDsProcessDefinition processDefinition) {
        processDefinition.setVersion(0);
        processDefinition.setProjectCode(-1L);
        boolean insertResult = tDsProcessDefinitionService.save(processDefinition);
        if (!insertResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "保存工作流定义失败");
        }

        TDsProcessDefinitionLog processDefinitionLog = ModelBeanUtil.copyBean(processDefinition, new TDsProcessDefinitionLog());
        processDefinitionLog.setOperator(null);
        processDefinitionLog.setOperateTime(processDefinition.getUpdateTime());
        processDefinitionLog.setId(null);
        insertResult = tDsProcessDefinitionLogService.save(processDefinitionLog);
        if (!insertResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "保存工作流定义失败");
        }
    }

    private void updateProcessDefine(TDsProcessDefinition processDefinition) {
        boolean insertResult = tDsProcessDefinitionService.updateById(processDefinition);
        if (!insertResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "更新工作流定义失败");
        }

        TDsProcessDefinitionLog processDefinitionLog = ModelBeanUtil.copyBean(processDefinition, new TDsProcessDefinitionLog());
        processDefinitionLog.setVersion(0);
        processDefinitionLog.setOperator(null);
        processDefinitionLog.setOperateTime(processDefinition.getUpdateTime());
        processDefinitionLog.setId(null);
        boolean updateResult = tDsProcessDefinitionLogService.updateByByCodeAndVersion(processDefinitionLog);
        if (!updateResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "更新工作流定义失败");
        }
    }

    private TDsProcessDefinitionLog publishProcessDefine(Long processDefinitionCode) {
        TDsProcessDefinitionLog tDsProcessDefinitionLog = tDsProcessDefinitionLogService.selectByCodeAndVersion(processDefinitionCode, 0);
        Integer version = tDsProcessDefinitionLogService.selectMaxVersionForDefinition(tDsProcessDefinitionLog.getCode());
        tDsProcessDefinitionLog.setId(null);
        tDsProcessDefinitionLog.setVersion(version + 1);
        tDsProcessDefinitionLog.setReleaseState(ReleaseState.ONLINE.getCode());
        tDsProcessDefinitionLog.setUpdateTime(new Date());
        boolean insertResult = tDsProcessDefinitionLogService.save(tDsProcessDefinitionLog);
        if (!insertResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "保存工作流定义失败");
        }

        TDsProcessDefinition tDsProcessDefinition = tDsProcessDefinitionService.getByCode(processDefinitionCode);
        tDsProcessDefinition.setVersion(tDsProcessDefinitionLog.getVersion());
        tDsProcessDefinition.setReleaseState(ReleaseState.ONLINE.getCode());
        tDsProcessDefinition.setUpdateTime(tDsProcessDefinitionLog.getUpdateTime());
        boolean updateResult = tDsProcessDefinitionService.updateById(tDsProcessDefinition);
        if (!updateResult) {
            throw new BizException(ResponseCode.FAILED.getCode(), "更新工作流定义失败");
        }

        return tDsProcessDefinitionLog;
    }


    private List<TDsTaskDefinitionLog> generateTaskDefinitionList(List<TaskDefinition> taskDefinitionList) {
        if (CollUtil.isEmpty(taskDefinitionList)) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "任务定义不能为空");
        }
        taskDefinitionList.forEach(TaskDefinition::validate);

        return taskDefinitionList.stream().map(it -> {
            if ("CMDB_INTEGRATION".equals(it.getTaskType())) {
                TDsTaskDefinitionLog tDsTaskDefinitionLog = ModelBeanUtil.copyBean(it, new TDsTaskDefinitionLog());
                AbstractParameters parameters = it.getParametersObj();
                parameters.setLocalParams(CollUtil.isNotEmpty(it.getTaskParamList()) ? it.getTaskParamList() : CollUtil.newArrayList());
                if (CollUtil.isNotEmpty(it.getResourceIds())) {
                    tDsTaskDefinitionLog.setResourceIds(it.getResourceIds().stream().map(Object::toString)
                        .collect(Collectors.joining(",")));
                    parameters.setResourceFilesList(it.getResourceIds().stream().map(ResourceInfo::new).collect(Collectors.toList()));
                } else {
                    tDsTaskDefinitionLog.setResourceIds("");
                    parameters.setResourceFilesList(CollUtil.newArrayList());
                }
                // 转化为真实类型
                if (StrUtil.isNotBlank(parameters.rawType())) {
                    tDsTaskDefinitionLog.setTaskType(parameters.rawType());
                    tDsTaskDefinitionLog.setDisplayType(it.getTaskType());
                    AbstractParameters.OtherParam otherParam = new AbstractParameters.OtherParam();
                    otherParam.setResourceFileNameList(resourceFileName(it.getResourceIds()));
                    parameters = parameters.toRawParameters(otherParam);
                }
                tDsTaskDefinitionLog.setFlag(Flag.valueOf(it.getFlag()).getCode());
                tDsTaskDefinitionLog.setTaskPriority(Priority.valueOf(it.getTaskPriority()).getCode());
                tDsTaskDefinitionLog.setTaskParams(JsonUtil.encode(parameters));
                return tDsTaskDefinitionLog;
            } else {
                TDsTaskDefinitionLog tDsTaskDefinitionLog = ModelBeanUtil.copyBean(it, new TDsTaskDefinitionLog());
                Map<String, Object> map = MapUtil.defaultIfEmpty(it.getTaskParams(), MapUtil.newHashMap());
                if (CollUtil.isNotEmpty(it.getTaskParamList())) {
                    map.put("localParams", it.getTaskParamList());
                } else {
                    map.put("localParams", CollUtil.newArrayList());
                }
                tDsTaskDefinitionLog.setFlag(Flag.valueOf(it.getFlag()).getCode());
                tDsTaskDefinitionLog.setTaskPriority(Priority.valueOf(it.getTaskPriority()).getCode());
                if (CollUtil.isNotEmpty(it.getResourceIds())) {
                    tDsTaskDefinitionLog.setResourceIds(it.getResourceIds().stream().map(Object::toString)
                        .collect(Collectors.joining(",")));
                    map.put("resourceList", it.getResourceIds().stream().map(ResourceInfo::new).collect(Collectors.toList()));
                } else {
                    tDsTaskDefinitionLog.setResourceIds("");
                    map.put("resourceList", CollUtil.newArrayList());
                }
                it.setTaskParams(map);
                tDsTaskDefinitionLog.setTaskParams(JsonUtil.encode(it.getTaskParams()));
                return tDsTaskDefinitionLog;
            }
        }).collect(Collectors.toList());

    }

    private List<String> resourceFileName(List<Integer> resourceIdList) {
        if (CollUtil.isEmpty(resourceIdList)) {
            return CollUtil.newArrayList();
        }
        List<TDsResources> tDsResourcesList = tDsResourcesService.listByIds(resourceIdList);
        return tDsResourcesList.stream().map(TDsResources::getAlias).collect(Collectors.toList());
    }


    private List<TDsProcessTaskRelationLog> generateTaskRelationList(List<TaskRelation> taskRelationList,
                                                                     List<TDsTaskDefinitionLog> taskDefinitionLogList) {
        try {
            taskDefinitionLogList = CollUtil.isEmpty(taskDefinitionLogList) ? CollUtil.newArrayList() : taskDefinitionLogList;

            List<TDsProcessTaskRelationLog> tDsProcessTaskRelationList;
            if (taskDefinitionLogList.size() == 1) {
                TDsTaskDefinitionLog tDsTaskDefinitionLog = taskDefinitionLogList.get(0);
                tDsProcessTaskRelationList = CollUtil.newArrayList(genNoPenetrationRelation(tDsTaskDefinitionLog.getCode(),
                        tDsTaskDefinitionLog.getVersion()));
            } else {
                tDsProcessTaskRelationList = taskRelationList.stream().map(it -> {
                    TDsProcessTaskRelationLog tDsProcessTaskRelationLog = ModelBeanUtil.copyBean(it, new TDsProcessTaskRelationLog());
                    tDsProcessTaskRelationLog.setConditionType(Optional.ofNullable(it.getConditionType()).map(ConditionType::valueOf)
                            .map(ConditionType::getCode).orElse(ConditionType.NONE.getCode()));
                    tDsProcessTaskRelationLog.setConditionParams(Optional.ofNullable(it.getConditionParams())
                            .map(JsonUtil::encode).orElse("{}"));
                    return tDsProcessTaskRelationLog;
                }).collect(Collectors.toList());
            }

            // check whether the task relation json is normal
            for (TDsProcessTaskRelationLog processTaskRelation : tDsProcessTaskRelationList) {
                if (processTaskRelation.getPostTaskCode() == 0) {
                    throw new BizException(ResponseCode.FAILED.getCode(), "工作流任务关系参数错误");
                }
            }

            List<TaskNode> taskNodeList = transformTask(tDsProcessTaskRelationList, taskDefinitionLogList);
            if (taskNodeList.size() != taskRelationList.size()) {
                Set<Long> postTaskCodes = tDsProcessTaskRelationList.stream().map(TDsProcessTaskRelationLog::getPostTaskCode)
                        .collect(Collectors.toSet());
                Set<Long> taskNodeCodes = taskNodeList.stream().map(TaskNode::getCode).collect(Collectors.toSet());
                Collection<Long> codes = CollectionUtils.subtract(postTaskCodes, taskNodeCodes);
                if (CollectionUtils.isNotEmpty(codes)) {
                    throw new BizException(ResponseCode.FAILED.getCode(), String.format("任务定义%s不存在",
                            codes.stream().map(Object::toString).collect(Collectors.joining(","))));
                }
            }
            // 判断任务节点是否有环
            DAG<String, TaskNode, String> dag = genDag(taskNodeList);
            if (CollUtil.isNotEmpty(taskNodeList) && dag.hasCycle()) {
                throw new BizException(ResponseCode.FAILED.getCode(), "流程节点间存在循环依赖");
            }

            // 获取起始顶点
            Set<Long> beginNodeSet = dag.getBeginNode().stream().map(Long::parseLong).collect(Collectors.toSet());
            // 插入起始顶点边
            List<TDsProcessTaskRelationLog> beginNodeRelationList = taskDefinitionLogList.stream()
                    .filter(it -> beginNodeSet.contains(it.getCode()))
                    .map(it -> genNoPenetrationRelation(it.getCode(), it.getVersion())).collect(Collectors.toList());

            // 过滤出需要添加的入度为0的边
            List<TDsProcessTaskRelationLog> list = beginNodeRelationList.stream()
                    .filter(it -> !tDsProcessTaskRelationList.contains(it)).collect(Collectors.toList());
            list.addAll(tDsProcessTaskRelationList);

            return list;
        } catch (Exception e) {
            LOGGER.error("转化工作流之间的关系异常", e);
            throw new BizException(ResponseCode.FAILED.getCode(), "转化工作流之间的关系异常", e);
        }
    }

    /**
     * 构架一个没有入度的边关系
     *
     * @param postTaskCode
     * @param postTaskVersion
     * @return
     */
    private TDsProcessTaskRelationLog genNoPenetrationRelation(Long postTaskCode, Integer postTaskVersion) {
        TDsProcessTaskRelationLog tDsProcessTaskRelationLog = new TDsProcessTaskRelationLog();
        tDsProcessTaskRelationLog.setPreTaskCode(0L);
        tDsProcessTaskRelationLog.setPreTaskVersion(0);
        tDsProcessTaskRelationLog.setPostTaskCode(postTaskCode);
        tDsProcessTaskRelationLog.setPostTaskVersion(postTaskVersion);
        tDsProcessTaskRelationLog.setConditionType(ConditionType.NONE.getCode());
        tDsProcessTaskRelationLog.setConditionParams(JsonUtil.encode(MapUtil.newHashMap()));
        return tDsProcessTaskRelationLog;
    }


    private List<TaskNode> transformTask(List<TDsProcessTaskRelationLog> taskRelationLogList,
                                         List<TDsTaskDefinitionLog> taskDefinitionLogList) {
        // 将依赖关系转化为Map<当前节点, 依赖列表>
        Map<Long, List<Long>> taskCodeMap = new HashMap<>(16);
        for (TDsProcessTaskRelationLog processTaskRelation : taskRelationLogList) {
            taskCodeMap.compute(processTaskRelation.getPostTaskCode(), (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>();
                }
                if (processTaskRelation.getPreTaskCode() != 0L) {
                    v.add(processTaskRelation.getPreTaskCode());
                }
                return v;
            });
        }
        Map<Long, TDsTaskDefinitionLog> taskDefinitionMap = taskDefinitionLogList.stream()
                .collect(Collectors.toMap(TDsTaskDefinitionLog::getCode, taskDefinitionLog -> taskDefinitionLog));
        // 构建TaskNode
        List<TaskNode> taskNodeList = new ArrayList<>();
        for (Map.Entry<Long, List<Long>> code : taskCodeMap.entrySet()) {
            TDsTaskDefinitionLog taskDefinition = taskDefinitionMap.get(code.getKey());
            if (taskDefinition != null) {
                TaskNode taskNode = new TaskNode();
                taskNode.setCode(taskDefinition.getCode());
                try {
                    taskNode.setPreTasks(JsonUtil.encode(code.getValue().stream().map(taskDefinitionMap::get)
                            .map(TDsTaskDefinitionLog::getCode).collect(Collectors.toList())));
                } catch (Exception e) {
                    LOGGER.error("转化工作流之间的关系异常, {} 找不到节点:{}", code.getKey(), code.getValue(), e);
                    throw new RuntimeException(e);
                }
                taskNodeList.add(taskNode);
            }
        }
        Set<Long> nodeCodeSet = taskNodeList.stream().map(TaskNode::getCode).collect(Collectors.toSet());
        // 顶点
        List<TDsTaskDefinitionLog> beginTaskDefinitionList = taskDefinitionLogList.stream()
                .filter(it -> !nodeCodeSet.contains(it.getCode())).collect(Collectors.toList());

        for (TDsTaskDefinitionLog tDsTaskDefinition : beginTaskDefinitionList) {
            TaskNode taskNode = new TaskNode();
            taskNode.setCode(tDsTaskDefinition.getCode());
            taskNode.setPreTasks(JsonUtil.encode(new ArrayList<>()));
            taskNodeList.add(taskNode);
        }

        return taskNodeList;
    }

    /**
     * 生成DAG
     *
     * @return
     */
    private DAG<String, TaskNode, String> genDag(List<TaskNode> taskNodeResponseList) {
        DAG<String, TaskNode, String> dag = new DAG<>();
        // Fill the vertices
        for (TaskNode taskNodeResponse : taskNodeResponseList) {
            dag.addNode(Long.toString(taskNodeResponse.getCode()), taskNodeResponse);
        }
        // Fill edge relations
        for (TaskNode taskNodeResponse : taskNodeResponseList) {
            List<String> preTasks = JsonUtil.decode2ListString(taskNodeResponse.getPreTasks());
            if (CollectionUtils.isNotEmpty(preTasks)) {
                for (String preTask : preTasks) {
                    if (!dag.addEdge(preTask, Long.toString(taskNodeResponse.getCode()))) {
                        throw new BizException(ResponseCode.FAILED.getCode(), "流程节点间存在循环依赖");
                    }
                }
            }
        }
        return dag;
    }


    private void processDefinitionUsedInOtherTaskValid(TDsProcessDefinition processDefinition) {
        // check process definition is already online
        if (processDefinition.getReleaseState() == ReleaseState.ONLINE.getCode()) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流定义%s已上线, 请先下线后再删除", processDefinition.getName()));
        }

        // check process instances is already running
        List<TDsProcessInstance> processInstances = processInstanceService.queryByProcessDefineCodeAndStatus(processDefinition.getCode(),
                CollUtil.newHashSet(0, 1, 12, 2, 4));
        if (CollectionUtils.isNotEmpty(processInstances)) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("删除工作流定义失败，有%s个运行中的工作流实例正在使用", processInstances.size()));
        }

        // check process used by other task, including subprocess and dependent task type
        Set<TaskMainInfo> taskDepOnProcess = taskDefinitionService
                .queryTaskDepOnProcess(processDefinition.getProjectCode(), processDefinition.getCode());
        if (CollectionUtils.isNotEmpty(taskDepOnProcess)) {
            String taskDepDetail = taskDepOnProcess.stream()
                    .map(task -> String.format("%s:%s", task.getProcessDefinitionName(),
                            task.getTaskName()))
                    .collect(Collectors.joining(","));
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("删除工作流定时失败，被其他任务引用：%s", taskDepDetail));
        }
    }

    /**
     * 保存或更新标签
     *
     * @param id
     * @param tagList
     */
    private void saveOrUpdateTag(Long id, List<String> tagList) {
        if (CollUtil.isEmpty(tagList)) {
            tagRelationService.removeByRecordIdAndRecordType(id, TagRelationRecordTypeEnum.DS_PROCESS_DEFINITION);
            return;
        }
        tagRelationService.saveTagAndTagRelation(TagTypeEnum.OFFLINE_WORKFLOW, tagList,
                TagRelationRecordTypeEnum.DS_PROCESS_DEFINITION, id);
    }


    private void createBusinessFlowTree(TDsProcessDefinition entity) {
        BusinessFlowTreeCreateReq createReq = new BusinessFlowTreeCreateReq();
        createReq.setName(entity.getName());
        createReq.setStatus(Arrays.stream(ReleaseState.values()).filter(it ->
                entity.getReleaseState().equals(it.getCode())).findFirst().map(Enum::name).orElse(null));
        createReq.setIsPublished(entity.getReleaseState());
        createReq.setType(BusinessFlowTreeReferenceTypeEnum.OFFLINE_WORKFLOW.code());
        createReq.setReferenceType(BusinessFlowTreeReferenceTypeEnum.OFFLINE_WORKFLOW.code());
        createReq.setCategory(BusinessFlowTreeCategoryEnum.OFFLINE_DATA_PROCESSING.code());
        createReq.setBusinessFlowId(entity.getBusinessFlowId());
        createReq.setReferenceId(entity.getId());
        createReq.setIsPublished(entity.getReleaseState());
        createReq.setResourceType(ProjectResourceTypeEnum.T_DS_PROCESS_DEFINITION.code());
        businessFlowTreeService.create(createReq);
    }

    private void updateBusinessFlowTree(TDsProcessDefinition entity) {
        BusinessFlowTreeResp treeResp = businessFlowTreeService.getOne(
                BusinessFlowTreeReferenceTypeEnum.OFFLINE_WORKFLOW.code(), entity.getId());
        BusinessFlowTreeUpdateReq updateReq = new BusinessFlowTreeUpdateReq();
        updateReq.setId(treeResp.getId());
        updateReq.setBusinessFlowId(entity.getBusinessFlowId());
        updateReq.setName(entity.getName());
        updateReq.setStatus(Arrays.stream(ReleaseState.values()).filter(it ->
                entity.getReleaseState().equals(it.getCode())).findFirst().map(Enum::name).orElse(null));
        updateReq.setIsPublished(entity.getReleaseState());
        businessFlowTreeService.update(updateReq);
    }

    private void deleteBusinessFlowTree(Long id) {
        businessFlowTreeService.delete(BusinessFlowTreeReferenceTypeEnum.OFFLINE_WORKFLOW.code(), id);
    }

    @Override
    public String sheetName() {
        return "离线工作流定义";
    }

    @Override
    public void verifyData(List<DataDeveloperExcelModel> importRows) {
        if (CollUtil.isEmpty(importRows)) {
            return;
        }

        List<TDsProcessDefinition> list = new ArrayList<>();
        for (int i = 1; ; i++) {
            Page<TDsProcessDefinition> page = tDsProcessDefinitionService.page(new Page<>(i, 1000));
            if (CollUtil.isEmpty(page.getRecords())) {
                break;
            }
            list.addAll(page.getRecords());
        }

        Map<String, TDsProcessDefinition> oldWorkFlow = list.stream()
                .collect(Collectors.toMap(it -> it.getBusinessFlowId() + "_" + it.getName(), it -> it, (v1, v2) -> v1));
        importRows.forEach(it -> {
            it.setType(BusinessFlowTreeReferenceTypeEnum.OFFLINE_WORKFLOW.code());
            TDsProcessDefinition tDsProcessDefinition = oldWorkFlow.get(it.getBusinessFlowId() + "_" + it.getName());
            if (Objects.nonNull(tDsProcessDefinition)) {
                it.setCoverField("name");
                it.setId(tDsProcessDefinition.getId());
                it.setCode(tDsProcessDefinition.getCode());

                if (ContextHolder.getProjectAuthorityEnable()) {
                    TDsProcessDefinition authProcessDefinition =
                            tDsProcessDefinitionService.selectByIdWithProjectAuth(tDsProcessDefinition.getId());
                    if (Objects.isNull(authProcessDefinition) || !authProcessDefinition.hasWritePrivilege()) {
                        it.setFailedReason("无权限修改");
                    }
                }
            }
        });

    }

    @Override
    public void saveRows(Boolean skip, List<DataDeveloperExcelModel> importRows) {
        if (CollUtil.isEmpty(importRows)) {
            return;
        }

        verifyData(importRows);

        saveRowsCustomer(skip,
                importRows,
                (item -> {
                    DataDeveloperExcelModel it = (DataDeveloperExcelModel) item;

                    ProcessDefinitionCreateReq req = new ProcessDefinitionCreateReq();
                    req.setBusinessFlowId(it.getBusinessFlowId());
                    req.setName(it.getName());
                    OfflineWorkFlowExcelExportModel exportModel =
                            JSONUtil.toBean(it.getTaskConfig(), OfflineWorkFlowExcelExportModel.class);
                    ModelBeanUtil.copyBean(exportModel, req);
                    req.setName(it.getName());
                    req.setSchedule(buildSchedule(exportModel.getSchedule()));
                    req.setGlobalParams(buildParamProperties(exportModel.getGlobalParams()));

                    Map<Long, Long> taskCodeMap = new HashMap(1);
                    req.setTaskDefinitionList(buildTaskDefinitionList(null, exportModel.getTaskDefinitionList(),
                            true, taskCodeMap));
                    req.setTaskRelationList(buildTaskRelationList(exportModel.getTaskRelationList(), taskCodeMap));
                    req.setLocations(buildLocations(exportModel.getLocations(), taskCodeMap));
                    ProcessDefinitionResp processDefinitionResp = create(req);
                    it.setId(processDefinitionResp.getId());
                    return req;
                }),
                (item -> {
                    DataDeveloperExcelModel it = (DataDeveloperExcelModel) item;
                    ProcessDefinitionUpdateReq req = new ProcessDefinitionUpdateReq();
                    req.setBusinessFlowId(it.getBusinessFlowId());
                    req.setName(it.getName());
                    OfflineWorkFlowExcelExportModel exportModel =
                            JSONUtil.toBean(it.getTaskConfig(), OfflineWorkFlowExcelExportModel.class);
                    ModelBeanUtil.copyBean(exportModel, req);
                    req.setId(it.getId());
                    req.setCode(it.getCode());
                    req.setName(it.getName());
                    Schedule schedule = schedulesService.get(req.getCode());
                    Long scheduleId = null;
                    if (schedule != null) {
                        scheduleId = schedule.getId();
                    }

                    Schedule schedule1 = buildSchedule(exportModel.getSchedule());
                    schedule1.setId(scheduleId);
                    req.setSchedule(schedule1);
                    req.setSchedule(buildSchedule(exportModel.getSchedule()));
                    req.setGlobalParams(buildParamProperties(exportModel.getGlobalParams()));
                    Map<Long, Long> taskCodeMap = new HashMap(1);
                    req.setTaskDefinitionList(buildTaskDefinitionList(req.getCode(), exportModel.getTaskDefinitionList(),
                            false, taskCodeMap));
                    req.setTaskRelationList(buildTaskRelationList(exportModel.getTaskRelationList(), taskCodeMap));
                    req.setLocations(buildLocations(exportModel.getLocations(), taskCodeMap));

                    update(req);
                    return req;
                }));
    }

    private List<TaskLocation> buildLocations(String locations, Map<Long, Long> taskDefinitionMap) {
        List<TaskLocation> relations = new ArrayList<>();
        JSONArray jsonArray = JSONUtil.parseArray(locations);
        for (int i = 0; i < jsonArray.size(); i++) {
            TaskLocation task = new TaskLocation();
            if (jsonArray.getJSONObject(i).getLong("taskCode") != null
                    && taskDefinitionMap.get(jsonArray.getJSONObject(i).getLong("taskCode")) != null) {
                task.setTaskCode(jsonArray.getJSONObject(i).getLong("taskCode"));
            }
            task.setX(jsonArray.getJSONObject(i).getLong("x"));
            task.setY(jsonArray.getJSONObject(i).getLong("y"));
            relations.add(task);
        }
        return relations;
    }

    private List<TaskDefinition> buildTaskDefinitionList(Long processDefinitionId, String taskDefinitionList,
                                                         boolean newFlag, Map<Long, Long> taskCodeMap) {
        List<TaskDefinition> relations = new ArrayList<>();
        JSONArray jsonArrayTemp = JSONUtil.parseArray(taskDefinitionList);
        Map<String, TaskDefinition> oldTaskDefinitionMap = new HashMap<>(1);
        if (!newFlag) {
            List<TaskDefinition> taskDefinitions = taskDefinitionService.queryLogByProcessDefinitionCode(processDefinitionId);
            Map<String, TaskDefinition> collect = taskDefinitions.stream().collect(Collectors.toMap(TaskDefinition::getName,
                    Function.identity()));
            oldTaskDefinitionMap.putAll(collect);
        }
        for (int i = 0; i < jsonArrayTemp.size(); i++) {
            TaskDefinition postTaskCodeName = oldTaskDefinitionMap.get(jsonArrayTemp.getJSONObject(i).getStr("name"));
            // 如何是新增的工作流定义或者新增的任务需要重新生成code
            if (newFlag && postTaskCodeName == null) {
                taskCodeMap.put(jsonArrayTemp.getJSONObject(i).getLong("code"), CodeGenerateUtils.getInstance().genCode());
            } else {
                taskCodeMap.put(jsonArrayTemp.getJSONObject(i).getLong("code"), postTaskCodeName.getCode());
            }
        }
        String taskDefinitionListNew = taskDefinitionList;
        if (taskCodeMap.size() > 0) {
            for (Map.Entry<Long, Long> codeNew : taskCodeMap.entrySet()) {
                taskDefinitionListNew = taskDefinitionListNew.replaceAll("" + codeNew.getKey(), codeNew.getValue().toString());
            }
        }

        JSONArray jsonArray = JSONUtil.parseArray(taskDefinitionListNew);
        for (int i = 0; i < jsonArray.size(); i++) {
            TaskDefinition task = JSONUtil.toBean(jsonArray.getJSONObject(i), TaskDefinition.class);
            if (!newFlag) {
                TaskDefinition postTaskCodeName = oldTaskDefinitionMap.get(jsonArray.getJSONObject(i).getStr("name"));
                if (postTaskCodeName != null) {
                    task.setId(postTaskCodeName.getId());
                    task.setCode(postTaskCodeName.getCode());
                }
            }
            JSONObject taskObject = jsonArray.getJSONObject(i);
            buildTaskInfo(task, taskObject);
            relations.add(task);
        }
        return relations;
    }

    private void buildTaskInfo(TaskDefinition task, JSONObject taskObject) {
        List<Integer> resourceIdsList = new ArrayList<>();
        List<ResourceInfo> resourceInfosList = new ArrayList<>();
        if (taskObject.get("resourceParams") != null
                && taskObject.get("resourceParams") instanceof JSONArray) {
            JSONArray resourceIds = taskObject.getJSONArray("resourceParams");
            if (resourceIds != null) {
                for (int j = 0; j < resourceIds.size(); j++) {
                    TDsResources one = tDsResourcesService.getOne(Wrappers.<TDsResources>lambdaQuery()
                            .eq(TDsResources::getFullName, resourceIds.getJSONObject(j).getStr("fullName"))
                            .eq(TDsResources::getType, resourceIds.getJSONObject(j).getStr("type")));

                    if (one != null) {
                        resourceIdsList.add(one.getId().intValue());
                        resourceInfosList.add(new ResourceInfo(one.getId().intValue()));
                    }
                }
                task.getTaskParams().put("resourceList", resourceInfosList);
            }
        }
        task.setResourceIds(resourceIdsList);
        Map<String, Object> taskParams = JSONUtil.toBean(taskObject.getJSONObject("taskParams"), Map.class);
        task.setTaskParamList(new ArrayList<>());
        if (taskParams != null) {
            List<ParamProperty> paramProperties = buildParamProperties(JSONUtil
                    .toJsonStr(taskParams.get("localParams")));
            if (taskParams.get("localParams") != null) {
                taskParams.put("localParams", paramProperties);
            }
            taskParams.put("resourceList", resourceIdsList);
            task.setTaskParams(taskParams);
            task.setTaskParamList(paramProperties);
        }

        if (task.getTaskParams() == null) {
            task.setTaskParams(new HashMap<>(1));
        }
    }

    private List<TaskRelation> buildTaskRelationList(String taskRelationListStr,
                                                     Map<Long, Long> taskCodeMap) {

        String taskRelationListNew = taskRelationListStr;
        if (taskCodeMap.size() > 0) {
            for (Map.Entry<Long, Long> codeNew : taskCodeMap.entrySet()) {
                taskRelationListNew = taskRelationListNew.replaceAll("" + codeNew.getKey(), codeNew.getValue().toString());
            }
        }

        List<TaskRelation> relations = new ArrayList<>();
        JSONArray jsonArray = JSONUtil.parseArray(taskRelationListNew);
        for (int i = 0; i < jsonArray.size(); i++) {
            TaskRelation taskRelation = jsonArray.getBean(i, TaskRelation.class);
            relations.add(taskRelation);
        }
        return relations;
    }

    private List<ParamProperty> buildParamProperties(String globalParams) {
        List<ParamProperty> paramProperties = new ArrayList<>();
        JSONArray jsonArray = JSONUtil.parseArray(globalParams);
        if (jsonArray != null) {
            for (int i = 0; i < jsonArray.size(); i++) {
                ParamProperty paramProperty = JSONUtil.toBean(jsonArray.getJSONObject(i), ParamProperty.class);
                paramProperties.add(paramProperty);
            }
        }

        return paramProperties;
    }

    private Schedule buildSchedule(String schedule) {

        return JSONUtil.toBean(schedule, Schedule.class);
    }

    @Override
    public List<DataDeveloperExcelModel> exportData(ImportExcelReq req) {
        return null;
    }
}
