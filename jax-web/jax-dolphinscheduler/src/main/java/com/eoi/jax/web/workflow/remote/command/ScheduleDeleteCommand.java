package com.eoi.jax.web.workflow.remote.command;

import org.apache.dolphinscheduler.common.utils.JSONUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
public class ScheduleDeleteCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long projectCode;

    private Integer scheduleId;

    public ScheduleDeleteCommand() {
    }

    public ScheduleDeleteCommand(Long projectCode, Integer scheduleId) {
        this.projectCode = projectCode;
        this.scheduleId = scheduleId;
    }

    /**
     * package response command
     *
     * @return command
     */
    public Command convert2Command() {
        Command command = new Command();
        command.setType(CommandType.SCHEDULE_DELETE_REQUEST);
        byte[] body = JSONUtils.toJsonByteArray(this);
        command.setBody(body);
        return command;
    }


    public Long getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(Long projectCode) {
        this.projectCode = projectCode;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    @Override
    public String toString() {
        return "ScheduleDeleteCommand{" +
                "projectId=" + projectCode +
                ", scheduleId=" + scheduleId +
                '}';
    }
}
