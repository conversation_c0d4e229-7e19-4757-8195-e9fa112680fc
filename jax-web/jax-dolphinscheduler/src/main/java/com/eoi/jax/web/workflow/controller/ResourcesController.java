package com.eoi.jax.web.workflow.controller;

import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.core.security.UserContext;
import com.eoi.jax.web.workflow.model.resources.ResourcesQueryReq;
import com.eoi.jax.web.workflow.service.ResourcesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/10/28
 */
@RestController
public class ResourcesController implements V2Controller {

    @Autowired
    private ResourcesService resourcesService;


    @Operation(summary = "资源查询")
    @GetMapping("workflow/resource/list")
    public Response list(@RequestParam("businessFlowId") Long businessFlowId) {
        return Response.success(resourcesService.list(businessFlowId));
    }

    @Operation(summary = "资源查询分页查询")
    @PostMapping("workflow/resource/query")
    public Response query(@Valid @RequestBody ResourcesQueryReq req) {
        return Response.success(resourcesService.query(req));
    }

    @Operation(summary = "根据id查询详情")
    @GetMapping("workflow/resource/{id}")
    public Response get(@Parameter(description = "资源id", required = true) @PathVariable("id") Long id) {
        return Response.success(resourcesService.get(id));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.CREATE, module = "数据开发", function = "离线开发", code = "resources")
    @Operation(summary = "资源创建")
    @PostMapping(value = "workflow/resource", consumes = "multipart/form-data")
    public Response create(@Parameter(description = "file", required = true) @RequestPart("file") MultipartFile file,
                           @RequestParam(value = "businessFlowId") Long businessFlowId,
                           @RequestParam(value = "alias") String alias,
                           @RequestParam(value = "description", required = false) String description,
                           @RequestParam(value = "clusterId") Long clusterId
    ) {
        return Response.success(resourcesService.create(businessFlowId, alias, description, clusterId, file));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.UPDATE, module = "数据开发", function = "离线开发", code = "resources")
    @Operation(summary = "资源更新")
    @PutMapping(value = "workflow/resource/{id}", consumes = "multipart/form-data")
    public Response update(@Parameter(description = "file", required = true) @RequestPart("file") MultipartFile file,
                           @OpPrimaryKey(name = "id") @Parameter(description = "资源id", required = true) @PathVariable("id") Long id,
                           @RequestParam(value = "businessFlowId") Long businessFlowId,
                           @RequestParam(value = "alias") String alias,
                           @RequestParam(value = "description", required = false) String description,
                           @RequestParam(value = "clusterId") Long clusterId
    ) {
        return Response.success(resourcesService.update(id, businessFlowId, alias, description, clusterId, file));
    }

    @AuditLog(category = "数据开发", opAction = OpActionEnum.DELETE, module = "数据开发", function = "离线开发", code = "resources")
    @Operation(summary = "资源删除")
    @DeleteMapping("workflow/resource/{id}")
    public Response delete(
            @OpPrimaryKey(name = "id")
            @Parameter(description = "工作流定义主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(resourcesService.delete(id));
    }


    @Operation(summary = "资源下载")
    @GetMapping("workflow/resource/download/{id}")
    public void download(@RequestParam(value = "jaxSuperProjectId", required = false) Long jaxSuperProjectId,
                         @Parameter(description = "资源id", required = true) @PathVariable("id") Long id,
                         HttpServletResponse response) {
        UserContext ctx = ContextHolder.getUserContext();
        if (ctx != null && jaxSuperProjectId != null) {
            ctx.setCurrentProjectId(jaxSuperProjectId);
        }
        resourcesService.download(id, response);
    }


    @Operation(summary = "资源关联数据")
    @GetMapping("workflow/resource/{id}/usage")
    public Response usage(@Parameter(description = "worker分组主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(resourcesService.usage(id));
    }

}
