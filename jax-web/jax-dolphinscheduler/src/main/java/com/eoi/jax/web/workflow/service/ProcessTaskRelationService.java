package com.eoi.jax.web.workflow.service;

import com.eoi.jax.web.repository.entity.TDsProcessTaskRelation;
import com.eoi.jax.web.repository.entity.TDsProcessTaskRelationLog;
import com.eoi.jax.web.repository.entity.TDsTaskDefinition;
import com.eoi.jax.web.workflow.model.processdefinition.TaskRelation;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
public interface ProcessTaskRelationService {

    /**
     * 保存任务关系
     * @param projectCode
     * @param processDefinitionCode
     * @param processDefinitionVersion
     * @param taskRelationList
     * @param taskDefinitionList
     */
    void saveTaskRelation(Long projectCode, Long processDefinitionCode,
                          Integer processDefinitionVersion,
                          List<TDsProcessTaskRelationLog> taskRelationList,
                          List<TDsTaskDefinition> taskDefinitionList);

    /**
     * 更新任务关系
     * @param projectCode
     * @param processDefinitionCode
     * @param processDefinitionVersion
     * @param taskRelationList
     * @param taskDefinitionList
     */
    void updateTaskRelation(Long projectCode, Long processDefinitionCode,
                            Integer processDefinitionVersion,
                            List<TDsProcessTaskRelationLog> taskRelationList,
                            List<TDsTaskDefinition> taskDefinitionList);


    /**
     * 发布任务关系
     * @param processDefinitionCode
     * @param processDefinitionVersion
     */
    void publishTaskRelation(Long processDefinitionCode, Integer processDefinitionVersion);


    /**
     * 根据工作流code查询任务关系日志
     * @param processCode
     * @param processVersion
     * @return
     */
    List<TaskRelation> queryLogByProcessDefinitionCode(Long processCode, Integer processVersion);

    /**
     * 根据工作流code
     * @param processCode
     * @param processVersion
     * @return
     */
    List<TDsProcessTaskRelation> queryByProcessCode(Long processCode, Integer processVersion);

    /**
     * 根据工作流编码删除
     * @param processCode
     * @return
     */
    boolean deleteByCode(Long processCode);
}
