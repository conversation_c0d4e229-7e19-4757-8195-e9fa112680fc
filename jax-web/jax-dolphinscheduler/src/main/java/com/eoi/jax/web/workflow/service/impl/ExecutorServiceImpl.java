package com.eoi.jax.web.workflow.service.impl;


import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.ingestion.model.projectresource.OfflineSparkProcessResp;
import com.eoi.jax.web.ingestion.model.projectresource.ProjectResourceReq;
import com.eoi.jax.web.ingestion.model.projectresource.ProjectResourceResp;
import com.eoi.jax.web.ingestion.service.ProjectResourceService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.search.result.DependentProcessDefinition;
import com.eoi.jax.web.repository.service.*;
import com.eoi.jax.web.workflow.enumrate.ExecuteType;
import com.eoi.jax.web.workflow.enumrate.TaskExecutionStatus;
import com.eoi.jax.web.workflow.exception.CronParseException;
import com.eoi.jax.web.workflow.model.processdefinition.Schedule;
import com.eoi.jax.web.workflow.model.processinstance.ProcessInstanceResp;
import com.eoi.jax.web.workflow.model.runmodel.ProcessDefinitionComplementRunReq;
import com.eoi.jax.web.workflow.model.runmodel.ProcessDefinitionComplementRunResp;
import com.eoi.jax.web.workflow.model.runmodel.ProcessInstanceOperateReq;
import com.eoi.jax.web.workflow.model.taskdefinition.DependentItem;
import com.eoi.jax.web.workflow.model.taskdefinition.DependentObject;
import com.eoi.jax.web.workflow.model.taskdefinition.DependentTaskModel;
import com.eoi.jax.web.workflow.remote.command.WorkflowStateEventChangeCommand;
import com.eoi.jax.web.workflow.remote.processor.StateEventCallbackService;
import com.eoi.jax.web.workflow.remote.utils.Host;
import com.eoi.jax.web.workflow.service.ExecutorService;
import com.eoi.jax.web.workflow.service.ProcessDefinitionHelper;
import com.eoi.jax.web.workflow.service.ProcessInstanceService;
import com.eoi.jax.web.workflow.service.SchedulesService;
import com.eoi.jax.web.workflow.service.TaskDefinitionService;
import com.eoi.jax.web.workflow.util.cron.CronUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dolphinscheduler.common.constants.Constants;
import org.apache.dolphinscheduler.common.enums.*;
import org.apache.dolphinscheduler.common.utils.DateUtils;
import org.apache.dolphinscheduler.common.utils.JSONUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import jakarta.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.dolphinscheduler.common.constants.CommandKeyConstants.*;
import static org.apache.dolphinscheduler.common.constants.Constants.COMMA;
import static org.apache.dolphinscheduler.common.constants.Constants.SCHEDULE_TIME_MAX_LENGTH;

/**
 * <AUTHOR>
 * @date 2023/10/25
 */
@Service
public class ExecutorServiceImpl implements ExecutorService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExecutorServiceImpl.class);

    @Autowired
    private TDsProcessDefinitionService tDsProcessDefinitionService;

    @Autowired
    private TDsTaskDefinitionService tDsTaskDefinitionService;

    @Autowired
    private TDsTaskDefinitionLogService tDsTaskDefinitionLogService;

    @Autowired
    private TDsProcessTaskRelationService tDsProcessTaskRelationService;

    @Autowired
    private TDsProcessInstanceService tDsProcessInstanceService;

    @Autowired
    private TDsTaskInstanceService tDsTaskInstanceService;

    @Autowired
    private TDsCommandService tDsCommandService;

    @Autowired
    private TaskDefinitionService taskDefinitionService;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Autowired
    private SchedulesService schedulesService;

    @Resource
    private StateEventCallbackService stateEventCallbackService;

    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Resource
    private ProjectResourceService projectResourceService;

    @Resource
    private ProcessDefinitionHelper processDefinitionHelper;

    /**
     * 启动业务流程
     *
     * @param req
     * @return
     */
    @Override
    public ProcessDefinitionComplementRunResp startProcessInstance(ProcessDefinitionComplementRunReq req) {
        // check process define release state
        TDsProcessDefinition processDefinition = tDsProcessDefinitionService.existId(req.getId(),
                new BizException(ResponseCode.ID_NOT_EXISTS));
        // 检查子工作流是否上线
        processDefinitionHelper.checkSubProcessIsOnline(processDefinition.getCode());
        if (CommandType.START_PROCESS != req.getCommandType()) {
            checkProcessDefinitionValid(processDefinition.getProjectCode(), processDefinition,
                    processDefinition.getCode(), processDefinition.getVersion());
        }

        checkScheduleTimeNum(req);

        // 资源配额
        if (projectResourceService.checkNeedCalResource()) {
            OfflineSparkProcessResp currentResource =
                    projectResourceService.getOfflineSparkProcessResource(processDefinition.getId());
            long currentCpu = currentResource.getCpu();
            long currentMem = currentResource.getMemory();
            if (currentCpu != 0 || currentMem != 0) {
                ProjectResourceReq projectResourceReq = new ProjectResourceReq();
                projectResourceReq.setStartCheck(true);
                projectResourceReq.setProcessDefinitionId(processDefinition.getId());
                ProjectResourceResp projectResource = projectResourceService.getProjectResource(projectResourceReq);
                projectResource.checkResource(currentCpu, currentMem, req.getForceStart());
            }
        }

        TDsProcessInstance lastProcessInstance = tDsProcessInstanceService.queryLast(processDefinition.getCode());
        if (Objects.nonNull(lastProcessInstance)) {
            if (!WorkflowExecutionStatus.of(lastProcessInstance.getState()).isFinished()) {
                return new ProcessDefinitionComplementRunResp(lastProcessInstance.getId(),
                        lastProcessInstance.getName(), true, "工作流实例正在运行");
            }
        }

        String statTime = DateUtil.formatDateTime(new Date());
        //开启事务
        TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
        try {
            /**
             * create command
             */
            this.createCommand(req);
            //提交事务
            dataSourceTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            //回滚事务
            dataSourceTransactionManager.rollback(transactionStatus);
            throw new BizException(ResponseCode.FAILED.getCode(), "工作流提交失败", e);
        }

        if (CommandType.START_PROCESS == req.getCommandType()) {
            try {
                for (int i = 1; i < 5; i++) {
                    TDsProcessInstance tDsProcessInstance =
                            tDsProcessInstanceService.queryTestInstance(processDefinition.getCode(), statTime);
                    if (Objects.nonNull(tDsProcessInstance)) {
                        return new ProcessDefinitionComplementRunResp(tDsProcessInstance.getId(), tDsProcessInstance.getName());
                    }
                    Thread.sleep(500 * i);
                }
                throw new BizException(ResponseCode.FAILED.getCode(), "工作流提交成功，但获取工作流实例id失败, 请到工作流实例页面查看");
            } catch (InterruptedException e) {
                throw new BizException(ResponseCode.FAILED.getCode(), "工作流提交成功，但获取工作流实例id失败, 请到工作流实例页面查看", e);
            }
        }
        return null;
    }


    private boolean createCommand(ProcessDefinitionComplementRunReq req) {
        TDsProcessDefinition processDefinition = tDsProcessDefinitionService.getById(req.getId());
        if (Objects.isNull(processDefinition)) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS);
        }

        Schedule schedule = schedulesService.get(processDefinition.getCode());

        TDsCommand command = new TDsCommand();
        command.setCommandType(req.getCommandType().getCode());
        command.setProcessDefinitionCode(processDefinition.getCode());
        if (CommandType.START_PROCESS == req.getCommandType()) {
            command.setProcessDefinitionVersion(0);
        } else {
            command.setProcessDefinitionVersion(processDefinition.getVersion());
        }
        command.setTaskDependType(req.getTaskDependType().getCode());
        if (StrUtil.isNotBlank(schedule.getFailureStrategy())) {
            command.setFailureStrategy(FailureStrategy.valueOf(schedule.getFailureStrategy()).getCode());
        }
        Map<String, String> cmdParam = new HashMap<>(4);

        command.setWarningType(null);
        command.setCommandParam(JSONUtils.toJsonString(cmdParam));
        command.setExecutorId(null);
        command.setWarningGroupId(null);
        command.setProcessInstancePriority(Priority.MEDIUM.getCode());
        command.setWorkerGroup(schedule.getWorkerGroup());
        command.setEnvironmentCode(null);
        command.setDryRun(null);
        command.setProcessInstanceId(0);

        // determine whether to complement
        if (req.getCommandType() == CommandType.COMPLEMENT_DATA) {
            if (!isValidateScheduleTime(req.scheduleDate())) {
                throw new BizException(ResponseCode.FAILED, "调度日期错误");
            }
            try {
                LOGGER.info("Start to create {} command, processId:{}.", req.getCommandType(), processDefinition.getId());
                createComplementCommandList(req, command);
                return true;
            } catch (CronParseException cronParseException) {
                throw new BizException(ResponseCode.FAILED.getCode(), cronParseException.getMessage());
            }
        } else {
            command.setScheduleTime(DateUtil.parseDateTime(req.getExecutionDate()));
            command.setCommandParam(JSONUtils.toJsonString(cmdParam));
            return tDsCommandService.save(command);
        }
    }


    private boolean isValidateScheduleTime(Map<String, String> schedule) {
        Map<String, String> scheduleResult = schedule;
        if (MapUtil.isEmpty(scheduleResult)) {
            return false;
        }
        if (scheduleResult.containsKey(CMD_PARAM_COMPLEMENT_DATA_SCHEDULE_DATE_LIST)) {
            if (scheduleResult.get(CMD_PARAM_COMPLEMENT_DATA_SCHEDULE_DATE_LIST) == null) {
                return false;
            }
        }
        if (scheduleResult.containsKey(CMD_PARAM_COMPLEMENT_DATA_START_DATE)) {
            String startDate = scheduleResult.get(CMD_PARAM_COMPLEMENT_DATA_START_DATE);
            String endDate = scheduleResult.get(CMD_PARAM_COMPLEMENT_DATA_END_DATE);
            if (startDate == null || endDate == null) {
                return false;
            }
            try {
                ZonedDateTime start = DateUtils.stringToZoneDateTime(startDate);
                ZonedDateTime end = DateUtils.stringToZoneDateTime(endDate);
                if (start.isAfter(end)) {
                    LOGGER.error("Complement data parameter error, start time should be before end time, startDate:{}, endDate:{}.",
                            start, end);
                    return false;
                }
            } catch (Exception ex) {
                LOGGER.warn("Parse schedule time error, startDate: {}, endDate: {}", startDate, endDate);
                return false;
            }
        }
        return true;
    }


    private void createComplementCommandList(ProcessDefinitionComplementRunReq req,
                                             TDsCommand command) throws CronParseException {
        String startDate = null, endDate = null, dateList = null;
        Map<String, String> scheduleParam = req.scheduleDate();
        if (scheduleParam.containsKey(CMD_PARAM_COMPLEMENT_DATA_SCHEDULE_DATE_LIST)) {
            dateList = scheduleParam.get(CMD_PARAM_COMPLEMENT_DATA_SCHEDULE_DATE_LIST);
            dateList = removeDuplicates(dateList);
        }
        if (scheduleParam.containsKey(CMD_PARAM_COMPLEMENT_DATA_START_DATE) &&
                scheduleParam.containsKey(CMD_PARAM_COMPLEMENT_DATA_END_DATE)) {
            startDate = scheduleParam.get(CMD_PARAM_COMPLEMENT_DATA_START_DATE);
            endDate = scheduleParam.get(CMD_PARAM_COMPLEMENT_DATA_END_DATE);
        }

        RunMode runMode = (req.getRunMode() == null) ? RunMode.RUN_MODE_SERIAL : req.getRunMode();
        if (RunMode.RUN_MODE_SERIAL == runMode) {
            serialRun(req, command, dateList, startDate, endDate);
        } else {
            parallelRun(req, command, dateList, startDate, endDate);
        }
    }

    /**
     * 去除重复的日期
     *
     * @param scheduleTimeList
     * @return
     */
    private String removeDuplicates(String scheduleTimeList) {
        if (StringUtils.isNotEmpty(scheduleTimeList)) {
            Set<String> dateSet = Arrays.stream(scheduleTimeList.split(COMMA)).map(String::trim).collect(Collectors.toSet());
            return dateSet.stream().sorted().collect(Collectors.joining(COMMA));
        }
        return null;
    }

    /**
     * 串行执行
     *
     * @param req
     * @param command
     * @param dateList
     * @param startDate
     * @param endDate
     */
    private void serialRun(ProcessDefinitionComplementRunReq req, TDsCommand command, String dateList, String startDate, String endDate) {

        Map<String, String> cmdParam = JSONUtils.toMap(command.getCommandParam());
        LOGGER.info("RunMode of {} command is serial run, processDefinitionCode:{}.",
                req.getCommandType(), command.getProcessDefinitionCode());
        // 指定日期执行
        if (StringUtils.isNotEmpty(dateList)) {
            cmdParam.put(CMD_PARAM_COMPLEMENT_DATA_SCHEDULE_DATE_LIST, dateList);
            command.setCommandParam(JSONUtils.toJsonString(cmdParam));
            boolean saveResult = tDsCommandService.save(command);
            if (!saveResult) {
                throw new BizException(ResponseCode.FAILED.getCode(), "插入Command失败");
            }
        }

        if (startDate != null && endDate != null) {
            cmdParam.put(CMD_PARAM_COMPLEMENT_DATA_START_DATE, startDate);
            cmdParam.put(CMD_PARAM_COMPLEMENT_DATA_END_DATE, endDate);
            command.setCommandParam(JSONUtils.toJsonString(cmdParam));
            boolean saveResult = tDsCommandService.save(command);
            if (!saveResult) {
                throw new BizException(ResponseCode.FAILED.getCode(), "插入Command失败");
            }

            // dependent process definition
            // 获取上下状态的调度配置
            List<TDsSchedules> schedules =
                    schedulesService.queryReleaseSchedulerListByProcessDefinitionCode(command.getProcessDefinitionCode());

            if (schedules.isEmpty() || req.getComplementDependentMode() == ComplementDependentMode.OFF_MODE) {
                LOGGER.info("Complement dependent mode is off mode or Scheduler is empty, " +
                        "so skip create complement dependent command, processDefinitionCode:{}.", command.getProcessDefinitionCode());
            } else {
                LOGGER.info("Complement dependent mode is all dependent and Scheduler is not empty, " +
                        "need create complement dependent command, processDefinitionCode:{}.", command.getProcessDefinitionCode());
                createComplementDependentCommand(schedules, command);
            }
        }
    }

    /**
     * 并行执行
     *
     * @param req
     * @param command
     * @param dateList
     * @param startDate
     * @param endDate
     * @throws CronParseException
     */
    private void parallelRun(ProcessDefinitionComplementRunReq req, TDsCommand command, String dateList,
                             String startDate, String endDate) throws CronParseException {
        Map<String, String> cmdParam = JSONUtils.toMap(command.getCommandParam());
        LOGGER.info("RunMode of {} command is parallel run, processDefinitionCode:{}.",
                req.getCommandType(), command.getProcessDefinitionCode());
        if (startDate != null && endDate != null) {
            List<TDsSchedules> schedules =
                    schedulesService.queryReleaseSchedulerListByProcessDefinitionCode(command.getProcessDefinitionCode());
            List<ZonedDateTime> listDate = CronUtils.getSelfFireDateList(DateUtils.stringToZoneDateTime(startDate),
                    DateUtils.stringToZoneDateTime(endDate), schedules);
            int listDateSize = listDate.size();
            int createCount = listDate.size();
            if (!CollectionUtils.isEmpty(listDate)) {
                if (req.getExpectedParallelismNumber() != null && req.getExpectedParallelismNumber() != 0) {
                    createCount = Math.min(createCount, req.getExpectedParallelismNumber());
                }
                LOGGER.info("Complement command run in parallel mode, current expectedParallelismNumber:{}.", createCount);

                // Distribute the number of tasks equally to each command.
                // The last command with insufficient quantity will be assigned to the remaining tasks.
                int itemsPerCommand = (listDateSize / createCount);
                int remainingItems = (listDateSize % createCount);
                int startDateIndex = 0;
                int endDateIndex = 0;

                for (int i = 1; i <= createCount; i++) {
                    int extra = (i <= remainingItems) ? 1 : 0;
                    int singleCommandItems = (itemsPerCommand + extra);

                    if (i == 1) {
                        endDateIndex += singleCommandItems - 1;
                    } else {
                        startDateIndex = endDateIndex + 1;
                        endDateIndex += singleCommandItems;
                    }

                    cmdParam.put(CMD_PARAM_COMPLEMENT_DATA_START_DATE, DateUtils.dateToString(listDate.get(startDateIndex)));
                    cmdParam.put(CMD_PARAM_COMPLEMENT_DATA_END_DATE, DateUtils.dateToString(listDate.get(endDateIndex)));
                    command.setCommandParam(JSONUtils.toJsonString(cmdParam));
                    LOGGER.info("Creating command, commandInfo:{}.", command);
                    if (!tDsCommandService.save(command)) {
                        throw new BizException(ResponseCode.FAILED.getCode(), "插入Command失败");
                    }

                    if (schedules.isEmpty() || req.getComplementDependentMode() == ComplementDependentMode.OFF_MODE) {
                        LOGGER.info("Complement dependent mode is off mode or Scheduler is empty, " +
                                        "so skip create complement dependent command, processDefinitionCode:{}.",
                                command.getProcessDefinitionCode());
                    } else {
                        LOGGER.info("Complement dependent mode is all dependent and Scheduler is not empty, " +
                                        "need create complement dependent command, processDefinitionCode:{}.",
                                command.getProcessDefinitionCode());
                        createComplementDependentCommand(schedules, command);
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(dateList)) {
            List<String> listDate = Arrays.asList(dateList.split(COMMA));
            int createCount = listDate.size();
            if (!CollectionUtils.isEmpty(listDate)) {
                if (req.getExpectedParallelismNumber() != null && req.getExpectedParallelismNumber() != 0) {
                    createCount = Math.min(createCount, req.getExpectedParallelismNumber());
                }
                LOGGER.info("Complement command run in parallel mode, current expectedParallelismNumber:{}.", createCount);
                for (List<String> stringDate : Lists.partition(listDate, createCount)) {
                    cmdParam.put(CMD_PARAM_COMPLEMENT_DATA_SCHEDULE_DATE_LIST, String.join(COMMA, stringDate));
                    command.setCommandParam(JSONUtils.toJsonString(cmdParam));
                    LOGGER.info("Creating command, commandInfo:{}.", command);
                    if (!tDsCommandService.save(command)) {
                        throw new BizException(ResponseCode.FAILED.getCode(), "插入Command失败");
                    }
                }
            }
        }
    }

    /**
     * 创建补数依赖的命令
     *
     * @param schedules
     * @param command
     */
    private void createComplementDependentCommand(List<TDsSchedules> schedules, TDsCommand command) {
        TDsCommand dependentCommand = ModelBeanUtil.copyBean(command, new TDsCommand());

        List<DependentProcessDefinition> dependentProcessDefinitionList =
                getComplementDependentDefinitionList(dependentCommand.getProcessDefinitionCode(),
                        CronUtils.getMaxCycle(schedules.get(0).getCrontab()), dependentCommand.getWorkerGroup());
        dependentCommand.setTaskDependType(TaskDependType.TASK_POST.getCode());
        for (DependentProcessDefinition dependentProcessDefinition : dependentProcessDefinitionList) {
            // If the id is Integer, the auto-increment id will be obtained by mybatis-plus
            // and causing duplicate when clone it.
            dependentCommand.setId(null);
            dependentCommand.setProcessDefinitionCode(dependentProcessDefinition.getProcessDefinitionCode());
            dependentCommand.setProcessDefinitionVersion(dependentProcessDefinition.getProcessDefinitionVersion());
            dependentCommand.setWorkerGroup(dependentProcessDefinition.getWorkerGroup());
            Map<String, String> cmdParam = JSONUtils.toMap(dependentCommand.getCommandParam());
            cmdParam.put(CMD_PARAM_START_NODES, String.valueOf(dependentProcessDefinition.getTaskDefinitionCode()));
            dependentCommand.setCommandParam(JSONUtils.toJsonString(cmdParam));
            boolean saveResult = tDsCommandService.save(dependentCommand);
            if (!saveResult) {
                throw new BizException(ResponseCode.FAILED.getCode(), "插入Command失败");
            }
        }
    }


    private List<DependentProcessDefinition> getComplementDependentDefinitionList(long processDefinitionCode,
                                                                                  CycleEnum processDefinitionCycle,
                                                                                  String workerGroup) {
        // 获取依赖任务
        List<DependentProcessDefinition> dependentProcessDefinitionList =
                taskDefinitionService.queryDependentProcessDefinitionByProcessDefinitionCode(processDefinitionCode);
        //
        return checkDependentProcessDefinitionValid(dependentProcessDefinitionList, processDefinitionCycle,
                workerGroup, processDefinitionCode);
    }

    /**
     * 检查依赖节点的依赖周期是否与依赖进程定义的调度周期一致，如果调度中没有工作组，则使用补体选择的工作组
     *
     * @param dependentProcessDefinitionList
     * @param processDefinitionCycle
     * @param workerGroup
     * @param upstreamProcessDefinitionCode
     * @return
     */
    private List<DependentProcessDefinition> checkDependentProcessDefinitionValid(
            List<DependentProcessDefinition> dependentProcessDefinitionList,
            CycleEnum processDefinitionCycle,
            String workerGroup,
            long upstreamProcessDefinitionCode) {
        List<DependentProcessDefinition> validDependentProcessDefinitionList = new ArrayList<>();

        List<Long> processDefinitionCodeList = dependentProcessDefinitionList.stream()
                .map(DependentProcessDefinition::getProcessDefinitionCode).collect(Collectors.toList());

        Map<Long, String> processDefinitionWorkerGroupMap =
                schedulesService.queryWorkerGroupByProcessDefinitionCodes(processDefinitionCodeList);

        for (DependentProcessDefinition dependentProcessDefinition : dependentProcessDefinitionList) {
            if (getDependentCycle(dependentProcessDefinition, upstreamProcessDefinitionCode) == processDefinitionCycle) {
                if (processDefinitionWorkerGroupMap.get(dependentProcessDefinition.getProcessDefinitionCode()) == null) {
                    dependentProcessDefinition.setWorkerGroup(workerGroup);
                }

                validDependentProcessDefinitionList.add(dependentProcessDefinition);
            }
        }

        return validDependentProcessDefinitionList;
    }


    /**
     * get dependent cycle
     *
     * @return CycleEnum
     */
    private CycleEnum getDependentCycle(DependentProcessDefinition dependentProcessDefinition, Long upstreamProcessDefinitionCode) {
        String nodeString = JSONUtils.getNodeString(dependentProcessDefinition.getTaskParams(), Constants.DEPENDENCE);
        DependentObject dependentObject = JSONUtils.parseObject(nodeString, DependentObject.class);
        if (Objects.isNull(dependentObject)) {
            throw new BizException(ResponseCode.FAILED.getCode(), "获取依赖任务参数失败");
        }
        List<DependentTaskModel> dependentTaskModelList = dependentObject.getDependTaskList();

        for (DependentTaskModel dependentTaskModel : dependentTaskModelList) {
            List<DependentItem> dependentItemList = dependentTaskModel.getDependItemList();
            for (DependentItem dependentItem : dependentItemList) {
                if (upstreamProcessDefinitionCode.equals(dependentItem.getDefinitionCode())) {
                    return cycle2CycleEnum(dependentItem.getCycle());
                }
            }
        }

        return CycleEnum.DAY;
    }


    private CycleEnum cycle2CycleEnum(String cycle) {
        CycleEnum cycleEnum = null;
        switch (cycle) {
            case "day":
                cycleEnum = CycleEnum.DAY;
                break;
            case "hour":
                cycleEnum = CycleEnum.HOUR;
                break;
            case "week":
                cycleEnum = CycleEnum.WEEK;
                break;
            case "month":
                cycleEnum = CycleEnum.MONTH;
                break;
            default:
                break;
        }
        return cycleEnum;
    }

    private void checkScheduleTimeNum(ProcessDefinitionComplementRunReq req) {
        try {
            if (StrUtil.isNotBlank(req.getExecutionDate())) {
                DateTime runDate = DateUtil.parseDateTime(req.getExecutionDate());
                Assert.isTrue(DateUtil.formatDateTime(runDate).equals(req.getExecutionDate()),
                        String.format("不存在的开始日期时间:%s", req.getExecutionDate()));
            }
        } catch (Exception e) {
            throw new BizException(ResponseCode.FAILED.getCode(), "日期格式必须是yyyy-MM-dd HH:mm:ss");
        }
        if (!CommandType.COMPLEMENT_DATA.equals(req.getCommandType())) {
            return;
        }
        try {
            // 验证日期格式必须是yyyy-MM-dd HH:mm:ss
            if (StrUtil.isNotBlank(req.getComplementStartDate())) {
                DateTime startDate = DateUtil.parseDateTime(req.getComplementStartDate());
                Assert.isTrue(DateUtil.formatDateTime(startDate).equals(req.getComplementStartDate()),
                        String.format("不存在的开始日期时间:%s", req.getComplementStartDate()));
            }
            if (StrUtil.isNotBlank(req.getComplementEndDate())) {
                DateTime endDate = DateUtil.parseDateTime(req.getComplementEndDate());
                Assert.isTrue(DateUtil.formatDateTime(endDate).equals(req.getComplementEndDate()),
                        String.format("不存在的结束日期时间:%s", req.getComplementStartDate()));
            }
        } catch (DateException e) {
            throw new BizException(ResponseCode.FAILED.getCode(), "日期格式必须是yyyy-MM-dd HH:mm:ss");
        }

        try {
            String complementScheduleDateList = req.getComplementScheduleDateList();
            if (StrUtil.isNotBlank(complementScheduleDateList)) {
                List<String> list = StrUtil.splitTrim(complementScheduleDateList, COMMA);
                if (list.size() > SCHEDULE_TIME_MAX_LENGTH) {
                    throw new BizException(ResponseCode.FAILED.getCode(), "补数日期个数不能超过100");
                }
                for (String date : list) {
                    DateTime dateTime = DateUtil.parseDateTime(date);
                    Assert.isTrue(DateUtil.formatDateTime(dateTime).equals(date), String.format("不存在的日期时间:%s", date));
                }
            }
        } catch (DateException e) {
            throw new BizException(ResponseCode.FAILED.getCode(), "补数日期必须是yyyy-MM-dd HH:mm:ss格式");
        }
    }

    @SuppressWarnings("all")
    @Override
    public ProcessInstanceResp execute(ProcessInstanceOperateReq req) {
        Integer processInstanceId = req.getProcessInstanceId();
        ExecuteType executeType = req.getExecuteType();
        // 根据流程实例Id获取流程实例
        TDsProcessInstance processInstance = tDsProcessInstanceService.getById(processInstanceId);
        if (Objects.isNull(processInstance)) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流实例%s不存在", processInstanceId));
        }
        if (ExecuteType.START_FAILURE_TASK_PROCESS.equals(executeType) || ExecuteType.REPEAT_RUNNING.equals(executeType)) {
            // 资源配额
            if (projectResourceService.checkNeedCalResource()) {
                TDsProcessDefinition processDefinition = tDsProcessDefinitionService.getByCode(processInstance.getProcessDefinitionCode());
                OfflineSparkProcessResp currentResource =
                        projectResourceService.getOfflineSparkProcessResource(processDefinition.getId());
                long currentCpu = currentResource.getCpu();
                long currentMem = currentResource.getMemory();

                if (currentCpu != 0 || currentMem != 0) {
                    ProjectResourceReq projectResourceReq = new ProjectResourceReq();
                    projectResourceReq.setStartCheck(true);
                    projectResourceReq.setProcessDefinitionId(processDefinition.getId());
                    ProjectResourceResp projectResource = projectResourceService.getProjectResource(projectResourceReq);
                    projectResource.checkResource(currentCpu, currentMem, req.getForceStart());
                }
            }
        }

        // 根据流程code和流程版本获取流程信息
        TDsProcessDefinition tDsProcessDefinition = tDsProcessDefinitionService.getByCode(processInstance.getProcessDefinitionCode());
        tDsProcessDefinition.setReleaseState(ReleaseState.ONLINE.getCode());
        if (executeType != ExecuteType.STOP && executeType != ExecuteType.PAUSE) {
            checkProcessDefinitionValid(tDsProcessDefinition.getProjectCode(), tDsProcessDefinition,
                    processInstance.getProcessDefinitionCode(), processInstance.getProcessDefinitionVersion());
        }
        // 检查执行类型
        checkExecuteType(processInstance, executeType);

        // get the startParams user specified at the first starting while repeat running is needed
        Map<String, Object> commandMap = JsonUtil.decode2Map(processInstance.getCommandParam());
        String startParams = null;
        if (MapUtils.isNotEmpty(commandMap) && executeType == ExecuteType.REPEAT_RUNNING) {
            Object startParamsJson = commandMap.get("StartParams");
            if (startParamsJson != null) {
                startParams = startParamsJson.toString();
            }
        }

        boolean result;
        switch (executeType) {
            case REPEAT_RUNNING:
                // 重跑
                result = insertCommand(processInstanceId, tDsProcessDefinition.getCode(),
                        tDsProcessDefinition.getVersion(), CommandType.REPEAT_RUNNING, startParams);
                break;
            case RECOVER_SUSPENDED_PROCESS:
                // 恢复挂起的流程
                result = insertCommand(processInstanceId, tDsProcessDefinition.getCode(),
                        tDsProcessDefinition.getVersion(), CommandType.RECOVER_SUSPENDED_PROCESS, startParams);
                break;
            case START_FAILURE_TASK_PROCESS:
                // 启动失败的流程
                result = insertCommand(processInstanceId, tDsProcessDefinition.getCode(),
                        tDsProcessDefinition.getVersion(), CommandType.START_FAILURE_TASK_PROCESS, startParams);
                break;
            case STOP:
                if (processInstance.getState() == WorkflowExecutionStatus.READY_STOP.getCode()) {
                    throw new BizException(ResponseCode.FAILED.getCode(),
                            String.format("工作流实例[%s]的状态已经是[%s]", processInstance.getName(), processInstance.getState()));
                } else {
                    // 向work节点发送停止的命令
                    result = updateProcessInstancePrepare(processInstance, CommandType.STOP, WorkflowExecutionStatus.READY_STOP);
                }
                break;
            case PAUSE:
                if (processInstance.getState() == WorkflowExecutionStatus.READY_PAUSE.getCode()) {
                    throw new BizException(ResponseCode.FAILED.getCode(),
                            String.format("工作流实例[%s]的状态已经是[%s]", processInstance.getName(), processInstance.getState()));
                } else {
                    // 向work节点发送暂停的命令
                    result = updateProcessInstancePrepare(processInstance, CommandType.PAUSE, WorkflowExecutionStatus.READY_PAUSE);
                }
                break;
            default:
                throw new BizException(ResponseCode.FAILED.getCode(), "请求参数无效");
        }

        return processInstanceService.get(processInstanceId);
    }


    @Override
    public Boolean forceTaskSuccess(Integer taskInstanceId) {
        TDsTaskInstance taskInstance = tDsTaskInstanceService.existId(taskInstanceId, new BizException(ResponseCode.ID_NOT_EXISTS));

        TaskExecutionStatus taskState = TaskExecutionStatus.of(taskInstance.getState());
        // 检查任务状态
        if (!taskState.isFailure() && !taskState.isKill()) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("任务实例%s的状态是%s，无法执行强制成功操作", taskInstanceId, taskState));
        }

        // change the state of the taskInstance instance
        taskInstance.setState(TaskExecutionStatus.FORCED_SUCCESS.getCode());
        boolean upadateResult = tDsTaskInstanceService.updateById(taskInstance);
        if (upadateResult) {
            forceProcessInstanceSuccessByTaskInstanceId(taskInstanceId);
        } else {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("任务实例%s强制成功失败", taskInstanceId));
        }
        return Boolean.TRUE;
    }


    /**
     * 工作流置成功
     *
     * @param taskInstanceId
     */
    private void forceProcessInstanceSuccessByTaskInstanceId(Integer taskInstanceId) {
        TDsTaskInstance task = tDsTaskInstanceService.getById(taskInstanceId);
        if (task == null) {
            return;
        }
        TDsProcessInstance processInstance = tDsProcessInstanceService.getById(task.getProcessInstanceId());
        if (Objects.isNull(processInstance)) {
            return;
        }
        WorkflowExecutionStatus workflowState = WorkflowExecutionStatus.of(processInstance.getState());
        if (workflowState.isFailure() || workflowState.isStop()) {
            List<TDsTaskInstance> validTaskList = tDsTaskInstanceService.findValidTaskListByProcessId(processInstance.getId());
            List<Long> instanceTaskCodeList =
                    validTaskList.stream().map(TDsTaskInstance::getTaskCode).collect(Collectors.toList());
            // 获取流程定义中的所有任务

            List<TDsTaskDefinitionLog> tDsTaskDefinitionLogList = tDsTaskDefinitionLogService.selectByProcessDefinitionCodeAndVersion(
                    processInstance.getProcessDefinitionCode(),
                    processInstance.getProcessDefinitionVersion());
            List<Long> definiteTaskCodeList =
                    tDsTaskDefinitionLogList.stream().filter(definitionLog -> definitionLog.getFlag() == Flag.YES.getCode())
                            .map(TDsTaskDefinitionLog::getCode).collect(Collectors.toList());
            // only all tasks have instances
            if (CollectionUtils.isEqualCollection(instanceTaskCodeList,
                    definiteTaskCodeList)) {
                List<Integer> failTaskList = validTaskList.stream()
                        .filter(instance -> {
                            TaskExecutionStatus taskState = TaskExecutionStatus.of(instance.getState());
                            return !(taskState.isForceSuccess() || taskState.isSuccess() || taskState.isKill() || taskState.isFailure());
                        }).map(TDsTaskInstance::getId).collect(Collectors.toList());
                if (failTaskList.isEmpty()) {
                    setStateWithDesc(processInstance, WorkflowExecutionStatus.SUCCESS, "success by task force success");
                    tDsProcessInstanceService.updateById(processInstance);
                }
            }
        }
    }


    private boolean insertCommand(Integer instanceId, long processDefinitionCode,
                                  int processVersion, CommandType commandType, String startParams) {
        // To add startParams only when repeat running is needed
        Map<String, Object> cmdParam = new HashMap<>(8);
        cmdParam.put(CMD_PARAM_RECOVER_PROCESS_ID_STRING, instanceId);
        if (!StringUtils.isEmpty(startParams)) {
            cmdParam.put(CMD_PARAM_START_PARAMS, startParams);
        }

        TDsCommand command = new TDsCommand();
        command.setCommandType(commandType.getCode());
        command.setProcessDefinitionCode(processDefinitionCode);
        command.setCommandParam(JsonUtil.encode(cmdParam));
        command.setExecutorId(null);
        command.setProcessDefinitionVersion(processVersion);
        command.setProcessInstanceId(instanceId);
        // 检测是否需要创建Command
        if (!verifyIsNeedCreateCommand(command)) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流实例[%s]正在执行命令，请稍等...", processDefinitionCode));
        }
        // 插入Command到数据库

        return tDsCommandService.save(command);
    }


    private void checkExecuteType(TDsProcessInstance processInstance, ExecuteType executeType) {
        WorkflowExecutionStatus executionStatus = WorkflowExecutionStatus.of(processInstance.getState());
        boolean checkResult = false;
        switch (executeType) {
            case PAUSE:
                if (executionStatus.isRunning()) {
                    checkResult = true;
                }
                break;
            case STOP:
                if (executionStatus.canStop()) {
                    checkResult = true;
                }
                break;
            case REPEAT_RUNNING:
                if (executionStatus.isFinished()) {
                    checkResult = true;
                }
                break;
            case START_FAILURE_TASK_PROCESS:
                if (executionStatus.isFailure()) {
                    checkResult = true;
                }
                break;
            case RECOVER_SUSPENDED_PROCESS:
                if (executionStatus.isPause() || executionStatus.isStop()) {
                    checkResult = true;
                }
                break;
            default:
                break;
        }
        if (!checkResult) {
            throw new BizException(ResponseCode.FAILED.getCode(),
                    String.format("工作流实例[%s]的状态是[%s]，无法执行[%s]操作", processInstance.getName(), executionStatus, executeType));
        }
    }


    private boolean verifyIsNeedCreateCommand(TDsCommand command) {
        boolean isNeedCreate = true;
        EnumMap<CommandType, Integer> cmdTypeMap = new EnumMap<>(CommandType.class);
        cmdTypeMap.put(CommandType.REPEAT_RUNNING, 1);
        cmdTypeMap.put(CommandType.RECOVER_SUSPENDED_PROCESS, 1);
        cmdTypeMap.put(CommandType.START_FAILURE_TASK_PROCESS, 1);
        CommandType commandType = CommandType.of(command.getCommandType());

        if (cmdTypeMap.containsKey(commandType)) {
            Map<String, Object> map = JsonUtil.decode2Map(command.getCommandParam());
            Integer processInstanceId = (Integer) map.get(CMD_PARAM_RECOVER_PROCESS_ID_STRING);

            List<TDsCommand> commands = tDsCommandService.list();
            // for all commands
            for (TDsCommand tmpCommand : commands) {
                if (cmdTypeMap.containsKey(CommandType.of(tmpCommand.getCommandType()))) {
                    Map<String, Object> tempMap = JsonUtil.decode2Map(tmpCommand.getCommandParam());
                    if (MapUtils.isNotEmpty(tempMap) && processInstanceId.equals(tempMap.get(CMD_PARAM_RECOVER_PROCESS_ID_STRING))) {
                        isNeedCreate = false;
                        break;
                    }
                }
            }
        }
        return isNeedCreate;
    }


    private boolean updateProcessInstancePrepare(TDsProcessInstance processInstance,
                                                 CommandType commandType, WorkflowExecutionStatus executionStatus) {
        processInstance.setCommandType(commandType.getCode());
        addHistoryCmd(processInstance, commandType);
        setStateWithDesc(processInstance, executionStatus, commandType.getDescp() + "by ui");
        boolean updateResult = tDsProcessInstanceService.updateById(processInstance);

        // determine whether the process is normal
        if (updateResult) {
            // 向work节点发送命令
            // directly send the process instance state change event to target master, not guarantee the event send
            // success
            WorkflowStateEventChangeCommand workflowStateEventChangeCommand = new WorkflowStateEventChangeCommand(
                    processInstance.getId(), 0, WorkflowExecutionStatus.of(processInstance.getState()),
                    processInstance.getId(), 0);
            Host host = new Host(processInstance.getHost());
            stateEventCallbackService.sendResult(host, workflowStateEventChangeCommand.convert2Command());
            return true;
        } else {
            throw new BizException(ResponseCode.FAILED.getCode(), "操作工作流实例错误");
        }
    }


    private void checkProcessDefinitionValid(long projectCode, TDsProcessDefinition processDefinition,
                                            long processDefineCode, Integer version) {
        if (processDefinition == null || projectCode != processDefinition.getProjectCode()) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流定义[%s]不存在", processDefineCode));
        } else if (processDefinition.getReleaseState() != ReleaseState.ONLINE.getCode()) {
            throw new BizException(ResponseCode.FAILED.getCode(), String.format("工作流定义[%s]工作流版本[%s]不是上线状态", processDefineCode, version));
        } else if (!checkSubProcessDefinitionValid(processDefinition)) {
            throw new BizException(ResponseCode.FAILED.getCode(), "存在子工作流定义不是上线状态");
        }
    }


    private boolean checkSubProcessDefinitionValid(TDsProcessDefinition processDefinition) {
        // query all subprocesses under the current process
        List<TDsProcessTaskRelation> processTaskRelations =
                tDsProcessTaskRelationService.queryDownstreamByProcessDefinitionCode(processDefinition.getCode());
        if (processTaskRelations.isEmpty()) {
            return true;
        }
        List<Long> relationCodes = processTaskRelations.stream().map(TDsProcessTaskRelation::getPostTaskCode)
                .distinct().collect(Collectors.toList());
        List<TDsTaskDefinition> taskDefinitions = tDsTaskDefinitionService.selectByCodeList(relationCodes);

        // find out the process definition code
        Set<Long> processDefinitionCodeSet = new HashSet<>();
        taskDefinitions.stream().filter(task -> "SUB_PROCESS".equalsIgnoreCase(task.getTaskType()))
                .forEach(taskDefinition -> processDefinitionCodeSet.add(
                        Long.valueOf(JSONUtils.getNodeString(taskDefinition.getTaskParams(), CMD_PARAM_SUB_PROCESS_DEFINE_CODE))));
        if (processDefinitionCodeSet.isEmpty()) {
            return true;
        }

        // check sub releaseState
        List<TDsProcessDefinition> processDefinitions = tDsProcessDefinitionService.listByCode(new ArrayList<>(processDefinitionCodeSet));
        return processDefinitions.stream().filter(definition -> definition.getReleaseState().equals(ReleaseState.OFFLINE.getCode()))
                .collect(Collectors.toSet()).isEmpty();
    }


    private void addHistoryCmd(TDsProcessInstance processInstance, CommandType cmd) {
        if (StrUtil.isBlank(processInstance.getHistoryCmd())) {
            processInstance.setHistoryCmd(cmd.toString());
        } else {
            processInstance.setHistoryCmd(String.format("%s,%s", processInstance.getHistoryCmd(), cmd.toString()));
        }
    }


    private void setStateWithDesc(TDsProcessInstance processInstance, WorkflowExecutionStatus state, String stateDesc) {
        processInstance.setState(state.getCode());
        List<StateDesc> stateDescList = new ArrayList<>();
        if (StrUtil.isNotBlank(processInstance.getStateHistory())) {
            stateDescList = JSONUtils.toList(processInstance.getStateHistory(), StateDesc.class);
        }
        stateDescList.add(new StateDesc(new Date(), state, stateDesc));
        processInstance.setStateHistory(JsonUtil.encode(stateDescList));
    }


    public static class StateDesc {

        private Date time;
        private WorkflowExecutionStatus state;
        private String desc;

        public StateDesc() {
        }

        public StateDesc(Date time, WorkflowExecutionStatus state, String desc) {
            this.time = time;
            this.state = state;
            this.desc = desc;
        }

        public Date getTime() {
            return time;
        }

        public void setTime(Date time) {
            this.time = time;
        }

        public WorkflowExecutionStatus getState() {
            return state;
        }

        public void setState(WorkflowExecutionStatus state) {
            this.state = state;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

}
