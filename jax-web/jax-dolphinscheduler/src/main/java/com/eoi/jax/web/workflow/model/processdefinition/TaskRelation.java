package com.eoi.jax.web.workflow.model.processdefinition;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.repository.entity.TDsProcessTaskRelation;
import com.eoi.jax.web.repository.entity.TDsProcessTaskRelationLog;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.dolphinscheduler.common.enums.ConditionType;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/12
 */
public class TaskRelation {

    private String name;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "被依赖的任务编码")
    private Long preTaskCode;

    @Schema(description = "被依赖的任务版本")
    private Integer preTaskVersion;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "任务编码")
    private Long postTaskCode;

    @Schema(description = "任务版本")
    private Integer postTaskVersion;

    private String conditionType;

    private Map<String, Object> conditionParams;

    public TDsProcessTaskRelation toEntity() {
        TDsProcessTaskRelation tDsProcessTaskRelation = ModelBeanUtil.copyBean(this, new TDsProcessTaskRelation());
        tDsProcessTaskRelation.setConditionType(ConditionType.valueOf(this.getConditionType()).getCode());
        tDsProcessTaskRelation.setConditionParams(JsonUtil.encode(this.getConditionParams()));
        return tDsProcessTaskRelation;
    }


    public TaskRelation fromEntity(TDsProcessTaskRelation entity) {
        TaskRelation taskRelation = ModelBeanUtil.copyBean(entity, this);
        taskRelation.setConditionType(Arrays.stream(ConditionType.values()).filter(it ->
                entity.getConditionType().equals(it.getCode())).findFirst().map(Enum::name).orElse(null));
        taskRelation.setConditionParams(JsonUtil.decode2Map(entity.getConditionParams()));
        return taskRelation;
    }

    public TaskRelation fromEntity(TDsProcessTaskRelationLog entity) {
        TaskRelation taskRelation = ModelBeanUtil.copyBean(entity, this);
        taskRelation.setConditionType(Arrays.stream(ConditionType.values()).filter(it ->
                entity.getConditionType().equals(it.getCode())).findFirst().map(Enum::name).orElse(null));
        taskRelation.setConditionParams(JsonUtil.decode2Map(entity.getConditionParams()));
        return taskRelation;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getPreTaskCode() {
        return preTaskCode;
    }

    public void setPreTaskCode(Long preTaskCode) {
        this.preTaskCode = preTaskCode;
    }

    public Integer getPreTaskVersion() {
        return preTaskVersion;
    }

    public void setPreTaskVersion(Integer preTaskVersion) {
        this.preTaskVersion = preTaskVersion;
    }

    public Long getPostTaskCode() {
        return postTaskCode;
    }

    public void setPostTaskCode(Long postTaskCode) {
        this.postTaskCode = postTaskCode;
    }

    public Integer getPostTaskVersion() {
        return postTaskVersion;
    }

    public void setPostTaskVersion(Integer postTaskVersion) {
        this.postTaskVersion = postTaskVersion;
    }

    public String getConditionType() {
        return conditionType;
    }

    public void setConditionType(String conditionType) {
        this.conditionType = conditionType;
    }

    public Map<String, Object> getConditionParams() {
        return conditionParams;
    }

    public void setConditionParams(Map<String, Object> conditionParams) {
        this.conditionParams = conditionParams;
    }
}
