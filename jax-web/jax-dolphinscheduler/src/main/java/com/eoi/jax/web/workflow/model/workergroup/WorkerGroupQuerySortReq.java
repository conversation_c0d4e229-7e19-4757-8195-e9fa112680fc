package com.eoi.jax.web.workflow.model.workergroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TDsWorkerGroup;

/**
 * <AUTHOR>
 * @date 2023/10/12
 */
public class WorkerGroupQuerySortReq implements ISortReq<TDsWorkerGroup> {


    private String updateTime;

    /**
     * 生成order排序
     *
     * @param wrapper
     * @return
     */
    @Override
    public QueryWrapper<TDsWorkerGroup> order(QueryWrapper<TDsWorkerGroup> wrapper) {
        wrapper.lambda().orderBy(isOrder(updateTime), isAsc(updateTime), TDsWorkerGroup::getUpdateTime);
        return wrapper;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
