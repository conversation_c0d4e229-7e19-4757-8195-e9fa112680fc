package com.eoi.jax.web.security.service;

import com.eoi.jax.web.core.service.IBaseService;
import com.eoi.jax.web.repository.entity.TbUserGroup;
import com.eoi.jax.web.repository.service.TbUserGroupService;
import com.eoi.jax.web.security.model.usergroup.*;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/4/24 10:13
 */
public interface UserGroupService extends IBaseService<
        TbUserGroupService,
        TbUserGroup,
        UserGroupResp,
        UserGroupCreateReq,
        UserGroupUpdateReq,
        UserGroupQueryReq> {

    /**
     * 关联用户
     *
     * @param req
     * @return
     */
    List<Long> addUsers(GroupUsersReq req);

    /**
     * 移除用户
     *
     * @param req
     * @return
     */
    List<Long> removeUsers(GroupUsersReq req);
}
