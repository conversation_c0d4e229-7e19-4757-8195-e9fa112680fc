package com.eoi.jax.web.security.model.permission;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbPermission;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class PermissionQueryReq extends BaseQueryReq<TbPermission> {

    private PermissionQueryFilterReq filter = new PermissionQueryFilterReq();
    private PermissionQuerySortReq sort = new PermissionQuerySortReq();

    @Override
    public IFilterReq<TbPermission> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbPermission> getSort() {
        return sort;
    }
}
