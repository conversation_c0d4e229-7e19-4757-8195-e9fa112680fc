package com.eoi.jax.web.security.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.security.model.role.RoleCreateReq;
import com.eoi.jax.web.security.model.role.RoleQueryReq;
import com.eoi.jax.web.security.model.role.RoleUpdateReq;
import com.eoi.jax.web.security.model.role.RoleUsersReq;
import com.eoi.jax.web.security.service.RoleService;
import com.eoi.jax.web.security.service.impl.RolePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 * @Desc:
 **/
@RestController
public class RoleController implements V2Controller {

    @Autowired
    private RoleService roleService;


    @Operation(summary = "全部查询")
    @GetMapping("security/role/list")
    public Response list() {
        return Response.success(roleService.all());
    }

    @Operation(summary = "全部查询")
    @PostMapping("security/role/list")
    public Response list(@RequestBody RoleQueryReq req) {
        return Response.success(roleService.all(req.query()));
    }

    @Operation(summary = "分页查询")
    @PostMapping("security/role/query")
    public Response query(@RequestBody RoleQueryReq req) {
        return Response.success(roleService.query(req));
    }

    @Operation(summary = "根据id查询详情")
    @GetMapping("security/role/{id}")
    public Response get(@Parameter(description = "id", required = true) @PathVariable("id") Long id) {
        return Response.success(roleService.get(id));
    }

    @Operation(summary = "创建")
    @PostMapping("security/role")
    public Response create(@Valid @RequestBody RoleCreateReq req) {
        return Response.success(roleService.create(req));
    }

    @Operation(summary = "更新")
    @PutMapping("security/role/{id}")
    public Response update(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                           @Valid @RequestBody RoleUpdateReq req) {
        req.setId(id);
        return Response.success(roleService.update(req));
    }

    @Operation(summary = "删除")
    @DeleteMapping("security/role/{id}")
    public Response delete(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(roleService.delete(id));
    }

    @Operation(summary = "查询关联数据")
    @GetMapping("security/role/{id}/usage")
    public Response usage(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(roleService.usage(id));
    }

    @Operation(summary = "查询用户拥有的所有权限")
    @GetMapping("security/role/user-role/{userId}")
    public Response getByUserId(@Parameter(description = "用户id", required = true) @PathVariable("userId") Long userId) {
        return Response.success(roleService.getRoleByUserId(userId));
    }
    @Operation(summary = "关联用户")
    @PutMapping("security/role/{id}/users")
    public Response addUsers(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                           @Valid @RequestBody RoleUsersReq req) {
        req.setId(id);
        return Response.success(roleService.addUsers(req));
    }

    @Operation(summary = "移除用户")
    @DeleteMapping("security/role/{id}/users")
    public Response removeUsers(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                              @Valid @RequestBody RoleUsersReq req) {
        req.setId(id);
        return Response.success(roleService.removeUsers(req));
    }

    @Operation(summary = "获取角色权限")
    @GetMapping("security/role/{id}/permissions")
    public Response getRolePermissions(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(roleService.getPermissionsByRoleId(id));
    }

    @Operation(summary = "修改角色权限")
    @PutMapping("security/role/{id}/permissions")
    public Response saveRolePermissions(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                                        @Valid @RequestBody List<RolePermission> req) {
        return Response.success(roleService.savePermissionsByRoleId(id, req));
    }
}
