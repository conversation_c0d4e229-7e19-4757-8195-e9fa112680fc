package com.eoi.jax.web.security.model.usergroup;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbUserGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class UserGroupQueryFilterReq implements IFilterReq<TbUserGroup> {

    @Schema(description = "用户组名称")
    private String name;

    @Override
    public QueryWrapper<TbUserGroup> where(QueryWrapper<TbUserGroup> wrapper) {
        wrapper.lambda().like(StringUtils.isNotBlank(name), TbUserGroup::getName, name);
        return wrapper;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
