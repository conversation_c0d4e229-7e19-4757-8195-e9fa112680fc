package com.eoi.jax.web.security.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.security.service.UserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * <AUTHOR> zsc
 * @create 2023/4/23 17:17
 */
@RestController
public class AuthController implements V2Controller {
    @Resource
    private UserService userService;

    @GetMapping("auth/current")
    public Response current() {
        return Response.success(userService.current());
    }
}
