package com.eoi.jax.web.security.unified.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.security.AuthCoreService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.service.*;
import com.eoi.jax.web.security.provider.UnifiedUser;
import com.eoi.jax.web.security.provider.UnifiedUserGroup;
import com.eoi.jax.web.security.provider.UnifiedUserPermission;
import com.eoi.jax.web.security.unified.service.UnifiedPortalAuthService;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/10/20
 */
@Service
public class UnifiedPortalAuthServiceImpl implements UnifiedPortalAuthService {
    private static final String USER_CACHE_NAME = "UnifiedPortalAuthServiceImpl:getSessionUser";
    private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(UnifiedPortalAuthServiceImpl.class);

    @Autowired
    private TbPermissionService tbPermissionService;
    @Autowired
    private TbUserService tbUserService;
    @Autowired
    private TbRoleService tbRoleService;
    @Autowired
    private TbUserGroupService tbUserGroupService;
    @Autowired
    private TbProjectUserRoleService tbProjectUserRoleService;
    @Autowired
    private AuthCoreService authCoreService;

    @Override
    public TbUser getByGuid(String guid) {
        return authCoreService.getUserByGuid(guid);
    }

    @Cacheable(cacheNames = USER_CACHE_NAME, key = "#userId+'-'+#projectId", unless = "#result == null")
    @Override
    public UnifiedUser getSessionUser(Long userId, Long projectId) {
        TbUser tbUser = tbUserService.getById(userId);
        if (tbUser == null) {
            return null;
        }
        UnifiedUser unifiedUser = new UnifiedUser();
        BeanUtil.copyProperties(tbUser, unifiedUser);
        List<TbRole> tbRoles = tbRoleService.getByUserId(userId);
        Set<Long> roleIds = tbRoles.stream().map(TbRole::getId).collect(Collectors.toSet());
        if (projectId != null) {
            List<TbProjectUserRole> tbProjectUserRoles = tbProjectUserRoleService.list(
                    new LambdaQueryWrapper<TbProjectUserRole>()
                            .eq(TbProjectUserRole::getUserId, userId)
                            .eq(TbProjectUserRole::getProjectId, projectId)
            );
            roleIds.addAll(tbProjectUserRoles.stream()
                    .filter(item -> item.getEnabled() == 1)
                    .map(TbProjectUserRole::getRoleId)
                    .collect(Collectors.toSet())
            );
            roleIds.removeAll(
                    tbProjectUserRoles.stream()
                            .filter(item -> item.getEnabled() == 0)
                            .map(TbProjectUserRole::getRoleId)
                            .collect(Collectors.toSet())
            );
        }
        List<TbPermission> tbPermissions = new ArrayList<>();
        if (!roleIds.isEmpty()) {
            tbPermissions = tbPermissionService.getByRoleIds(new ArrayList<>(roleIds));
        }
        unifiedUser.setPermissions(tbPermissions.stream().map(TbPermission::getCode).distinct().map(code -> {
            UnifiedUserPermission permission = new UnifiedUserPermission();
            permission.setCode(code);
            return permission;
        }).collect(Collectors.toList()));
        List<TbUserGroup> tbUserGroups = tbUserGroupService.getByUserId(userId);
        unifiedUser.setGroups(tbUserGroups.stream().distinct().map(item -> {
            UnifiedUserGroup group = new UnifiedUserGroup();
            group.setCode(item.getCode());
            return group;
        }).collect(Collectors.toList()));
        return unifiedUser;
    }

    @Scheduled(fixedRate = 2000)
    @CacheEvict(cacheNames = {USER_CACHE_NAME}, allEntries = true)
    public void clearSessionUserCache() {
        // clear user token cache
    }
}
