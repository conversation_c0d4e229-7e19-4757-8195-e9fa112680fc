package com.eoi.jax.web.security.model.permission;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.repository.entity.TbPermission;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class PermissionResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbPermission> {
    @Schema(description = "主键id")
    @OpPrimaryKey
    private Long id;

    @Schema(description = "权限名字")
    @OpPrimaryName
    private String name;

    @Schema(description = "http请求方式（GET/POST等等）")
    private String httpMethod;

    @Schema(description = "icon")
    private String icon;

    @Schema(description = "权限地址")
    private String uri;

    @Schema(description = "权限（全局唯一）")
    private String permission;

    @Schema(description = "权限代码（和权限1对多关系）")
    private String code;

    @Schema(description = "权限代码显示名称")
    private String codeName;

    @Schema(description = "父级编号")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long parentId;

    @Schema(description = "所有父级编号")
    private String parentPath;

    @Schema(description = "权限类型")
    private String type;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "是否显示")
    private Boolean isDisplay;

    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    @Schema(description = "是否外部链接")
    private Boolean externalLink;

    @Schema(description = "打开方式")
    private String openType;


    public Boolean getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(Boolean externalLink) {
        this.externalLink = externalLink;
    }


    public String getOpenType() {
        return openType;
    }

    public void setOpenType(String openType) {
        this.openType = openType;
    }


    @Override
    public PermissionResp fromEntity(TbPermission table) {
        IRespModel.super.fromEntity(table);
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHttpMethod() {
        return httpMethod;
    }


    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public String getParentPath() {
        return parentPath;
    }

    public void setParentPath(String parentPath) {
        this.parentPath = parentPath;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Boolean getIsDisplay() {
        return isDisplay;
    }

    public void setIsDisplay(Boolean display) {
        isDisplay = display;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
