package com.eoi.jax.web.security.service;

import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.service.IBaseService;
import com.eoi.jax.web.repository.entity.TbProject;
import com.eoi.jax.web.repository.service.TbProjectService;
import com.eoi.jax.web.security.model.project.*;
import com.eoi.jax.web.security.model.user.UserQueryReq;
import com.eoi.jax.web.security.model.user.UserResp;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/4/24 10:13
 */
public interface ProjectService extends IBaseService<
        TbProjectService,
        TbProject,
        ProjectResp,
        ProjectCreateReq,
        ProjectUpdateReq,
        ProjectQueryReq> {

    /**
     * 获取当前登录用户的项目列表
     *
     * @return
     */
    CurrentUserProjects getLoginUserProjectList();

    /**
     * 获取项目用户角色列表
     *
     * @param req
     * @return
     */
    Paged<UserResp> queryProjectUserRoles(UserQueryReq req);

    /**
     * 添加项目用户角色
     *
     * @param req
     */
    void addProjectUserRole(ProjectUserRole req);

    /**
     * 删除项目用户角色
     *
     * @param req
     */
    void removeProjectUserRole(ProjectUserRole req);

    /**
     * 项目资源授权
     *
     * @param req
     */
    void saveProjectResource(List<ProjectResource> req);

    /**
     * 获取项目资源共享列表
     *
     * @param id
     * @param resourceType
     * @param resourceId
     * @return
     */
    List<ResourceProject> getProjectResourceShare(Long id, String resourceType, Long resourceId);

    /**
     * 保存项目资源共享列表
     *
     * @param id
     * @param resourceType
     * @param resourceId
     * @param projects
     * @return
     */
    void saveProjectResourceShare(Long id, String resourceType, Long resourceId, List<ResourceProject> projects);

    /**
     * 获取项目包含已使用资源
     *
     * @param id
     * @return
     */
    ProjectResp getIncludeResource(Long id);
}
