package com.eoi.jax.web.security.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.security.model.usergroup.GroupUsersReq;
import com.eoi.jax.web.security.model.usergroup.UserGroupCreateReq;
import com.eoi.jax.web.security.model.usergroup.UserGroupQueryReq;
import com.eoi.jax.web.security.model.usergroup.UserGroupUpdateReq;
import com.eoi.jax.web.security.service.UserGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 * @Desc:
 **/
@RestController
public class UserGroupController implements V2Controller {

    @Autowired
    private UserGroupService userGroupService;

    @Operation(summary = "全部查询")
    @GetMapping("security/user-group/list")
    public Response list() {
        return Response.success(userGroupService.all());
    }

    @Operation(summary = "全部查询")
    @PostMapping("security/user-group/list")
    public Response list(@RequestBody UserGroupQueryReq req) {
        return Response.success(userGroupService.all(req.query()));
    }

    @Operation(summary = "分页查询")
    @PostMapping("security/user-group/query")
    public Response query(@RequestBody UserGroupQueryReq req) {
        return Response.success(userGroupService.query(req));
    }

    @Operation(summary = "根据id查询详情")
    @GetMapping("security/user-group/{id}")
    public Response get(@Parameter(description = "id", required = true) @PathVariable("id") Long id) {
        return Response.success(userGroupService.get(id));
    }

    @Operation(summary = "创建")
    @PostMapping("security/user-group")
    public Response create(@Valid @RequestBody UserGroupCreateReq req) {
        return Response.success(userGroupService.create(req));
    }

    @Operation(summary = "更新")
    @PutMapping("security/user-group/{id}")
    public Response update(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                           @Valid @RequestBody UserGroupUpdateReq req) {
        req.setId(id);
        return Response.success(userGroupService.update(req));
    }

    @Operation(summary = "删除")
    @DeleteMapping("security/user-group/{id}")
    public Response delete(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(userGroupService.delete(id));
    }

    @Operation(summary = "查询关联数据")
    @GetMapping("security/user-group/{id}/usage")
    public Response usage(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(userGroupService.usage(id));
    }


    @Operation(summary = "关联用户")
    @PutMapping("security/user-group/{id}/users")
    public Response addUsers(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                             @Valid @RequestBody GroupUsersReq req) {
        req.setId(id);
        return Response.success(userGroupService.addUsers(req));
    }

    @Operation(summary = "移除用户")
    @DeleteMapping("security/user-group/{id}/users")
    public Response removeUsers(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                                @Valid @RequestBody GroupUsersReq req) {
        req.setId(id);
        return Response.success(userGroupService.removeUsers(req));
    }
}
