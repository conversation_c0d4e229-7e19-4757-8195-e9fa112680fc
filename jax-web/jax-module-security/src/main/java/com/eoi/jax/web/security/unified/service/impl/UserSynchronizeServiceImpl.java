package com.eoi.jax.web.security.unified.service.impl;

import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.repository.entity.TbUser;
import com.eoi.jax.web.repository.service.TbUserService;
import com.eoi.jax.web.security.unified.model.UnifiedUserInfo;
import com.eoi.jax.web.security.unified.model.UnifiedUserResp;
import com.eoi.jax.web.security.unified.service.UserSynchronizeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Author: tangy
 * @Date: 2023/6/6
 * @Desc:
 **/
@Service
public class UserSynchronizeServiceImpl implements UserSynchronizeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserSynchronizeService.class);
    @Autowired
    private TbUserService tbUserService;

    @Override
    public UnifiedUserResp addUser(UnifiedUserInfo account) {
        TbUser byGuid = tbUserService.getByGuid(String.valueOf(account.getUserId()));
        if (byGuid != null) {
            throw new BizException(ResponseCode.CODE_EXIST.getCode(), "用户已经存在");
        }
        TbUser tbUser = new TbUser();
        tbUser.setId(IdUtil.genId());
        tbUser.setGuid(String.valueOf(account.getUserId()));
        tbUser.setAccount(account.getUserName());
        tbUser.setName(StringUtils.isBlank(account.getDisplayName()) ? account.getUserName() : account.getDisplayName());
        tbUser.setIsDeleted(0);
        tbUser.setPlatform("unified");
        tbUser.setPhone(account.getPhoneNumber());
        tbUser.setEmail(account.getEmail());
        tbUser.setCreateTime(new Date());
        tbUser.setUpdateTime(new Date());

        try {
            tbUserService.save(tbUser);
            UnifiedUserResp userResp = new UnifiedUserResp().fromEntity(tbUser);
            return userResp;
        } catch (Exception e) {
            LOGGER.error("统一门户用户新增失败: {}", e.getMessage(), e);
            throw new BizException(ResponseCode.FAILED.getCode(), "用户新增失败");
        }
    }

    @Override
    public UnifiedUserResp updateAccount(UnifiedUserInfo account) {
        if (account.getUserId() == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "用户不存在");
        }
        TbUser tbUser = tbUserService.getByGuid(String.valueOf(account.getUserId()));
        if (tbUser == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "用户不存在");
        }
        tbUser.setAccount(account.getUserName());
        tbUser.setName(StringUtils.isBlank(account.getDisplayName()) ? account.getUserName() : account.getDisplayName());
        tbUser.setIsDeleted(0);
        tbUser.setPhone(account.getPhoneNumber());
        tbUser.setEmail(account.getEmail());
        tbUser.setUpdateTime(new Date());
        try {
            tbUserService.updateById(tbUser);
            UnifiedUserResp userResp = new UnifiedUserResp().fromEntity(tbUser);
            return userResp;
        } catch (Exception e) {
            LOGGER.error("统一门户用户更新失败: {}", e.getMessage(), e);
            throw new BizException(ResponseCode.FAILED.getCode(), "用户更新失败");
        }
    }

    @Override
    public UnifiedUserResp deleteAccount(UnifiedUserInfo account) {
        if (account.getUserId() == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "用户不存在");
        }
        TbUser tbUser = tbUserService.getByGuid(String.valueOf(account.getUserId()));
        if (tbUser == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "用户不存在");
        }

        try {
            tbUserService.removeById(tbUser.getId());
            UnifiedUserResp userResp = new UnifiedUserResp().fromEntity(tbUser);
            return userResp;
        } catch (Exception e) {
            LOGGER.error("统一门户用户删除失败: {}", e.getMessage(), e);
            throw new BizException(ResponseCode.FAILED.getCode(), "用户删除失败");
        }
    }
}
