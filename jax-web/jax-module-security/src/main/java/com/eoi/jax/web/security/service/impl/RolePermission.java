package com.eoi.jax.web.security.service.impl;

import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.core.model.IModel;
import com.eoi.jax.web.repository.entity.TbRolePermission;

/**
 * <AUTHOR>
 * @Date 2023/10/16
 */
public class RolePermission implements IModel<TbRolePermission> {
    private Long roleId;
    private String permissionCode;

    public RolePermission fromEntity(TbRolePermission tbRolePermission) {
        return this.copyFrom(tbRolePermission);
    }

    public TbRolePermission toEntity(TbRolePermission entity) {
        entity.setId(IdUtil.genId());
        return this.copyTo(entity);
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }
}
