package com.eoi.jax.web.security.model.role;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbRole;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class RoleUpdateReq implements IUpdateModel<TbRole> {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "角色代码")
    @NotBlank(message = "角色代码不能为空")
    private String code;

    @Schema(description = "角色名字")
    @NotBlank(message = "角色名字不能为空")
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Override
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
