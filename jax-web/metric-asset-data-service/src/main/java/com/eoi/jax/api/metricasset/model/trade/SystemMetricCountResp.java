package com.eoi.jax.api.metricasset.model.trade;

/**
 * <AUTHOR> zsc
 * @create 2024/7/22 13:51
 */
public class SystemMetricCountResp {
    private String systemName;
    private Long count;
    private Long successCount;
    private Double successRate;
    private Double avgDurationTime;
    private Long durationTime;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public Long getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Double getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(Double successRate) {
        this.successRate = successRate;
    }

    public Double getAvgDurationTime() {
        return avgDurationTime;
    }

    public void setAvgDurationTime(Double avgDurationTime) {
        this.avgDurationTime = avgDurationTime;
    }

    public Long getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(Long durationTime) {
        this.durationTime = durationTime;
    }
}
