package com.eoi.jax.api.metricasset.model.objectmetric;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class MetricDimensionResp {
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "维度名称")
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "字段名称")
    private String code;

    @Schema(description = "是否是关键维度")
    private Boolean isKeyDimension;

    @Schema(description = "允许的操作动作")
    private String action;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getIsKeyDimension() {
        return isKeyDimension;
    }

    public void setIsKeyDimension(Boolean keyDimension) {
        isKeyDimension = keyDimension;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

}
