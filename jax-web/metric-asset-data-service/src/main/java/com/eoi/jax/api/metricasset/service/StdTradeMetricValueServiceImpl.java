package com.eoi.jax.api.metricasset.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.annotation.DataServiceHandler;
import com.eoi.jax.api.dataservice.annotation.DsDatasourceParam;
import com.eoi.jax.api.dataservice.annotation.DsReqParam;
import com.eoi.jax.api.dataservice.annotation.DsRespParam;
import com.eoi.jax.api.dataservice.enumrate.DsColumnTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsRespColumnTypeEnum;
import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.CkManConnectionSetting;
import com.eoi.jax.api.dataservice.model.request.DataServiceRequest;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.api.metricasset.util.ClickhouseUtil;
import com.eoi.jax.api.metricasset.util.ReqParamUtil;
import com.eoi.jax.api.metricasset.util.TradeMetricUtil;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.repository.entity.TbMetricItem;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.service.TbTableDeployService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/18
 */
@DataServiceHandler(
        name = "stdTransMetricValueApi",
        description = "交易指标区间范围查询",
        datasources = {@DsDatasourceParam(name = "ck", display = "tradeCk", type = DsDatasourceTypeEnum.CLICKHOUSE)},
        reqParams = {
            @DsReqParam(
                name = "seriesIds",
                desc = "应用系统监控id",
                type = "LIST_LONG",
                sampleValue = "[1]",
                required = true),
            @DsReqParam(
                name = "timeFrom",
                desc = "时间范围",
                type = "STRING",
                sampleValue = "2023-09-19 09:00:00",
                required = true),
            @DsReqParam(
                name = "timeTo",
                desc = "时间范围",
                type = "STRING",
                sampleValue = "2023-09-19 09:00:00",
                required = true),
            @DsReqParam(
                name = "metricCodes",
                desc = "指标code",
                type = "LIST_STRING",
                sampleValue = "[\"transCountByMinute\",\"transDurationByMinute\",\"transSuccessCountByMinute\"]",
                required = true),
            @DsReqParam(
                name = "midMetricSetting",
                desc = "指标code",
                type = "OBJECT",
                defaultValue = "{\"transBusinessSuccessRate\":[\"transSuccessCountByMinute\",\"transCountByMinute\"]," +
                        "\"transDurationAvg\":[\"transDurationByMinute\",\"transCountByMinute\"]}",
                required = true)
        },
        respParams = {
            @DsRespParam(
                name = "tradeValues",
                desc = "指标值",
                type = DsRespColumnTypeEnum.OBJECT)
        }
)
public class StdTradeMetricValueServiceImpl implements IDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StdTradeMetricValueServiceImpl.class);

    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        // 请求参数
        DataServiceRequest reqParam = dataServiceSetting.getDataServiceRequest();
        List<Long> seriesIds = (List) ReqParamUtil.convertReqVal("seriesIds", DsColumnTypeEnum.LIST_LONG.code(),
                reqParam.getParam().get("seriesIds"), null);
        List<String> metricCodes = (List) ReqParamUtil.convertReqVal("metricCodes", DsColumnTypeEnum.LIST_STRING.code(),
                reqParam.getParam().get("metricCodes"), null);
        Object settingParam = reqParam.getParam().get("midMetricSetting");
        Map<String, List<String>> metricSetting = TradeMetricUtil.calculateMetric(settingParam, metricCodes);
        String timeFrom = reqParam.getParam().get("timeFrom").toString();
        String timeTo = reqParam.getParam().get("timeTo").toString();
        Calendar timeFromCalendar = Calendar.getInstance();
        timeFromCalendar.setTimeInMillis(DateUtil.parse(timeFrom).getTime() / 60000 * 60000);
        Calendar timeToCalendar = Calendar.getInstance();
        timeToCalendar.setTimeInMillis(DateUtil.parse(timeTo).getTime() / 60000 * 60000);

        if (!timeToCalendar.after(timeFromCalendar)) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    String.format("请求参数timeTo[%s],需大于timeFrom[%s]", timeTo, timeFrom));
        }
        // 获取数据源
        Map<String, DsDatasource> dsDatasourceMap = dataServiceSetting.getDsDatasourceMap();
        DsDatasource ckDatasource = dsDatasourceMap.get("ck");
        CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) ckDatasource.getConnectionSetting();
        Connection jdbcConn = ClickhouseUtil.getConnection(ckManConnectionSetting.getCkJdbcUrl(), ckManConnectionSetting.getCkUsername(),
                ckManConnectionSetting.getCkPassword());
        // 指标模型
        metricCodes = metricSetting.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        List<TbMetricItem> metricItems = TradeMetricUtil.getMetricCode(metricCodes);
        TbTableDeployService tableDeployService = ContextHolder.getBean(TbTableDeployService.class);
        TbTableDeploy lastSuccessTableDeploy = tableDeployService.getLastSuccessTableDeploy(metricItems.get(0).getIdxId());
        String ckTableName = lastSuccessTableDeploy.getIdxTbName();
        String idxDatabase = JSONUtil.toBean(lastSuccessTableDeploy.getSetting(), Map.class).get("database").toString();
        ckTableName = String.format("`%s`.`%s`", idxDatabase, ckTableName);
        // 获取指标值
        // 注意：%Y-%m-%d %H:%M:%S，
        // %M在v23.4版本后为月份的英文，如：2023-08-17 01:August:00；
        // %M在v23.4版本前为分钟00-59，如：2023-08-17 01:00:00；
        // 所以需要使用%R代替%H:%M，%R更有兼容性，使用 %Y-%m-%d %R:%S 代替 %Y-%m-%d %H:%M:%S
        String dimensionValueSql = String.format("select  seriesId," +
                "formatDateTime(toStartOfInterval(timestamp, interval 1 minute ),'%%Y-%%m-%%d %%T') as timestamp," +
                "%s " +
                " from (" +
                "select __series_id as seriesId,timestamp," +
                TradeMetricUtil.getSumMetricSql(metricItems) +
                " from  %s " +
                " where seriesId in (%s) and timestamp >= '%s' and timestamp <= '%s' " +
                " group by seriesId,timestamp" +
                ") group by seriesId,timestamp order by timestamp desc",
                metricCodes.stream().map(x -> String.format("sum(%s) as %s", x, x)).collect(Collectors.joining(",")),
                ckTableName, seriesIds.stream().map(x -> x.toString()).collect(Collectors.joining(",")),
                timeFrom, timeTo);
        LOGGER.info(dimensionValueSql);
        Map<String, List<Map<String, Object>>> seriesIdMetricMap = ClickhouseUtil.query(jdbcConn, dimensionValueSql, null)
                .stream().collect(Collectors.groupingBy(x -> x.get("seriesId").toString()));
        List<Map<String, Object>> ret = new ArrayList<>();
        // 补零
        seriesIds.forEach(id -> {
            List<Map<String, Object>> maps = seriesIdMetricMap.containsKey(id.toString()) ?
                    seriesIdMetricMap.get(id.toString()) : new LinkedList<>();
            fill0MetricValue(maps, metricSetting, timeFromCalendar, timeToCalendar);
            Map<String, Object> row = new HashMap<>(2);
            row.put("seriesId", id);
            row.put("value", maps);
            ret.add(row);
        });
        DataServiceResponseData resp = new DataServiceResponseData();
        resp.setRows(ret);
        return resp;
    }

    private void fill0MetricValue(List<Map<String, Object>> metricValue, Map<String, List<String>> metricCodes,
                          Calendar timeFrom, Calendar tmpTimeTo) {
        Calendar timeTo = Calendar.getInstance();
        timeTo.setTime(tmpTimeTo.getTime());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int index = 0;
        while (timeTo.compareTo(timeFrom) >= 0) {
            Map<String, Object> colValue = metricValue.size() > index ? metricValue.get(index) : null;
            String curTime = colValue != null ? colValue.get("timestamp").toString() : null;
            if (StrUtil.isBlank(curTime) || curTime.compareTo(format.format(timeTo.getTime())) < 0) {
                Map<String, Object> zeroValue = new HashMap<>(16);
                zeroValue.put("timestamp", format.format(timeTo.getTime()));
                metricCodes.keySet().forEach(x -> zeroValue.put(x, 0L));
                metricValue.add(index, zeroValue);
            } else if (curTime.compareTo(format.format(timeTo.getTime())) > 0) {
                metricValue.remove(index);
                continue;
            } else {
                colValue.remove("seriesId");
                metricCodes.forEach((metricName, setting) -> {
                    if (setting.size() == 2) {
                        double v1 = Double.parseDouble(ObjectUtil.defaultIfNull(colValue.get(setting.get(0)), 0).toString());
                        double v2 = Double.parseDouble(ObjectUtil.defaultIfNull(colValue.get(setting.get(1)), 0).toString());
                        Double val = v2 == 0 ? 0 : v1 / v2 * 100;
                        colValue.put(metricName, val);
                    }
                });
            }
            index++;
            timeTo.add(Calendar.MINUTE, -1);
        }
        metricValue.subList(0, index);
        Collections.reverse(metricValue);
    }
}
