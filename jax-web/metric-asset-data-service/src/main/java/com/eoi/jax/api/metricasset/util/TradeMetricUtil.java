package com.eoi.jax.api.metricasset.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.repository.entity.TbMetricItem;
import com.eoi.jax.web.repository.service.TbColumnService;
import com.eoi.jax.web.repository.service.TbMetricItemService;
import com.eoi.jax.web.repository.service.TbTableService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: yaru.ma
 * @Date: 2023/9/18
 */
public class TradeMetricUtil {

    /**
     * 指标模型(多值模型)
     *
     * @param metricCodes
     * @return
     */
    public static List<TbMetricItem> getMetricCode(List<String> metricCodes) {
        if (CollUtil.isEmpty(metricCodes)) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    "请求参数[metricCodes]不能为空");
        }
        TbMetricItemService tbMetricItemService = ContextHolder.getBean(TbMetricItemService.class);
        List<TbMetricItem> metricItems = tbMetricItemService
                .list(new LambdaQueryWrapper<TbMetricItem>().in(TbMetricItem::getNameEn, metricCodes));
        if (metricItems.size() < metricCodes.size()) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    "请求参数[metricCodes]中有指标未建立指标模型或指标重复");
        }
        long idxCount = metricItems.stream().map(TbMetricItem::getIdxId).distinct().count();
        if (idxCount != 1) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    "请求参数[metricCodes]关联的不是同一个指标表");
        }
        return metricItems;
    }

    public static String getSumMetricSql(List<TbMetricItem> metricItems) {
        return getMetricSql(metricItems, "sum");
    }


    public static String getMetricSql(List<TbMetricItem> metricItems, String aggMethod) {
        TbTableService tableService = ContextHolder.getBean(TbTableService.class);
        Boolean singleModel = "SINGLE".equals(tableService.getById(metricItems.get(0).getIdxId()).getStorageMode());
        if (singleModel) {
            return StrUtil.isNotBlank(aggMethod) ? String.format(" %s(`value`) as `value`", aggMethod) : " `value` as `value` ";
        } else {
            TbColumnService tbColumnService = ContextHolder.getBean(TbColumnService.class);
            Map<String, String> colMap = metricItems.stream().collect(Collectors.toMap(TbMetricItem::getNameEn,
                    x -> tbColumnService.existId(x.getIdxColumnId(), null).getColName()));
            return colMap.keySet().stream()
                    .map(x -> StrUtil.isNotBlank(aggMethod) ?
                            String.format(" %s(`%s`) as `%s` ", aggMethod, colMap.get(x), x) :
                            String.format("`%s` as `%s`", colMap.get(x), x))
                    .collect(Collectors.joining(","));
        }
    }


    public static Map<String, List<String>> calculateMetric(Object settingParam, List<String> metricCodes) {
        // 衍生指标需要计算
        Map<String, List<String>> midMetricSetting = (Map) (settingParam instanceof String ?
                JsonUtil.decode2Map(settingParam.toString()) : settingParam);
        Map<String, List<String>> metricSetting = new HashMap<>(metricCodes.size());
        for (String metricCode : metricCodes) {
            if (!midMetricSetting.containsKey(metricCode)) {
                metricSetting.put(metricCode, Arrays.asList(metricCode));
            } else {
                metricSetting.put(metricCode, midMetricSetting.get(metricCode));
            }
        }
        return metricSetting;
    }

}
