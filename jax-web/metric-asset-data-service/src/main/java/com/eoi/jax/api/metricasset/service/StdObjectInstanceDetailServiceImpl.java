package com.eoi.jax.api.metricasset.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.annotation.*;
import com.eoi.jax.api.dataservice.enumrate.DsColumnTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsRespColumnTypeEnum;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.CkManConnectionSetting;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.api.metricasset.model.objecttable.ObjectInstanceDetailReq;
import com.eoi.jax.api.metricasset.util.ClickhouseUtil;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.util.JaxLoggerUtil;
import com.eoi.jax.web.data.service.service.DatasourceHelperService;
import com.eoi.jax.web.repository.entity.TbSystemConfig;
import com.eoi.jax.web.repository.service.TbDatasourceService;
import com.eoi.jax.web.repository.service.TbSystemConfigService;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/9/20
 */

@DataServiceHandler(
        name = "objectInstanceDetail",
        description = "查询对象模型实例详情",
        accessJaxMeta = false,
        enablePaged = false,
        datasources = {
                @DsDatasourceParam(name = "clickhouse", display = "cmdb宽表数据库", type = DsDatasourceTypeEnum.CLICKHOUSE)
        },
        serviceParams = {
                @ServiceSettingParam(name = "tbName", value = "", desc = "宽表名称，格式：数据库名.表名，如果该值为空，默认使用系统配置参数",
                        type = DsColumnTypeEnum.STRING)
        },
        reqParams = {
                @DsReqParam(name = "partition",
                        desc = "数据分区，会查找当前传入的分区，如果没有，则会找当天最新的分区，" +
                                "如果当天没有分区，返回空，格式：yyyyMMddHHmmss，例如：20240618123925",
                        type = "STRING", required = false),
                @DsReqParam(name = "objInstanceId", desc = "对象模型实例id", type = "STRING", required = true)
        },
        respParams = {
                @DsRespParam(name = "objectId", desc = "实例id", type = DsRespColumnTypeEnum.LONG),
                @DsRespParam(name = "otherKeyClumn", desc = "其他标识字段或关键字段", type = DsRespColumnTypeEnum.LONG)
        }
)
@SuppressWarnings("all")
public class StdObjectInstanceDetailServiceImpl implements IDataService {

    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        ObjectInstanceDetailReq req = BeanUtil.mapToBean(dataServiceSetting.getDataServiceRequest().getParam(),
                ObjectInstanceDetailReq.class, true);
        DsDatasource clickhouseDs = dataServiceSetting.getDsDatasourceMap().get("clickhouse");
        CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) clickhouseDs.getConnectionSetting();
        Connection jdbcConn = null;
        String sql = "";
        try {
            String usedPartition = "";
            if (StringUtils.isBlank(req.getTbName()) || StringUtils.isBlank(req.getPartition())) {
                TbSystemConfigService tbSystemConfigService = ContextHolder.getBean(TbSystemConfigService.class);
                String prefixKey = "jax.modeling.object.instance.wide.table";
                TbSystemConfig systemConfig = tbSystemConfigService.getByPrefixKey(prefixKey);
                if (systemConfig != null) {
                    if (systemConfig.getDsId() != null && StringUtils.isBlank(req.getTbName())) {
                        DatasourceHelperService datasourceHelperService = ContextHolder.getBean(DatasourceHelperService.class);
                        TbDatasourceService datasourceService = ContextHolder.getBean(TbDatasourceService.class);
                        DsDatasource dsDatasource = datasourceHelperService.getDsDatasource(systemConfig.getDsId());
                        CkManConnectionSetting setting = (CkManConnectionSetting) dsDatasource.getConnectionSetting();
                        jdbcConn = ClickhouseUtil.getConnection(setting.getCkJdbcUrl(),
                                setting.getCkUsername(),
                                setting.getCkPassword());
                    }
                    if (StringUtils.isNotBlank(systemConfig.getSetting())) {
                        Map<String, Object> setting = JSONUtil.toBean(systemConfig.getSetting(), Map.class);
                        if (StringUtils.isBlank(req.getTbName())) {
                            req.setTbName((String) setting.get("cmdbWideTable"));
                        }
                        if (StringUtils.isBlank(req.getPartition())) {
                            usedPartition = (String) setting.get("usedPartition");
                        }
                    }
                }
            }
            if (StringUtils.isBlank(req.getTbName())) {
                throw new RuntimeException("宽表名称不能为空");
            }

            if (jdbcConn == null) {
                jdbcConn = ClickhouseUtil.getConnection(ckManConnectionSetting.getCkJdbcUrl(),
                        ckManConnectionSetting.getCkUsername(),
                        ckManConnectionSetting.getCkPassword());
            }

            if (StringUtils.isNotBlank(req.getPartition())) {
                usedPartition = "";
                Date date = DateUtil.parse(req.getPartition(), "yyyyMMddHHmmss");
                String day = req.getPartition().substring(0, 8);
                String selectPartionsSql = " select distinct toYYYYMMDDhhmmss(date) as partition from  " + req.getTbName() +
                        " where toString(toYYYYMMDDhhmmss(date)) like '" + day + "%' " +
                        " order by date desc;";
                List<Map<String, Object>> partions = ClickhouseUtil.query(jdbcConn, selectPartionsSql, null);
                if (partions.size() > 0) {
                    boolean hasPartion = false;
                    for (Map<String, Object> partion : partions) {
                        if (req.getPartition().equals(partion.get("partition"))) {
                            hasPartion = true;
                            usedPartition = req.getPartition();
                            break;
                        }
                    }
                    if (!hasPartion) {
                        usedPartition = partions.get(0).get("partition").toString();
                    }
                }
            }
            if (StringUtils.isBlank(usedPartition)) {
                throw new RuntimeException("没有找到可用的分区: " + usedPartition);
            }

            sql = getSql(req, usedPartition);
            List<Map<String, Object>> valueResult = ClickhouseUtil.query(jdbcConn, sql, null);

            List<Map<String, Object>> rows = new LinkedList<>();
            if (valueResult != null && valueResult.size() > 0) {
                Map<String, Object> detail = valueResult.get(0);
                Map<String, Object> properMap = (Map<String, Object>) detail.getOrDefault("properties", new HashMap<>(16));
                detail.remove("properties");
                detail.remove("date");
                detail.remove("snapshotDate");
                detail.remove("source");
                detail.remove("tag");
                detail.remove("vid");
                detail.putAll(properMap);
                rows.add(detail);
            }
            DataServiceResponseData resp = new DataServiceResponseData();
            resp.setRows(rows);
            resp.setTotalNum(resp.getRows() == null ? 0 : (long) resp.getRows().size());
            return resp;
        } catch (Exception e) {
            JaxLoggerUtil.getScheduleJobLogger()
                    .error("查询异常，req=" + JSONUtil.toJsonPrettyStr(dataServiceSetting.getDataServiceRequest())
                            + ", sql=" + sql != null ? sql : "", e);
            throw e;
        } finally {
            ClickhouseUtil.close(null, null, jdbcConn);
        }
    }

    public String getSql(ObjectInstanceDetailReq req, String partition) {
        StringBuilder sql = new StringBuilder("select *");
        sql.append(" from ").append(req.getTbName());
        sql.append(" where objectId='").append(req.getObjInstanceId()).append("' ");
        if (StringUtils.isNotBlank(partition)) {
            sql.append(" and toYYYYMMDDhhmmss(date)='").append(partition).append("' ");
        }
        sql.append(" order by date desc ").append(" limit 1");

        return sql.toString();
    }


}
