package com.eoi.jax.api.metricasset.model.nebula;

import cn.hutool.core.collection.CollUtil;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zsc
 * @create 2023/11/16 13:24
 */

public class StdObjectInstanceRelationBatchQueryReq {

    private Boolean useDefaultCfg;
    private String namespace;
    private List<String> objTypes;
    private List<String> objInstanceIds;

    private Integer pathLength;

    private Map<String, Object> properties;


    public String buildMatchSql() {
        StringBuilder sqlBuilder = new StringBuilder("USE ").append(namespace).append("; ");
        if (pathLength == null) {
            sqlBuilder.append(" MATCH p = (r)-[*]->(t)");
        } else {
            sqlBuilder.append(" MATCH p = (r)-[e*1..").append(pathLength).append("]->(t)");
        }
        sqlBuilder.append(" WHERE id(r) in ['")
                .append(String.join("','", objInstanceIds))
                .append("']");
        String whereSql = buildWhereSql();
        sqlBuilder.append(whereSql);
        sqlBuilder.append(" RETURN p");
        return sqlBuilder.toString();
    }

    public String buildWhereSql() {
        StringBuilder whereBuilder = new StringBuilder();

        if (CollUtil.isNotEmpty(objTypes) || CollUtil.isNotEmpty(properties)) {
            whereBuilder.append(" AND all( x IN nodes(p)[1..] WHERE 1==1 ");
            if (CollUtil.isNotEmpty(objTypes)) {
                whereBuilder.append(" AND properties(x).objectType IN [");
                for (String objType : objTypes) {
                    whereBuilder.append("'").append(objType).append("',");
                }
                whereBuilder.deleteCharAt(whereBuilder.length() - 1);
                whereBuilder.append("]");
            }
            if (CollUtil.isNotEmpty(properties)) {
                for (Map.Entry<String, Object> entry : properties.entrySet()) {
                    whereBuilder.append(" AND properties(x).").append(entry.getKey().trim()).append(" == '")
                            .append(entry.getValue()).append("'");
                }
            }
            whereBuilder.append(")");
        }

        return whereBuilder.toString();
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public List<String> getObjTypes() {
        return objTypes;
    }

    public void setObjTypes(List<String> objTypes) {
        this.objTypes = objTypes;
    }

    public Integer getPathLength() {
        return pathLength;
    }

    public Boolean getUseDefaultCfg() {
        return useDefaultCfg;
    }

    public void setUseDefaultCfg(Boolean useDefaultCfg) {
        this.useDefaultCfg = useDefaultCfg;
    }

    public void setPathLength(Integer pathLength) {
        this.pathLength = pathLength;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public List<String> getObjInstanceIds() {
        return objInstanceIds;
    }

    public void setObjInstanceIds(List<String> objInstanceIds) {
        this.objInstanceIds = objInstanceIds;
    }
}
