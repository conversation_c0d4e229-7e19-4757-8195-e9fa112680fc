package com.eoi.jax.api.metricasset.model.objectmetric;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/11/10 15:27
 */

public class MetricSingleValuesReq {
    private String timeFrom;

    private String timeTo;

    private Long metricId;

    private List<Long> seriesIds;

    private Integer maxRow;

    private Integer step;

    private boolean autoFillZero;

    private String aggType;

    public String getTimeFrom() {
        return timeFrom;
    }

    public void setTimeFrom(String timeFrom) {
        this.timeFrom = timeFrom;
    }

    public String getTimeTo() {
        return timeTo;
    }

    public void setTimeTo(String timeTo) {
        this.timeTo = timeTo;
    }

    public Long getMetricId() {
        return metricId;
    }

    public void setMetricId(Long metricId) {
        this.metricId = metricId;
    }

    public List<Long> getSeriesIds() {
        return seriesIds;
    }

    public void setSeriesIds(List<Long> seriesIds) {
        this.seriesIds = seriesIds;
    }

    public Integer getMaxRow() {
        return maxRow;
    }

    public void setMaxRow(Integer maxRow) {
        this.maxRow = maxRow;
    }

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public boolean isAutoFillZero() {
        return autoFillZero;
    }

    public void setAutoFillZero(boolean autoFillZero) {
        this.autoFillZero = autoFillZero;
    }

    public String getAggType() {
        return aggType;
    }

    public void setAggType(String aggType) {
        this.aggType = aggType;
    }
}
