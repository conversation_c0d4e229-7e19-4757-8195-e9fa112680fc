package com.eoi.jax.api.metricasset.model.objectmetric;

/**
 * <AUTHOR> zsc
 * @create 2023/1/3 17:05
 */
public class MetricItemTreeReq {
    private String itemName;

    private String objTypeCode;

    private Long objId;

    private Long categoryId;

    private String categoryType;

    private String objInstanceId;

    private Boolean returnNoMetric;

    public void setCategoryType(String categoryType) {
        this.categoryType = categoryType;
    }

    public String getCategoryType() {
        return categoryType;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getObjTypeCode() {
        return objTypeCode;
    }

    public void setObjTypeCode(String objTypeCode) {
        this.objTypeCode = objTypeCode;
    }

    public String getObjInstanceId() {
        return objInstanceId;
    }

    public void setObjInstanceId(String objInstanceId) {
        this.objInstanceId = objInstanceId;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Boolean getReturnNoMetric() {
        return returnNoMetric;
    }

    public void setReturnNoMetric(Boolean returnNoMetric) {
        this.returnNoMetric = returnNoMetric;
    }
}
