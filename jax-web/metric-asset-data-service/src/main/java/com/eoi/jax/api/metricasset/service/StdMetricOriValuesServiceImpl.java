package com.eoi.jax.api.metricasset.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.annotation.DataServiceHandler;
import com.eoi.jax.api.dataservice.annotation.DsReqParam;
import com.eoi.jax.api.dataservice.annotation.DsRespParam;
import com.eoi.jax.api.dataservice.annotation.ServiceSettingParam;
import com.eoi.jax.api.dataservice.enumrate.DsColumnTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsRespColumnTypeEnum;
import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.CkManConnectionSetting;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.api.metricasset.model.objectmetric.MetricDimSerialsIdResp;
import com.eoi.jax.api.metricasset.model.objectmetric.MetricSingleValuesReq;
import com.eoi.jax.api.metricasset.model.objectmetric.MetricValuesResp;
import com.eoi.jax.api.metricasset.util.ClickhouseUtil;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.util.JaxLoggerUtil;
import com.eoi.jax.web.data.service.service.DatasourceHelperService;
import com.eoi.jax.web.repository.entity.TbColumn;
import com.eoi.jax.web.repository.entity.TbMeasureUnit;
import com.eoi.jax.web.repository.entity.TbMetricItem;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.service.TbColumnService;
import com.eoi.jax.web.repository.service.TbMeasureUnitService;
import com.eoi.jax.web.repository.service.TbMetricItemService;
import com.eoi.jax.web.repository.service.TbTableDeployService;
import org.springframework.util.Assert;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/18
 */
@DataServiceHandler(
        name = "stdMetricOriValues",
        description = "查询对象实例维度指定指标项的指标数据(用于查询全部指标数据，不聚合，返回原始数据)",
        accessJaxMeta = false,
        enablePaged = false,
        datasources = {},
        serviceParams = {
                @ServiceSettingParam(name = "maxRow", value = "600000", desc = "返回的最大行数", type = DsColumnTypeEnum.INT)
        },
        reqParams = {
                @DsReqParam(name = "timeFrom", desc = "开始时间", type = "STRING", sampleValue = "2023-09-19 09:00:00", required = true),
                @DsReqParam(name = "timeTo", desc = "结束时间", type = "STRING", sampleValue = "2023-09-19 09:00:00", required = true),
                @DsReqParam(name = "metricId", desc = "指标项ids", type = "LONG", required = true),
                @DsReqParam(name = "seriesIds", desc = "指标维度seriesIds", type = "LIST_LONG", required = true)
        },
        respParams = {
                @DsRespParam(name = "metric", desc = "指标维度信息", type = DsRespColumnTypeEnum.OBJECT),
                @DsRespParam(name = "props", desc = "其他属性", type = DsRespColumnTypeEnum.OBJECT),
                @DsRespParam(name = "values", desc = "指标值数据", type = DsRespColumnTypeEnum.OBJECT)
        }
)
@SuppressWarnings("all")
public class StdMetricOriValuesServiceImpl implements IDataService {

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        MetricSingleValuesReq req = BeanUtil.mapToBean(dataServiceSetting.getDataServiceRequest().getParam(),
                MetricSingleValuesReq.class, true);
        Assert.isTrue(req.getTimeFrom() != null, "请求参数timeFrom不能为空");
        Assert.isTrue(req.getTimeTo() != null, "请求参数timeTo不能为空");
        Assert.isTrue(req.getMetricId() != null, "请求参数metricId不能为空");
        Assert.isTrue(CollUtil.isNotEmpty(req.getSeriesIds()), "请求参数seriesIds不能为空");
        if (req.getTimeTo().compareTo(req.getTimeFrom()) < 0) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    String.format("请求参数timeTo[%s],需大于timeFrom[%s]",
                            req.getTimeTo(), req.getTimeFrom()));
        }

        Integer maxRow = req.getMaxRow();

        TbMetricItemService tbMetricItemService = ContextHolder.getBean(TbMetricItemService.class);
        TbMetricItem tbMetricItem = tbMetricItemService.getById(req.getMetricId());
        if (tbMetricItem == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "指标项:" + req.getMetricId() + "不存在");
        }
        Long idxTbId = tbMetricItem.getIdxId();
        if (idxTbId == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(), "指标项未关联指标表");
        }

        TbTableDeployService tbTableDeployService = ContextHolder.getBean(TbTableDeployService.class);
        DatasourceHelperService datasourceHelperService = ContextHolder.getBean(DatasourceHelperService.class);
        TbTableDeploy tableDeploy = tbTableDeployService.getLastByTableId(idxTbId);

        TbColumnService tbColumnService = ContextHolder.getBean(TbColumnService.class);

        TbColumn tbColumn = tbColumnService.getById(tbMetricItem.getIdxColumnId());
        if (tbColumn == null) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS.getCode(),
                    "指标项关联的指标表列字段不存在，columnId=" + tbMetricItem.getIdxColumnId());
        }
        String idxTbColumn = tbColumn.getColName();
        String dimTbName = tableDeploy.getDimTbName();
        String idxTbName = tableDeploy.getIdxTbName();
        String dbName = JSONUtil.toBean(tableDeploy.getSetting(), Map.class).get("database").toString();
        DsDatasource dsDatasource = datasourceHelperService.getDsDatasource(tableDeploy.getDsId());
        CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) dsDatasource.getConnectionSetting();
        Connection jdbcConn = null;
        StringBuilder sb = null;
        String sql = "";
        try {
            jdbcConn = ClickhouseUtil.getConnection(ckManConnectionSetting.getCkJdbcUrl(),
                    ckManConnectionSetting.getCkUsername(),
                    ckManConnectionSetting.getCkPassword());
            String seriesIds = req.getSeriesIds().stream()
                    .map(String::valueOf).collect(Collectors.joining(","));
            // 查询出维度表记录
            sb = new StringBuilder("select distinct `__series_id`, `__mgmt_id`, labels, `__name__`, metricId, objectId from ")
                    .append(dbName).append(".").append("`").append(dimTbName).append("`")
                    .append(" where ");
            sb.append(" `__series_id` in (")
                    .append(seriesIds)
                    .append(") ");
            List<Map<String, Object>> seriesList = ClickhouseUtil.query(jdbcConn, sb.toString(), null);
            List<MetricDimSerialsIdResp> metricDimensionRespList = StdMetricSeriesServiceImpl.getMetricDimSerialsIdResps(seriesList);

            Map<String, MetricDimSerialsIdResp> dimResultMap = new LinkedHashMap<>(16);
            metricDimensionRespList.forEach(x -> {
                dimResultMap.put(x.getSeriesId(), x);
            });

            // 查询出指标表记录
            sql = String.format(
                    getNormalSql(),
                    idxTbColumn, dbName, idxTbName, req.getTimeFrom(), req.getTimeTo(), seriesIds);

            List<Map<String, Object>> valueResult = ClickhouseUtil.query(jdbcConn, sql, maxRow);
            Map<String, List<Map<String, Object>>> valueResultMap = new LinkedHashMap<>(16);
            valueResult.forEach(x -> {
                String seriesId = x.get("__series_id").toString();
                List<Map<String, Object>> list1 = valueResultMap.get(seriesId);
                if (list1 == null) {
                    list1 = new ArrayList<>();
                    valueResultMap.put(seriesId, list1);
                }
                list1.add(x);
            });

            List<Map<String, Object>> respList = new ArrayList<>();
            if (CollUtil.isNotEmpty(valueResultMap)) {
                TbMeasureUnit tbMeasureUnit = ContextHolder.getBean(TbMeasureUnitService.class).getById(tbMetricItem.getUnitId());
                for (Map.Entry entry : valueResultMap.entrySet()) {
                    String seriesId = (String) entry.getKey();
                    List<Map<String, Object>> valueList = (List<Map<String, Object>>) entry.getValue();
                    MetricValuesResp resp = new MetricValuesResp();
                    Map<String, Object> metric = new LinkedHashMap<>();
                    MetricDimSerialsIdResp dimMap = dimResultMap.get(seriesId);
                    if (dimMap != null) {
                        metric.putAll(BeanUtil.beanToMap(dimMap));
                    }
                    Map<String, Object> props = new LinkedHashMap<>();
                    props.put("unit", tbMeasureUnit != null ? tbMeasureUnit.getName() : "");
                    resp.setProps(props);
                    resp.setMetric(metric);
                    List<Object[]> values = new LinkedList<>();
                    resp.setValues(values);
                    respList.add(BeanUtil.beanToMap(resp));

                    for (Map<String, Object> valueMap : valueList) {
                        values.add(new Object[]{valueMap.get("ts").toString(), valueMap.get("value").toString()});
                    }
                }
            }

            DataServiceResponseData resp = new DataServiceResponseData();
            resp.setRows(respList);
            resp.setTotalNum(resp.getRows() == null ? 0 : (long) resp.getRows().size());
            return resp;
        } catch (Exception e) {
            JaxLoggerUtil.getScheduleJobLogger().error("查询ck异常，sql=" + sb != null ? sb.toString() : "", e);
            throw e;
        } finally {
            ClickhouseUtil.close(null, null, jdbcConn);
        }
    }


    private String getNormalSql() {
        return
                "         select  " +
                        "            `__series_id`,  " +
                        "            `timestamp` as ts,  " +
                        "            `%s` as `value` " +
                        "         from `%s`.%s   " +
                        "         where `timestamp` >= '%s' " +
                        "         and `timestamp` <= '%s' " +
                        "         and `__series_id` in (%s) " +
                        "   order by `__series_id` ,`timestamp` ";
    }


}
