package com.eoi.jax.api.metricasset.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.annotation.*;
import com.eoi.jax.api.dataservice.enumrate.DsColumnTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsRespColumnTypeEnum;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.NebularConnectionSetting;
import com.eoi.jax.api.dataservice.model.request.DataServiceRequest;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.api.dataservice.util.JaxLogUtil;
import com.eoi.jax.api.metricasset.model.nebula.StdObjectInstanceRelationByGoQueryReq;
import com.eoi.jax.api.metricasset.util.NebulaClient;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.repository.entity.TbDatasource;
import com.eoi.jax.web.repository.entity.TbSystemConfig;
import com.eoi.jax.web.repository.service.TbDatasourceService;
import com.eoi.jax.web.repository.service.TbSystemConfigService;
import com.vesoft.nebula.client.graph.data.Relationship;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date 2024/5/28
 */
@DataServiceHandler(
        name = "stdObjectInstanceRelationByGo",
        description = "查询对象实例的关联关系（从图库查）",
        accessJaxMeta = false,
        enablePaged = false,
        datasources = {
                @DsDatasourceParam(name = "nebular", display = "nebular图数据库", type = DsDatasourceTypeEnum.NEBULA),
        },
        serviceParams = {
                @ServiceSettingParam(name = "useDefaultCfg", value = "true", desc = "是否使用默认的系统配置参数",
                        type = DsColumnTypeEnum.BOOLEAN),
                @ServiceSettingParam(name = "maxEdge", value = "50000", desc = "最大返回的边数量,系统默认最大返回50000条边",
                        type = DsColumnTypeEnum.INT)
        },
        reqParams = {
                @DsReqParam(name = "objInstanceId", desc = "起始节点对象实例id", type = "STRING", sampleValue = "", required = true),
                @DsReqParam(name = "namespace",
                        desc = "图库命名空间，如果使用默认图空间名参数格式：20240618123925，" +
                                "只有使用默认的配置，自动查询当前传入的图空间名称，如果存在就使用当前图空间名，" +
                                "否则接口会截取图空间名最后的时间串，自动使用当天最新的图空间版本。" +
                                "如果不是使用默认配置，接口会直接查询当前传入的图空间名 。",
                        type = "STRING",
                        sampleValue = "", required = false),
                @DsReqParam(name = "pathLength", desc = "最大路径长度", type = "INT", defaultValue = "3", required = true),
                @DsReqParam(name = "countNextNode", desc = "是否计算最后一个节点的下级数量", type = "BOOLEAN", defaultValue = "false", required = true),
                @DsReqParam(name = "queryTowDirection", desc = "是否查询2个方向的数据", type = "BOOLEAN", defaultValue = "false", required = true)
        },
        respParams = {
                @DsRespParam(name = "tags", desc = "对象实例节点信息", type = DsRespColumnTypeEnum.OBJECT),
                @DsRespParam(name = "edges", desc = "关系路径边信息", type = DsRespColumnTypeEnum.OBJECT)
        }
)
@SuppressWarnings("all")
public class StdObjectInstanceRelationByGoServiceImpl implements IDataService {

    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        StdObjectInstanceRelationByGoQueryReq req = BeanUtil.mapToBean(dataServiceSetting.getDataServiceRequest().getParam(),
                StdObjectInstanceRelationByGoQueryReq.class, true);
        if (req.getUseDefaultCfg() == null) {
            req.setUseDefaultCfg(false);
        }

        if (req.getObjInstanceId() == null) {
            throw new RuntimeException("起始节点对象实例id不能为空");
        }
        if (req.getPathLength() != null && req.getPathLength() < 1) {
            throw new RuntimeException("最大路径长度不能小于1");
        }
        if (!req.getUseDefaultCfg() && StringUtils.isBlank(req.getNamespace())) {
            throw new RuntimeException("不使用默认配置时，命名空间不能为空");
        }

        NebulaClient nebulaClient = null;
        if (req.getUseDefaultCfg()) {
            TbSystemConfigService tbSystemConfigService = ContextHolder.getBean(TbSystemConfigService.class);
            String prefixKey = "jax.modeling.object.instance.nebula";
            TbSystemConfig systemConfig = tbSystemConfigService.getByPrefixKey(prefixKey);

            String baseSpace = "";
            String imageSpace = "";
            if (systemConfig == null) {
                throw new RuntimeException("系统nebula配置参数不存在");
            }
            if (systemConfig.getDsId() != null) {
                TbDatasourceService datasourceService = ContextHolder.getBean(TbDatasourceService.class);
                TbDatasource tbDatasource = datasourceService.getById(systemConfig.getDsId());
                Map<String, Object> nebulaSetting = JSONUtil.toBean(tbDatasource.getSetting(), Map.class);
                NebularConnectionSetting nebularConnectionSetting = BeanUtil.mapToBean(nebulaSetting,
                        NebularConnectionSetting.class, true);
                nebulaClient = new NebulaClient(
                        nebularConnectionSetting.getAddress(),
                        nebularConnectionSetting.getUsername(),
                        nebularConnectionSetting.getPassword()
                );
            }

            if (StringUtils.isNotBlank(systemConfig.getSetting())) {
                Map<String, Object> setting = JSONUtil.toBean(systemConfig.getSetting(), Map.class);
                baseSpace = (String) setting.get("baseSpace");
                imageSpace = (String) setting.get("imageSpace");
            }

            if (StringUtils.isNotBlank(req.getNamespace())) {
                String reqSpace = req.getNamespace();
                req.setNamespace(null);
                Date date = DateUtil.parse(reqSpace, "yyyyMMddHHmmss");
                String prefix = baseSpace + "_" + reqSpace.substring(0, 8);
                String completeSpace = baseSpace + "_" + reqSpace;
                String querySpace = "SHOW SPACES;";
                try {
                    ResultSet resultSet = nebulaClient.doExecute(querySpace);
                    List<String> names = new ArrayList<>();
                    if (resultSet.isSucceeded()) {
                        for (int i = 0, size = resultSet.rowsSize(); i < size; i++) {
                            ResultSet.Record record = resultSet.rowValues(i);
                            List<ValueWrapper> valueList = record.values();
                            for (ValueWrapper valueWrapper : valueList) {
                                String space = (String) nebulaClient.wrapperValue(valueWrapper);
                                if (space.startsWith(prefix)) {
                                    names.add(space);
                                }
                            }
                        }
                    }
                    boolean containsSpace = false;
                    if (names.size() > 0) {
                        for (String name : names) {
                            if (name.equals(completeSpace)) {
                                req.setNamespace(name);
                                containsSpace = true;
                                break;
                            }
                        }
                        if (!containsSpace) {
                            Collections.sort(names);
                            req.setNamespace(names.get(names.size() - 1));
                        }
                    }
                } catch (Exception e) {
                    JaxLogUtil.getDataServiceLogger().error("execute nebula sql error, querySql=" + querySpace, e);
                    throw new RuntimeException(e);
                }
                if (StringUtils.isBlank(req.getNamespace())) {
                    throw new RuntimeException("nebula命名空间[" + reqSpace + "]不存在，且当天没有版本镜像");
                }
            } else {
                req.setNamespace(imageSpace);
            }
        }


        if (StringUtils.isBlank(req.getNamespace())) {
            throw new RuntimeException("nebula命名空间没有找到");
        }

        if (nebulaClient == null) {
            nebulaClient = createNebulaClient(dataServiceSetting);
        }

        String querySql = "";
        try {
            Map<String, Object> row = new LinkedHashMap<>();
            if (req.getCountNextNode() != null && req.getCountNextNode()) {
                Map<String, Map<String, Object>> oriTags = new LinkedHashMap<>();
                List<Map<String, Object>> oriEdges = new LinkedList<>();
                Map<String, Map<String, Object>> countNextTags = new LinkedHashMap<>();
                List<Map<String, Object>> countNextEdges = new LinkedList<>();

                querySql = req.buildOriMatchSql();
                NebulaClient finalNebulaClient = nebulaClient;
                String finalQuerySql = querySql;
                CompletableFuture<Void> oriFuture = CompletableFuture.runAsync(() -> {
                    queryGoSql(finalNebulaClient, finalQuerySql, oriTags, oriEdges);
                }).exceptionally(ex -> {
                    throw new RuntimeException("查询nebula失败：sql=" + finalQuerySql, ex);
                });

                String countNextMatchSql = req.buildCountNextMatchSql();
                CompletableFuture<Void> countNextFuture = CompletableFuture.runAsync(() -> {
                    queryGoSql(finalNebulaClient, countNextMatchSql, countNextTags, countNextEdges);
                }).exceptionally(ex -> {
                    throw new RuntimeException("查询多一跳nebula失败：sql=" + countNextMatchSql, ex);
                });
                oriFuture.get();
                countNextFuture.get();

                // 查询多一跳的数据
                if (oriEdges.size() > 0) {
                    Map<String, Set<String>> nextNodeCount = new HashMap<>();
                    for (Map<String, Object> e : countNextEdges) {
                        String srcId = String.valueOf(e.get("source"));
                        String dstId = String.valueOf(e.get("target"));
                        Set<String> nextNodes = nextNodeCount.get(srcId);
                        if (nextNodes == null) {
                            nextNodes = new HashSet<>();
                            nextNodeCount.put(srcId, nextNodes);
                        }
                        nextNodes.add(dstId);
                    }

                    Map<String, Set<String>> oriNodeCount = new HashMap<>();
                    for (Map<String, Object> e : oriEdges) {
                        String srcId = String.valueOf(e.get("source"));
                        String dstId = String.valueOf(e.get("target"));
                        Set<String> nextNodes = oriNodeCount.get(srcId);
                        if (nextNodes == null) {
                            nextNodes = new HashSet<>();
                            oriNodeCount.put(srcId, nextNodes);
                        }
                        nextNodes.add(dstId);
                    }

                    for (Map.Entry<String, Map<String, Object>> entry : oriTags.entrySet()) {
                        String srcId = entry.getKey();
                        Map<String, Object> tag = entry.getValue();
                        // 是否最后一个节点
                        Set<String> nextNodes = oriNodeCount.getOrDefault(srcId, new HashSet<>());
                        if (nextNodes.size() > 0) {
                            continue;
                        }

                        Set<String> nextNodes2 = nextNodeCount.getOrDefault(srcId, new HashSet<>());
                        tag.put("nextNodeNum", nextNodes2.size());
                    }
                }

                row.put("tags", oriTags.values());
                row.put("edges", oriEdges);

            } else {
                Map<String, Map<String, Object>> oriTags = new LinkedHashMap<>();
                List<Map<String, Object>> oriEdges = new LinkedList<>();
                querySql = req.buildOriMatchSql();
                queryGoSql(nebulaClient, querySql, oriTags, oriEdges);

                row.put("tags", oriTags.values());
                row.put("edges", oriEdges);
            }

            DataServiceResponseData resp = new DataServiceResponseData();
            List<Map<String, Object>> rows = new ArrayList<>();
            rows.add(row);
            resp.setRows(rows);
            resp.setTotalNum((long) rows.size());

            return resp;
        } catch (Exception e) {
            JaxLogUtil.getDataServiceLogger().error("execute nebula sql error, querySql=" + querySql, e);
            throw new RuntimeException(e);
        } finally {
            if (nebulaClient != null) {
                nebulaClient.close();
            }
        }
    }

    private static void queryGoSql(NebulaClient nebulaClient, String querySql,
                                   Map<String, Map<String, Object>> tags, List<Map<String, Object>> edges) {
        try {
            ResultSet resultSet = nebulaClient.doExecute(querySql);
            if (resultSet.isSucceeded()) {
                for (int i = 0, size = resultSet.rowsSize(); i < size; i++) {
                    ResultSet.Record record = resultSet.rowValues(i);
                    List<ValueWrapper> valueList = record.values();
                    for (ValueWrapper valueWrapper : valueList) {
                        if (valueWrapper == null || valueWrapper.isNull()) {
                            continue;
                        }
                        if (valueWrapper.isMap()) {
                            Map<String, ValueWrapper> rMap = valueWrapper.asMap();
                            String id = (String) nebulaClient.wrapperValue(rMap.get("objectId"));
                            Map<String, Object> value = new HashMap<>();
                            for (Map.Entry entry : rMap.entrySet()) {
                                value.put((String) entry.getKey(), nebulaClient.wrapperValue((ValueWrapper) entry.getValue()));
                            }
                            tags.put(id, value);
                        } else {
                            Relationship edge = valueWrapper.asRelationship();
                            Map<String, Object> value = new HashMap<>();
                            Map<String, Object> properties = new HashMap<>();
                            for (Map.Entry entry : edge.properties().entrySet()) {
                                properties.put((String) entry.getKey(), nebulaClient.wrapperValue((ValueWrapper) entry.getValue()));
                            }
                            value.put("target", nebulaClient.wrapperValue(edge.dstId()));
                            value.put("source", nebulaClient.wrapperValue(edge.srcId()));
                            value.put("rank", edge.ranking());
                            value.put("edgeName", edge.edgeName());
                            value.put("properties", properties);
                            edges.add(value);
                        }
                    }
                }
            } else {
                throw new RuntimeException("查询nebula失败：" + resultSet.getErrorMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public NebulaClient createNebulaClient(DataServiceSetting dataServiceSetting) {
        DsDatasource nebularDs = dataServiceSetting.getDsDatasourceMap().get("nebular");
        NebularConnectionSetting nebularConnection = (NebularConnectionSetting) nebularDs.getConnectionSetting();
        return new NebulaClient(
                nebularConnection.getAddress(),
                nebularConnection.getUsername(),
                nebularConnection.getPassword()
        );
    }

    public static void main(String[] args) {
//        Date date = DateUtil.parse("a20240618123925", "yyyyMMddHHmmss");
//        String args1 = "tzb_cmdb_20240618123925";
//        // 截取最后14位
//        String partition = args1.substring(args1.length() - 14);
//        String partition2 = args1.substring(0, args1.length() - 15);
//        System.out.println(partition);
//        System.out.println(partition2);
//
//        System.out.println("20240618123925".substring(0, 8));

        // 调试时注释掉获取系统配置的步骤
//        String baseSpace = "tzb_cmdb";
//        String imageSpace = "";
//        if (nebulaClient == null) {
//            nebulaClient = createNebulaClient(dataServiceSetting);
//        }


        StdObjectInstanceRelationByGoServiceImpl stdObjectInstanceRelationService = new StdObjectInstanceRelationByGoServiceImpl();
        DataServiceSetting dataServiceSetting = new DataServiceSetting();
        Map<String, DsDatasource> dsDatasourceMap = new HashMap<>();

        DsDatasource dsDatasource = new DsDatasource();
        NebularConnectionSetting nebularConnectionSetting = new NebularConnectionSetting();
        nebularConnectionSetting.setAddress("**************:9669");
        nebularConnectionSetting.setMetaAddress("**************:9559");
        nebularConnectionSetting.setUsername("root");
        nebularConnectionSetting.setPassword("123");
        nebularConnectionSetting.setMaxConnSize(777);

        dsDatasource.setConnectionSetting(nebularConnectionSetting);
        dsDatasourceMap.put("nebular", dsDatasource);
        dataServiceSetting.setDsDatasourceMap(dsDatasourceMap);

        DataServiceRequest request = new DataServiceRequest();
        Map<String, Object> param = new HashMap<>();
        param.put("useDefaultCfg", true);

        param.put("objInstanceId", "8u3hkl5m9xf2");
        param.put("countNextNode", true);
        param.put("queryTowDirection", true);
        param.put("namespace", "tzb_cmdb_20240912155035");
//        param.put("namespace", "20240703123926");
//        param.put("objTypes", Arrays.asList("kafka", "os_linux"));
        param.put("pathLength", 1);

        Map<String, Object> properties = new HashMap<>();
//        properties.put("status", "enabled");
//        properties.put("backupDate", "2024-02-21");

        param.put("properties", properties);
        request.setParam(param);
        dataServiceSetting.setDataServiceRequest(request);
        DataServiceResponseData responseData = stdObjectInstanceRelationService.execute(dataServiceSetting);

        JaxLogUtil.getDataServiceLogger().info(JSONUtil.toJsonPrettyStr(responseData));

    }
}
