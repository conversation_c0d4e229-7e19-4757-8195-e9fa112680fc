package com.eoi.jax.api.metricasset.model.nebula;

/**
 * <AUTHOR> zsc
 * @create 2023/11/16 13:24
 */

public class StdObjectInstanceRelationByGoQueryReq {

    private Boolean useDefaultCfg;
    private String namespace;

    private String objInstanceId;

    private Integer pathLength;
    private Integer maxEdge;

    private Boolean countNextNode;

    private Boolean queryTowDirection;


    //$var1 = GO 1 TO 5 STEPS FROM '8u3hkl5m9xf3' OVER *  yield  DISTINCT properties($^) AS src, properties($$) AS dst, edge AS e ; \
    //yield distinct $var1.src as s
    //union
    //yield distinct $var1.dst as s
    //union
    //yield distinct $var1.e as s
    //
    public String buildOriMatchSql() {
        StringBuilder sqlBuilder = new StringBuilder("USE ").append(namespace).append("; ");
        sqlBuilder.append("$va = GO 1 TO ").append(pathLength).append(" STEPS FROM '").append(objInstanceId).append("' ");
        sqlBuilder.append(" OVER * ");
        if (queryTowDirection != null && queryTowDirection) {
            sqlBuilder.append(" BIDIRECT ");
        }
        sqlBuilder.append(" YIELD  DISTINCT properties($^) AS src, properties($$) AS dst, edge AS e | LIMIT ")
                .append(maxEdge == null || maxEdge <= 0 ? 50000 : maxEdge).append(";  ");
        sqlBuilder.append(" YIELD  DISTINCT $va.src as s UNION YIELD  DISTINCT $va.dst as s UNION YIELD  DISTINCT $va.e as s;");
        return sqlBuilder.toString();
    }

    public String buildCountNextMatchSql() {
        StringBuilder sqlBuilder = new StringBuilder("USE ").append(namespace).append("; ");
        sqlBuilder.append("$va = GO 1 TO ").append(countNextNode != null && countNextNode ? pathLength + 1 : pathLength)
                .append(" STEPS FROM '").append(objInstanceId).append("' ");
        sqlBuilder.append(" OVER * ");
        sqlBuilder.append(" YIELD  DISTINCT properties($^) AS src, properties($$) AS dst, edge AS e | LIMIT ")
                .append(maxEdge == null || maxEdge <= 0 ? 50000 : maxEdge).append(";  ");
        sqlBuilder.append(" YIELD  DISTINCT $va.src as s UNION YIELD  DISTINCT $va.dst as s UNION YIELD  DISTINCT $va.e as s;");
        return sqlBuilder.toString();
    }

    public Integer getMaxEdge() {
        return maxEdge;
    }

    public void setMaxEdge(Integer maxEdge) {
        this.maxEdge = maxEdge;
    }

    public Boolean getUseDefaultCfg() {
        return useDefaultCfg;
    }

    public void setUseDefaultCfg(Boolean useDefaultCfg) {
        this.useDefaultCfg = useDefaultCfg;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getObjInstanceId() {
        return objInstanceId;
    }

    public void setObjInstanceId(String objInstanceId) {
        this.objInstanceId = objInstanceId;
    }

    public Boolean getCountNextNode() {
        return countNextNode;
    }

    public Boolean getQueryTowDirection() {
        return queryTowDirection;
    }

    public void setQueryTowDirection(Boolean queryTowDirection) {
        this.queryTowDirection = queryTowDirection;
    }

    public void setCountNextNode(Boolean countNextNode) {
        this.countNextNode = countNextNode;
    }

    public Integer getPathLength() {
        return pathLength;
    }

    public void setPathLength(Integer pathLength) {
        this.pathLength = pathLength;
    }
}
