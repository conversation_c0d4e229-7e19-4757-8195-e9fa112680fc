package com.eoi.jax.api.metricasset.model.nebula;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2024/7/23 17:16
 */
public class Link {

    public static class LinkTagNode {
        private String nodeId;
        private String nodeType;
        private List<LinkTag> tags;

        public String getNodeId() {
            return nodeId;
        }

        public void setNodeId(String nodeId) {
            this.nodeId = nodeId;
        }

        public String getNodeType() {
            return nodeType;
        }

        public void setNodeType(String nodeType) {
            this.nodeType = nodeType;
        }

        public List<LinkTag> getTags() {
            return tags;
        }

        public void setTags(List<LinkTag> tags) {
            this.tags = tags;
        }
    }

    public static class LinkEdgeNode {
        private String sourceId;
        private String targetId;
        private String nodeType;
        private List<LinkRelation> relations;

        public String getSourceId() {
            return sourceId;
        }

        public void setSourceId(String sourceId) {
            this.sourceId = sourceId;
        }

        public String getTargetId() {
            return targetId;
        }

        public void setTargetId(String targetId) {
            this.targetId = targetId;
        }

        public String getNodeType() {
            return nodeType;
        }

        public void setNodeType(String nodeType) {
            this.nodeType = nodeType;
        }

        public List<LinkRelation> getRelations() {
            return relations;
        }

        public void setRelations(List<LinkRelation> relations) {
            this.relations = relations;
        }
    }

    public static class LinkTag {
        private String objectType;
        private String tbName;

        public LinkTag() {
        }

        public LinkTag(String objectType, String tbName) {
            this.objectType = objectType;
            this.tbName = tbName;
        }

        public String getObjectType() {
            return objectType;
        }

        public void setObjectType(String objectType) {
            this.objectType = objectType;
        }

        public String getTbName() {
            return tbName;
        }

        public void setTbName(String tbName) {
            this.tbName = tbName;
        }
    }

    public static class LinkRelation {
        private String relationType;

        public LinkRelation(String relationType) {
            this.relationType = relationType;
        }

        public String getRelationType() {
            return relationType;
        }

        public void setRelationType(String relationType) {
            this.relationType = relationType;
        }
    }

    public static void main(String[] args) {

    }


}
