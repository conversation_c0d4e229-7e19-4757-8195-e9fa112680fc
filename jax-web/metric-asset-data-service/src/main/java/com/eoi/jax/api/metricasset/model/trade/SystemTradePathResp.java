package com.eoi.jax.api.metricasset.model.trade;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/11/10 9:57
 */
public class SystemTradePathResp {

    private List<Node> nodes = new ArrayList<>();
    private List<List<Edge>> edges = new ArrayList<>();

    public List<Node> getNodes() {
        return nodes;
    }

    public void setNodes(List<Node> nodes) {
        this.nodes = nodes;
    }

    public List<List<Edge>> getEdges() {
        return edges;
    }

    public void setEdges(List<List<Edge>> edges) {
        this.edges = edges;
    }

//  "  p.trans_link_name as transLinkName,\n" +
    //                "  p.`status` as linkStatus,\n" +
    //                "  e.trans_link_id AS transLinkId,\n" +
    //                "  e.from_node_id AS fromNodeId,\n" +
    //                "  e.to_node_id AS toNodeId,\n" +
    //                "  e.graph_data AS graphData,\n" +
    //                "  fn.trans_code AS fnTransCode,\n" +
    //                "  fn.service_id AS fnServiceId,\n" +
    //                "  fn.root_node AS fnRootNode,\n" +
    //                "  fn.system_id1 AS fnSystemId1,\n" +
    //                "  fn.system_id2 AS fnSystemId2,\n" +
    //                "  fn.system_id3 AS fnSystemId3,\n" +
    //                "  fn.trans_name AS fnTransName,\n" +
    //                "  fn.indicator_calc_node AS fnIndicatorCalcNode,\n" +
    //                "  tn.trans_code AS tnTransCode,\n" +
    //                "  tn.service_id AS tnServiceId,\n" +
    //                "  tn.root_node AS tnRootNode,\n" +
    //                "  tn.system_id1 AS tnSystemId1,\n" +
    //                "  tn.system_id2 AS tnSystemId2,\n" +
    //                "  tn.system_id3 AS tnSystemId3,\n" +
    //                "  tn.trans_name AS tnTransName,\n" +
    //                "  tn.indicator_calc_node AS tnIndicatorCalcNode\n" +

    public static class Node {
        private Long nodeId;
        private Long systemId;
        private String systemName;
        private String systemCode;
        private Long parentSystemId;
        private String transCode;
        private String transName;
        private Long serviceId;
        private String serviceName;
        private String serviceCode;

        private Integer rootNode;

        private Long count;
        private Long successCount;
        private Double successRate;
        private Double avgDurationTime;
        private Long durationTime;

        public Double getAvgDurationTime() {
            return avgDurationTime;
        }

        public void setAvgDurationTime(Double avgDurationTime) {
            this.avgDurationTime = avgDurationTime;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }

        public Long getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(Long successCount) {
            this.successCount = successCount;
        }

        public Double getSuccessRate() {
            return successRate;
        }

        public void setSuccessRate(Double successRate) {
            this.successRate = successRate;
        }

        public Long getDurationTime() {
            return durationTime;
        }

        public void setDurationTime(Long durationTime) {
            this.durationTime = durationTime;
        }

        public Long getSystemId() {
            return systemId;
        }

        public void setSystemId(Long systemId) {
            this.systemId = systemId;
        }

        public String getSystemName() {
            return systemName;
        }

        public void setSystemName(String systemName) {
            this.systemName = systemName;
        }

        public String getSystemCode() {
            return systemCode;
        }

        public void setSystemCode(String systemCode) {
            this.systemCode = systemCode;
        }

        public Long getParentSystemId() {
            return parentSystemId;
        }

        public void setParentSystemId(Long parentSystemId) {
            this.parentSystemId = parentSystemId;
        }

        public String getTransCode() {
            return transCode;
        }

        public void setTransCode(String transCode) {
            this.transCode = transCode;
        }

        public String getTransName() {
            return transName;
        }

        public void setTransName(String transName) {
            this.transName = transName;
        }

        public Long getNodeId() {
            return nodeId;
        }

        public void setNodeId(Long nodeId) {
            this.nodeId = nodeId;
        }

        public Long getServiceId() {
            return serviceId;
        }

        public void setServiceId(Long serviceId) {
            this.serviceId = serviceId;
        }

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public String getServiceCode() {
            return serviceCode;
        }

        public void setServiceCode(String serviceCode) {
            this.serviceCode = serviceCode;
        }

        public Integer getRootNode() {
            return rootNode;
        }

        public void setRootNode(Integer rootNode) {
            this.rootNode = rootNode;
        }
    }

    public static class Edge {
        private String transLinkName;
        private Long transLinkId;
        private Long source;
        private Long target;

        public String getTransLinkName() {
            return transLinkName;
        }

        public void setTransLinkName(String transLinkName) {
            this.transLinkName = transLinkName;
        }

        public Long getTransLinkId() {
            return transLinkId;
        }

        public void setTransLinkId(Long transLinkId) {
            this.transLinkId = transLinkId;
        }

        public Long getSource() {
            return source;
        }

        public void setSource(Long source) {
            this.source = source;
        }

        public Long getTarget() {
            return target;
        }

        public void setTarget(Long target) {
            this.target = target;
        }
    }


}
