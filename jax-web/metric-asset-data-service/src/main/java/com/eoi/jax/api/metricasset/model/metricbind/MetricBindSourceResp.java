package com.eoi.jax.api.metricasset.model.metricbind;


import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.repository.entity.TbMetricBindSource;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class MetricBindSourceResp implements IRespModel<TbMetricBindSource> {

    @Schema(description = "id")
    private Long id;

    /**
     * 指标项id
     */
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "指标项id")
    private Long metricId;

    /**
     * 指标来源id
     */
    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "指标来源id")
    private Long metricSourceId;

    /**
     * 指标匹配关键字
     */
    @Schema(description = "指标匹配关键字")
    private String bindKeyword;

    /**
     * 维度提取的规则JSON
     */
    @Schema(description = "维度提取的规则")
    private List<DimensionRuleExtractionReq> dimensionRule;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    @Schema(description = "维度匹配状态,匹配的:MATCHED 需要删除:NEED_DELETE 需要新增:NEED_ADD")
    private String status;

    @Schema(description = "指标来源")
    private MetricSourceResp metricSourceResp;

    /**
     * response from entity
     *
     * @param tbMetricBindSource
     * @return
     */
    @Override
    public MetricBindSourceResp fromEntity(TbMetricBindSource tbMetricBindSource) {
        MetricBindSourceResp resp = IRespModel.super.fromEntity(tbMetricBindSource);
        if (StrUtil.isNotBlank(tbMetricBindSource.getDimensionRule())) {
            resp.setDimensionRule(JsonUtil.decode(tbMetricBindSource.getDimensionRule(),
                    new TypeReference<List<DimensionRuleExtractionReq>>() {
                    }));
        }
        return resp;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getMetricId() {
        return metricId;
    }

    public void setMetricId(Long metricId) {
        this.metricId = metricId;
    }

    public Long getMetricSourceId() {
        return metricSourceId;
    }

    public void setMetricSourceId(Long metricSourceId) {
        this.metricSourceId = metricSourceId;
    }

    public String getBindKeyword() {
        return bindKeyword;
    }

    public void setBindKeyword(String bindKeyword) {
        this.bindKeyword = bindKeyword;
    }

    public List<DimensionRuleExtractionReq> getDimensionRule() {
        return dimensionRule;
    }

    public void setDimensionRule(List<DimensionRuleExtractionReq> dimensionRule) {
        this.dimensionRule = dimensionRule;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public MetricSourceResp getMetricSourceResp() {
        return metricSourceResp;
    }

    public void setMetricSourceResp(MetricSourceResp metricSourceResp) {
        this.metricSourceResp = metricSourceResp;
    }
}
