package com.eoi.jax.ai.service;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/6/5
 */
public class DragonflyAiReq {
    @JsonProperty("clickhouse_conn")
    private CkConn clickhouseConn;
    @JsonProperty("alg_param")
    private Map<String, Object> algParam;

    public CkConn getClickhouseConn() {
        return clickhouseConn;
    }

    public void setClickhouseConn(CkConn clickhouseConn) {
        this.clickhouseConn = clickhouseConn;
    }

    public Map<String, Object> getAlgParam() {
        return algParam;
    }

    public void setAlgParam(Map<String, Object> algParam) {
        this.algParam = algParam;
    }

    public static final class CkConn {
        private String host;
        private Integer port;
        private String username;
        private String password;
        private String database;
        private String query;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getDatabase() {
            return database;
        }

        public void setDatabase(String database) {
            this.database = database;
        }

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }
    }

    public static final class AlgParam {
        @JsonProperty("dimension_list")
        private String dimensionList;
        @JsonProperty("time_field")
        private String timeField;
        @JsonProperty("value_field")
        private String valueField;
        @JsonProperty("count_mode")
        private String countMode;
        @JsonProperty("duration_field")
        private String durationField;
        @JsonProperty("success_field")
        private String successField;
        @JsonProperty("fail_value")
        private String failValue;
        @JsonProperty("anomaly_start_time")
        private String anomalyStartTime;
        @JsonProperty("anomaly_end_time")
        private String anomalyEndTime;
        @JsonProperty("normaly_start_time")
        private String normalyStartTime;
        @JsonProperty("normaly_end_time")
        private String normalyEndTime;

        @JsonProperty("ratio_threshold")
        private String ratioThreshold;
        @JsonProperty("entropy_weight")
        private String entropyWeight;
        @JsonProperty("influence_threshold")
        private String influenceThreshold;
        @JsonProperty("max_layer")
        private String maxLayer;
        @JsonProperty("top_k")
        private String topK;

        public String getDimensionList() {
            return dimensionList;
        }

        public void setDimensionList(String dimensionList) {
            this.dimensionList = dimensionList;
        }

        public String getTimeField() {
            return timeField;
        }

        public void setTimeField(String timeField) {
            this.timeField = timeField;
        }

        public String getValueField() {
            return valueField;
        }

        public void setValueField(String valueField) {
            this.valueField = valueField;
        }

        public String getCountMode() {
            return countMode;
        }

        public void setCountMode(String countMode) {
            this.countMode = countMode;
        }

        public String getDurationField() {
            return durationField;
        }

        public void setDurationField(String durationField) {
            this.durationField = durationField;
        }

        public String getSuccessField() {
            return successField;
        }

        public void setSuccessField(String successField) {
            this.successField = successField;
        }

        public String getFailValue() {
            return failValue;
        }

        public void setFailValue(String failValue) {
            this.failValue = failValue;
        }

        public String getAnomalyStartTime() {
            return anomalyStartTime;
        }

        public void setAnomalyStartTime(String anomalyStartTime) {
            this.anomalyStartTime = anomalyStartTime;
        }

        public String getAnomalyEndTime() {
            return anomalyEndTime;
        }

        public void setAnomalyEndTime(String anomalyEndTime) {
            this.anomalyEndTime = anomalyEndTime;
        }

        public String getNormalyStartTime() {
            return normalyStartTime;
        }

        public void setNormalyStartTime(String normalyStartTime) {
            this.normalyStartTime = normalyStartTime;
        }

        public String getNormalyEndTime() {
            return normalyEndTime;
        }

        public void setNormalyEndTime(String normalyEndTime) {
            this.normalyEndTime = normalyEndTime;
        }

        public String getRatioThreshold() {
            return ratioThreshold;
        }

        public void setRatioThreshold(String ratioThreshold) {
            this.ratioThreshold = ratioThreshold;
        }

        public String getEntropyWeight() {
            return entropyWeight;
        }

        public void setEntropyWeight(String entropyWeight) {
            this.entropyWeight = entropyWeight;
        }

        public String getInfluenceThreshold() {
            return influenceThreshold;
        }

        public void setInfluenceThreshold(String influenceThreshold) {
            this.influenceThreshold = influenceThreshold;
        }

        public String getMaxLayer() {
            return maxLayer;
        }

        public void setMaxLayer(String maxLayer) {
            this.maxLayer = maxLayer;
        }

        public String getTopK() {
            return topK;
        }

        public void setTopK(String topK) {
            this.topK = topK;
        }
    }
}
