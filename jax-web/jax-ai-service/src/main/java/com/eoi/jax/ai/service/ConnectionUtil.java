package com.eoi.jax.ai.service;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.CkManConnectionSetting;
import com.eoi.jax.api.dataservice.model.ds.conn.JdbcConnectionSetting;
import com.eoi.jax.api.dataservice.model.ds.conn.NebularConnectionSetting;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.TableDeployPlatformEnum;
import com.eoi.jax.web.core.common.enumrate.TableDeployStatusEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.data.service.service.DatasourceHelperService;
import com.eoi.jax.web.ingestion.enumrate.DatasourcePlatformEnum;
import com.eoi.jax.web.repository.entity.TbTable;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.search.query.TableDeployCheckExistParam;
import com.eoi.jax.web.repository.service.TbTableDeployService;
import com.eoi.jax.web.repository.service.TbTableService;

import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.eoi.jax.api.dataservice.util.ConnectionUtil.MysqlDatabase.MYSQL_PATTERN;

/**
 * <AUTHOR>
 * @Date 2024/6/14
 */
public class ConnectionUtil {
    public static ConnInfo getTbConnInfo(String tableName) {
        TbTableService tbTableService = ContextHolder.getBean(TbTableService.class);
        List<TbTable> tbTableList = tbTableService.list(
                new LambdaQueryWrapper<TbTable>().eq(TbTable::getTbName, tableName));
        if (tbTableList == null || tbTableList.isEmpty()) {
            throw new BizException(ResponseCode.FAILED.getCode(), "表不存在：tbName=" + tableName);
        }
        TbTable tbTable = tbTableList.get(0);
        TbTableDeployService tbTableDeployService = ContextHolder.getBean(TbTableDeployService.class);
        TableDeployCheckExistParam tableDeployParam = new TableDeployCheckExistParam();
        tableDeployParam.setTableId(tbTable.getId());
        tableDeployParam.setInStatus(Arrays.asList(TableDeployStatusEnum.SUCCESS.code()));
        TbTableDeploy tableDeploy = tbTableDeployService.checkIfExistAndReturn(tableDeployParam);
        if (tableDeploy == null) {
            throw new BizException(ResponseCode.FAILED.getCode(), "表未发布：tbName=" + tableName);
        }
        ConnInfo connInfo = getDsConnInfo(tableDeploy.getDsId());
        connInfo.setTable(tableDeploy.getTbName());
        if (TableDeployPlatformEnum.CLICKHOUSE.equals(tableDeploy.getPlatform())) {
            Map<String, Object> tableDeploySetting = JsonUtil.decode2Map(tableDeploy.getSetting());
            String database = (String) tableDeploySetting.get("database");
            connInfo.setDatabase(database);
        }
        return connInfo;
    }

    public static ConnInfo getDsConnInfo(Long dsId) {
        DatasourceHelperService datasourceHelperService = ContextHolder.getBean(DatasourceHelperService.class);
        DsDatasource dsDatasource = datasourceHelperService.getDsDatasource(dsId);
        ConnInfo connInfo = new ConnInfo();
        connInfo.setDbType(dsDatasource.getPlatform());
        if (DatasourcePlatformEnum.CLICKHOUSE.equals(dsDatasource.getPlatform())) {
            CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) dsDatasource.getConnectionSetting();
            connInfo.setUsername(ckManConnectionSetting.getCkUsername());
            connInfo.setPassword(ckManConnectionSetting.getCkPassword());
            analyzeJdbcUrlHostPort(ckManConnectionSetting.getCkJdbcUrl(), connInfo);
        } else if (DatasourcePlatformEnum.MYSQL.equals(dsDatasource.getPlatform())) {
            JdbcConnectionSetting connectionSetting = (JdbcConnectionSetting) dsDatasource.getConnectionSetting();
            connInfo.setUsername(connectionSetting.getUsername());
            connInfo.setPassword(connectionSetting.getPassword());
            analyzeJdbcUrlHostPort(connectionSetting.getAddress(), connInfo);
            List<String> matchGroups = ReUtil.getAllGroups(MYSQL_PATTERN, connectionSetting.getAddress());
            if (matchGroups.size() > 2 && StrUtil.isNotEmpty(matchGroups.get(2))) {
                connInfo.setDatabase(matchGroups.get(2));
            }
        } else if (DatasourcePlatformEnum.DAMENG.equals(dsDatasource.getPlatform())) {
            JdbcConnectionSetting connectionSetting = (JdbcConnectionSetting) dsDatasource.getConnectionSetting();
            connInfo.setUsername(connectionSetting.getUsername());
            connInfo.setPassword(connectionSetting.getPassword());
            analyzeJdbcUrlHostPort(connectionSetting.getAddress(), connInfo);
        } else {
            throw new BizException(ResponseCode.FAILED.getCode(), "只支持CLICKHOUSE、MYSQL和DAMENG数据源,当前数据源为" + dsDatasource.getPlatform());
        }
        return connInfo;
    }

    public static NebulaInfo getNebulaInfo(NebularConnectionSetting setting) {
        String[] addressArr = setting.getAddress().split(",");
        String address = addressArr[0];
        String host = address.split(":")[0];
        Integer port = Integer.parseInt(address.split(":")[1]);
        NebulaInfo nebulaInfo = new NebulaInfo();
        nebulaInfo.setHost(host);
        nebulaInfo.setPort(port);
        nebulaInfo.setUsername(setting.getUsername());
        nebulaInfo.setPassword(setting.getPassword());
        return nebulaInfo;
    }

    private static void analyzeJdbcUrlHostPort(String jdbcUrl, ConnInfo connInfo) {
        try {
            URI uri = new URI(jdbcUrl.substring(5));
            connInfo.setHost(uri.getHost());
            connInfo.setPort(uri.getPort());
        } catch (Exception e) {
            throw new BizException(ResponseCode.FAILED, "jdbc url解析失败" + jdbcUrl);
        }
    }
}
