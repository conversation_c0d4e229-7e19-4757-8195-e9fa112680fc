package com.eoi.jax.ai.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;
import com.eoi.jax.api.dataservice.model.ds.conn.NebularConnectionSetting;
import com.eoi.jax.api.dataservice.util.JaxLogUtil;
import com.eoi.jax.api.dataservice.v2.annotation.DataServiceHandler;
import com.eoi.jax.api.dataservice.v2.annotation.DatasourceParam;
import com.eoi.jax.api.dataservice.v2.request.DataServiceRequest;
import com.eoi.jax.api.dataservice.v2.response.DsResponseData;
import com.eoi.jax.api.dataservice.v2.service.IDataServiceV2;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.service.*;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/5/31
 */
@DataServiceHandler(
        name = "TopoRca",
        description = "告警根因分析",
        accessJaxMeta = true
)
@SuppressWarnings("all")
public class TopoRca implements IDataServiceV2<TopoRcaReq, TopoRcaResp> {
    private static final Logger LOG = LoggerFactory.getLogger(TopoRca.class);

    @DatasourceParam(display = "Nebular数据源", type = DsDatasourceTypeEnum.NEBULA)
    private NebularConnectionSetting nebularConnectionSetting;

    @NotNull
    private static List<List<Object>> getLinks(List<String> relationNames) {
        List<List<Object>> links = new ArrayList<>();
        // 查询path
        TbObjectRelationPathService tbObjectRelationPathService = ContextHolder.getBean(TbObjectRelationPathService.class);
        TbObjectTableService tbObjectTableService = ContextHolder.getBean(TbObjectTableService.class);
        TbObjectRelationTypeService tbObjectRelationTypeService = ContextHolder.getBean(TbObjectRelationTypeService.class);
        List<TbObjectRelationPath> tbObjectRelationPaths =
                tbObjectRelationPathService.list(
                        new LambdaQueryWrapper<TbObjectRelationPath>()
                                .in(TbObjectRelationPath::getQueryName, relationNames)
                                .eq(TbObjectRelationPath::getStatus, 1)
                );

        List<TbObjectTable> tbObjectTables = tbObjectTableService.list();
        Map<Long, TbObjectTable> objectTableMap = tbObjectTables.stream().collect(Collectors.toMap(TbObjectTable::getId, v -> v));

        List<TbObjectRelationType> relationTypes = tbObjectRelationTypeService.list();
        Map<Long, TbObjectRelationType> relationTypeMap = relationTypes.stream()
                .collect(Collectors.toMap(TbObjectRelationType::getId, v -> v));

        for (TbObjectRelationPath path : tbObjectRelationPaths) {
            List<ObjectPathRule> objectPathRules = JsonUtil.decode(path.getQueryPathRule(),
                    new TypeReference<List<ObjectPathRule>>() {
                    });
            List<ObjectPathRule> tags = new ArrayList<>();
            List<ObjectPathRule> edges = new ArrayList<>();

            ObjectPathRule beginTag = null;
            for (int i = 0, len = objectPathRules.size(); i < len; i++) {
                ObjectPathRule objectPathRule = objectPathRules.get(i);
                if (objectPathRule.getNodeType().equals(ObjectPathRule.TAG)) {
                    tags.add(objectPathRule);
                    if (StringUtils.isBlank(objectPathRule.getParentNodeId())) {
                        beginTag = objectPathRule;
                    }
                }
                if (objectPathRule.getNodeType().equals(ObjectPathRule.EDGE)) {
                    edges.add(objectPathRule);
                }
            }

            List<ObjectPathRule> sortedPaths = new ArrayList<>();
            sortedPaths.add(beginTag);
            for (int i = 0, len = edges.size(); i < len; i++) {
                for (ObjectPathRule edge : edges) {
                    String eSourceId = edge.getSourceId();
                    String eTargetId = edge.getTargetId();
                    if (eSourceId.equals(beginTag.getNodeId())) {
                        sortedPaths.add(edge);
                        for (ObjectPathRule tag : tags) {
                            if (eTargetId.equals(tag.getNodeId())) {
                                beginTag = tag;
                                sortedPaths.add(tag);
                                break;
                            }
                        }
                        break;
                    }
                }
            }

            List<Object> link = new ArrayList<>();
            for (ObjectPathRule p : sortedPaths) {
                if (ObjectPathRule.TAG.equals(p.getNodeType())) {
                    TopoRcaAiReq.LinkTagNode node = new TopoRcaAiReq.LinkTagNode();
                    link.add(node);
                    node.setNodeId(p.getNodeId());
                    node.setNodeType(p.getNodeType());
                    List<TopoRcaAiReq.LinkTag> linkTags = new ArrayList<>();
                    node.setTags(linkTags);
                    for (ObjectPathRule.ObjectTag t : p.getTags()) {
                        if (t.getObjectType().equals(ObjectPathRule.ObjectTag.OBJECT)) {
                            TbObjectTable tbObjectTable = objectTableMap.get(t.getObjectId());
                            linkTags.add(new TopoRcaAiReq.LinkTag(tbObjectTable.getTypeCode(), tbObjectTable.getTbName()));
                        }
                        if (t.getObjectType().equals(ObjectPathRule.ObjectTag.CATEGORY)) {
                            tbObjectTables.stream().filter(x ->
                                    x.getCategoryPath().contains(t.getObjectId().toString())
                            ).forEach(tbObjectTable -> {
                                linkTags.add(new TopoRcaAiReq.LinkTag(tbObjectTable.getTypeCode(), tbObjectTable.getTbName()));
                            });
                        }
                    }
                }
                if (ObjectPathRule.EDGE.equals(p.getNodeType())) {
                    TopoRcaAiReq.LinkEdgeNode node = new TopoRcaAiReq.LinkEdgeNode();
                    link.add(node);
                    node.setSourceId(p.getSourceId());
                    node.setTargetId(p.getTargetId());
                    node.setNodeType(p.getNodeType());
                    List<TopoRcaAiReq.LinkRelation> relationList = new ArrayList<>();
                    node.setRelations(relationList);
                    for (int i = 0, len = p.getRelations().size(); i < len; i++) {
                        ObjectPathRule.ObjectEdge relation = p.getRelations().get(i);
                        TbObjectRelationType relationType = relationTypeMap.get(relation.getRelationId());
                        relationList.add(new TopoRcaAiReq.LinkRelation(relationType.getCode()));
                    }
                }
            }

            links.add(link);
        }
        return links;
    }

    @Override
    public DsResponseData<TopoRcaResp> execute(DataServiceRequest<TopoRcaReq> request) {
//        UnifiedLicenseProvider provider = new UnifiedLicenseProvider();
//        if (!provider.has("algtoprca")) {
//            throw new BizException(ResponseCode.FAILED.getCode(), "License校验失败");
//        }
        AiServiceClient client = new AiServiceClient();
        TopoRcaAiReq aiReq = getReq(request);
        List<TopoRcaAiResp> aiResp = client.call(aiReq);
        return getResp(aiResp);
    }

    public TopoRcaAiReq getReq(DataServiceRequest<TopoRcaReq> request) {
        LOG.info("request:{}", JsonUtil.encode(request.getParam()));
        TopoRcaAiReq aiReq = new TopoRcaAiReq();
        aiReq.setAlarmSource(new TopoRcaAiReq.AlarmSource());
        aiReq.setTopoSource(new TopoRcaAiReq.TopoSource());
        aiReq.setAlgParam(new TopoRcaAiReq.AlgParam());
        aiReq.setExtendParam(request.getParam().getExtendParam());
        aiReq.getTopoSource().setNebulaConn(new TopoRcaAiReq.NebulaConn());
        String tbName = request.getParam().getTbName();
        ConnInfo connInfo = ConnectionUtil.getTbConnInfo(tbName);
        String sql = "";
        String startTime = "";
        String endTime = "";
        String fromTable = StrUtil.isBlank(connInfo.getDatabase()) ? tbName : connInfo.getDatabase() + "." + tbName;
        String alarmTimeFieldAs = "";
        if (TopoRcaReq.TIME_FORMAT_NUM_MS.equals(request.getParam().getAlarmTimeFieldType())) {
            startTime = DateUtil.parseDateTime(request.getParam().getAlarmStartTime()).getTime() + "";
            endTime = DateUtil.parseDateTime(request.getParam().getAlarmEndTime()).getTime() + "";
            alarmTimeFieldAs = request.getParam().getAlarmTimeField() + "_format_date";
            sql = String.format("select FROM_UNIXTIME(%s/1000) AS %s, tmp.* from %s as tmp where %s >= %s and %s <= %s ",
                    request.getParam().getAlarmTimeField(),
                    alarmTimeFieldAs,
                    fromTable,
                    request.getParam().getAlarmTimeField(),
                    startTime,
                    request.getParam().getAlarmTimeField(),
                    endTime
            );
        } else if (TopoRcaReq.TIME_FORMAT_NUM_S.equals(request.getParam().getAlarmTimeFieldType())) {
            startTime = DateUtil.parseDateTime(request.getParam().getAlarmStartTime()).getTime() / 1000 + "";
            endTime = DateUtil.parseDateTime(request.getParam().getAlarmEndTime()).getTime() / 1000 + "";
            alarmTimeFieldAs = request.getParam().getAlarmTimeField() + "_format_date";
            sql = String.format("select FROM_UNIXTIME(%s) AS %s, tmp.* from %s as tmp where %s >= %s and %s <= %s ",
                    request.getParam().getAlarmTimeField(),
                    alarmTimeFieldAs,
                    fromTable,
                    request.getParam().getAlarmTimeField(),
                    startTime,
                    request.getParam().getAlarmTimeField(),
                    endTime
            );
        } else {
            startTime = "'" + request.getParam().getAlarmStartTime() + "'";
            endTime = "'" + request.getParam().getAlarmEndTime() + "'";
            sql = String.format("select * from %s where %s >= %s and %s <= %s ",
                    fromTable,
                    request.getParam().getAlarmTimeField(),
                    startTime,
                    request.getParam().getAlarmTimeField(),
                    endTime
            );
        }
        if (CollUtil.isNotEmpty(request.getParam().getAlarmLevels())
                && StrUtil.isNotEmpty(request.getParam().getAlarmLevelField())) {
            sql = sql + " and " + request.getParam().getAlarmLevelField()
                    + " in ('" + StrUtil.join("','", request.getParam().getAlarmLevels()) + "')";
        }

        if (CollUtil.isNotEmpty(request.getParam().getAlarmBusinesses())
                && StrUtil.isNotEmpty(request.getParam().getAlarmBusinessField())) {
            sql = sql + " and " + request.getParam().getAlarmBusinessField()
                    + " in ('" + StrUtil.join("','", request.getParam().getAlarmBusinesses()) + "')";
        }

        aiReq.getAlarmSource().setCommonDbConn(new TopoRcaAiReq.CommonDbConn());
        copy(connInfo, aiReq.getAlarmSource().getCommonDbConn());
        aiReq.getAlarmSource().getCommonDbConn().setQuery(sql);
        aiReq.getAlarmSource().setAlarmFieldsMap(request.getParam().getAlarmFieldsMap() == null ? new ArrayList<>()
                : request.getParam().getAlarmFieldsMap());
        if (StrUtil.isNotEmpty(alarmTimeFieldAs)) {
            for (TopoRcaReq.AlarmFields alarmField : aiReq.getAlarmSource().getAlarmFieldsMap()) {
                if (alarmField.getFrom().equals(request.getParam().getAlarmTimeField())) {
                    alarmField.setFrom(alarmTimeFieldAs);
                }
            }
        }
        // 如果nebula space为空取系统配置
        if (StringUtils.isBlank(request.getParam().getNebularSpace())) {
            String imageSpace = NebulaConfig.getCurrentNamespace();
            aiReq.getTopoSource().getNebulaConn().setNamespace(imageSpace);
        } else {
            aiReq.getTopoSource().getNebulaConn().setNamespace(request.getParam().getNebularSpace());
        }

        NebulaInfo nebulaInfo = ConnectionUtil.getNebulaInfo(nebularConnectionSetting);
        aiReq.getTopoSource().setCiClassifyApp(request.getParam().getCiClassifyApp());
        aiReq.getTopoSource().setBypass(request.getParam().getBypass() == null ? new ArrayList<>() : request.getParam().getBypass());

        // link已生成，明天改服务参数为普通参数，然后对比接口文档参数，发布测试
        if (request.getParam().getLinks() != null && request.getParam().getLinks().size() > 0) {
            List<List<Object>> links = getLinks(request.getParam().getLinks());
            aiReq.getTopoSource().setLinks(links);
            JaxLogUtil.getDataServiceLogger().info("links:{}", JsonUtil.encode(links));
        } else {
            aiReq.getTopoSource().setLinks(new ArrayList<>());
        }
        aiReq.getTopoSource().getNebulaConn().setHost(nebulaInfo.getHost());
        aiReq.getTopoSource().getNebulaConn().setPort(nebulaInfo.getPort());
        aiReq.getTopoSource().getNebulaConn().setUsername(nebulaInfo.getUsername());
        aiReq.getTopoSource().getNebulaConn().setPassword(nebulaInfo.getPassword());
        aiReq.getAlgParam().setDims(request.getParam().getDims() == null ?
                new ArrayList<>() : request.getParam().getDims());
        aiReq.getAlgParam().setLevelScoreMap(request.getParam().getLevelScore() == null ?
                new ArrayList<>() : request.getParam().getLevelScore());
        aiReq.getAlgParam().setCiClassifyScoreMap(request.getParam().getCiTypeScore() == null ?
                new ArrayList<>() : request.getParam().getCiTypeScore());
        aiReq.getAlgParam().setDepth(request.getParam().getDepth());
        aiReq.getAlgParam().setMaxAnalyzeCount(request.getParam().getMaxAnalyzeCount());
        aiReq.getAlgParam().setReturnCount(request.getParam().getReturnCount());

        LOG.info("aiReq请求参数:{}", JsonUtil.encode(aiReq));
        return aiReq;
    }

    public DsResponseData<TopoRcaResp> getResp(List<TopoRcaAiResp> aiResp) {
        DsResponseData<TopoRcaResp> result = new DsResponseData<>();
        result.setRows(new ArrayList<>());
        for (TopoRcaAiResp respItem : aiResp) {
            TopoRcaResp row = new TopoRcaResp();
            BeanUtil.copyProperties(respItem, row);
            result.getRows().add(row);
        }
        result.setTotalNum((long) result.getRows().size());
        return result;
    }

    private TopoRcaAiReq.CommonDbConn copy(ConnInfo source, TopoRcaAiReq.CommonDbConn target) {
        target.setDbType(source.getDbType().toLowerCase());
        target.setHost(source.getHost());
        target.setPort(source.getPort());
        target.setUsername(source.getUsername());
        target.setPassword(source.getPassword());
        target.setDatabase(source.getDatabase());
        return target;
    }
}
