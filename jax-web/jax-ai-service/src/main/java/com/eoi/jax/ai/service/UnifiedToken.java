package com.eoi.jax.ai.service;

/**
 * <AUTHOR>
 * @Date 2024/6/6
 */
public class UnifiedToken {
    private int duration;
    private String randomPaddingValue;
    private Long userId;
    private Long timestamp;
    private int status;

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getRandomPaddingValue() {
        return randomPaddingValue;
    }

    public void setRandomPaddingValue(String randomPaddingValue) {
        this.randomPaddingValue = randomPaddingValue;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
