package com.eoi.jax.api.fullmonitor;


import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/19
 */
public class ObjectPathGroupGraphTest {

    @Test
    public void testCase1() {
        ObjectPathGroupGraph graph = new ObjectPathGroupGraph();
        // build a graph
        graph.addEdge("a", "la1");
        graph.addEdge("a", "la2");
        graph.addEdge("a", "la3");
        graph.addEdge("a", "la4");
        graph.addEdge("la1", "ma1");
        graph.addEdge("la2", "ma2");
        graph.addEdge("la3", "ma3");
        graph.addEdge("la4", "ma4");
        graph.addEdge("ma1", "n1");
        graph.addEdge("ma2", "n1");
        graph.addEdge("ma3", "n2");
        graph.addEdge("ma4", "n2");
        graph.addEdge("n1", "o1");
        graph.addEdge("n2", "o1");

        //build b graph
        graph.addEdge("b", "lb1");
        graph.addEdge("b", "lb2");
        graph.addEdge("b", "lb3");
        graph.addEdge("b", "lb4");
        graph.addEdge("lb1", "mb1");
        graph.addEdge("lb2", "mb2");
        graph.addEdge("lb3", "mb3");
        graph.addEdge("lb4", "mb4");
        graph.addEdge("mb1", "n3");
        graph.addEdge("mb2", "n3");
        graph.addEdge("mb3", "n4");
        graph.addEdge("mb4", "n4");
        graph.addEdge("n3", "o1");
        graph.addEdge("n4", "o1");
        graph.addEdge("o1", "v");

        // build c graph
        graph.addEdge("c", "lc1");
        graph.addEdge("c", "lc2");
        graph.addEdge("c", "lc3");
        graph.addEdge("c", "lc4");
        graph.addEdge("lc1", "mc1");
        graph.addEdge("lc2", "mc2");
        graph.addEdge("lc3", "mc3");
        graph.addEdge("lc4", "mc4");
        graph.addEdge("mc1", "n5");
        graph.addEdge("mc2", "n5");
        graph.addEdge("mc3", "n6");
        graph.addEdge("mc4", "n6");
        graph.addEdge("n5", "o2");
        graph.addEdge("n6", "o2");

        // build d graph
        graph.addEdge("d", "ld1");
        graph.addEdge("d", "ld2");
        graph.addEdge("d", "ld3");
        graph.addEdge("d", "ld4");
        graph.addEdge("ld1", "md1");
        graph.addEdge("ld2", "md2");
        graph.addEdge("ld3", "md3");
        graph.addEdge("ld4", "md4");
        graph.addEdge("md1", "n7");
        graph.addEdge("md2", "n7");
        graph.addEdge("md3", "n8");
        graph.addEdge("md4", "n8");
        graph.addEdge("n7", "o2");
        graph.addEdge("n8", "o2");

        // build e graph
        graph.addEdge("e", "le1");
        graph.addEdge("e", "le2");
        graph.addEdge("e", "le3");
        graph.addEdge("e", "le4");
        graph.addEdge("le1", "me1");
        graph.addEdge("le2", "me2");
        graph.addEdge("le3", "me3");
        graph.addEdge("le4", "me4");
        graph.addEdge("me1", "n9");
        graph.addEdge("me2", "n9");
        graph.addEdge("me3", "n10");
        graph.addEdge("me4", "n10");
        graph.addEdge("n9", "o3");
        graph.addEdge("n10", "o3");

        List<ObjectPathGroupGraph.Group> groups = graph.findGroup(new LinkedHashSet<>(Arrays.asList("a", "b", "c", "d", "e")));
        Assert.assertEquals(groups.size(), 3);
        Assert.assertEquals(groups.get(0).getGroupIds(), new LinkedHashSet<>(Arrays.asList("c", "d")));
        Assert.assertEquals(groups.get(0).getRoot(), new LinkedHashSet<>(Arrays.asList("o2")));
        Assert.assertEquals(groups.get(0).getPaths().size(), 28);
        Assert.assertEquals(groups.get(1).getGroupIds(), new LinkedHashSet<>(Arrays.asList("e")));
        Assert.assertEquals(groups.get(1).getRoot().size(), 0);
        Assert.assertEquals(groups.get(1).getPaths().size(), 14);
        Assert.assertEquals(groups.get(2).getGroupIds(), new LinkedHashSet<>(Arrays.asList("a", "b")));
        Assert.assertEquals(groups.get(2).getRoot(), new LinkedHashSet<>(Arrays.asList("v", "o1")));
        Assert.assertEquals(groups.get(2).getPaths().size(), 29);
    }

    @Test
    public void testCycleCase1() {
        ObjectPathGroupGraph graph = new ObjectPathGroupGraph();
        // build a graph
        graph.addEdge("a", "la1");
        graph.addEdge("a", "la2");
        graph.addEdge("a", "la3");
        graph.addEdge("a", "la4");
        graph.addEdge("la1", "ma1");
        graph.addEdge("la2", "ma2");
        graph.addEdge("la3", "ma3");
        graph.addEdge("la4", "ma4");
        graph.addEdge("ma1", "n1");
        graph.addEdge("ma2", "n1");
        graph.addEdge("ma3", "n2");
        graph.addEdge("ma4", "n2");
        graph.addEdge("n1", "o1");
        graph.addEdge("n2", "o1");
        graph.addEdge("o1", "v");
        graph.addEdge("o1", "mb4");

        //build b graph
        graph.addEdge("b", "lb1");
        graph.addEdge("b", "lb2");
        graph.addEdge("b", "lb3");
        graph.addEdge("b", "lb4");
        graph.addEdge("lb1", "mb1");
        graph.addEdge("lb2", "mb2");
        graph.addEdge("lb3", "mb3");
        graph.addEdge("lb4", "mb4");
        graph.addEdge("mb1", "n3");
        graph.addEdge("mb2", "n3");
        graph.addEdge("mb3", "n4");
        graph.addEdge("mb4", "n4");
        graph.addEdge("n3", "o1");
        graph.addEdge("n4", "o1");

        // build c graph
        graph.addEdge("c", "lc1");
        graph.addEdge("c", "lc2");
        graph.addEdge("c", "lc3");
        graph.addEdge("c", "lc4");
        graph.addEdge("lc1", "mc1");
        graph.addEdge("lc2", "mc2");
        graph.addEdge("lc3", "mc3");
        graph.addEdge("lc4", "mc4");
        graph.addEdge("mc1", "n5");
        graph.addEdge("mc2", "n5");
        graph.addEdge("mc3", "n6");
        graph.addEdge("mc4", "n6");
        graph.addEdge("n5", "o2");
        graph.addEdge("n6", "o2");

        // build d graph
        graph.addEdge("d", "ld1");
        graph.addEdge("d", "ld2");
        graph.addEdge("d", "ld3");
        graph.addEdge("d", "ld4");
        graph.addEdge("ld1", "md1");
        graph.addEdge("ld2", "md2");
        graph.addEdge("ld3", "md3");
        graph.addEdge("ld4", "md4");
        graph.addEdge("md1", "n7");
        graph.addEdge("md2", "n7");
        graph.addEdge("md3", "n8");
        graph.addEdge("md4", "n8");
        graph.addEdge("n7", "o2");
        graph.addEdge("n8", "o2");

        // build e graph
        graph.addEdge("e", "le1");
        graph.addEdge("e", "le2");
        graph.addEdge("e", "le3");
        graph.addEdge("e", "le4");
        graph.addEdge("le1", "me1");
        graph.addEdge("le2", "me2");
        graph.addEdge("le3", "me3");
        graph.addEdge("le4", "me4");
        graph.addEdge("me1", "n9");
        graph.addEdge("me2", "n9");
        graph.addEdge("me3", "n10");
        graph.addEdge("me4", "n10");
        graph.addEdge("n9", "o3");
        graph.addEdge("n10", "o3");




        List<ObjectPathGroupGraph.Group> groups = graph.findGroup(new LinkedHashSet<>(Arrays.asList("a", "b", "c", "d", "e")));

        Assert.assertEquals(groups.size(), 3);
        Assert.assertEquals(groups.get(0).getGroupIds(), new LinkedHashSet<>(Arrays.asList("c", "d")));
        Assert.assertEquals(groups.get(0).getRoot(), new LinkedHashSet<>(Arrays.asList("o2")));
        Assert.assertEquals(groups.get(0).getPaths().size(), 28);
        Assert.assertEquals(groups.get(1).getGroupIds(), new LinkedHashSet<>(Arrays.asList("e")));
        Assert.assertEquals(groups.get(1).getRoot().size(), 0);
        Assert.assertEquals(groups.get(1).getPaths().size(), 14);
        Assert.assertEquals(groups.get(2).getGroupIds(), new LinkedHashSet<>(Arrays.asList("a", "b")));
        Assert.assertEquals(groups.get(2).getRoot(), new LinkedHashSet<>(Arrays.asList("v", "o1", "n4", "mb4")));
        Assert.assertEquals(groups.get(2).getPaths().size(), 30);
    }
}
