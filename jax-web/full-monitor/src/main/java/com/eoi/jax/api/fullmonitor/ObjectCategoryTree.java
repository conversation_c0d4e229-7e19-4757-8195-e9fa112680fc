package com.eoi.jax.api.fullmonitor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/20
 */
public class ObjectCategoryTree {
    private Long id;
    private String name;
    private String type;
    private String objectType;
    private List<ObjectCategoryTree> children;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public List<ObjectCategoryTree> getChildren() {
        return children;
    }

    public void setChildren(List<ObjectCategoryTree> children) {
        this.children = children;
    }
}
