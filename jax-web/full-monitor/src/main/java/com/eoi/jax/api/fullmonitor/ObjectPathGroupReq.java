package com.eoi.jax.api.fullmonitor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/17
 */
public class ObjectPathGroupReq {
    private String namespace;
    private List<String> objectIds;

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public List<String> getObjectIds() {
        return objectIds;
    }

    public void setObjectIds(List<String> objectIds) {
        this.objectIds = objectIds;
    }
}
