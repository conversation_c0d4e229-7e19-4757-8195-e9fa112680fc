package com.eoi.jax.api.fullmonitor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.api.dataservice.annotation.DataServiceHandler;
import com.eoi.jax.api.dataservice.annotation.DsDatasourceParam;
import com.eoi.jax.api.dataservice.annotation.DsReqParam;
import com.eoi.jax.api.dataservice.annotation.DsRespParam;
import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsRespColumnTypeEnum;
import com.eoi.jax.api.dataservice.exception.DataServiceException;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.ds.DsDatasource;
import com.eoi.jax.api.dataservice.model.ds.conn.CkManConnectionSetting;
import com.eoi.jax.api.dataservice.model.ds.conn.MysqlConnectionSetting;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.repository.entity.TbMetricItem;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.service.TbTableDeployService;

import java.sql.Connection;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/18
 */
@DataServiceHandler(
        name = "transMetricDimAllApi",
        description = "交易指标维度分布查询api",
        accessJaxMeta = false,
        enablePaged = false,
        datasources = {@DsDatasourceParam(name = "ck", display = "tradeCk", type = DsDatasourceTypeEnum.CLICKHOUSE),
            @DsDatasourceParam(name = "mysql", display = "tradeDic", type = DsDatasourceTypeEnum.MYSQL)},
        reqParams = {
            @DsReqParam(
                name = "metricCodes",
                desc = "应用系统监控id",
                type = "LIST_STRING",
                sampleValue = "[1]",
                required = true),
            @DsReqParam(
                name = "timeFrom",
                desc = "时间范围",
                type = "STRING",
                sampleValue = "2023-09-19 09:00:00",
                required = true),
            @DsReqParam(
                name = "timeTo",
                desc = "时间范围",
                type = "STRING",
                sampleValue = "2023-09-19 09:00:00",
                required = true),
            @DsReqParam(
                name = "objectId",
                desc = "应用id",
                type = "STRING",
                required = true),
            @DsReqParam(
                name = "dimKey",
                desc = "维度键过滤",
                type = "STRING",
                required = true),
            @DsReqParam(
                name = "mysqlDimValueDicName",
                desc = "mysql应用交易维度键表",
                type = "STRING",
                defaultValue = "dim_application_dimension_value",
                required = true),
            @DsReqParam(
                name = "workdays",
                desc = "查询指定工作日范围内指标",
                type = "LIST_STRING",
                sampleValue = "[]"),
            @DsReqParam(
                name = "midMetricSetting",
                desc = "指标code",
                type = "OBJECT",
                defaultValue = "{\"transBusinessSuccessRate\":[\"transSuccessCountByMinute\",\"transCountByMinute\"]," +
                        "\"transDurationAvg\":[\"transDurationByMinute\",\"transCountByMinute\"]}",
                required = true)
        },
        respParams = {
            @DsRespParam(
                name = "tradeValues",
                desc = "指标值",
                type = DsRespColumnTypeEnum.OBJECT)
        }
)
public class TradeMetricDimAllServiceImpl implements IDataService {

    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        // 请求参数
        TradeMetricDimAllReq reqParam = ReqParamUtil.decode(dataServiceSetting.getDataServiceRequest(), TradeMetricDimAllReq.class);
        if (reqParam.getTimeTo().compareTo(reqParam.getTimeFrom()) < 0) {
            throw new DataServiceException(ResponseCode.ILLEGAL_REQUEST_PARAM.getCode(),
                    String.format("请求参数timeTo[%s],需大于timeFrom[%s]",
                            reqParam.getTimeTo(), reqParam.getTimeFrom()));
        }
        Object settingParam = dataServiceSetting.getDataServiceRequest().getParam().get("midMetricSetting");
        Map<String, List<String>> metricSetting = TradeMetricUtil.calculateMetric(settingParam, reqParam.getMetricCodes());
        // 获取数据源
        Map<String, DsDatasource> dsDatasourceMap = dataServiceSetting.getDsDatasourceMap();
        DsDatasource ckDatasource = dsDatasourceMap.get("ck");
        CkManConnectionSetting ckManConnectionSetting = (CkManConnectionSetting) ckDatasource.getConnectionSetting();
        Connection jdbcConn = ClickhouseUtil.getConnection(ckManConnectionSetting.getCkJdbcUrl(), ckManConnectionSetting.getCkUsername(),
                ckManConnectionSetting.getCkPassword());
        DsDatasource mysqlDatasource = dsDatasourceMap.get("mysql");
        MysqlConnectionSetting mysqlConnection = (MysqlConnectionSetting) mysqlDatasource.getConnectionSetting();
        Connection mysqlConn = MysqlUtil.getConnection(mysqlConnection.getAddress(), mysqlConnection.getUsername(),
                mysqlConnection.getPassword());
        // 指标模型
        List<String> metricCodes = metricSetting.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        List<TbMetricItem> metricItems = TradeMetricUtil.getMetricCode(metricCodes);
        TbTableDeployService tableDeployService = ContextHolder.getBean(TbTableDeployService.class);
        TbTableDeploy lastSuccessTableDeploy = tableDeployService.getLastSuccessTableDeploy(metricItems.get(0).getIdxId());
        String ckTableName = lastSuccessTableDeploy.getIdxTbName();
        String idxDatabase = JSONUtil.toBean(lastSuccessTableDeploy.getSetting(), Map.class).get("database").toString();
        ckTableName = String.format("`%s`.`%s`", idxDatabase, ckTableName);
        String seriesTableName = String.format("`%s`.`%s`", idxDatabase, lastSuccessTableDeploy.getDimTbName());
        String dimKey = reqParam.getDimKey();
        String mysqlDimKey = "dimensionKey";
        String mysqlDimValue = "dimensionValue";
        String mysqlDimValueName = "dimensionValueDesc";
        // 获取指标值
        String dimensionValueSql = buildDimensionValueSql(reqParam, metricCodes, metricItems, ckTableName, seriesTableName, dimKey);
        List<Map<String, Object>> dimAllRet = ClickhouseUtil.query(jdbcConn, dimensionValueSql, null);
        String mysqlDimDicName = reqParam.getMysqlDimValueDicName();
        String dimDicSql = String.format("select * from  %s where objectId = '%s' and %s = '%s'  ",
                mysqlDimDicName, reqParam.getObjectId(), mysqlDimKey, dimKey);
        Map<String, Object> dimDic = MysqlUtil.query(mysqlConn, dimDicSql, null)
                .stream()
                .collect(Collectors.toMap(x -> x.get(mysqlDimValue).toString(), x -> x.get(mysqlDimValueName), (x, y) -> x));
        for (Map<String, Object> colValue : dimAllRet) {
            String dv = colValue.getOrDefault(mysqlDimValue, "").toString();
            colValue.put("dimName", dimDic.getOrDefault(dv, dv));
            metricSetting.forEach((metricName, subMetrics) -> {
                if (subMetrics.size() == 2) {
                    double v1 = Double.parseDouble(ObjectUtil.defaultIfNull(colValue.get(subMetrics.get(0)), 0).toString());
                    double v2 = Double.parseDouble(ObjectUtil.defaultIfNull(colValue.get(subMetrics.get(1)), 0).toString());
                    Double val = v2 == 0 ? 0 : v1 / v2 * (metricName.contains("Rate") ? 100 : 1);
                    colValue.put(metricName, val);
                }
            });
        }
        DataServiceResponseData resp = new DataServiceResponseData();
        resp.setRows(dimAllRet);
        return resp;
    }

    private String buildDimensionValueSql(TradeMetricDimAllReq reqParam, List<String> metricCodes, List<TbMetricItem> metricItems,
                                          String ckTableName, String seriesTableName, String dimKey) {
        List<String> workdays = reqParam.getWorkdays();
        if (CollUtil.isEmpty(workdays)) {
            workdays = Arrays.asList(reqParam.getTimeFrom().substring(0, 10),
                    reqParam.getTimeTo().substring(0, 10));
        }
        String timeFromMinute = reqParam.getTimeFrom().substring(11, 16);
        String timeToMinute = reqParam.getTimeTo().substring(11, 16);
        String dimensionValueSql = String.format("select series.dimensionValue ,%s" +
                        " from (" +
                        "   select __series_id, " +
                        TradeMetricUtil.getSumMetricSql(metricItems) +
                        "   from %s" +
                        "   where toDate(timestamp) in ('%s') " +
                        "   and (formatDateTime(timestamp,'%%R') >= '%s' %s formatDateTime(timestamp,'%%R') <= '%s') " +
                        "   group by __series_id ) as value " +
                        "global join (" +
                        "   select __series_id,%s as dimensionValue from %s " +
                        "   where  objectId = '%s' and __name__='%s' " +
                        "  ) as series " +
                        "on value.__series_id = series.__series_id",
                metricCodes.stream().collect(Collectors.joining(",")),
                ckTableName, workdays.stream().collect(Collectors.joining("','")),
                timeFromMinute, timeFromMinute.compareTo(timeToMinute) > 0 ? "or" : "and", timeToMinute,
                dimKey, seriesTableName, reqParam.getObjectId(),
                JsonUtil.encode(Arrays.asList(dimKey, "objectId").stream().sorted().collect(Collectors.toList()))
        );
        return dimensionValueSql;
    }

}
