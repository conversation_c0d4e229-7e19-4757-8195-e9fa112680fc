package com.eoi.jax.api.fullmonitor;

import com.eoi.jax.api.dataservice.annotation.DataServiceHandler;
import com.eoi.jax.api.dataservice.annotation.DsDatasourceParam;
import com.eoi.jax.api.dataservice.annotation.DsReqParam;
import com.eoi.jax.api.dataservice.annotation.DsRespParam;
import com.eoi.jax.api.dataservice.enumrate.DsDatasourceTypeEnum;
import com.eoi.jax.api.dataservice.enumrate.DsRespColumnTypeEnum;
import com.eoi.jax.api.dataservice.model.ds.DataServiceSetting;
import com.eoi.jax.api.dataservice.model.response.DataServiceResponseData;
import com.eoi.jax.api.dataservice.service.IDataService;
import com.eoi.jax.web.core.client.nebula.NebulaClient;
import com.vesoft.nebula.client.graph.data.ResultSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/14
 */
@DataServiceHandler(
        name = "ObjectCountService",
        description = "监控对象统计",
        accessJaxMeta = false,
        enablePaged = true,
        datasources = {
            @DsDatasourceParam(
                name = "nebular",
                display = "nebular",
                type = DsDatasourceTypeEnum.NEBULA
                )
        },
        reqParams = {
            @DsReqParam(
                name = "objectType",
                desc = "objectType",
                type = "STRING",
                required = true
                ),
            @DsReqParam(
                name = "objectIds",
                desc = "objectIds",
                type = "LIST_STRING",
                required = false
                ),
            @DsReqParam(
                name = "objectName",
                desc = "objectName",
                type = "STRING",
                required = false
                ),
            @DsReqParam(
                name = "appCategory",
                desc = "appCategory",
                type = "STRING",
                required = false
                ),
            @DsReqParam(
                name = "appLevel",
                desc = "appLevel",
                type = "STRING",
                required = false
                ),
            @DsReqParam(
                name = "appOrganization",
                desc = "appOrganization",
                type = "STRING",
                required = false
                )
        },
        respParams = {
            @DsRespParam(
                name = "objectId",
                desc = "objectId",
                type = DsRespColumnTypeEnum.STRING
                ),
            @DsRespParam(
                name = "objectName",
                desc = "objectName",
                type = DsRespColumnTypeEnum.STRING
                ),
            @DsRespParam(
                name = "objectType",
                desc = "objectType",
                type = DsRespColumnTypeEnum.STRING
                )
        }
)
public class ObjectCountServiceImpl extends AbstractObjectService implements IDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ObjectCountServiceImpl.class);
    @Override
    public DataServiceResponseData execute(DataServiceSetting dataServiceSetting) {
        try (NebulaClient nebulaClient = createNebulaClient(dataServiceSetting)) {
            ObjectQueryReq req = ReqParamUtil.decode(dataServiceSetting.getDataServiceRequest(), ObjectQueryReq.class);
            req.setNamespace(NebulaConfig.getCurrentNamespace());
            String sql = req.buildCountSql();
            LOGGER.info("execute sql: {}", sql);
            ResultSet resultSet = nebulaClient.doExecute(sql);
            List<Map<String, Object>> rows = NebularUtil.decodeColumnList(resultSet);
            DataServiceResponseData resp = new DataServiceResponseData();
            resp.setRows(rows);
            resp.setTotalNum((long) rows.size());
            return resp;
        }
    }
}
