package com.eoi.jax.api.fullmonitor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/17
 */
public class ObjectPathRule {
    public static final String TAG = "TAG";
    public static final String EDGE = "EDGE";
    private String nodeId;
    private String nodeType;
    private String sourceId;
    private String targetId;
    private List<ObjectTag> tags;
    private List<ObjectEdge> relations;

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public List<ObjectTag> getTags() {
        return tags;
    }

    public void setTags(List<ObjectTag> tags) {
        this.tags = tags;
    }

    public List<ObjectEdge> getRelations() {
        return relations;
    }

    public void setRelations(List<ObjectEdge> relations) {
        this.relations = relations;
    }

    public static final class ObjectTag {
        public static final String OBJECT = "OBJECT";
        public static final String CATEGORY = "CATEGORY";
        private Long objectId;
        private String objectType;

        public Long getObjectId() {
            return objectId;
        }

        public void setObjectId(Long objectId) {
            this.objectId = objectId;
        }

        public String getObjectType() {
            return objectType;
        }

        public void setObjectType(String objectType) {
            this.objectType = objectType;
        }
    }

    public static final class ObjectEdge {
        private Long relationId;

        public Long getRelationId() {
            return relationId;
        }

        public void setRelationId(Long relationId) {
            this.relationId = relationId;
        }
    }
}
