<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2
          http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <!-- Assembles a packaged version targeting OS installation. -->
    <id>release</id>
    <formats>
        <format>dir</format>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.basedir}/release</directory>
            <outputDirectory>jax</outputDirectory>
            <includes>
                <include>start.sh</include>
                <include>stop.sh</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/release</directory>
            <outputDirectory>jax/jax/</outputDirectory>
            <includes>
                <include>logback-spring.xml</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/release/debug</directory>
            <outputDirectory>jax/jax/debug</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/../jax-web-repository/src/main/resources/db/migration</directory>
            <outputDirectory>jax/jax/db/</outputDirectory>
            <includes>
                <include>**</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/../jax-web-core/src/main/resources</directory>
            <outputDirectory>jax/jax/</outputDirectory>
            <includes>
                <include>bootstrap.yml</include>
                <include>application.yml</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/target</directory>
            <includes>
                <include>jax-web-boot-${project.version}-exec.jar</include>
            </includes>
            <outputDirectory>jax/jax/</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/target/lib</directory>
            <outputDirectory>jax/jax/lib</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/../../libs</directory>
            <outputDirectory>jax/jax/lib</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
