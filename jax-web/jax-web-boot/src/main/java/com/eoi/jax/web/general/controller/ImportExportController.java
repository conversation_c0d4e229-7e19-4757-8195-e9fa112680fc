package com.eoi.jax.web.general.controller;

import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpParameters;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.core.security.UserContext;
import com.eoi.jax.web.data.modeling.model.excel.ImportQuery;
import com.eoi.jax.web.general.model.ImportModel;
import com.eoi.jax.web.general.service.ImportHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 导入excel
 *
 * @Author: yaru.ma
 * @Date: 2022/11/23
 */
@RestController
public class ImportExportController implements V2Controller {

    @Autowired
    private ImportHistoryService importHistoryService;


    @Operation(summary = "数据样例")
    @GetMapping("data/modeling/excel/example")
    public Response example(
        @Parameter(description = "类型", required = true, example = "name") @RequestParam("type") String type,
        @Parameter(description = "模型发布Id") @RequestParam(value = "tableDeployId", required = false) Long tableDeployId
    ) {
        Map<String, Object> params = null;
        if (Objects.nonNull(tableDeployId)) {
            params = new HashMap<>(2);
            params.put("tableDeployId", tableDeployId);
        }
        return Response.success(importHistoryService.example(type, params));
    }

    @Operation(summary = "excel模板下载")
    @GetMapping("data/modeling/excel/download")
    public void download(HttpServletResponse response,
                         @RequestParam(value = "jaxSuperProjectId", required = false) Long jaxSuperProjectId,
                         @Parameter(description = "类型", required = true, example = "name") @RequestParam("type") String type,
                         @Parameter(description = "模型发布Id") @RequestParam(value = "tableDeployId", required = false) Long tableDeployId,
                         @Parameter(description = "文件类型") @RequestParam(value = "fileType", required = false) String fileType
    ) {
        UserContext ctx = ContextHolder.getUserContext();
        if (ctx != null && jaxSuperProjectId != null) {
            ctx.setCurrentProjectId(jaxSuperProjectId);
        }
        Map<String, Object> params = null;
        if (Objects.nonNull(tableDeployId)) {
            params = new HashMap<>(2);
            params.put("tableDeployId", tableDeployId);
            params.put("fileType", fileType);
        }
        importHistoryService.download(type, response, params);
    }

    @Operation(summary = "excal导入预览")
    @PostMapping(value = "data/modeling/excel/preview", consumes = "multipart/form-data")
    public Response previewExcel(
        @Parameter(description = "excel文件", required = true) @RequestPart("file") MultipartFile excelFile,
        @Parameter(description = "类型", required = true, example = "name") @RequestParam("type") String type,
        ImportModel model
    ) {
        UserContext ctx = ContextHolder.getUserContext();
        if (ctx != null && model.getJaxSuperProjectId() != null) {
            ctx.setCurrentProjectId(model.getJaxSuperProjectId());
        }
        try (InputStream inputStream = excelFile.getInputStream()) {
            Map<String, Object> params = new HashMap<>(1);
            if (model != null && model.getFlowId() != null) {
                params.put("flowId", model.getFlowId());
            }
            if (model != null && model.getTableDeployId() != null) {
                params.put("tableDeployId", model.getTableDeployId());
            }
            if (model != null && model.getFileType() != null) {
                params.put("fileType", model.getFileType());
            }
            return Response.success(importHistoryService.preview(type, inputStream, params));
        } catch (IOException e) {
            return new Response().fail(ResponseCode.INVALID_PARAM);
        }
    }

    @Operation(summary = "excel文件导入")
    @PostMapping(value = "data/modeling/excel/import", consumes = "multipart/form-data")
    @AuditLog(category = "导入导出", opAction = OpActionEnum.UPLOAD, module = "导入", function = "维度建模导入", code = "import")
    public Response importExcel(
        @Parameter(description = "excel文件", required = true) @RequestPart("excelFile") MultipartFile excelFile,
        @OpParameters @Parameter(description = "类型", required = true, example = "name") @RequestParam("type") String type,
        @Parameter(description = "导入模式", required = true, example = "skip/cover") @RequestParam("mode") String mode,
        ImportModel model
    ) {
        UserContext ctx = ContextHolder.getUserContext();
        if (ctx != null && model.getJaxSuperProjectId() != null) {
            ctx.setCurrentProjectId(model.getJaxSuperProjectId());
        }
        Map<String, Object> params = new HashMap<>(1);
        if (model != null && model.getFlowId() != null) {
            params.put("flowId", model.getFlowId());
        }
        if (model != null && model.getTableDeployId() != null) {
            params.put("tableDeployId", model.getTableDeployId());
        }
        if (model != null && model.getFileType() != null) {
            params.put("fileType", model.getFileType());
        }
        return Response.success(importHistoryService.importFile(type, excelFile, mode, params));
    }

    @Operation(summary = "导出数据")
    @PostMapping(value = "data/modeling/excel/export", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public void export(HttpServletResponse response,
                       @Parameter(description = "类型") ImportExcelReq req
    ) {
        UserContext ctx = ContextHolder.getUserContext();
        if (ctx != null) {
            ctx.setCurrentProjectId(req.getJaxSuperProjectId());
        }
        importHistoryService.export(req, response);
    }

    @Operation(summary = "历史导入详情")
    @GetMapping(value = "data/modeling/excel/{id}")
    public Response downloadHis(
        @Parameter(description = "历史导入id", required = true) @PathVariable("id") Long id
    ) {
        return Response.success(importHistoryService.getHisById(id));
    }

    @Operation(summary = "历史导入情况-分页")
    @PostMapping(value = "data/modeling/excel/list/query")
    public Response page(
        @RequestBody ImportQuery req
    ) {
        return Response.success(importHistoryService.queryWithProjectAuth(req));
    }

    @Operation(summary = "下载历史导入的文件")
    @GetMapping(value = "data/modeling/excel/{id}/download")
    public void downloadHis(HttpServletResponse response,
                            @Parameter(description = "历史导入id", required = true) @PathVariable("id") Long id
    ) {
        importHistoryService.downloadHis(id, response);
    }

    @Operation(summary = "pipeline/工作流导入预览")
    @PostMapping(value = "data/modeling/data-development/preview", consumes = "multipart/form-data")
    public Response previewPipeline(
        @Parameter(description = "zip文件", required = true) @RequestPart("file") MultipartFile zipFile,
        @Parameter(description = "类型", required = true, example = "name") @RequestParam("type") String type,
        @Parameter(description = "流程节点id") @RequestParam("flowId") Long flowId
    ) {
        try {
            Map<String, Object> params = new HashMap<>(2);
            params.put("flowId", flowId);
            return Response.success(importHistoryService.preview(type, zipFile.getInputStream(), params));
        } catch (IOException e) {
            return new Response().fail(ResponseCode.INVALID_PARAM);
        }
    }

    @Operation(summary = "pipeline/工作流导入")
    @PostMapping(value = "data/modeling/data-development/import", consumes = "multipart/form-data")
    @AuditLog(category = "导入导出", opAction = OpActionEnum.UPLOAD, module = "导入", function = "数据开发导入", code = "import")
    public Response importPipeline(
        @Parameter(description = "文件", required = true) @RequestPart("file") MultipartFile excelFile,
        @OpParameters @Parameter(description = "类型", required = true, example = "name") @RequestParam("type") String type,
        @Parameter(description = "导入模式", required = true, example = "skip/cover") @RequestParam("mode") String mode,
        @Parameter(description = "流程节点id") @RequestParam("flowId") Long flowId
    ) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("flowId", flowId);
        return Response.success(importHistoryService.importFile(type, excelFile, mode, params));
    }


}
