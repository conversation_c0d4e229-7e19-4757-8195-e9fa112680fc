package com.eoi.jax.web.general.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileReader;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.constant.ExcelImportType;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.AppConfig;
import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.excel.SheetResp;
import com.eoi.jax.web.core.service.BaseProjectAuthServiceImpl;
import com.eoi.jax.web.data.modeling.model.excel.ExcelImportDetail;
import com.eoi.jax.web.data.modeling.model.excel.ExcelImportHistoryCreateReq;
import com.eoi.jax.web.data.modeling.model.excel.ExcelImportHistoryUpdateReq;
import com.eoi.jax.web.data.modeling.model.excel.ImportExcelResp;
import com.eoi.jax.web.data.modeling.model.excel.ImportQuery;
import com.eoi.jax.web.general.service.ImportExportService;
import com.eoi.jax.web.general.service.ImportHistoryService;
import com.eoi.jax.web.repository.entity.TbImportDetail;
import com.eoi.jax.web.repository.entity.TbImportHistory;
import com.eoi.jax.web.repository.service.TbImportDetailService;
import com.eoi.jax.web.repository.service.TbImportHistoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: yaru.ma
 * @Date: 2023/4/14
 */
@Service
public class ImportHistoryServiceImpl extends BaseProjectAuthServiceImpl<
        TbImportHistoryService,
        TbImportHistory,
        ImportExcelResp,
        ExcelImportHistoryCreateReq,
        ExcelImportHistoryUpdateReq,
        ImportQuery> implements ImportHistoryService {

    public ImportHistoryServiceImpl(@Autowired TbImportHistoryService importHistoryService) {
        super(importHistoryService);
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(ImportHistoryService.class.getName());
    @Autowired
    private TbImportDetailService importDetailService;
    @Autowired
    private TbImportHistoryService importHistoryService;
    @Autowired
    private IngestionImportServiceImpl importIngestionService;
    @Autowired
    private DataDeveloperExcelServiceImpl dataDeveloperExcelServiceImpl;
    @Autowired
    private ExcelServiceImpl excelService;
    @Autowired
    private AppConfig appConfig;
    @Autowired
    private OfflineWorkflowServiceImpl offlineWorkflowService;
    @Autowired
    private ObjectModelImportServiceImpl objectModelImportService;
    @Autowired
    private ModelImportServiceImpl modelImportServiceImpl;

    private static final String SUCCESS = "SUCCESS";
    private static final String PART_SUCCESS = "PART_SUCCESS";
    private static final String FAILED = "FAILED";
    private static final String DOWNLOAD_URL = "/api/v2/data/modeling/excel/%s/download";

    @Override
    public ImportExcelResp getHisById(Long hisId) {
        TbImportHistory his = importHistoryService.selectByIdWithProjectAuth(hisId);
        Map<String, List<TbImportDetail>> details = importDetailService
                .list(new LambdaQueryWrapper<TbImportDetail>().eq(TbImportDetail::getHisId, hisId))
                .stream().collect(Collectors.groupingBy(TbImportDetail::getSheetName));
        ImportExcelResp resp = new ImportExcelResp().copyFrom(his);
        List<SheetResp> sheetResp = getDealService(his.getImportType()).wrap2Sheet(his.getImportType(),
                details, his.getSchemaConfig());
        resp.setSheetModels(sheetResp);
        return resp;
    }

    @Override
    public void downloadHis(Long hisId, HttpServletResponse response) {
        TbImportHistory his = importHistoryService.getById(hisId);
        FileReader fileReader = new FileReader(his.getFilePath());
        try {
            OutputStream out = response.getOutputStream();
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    URLEncoder.encode(his.getOriginName(), "UTF-8"));
            out.write(fileReader.readBytes());
            out.flush();
            out.close();
        } catch (Exception e) {
            throw new BizException(ResponseCode.FAILED.getCode(), "下载excel失败:" + e.getMessage());
        }
    }

    @Override
    public void export(ImportExcelReq req, HttpServletResponse response) {
        getDealService(req.getType()).export(req, response);
    }

    @Override
    public List<SheetResp> example(String type, Map<String, Object> params) {
        return getDealService(type).example(type, params);
    }

    @Override
    public void download(String type, HttpServletResponse response, Map<String, Object> params) {
        getDealService(type).download(type, response, params);
    }

    @Override
    public List<SheetResp> preview(String type, InputStream inputStream, Map<String, Object> params) {
        return getDealService(type).preview(type, inputStream, params);
    }

    @Override
    public ImportExcelResp importFile(String type, MultipartFile excelFile, String mode, Map<String, Object> params) {
        long startTime = System.currentTimeMillis();
        String fileName = excelFile.getOriginalFilename();
        // 1.上传文件
        File file = saveFile2Local(excelFile);
        ImportExportService dealService = getDealService(type);
        // 2. 导入
        List<SheetResp> models = dealService.importFile(type, mode, file, params);
        // 3.插入历史记录
        TbImportHistory his = save2Db(type, file, startTime, models, dealService.example(type, params), fileName, mode);
        ImportExcelResp importExcelResp = new ImportExcelResp().copyFrom(his);
        importExcelResp.setSheetModels(models);
        return importExcelResp;
    }

    private ImportExportService getDealService(String type) {
        if (ExcelImportType.PIPELINE.equals(type)) {
            return importIngestionService;
        } else if (ExcelImportType.DATA_DEVELOPMENT.equals(type)) {
            return dataDeveloperExcelServiceImpl;
        } else if (ExcelImportType.WORKFLOW.equals(type)) {
            return offlineWorkflowService;
        } else if (ExcelImportType.OBJECT_INSTANCE.equals(type)) {
            return objectModelImportService;
        } else if (ExcelImportType.MODEL_DATA.equals(type)) {
            return modelImportServiceImpl;
        } else {
            return excelService;
        }
    }

    @SuppressWarnings("all")
    private TbImportHistory save2Db(String type, File file, long startTime, List<SheetResp> models,
                                    List<SheetResp> schema, String fileName, String mode) {
        ExcelImportHistoryCreateReq history = new ExcelImportHistoryCreateReq();
        history.setImportType(type);
        history.setImportMode(mode);
        history.setSuccessRow(models.stream().flatMap(x -> x.getRows().stream())
                .filter(AbstractExcelRowResp::isSuccess).count());
        history.setFailedRow(models.stream().flatMap(x -> x.getRows().stream())
                .filter(x -> !x.isSuccess()).count());
        history.setSchemaConfig(JsonUtil.encode(schema));
        String status = SUCCESS;
        if (history.getFailedRow() > 0 && history.getSuccessRow() > 0) {
            status = PART_SUCCESS;
        } else if (history.getFailedRow() > 0) {
            status = FAILED;
        }
        history.setImportStatus(status);
        history.setFileSize(file.length());
        history.setOriginName(fileName);
        history.setFilePath(file.getAbsolutePath());
        TbImportHistory historyEntity = history.toEntity();
        String url = null;
        try {
            url = Common.urlPathsJoin(appConfig.getServer().getListenHttp(),
                    String.format(DOWNLOAD_URL, historyEntity.getId().toString()));
        } catch (IOException e) {
            throw new BizException(ResponseCode.FAILED, "文件保存失败");
        }
        historyEntity.setFileUrl(url);
        historyEntity.setCreateTime(new Date(startTime));
        importHistoryService.save(historyEntity);
        List<TbImportDetail> details = new ArrayList<>();
        for (SheetResp model : models) {
            details.addAll(model.getRows().stream().map(row -> {
                ExcelImportDetail excelImportDetail = new ExcelImportDetail();
                excelImportDetail.setHisId(historyEntity.getId());
                excelImportDetail.setRowDetail(JsonUtil.encode(row));
                excelImportDetail.setSheetName(model.getSheetName());
                excelImportDetail.setRowStatus(row.isSuccess() ? SUCCESS : FAILED);
                return excelImportDetail;
            }).map(x -> x.toEntity()).collect(Collectors.toList()));
        }
        importDetailService.saveBatch(details);
        return historyEntity;
    }

    private File saveFile2Local(MultipartFile excelFile) {
        String fileSuffix = "." + Common.getFileExt(excelFile.getOriginalFilename());
        String fileName = excelFile.getOriginalFilename().substring(0, excelFile.getOriginalFilename().lastIndexOf("."));
        String filePath = Common.pathsJoin(appConfig.getJax().getExcelDir(),
                fileName + "." + System.currentTimeMillis() + fileSuffix);
        try (InputStream inputStream = excelFile.getInputStream()) {
            FileUtil.del(filePath);
            File file = FileUtil.touch(filePath);
            FileUtil.writeFromStream(inputStream, file);
            return file;
        } catch (Exception e) {
            throw new BizException(ResponseCode.UPLOAD_ERROR, e);
        }
    }
}
