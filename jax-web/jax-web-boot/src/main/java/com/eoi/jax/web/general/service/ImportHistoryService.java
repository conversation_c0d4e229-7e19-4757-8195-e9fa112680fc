package com.eoi.jax.web.general.service;

import com.eoi.jax.web.core.service.IBaseProjectAuthService;
import com.eoi.jax.web.data.modeling.model.excel.ExcelImportHistoryCreateReq;
import com.eoi.jax.web.data.modeling.model.excel.ExcelImportHistoryUpdateReq;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.data.modeling.model.excel.ImportExcelResp;
import com.eoi.jax.web.data.modeling.model.excel.ImportQuery;
import com.eoi.jax.web.core.excel.SheetResp;
import com.eoi.jax.web.repository.entity.TbImportHistory;
import com.eoi.jax.web.repository.service.TbImportHistoryService;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @Author: yaru.ma
 * @Date: 2023/4/14
 */
public interface ImportHistoryService extends IBaseProjectAuthService<
        TbImportHistoryService,
        TbImportHistory,
        ImportExcelResp,
        ExcelImportHistoryCreateReq,
        ExcelImportHistoryUpdateReq,
        ImportQuery> {

    /**
     * 导入历史详情
     *
     * @param hisId
     * @return
     */
    ImportExcelResp getHisById(Long hisId);

    /**
     * 下载导入文件
     *
     * @param hisId
     * @param response
     */
    void downloadHis(Long hisId, HttpServletResponse response);

    /**
     * 导出
     *
     * @param req
     * @param response
     */
    void export(ImportExcelReq req, HttpServletResponse response);

    /**
     * 样例数据
     *
     * @param type
     * @param params
     * @return
     */
    List<SheetResp> example(String type, Map<String, Object> params);

    /**
     * 下载
     *
     * @param type
     * @param response
     * @param params
     */
    void download(String type, HttpServletResponse response, Map<String, Object> params);

    /**
     * 预览
     *
     * @param type
     * @param inputStream
     * @param params
     * @return
     */
    List<SheetResp> preview(String type, InputStream inputStream, Map<String, Object> params);

    /**
     * 导入
     *
     * @param type
     * @param excelFile
     * @param mode
     * @param params
     * @return
     */
    ImportExcelResp importFile(String type, MultipartFile excelFile, String mode, Map<String, Object> params);
}
