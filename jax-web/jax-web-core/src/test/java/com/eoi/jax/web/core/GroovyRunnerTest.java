package com.eoi.jax.web.core;

import com.eoi.jax.web.core.script.GroovyScriptRunner;
import com.eoi.jax.web.core.script.ScriptDescribe;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Date 2023/12/13
 */
public class GroovyRunnerTest {

    @Test
    public void test1() throws Exception {
        ScriptDescribe scriptDescribe = new ScriptDescribe();
        scriptDescribe.setScript("java.util.Date date = new java.util.Date();" +
                "java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat(\"yyyy-MM-dd hh:mm:ss\");" +
                "dateFormat.format(date);");
        GroovyScriptRunner runner = new GroovyScriptRunner(scriptDescribe);
        Object obj = runner.evaluateValue();
        String objStr = String.valueOf(obj);
        Assert.assertEquals(objStr.length(), 19);
    }

    @Test
    public void test2() throws Exception {
        ScriptDescribe scriptDescribe = new ScriptDescribe();
        scriptDescribe.setScript("java.util.Date date = new java.util.Date();" +
                "java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat(\"yyyy-MM-dd hh:mm:ss\");" +
                "return dateFormat.format(date);");
        GroovyScriptRunner runner = new GroovyScriptRunner(scriptDescribe);
        Object obj = runner.evaluateValue();
        String objStr = String.valueOf(obj);
        Assert.assertEquals(objStr.length(), 19);
    }
}
