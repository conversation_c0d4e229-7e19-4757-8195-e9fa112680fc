package com.eoi.jax.web.core.client.yarn;

import cn.hutool.core.lang.Assert;
import org.junit.Test;

import java.net.URI;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
public class HdfsJmxClientTest {

    @Test
    public void test(){
        HdfsJmxClient hdfsJmxClient = new HdfsJmxClient("http://hadoop1:9870/");
        HadoopJmxMetric.NameNodeInfo nameNodeInfo = hdfsJmxClient.jmxNameNodeInfo();
        System.out.println(nameNodeInfo);
        System.out.println(nameNodeInfo.getUsedPercent());
//        System.out.println(nameNodeInfo.getUsedPercent());
        Assert.notNull(nameNodeInfo);
    }


    //@Test
    public void testKrb5Auth() {
        System.setProperty("java.security.krb5.conf", "D:\\workspaces\\env\\hadoop-test\\krb5.conf");
        HdfsJmxClient hdfsJmxClient = new HdfsJmxClient("https://hadoop9001:9871");
        hdfsJmxClient.withKrb5Auth("D:\\workspaces\\env\\hadoop-test\\flink.keytab", "<EMAIL>");
        HadoopJmxMetric.NameNodeInfo nameNodeInfo = hdfsJmxClient.jmxNameNodeInfo();
        Assert.notNull(nameNodeInfo);
    }


    public static void main(String[] args) throws URISyntaxException {
        URI uri = new URI("hdfs://namenode-hostname:8020");

        System.out.println(uri.getScheme());
        System.out.println(uri.getAuthority());
    }

}
