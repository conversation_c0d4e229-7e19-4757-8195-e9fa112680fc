package com.eoi.jax.web.core.mybatis.strategy;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eoi.jax.web.repository.annotation.ProjectAuthAnnotation;
import com.eoi.jax.web.repository.entity.TbAlert;
import com.eoi.jax.web.repository.search.query.AlertParam;
import com.eoi.jax.web.repository.search.query.AlertVisualizationParam;
import com.eoi.jax.web.repository.search.result.AlertTheTopCountResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 告警表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Mapper
public interface TestAlertMapper extends BaseMapper<TbAlert> {


    /**
     * 计数模型个数
     *
     * @param param
     * @return
     */
    @ProjectAuthAnnotation(resourceType = {"tb_realtime_check_task"}, resourceIdColumn = "r.task_id", returnProjectAuthField = false)
    int countByTable(@Param("param") AlertParam param);

    /**
     * 计算异常作业排名
     *
     * @param alertVisualizationParam
     * @return
     */
    @ProjectAuthAnnotation(resourceTypeColumn = "p.resource_type", resourceIdColumn = "p.process_id")
    List<AlertTheTopCountResult> selectTopPipeline(@Param("param") AlertVisualizationParam alertVisualizationParam);

}
