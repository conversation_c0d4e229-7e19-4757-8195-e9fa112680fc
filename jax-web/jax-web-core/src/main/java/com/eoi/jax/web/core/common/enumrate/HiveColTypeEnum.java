package com.eoi.jax.web.core.common.enumrate;

import com.eoi.jax.web.core.common.constant.ICodeEnum;

import java.util.Objects;

public enum HiveColTypeEnum implements ICodeEnum {

    HIVE_INT("INT"),

    HIVE_DECIMAL("DECIMAL(%s,%s)"),

    HIVE_TIMESTAMP("TIMESTAMP"),

    HIVE_DATE("DATE"),

    HIVE_BIGINT("BIGINT"),

    HIVE_DOUBLE("DOUBLE"),

    HIVE_VARCHAR("VARCHAR(%s)"),

    HIVE_STRING("STRING"),

    HIVE_BOOL("BOOLEAN"),

    HIVE_STRUCT("STRUCT<%s>");

    private final String code;

    HiveColTypeEnum(String code) {
        this.code = code;
    }

    @Override
    public String code() {
        return code;
    }


    public static HiveColTypeEnum from(ColumnTypeEnum type, Integer colLength) {
        HiveColTypeEnum hiveType;
        switch (type) {
            case TIMESTAMP:
            case DATETIME:
                hiveType = HIVE_TIMESTAMP;
                break;
            case BIGINT:
                hiveType = HIVE_BIGINT;
                break;
            case DOUBLE:
                hiveType = HIVE_DOUBLE;
                break;
            case DECIMAL:
                // hive decimal长度和精度与java一致
                hiveType = HIVE_DECIMAL;
                break;
            case STRING:
                // hive varchar 最大支持65535, STRING 无长度限制
                if (Objects.nonNull(colLength) && colLength < 65535) {
                    hiveType = HIVE_VARCHAR;
                } else {
                    hiveType = HIVE_STRING;
                }
                break;
            case DATE:
                hiveType = HIVE_DATE;
                break;
            case INT:
                hiveType = HIVE_INT;
                break;
            case BOOLEAN:
                hiveType = HIVE_BOOL;
                break;
            case COMPLEX:
                hiveType = HIVE_STRUCT;
                break;
            default:
                hiveType = HIVE_STRING;
                break;
        }
        return hiveType;
    }
}
