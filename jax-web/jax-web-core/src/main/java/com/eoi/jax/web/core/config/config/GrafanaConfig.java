package com.eoi.jax.web.core.config.config;

import cn.hutool.core.lang.Console;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.base.param.Parameter;
import com.eoi.jax.base.param.ParameterMeta;
import com.eoi.jax.base.param.ParameterUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/28
 */
public class GrafanaConfig {

    @Parameter(
            label = "Grafana地址",
            description = "Grafana地址，如：http://localhost:3000",
            optional = false,
            defaultValue = ""
    )
    private String url;

    @Parameter(
            label = "Grafana认证模式",
            description = "Grafana认证模式。anonymous：匿名模式，jwt：JWT认证",
            optional = false,
            defaultValue = "JWT",
            candidates = {"ANONYMOUS", "JWT"}
    )
    private String authMode;

    @Parameter(
            label = "已注册Grafana数据源列表",
            description = "此配置由中台自动生成，不需要手工设置。该列表记录了中台自动注册数据源到Grafana的列表",
            optional = true
    )
    private List<Datasource> registerDatasource = new ArrayList<>();

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAuthMode() {
        return authMode;
    }

    public void setAuthMode(String authMode) {
        this.authMode = authMode;
    }

    public List<Datasource> getRegisterDatasource() {
        return registerDatasource;
    }

    public void setRegisterDatasource(List<Datasource> registerDatasource) {
        this.registerDatasource = registerDatasource;
    }

    public Datasource getRegisterDatasource(String dsCode) {
        if (registerDatasource == null) {
            return null;
        }
        for (Datasource ds : registerDatasource) {
            if (ds.getDsCode().equals(dsCode)) {
                return ds;
            }
        }
        return null;
    }

    public static class Datasource {
        @Parameter(
                label = "数据源标识",
                description = "中台地址管理中的数据源标识，也是Grafana的数据源名称"
        )
        private String dsCode;
        @Parameter(
                label = "数据源名称",
                description = "中台地址管理中的数据源名称，它不是Grafana的名字"
        )
        private String dsName;
        @Parameter(
                label = "注册Grafana时间",
                description = "中台自动注册数据源到Grafana的时间，该时间用于对比数据源更新时间来判断是否需要更新Grafana的数据源信息"
        )
        private String registerDateTime;

        public String getDsCode() {
            return dsCode;
        }

        public void setDsCode(String dsCode) {
            this.dsCode = dsCode;
        }

        public String getDsName() {
            return dsName;
        }

        public void setDsName(String dsName) {
            this.dsName = dsName;
        }

        public String getRegisterDateTime() {
            return registerDateTime;
        }

        public void setRegisterDateTime(String registerDateTime) {
            this.registerDateTime = registerDateTime;
        }
    }

    public static void main(String[] args) throws Exception {
        List<ParameterMeta> parameterMetas = ParameterUtil.analyzeParameters(new GrafanaConfig());
        Console.print(JSONUtil.toJsonStr(parameterMetas));
    }
}
