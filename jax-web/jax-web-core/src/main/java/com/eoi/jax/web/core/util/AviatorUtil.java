package com.eoi.jax.web.core.util;

import com.eoi.aviator.AviatorEvaluator;
import com.eoi.aviator.AviatorEvaluatorInstance;
import com.eoi.aviator.Expression;
import com.eoi.aviator.Options;
import com.eoi.aviator.runtime.JavaMethodReflectionFunctionMissing;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/14
 */
public class AviatorUtil {
    private static AviatorEvaluatorInstance instance = AviatorEvaluator.newInstance();

    static {
        instance.setOption(Options.USE_USER_ENV_AS_TOP_ENV_DIRECTLY, false);
        instance.setFunctionMissing(JavaMethodReflectionFunctionMissing.getInstance());
    }

    public static Object eval(String expStr, Map<String, Object> env) throws Exception {
        Expression exp = instance.compile(expStr, true);
        return exp.execute(env);
    }

    /**
     * 测试数字表达式是否合法
     * 仅支持数字的加减乘除
     *
     * @param expression
     * @return
     */
    public static List<String> testMathExpression(String expression) {
        AviatorEvaluatorInstance mathInstance = AviatorEvaluator.newInstance();
        mathInstance.getFuncMap().clear();
        try {
            Expression compile = mathInstance.compile(expression);
            Map<String, Object> contextValue = compile.getVariableNames().stream().collect(Collectors.toMap(x -> x, x -> 1));
            compile.execute(contextValue);
            return compile.getVariableNames();
        } catch (Exception e) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "表达式不合法:" + e.getMessage());
        }
    }

    public static List<String> getVariableNames(String expressionStr) {
        Expression expression = instance.compile(expressionStr, false);
        return expression.getVariableNames();
    }

}
