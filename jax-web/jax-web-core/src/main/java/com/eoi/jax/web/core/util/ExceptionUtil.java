package com.eoi.jax.web.core.util;

import com.eoi.jax.web.core.common.exception.BizException;

/**
 * <AUTHOR> zsc
 * @create 2023/1/15 12:58
 */
public class ExceptionUtil {
    /**
     * 截取异常堆栈信息
     *
     * @param ex
     * @return
     */
    public static String stackTrace(Throwable ex) {
        if (ex == null) {
            return null;
        }
        if (ex instanceof BizException && ex.getCause() != null) {
            return cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(ex.getCause(), 10240);
        }
        return cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(ex, 10240);
    }
}
