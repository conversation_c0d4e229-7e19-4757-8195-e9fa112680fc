package com.eoi.jax.web.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "jax.tool")
public class ToolConfig implements ConfigToMap {
    private String jaxToolBin;
    private Integer jaxToolTimeoutMillis = 60000;

    public String getJaxToolBin() {
        return jaxToolBin;
    }

    public ToolConfig setJaxToolBin(String jaxToolBin) {
        this.jaxToolBin = jaxToolBin;
        return this;
    }

    public Integer getJaxToolTimeoutMillis() {
        return jaxToolTimeoutMillis == null ? 60000 : jaxToolTimeoutMillis;
    }

    public ToolConfig setJaxToolTimeoutMillis(Integer jaxToolTimeoutMillis) {
        this.jaxToolTimeoutMillis = jaxToolTimeoutMillis == null ? 60000 : jaxToolTimeoutMillis;
        return this;
    }
}
