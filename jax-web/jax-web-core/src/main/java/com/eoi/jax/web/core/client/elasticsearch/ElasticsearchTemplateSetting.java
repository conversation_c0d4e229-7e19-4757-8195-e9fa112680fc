package com.eoi.jax.web.core.client.elasticsearch;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.base.param.Parameter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: yaru.ma
 * @Date: 2024/4/24
 */
public class ElasticsearchTemplateSetting {

    private String templateIndexPrefix;
    private String templateIndexMiddle;
    private String templateIndexSuffix;
    private Integer indexShards;
    private Integer indexReplicas;
    private String nodeType;
    private String dynamicMapping;
    private String indexNameStrategy;
    private List<String> indexNamePatterns;
    private List<ElasticsearchColumnSetting> columnDeployList;
    private String lifecyclePolicy;
    /**
     * 索引时间字段类型，EVENT_TIME 数据时间，PROCESSING_TIME 写入时间
     */
    private String timeFieldType;
    /**
     * 索引时间字段类型为数据时间是，对应的模型字段
     */
    private String timeField;

    /**
     * 高级配置-模板优先级
     *
     * @return
     */
    @Parameter(label = "模板优先级", defaultValue = "100")
    private Integer order;


    public String getTemplateIndexPrefix() {
        return templateIndexPrefix;
    }

    public void setTemplateIndexPrefix(String templateIndexPrefix) {
        this.templateIndexPrefix = templateIndexPrefix;
    }

    public String getTemplateIndexMiddle() {
        return templateIndexMiddle;
    }

    public void setTemplateIndexMiddle(String templateIndexMiddle) {
        this.templateIndexMiddle = templateIndexMiddle;
    }

    public String getTemplateIndexSuffix() {
        return templateIndexSuffix;
    }

    public void setTemplateIndexSuffix(String templateIndexSuffix) {
        this.templateIndexSuffix = templateIndexSuffix;
    }

    public Integer getIndexShards() {
        return indexShards;
    }

    public void setIndexShards(Integer indexShards) {
        this.indexShards = indexShards;
    }

    public Integer getIndexReplicas() {
        return indexReplicas;
    }

    public void setIndexReplicas(Integer indexReplicas) {
        this.indexReplicas = indexReplicas;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getDynamicMapping() {
        return dynamicMapping;
    }

    public void setDynamicMapping(String dynamicMapping) {
        this.dynamicMapping = dynamicMapping;
    }

    public String getIndexNameStrategy() {
        return indexNameStrategy;
    }

    public void setIndexNameStrategy(String indexNameStrategy) {
        this.indexNameStrategy = indexNameStrategy;
    }

    public List<String> getIndexNamePatterns() {
        return indexNamePatterns;
    }

    public void setIndexNamePatterns(List<String> indexNamePatterns) {
        this.indexNamePatterns = indexNamePatterns;
    }

    public List<ElasticsearchColumnSetting> getColumnDeployList() {
        return columnDeployList;
    }

    public void setColumnDeployList(List<ElasticsearchColumnSetting> columnDeployList) {
        this.columnDeployList = columnDeployList;
    }

    public String getLifecyclePolicy() {
        return lifecyclePolicy;
    }

    public void setLifecyclePolicy(String lifecyclePolicy) {
        this.lifecyclePolicy = lifecyclePolicy;
    }

    public String getTimeFieldType() {
        return timeFieldType;
    }

    public void setTimeFieldType(String timeFieldType) {
        this.timeFieldType = timeFieldType;
    }

    public String getTimeField() {
        return timeField;
    }

    public void setTimeField(String timeField) {
        this.timeField = timeField;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public static Template generateTemplate(ElasticsearchTemplateSetting setting) {
        Template template = new Template();
        String indexPatterns = setting.getTemplateIndexPrefix()
                + setting.getTemplateIndexMiddle()
                + setting.getTemplateIndexSuffix();
        template.setIndexPatterns(indexPatterns);
        Template.IndexSettings indexSettings = new Template.IndexSettings();
        IndexSetting indexSetting = new IndexSetting();
        indexSetting.setNumberOfShards(setting.getIndexShards());
        indexSetting.setNumberOfReplicas(setting.getIndexReplicas());
        indexSetting.setLifecyclePolicy(setting.getLifecyclePolicy());
        if (StrUtil.isNotEmpty(setting.getNodeType())) {
            IndexSetting.AllocationRequire allocationRequire = new IndexSetting.AllocationRequire();
            allocationRequire.setBoxType(setting.getNodeType());
            IndexSetting.ShardAllocation shardAllocation = new IndexSetting.ShardAllocation();
            shardAllocation.setRequire(allocationRequire);
            IndexSetting.ShardRouting shardRouting = new IndexSetting.ShardRouting();
            shardRouting.setAllocation(shardAllocation);
            indexSetting.setRouting(shardRouting);
        }
        indexSettings.setIndex(indexSetting);
        template.setSettings(indexSettings);
        template.setOrder(setting.getOrder());
        IndexMapping mappings = new IndexMapping();
        mappings.setDynamic(setting.getDynamicMapping());
        if (CollUtil.isNotEmpty(setting.getColumnDeployList())) {
            Map<String, IndexMapping.Property> properties = new LinkedHashMap<>();
            for (ElasticsearchColumnSetting columnSetting : setting.getColumnDeployList()) {
                IndexMapping.Property property = new IndexMapping.Property();
                property.setType(columnSetting.getPropertyType());
                if (CollUtil.isNotEmpty(columnSetting.getDatetimeFormat())) {
                    property.setFormat(StrUtil.join("||", columnSetting.getDatetimeFormat()));
                }
                properties.put(columnSetting.getPropertyName(), property);
            }
            mappings.setProperties(properties);
        }
        template.setMappings(mappings);
        return template;
    }
}
