package com.eoi.jax.web.core.util;

import cn.hutool.core.lang.UUID;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class JwtUtil {


    /**
     * 由字符串生成加密key
     * use HS256
     *
     * @param key
     * @return
     */
    public static SecretKey getSecretKey(String key) {
        // 本地的密码解码
        byte[] encodedKey = Base64.decodeBase64(key);
        // 根据给定的字节数组使用AES加密算法构造一个密钥
        SecretKey secretKey = new SecretKeySpec(encodedKey, 0, encodedKey.length, "AES");
        return secretKey;
    }


    /**
     * use RS256
     *
     * @param rsaPrivateKey
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    private static PrivateKey getPrivateKey(String rsaPrivateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(rsaPrivateKey));
        KeyFactory kf = KeyFactory.getInstance("RSA");
        PrivateKey privKey = kf.generatePrivate(keySpec);
        return privKey;
    }

    /**
     * 创建jwt
     *
     * @param key
     * @param expireTime
     * @param subject
     * @param claims
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    public static String createJwt(String key, Date expireTime, String subject, Map<String, Object> claims)
            throws NoSuchAlgorithmException,
            InvalidKeySpecException {
        // 设置头部信息
        Map<String, Object> header = new HashMap(16);
        header.put("typ", "JWT");
        header.put("alg", "RS256");

        String jwtId = UUID.randomUUID().toString();
        // 生成JWT的时间
        long nowTime = System.currentTimeMillis();
        Date issuedAt = new Date(nowTime);
        PrivateKey privateKey = getPrivateKey(key);
        // 为payload添加各种标准声明和私有声明
        JwtBuilder builder = Jwts.builder()
                .setHeader(header)
                .setClaims(claims)
                .setId(jwtId)
                .setIssuedAt(issuedAt)
                .setIssuer("jax")
                .setSubject(subject)
                .setExpiration(expireTime)
                .signWith(SignatureAlgorithm.RS256, privateKey);

        return builder.compact();
    }


    /**
     * 解密jwt
     *
     * @param key
     * @param jwt
     * @return
     * @throws Exception
     */
    public static Claims parseJwt(String key, String jwt) throws Exception {
        Claims claims = Jwts.parser()
                .setSigningKey(getPrivateKey(key))
                .parseClaimsJws(jwt).getBody();
        return claims;
    }

    /**
     * 校验jwt
     *
     * @param jwt
     * @return rue-有效，false-失效
     */
    public static boolean verifyToken(String key, String jwt) {
        if (StringUtils.isEmpty(jwt)) {
            return false;
        }
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(getPrivateKey(key))
                    .parseClaimsJws(jwt)
                    .getBody();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }


    /**
     * 获取rsa密钥对
     *
     * @throws NoSuchAlgorithmException
     */
    private static void getRsaKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        keyGen.initialize(2048);
        KeyPair pair = keyGen.generateKeyPair();

        byte[] publicBytes = pair.getPublic().getEncoded();
        byte[] privateBytes = pair.getPrivate().getEncoded();
    }


}
