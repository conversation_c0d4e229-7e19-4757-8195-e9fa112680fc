package com.eoi.jax.web.core.model;

/**
 * @Author: tangy
 * @Date: 2023/10/16
 * @Desc:
 **/
public interface IProjectAuthModel {
    /**
     * 是否是所有者
     *
     * @return
     */
    Boolean getProjectAuthIsOwner();

    /**
     * 项目Id
     *
     * @return
     */
    Long getProjectAuthProjectId();

    /**
     * 归属项目名称
     *
     * @return
     */
    String getProjectAuthOwnerProjectName();

    /**
     * 归属项目Id
     *
     * @return
     */
    Long getProjectAuthOwnerProjectId();

    /**
     * 项目权限
     *
     * @return
     */
    String getProjectAuthPrivilege();

    /**
     * 资源类型
     *
     * @return
     */
    String getProjectAuthResourceType();

    /**
     * 权限信息
     * @return
     */
    IProjectAuthModelResp getProjectAuth();
}
