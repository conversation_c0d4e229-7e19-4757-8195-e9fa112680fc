package com.eoi.jax.web.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
@Configuration
@ConfigurationProperties(prefix = "jax.website")
public class WebsiteConfig implements ConfigToMap {
    private String root;
    private String index;

    public String getRoot() {
        return root;
    }

    public WebsiteConfig setRoot(String root) {
        this.root = root;
        return this;
    }

    public String getIndex() {
        return index;
    }

    public WebsiteConfig setIndex(String index) {
        this.index = index;
        return this;
    }
}
