package com.eoi.jax.web.core.client.yarn;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
public class YarnAppAttempt {
    private Integer id;
    private Long startTime;
    private Long finishedTime;
    private String containerId;
    private String nodeHttpAddress;
    private String nodeId;
    private String logsLink;
    private String appAttemptId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getFinishedTime() {
        return finishedTime;
    }

    public void setFinishedTime(Long finishedTime) {
        this.finishedTime = finishedTime;
    }

    public String getContainerId() {
        return containerId;
    }

    public void setContainerId(String containerId) {
        this.containerId = containerId;
    }

    public String getNodeHttpAddress() {
        return nodeHttpAddress;
    }

    public void setNodeHttpAddress(String nodeHttpAddress) {
        this.nodeHttpAddress = nodeHttpAddress;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getLogsLink() {
        return logsLink;
    }

    public void setLogsLink(String logsLink) {
        this.logsLink = logsLink;
    }

    public String getAppAttemptId() {
        return appAttemptId;
    }

    public void setAppAttemptId(String appAttemptId) {
        this.appAttemptId = appAttemptId;
    }

    public static final class YarnAppAttemptsResp {
        private YarnAppAttemptsAttemptResp appAttempts;

        public YarnAppAttemptsAttemptResp getAppAttempts() {
            return appAttempts;
        }

        public void setAppAttempts(YarnAppAttemptsAttemptResp appAttempts) {
            this.appAttempts = appAttempts;
        }
    }

    public static final class YarnAppAttemptsAttemptResp {
        private List<YarnAppAttempt> appAttempt;

        public List<YarnAppAttempt> getAppAttempt() {
            return appAttempt;
        }

        public void setAppAttempt(List<YarnAppAttempt> appAttempt) {
            this.appAttempt = appAttempt;
        }
    }
}
