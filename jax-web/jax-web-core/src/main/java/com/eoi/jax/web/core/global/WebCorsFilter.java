package com.eoi.jax.web.core.global;

import com.eoi.jax.web.core.config.AppConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfiguration;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/10
 */
@Component
@WebFilter(urlPatterns = "/*", filterName = "CorsFilter")
public class WebCorsFilter implements Filter {
    @Autowired
    private AppConfig appConfig;

    @Override
    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws IOException, ServletException {
        if (Boolean.TRUE.equals(appConfig.getJax().getCors().getEnabled())
                && req instanceof HttpServletRequest
                && resp instanceof HttpServletResponse) {
            HttpServletRequest request = (HttpServletRequest) req;
            HttpServletResponse response = (HttpServletResponse) resp;
            String reqOrigin = request.getHeader("Origin");
            response.setHeader("Access-Control-Allow-Origin", reqOrigin == null ? CorsConfiguration.ALL : reqOrigin);
            if (appConfig.getJax().getCors().getHeaders() != null) {
                for (Map.Entry<String, String> header : appConfig.getJax().getCors().getHeaders().entrySet()) {
                    response.setHeader(header.getKey(), header.getValue());
                }
            }
        }
        chain.doFilter(req, resp);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void destroy() {
    }
}
