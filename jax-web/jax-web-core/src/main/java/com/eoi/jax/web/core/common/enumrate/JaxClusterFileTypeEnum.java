package com.eoi.jax.web.core.common.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public enum JaxClusterFileTypeEnum implements ICodeEnum {


    HADOOP_CONF("HADOOP_CONF", "hadoop配置文件");

    private final String code;

    private final String message;

    JaxClusterFileTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static JaxClusterFileTypeEnum fromString(String code) {
        for (JaxClusterFileTypeEnum value : JaxClusterFileTypeEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        for (JaxClusterFileTypeEnum value : JaxClusterFileTypeEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }

}
