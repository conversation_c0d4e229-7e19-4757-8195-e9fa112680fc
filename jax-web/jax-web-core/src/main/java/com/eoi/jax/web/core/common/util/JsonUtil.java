package com.eoi.jax.web.core.common.util;

import com.eoi.jax.web.core.common.exception.JsonException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/1
 */
public final class JsonUtil {
    public static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }

    private JsonUtil() {
        // forbid init instance
    }

    /**
     * serialize any value as a String
     *
     * @param value any Object
     * @return JSON String
     */
    public static String encode(Object value) {
        if (value == null) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(value);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }

    /**
     * deserialize JSON content from given JSON String
     *
     * @param value     JSON String
     * @param valueType target class
     * @param <T>       target class
     * @return JSON content
     */
    public static <T> T decode(String value, Class<T> valueType) {
        if (value == null) {
            return null;
        }
        try {
            return MAPPER.readValue(value, valueType);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }

    /**
     * deserialize JSON content from given JSON byte array
     *
     * @param value     JSON byte array
     * @param valueType target class
     * @param <T>       target class
     * @return JSON content
     */
    public static <T> T decode(byte[] value, Class<T> valueType) {
        if (value == null) {
            return null;
        }
        try {
            return MAPPER.readValue(value, valueType);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }

    /**
     * deserialize JSON content from given JSON String
     *
     * @param value        JSON String
     * @param valueTypeRef target class reference
     * @param <T>          target class reference
     * @return JSON content
     */
    public static <T> T decode(String value, TypeReference<T> valueTypeRef) {
        if (value == null) {
            return null;
        }
        try {
            return MAPPER.readValue(value, valueTypeRef);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }

    /**
     * deserialize Map<String, Object> from given JSON String
     *
     * @param value JSON String
     * @return Map<String, Object>
     */
    public static Map<String, Object> decode2Map(String value) {
        if (value == null) {
            return null;
        }
        return decode(value, new TypeReference<Map<String, Object>>() {
        });
    }

    /**
     * deserialize List<Map<>> from given JSON String
     *
     * @param value JSON String
     * @return List<Map <>>
     */
    public static List<Map<String, Object>> decode2ListMap(String value) {
        if (value == null) {
            return null;
        }
        return decode(value, new TypeReference<List<Map<String, Object>>>() {
        });
    }

    /**
     * deserialize List<String> from given JSON String
     *
     * @param value JSON String
     * @return List<String>
     */
    public static List<String> decode2ListString(String value) {
        if (value == null) {
            return null;
        }
        return decode(value, new TypeReference<List<String>>() {
        });
    }

    /**
     * deserialize Map<String, String> from given JSON String
     *
     * @param value JSON String
     * @return Map<String, String>
     */
    public static Map<String, String> decode2MapString(String value) {
        if (value == null) {
            return null;
        }
        return decode(value, new TypeReference<Map<String, String>>() {
        });
    }

    /**
     * deserialize Jackson {@link JsonNode} from given JSON String
     *
     * @param value JSON String
     * @return Jackson {@link JsonNode}
     */
    public static JsonNode decode2Tree(String value) {
        try {
            return MAPPER.readTree(value);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }
}
