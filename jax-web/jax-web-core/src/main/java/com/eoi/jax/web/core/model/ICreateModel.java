package com.eoi.jax.web.core.model;

import com.eoi.jax.web.core.common.util.ModelBeanUtil;

/**
 * @param <T> the actual model
 * <AUTHOR>
 * @date 2022/11/17
 */
public interface ICreateModel<T> extends IModel<T> {

    /**
     * request to entity
     * @return
     */
    T toEntity();

    /**
     * auto fill fields:
     * - auto gen id For IdEntity
     * - auto set createTime For IOperatorEntity
     * - auto set updateTime For IOperatorEntity
     * - auto set createUser For IOperatorEntity
     * - auto set updateUser For IOperatorEntity
     * - auto set isDelete false For ILogicDeleteEntity
     * - auto set isLeaf true For ITreeEntity
     * @param t
     * @return filled entity
     */
    default T toEntity(T t) {
        copyTo(t);
        ModelBeanUtil.setCreateDefaultValue(t);
        return t;
    }
}
