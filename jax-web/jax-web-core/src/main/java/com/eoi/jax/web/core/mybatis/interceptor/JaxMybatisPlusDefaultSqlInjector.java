package com.eoi.jax.web.core.mybatis.interceptor;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;

import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/10/13
 * @Desc: 增加默认方法
 **/
public class JaxMybatisPlusDefaultSqlInjector extends DefaultSqlInjector {
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        methodList.add(new SelectListWithProjectAuth());
        methodList.add(new SelectByIdWithProjectAuth());
        methodList.add(new SelectPageWithProjectAuth());
        methodList.add(new SelectCountWithProjectAuth());
        methodList.add(new DeleteByIdWithProjectAuth());
        methodList.add(new DeleteByMapWithProjectAuth());
        methodList.add(new DeleteByWrapperWithProjectAuth());
        methodList.add(new SelectListWithUserInfo());
        methodList.add(new SelectBatchIdsWithUserInfo());
        methodList.add(new SelectByIdWithUserInfo());
        methodList.add(new SelectOneWithUserInfo());
        methodList.add(new SelectPageWithUserInfo());
        return methodList;
    }
}
