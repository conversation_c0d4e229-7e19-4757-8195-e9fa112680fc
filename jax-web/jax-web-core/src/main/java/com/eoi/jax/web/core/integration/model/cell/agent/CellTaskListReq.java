package com.eoi.jax.web.core.integration.model.cell.agent;

import com.eoi.jax.web.core.integration.model.cell.CellClusterBaseRequestInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/3
 */
public class CellTaskListReq extends CellClusterBaseRequestInfo {

    /**
     * 分页的from
     */
    private Long from;
    /**
     * 每页的大小
     */
    private Long size;
    /**
     * 排序
     */
    private Sort sort;

    /**
     * 来源标识
     */
    private String source;

    /**
     * 采集器的hostname 或者 ip
     */
    private String hostnameOrIp;
    /**
     * 采集器的系统
     */
    private String os;
    /**
     * task_id 列表
     */
    private List<Long> taskIds;
    /**
     * 任务的状态
     */
    private String taskStatus;


    public static class Sort {
        private String field;
        private String order;

        public Sort() {
        }

        public Sort(String field, String order) {
            this.field = field;
            this.order = order;
        }

        public String getField() {
            return field;
        }

        public void setField(String field) {
            this.field = field;
        }

        public String getOrder() {
            return order;
        }

        public void setOrder(String order) {
            this.order = order;
        }
    }

    public Long getFrom() {
        return from;
    }

    public void setFrom(Long from) {
        this.from = from;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Sort getSort() {
        return sort;
    }

    public void setSort(Sort sort) {
        this.sort = sort;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getHostnameOrIp() {
        return hostnameOrIp;
    }

    public void setHostnameOrIp(String hostnameOrIp) {
        this.hostnameOrIp = hostnameOrIp;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public List<Long> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }
}
