package com.eoi.jax.web.core.common.exception;

import com.eoi.jax.web.core.common.constant.ResponseCode;

/**
 * <AUTHOR>
 * @date 2022/10/8
 */
public class LockException extends JaxException {
    private final String code;
    private final String message;
    private Throwable cause;
    private Object entity;

    public LockException(ResponseCode code) {
        this(code.getCode(), code.getMessage());
    }

    public LockException(ResponseCode code, Throwable cause) {
        this(code.getCode(), code.getMessage(), cause);
    }

    public LockException(ResponseCode code, Object info) {
        this(code.getCode(), code.getMessage(), null, info);
    }


    public LockException(ResponseCode code, Throwable cause, Object entity) {
        this(code.getCode(), code.getMessage(), cause, entity);
    }

    public LockException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public LockException(String code, String message, Object entity) {
        super(message);
        this.code = code;
        this.message = message;
        this.entity = entity;
    }

    public LockException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
        this.cause = cause;
    }

    public LockException(String code, String message, Throwable cause, Object entity) {
        super(message, cause);
        this.code = code;
        this.message = message;
        this.cause = cause;
        this.entity = entity;
    }

    public String getCode() {
        return code;
    }

    public Object getEntity() {
        return entity;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public Throwable getCause() {
        return cause;
    }
}
