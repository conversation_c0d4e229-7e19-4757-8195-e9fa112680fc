package com.eoi.jax.web.core.model.filecheck;

import com.eoi.jax.web.core.model.jaxapi.JaxApiBaseReq;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
public class FileCheckReq extends JaxApiBaseReq {

    @NotBlank(message = "path不能为空")
    private String path;

    private boolean recursive = false;

    public FileCheckReq() {
    }

    public FileCheckReq(String path, boolean recursive) {
        this.path = path;
        this.recursive = recursive;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public boolean isRecursive() {
        return recursive;
    }

    public void setRecursive(boolean recursive) {
        this.recursive = recursive;
    }
}
