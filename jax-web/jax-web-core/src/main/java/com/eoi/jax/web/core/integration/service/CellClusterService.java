package com.eoi.jax.web.core.integration.service;

import com.eoi.jax.web.core.integration.model.cell.agent.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/12/20
 **/
public interface CellClusterService {
    /**
     * 获取agent操作状态
     *
     * @param req
     * @return
     */
    List<CellAgentOperateStatus> getAgentsOperateStatus(CellAgentReq req);


    /**
     * 获取Agent列表
     *
     * @param cellAgentListReq CellAgentListReq
     * @return CellAgentListResp
     */
    CellAgentListResp agents(CellAgentListReq cellAgentListReq);


    /**
     * 根据agentIds获取agent信息
     *
     * @param req CellAgentIdsReq
     * @return List<CellAgentInfo>
     */
    List<CellAgentInfo> getAgentById(CellAgentIdsReq req);


    /**
     * 获取文件列表
     *
     * @param agentId agentId
     * @param req     请求参数
     * @return 文件列表
     */
    List<String> getFc(Long agentId, CellAgentFcReq req);


    /**
     * 获取指定文件的开头部分数据
     *
     * @param agentId agentId
     * @param req     请求参数
     * @return 文件内容
     */
    String getAcat(Long agentId, CellAgentAcatReq req);


    /**
     * 查询agent的配置版本号
     *
     * @param req CellConfigVersionReq
     * @return CellConfigVersionResp
     */
    CellConfigVersionResp getConfigVersion(CellConfigVersionReq req);


    /**
     * 配置下发接口
     *
     * @param req 请求参数
     * @return 是否发布成功
     */
    boolean publishCellTask(CellTaskPublishReq req);

    /**
     * 获取指定agent的所有task的状态
     *
     * @param req 请求参数
     * @return List<CellTaskStatus>
     */
    List<CellTaskStatus> getTasksStatus(CellTaskStatusReq req);

    /**
     * 根据条件查询CellTask
     *
     * @param req CellTaskListReq
     * @return List<CellTaskListResp>
     */
    CellTaskListResp listCellTask(CellTaskListReq req);

    /**
     * 获取agent下指定文件的charset
     *
     * @param agentId agentId
     * @param req     请求参数
     * @return 根据文件获取的字符集
     */
    String getCharset(Long agentId, CellAgentFileCharsetReq req);


    /**
     * 根据taskId获取task的指标信息
     *
     * @param taskId JobId
     * @param req   CellMetricsReq
     * @return Map<String, List < CellMetricsResp>>
     */
    Map<String, List<CellMetricsResp>> taskMetrics(Long taskId, CellMetricsReq req);

    /**
     * 根据agentId查询agent指标
     *
     * @param agentId agentId
     * @param req     CellMetricsReq
     * @return CellAgentMetricsResp
     */
    CellAgentMetricsResp agentMetrics(Long agentId, CellMetricsReq req);

    /**
     * 根据agentId和进程名称获取配置文件
     *
     * @param agentId agentId
     * @param req     CellAgentConfigReq
     * @return Map<String, CellAgentConfigResp>
     */
    Map<String, CellAgentConfigResp> agentConfig(Long agentId, CellAgentConfigReq req);

    /**
     * 根据agentId和进程名称获取日志
     *
     * @param agentId agentId
     * @param req     CellAgentLogReq
     * @return
     */
    CellAgentLogResp agentLog(Long agentId, CellAgentLogReq req);

    /**
     * 获取指定的Task信息
     * @param req
     * @return
     */
    List<CellTaskInfo> getTaskByAgentIdAndTaskId(CellTaskReq req);

    /**
     * 获取所有的Cell标签
     * @param baseUrl
     * @return
     */
    List<CellGroupResp> getAllCellGroup(String baseUrl);
}
