package com.eoi.jax.web.core.integration.service;

import com.eoi.jax.web.core.integration.model.ck.CkCluster;
import com.eoi.jax.web.core.integration.model.ck.CkManBaseRequestInfo;

import java.util.List;
import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/12/20
 **/
public interface CkManService {

    /**
     * 获取所有ck集群信息
     *
     * @param baseInfo
     * @return
     */
    List<CkCluster> getClusters(CkManBaseRequestInfo baseInfo);

    /**
     * 查询sql执行
     *
     * @param baseInfo
     * @param sql
     * @return
     */
    List<Map<String, Object>> queryCkBySql(CkManBaseRequestInfo baseInfo, String sql);

    /**
     * 执行单句sql,执行sql失败以CkManApiException方式抛出异常
     *
     * @param baseInfo
     * @param sql
     * @return
     */
    void executeSql(CkManBaseRequestInfo baseInfo, String sql);

    /**
     * 查询集群下的数据库列表
     *
     * @param baseInfo
     * @return
     */
    List<String> selectDatabases(CkManBaseRequestInfo baseInfo);
}
