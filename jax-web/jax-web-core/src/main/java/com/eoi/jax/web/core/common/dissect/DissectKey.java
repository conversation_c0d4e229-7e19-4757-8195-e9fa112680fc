

package com.eoi.jax.web.core.common.dissect;


import java.util.EnumSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class DissectKey {
    private static final Pattern LEFT_MODIFIER_PATTERN = Pattern.compile("([+*&?])(.*?)(->)?$", Pattern.DOTALL);
    private static final Pattern RIGHT_PADDING_PATTERN = Pattern.compile("^(.*?)(->)?$", Pattern.DOTALL);
    private static final Pattern APPEND_WITH_ORDER_PATTERN = Pattern.compile("[+](.*?)(/)([0-9]+)(->)?$", Pattern.DOTALL);
    private final Modifier modifier;
    private boolean skip;
    private boolean skipRightPadding;
    private int appendPosition;
    private String name;


    public DissectKey(String key) {
        skip = key == null || key.isEmpty();
        modifier = Modifier.findModifier(key);
        switch (modifier) {
            case NONE:
                Matcher matcher = RIGHT_PADDING_PATTERN.matcher(key);
                while (matcher.find()) {
                    name = matcher.group(1);
                    skipRightPadding = matcher.group(2) != null;
                }
                skip = name.isEmpty();
                break;
            case NAMED_SKIP:
                matcher = LEFT_MODIFIER_PATTERN.matcher(key);
                while (matcher.find()) {
                    name = matcher.group(2);
                    skipRightPadding = matcher.group(3) != null;
                }
                skip = true;
                break;
            case APPEND:
            case FIELD_NAME:
            case FIELD_VALUE:
                matcher = LEFT_MODIFIER_PATTERN.matcher(key);
                while (matcher.find()) {
                    name = matcher.group(2);
                    skipRightPadding = matcher.group(3) != null;
                }
                break;
            case APPEND_WITH_ORDER:
                matcher = APPEND_WITH_ORDER_PATTERN.matcher(key);
                while (matcher.find()) {
                    name = matcher.group(1);
                    appendPosition = Short.valueOf(matcher.group(3));
                    skipRightPadding = matcher.group(4) != null;
                }
                break;
            default:
                break;
        }
        boolean b = name == null || (name.isEmpty() && !skip);
        if (b) {
            throw new AbstractDissectException.KeyParseException(key, "The key name could be determined");
        }
    }

    /**
     * Copy constructor to explicitly override the modifier.
     * @param key The key to copy (except for the modifier)
     * @param modifier the modifer to use for this copy
     */
    public DissectKey(DissectKey key, Modifier modifier) {
        this.modifier = modifier;
        this.skipRightPadding = key.skipRightPadding;
        this.skip = key.skip;
        this.name = key.name;
        this.appendPosition = key.appendPosition;
    }

    public Modifier getModifier() {
        return modifier;
    }

    public boolean skip() {
        return skip;
    }

    public boolean skipRightPadding() {
        return skipRightPadding;
    }

    int getAppendPosition() {
        return appendPosition;
    }

    public String getName() {
        return name;
    }

    //generated
    @Override
    public String toString() {
        return "DissectKey{" +
            "modifier=" + modifier +
            ", skip=" + skip +
            ", appendPosition=" + appendPosition +
            ", name='" + name + '\'' +
            '}';
    }

    public enum Modifier {
        NONE(""), APPEND_WITH_ORDER("/"), APPEND("+"), FIELD_NAME("*"), FIELD_VALUE("&"), NAMED_SKIP("?");

        private static final Pattern MODIFIER_PATTERN = Pattern.compile("[/+*&?]");

        private final String modifier;

        @Override
        public String toString() {
            return modifier;
        }

        Modifier(final String modifier) {
            this.modifier = modifier;
        }

        //package private for testing
        static Modifier fromString(String modifier) {
            return EnumSet.allOf(Modifier.class).stream().filter(km -> km.modifier.equals(modifier))
                .findFirst().orElseThrow(() -> new IllegalArgumentException("Found invalid modifier."));
        }

        private static Modifier findModifier(String key) {
            Modifier modifier = Modifier.NONE;
            if (key != null && !key.isEmpty()) {
                Matcher matcher = MODIFIER_PATTERN.matcher(key);
                int matches = 0;
                while (matcher.find()) {
                    Modifier priorModifier = modifier;
                    modifier = Modifier.fromString(matcher.group());
                    boolean b = ++matches > 1 && !(APPEND.equals(priorModifier) && APPEND_WITH_ORDER.equals(modifier));
                    if (b) {
                        throw new AbstractDissectException.KeyParseException(key, "multiple modifiers are not allowed.");
                    }
                }
            }
            return modifier;
        }
    }
}
