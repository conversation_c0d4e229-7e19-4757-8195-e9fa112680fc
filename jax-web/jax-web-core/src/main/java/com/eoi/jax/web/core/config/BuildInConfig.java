package com.eoi.jax.web.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "jax.buildin")
public class BuildInConfig implements ConfigToMap {
    private String jobJarDir;
    private String pipelineDir;

    public String getJobJarDir() {
        return jobJarDir;
    }

    public BuildInConfig setJobJarDir(String jobJarDir) {
        this.jobJarDir = jobJarDir;
        return this;
    }

    public String getPipelineDir() {
        return pipelineDir;
    }

    public BuildInConfig setPipelineDir(String pipelineDir) {
        this.pipelineDir = pipelineDir;
        return this;
    }
}
