package com.eoi.jax.web.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.common.enumrate.ConfigChangeType;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.SystemConfigChangeEvent;
import com.eoi.jax.web.core.config.SystemConfigChangeListener;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.util.ThreadPoolUtil;
import com.eoi.jax.web.repository.entity.TbDatasource;
import com.eoi.jax.web.repository.entity.TbSystemConfig;
import com.eoi.jax.web.repository.service.TbDatasourceService;
import com.eoi.jax.web.repository.service.TbSystemConfigService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023/8/1
 */
@Component
public class SystemConfigHolderImpl implements SystemConfigHolder {

    private static final Logger LOG = LoggerFactory.getLogger(SystemConfigHolderImpl.class);

    @Autowired
    private TbSystemConfigService tbSystemConfigService;

    @Autowired
    private TbDatasourceService tbDatasourceService;

    /**
     * Map<kye, <Md5, Object>>
     */
    private final Map<String, Pair<String, Object>> configMap = new ConcurrentHashMap<>(8);

    private final Map<String, List<SystemConfigChangeListener>> listener = new ConcurrentHashMap<>(8);

    private static final Pair<String, Object> EMPTY_PAIR = new Pair<>(null, null);

    @EventListener(ApplicationReadyEvent.class)
    @Override
    public synchronized void refreshConfig() {
        try {
            final Set<String> oldKeySet = new HashSet<>(configMap.keySet());
            List<SystemConfigChangeEvent> changeEventList = new ArrayList<>();
            for (int i = 1; ; i++) {
                Page<TbSystemConfig> page = tbSystemConfigService.page(new Page<>(i, 500, false));
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                for (TbSystemConfig tbSystemConfig : page.getRecords()) {
                    try {
                        String newMd5 = computeMd5(tbSystemConfig);
                        Pair<String, Object> oldConfig = configMap.getOrDefault(tbSystemConfig.getPrefixKey(), EMPTY_PAIR);
                        // 比较判断是否有变化，然后发布事件
                        ConfigChangeType configChangeType = diffConfig(oldConfig.getKey(), newMd5);
                        if (Objects.nonNull(configChangeType)) {
                            LOG.info("检查到配置发生变化prefixKey:{}, 变更类型:{}, newConfig:{}, oldConfig:{}",
                                tbSystemConfig.getPrefixKey(), configChangeType,
                                tbSystemConfig.getSetting(), JsonUtil.encode(oldConfig.getValue()));
                            // 旧的配置
                            Map<String, Object> oldConfigMap = getConfig(tbSystemConfig.getPrefixKey());
                            // 解析配置
                            Map<String, Object> newConfigMap = mergeConfig(tbSystemConfig);

                            Object config = mapConfigToBean(tbSystemConfig.getPrefixKey(), newConfigMap);
                            // 更新配置
                            configMap.put(tbSystemConfig.getPrefixKey(), new Pair<>(newMd5, config));
                            changeEventList.add(buildChangeEvent(tbSystemConfig.getPrefixKey(), configChangeType,
                                oldConfigMap, newConfigMap));
                        }
                        // 已处理的删除
                        oldKeySet.remove(tbSystemConfig.getPrefixKey());
                    } catch (Exception e) {
                        LOG.error("重新加载配置:{}异常", tbSystemConfig.getPrefixKey(), e);
                    }
                }
            }

            if (CollUtil.isNotEmpty(oldKeySet)) {
                for (String key : oldKeySet) {
                    LOG.info("检查到配置已删除化prefixKey:{}, :{}", key, ConfigChangeType.DELETED);
                    // 删除内存中的配置
                    Map<String, Object> oldConfigMap = beanConfigToMap(configMap.remove(key));
                    changeEventList.add(buildChangeEvent(key, ConfigChangeType.DELETED, oldConfigMap, null));
                }
            }

            for (SystemConfigChangeEvent event : changeEventList) {
                // 发布事件
                publishChangeEvent(event);
            }
        } catch (Exception e) {
            LOG.error("重新加载配置异常", e);
        }
    }

    /**
     * 注册配置改变事件
     *
     * @param prefixKey
     * @param systemConfigChangeListener
     */
    @Override
    public void addChangeListener(String prefixKey, SystemConfigChangeListener systemConfigChangeListener) {
        Assert.notBlank(prefixKey, "prefixKey不能为空");
        List<SystemConfigChangeListener> listenerList = listener.getOrDefault(prefixKey, new ArrayList<>());
        listenerList.add(systemConfigChangeListener);
        listener.put(prefixKey, listenerList);
    }

    @Override
    public void addChangeListener(SystemConfigEnum systemConfigEnum, SystemConfigChangeListener systemConfigChangeListener) {
        addChangeListener(systemConfigEnum.getKey(), systemConfigChangeListener);
    }

    private SystemConfigChangeEvent buildChangeEvent(String prefixKey,
                                                     ConfigChangeType type,
                                                     Map<String, Object> oldConfigMap,
                                                     Map<String, Object> newConfigMap) {
        SystemConfigChangeEvent event = new SystemConfigChangeEvent();
        event.setPrefixKey(prefixKey);
        event.setChangeType(type);
        if (Objects.nonNull(oldConfigMap)) {
            event.setOldConfigMap(new HashMap<>(oldConfigMap));
        }
        if (Objects.nonNull(newConfigMap)) {
            event.setNewConfigMap(new HashMap<>(newConfigMap));
        }
        return event;
    }

    /**
     * 发布事件
     *
     * @param event
     */
    private void publishChangeEvent(SystemConfigChangeEvent event) {
        List<SystemConfigChangeListener> listenerList = listener.get(event.getPrefixKey());
        if (CollUtil.isEmpty(listenerList)) {
            return;
        }
        for (SystemConfigChangeListener changeListener : listenerList) {
            ThreadPoolUtil.THREAD_POOL.submit(() -> {
                try {
                    changeListener.update(event);
                } catch (Exception e) {
                    LOG.error("系统配置事件处理异常", e);
                }
            });
        }
    }


    /**
     * 解析配置
     *
     * @param tbSystemConfig
     * @return
     */
    private Map<String, Object> mergeConfig(TbSystemConfig tbSystemConfig) {
        Map<String, Object> map = new HashMap<>(8);
        if (Objects.nonNull(tbSystemConfig.getDsId())) {
            TbDatasource tbDatasource = tbDatasourceService.getById(tbSystemConfig.getDsId());
            if (Objects.nonNull(tbDatasource) && StrUtil.isNotBlank(tbDatasource.getSetting())) {
                Map<String, Object> datasourceSettingMap = JsonUtil.decode(tbDatasource.getSetting(),
                    new TypeReference<Map<String, Object>>() {
                    });
                datasourceSettingMap.put("dsId", tbSystemConfig.getDsId());
                map.putAll(datasourceSettingMap);
            }
        }
        if (StrUtil.isBlank(tbSystemConfig.getSetting())) {
            return map;
        }
        Map<String, Object> sysConfigMap = JsonUtil.decode(tbSystemConfig.getSetting(), new TypeReference<Map<String, Object>>() {
        });
        if (CollUtil.isNotEmpty(sysConfigMap)) {
            map.putAll(sysConfigMap);
        }
        return map;
    }

    private String computeMd5(TbSystemConfig tbSystemConfig) {
        if (Objects.isNull(tbSystemConfig)) {
            return null;
        }
        StringBuilder newContent = new StringBuilder();
        if (Objects.nonNull(tbSystemConfig.getDsId())) {
            newContent.append(tbSystemConfig.getDsId()).append(";");
        }
        if (Objects.nonNull(tbSystemConfig.getSetting())) {
            newContent.append(tbSystemConfig.getSetting()).append(";");
        }
        return new Digester(DigestAlgorithm.MD5).digestHex(newContent.toString());
    }

    /**
     * 比较配置
     *
     * @return
     */
    private ConfigChangeType diffConfig(String oldMd5, String newMd5) {
        if (StrUtil.isBlank(oldMd5) && StrUtil.isBlank(newMd5)) {
            return null;
        } else if (StrUtil.isBlank(oldMd5) && StrUtil.isNotBlank(newMd5)) {
            return ConfigChangeType.ADDED;
        } else if (StrUtil.isBlank(newMd5) && StrUtil.isNotBlank(oldMd5)) {
            return ConfigChangeType.DELETED;
        }

        if (!oldMd5.equals(newMd5)) {
            return ConfigChangeType.MODIFIED;
        }
        return null;
    }

    /**
     * 包含Key
     *
     * @param key
     * @return
     */
    @Override
    public boolean containKey(String key) {
        return this.configMap.containsKey(key);
    }

    /**
     * 包含Key
     *
     * @param key
     * @return
     */
    @Override
    public boolean containKey(SystemConfigEnum key) {
        return containKey(key.getKey());
    }

    @Override
    public Map<String, Object> getConfig(String key) {
        if (!containKey(key)) {
            return null;
        }
        Pair<String, Object> pair = this.configMap.get(key);
        if (Objects.isNull(pair) || Objects.isNull(pair.getValue())) {
            return null;
        }
        return Collections.unmodifiableMap(beanConfigToMap(pair.getValue()));
    }

    @Override
    public Map<String, Object> getConfig(SystemConfigEnum key) {
        return getConfig(key.getKey());
    }

    private Map<String, Object> beanConfigToMap(Object config) {
        if (config instanceof Map) {
            return (Map<String, Object>) config;
        }
        return BeanUtil.beanToMap(config);
    }

    private Object mapConfigToBean(String key, Map<String, Object> mapConfig) {
        if (Objects.isNull(mapConfig)) {
            return null;
        }
        SystemConfigEnum systemConfigEnum = SystemConfigEnum.fromString(key);
        if (Objects.isNull(systemConfigEnum) || Objects.isNull(systemConfigEnum.getConfigClass())) {
            return mapConfig;
        }
        Object o = BeanUtil.mapToBean(mapConfig, systemConfigEnum.getConfigClass(), true);
        Field[] fields = ReflectUtil.getFields(o.getClass());
        if (mapConfig.size() > fields.length) {
            LOG.warn("配置项[{}]的配置项数量与配置类[{}]的属性数量不一致, 配置项数量:{}, 配置类属性数量:{}",
                    key, o.getClass().getSimpleName(), mapConfig.size(), fields.length);
        }
        return o;
    }

    @Override
    public <T> T getBean(String key, Class<T> beanClass) {
        Map<String, Object> map = getConfig(key);
        if (Objects.isNull(map)) {
            return null;
        }
        return BeanUtil.mapToBean(map, beanClass, false);
    }

    @Override
    public <T> T getBean(SystemConfigEnum key) {
        //noinspection unchecked
        return (T) this.configMap.getOrDefault(key.getKey(), EMPTY_PAIR).getValue();
    }

    @Override
    public Object get(String key, String subKey) {
        Map<String, Object> map = getConfig(key);
        if (Objects.isNull(map)) {
            return null;
        }
        return map.get(subKey);
    }

    @Override
    public Integer getInt(String key, String subKey) {
        Object o = get(key, subKey);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof Integer) {
            return (Integer) o;
        }
        if (o instanceof String) {
            return Integer.valueOf((String) o);
        }
        throw new NumberFormatException("未知的类型");
    }

    @Override
    public String getStr(String key, String subKey) {
        Object o = get(key, subKey);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof String) {
            return (String) o;
        }
        return String.valueOf(o);
    }

    @Override
    public Long getLong(String key, String subKey) {
        Object o = get(key, subKey);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof Long) {
            return (Long) o;
        }
        if (o instanceof Integer) {
            return ((Integer) o).longValue();
        }
        if (o instanceof String) {
            return Long.valueOf((String) o);
        }
        throw new NumberFormatException("未知的类型");
    }

    @Override
    public Boolean getBool(String key, String subKey) {
        Object o = get(key, subKey);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof Boolean) {
            return (Boolean) o;
        }
        if (o instanceof String) {
            return Boolean.valueOf((String) o);
        }
        throw new NumberFormatException("未知的类型");
    }

    @Override
    public Float getFloat(String key, String subKey) {
        Object o = get(key, subKey);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof Float) {
            return (Float) o;
        }
        if (o instanceof String) {
            return Float.valueOf((String) o);
        }
        throw new NumberFormatException("未知的类型");
    }

    @Override
    public Double getDouble(String key, String subKey) {
        Object o = get(key, subKey);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof Double) {
            return (Double) o;
        }
        if (o instanceof Float) {
            return ((Float) o).doubleValue();
        }
        if (o instanceof String) {
            return Double.valueOf((String) o);
        }
        throw new NumberFormatException("未知的类型");
    }

    @Override
    public Map<String, Object> getConfig(String key, Map<String, Object> defaultValue) {
        Map<String, Object> config = getConfig(key);
        return config == null ? defaultValue : config;
    }

    @Override
    public <T> T getBean(String key, Class<T> beanClass, T defaultValue) {
        T bean = getBean(key, beanClass);
        return bean == null ? defaultValue : bean;
    }

    @Override
    public Object get(String key, String subKey, Object defaultValue) {
        Object obj = get(key, subKey);
        return obj == null ? defaultValue : obj;
    }

    @Override
    public Integer getInt(String key, String subKey, Integer defaultValue) {
        Integer obj = getInt(key, subKey);
        return obj == null ? defaultValue : obj;
    }

    @Override
    public String getStr(String key, String subKey, String defaultValue) {
        String obj = getStr(key, subKey);
        return obj == null ? defaultValue : obj;
    }

    @Override
    public Long getLong(String key, String subKey, Long defaultValue) {
        Long obj = getLong(key, subKey);
        return obj == null ? defaultValue : obj;
    }

    @Override
    public Boolean getBool(String key, String subKey, Boolean defaultValue) {
        Boolean obj = getBool(key, subKey);
        return obj == null ? defaultValue : obj;
    }

    @Override
    public Float getFloat(String key, String subKey, Float defaultValue) {
        Float obj = getFloat(key, subKey);
        return obj == null ? defaultValue : obj;
    }

    @Override
    public Double getDouble(String key, String subKey, Double defaultValue) {
        Double obj = getDouble(key, subKey);
        return obj == null ? defaultValue : obj;
    }

    @Override
    public Object getObject(String key, String subKey) {
        return get(key, subKey);
    }

    @Override
    public Object getObject(String key, String subKey, Object defaultValue) {
        Object o = get(key, subKey);
        if (defaultValue != null && o != null && !o.getClass().equals(defaultValue.getClass())) {
            LOG.warn("数据库配置{}.{}值类型与默认数据值类型不一致,值类型: {}, 默认值类型:{}",
                    key, subKey, o.getClass(), defaultValue.getClass());
        }
        return o != null ? o : defaultValue;
    }
}
