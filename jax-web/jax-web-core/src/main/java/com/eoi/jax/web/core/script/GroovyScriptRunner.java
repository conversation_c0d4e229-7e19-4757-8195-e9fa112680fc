package com.eoi.jax.web.core.script;

import com.eoi.jax.web.core.common.exception.ScriptException;
import com.eoi.jax.web.core.script.groovy.GroovySandbox;


public class GroovyScriptRunner extends AbstractScriptRunner {
    private ScriptDescribe scriptDescribe;

    public GroovyScriptRunner(ScriptDescribe scriptDescribe) {
        this.scriptDescribe = scriptDescribe;
    }

    @Override
    public Object evaluateValue() throws ScriptException {
        try {
            return GroovySandbox.secureEvaluate(scriptDescribe.getScript());
        } catch (Exception e) {
            throw new ScriptException(e);
        }
    }
}
