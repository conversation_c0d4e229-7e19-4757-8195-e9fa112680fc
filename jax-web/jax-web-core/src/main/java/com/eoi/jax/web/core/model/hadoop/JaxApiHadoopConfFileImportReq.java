package com.eoi.jax.web.core.model.hadoop;

import com.eoi.jax.web.core.model.jaxapi.JaxApiBaseReq;

import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class JaxApiHadoopConfFileImportReq extends JaxApiBaseReq {

    private Long clusterId;

    private List<String> fileNames;

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    public List<String> getFileNames() {
        return fileNames;
    }

    public void setFileNames(List<String> fileNames) {
        this.fileNames = fileNames;
    }
}
