package com.eoi.jax.web.core.client.krb5;

import javax.security.auth.login.AppConfigurationEntry;
import javax.security.auth.login.Configuration;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/4/11
 */
public class Krb5LoginConfig extends Configuration {
    private String keyTabLocation;
    private String principalName;
    private boolean debug;

    public Krb5LoginConfig(String keyTabLocation, String principalName, boolean debug) {
        this.keyTabLocation = keyTabLocation;
        this.principalName = principalName;
        this.debug = debug;
    }

    @Override
    public AppConfigurationEntry[] getAppConfigurationEntry(String name) {
        HashMap<String, String> options = new HashMap(16);
        options.put("useTicketCache", "false");
        options.put("useKeyTab", "true");
        options.put("keyTab", this.keyTabLocation);
        options.put("refreshKrb5Config", "true");
        options.put("principal", this.principalName);
        options.put("storeKey", "true");
        options.put("doNotPrompt", "true");
        options.put("isInitiator", "true");
        if (this.debug) {
            options.put("debug", "true");
        }
        AppConfigurationEntry appConfigurationEntry = new AppConfigurationEntry(
                "com.sun.security.auth.module.Krb5LoginModule",
                AppConfigurationEntry.LoginModuleControlFlag.REQUIRED,
                options
        );
        return new AppConfigurationEntry[]{appConfigurationEntry};
    }
}
