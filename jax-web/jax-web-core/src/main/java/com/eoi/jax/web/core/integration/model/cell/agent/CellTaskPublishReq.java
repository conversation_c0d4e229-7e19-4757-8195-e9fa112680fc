package com.eoi.jax.web.core.integration.model.cell.agent;


import com.eoi.jax.web.core.integration.model.cell.CellClusterBaseRequestInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public class CellTaskPublishReq extends CellClusterBaseRequestInfo {

    private String source;

    private List<Agents> agents;


    public static class Agents {
        private Long version;
        private Long agentId;
        private List<CellTasks> tasks;

        public Long getVersion() {
            return version;
        }

        public void setVersion(Long version) {
            this.version = version;
        }

        public Long getAgentId() {
            return agentId;
        }

        public void setAgentId(Long agentId) {
            this.agentId = agentId;
        }

        public List<CellTasks> getTasks() {
            return tasks;
        }

        public void setTasks(List<CellTasks> tasks) {
            this.tasks = tasks;
        }

        @Override
        public String toString() {
            return "Agents{" +
                    "version=" + version +
                    ", agentId=" + agentId +
                    ", tasks=" + tasks +
                    '}';
        }
    }

    public static class CellTasks {
        private Long id;
        private String name;
        private Boolean enabled;
        private String global;
        private String inputs;
        private String outputs;
        private String mapping;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public String getGlobal() {
            return global;
        }

        public void setGlobal(String global) {
            this.global = global;
        }

        public String getInputs() {
            return inputs;
        }

        public void setInputs(String inputs) {
            this.inputs = inputs;
        }

        public String getOutputs() {
            return outputs;
        }

        public void setOutputs(String outputs) {
            this.outputs = outputs;
        }

        public String getMapping() {
            return mapping;
        }

        public void setMapping(String mapping) {
            this.mapping = mapping;
        }

        @Override
        public String toString() {
            return "CellTasks{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", enabled=" + enabled +
                    ", inputs='" + inputs + '\'' +
                    ", outputs='" + outputs + '\'' +
                    ", mapping='" + mapping + '\'' +
                    '}';
        }
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public List<Agents> getAgents() {
        return agents;
    }

    public void setAgents(List<Agents> agents) {
        this.agents = agents;
    }

    @Override
    public String toString() {
        return "CellTaskPublishReq{" +
                "source='" + source + '\'' +
                ", agents=" + agents +
                '}';
    }
}
