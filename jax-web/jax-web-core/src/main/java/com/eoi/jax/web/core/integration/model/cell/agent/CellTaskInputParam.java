package com.eoi.jax.web.core.integration.model.cell.agent;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/7
 */
public class CellTaskInputParam {

    public abstract static class AbstractInput {

    }

    public static class FileInput extends AbstractInput {
        /*--------------------额外字段-----------------------*/
        private String id;

        private Boolean enable;

        private String mode;

        private Map<String, String> fields;

        /*-------------------------------------------*/


        /**
         * 文件路径
         */
        private String path;
        /**
         * 是否包含子文件
         */
        private Boolean recursive;
        /**
         * 字符集
         */
        private String charset;
        /**
         * 多行合并
         */
        private String multiline;
        /**
         * 文件白名单
         */
        private List<String> whitelist;
        /**
         * 文件黑名单
         */
        private List<String> blacklist;
        /**
         * 包含内容
         */
        private List<String> include;
        /**
         * 排除文件内容
         */
        private List<String> exclude;
        /**
         * 不采集此时间前的文件
         */
        private Long ignoreOlderThan;
        /**
         * 仅采集增量数据
         */
        private Boolean incremental;
        /**
         * 文件重命名检查
         */
        private Boolean renamecheck;
        /**
         * 文件尾无结束标志时等待
         */
        private Integer flushPartial;
        /**
         * 已经打开文件检查新数据间隔
         */
        private Integer pollInterval;
        /**
         * 检测新文件产生的时间间隔
         */
        private Integer dircheckInterval;
        /**
         * 已读文件延后检查
         * backoffFactor
         * maxBackoff
         */
        private Integer backoff;
        /**
         * 已读文件延后检查间隔增长因子,前端无需配置
         */
        private Integer backoffFactor;
        /**
         * 已读文件延后检查间隔增长上限,前端无需配置
         */
        private Integer maxBackoff;
        /**
         * 文件读后处理，NONE、RENAME、DELETE
         */
        private String actionWhenEof;
        /**
         * 文件读后处理等待时间
         */
        private Long actionMinWait;
        /**
         * 日志定时采集
         */
        private String workTimeInterval;
        /**
         * 缓存刷新间隔
         */
        private Integer flushCacheInterval;
        /**
         * 日志哈希标识
         */
        private Boolean hashEnable;
        /**
         * 哈希计算长度
         */
        private Integer hashDataLength;
        /**
         * 过滤小于“哈希计算长度”的文件
         */
        private Boolean hashWaitData;
        /**
         * 同时采集文件数量
         */
        private Integer openfiles;
        /**
         * 单条消息最大长度
         */
        private Integer msgMaxLength;
        /**
         * 单次发送最大消息数量
         */
        private Integer batch;
        /**
         * 指定文件采集顺序
         */
        private String openFileSort;
        /**
         * 忽略软链接文件
         */
        @JsonProperty(value = "IgnoreSymlink")
        private Boolean ignoreSymlink;
        /**
         * 网络带宽权重
         */
        private Integer bandwidthweight;
        /**
         * 海量文件处理策略,当为true时，才展示maxAddFiles、fileExpiredTime、dirExpiredTime、expiredCheckInterval四个参数
         */
        private Boolean fileExpiredEnable;
        /**
         * 单次扫描最多文件数
         */
        private Integer maxAddFiles;
        /**
         * 文件过期时间
         */
        private Integer fileExpiredTime;
        /**
         * 目录过期时间
         */
        private Integer dirExpiredTime;
        /**
         * 过期文件是否存在检查间隔
         */
        private Integer expiredCheckInterval;
        /**
         * 多行合并等待
         */
        private Boolean waitOnEof;
        /**
         * 这个参数不在前端展示，但是这个参数cell接口需要，固定为true
         */
        private Boolean savepos;
        /**
         *
         */
        private List<String> innerWhiteList = new ArrayList<>();
        /**
         *
         */
        private List<String> innerBlackList = new ArrayList<>();

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public Map<String, String> getFields() {
            return fields;
        }

        public void setFields(Map<String, String> fields) {
            this.fields = fields;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public Boolean getRecursive() {
            return recursive;
        }

        public void setRecursive(Boolean recursive) {
            this.recursive = recursive;
        }

        public String getCharset() {
            return charset;
        }

        public void setCharset(String charset) {
            this.charset = charset;
        }

        public String getMultiline() {
            return multiline;
        }

        public void setMultiline(String multiline) {
            this.multiline = multiline;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }

        public List<String> getBlacklist() {
            return blacklist;
        }

        public void setBlacklist(List<String> blacklist) {
            this.blacklist = blacklist;
        }

        public List<String> getInclude() {
            return include;
        }

        public void setInclude(List<String> include) {
            this.include = include;
        }

        public List<String> getExclude() {
            return exclude;
        }

        public void setExclude(List<String> exclude) {
            this.exclude = exclude;
        }

        public Long getIgnoreOlderThan() {
            return ignoreOlderThan;
        }

        public void setIgnoreOlderThan(Long ignoreOlderThan) {
            this.ignoreOlderThan = ignoreOlderThan;
        }

        public Boolean getIncremental() {
            return incremental;
        }

        public void setIncremental(Boolean incremental) {
            this.incremental = incremental;
        }

        public Boolean getRenamecheck() {
            return renamecheck;
        }

        public void setRenamecheck(Boolean renamecheck) {
            this.renamecheck = renamecheck;
        }

        public Integer getFlushPartial() {
            return flushPartial;
        }

        public void setFlushPartial(Integer flushPartial) {
            this.flushPartial = flushPartial;
        }

        public Integer getPollInterval() {
            return pollInterval;
        }

        public void setPollInterval(Integer pollInterval) {
            this.pollInterval = pollInterval;
        }

        public Integer getDircheckInterval() {
            return dircheckInterval;
        }

        public void setDircheckInterval(Integer dircheckInterval) {
            this.dircheckInterval = dircheckInterval;
        }

        public Integer getBackoff() {
            return backoff;
        }

        public void setBackoff(Integer backoff) {
            this.backoff = backoff;
        }

        public Integer getBackoffFactor() {
            return backoffFactor;
        }

        public void setBackoffFactor(Integer backoffFactor) {
            this.backoffFactor = backoffFactor;
        }

        public Integer getMaxBackoff() {
            return maxBackoff;
        }

        public void setMaxBackoff(Integer maxBackoff) {
            this.maxBackoff = maxBackoff;
        }

        public String getActionWhenEof() {
            return actionWhenEof;
        }

        public void setActionWhenEof(String actionWhenEof) {
            this.actionWhenEof = actionWhenEof;
        }

        public Long getActionMinWait() {
            return actionMinWait;
        }

        public void setActionMinWait(Long actionMinWait) {
            this.actionMinWait = actionMinWait;
        }

        public String getWorkTimeInterval() {
            return workTimeInterval;
        }

        public void setWorkTimeInterval(String workTimeInterval) {
            this.workTimeInterval = workTimeInterval;
        }

        public Integer getFlushCacheInterval() {
            return flushCacheInterval;
        }

        public void setFlushCacheInterval(Integer flushCacheInterval) {
            this.flushCacheInterval = flushCacheInterval;
        }

        public Boolean getHashEnable() {
            return hashEnable;
        }

        public void setHashEnable(Boolean hashEnable) {
            this.hashEnable = hashEnable;
        }

        public Integer getHashDataLength() {
            return hashDataLength;
        }

        public void setHashDataLength(Integer hashDataLength) {
            this.hashDataLength = hashDataLength;
        }

        public Boolean getHashWaitData() {
            return hashWaitData;
        }

        public void setHashWaitData(Boolean hashWaitData) {
            this.hashWaitData = hashWaitData;
        }

        public Integer getOpenfiles() {
            return openfiles;
        }

        public void setOpenfiles(Integer openfiles) {
            this.openfiles = openfiles;
        }

        public Integer getMsgMaxLength() {
            return msgMaxLength;
        }

        public void setMsgMaxLength(Integer msgMaxLength) {
            this.msgMaxLength = msgMaxLength;
        }

        public Integer getBatch() {
            return batch;
        }

        public void setBatch(Integer batch) {
            this.batch = batch;
        }

        public String getOpenFileSort() {
            return openFileSort;
        }

        public void setOpenFileSort(String openFileSort) {
            this.openFileSort = openFileSort;
        }

        public Boolean getIgnoreSymlink() {
            return ignoreSymlink;
        }

        public void setIgnoreSymlink(Boolean ignoreSymlink) {
            this.ignoreSymlink = ignoreSymlink;
        }

        public Integer getBandwidthweight() {
            return bandwidthweight;
        }

        public void setBandwidthweight(Integer bandwidthweight) {
            this.bandwidthweight = bandwidthweight;
        }

        public Boolean getFileExpiredEnable() {
            return fileExpiredEnable;
        }

        public void setFileExpiredEnable(Boolean fileExpiredEnable) {
            this.fileExpiredEnable = fileExpiredEnable;
        }

        public Integer getMaxAddFiles() {
            return maxAddFiles;
        }

        public void setMaxAddFiles(Integer maxAddFiles) {
            this.maxAddFiles = maxAddFiles;
        }

        public Integer getFileExpiredTime() {
            return fileExpiredTime;
        }

        public void setFileExpiredTime(Integer fileExpiredTime) {
            this.fileExpiredTime = fileExpiredTime;
        }

        public Integer getDirExpiredTime() {
            return dirExpiredTime;
        }

        public void setDirExpiredTime(Integer dirExpiredTime) {
            this.dirExpiredTime = dirExpiredTime;
        }

        public Integer getExpiredCheckInterval() {
            return expiredCheckInterval;
        }

        public void setExpiredCheckInterval(Integer expiredCheckInterval) {
            this.expiredCheckInterval = expiredCheckInterval;
        }

        public Boolean getWaitOnEof() {
            return waitOnEof;
        }

        public void setWaitOnEof(Boolean waitOnEof) {
            this.waitOnEof = waitOnEof;
        }

        public Boolean getSavepos() {
            return savepos;
        }

        public void setSavepos(Boolean savepos) {
            this.savepos = savepos;
        }

        public List<String> getInnerWhiteList() {
            return innerWhiteList;
        }

        public void setInnerWhiteList(List<String> innerWhiteList) {
            this.innerWhiteList = innerWhiteList;
        }

        public List<String> getInnerBlackList() {
            return innerBlackList;
        }

        public void setInnerBlackList(List<String> innerBlackList) {
            this.innerBlackList = innerBlackList;
        }

    }

    public static class TcpUdpSyslogInput extends AbstractInput {

        private String id;
        private Boolean enable;
        private Map<String, String> fields;
        /**
         * 格式
         */
        private String format;
        /**
         * 多行合并
         */
        private String multiline;
        /**
         * ip地址
         */
        private String host;
        /**
         * 端口号
         */
        private Integer port;
        /**
         * 字符集
         */
        private String charset;
        /**
         * 包含
         */
        private List<Map<String, String>> includes;
        /**
         * 排除
         */
        private List<Map<String, String>> excludes;
        /**
         * 网络带宽
         */
        private Integer bandwidthweight;


        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public Map<String, String> getFields() {
            return fields;
        }

        public void setFields(Map<String, String> fields) {
            this.fields = fields;
        }

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }

        public String getMultiline() {
            return multiline;
        }

        public void setMultiline(String multiline) {
            this.multiline = multiline;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getCharset() {
            return charset;
        }

        public void setCharset(String charset) {
            this.charset = charset;
        }

        public List<Map<String, String>> getIncludes() {
            return includes;
        }

        public void setIncludes(List<Map<String, String>> includes) {
            this.includes = includes;
        }

        public List<Map<String, String>> getExcludes() {
            return excludes;
        }

        public void setExcludes(List<Map<String, String>> excludes) {
            this.excludes = excludes;
        }

        public Integer getBandwidthweight() {
            return bandwidthweight;
        }

        public void setBandwidthweight(Integer bandwidthweight) {
            this.bandwidthweight = bandwidthweight;
        }
    }

    public static class KafkaInput extends AbstractInput {

        private List<String> brokers;

        @JsonProperty(value = "initial_offset")
        private String initialOffset;

        private List<String> topics;

        private Map<String, Object> fields;

        private String groupId;

        private Map<String, Object> security;

        public List<String> getBrokers() {
            return brokers;
        }

        public void setBrokers(List<String> brokers) {
            this.brokers = brokers;
        }

        public String getInitialOffset() {
            return initialOffset;
        }

        public void setInitialOffset(String initialOffset) {
            this.initialOffset = initialOffset;
        }

        public List<String> getTopics() {
            return topics;
        }

        public void setTopics(List<String> topics) {
            this.topics = topics;
        }

        public Map<String, Object> getFields() {
            return fields;
        }

        public void setFields(Map<String, Object> fields) {
            this.fields = fields;
        }

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public Map<String, Object> getSecurity() {
            return security;
        }

        public void setSecurity(Map<String, Object> security) {
            this.security = security;
        }
    }

    public static class ArchiveInput extends AbstractInput {
        /*--------------------额外字段-----------------------*/
        private String id;

        private Boolean enable;

        private String mode;

        private Map<String, String> fields;

        /*-------------------------------------------*/


        /**
         * 文件路径
         */
        private String path;
        /**
         * 是否包含子文件
         */
        private Boolean recursive;
        /**
         * 字符集
         */
        private String charset;
        /**
         * 多行合并
         */
        private String multiline;
        /**
         * 文件白名单
         */
        private List<String> whitelist;
        /**
         * 文件黑名单
         */
        private List<String> blacklist;
        /**
         * 包含内容
         */
        private List<String> include;
        /**
         * 排除文件内容
         */
        private List<String> exclude;
        /**
         * 指定文件采集顺序
         */
        private String openFileSort;
        /**
         * 单条消息最大长度
         */
        private Integer msgMaxLength;
        /**
         * 这个参数不在前端展示，但是这个参数cell接口需要，固定为true
         */
        private Boolean savepos;
        /**
         * 是否忽略cache
         */
        private Boolean ignoreLastCache;
        /**
         *
         */
        private List<String> innerWhiteList = new ArrayList<>();
        /**
         *
         */
        private List<String> innerBlackList = new ArrayList<>();

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public Map<String, String> getFields() {
            return fields;
        }

        public void setFields(Map<String, String> fields) {
            this.fields = fields;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public Boolean getRecursive() {
            return recursive;
        }

        public void setRecursive(Boolean recursive) {
            this.recursive = recursive;
        }

        public String getCharset() {
            return charset;
        }

        public void setCharset(String charset) {
            this.charset = charset;
        }

        public String getMultiline() {
            return multiline;
        }

        public void setMultiline(String multiline) {
            this.multiline = multiline;
        }

        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }

        public List<String> getBlacklist() {
            return blacklist;
        }

        public void setBlacklist(List<String> blacklist) {
            this.blacklist = blacklist;
        }

        public List<String> getInclude() {
            return include;
        }

        public void setInclude(List<String> include) {
            this.include = include;
        }

        public List<String> getExclude() {
            return exclude;
        }

        public void setExclude(List<String> exclude) {
            this.exclude = exclude;
        }

        public String getOpenFileSort() {
            return openFileSort;
        }

        public void setOpenFileSort(String openFileSort) {
            this.openFileSort = openFileSort;
        }

        public Integer getMsgMaxLength() {
            return msgMaxLength;
        }

        public void setMsgMaxLength(Integer msgMaxLength) {
            this.msgMaxLength = msgMaxLength;
        }

        public Boolean getSavepos() {
            return savepos;
        }

        public void setSavepos(Boolean savepos) {
            this.savepos = savepos;
        }

        public Boolean getIgnoreLastCache() {
            return ignoreLastCache;
        }

        public void setIgnoreLastCache(Boolean ignoreLastCache) {
            this.ignoreLastCache = ignoreLastCache;
        }

        public List<String> getInnerWhiteList() {
            return innerWhiteList;
        }

        public void setInnerWhiteList(List<String> innerWhiteList) {
            this.innerWhiteList = innerWhiteList;
        }

        public List<String> getInnerBlackList() {
            return innerBlackList;
        }

        public void setInnerBlackList(List<String> innerBlackList) {
            this.innerBlackList = innerBlackList;
        }
    }

    public static class WinEventLogInput extends AbstractInput {
        /*--------------------额外字段-----------------------*/
        private String id;

        private Boolean enable;

        private Map<String, String> fields;

        /*-------------------------------------------*/

        /**
         * query语句
         */
        private List<String> query;

        /**
         * 是否为utf8
         */
        private Boolean utf8;

        private Boolean incremental;

        private Integer pollinterval;

        private Boolean savepos;


        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public Map<String, String> getFields() {
            return fields;
        }

        public void setFields(Map<String, String> fields) {
            this.fields = fields;
        }

        public List<String> getQuery() {
            return query;
        }

        public void setQuery(List<String> query) {
            this.query = query;
        }

        public Boolean getUtf8() {
            return utf8;
        }

        public void setUtf8(Boolean utf8) {
            this.utf8 = utf8;
        }

        public Boolean getIncremental() {
            return incremental;
        }

        public void setIncremental(Boolean incremental) {
            this.incremental = incremental;
        }

        public Integer getPollinterval() {
            return pollinterval;
        }

        public void setPollinterval(Integer pollinterval) {
            this.pollinterval = pollinterval;
        }

        public Boolean getSavepos() {
            return savepos;
        }

        public void setSavepos(Boolean savepos) {
            this.savepos = savepos;
        }
    }

    public static class JdbcInput extends AbstractInput {
        /*--------------------额外字段-----------------------*/
        private String id;

        private Boolean enable;

        private Map<String, String> fields;

        /*-------------------------------------------*/

        /**
         * 数据库驱动
         */
        @JsonProperty("jdbc_driver_class")
        @JsonAlias(value = "jdbc_driver_class")
        private String jdbcDriverClass;

        /**
         * 数据库连接参数
         */
        @JsonProperty("jdbc_connection_string")
        @JsonAlias(value = "jdbc_connection_string")
        private String jdbcConnectionString;

        /**
         * 数据库用户名
         */
        @JsonProperty("jdbc_user")
        @JsonAlias(value = "jdbc_user")
        private String jdbcUser;

        /**
         * 数据库密码
         */
        @JsonProperty("jdbc_password")
        @JsonAlias(value = "jdbc_password")
        private String jdbcPassword;

        /**
         * JDBC JAR
         */
        @JsonProperty("jdbc_driver_library")
        @JsonAlias(value = "jdbc_driver_library")
        private String jdbcDriverLibrary;

        /**
         * 采集周期
         */
        @JsonProperty("cron_schedule")
        @JsonAlias(value = "cron_schedule")
        private String cronSchedule;

        /**
         * 采集SQL语句
         */
        @JsonProperty("sql_statement")
        @JsonAlias(value = "sql_statement")
        private String sqlStatement;

        /**
         * 增量字段类型
         */
        @JsonProperty("tracking_column_type")
        @JsonAlias(value = "tracking_column_type")
        private String trackingColumnType;

        /**
         * 增量字段
         */
        @JsonProperty("tracking_column")
        @JsonAlias(value = "tracking_column")
        private String trackingColumn;

        /**
         * 同步模式
         */
        private String syncMode;

        private String increment;

        /**
         * 单个字段字段编码
         */
        private Map<String, Object> columnsCharset;

        /**
         * 采集模式
         */
        private Integer mode;


        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public Map<String, String> getFields() {
            return fields;
        }

        public void setFields(Map<String, String> fields) {
            this.fields = fields;
        }

        public String getJdbcDriverClass() {
            return jdbcDriverClass;
        }

        public void setJdbcDriverClass(String jdbcDriverClass) {
            this.jdbcDriverClass = jdbcDriverClass;
        }

        public String getJdbcConnectionString() {
            return jdbcConnectionString;
        }

        public void setJdbcConnectionString(String jdbcConnectionString) {
            this.jdbcConnectionString = jdbcConnectionString;
        }

        public String getJdbcUser() {
            return jdbcUser;
        }

        public void setJdbcUser(String jdbcUser) {
            this.jdbcUser = jdbcUser;
        }

        public String getJdbcPassword() {
            return jdbcPassword;
        }

        public void setJdbcPassword(String jdbcPassword) {
            this.jdbcPassword = jdbcPassword;
        }

        public String getJdbcDriverLibrary() {
            return jdbcDriverLibrary;
        }

        public void setJdbcDriverLibrary(String jdbcDriverLibrary) {
            this.jdbcDriverLibrary = jdbcDriverLibrary;
        }

        public String getCronSchedule() {
            return cronSchedule;
        }

        public void setCronSchedule(String cronSchedule) {
            this.cronSchedule = cronSchedule;
        }

        public String getSqlStatement() {
            return sqlStatement;
        }

        public void setSqlStatement(String sqlStatement) {
            this.sqlStatement = sqlStatement;
        }

        public String getTrackingColumnType() {
            return trackingColumnType;
        }

        public void setTrackingColumnType(String trackingColumnType) {
            this.trackingColumnType = trackingColumnType;
        }

        public String getTrackingColumn() {
            return trackingColumn;
        }

        public void setTrackingColumn(String trackingColumn) {
            this.trackingColumn = trackingColumn;
        }

        public String getSyncMode() {
            return syncMode;
        }

        public void setSyncMode(String syncMode) {
            this.syncMode = syncMode;
        }

        public String getIncrement() {
            return increment;
        }

        public void setIncrement(String increment) {
            this.increment = increment;
        }

        public Map<String, Object> getColumnsCharset() {
            return columnsCharset;
        }

        public void setColumnsCharset(Map<String, Object> columnsCharset) {
            this.columnsCharset = columnsCharset;
        }

        public Integer getMode() {
            return mode;
        }

        public void setMode(Integer mode) {
            this.mode = mode;
        }
    }


    public static class LogstashInput extends AbstractInput {

        private String config;

        public String getConfig() {
            return config;
        }

        public void setConfig(String config) {
            this.config = config;
        }
    }

}
