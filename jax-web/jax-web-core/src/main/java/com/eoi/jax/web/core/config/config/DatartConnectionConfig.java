package com.eoi.jax.web.core.config.config;

import cn.hutool.core.lang.Console;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.base.param.InputType;
import com.eoi.jax.base.param.Parameter;
import com.eoi.jax.base.param.ParameterMeta;
import com.eoi.jax.base.param.ParameterUtil;

import java.util.List;

public class DatartConnectionConfig {

    @Parameter(
            label = "datart地址",
            description = "格式为：http://<ip>:<port>",
            optional = false
    )
    private String address;

    @Parameter(
            label = "登录账号",
            description = "登录datart的账号",
            optional = false
    )
    private String user;


    @Parameter(
            label = "登录密码",
            description = "登录datart的密码",
            optional = false,
            inputType = InputType.PASSWORD
    )
    private String password;


    @Parameter(
            label = "归属租户",
            description = "同步数据到datart时，归属的租户，填名字即可",
            optional = true
    )
    private String org;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOrg() {
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public static void main(String[] args) throws Exception {
        List<ParameterMeta> parameterMetas = ParameterUtil.analyzeParameters(new DatartConnectionConfig());
        Console.print(JSONUtil.toJsonStr(parameterMetas));
    }
}
