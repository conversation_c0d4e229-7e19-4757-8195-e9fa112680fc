package com.eoi.jax.web.core.util;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/4/23 9:57
 */
public class MetricUtil {
    private static final List<Integer> INTERVALS
            = Arrays.asList(
                new Integer[]{1, 2, 5, 10, 15, 20, 30, 60, 120, 300, 600, 900, 1200, 1800, 3600, 7200, 18000, 36000, 54000, 72000, 108000});


    /**
     * 获取普罗米修斯步长step（默认最小15s）
     *
     * @param beginTime
     * @param endTime
     * @param width
     * @return
     */
    public static int getPrometheusStep(Date beginTime, Date endTime, int width) {
        int timeRange = (int) ((endTime.getTime() - beginTime.getTime()) / 1000);
        return calStep(timeRange, width, 15);
    }

    /**
     * 获取普罗米修斯步长step（默认最小15s）
     *
     * @param timeRange
     * @param width
     * @return
     */
    public static int getPrometheusStep(int timeRange, int width) {
        return calStep(timeRange, width, 15);
    }

    /**
     * 计算step
     *
     * @param timeRange
     * @param width
     * @param minStep
     * @return
     */
    public static int calStep(int timeRange, int width, int minStep) {
        int interval = timeRange / width;
        int maxStep = INTERVALS.get(INTERVALS.size() - 1);
        if (interval >= maxStep) {
            return maxStep;
        }

        if (minStep <= 0) {
            minStep = INTERVALS.get(0);
        }
        if (interval <= minStep) {
            return minStep;
        }

        int step = minStep;
        for (int i = 0, len = INTERVALS.size() - 1; i < len; i++) {
            int cur = INTERVALS.get(i);
            int next = INTERVALS.get(i + 1);
            if (interval >= cur && interval <= next) {
                int mid = (next - cur) / 2 + cur;
                if (interval <= mid) {
                    step = cur;
                } else {
                    step = next;
                }
                break;
            }
        }
        return step;
    }

}
