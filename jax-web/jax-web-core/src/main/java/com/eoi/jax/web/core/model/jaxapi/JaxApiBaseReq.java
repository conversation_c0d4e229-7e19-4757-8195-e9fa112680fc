package com.eoi.jax.web.core.model.jaxapi;

import java.io.Serializable;

/**
 * <AUTHOR> zsc
 * @create 2024/3/10 22:07
 */
public class JaxApiBaseReq implements Serializable {

    private String ip;

    private Integer port;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

}
