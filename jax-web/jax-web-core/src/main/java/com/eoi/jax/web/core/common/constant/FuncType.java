package com.eoi.jax.web.core.common.constant;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Date: 2022/12/6
 **/
public enum FuncType implements ICodeEnum {
    /**
     * 求和
     */
    SUM("SUM", "求和"),
    /**
     * 平均值
     */
    AVG("AVG", "平均值"),
    /**
     * 计数
     */
    COUNT("COUNT", "计数"),
    /**
     * 最小值
     */
    MIN("MIN", "最小值"),
    /**
     * 最大值
     */
    MAX("MAX", "最大值"),
    /**
     * 第一个
     */
    FIRST("FIRST", "第一个"),
    /**
     * 最后一个
     */
    LAST("LAST", "最后一个"),
    /**
     * 其它
     */
    OTHER("OTHER", "其它");


    private final String code;
    private final String name;

    public String getName() {
        return name;
    }

    FuncType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }


    /**
     * 根据code 查找枚举类
     *
     * @param code code值
     * @return UnitType
     */
    public static FuncType getEnumByCode(String code) {
        for (FuncType value : FuncType.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 组装code,name值, 保证有序
     *
     * @return
     */
    public static Map<String, String> valueList() {
        return Arrays.stream(FuncType.values()).collect(LinkedHashMap::new,
                (map, item) -> map.put(item.code(), item.getName()), Map::putAll);
    }
}
