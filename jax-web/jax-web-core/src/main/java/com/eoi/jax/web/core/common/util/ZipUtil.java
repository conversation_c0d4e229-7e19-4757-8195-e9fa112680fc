package com.eoi.jax.web.core.common.util;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtil {

    public static void addDir(ZipOutputStream zip, String path) throws IOException {
        path = StrUtil.addSuffixIfNot(path, StrUtil.SLASH);
        zip.putNextEntry(new ZipEntry(path));
        zip.closeEntry();
    }

    public static void addFile(ZipOutputStream zip, String name, String path) throws IOException {
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            addFile(zip, name, new FileInputStream(file));
        }
    }

    public static void addFile(ZipOutputStream zip, String name, byte[] bytes) throws IOException {
        zip.putNextEntry(new ZipEntry(name));
        zip.write(bytes);
        zip.closeEntry();
    }

    public static void addFile(ZipOutputStream zip, String name, InputStream in) throws IOException {
        zip.putNextEntry(new ZipEntry(name));
        IoUtil.copy(in, zip);
        zip.closeEntry();
        in.close();
    }
}
