package com.eoi.jax.web.core.common.enumrate;

import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: tangy
 * @Date: 2023/7/26
 * @Desc:
 **/
public enum ApiStatusEnum implements ICodeMessageEnum {
    DRAFT("DRAFT", "草稿"),
    ONLINE("ONLINE", "已上线"),
    OFFLINE("OFFLINE", "已下线");

    ApiStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private final String code;

    private final String message;

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public boolean equals(String code) {
        return ICodeMessageEnum.super.equals(code);
    }

    @Override
    public boolean notEquals(String code) {
        return ICodeMessageEnum.super.notEquals(code);
    }

    @Override
    public String message() {
        return message;
    }

    public static ApiStatusEnum fromString(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (ApiStatusEnum value : ApiStatusEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }
    public static String getMessageByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (ApiStatusEnum value : ApiStatusEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }
}
