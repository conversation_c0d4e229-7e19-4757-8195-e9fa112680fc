package com.eoi.jax.web.core.common.exception;

import com.eoi.jax.web.core.common.constant.ResponseCode;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
public class JaxApiException extends BizException {
    public JaxApiException(ResponseCode code) {
        super(code);
    }

    public JaxApiException(ResponseCode code, Throwable cause) {
        super(code, cause);
    }

    public JaxApiException(ResponseCode code, Object info) {
        super(code, info);
    }

    public JaxApiException(ResponseCode code, Throwable cause, Object entity) {
        super(code, cause, entity);
    }

    public JaxApiException(String code, String message) {
        super(code, message);
    }

    public JaxApiException(String code, String message, Object entity) {
        super(code, message, entity);
    }

    public JaxApiException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public JaxApiException(String code, String message, Throwable cause, Object entity) {
        super(code, message, cause, entity);
    }
}
