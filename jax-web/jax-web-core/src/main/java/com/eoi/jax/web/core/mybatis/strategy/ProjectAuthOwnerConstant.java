package com.eoi.jax.web.core.mybatis.strategy;

/**
 * @Author: tangy
 * @Date: 2023/10/12
 * @Desc: 资源归属项目常量类
 **/
public class ProjectAuthOwnerConstant {
    /**
     * 项目资源权限关系表
     */
    public static final String PROJECT_RESOURCE_TABLE_NAME = "tb_project_resource";
    /**
     * 别名
     */
    public static final String PROJECT_RESOURCE_ALIAS = "_data_project_owner";
    /**
     * 关联字段
     */
    public static final String RESOURCE_ID_COLUMN = "resource_id";
    /**
     * 资源类型字段
     */
    public static final String RESOURCE_TYPE_COLUMN = "resource_type";
    /**
     * 项目id字段
     */
    public static final String PROJECT_ID_COLUMN = "project_id";
    /**
     * 项目id字段
     */
    public static final String PROJECT_IS_OWNER_COLUMN = "is_owner";
    /**
     * 额外返回字段
     */
    public static final String[] PROJECT_AUTHORITY_EXT_COLUMN = new String[]{"project_id"};

    /**
     * 列前缀
     */
    public static final String COLUMN_PREFIX = "project_auth_owner_";
}
