package com.eoi.jax.web.core.security;

import java.io.Serializable;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2023/4/7
 */
public interface User extends Serializable {

    /**
     * 用户唯一标识
     * <p>
     * 需要为用户表主键、账号或生份证号等具体唯一约束性质的字段
     * 在系统数据库中唯一标识具有唯一约束
     *
     * @return
     */
    String getIdentity();

    /**
     * 用户名
     * <p>
     * 用户的名字
     * 用于展示系统登录人、操作人等信息
     *
     * @return
     */
    String getName();

    /**
     * 用户全量信息
     *
     * 用于判断用户信息是否发生变化
     * @return
     */
    default String toInfoString() {
        return "User(identity=" + this.getIdentity() + ", name=" + this.getName() + ")";
    }
}
