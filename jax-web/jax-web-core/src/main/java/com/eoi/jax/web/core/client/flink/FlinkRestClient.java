package com.eoi.jax.web.core.client.flink;

import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.eoi.jax.web.core.common.exception.JaxException;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.JsonUtil;

/**
 * <AUTHOR>
 * @date 2023/3/12
 */
public class FlinkRestClient {
    private static final int CONNECTION_TIMEOUT = 2000;
    private static final int SOCKET_TIMEOUT = 5000;
    private final String server;

    public FlinkRestClient(String server) {
        this.server = server;
    }

    private HttpRequest request(HttpRequest httpRequest) {
        httpRequest.setConnectionTimeout(CONNECTION_TIMEOUT);
        httpRequest.setReadTimeout(SOCKET_TIMEOUT);
        return httpRequest;
    }

    public FlinkOverview getOverview() {
        HttpResponse httpResponse = null;
        try {
            String url = Common.urlPathsJoin(server, "/overview");
            httpResponse = request(HttpRequest.get(url).contentType("application/json")).execute();
            if (!httpResponse.isOk()) {
                throw new JaxException("http code " + httpResponse.getStatus());
            }
            String respBody = httpResponse.body();
            FlinkOverview overview = JsonUtil.decode(respBody, FlinkOverview.class);
            return overview;
        } finally {
            IoUtil.close(httpResponse);
        }
    }
}
