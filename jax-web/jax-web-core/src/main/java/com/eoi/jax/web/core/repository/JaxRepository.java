package com.eoi.jax.web.core.repository;


import com.eoi.jax.web.repository.mapper.*;
import com.eoi.jax.web.repository.service.*;

/**
 * <AUTHOR>
 * @date 2022/9/30
 */
public interface JaxRepository {

    /**
     * DataBusiness Repository
     *
     * @return
     */
    TbBusinessCategoryService businessCategory();

    /**
     * rule Repository
     *
     * @return
     */
    TbCheckRuleService checkRule();

    /**
     * layer Repository
     *
     * @return
     */
    TbWarehouseLayerService layer();

    /**
     * folder Repository
     *
     * @return
     */
    TbDictFolderService folder();

    /**
     * 标准集
     * dictGroup Repository
     *
     * @return
     */
    TbDictGroupService dictGroup();

    /**
     * 标准
     * columnDict Repository
     *
     * @return
     */
    TbColumnDictService columnDict();

    /**
     * 标准代码
     * columnDict Repository
     *
     * @return
     */
    TbDictEnumService enumDict();

    /**
     * 标准代码值
     * measure Repository
     *
     * @return
     */
    TbMeasureUnitService measure();

    /**
     * 命名字典
     * nameDict Repository
     *
     * @return
     */
    TbNameDictService name();

    /**
     * 标准代码值
     * enumValue Repository
     *
     * @return
     */
    TbEnumValueService enumValue();

    /**
     * 维度
     * dimension Repository
     *
     * @return
     */
    TbDimensionService dimension();

    /**
     * 数据集市
     * dataMart Repository
     *
     * @return
     */
    TbDataMartService dataMart();

    /**
     * 主题域
     * dataSubject Repository
     *
     * @return
     */
    TbDataSubjectService dataSubject();

    /**
     * 业务分类关联数据域
     *
     * @return
     */
    VDataDomainBusinessCategoryMapper vDomainCategory();

    /**
     * 表
     * table Repository
     *
     * @return
     */
    TbTableService table();

    /**
     * 业务过程
     * businessProcess Repository
     *
     * @return
     */
    TbBusinessProcessService businessProcessService();

    /**
     * 维度建模，列属性关联表
     *
     * @return
     */
    VColumnMapper vColumn();

    /**
     * 数据域和业务域关联表
     *
     * @return
     */
    TbDataDomainBusinessCategoryService dataDomainBusinessCategoryService();

    /**
     * 数据域
     *
     * @return
     */
    TbDataDomainService dataDomainService();

    /**
     * 数据域left join 业务过程
     *
     * @return
     */
    VDataDomainBusinessProcessMapper vDataDomainBusinessProcess();

    /**
     * 时间周期
     *
     * @return
     */
    TbTimePeriodService timePeriodService();

    /**
     * 修饰词
     *
     * @return
     */
    VAdjunctMapper vAdjunctMapper();

    /**
     * 表字段
     *
     * @return
     */
    TbColumnService column();

    /**
     * 修饰指标
     *
     * @return
     */
    TbAdjunctService adj();

    /**
     * 派生指标
     *
     * @return
     */
    TbDerivativeIndicatorService derivativeIndicator();

    /**
     * 原子指标
     *
     * @return
     */
    TbAtomicIndicatorService atomicIndicator();

    /**
     * 数据中心
     *
     * @return
     */
    TbDataCenterService dataCenterService();

    /**
     * 数据中心映射关系
     *
     * @return
     */
    TbDataCenterMappingService dataCenterMappingService();

    /**
     * 网关
     *
     * @return
     */
    TbCellService cellService();

    /**
     * 数据源
     *
     * @return
     */
    TbDatasourceService datasourceService();

    /**
     * 存储集群
     *
     * @return
     */
    TbStorageClusterService storageClusterService();

    /**
     * ck存储作业
     *
     * @return
     */
    TbStorageCkService storageCkService();

    /**
     * 注册中心
     *
     * @return
     */
    TbRegisterCenterService registerCenterService();

    /**
     * 模型发布表
     *
     * @return
     */
    TbTableDeployService tableDeployService();

    /**
     * 模型发布历史表
     *
     * @return
     */
    TbTableDeployHistoryService tableDeployHistoryService();

    /**
     * 模型字段发布表
     *
     * @return
     */
    TbColumnDeployService columnDeployService();

    /**
     * 数仓分层视图
     *
     * @return
     */
    VWarehouseLayerMapper vWarehouseLayerMapper();

    /**
     * 模型发布视图
     *
     * @return
     */
    VTbTableDeployMapper vTbTableDeployMapper();

    /**
     * 数据处理
     *
     * @return
     */
    TbProcessService processService();

    /**
     * 业务流程
     *
     * @return
     */
    TbBusinessFlowService businessFlowService();

    /**
     * 数据集成job视图
     *
     * @return
     */
    VIngestionJobMapper vIngestionJobMapper();

    /**
     * 集群
     *
     * @return
     */
    TbClusterService tbClusterService();

    /**
     * 模型库
     *
     * @return
     */
    TbColumnTemplateService colTemplate();

    /**
     * 模型库字段
     *
     * @return
     */
    TbColumnTemplateDetailService colTemplateDetail();

    /**
     * 数据服务
     *
     * @return
     */
    TbApiService dataServiceApiService();

    /**
     * 对象模型
     *
     * @return
     */
    TbObjectTableService objectTable();

    /**
     * 对象种类
     *
     * @return
     */
    TbObjectCategoryService objectCategory();

    /**
     * 对象关系类型
     *
     * @return
     */
    TbObjectRelationTypeService objectRelationType();

    /**
     * 对象关系
     *
     * @return
     */
    TbObjectRelationService objectRelation();

    /**
     * 对象关系路径
     *
     * @return
     */
    TbObjectRelationPathService objectRelationPath();

    /**
     * 自监控监控规则
     *
     * @return
     */
    TbSelfMonitorAlarmRuleService selfAlarmRule();

    /**
     * 自监控监控分类
     *
     * @return
     */
    TbSelfMonitorCategoryService selfMonitorCategoryService();

    /**
     * 自监控监控项
     *
     * @return
     */
    TbSelfMonitorItemService selfMonitorItemService();

    /**
     * pipeline
     *
     * @return
     */
    TbPipelineService pipeline();

    /**
     * 告警
     *
     * @return
     */
    TbSelfMonitorAlarmService alarmService();

    /**
     * 原始告警
     *
     * @return
     */
    TbSelfMonitorEventService eventService();

    /**
     * zabbix指标模板提取规则
     *
     * @return
     */
    TbZabbixExtractRuleService zabbixExtractRule();

    /**
     * zabbix模板表
     *
     * @return
     */
    TbZabbixExtractTemplatesService zabbixTemplates();

    /**
     * blueking接入
     *
     * @return
     */
    TbBluekingIntegrationService bluekingIntegrationService();

    /**
     * 生命周期策略
     *
     * @return
     */
    TbLifecycleStrategyService lifecycleStrategyService();

    /**
     * 生命周期匹配
     *
     * @return
     */
    TbLifecycleStrategyMatchService lifecycleStrategyMatchService();

    /**
     * 生命周期日志
     * @return
     */
    TbLifecycleBatchLogService lifecycleLogService();

    /**
     * 生命周期执行器
     *
     * @return
     */
    TbLifecycleExecutorService lifecycleExecutorService();

    /**
     * 生命周期执行批次
     *
     * @return
     */
    TbLifecycleBatchService lifecycleBatchService();

    /**
     * 生命周期job
     *
     * @return
     */
    TbLifecycleBatchJobService lifecycleJobService();

    /**
     * 生命周期Task
     *
     * @return
     */
    TbLifecycleBatchTaskService lifecycleTaskService();


    /**
     * 生命周期统计
     *
     * @return
     */
    VLifecycleStatisticsMapper lifecycleStatisticsMapper();

}
