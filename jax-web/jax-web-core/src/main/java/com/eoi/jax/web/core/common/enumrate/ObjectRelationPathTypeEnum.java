package com.eoi.jax.web.core.common.enumrate;

import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: tangy
 * @Date: 2023/9/13
 * @Desc:
 **/
public enum ObjectRelationPathTypeEnum implements ICodeMessageEnum {

    CORRELATION("CORRELATION", "应用系统调用关系"),
    HOMOLOGY("HOMOLOGY", "应用系统同源关系");

    ObjectRelationPathTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private final String code;

    private final String message;

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public boolean equals(String code) {
        return ICodeMessageEnum.super.equals(code);
    }

    @Override
    public boolean notEquals(String code) {
        return ICodeMessageEnum.super.notEquals(code);
    }

    @Override
    public String message() {
        return message;
    }

    public static ObjectRelationPathTypeEnum fromString(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (ObjectRelationPathTypeEnum value : ObjectRelationPathTypeEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (ObjectRelationPathTypeEnum value : ObjectRelationPathTypeEnum.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }

    public static String getCodeByMessage(String message) {
        if (StringUtils.isEmpty(message)) {
            return null;
        }
        for (ObjectRelationPathTypeEnum value : ObjectRelationPathTypeEnum.values()) {
            if (value.message().equals(message)) {
                return value.code();
            }
        }
        return null;
    }
}
