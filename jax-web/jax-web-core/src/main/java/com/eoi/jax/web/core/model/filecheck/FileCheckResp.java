package com.eoi.jax.web.core.model.filecheck;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
public class FileCheckResp implements Serializable {

    private String serverId;

    private String serverAddress;

    private Map<String, String> md5Map;

    private Boolean basePathExist = true;


    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getServerAddress() {
        return serverAddress;
    }

    public void setServerAddress(String serverAddress) {
        this.serverAddress = serverAddress;
    }

    public Map<String, String> getMd5Map() {
        return md5Map;
    }

    public void setMd5Map(Map<String, String> md5Map) {
        this.md5Map = md5Map;
    }

    public Boolean getBasePathExist() {
        return basePathExist;
    }

    public void setBasePathExist(Boolean basePathExist) {
        this.basePathExist = basePathExist;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FileCheckResp that = (FileCheckResp) o;
        return Objects.equals(serverId, that.serverId) && Objects.equals(serverAddress, that.serverAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hash(serverId, serverAddress);
    }
}
