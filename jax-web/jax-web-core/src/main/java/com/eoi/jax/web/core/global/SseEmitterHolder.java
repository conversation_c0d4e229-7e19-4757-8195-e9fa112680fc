package com.eoi.jax.web.core.global;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2024/1/16
 */
public class SseEmitterHolder {
    private final SseEmitter sseEmitter;
    private boolean complete;
    private boolean failed;
    private Throwable failure;

    public SseEmitterHolder(SseEmitter sseEmitter) {
        this.sseEmitter = sseEmitter;
    }

    public static SseEmitter.SseEventBuilder event() {
        return SseEmitter.event();
    }

    public SseEmitter getEmitter() {
        return sseEmitter;
    }

    public boolean isComplete() {
        return complete;
    }

    public boolean isFailed() {
        return failed;
    }

    public Throwable getFailure() {
        return failure;
    }

    public synchronized void send(SseEmitter.SseEventBuilder event) throws IOException {
        sseEmitter.send(event);
    }

    public synchronized void send(String object) throws IOException {
        sseEmitter.send(object);
    }

    public synchronized void completeWithError(Throwable ex) {
        this.complete = true;
        this.failed = true;
        this.failure = ex;
        sseEmitter.completeWithError(ex);
    }

    public synchronized void complete() {
        this.complete = true;
        sseEmitter.complete();
    }
}
