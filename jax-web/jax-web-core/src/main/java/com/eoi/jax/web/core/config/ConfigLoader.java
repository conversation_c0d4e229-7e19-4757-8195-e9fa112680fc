package com.eoi.jax.web.core.config;

import com.eoi.jax.web.core.common.ContextHolder;

/**
 * <AUTHOR>
 * @date 2023/2/22
 */
public final class ConfigLoader {
    private static AppConfig config;

    private ConfigLoader() {
        // forbid init instance
    }

    public static AppConfig load() {
        if (config == null) {
            config = ContextHolder.getBean(AppConfig.class);
        }
        return config;
    }
}
