package com.eoi.jax.web.core.script.groovy;

import groovy.grape.GrabAnnotationTransformation;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.kohsuke.groovy.sandbox.SandboxTransformer;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/12/13
 */
public class GroovySandbox {
    public static CompilerConfiguration createSecureConfig() {
        CompilerConfiguration config = new CompilerConfiguration();
        config.setDisabledGlobalASTTransformations(new HashSet<>(Collections.singletonList(
                GrabAnnotationTransformation.class.getName())));
        config.addCompilationCustomizers(new RejectAstTransformsCustomizer());
        config.addCompilationCustomizers(new SandboxTransformer());
        return config;
    }

    public static GroovyShell createSecureGroovyShell() {
        CompilerConfiguration config = createSecureConfig();
        return new GroovyShell(config);
    }

    public static GroovyShell createSecureGroovyShell(Binding binding) {
        CompilerConfiguration config = createSecureConfig();
        return new GroovyShell(binding, config);
    }

    public static Object secureEvaluate(String script) {
        return secureEvaluateWithBinding(script, null);
    }

    public static Object secureEvaluateWithParam(String script, Map<String, Object> param) {
        return secureEvaluateWithBinding(script, new Binding(param));
    }

    public static Object secureEvaluateWithBinding(String script, Binding binding) {
        SandboxInterceptor interceptor = new SandboxInterceptor();
        interceptor.register();
        try {
            GroovyShell shell = null;
            if (binding != null) {
                shell = createSecureGroovyShell(binding);
            } else {
                shell = createSecureGroovyShell();
            }
            return shell.evaluate(script);
        } finally {
            interceptor.unregister();
        }
    }
}
