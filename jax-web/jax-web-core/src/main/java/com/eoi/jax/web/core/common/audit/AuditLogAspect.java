package com.eoi.jax.web.core.common.audit;

import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.HttpServletUtil;
import com.eoi.jax.web.core.common.util.IdUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.repository.entity.TbAuditLog;
import com.eoi.jax.web.repository.service.TbAuditLogService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2023/4/12
 */
@Aspect
@Component
public class AuditLogAspect {

    private static Logger logger = LoggerFactory.getLogger(AuditLogAspect.class);
    @Autowired
    private HttpServletRequest request;

    @Autowired
    private TbAuditLogService auditLogService;

    @Autowired
    private Environment environment;

    @Resource
    private SystemConfigHolder systemConfigHolder;

    @Pointcut("@annotation(com.eoi.jax.web.core.common.audit.AuditLog)")
    public void auditJoinPoint() {
        //切入点，没有业务意义
    }

    @Around("auditJoinPoint()")
    public Object handle(ProceedingJoinPoint joinPoint) throws Throwable {
        //增加请求时间
        request.setAttribute("requestTimeForAuditLogAspect", new Date());
        String throwError = null;
        Object retValue = null;
        Throwable processThrowable = null;
        try {
            retValue = joinPoint.proceed();
        } catch (Throwable e) {
            if (e instanceof BizException &&
                    ResponseCode.OPERATION_NO_PERMISSION.equals(((BizException) e).getCode())) {
                throw e;
            }
            throwError = e.getMessage();
            processThrowable = e;
            if (throwError != null && throwError.length() > 255) {
                throwError = throwError.substring(0, 255);
            }
        }
        Signature signature = joinPoint.getSignature();
        AuditLog auditLogAnnotation = ((MethodSignature) signature).getMethod().getAnnotation(AuditLog.class);
        auditRequest(request, joinPoint, retValue, auditLogAnnotation, throwError);
        if (processThrowable != null) {
            throw processThrowable;
        }
        return retValue;
    }

    public void auditRequest(HttpServletRequest request, ProceedingJoinPoint joinPoint, Object retValue,
                             AuditLog auditAnnotation, String throwError) {
        try {

            String aFalse = environment.getProperty("jax.auditLog", "false");
            Boolean enableAuditLog = systemConfigHolder.getBool("jax.auditLog",
                    "enable", aFalse.equals("true"));
            if (!enableAuditLog) {
                logger.debug("未开启审计日志记录, 如需开启请配置参数jax.auditLog=true");
                return;
            }
            Object requestTimeForAuditLogAspect = request.getAttribute("requestTimeForAuditLogAspect");
            String ip = request == null ? null : HttpServletUtil.getIpAddr(request);
            String application = request == null ? null : HttpServletUtil.getApplication(request);

            AuditLogInfo log = new AuditLogInfo();
            log.setOpTime((Date) requestTimeForAuditLogAspect);
            log.setOpIp(ip);
            log.setOpApplication(application);
            log.setOpApi(request.getMethod() + ":" + request.getRequestURI());
            if (StringUtils.isNotBlank(throwError)) {
                log.setOpErrorMsg(throwError);
                log.setOpStatus(OpStatusEnum.FAIL.code());
            } else {
                log.setOpStatus(OpStatusEnum.SUCCESS.code());
            }
            Object[] args = joinPoint.getArgs();
            dealArgsAnnotation(joinPoint, log, args);
            dealReturnValueAnnotation(retValue, auditAnnotation, log);
            log.withAnnotation(auditAnnotation);
            log.setOpErrorMsg(throwError);
            dealLast(log, joinPoint);

            TbAuditLog entity = new TbAuditLog();
            BeanUtils.copyProperties(log, entity);
            if (entity.getOpPrimaryName() == null && entity.getOpPrimaryKey() != null) {
                entity.setOpPrimaryName(String.valueOf(entity.getOpPrimaryKey()));
            }
            entity.setOpApplication(ContextHolder.getEoiApp());
            entity.setOpEndTime(new Date());
            entity.setOpUser(ContextHolder.getUserId());
            auditLogService.saveBatch(Arrays.asList(entity));
        } catch (Exception e) {
            logger.error("audit log save failed! {}", e);
        }
    }

    private void dealReturnValueAnnotation(Object retValue, AuditLog auditAnnotation, AuditLogInfo log) {
        if (retValue == null || !(retValue instanceof Response) || ((Response) retValue).getData() == null) {
            return;
        }
        Object entity = ((Response) retValue).getData();
        OpPrimaryKey keyAnnotation = entity.getClass().getAnnotation(OpPrimaryKey.class);
        OpPrimaryName nameAnnotation = entity.getClass().getAnnotation(OpPrimaryName.class);
        if (keyAnnotation != null || nameAnnotation != null) {
            String primaryKey = null;
            String primaryName = null;
            if (keyAnnotation != null) {
                primaryKey = keyAnnotation.name();
            }
            if (nameAnnotation != null) {
                primaryName = nameAnnotation.name();
            }
            Field[] fields = ReflectUtil.getFields(entity.getClass());
            for (Field field : fields) {
                if (log.getOpPrimaryKey() != null && StringUtils.isNotBlank(log.getOpPrimaryName())) {
                    break;
                }
                if (StringUtils.isNotBlank(primaryKey) && field.getName().equals(primaryKey)) {
                    Object fieldValue = null;
                    try {
                        fieldValue = ReflectUtil.getFieldValue(entity, field);
                    } catch (Exception e) {
                        logger.error("解析参数失败{}", e);
                    }
                    log.setOpPrimaryKey(String.valueOf(fieldValue));
                }
                if (StringUtils.isNotBlank(primaryName) && field.getName().equals(primaryName)) {
                    Object fieldValue = null;
                    try {
                        fieldValue = ReflectUtil.getFieldValue(entity, field);
                    } catch (Exception e) {
                        logger.error("解析参数失败{}", e);
                    }
                    log.setOpPrimaryName(String.valueOf(fieldValue));
                }

            }
        }
        try {
            withAnnotation(log, null, entity.getClass(), entity);
        } catch (Exception e) {
            logger.warn("记录审计日志失败:{]", e.getMessage(), e);
        }
    }

    private void dealArgsAnnotation(JoinPoint joinPoint, AuditLogInfo log, Object[] args) {
        if (args == null || args.length == 0) {
            return;
        }
        if (!(joinPoint.getSignature() instanceof MethodSignature)) {
            return;
        }
        Signature signature = joinPoint.getSignature();
        Method method = ((MethodSignature) signature).getMethod();
        Class<?>[] parameterTypes = method.getParameterTypes();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        if (args.length == parameterTypes.length && parameterTypes.length == parameterAnnotations.length) {
            for (int i = 0; i < parameterTypes.length; i++) {
                Object parameterObject = args[i];
                if (parameterObject == null) {
                    continue;
                }
                Class<?> parameterType = parameterTypes[i];
                for (Annotation annotation : parameterAnnotations[i]) {
                    withAnnotation(log, annotation, parameterType, parameterObject);
                }
            }
        }
    }

    /**
     * 处理用户信息，以及请求参数
     *
     * @param log
     * @param joinPoint
     */
    private void dealLast(AuditLogInfo log, ProceedingJoinPoint joinPoint) {
        log.setId(IdUtil.genId());
        if (ContextHolder.getUserId() != null) {
            log.setOpUser(ContextHolder.getUserId());
        }
        if (StringUtils.isBlank(log.getOpParameters())) {
            log.setOpParameters(JsonUtil.encode(getAllArguments(joinPoint)));
        }
        if (StringUtils.isBlank(log.getOpCode()) && StringUtils.isNotBlank(log.getOpFunc())) {
            log.setOpCode(log.getOpFunc());
        }
    }

    //获取所有属性值
    private Map<String, Object> getAllArguments(ProceedingJoinPoint proceedingJoinPoint) {
        //参数值
        Object[] paramValues = proceedingJoinPoint.getArgs();
        Map<String, Object> requestParams = new HashMap<>(paramValues.length);
        for (int i = 0; i < paramValues.length; i++) {
            Object value = paramValues[i];
            //如果是文件对象
            if (value instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) value;
                //获取文件名
                value = file.getOriginalFilename();
            }
            String paramName = "parameter" + i;
            requestParams.put(paramName, value);
        }
        return requestParams;
    }

    private AuditLogInfo withAnnotation(AuditLogInfo log, Annotation annotation, Class<?> clazz, Object obj) {
        if (obj == null) {
            return log;
        }
        if (obj instanceof List) {
            return log;
        }
        if (annotation != null) {
            log.withAnnotation(annotation, obj);
        }
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            Object fieldValue = null;
            try {
                fieldValue = ReflectUtil.getFieldValue(obj, field);
            } catch (Exception e) {
                logger.debug("解析参数失败{}", e);
            }
            if (fieldValue == null) {
                continue;
            }
            Annotation[] fieldAnnotations = field.getDeclaredAnnotations();
            if (fieldAnnotations == null) {
                continue;
            }
            for (Annotation fieldAnnotation : fieldAnnotations) {
                if (fieldAnnotation != null) {
                    log.withAnnotation(fieldAnnotation, fieldValue);
                }
            }
        }
        return log;
    }
}
