package com.eoi.jax.web.core.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR> zsc
 * @create 2024/2/27 14:50
 */
public class IpUtil {

    /**
     * 获取本机IP
     *
     * @return
     */
    public static String getLocalIp() {
        try {
            InetAddress ipAddress = InetAddress.getLocalHost();
            return ipAddress.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * 判断是否为内网IP
     *
     * @param ip
     * @return
     */
    public static boolean isInnerIp(String ip) {
        if (ip.startsWith("192.168.") || ip.startsWith("10.") || ip.startsWith("172.")) {
            return true;
        }
        return false;
    }
}
