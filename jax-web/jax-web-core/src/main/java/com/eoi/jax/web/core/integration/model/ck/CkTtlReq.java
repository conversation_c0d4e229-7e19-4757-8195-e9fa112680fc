package com.eoi.jax.web.core.integration.model.ck;

import java.util.List;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 */
public class CkTtlReq {
    private List<Table> tables;
    private String ttlType;
    private List<Ttl> ttl;

    public List<Table> getTables() {
        return tables;
    }

    public void setTables(List<Table> tables) {
        this.tables = tables;
    }

    public String getTtlType() {
        return ttlType;
    }

    public void setTtlType(String ttlType) {
        this.ttlType = ttlType;
    }

    public List<Ttl> getTtl() {
        return ttl;
    }

    public void setTtl(List<Ttl> ttl) {
        this.ttl = ttl;
    }

    public static class Table {
        private String tableName;
        private String database;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getDatabase() {
            return database;
        }

        public void setDatabase(String database) {
            this.database = database;
        }
    }


    public static class Ttl {
        private String timeColumn = "@timestamp";
        private Long interval;
        private String unit;
        private String action;
        private String target;

        public String getTimeColumn() {
            return timeColumn;
        }

        public void setTimeColumn(String timeColumn) {
            this.timeColumn = timeColumn;
        }

        public Long getInterval() {
            return interval;
        }

        public void setInterval(Long interval) {
            this.interval = interval;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getTarget() {
            return target;
        }

        public void setTarget(String target) {
            this.target = target;
        }
    }
}
