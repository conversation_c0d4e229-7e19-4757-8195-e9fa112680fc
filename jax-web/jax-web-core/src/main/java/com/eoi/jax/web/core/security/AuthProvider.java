package com.eoi.jax.web.core.security;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 登录认证接口
 *
 * <AUTHOR>
 * @date 2023/4/7
 */
public interface AuthProvider {

    /**
     * 认证身份
     * <p>
     * 解析http请求中的token，返回用户身份信息
     * 系统会根据用户信息的唯一标识自动创建或者更新用户身份信息
     * <p>
     * 返回值：必须实现User接口，建议实现UserInfo来返回更多用户信息
     * 异常：解析出现异常的请求会被拒绝
     *
     * @param httpServletRequest http请求
     * @return 用户信息 返回空表示认证未通过
     */
    User identify(HttpServletRequest httpServletRequest);

    /**
     * 获取用户权限
     *
     * @param user 用户身份
     * @return 用户权限
     */
    List<Permission> getPermissions(User user);

    /**
     * 获取用户组
     *
     * @param user 用户身份
     * @return 用户组
     */
    List<Group> getGroups(User user);

    /**
     * 登出
     * 当用户在jax系统登出的时候，jax会自动调用该接口来同时登出第三方的系统。
     *
     * @param user
     */
    void logout(User user);

    /**
     * 注入拦截器
     * @return
     */
    default AuthProviderInterceptor registerInterceptor() {
        return null;
    }
}
