package com.eoi.jax.web.core.common.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

public final class Common {
    private Common() {
        // forbid init instance
    }

    public static <T> boolean isEmpty(List<T> s) {
        return s == null || s.isEmpty();
    }

    public static <T> boolean noEmpty(List<T> s) {
        return !isEmpty(s);
    }

    public static boolean isEmpty(String s) {
        return s == null || s.trim().length() == 0;
    }

    public static boolean noEmpty(String s) {
        return !isEmpty(s);
    }

    public static List<String> split(CharSequence str, CharSequence separator) {
        List<String> list = StrUtil.split(str, separator);
        return new ArrayList<>(list);
    }

    public static boolean isDesc(String s) {
        return noEmpty(s) && "desc".equalsIgnoreCase(s);
    }

    public static boolean isAsc(String s) {
        return noEmpty(s) && "asc".equalsIgnoreCase(s);
    }

    public static boolean equals(String s1, String s2) {
        return (isEmpty(s1) && isEmpty(s2)) || (s1 != null && s1.equals(s2));
    }

    public static boolean notEquals(String s1, String s2) {
        return !equals(s1, s2);
    }

    public static String toStringNull(Number num) {
        return null == num ? null : String.valueOf(num);
    }

    public static String pathsJoin(String first, String... more) {
        Path path = Paths.get(first, more);
        return path.toString();
    }

    public static String urlPathsJoin(String first, String... more) {
        for (String item : more) {
            if (first.endsWith("/") && item.startsWith("/")) {
                first = String.format("%s%s", first.substring(0, first.length() - 1), item);
            } else if (first.endsWith("/") || item.startsWith("/")) {
                first = String.format("%s%s", first, item);
            } else {
                first = String.format("%s/%s", first, item);
            }
        }
        return first;
    }

    public static String getFileExt(String path) {
        if (".tar.gz".equals(path)) {
            return "tar.gz";
        }
        return FileUtil.extName(path);
    }

    public static boolean verifyName(String name) {
        if (isEmpty(name)) {
            return false;
        }
        int length = name.length();
        if (length > 255) {
            return false;
        }
        for (int i = 0; i < length; i++) {
            char v = name.charAt(i);
            if (!Character.isDigit(v)
                    && !Character.isLowerCase(v)
                    && !Character.isUpperCase(v)
                    && v != '-'
                    && v != '_') {
                return false;
            }
        }
        return true;
    }


    /**
     * 校验表名
     * 小写字母开头，支持大小写字母、数字下划线和连字符的组合
     *
     * @param enName
     * @return
     */
    public static boolean verifyTbName(String enName) {
        if (isEmpty(enName)) {
            return false;
        }
        int length = enName.length();
        if (length > 255) {
            return false;
        }
        if (!Character.isLowerCase(enName.charAt(0))) {
            return false;
        }
        for (int i = 1; i < length; i++) {
            char v = enName.charAt(i);
            if (!Character.isDigit(v)
                    && !Character.isLowerCase(v)
                    && !Character.isUpperCase(v)
                    && v != '_'
                    && v != '-') {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验英文缩写
     * 小写字母开头，支持大小写字母、数字和下划线的组合
     *
     * @param enName
     * @return
     */
    public static boolean verifyCode(String enName) {
        if (isEmpty(enName)) {
            return false;
        }
        int length = enName.length();
        if (length > 255) {
            return false;
        }
        if (!Character.isLowerCase(enName.charAt(0))) {
            return false;
        }
        for (int i = 1; i < length; i++) {
            char v = enName.charAt(i);
            if (!Character.isDigit(v)
                    && !Character.isLowerCase(v)
                    && !Character.isUpperCase(v)
                    && v != '_') {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验英文名称
     * 支持小写/大写字母、数字和下划线的组合
     *
     * @param enName
     * @return
     */
    public static boolean verifyEnName(String enName) {
        if (isEmpty(enName)) {
            return false;
        }
        int length = enName.length();
        if (length > 255) {
            return false;
        }
        for (int i = 0; i < length; i++) {
            char v = enName.charAt(i);
            if (!Character.isDigit(v)
                    && !Character.isLowerCase(v)
                    && !Character.isUpperCase(v)
                    && v != '-'
                    && v != '_'
                    && !Character.isSpaceChar(v)) {
                return false;
            }
        }
        return true;
    }

    public static String encodeToSafeString(String value) {
        if (isEmpty(value)) {
            return "";
        }
        int length = value.length();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            char v = value.charAt(i);
            if (Character.isDigit(v)
                    || Character.isLowerCase(v)
                    || Character.isUpperCase(v)) {
                builder.append(v);
            } else {
                builder.append('_');
            }

        }
        return builder.toString();
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = new HashSet<>();
        return t -> seen.add(keyExtractor.apply(t));
    }

    public static String formatDuration(long ms) {
        double fractional = ms;
        String unit;
        if (fractional >= 1000) {
            fractional /= 1000;
            unit = "s";
        } else {
            return ms + "ms";
        }
        if (fractional >= 60) {
            fractional /= 60;
            unit = "m";
        }
        if (fractional >= 60) {
            fractional /= 60;
            unit = "h";
        }
        if (fractional >= 24) {
            fractional /= 24;
            unit = "d";
        }
        //格式化小数
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(fractional) + unit;
    }

    public static String formatBytes(long size) {
        double fractional = size;
        String unit;
        if (fractional >= 1024) {
            fractional /= 1024;
            unit = "KB";
        } else {
            return size + "B";
        }
        if (fractional >= 1024) {
            fractional /= 1024;
            unit = "MB";
        }
        if (fractional >= 1024) {
            fractional /= 1024;
            unit = "GB";
        }
        if (fractional >= 1024) {
            fractional /= 1024;
            unit = "TB";
        }
        //格式化小数
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(fractional) + unit;
    }

    public static String formatBytesRate(long size, long ms) {
        if (ms <= 1000) {
            return formatBytes(size) + "/s";
        }
        return formatBytes(size / ms * 1000) + "/s";
    }

    public static String formatCount(long count) {
        double fractional = count;
        String unit = "";
        if (fractional > 10000) {
            fractional /= 10000;
            unit = "万";
        } else {
            return String.valueOf(count);
        }
        if (fractional > 10000) {
            fractional /= 10000;
            unit = "亿";
        }
        //格式化小数
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(fractional) + unit;
    }

    public static String formatCountRate(long count, long ms) {
        if (ms <= 1000) {
            return formatBytes(count) + "/s";
        }
        return formatBytes(count / ms * 1000) + "/s";
    }
}
