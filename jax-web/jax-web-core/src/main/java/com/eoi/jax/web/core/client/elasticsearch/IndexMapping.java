package com.eoi.jax.web.core.client.elasticsearch;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class IndexMapping {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dynamic;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Property> properties;

    public String getDynamic() {
        return dynamic;
    }

    public IndexMapping setDynamic(String dynamic) {
        this.dynamic = dynamic;
        return this;
    }

    public Map<String, Property> getProperties() {
        return properties;
    }

    public IndexMapping setProperties(Map<String, Property> properties) {
        this.properties = properties;
        return this;
    }

    public static class Property {
        private String type;
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String format;

        public String getType() {
            return type;
        }

        public Property setType(String type) {
            this.type = type;
            return this;
        }

        public String getFormat() {
            return format;
        }

        public Property setFormat(String format) {
            this.format = format;
            return this;
        }
    }
}
