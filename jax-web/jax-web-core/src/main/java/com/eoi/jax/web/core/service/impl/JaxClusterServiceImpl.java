package com.eoi.jax.web.core.service.impl;

import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.config.AppConfig;
import com.eoi.jax.web.core.config.NacosDiscoveryConfig;
import com.eoi.jax.web.core.integration.model.nacos.NacosInstance;
import com.eoi.jax.web.core.integration.model.nacos.NacosInstanceReq;
import com.eoi.jax.web.core.integration.service.NacosService;
import com.eoi.jax.web.core.model.jaxcluster.JaxClusterNode;
import com.eoi.jax.web.core.model.jaxcluster.JaxClusterNodes;
import com.eoi.jax.web.core.service.JaxClusterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2024/3/11 13:49
 */
@Service
public class JaxClusterServiceImpl implements JaxClusterService {
    private static final Logger LOGGER = LoggerFactory.getLogger(JaxClusterServiceImpl.class);

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private NacosDiscoveryConfig nacosDiscoveryConfig;

    @Resource
    private NacosService nacosService;

    @Override
    public JaxClusterNodes getClusterNodes() {
        JaxClusterNodes result = new JaxClusterNodes();
        result.setEnabledNacos(nacosDiscoveryConfig.getEnabled());
        result.setNodes(new ArrayList<>());

        if (nacosDiscoveryConfig.getEnabled()) {
            NacosInstanceReq nacosInstanceReq = new NacosInstanceReq();
            nacosInstanceReq.setServiceName(appConfig.getApplicationName());
            nacosInstanceReq.setGroupName(nacosDiscoveryConfig.getGroup());
            nacosInstanceReq.setNamespaceName(nacosDiscoveryConfig.getNamespace());
            nacosInstanceReq.setHealthyOnly(true);
            nacosInstanceReq.setBaseUrl(nacosDiscoveryConfig.getServerAddr());
            nacosInstanceReq.setUsername(nacosDiscoveryConfig.getUsername());
            nacosInstanceReq.setPassword(nacosDiscoveryConfig.getPassword());

            List<NacosInstance> instanceList = nacosService.getNacosInstanceList(nacosInstanceReq);
            for (NacosInstance instance : instanceList) {
                String serverId = String.valueOf(instance.getMetadata().getOrDefault("jaxServerId", ""));
                JaxClusterNode node = new JaxClusterNode();
                node.setIp(instance.getIp());
                node.setPort(instance.getPort());
                node.setServerId(serverId);
                result.getNodes().add(node);
            }
        } else {
            JaxClusterNode node = getLocalNode();
            result.getNodes().add(node);
        }
        return result;
    }

    @Override
    public JaxClusterNode getLocalNode() {
        String serverId = appConfig.getJax().getServerId().toString();
        String ip = null;
        try {
            ip = appConfig.getServer().getListenAddress();
        } catch (IOException e) {
            LOGGER.warn("get local ip failed", e);
            throw new BizException(ResponseCode.FAILED, e);
        }
        int port = appConfig.getServer().getPort();
        JaxClusterNode node = new JaxClusterNode();
        node.setIp(ip);
        node.setPort(port);
        node.setServerId(serverId);
        return node;
    }

    @Override
    public JaxClusterNode getClusterNode(Long serverId) {
        JaxClusterNodes nodes = getClusterNodes();
        for (JaxClusterNode node : nodes.getNodes()) {
            if (node.getServerId().equals(String.valueOf(serverId))) {
                return node;
            }
        }
        throw new BizException(ResponseCode.JAX_NODE_NOT_FOUND);
    }
}
