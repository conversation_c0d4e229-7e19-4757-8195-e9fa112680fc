
package com.eoi.jax.web.core.common.enumrate;

import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;

/**
 * @Author: yaru.ma
 * @Date: 2024/1/16
 */
public enum ClickhouseEngineCatalogEnum implements ICodeMessageEnum {

    MERGE_TREE_FAMILY("MergeTreeFamily", "合并树家族"),
    EXTERNAL("External", "外部表"),
    SPECIAL("Special", "特殊");


    ClickhouseEngineCatalogEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private final String code;

    private final String message;

    @Override
    public String code() {
        return code;
    }

    @Override
    public String message() {
        return message;
    }

}
