package com.eoi.jax.web.core.integration.model.cell.agent;

import com.eoi.jax.web.core.integration.model.cell.CellClusterBaseRequestInfo;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @date 2023/1/31
 */
public class CellMetricsReq extends CellClusterBaseRequestInfo {

    /**
     * 查询区间，5m
     */
    private String bucket;
    /**
     * 是否按照区间值进行汇总，按时间分桶、按ProcessName分桶后，ProcessName分桶内求和总数
     */
    @JsonProperty(value = "show_total")
    private Boolean showTotal;
    /**
     * 时间范围
     */
    @JsonProperty(value = "time_range")
    private TimeRange timeRange;
    /**
     * 产线名称
     */
    private String source;


    public static class TimeRange {
        /**
         * 时间区间起点
         */
        private String min;
        /**
         * 时间区间重点
         */
        private String max;


        public TimeRange() {
        }

        public TimeRange(String min, String max) {
            this.min = min;
            this.max = max;
        }

        public String getMin() {
            return min;
        }

        public void setMin(String min) {
            this.min = min;
        }

        public String getMax() {
            return max;
        }

        public void setMax(String max) {
            this.max = max;
        }
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public boolean isShowTotal() {
        return showTotal;
    }

    public void setShowTotal(boolean showTotal) {
        this.showTotal = showTotal;
    }

    public TimeRange getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(TimeRange timeRange) {
        this.timeRange = timeRange;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
