package com.eoi.jax.web.core.excel;

import com.eoi.jax.web.core.common.constant.ICodeMessageEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: yaru.ma
 * @Date: 2022/11/23
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Excel {

    int no();

    /**
     * 列名
     */
    String name() default "";

    /**
     * 宽度
     */
    int width() default 20;

    /**
     * 忽略该字段
     */
    boolean skip() default false;

    /**
     * 下拉框选项-中文 ，用于下拉框选项的展示
     *
     * @return
     */
    String[] selects() default {};

    /**
     * 下拉框选项-英文 ，用于下拉框选择后的保存，与select一一对应
     *
     * @return
     */
    String[] selectEns() default {};

    Class<? extends ICodeMessageEnum>[] selectionEnum() default {};
}
