package com.eoi.jax.web.core.integration.service.impl;

import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.integration.service.MysqlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * @Author: zsc
 * @Date: 2022/12/20
 **/
@Service
public class MysqlServiceImpl implements MysqlService {

    private static Logger log = LoggerFactory.getLogger(MysqlServiceImpl.class);

    /**
     * 执行DDL
     * DDL语句不支持事务，因此未做事务处理
     *
     * @param url
     * @param userName
     * @param password
     * @param sql
     */
    @Override
    public void executeDdlSql(String url, String userName, String password, String sql) {
        Connection conn = null;
        Statement statement = null;
        try {
            conn = DriverManager.getConnection(url, userName, password);
            statement = conn.createStatement();
            statement.executeUpdate(sql);
        } catch (Exception e) {
            throw new BizException(ResponseCode.DATABASE_MYSQL_EXECUTE_FAILED.getCode(), e.getMessage());
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                }
            }
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                }
            }
        }
    }

}
