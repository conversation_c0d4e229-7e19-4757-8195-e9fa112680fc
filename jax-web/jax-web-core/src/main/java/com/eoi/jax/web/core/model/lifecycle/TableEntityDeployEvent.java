package com.eoi.jax.web.core.model.lifecycle;

import com.eoi.jax.web.core.common.enumrate.TableDeployActionTypeEnum;
import com.eoi.jax.web.repository.entity.TbTable;

/**
 * 生命周期管理-模型变动事件
 * 仅监控模型发布的变化
 *
 * @Author: yaru.ma
 * @Date: 2024/10/21
 */
public class TableEntityDeployEvent {

    private TableDeployActionTypeEnum actionTypeEnum;

    private TbTable table;


    public TableEntityDeployEvent(TableDeployActionTypeEnum actionTypeEnum, TbTable table) {
        this.actionTypeEnum = actionTypeEnum;
        this.table = table;
    }

    public TableEntityDeployEvent() {
    }

    public TableDeployActionTypeEnum getActionTypeEnum() {
        return actionTypeEnum;
    }

    public void setActionTypeEnum(TableDeployActionTypeEnum actionTypeEnum) {
        this.actionTypeEnum = actionTypeEnum;
    }

    public TbTable getTable() {
        return table;
    }

    public void setTable(TbTable table) {
        this.table = table;
    }
}
