package com.eoi.jax.web.core.client.elasticsearch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
public class ElasticsearchColumnSetting {
    @Schema(description = "模型列Id")
    private Long colId;
    @Schema(description = "模型列名称")
    private String colName;
    @Schema(description = "模型列类型")
    private String colType;
    @Schema(description = "es的字段名称")
    private String propertyName;
    @Schema(description = "es的字段类型")
    private String propertyType;
    @Schema(description = "es的字段格式")
    private List<String> datetimeFormat;
    @Schema(description = "是否主键")
    private Boolean isPrimaryKey;

    public Long getColId() {
        return colId;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(String propertyType) {
        this.propertyType = propertyType;
    }

    public List<String> getDatetimeFormat() {
        return datetimeFormat;
    }

    public void setDatetimeFormat(List<String> datetimeFormat) {
        this.datetimeFormat = datetimeFormat;
    }

    public Boolean getIsPrimaryKey() {
        return isPrimaryKey;
    }

    public void setIsPrimaryKey(Boolean primaryKey) {
        this.isPrimaryKey = primaryKey;
    }
}
