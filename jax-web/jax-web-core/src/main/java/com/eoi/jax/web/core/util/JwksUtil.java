package com.eoi.jax.web.core.util;

import com.nimbusds.jose.*;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.mint.DefaultJWSMinter;
import com.nimbusds.jwt.JWTClaimsSet;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/11/19
 */
public class JwksUtil {

    public static String genGrafanaAdminJwt() throws Exception {
        Map<String, Object> payload = new HashMap<>(4);
        payload.put("role", "Admin");
        String jwt = genJwt("jaxAdmin", new Date(System.currentTimeMillis() + 3600000), payload);
        return jwt;
    }

    public static String genGrafanaViewJwt() throws Exception {
        Map<String, Object> payload = new HashMap<>(4);
        payload.put("role", "Viewer");
        String jwt = genJwt("jaxViewer", new Date(System.currentTimeMillis() + 24 * 3600000), payload);
        return jwt;
    }

    public static String genJwt(
            String subject,
            Date expirationTime,
            Map<String, Object> payloadMap
    ) throws Exception {
        ClassPathResource classPathResource = new ClassPathResource("security/jwks.json");
        InputStream inputStream = classPathResource.getInputStream();
        JWKSet jwkSet = JWKSet.load(inputStream);
        DefaultJWSMinter minter = new DefaultJWSMinter();
        minter.setJWKSource(new ImmutableJWKSet(jwkSet));
        JWSHeader header = new JWSHeader.Builder(JWSAlgorithm.RS256)
                .type(JOSEObjectType.JWT)
                .build();
        // Create JWT
        JWTClaimsSet jwtClaims = new JWTClaimsSet.Builder()
                .subject(subject)
                .expirationTime(expirationTime)
                .build();
        Payload payload = jwtClaims.toPayload();
        Map<String, Object> map = payload.toJSONObject();
        map.putAll(payloadMap);
        JWSObject jwsObject = minter.mint(header, new Payload(map), null);
        return jwsObject.serialize();
    }
}
