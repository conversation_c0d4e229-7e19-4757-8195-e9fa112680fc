package com.eoi.jax.web.core.common.audit;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eoi.jax.web.core.common.util.JsonUtil;
import org.springframework.web.multipart.MultipartFile;

import java.lang.annotation.Annotation;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/12
 */
public class AuditLogInfo {
    private Long id;
    private String opApi;
    private String opApplication;
    private String opCategory;
    private String opModule;
    private String opFunc;
    private String opCode;
    private Date opTime;
    private Long opUser;
    private String opAction;
    private Long opPrimaryKey;
    private String opPrimaryName;

    private String opParameters;
    private String opIp;
    private String opStatus;
    private String opErrorMsg;

    public AuditLogInfo withAnnotation(AuditLog auditLog) {
        if (StrUtil.isNotEmpty(auditLog.category())) {
            setOpCategory(auditLog.category());
        }
        if (StrUtil.isEmpty(this.opModule) && StrUtil.isNotEmpty(auditLog.module())) {
            setOpModule(auditLog.module());
        }
        if (StrUtil.isNotEmpty(auditLog.function())) {
            setOpFunc(auditLog.function());
        }
        if (StrUtil.isNotEmpty(auditLog.code())) {
            setOpCode(auditLog.code());
        }
        if (auditLog.opAction() != null) {
            setOpAction(auditLog.opAction().name());
        }

        return this;
    }

    public AuditLogInfo withAnnotation(Annotation annotation, Object obj) {
        if (annotation instanceof OpModule) {
            setOpModule(formatString(obj));
        }
        if (annotation instanceof OpPrimaryKey) {
            String string = formatString(obj);
            string = dealPrimaryForAdd(((OpPrimaryKey) annotation).name(), string);
            setOpPrimaryKey(string);
        }
        if (annotation instanceof OpPrimaryName) {
            String string = formatString(obj);
            string = dealPrimaryForAdd(((OpPrimaryName) annotation).name(), string);
            setOpPrimaryName(string);
        }
        if (annotation instanceof OpParameters) {
            String string = formatString(obj);
            if (StringUtils.isNotBlank(string) && ((OpParameters) annotation).ignoreField().length > 0) {
                JSONObject decode = null;
                try {
                    decode = JsonUtil.decode(string, JSONObject.class);
                    for (int i = 0; i < ((OpParameters) annotation).ignoreField().length; i++) {
                        String field = ((OpParameters) annotation).ignoreField()[i];
                        if (decode.get(field) != null) {
                            decode.put(field, "***");
                        }
                    }
                } catch (Exception e) {

                }
                if (decode != null) {
                    string = JsonUtil.encode(decode);
                }

            }
            setOpParameters(string);
        }
        return this;
    }

    private static String dealPrimaryForAdd(String propertyName, String string) {
        if (StringUtils.isNotBlank(string) && StringUtils.isNotBlank(propertyName)) {
            JSONObject decode = null;
            try {
                decode = JsonUtil.decode(string, JSONObject.class);
            } catch (Exception e) {

            }
            if (decode != null && decode.get(propertyName) != null) {
                string = String.valueOf(decode.get(propertyName));
            }
        }
        return string;
    }

    private String formatString(Object obj) {
        if (obj == null || obj instanceof MultipartFile) {
            return null;
        }
        String result = null;
        if (obj instanceof Integer) {
            int v = (Integer) obj;
            result = String.valueOf(v);
        } else if (obj instanceof Long) {
            long v = (Long) obj;
            result = String.valueOf(v);
        } else if (obj instanceof Float) {
            float v = (Float) obj;
            //禁止科学计数法输出
            result = new BigDecimal(String.valueOf(v)).stripTrailingZeros().toPlainString();
        } else if (obj instanceof Double) {
            double v = (Double) obj;
            //禁止科学计数法输出
            result = new BigDecimal(String.valueOf(v)).stripTrailingZeros().toPlainString();
        } else if (obj instanceof String) {
            result = (String) obj;
        } else {
            result = JsonUtil.encode(obj);
        }
        return result;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpApi() {
        return opApi;
    }

    public void setOpApi(String opApi) {
        if (opApi != null && opApi.length() > 255) {
            opApi = opApi.substring(opApi.length() - 200, opApi.length());
        }
        this.opApi = opApi;
    }

    public void setOpPrimaryKey(Long opPrimaryKey) {
        this.opPrimaryKey = opPrimaryKey;
    }

    public String getOpApplication() {
        return opApplication;
    }

    public AuditLogInfo setOpApplication(String opApplication) {
        this.opApplication = opApplication;
        return this;
    }

    public String getOpCategory() {
        return opCategory;
    }

    public AuditLogInfo setOpCategory(String opCategory) {
        this.opCategory = opCategory;
        return this;
    }

    public String getOpModule() {
        return opModule;
    }

    public AuditLogInfo setOpModule(String opModule) {
        this.opModule = opModule;
        return this;
    }


    public Long getOpUser() {
        return opUser;
    }

    public AuditLogInfo setOpUser(Long opUser) {
        this.opUser = opUser;
        return this;
    }

    public String getOpAction() {
        return opAction;
    }

    public AuditLogInfo setOpAction(String opAction) {
        this.opAction = opAction;
        return this;
    }

    public Date getOpTime() {
        return opTime;
    }

    public void setOpTime(Date opTime) {
        this.opTime = opTime;
    }

    public Long getOpPrimaryKey() {
        return opPrimaryKey;
    }

    public void setOpPrimaryKey(String opPrimaryKey) {
        if (this.opPrimaryKey != null || StringUtils.isEmpty(opPrimaryKey)) {
            return;
        }
        try {
            this.opPrimaryKey = Long.parseLong(opPrimaryKey);
        } catch (Exception e) {
            this.opErrorMsg = this.opErrorMsg + "主键错误:" + opPrimaryKey;
        }
    }

    public String getOpPrimaryName() {
        return opPrimaryName;
    }

    public void setOpPrimaryName(String opPrimaryName) {
        if (StringUtils.isNotBlank(this.opPrimaryName) || StringUtils.isEmpty(opPrimaryName)) {
            return;
        }
        this.opPrimaryName = opPrimaryName;
    }

    public String getOpParameters() {
        return opParameters;
    }

    public void setOpParameters(String opParameters) {
        this.opParameters = opParameters;
    }

    public String getOpIp() {
        return opIp;
    }

    public AuditLogInfo setOpIp(String opIp) {
        this.opIp = opIp;
        return this;
    }

    public String getOpFunc() {
        return opFunc;
    }

    public void setOpFunc(String opFunc) {
        this.opFunc = opFunc;
    }

    public String getOpCode() {
        return opCode;
    }

    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    public String getOpStatus() {
        return opStatus;
    }

    public void setOpStatus(String opStatus) {
        this.opStatus = opStatus;
    }

    public String getOpErrorMsg() {
        return opErrorMsg;
    }

    public void setOpErrorMsg(String opErrorMsg) {
        this.opErrorMsg = opErrorMsg;
    }
}
