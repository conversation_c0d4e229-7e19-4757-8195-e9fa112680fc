package com.eoi.jax.web.core.client.k8s;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import io.fabric8.kubernetes.api.model.Service;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class K8sClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(K8sClient.class);

    private Config config;

    public K8sClient(String configFile, String context) {
        System.setProperty(Config.KUBERNETES_DISABLE_AUTO_CONFIG_SYSTEM_PROPERTY, "true");
        try {
            File file = new File(configFile);
            FileInputStream fileInputStream = new FileInputStream(file);
            config = Config.fromKubeconfig(context, IoUtil.read(fileInputStream, StandardCharsets.UTF_8), null);
        } catch (Exception e) {
            LOGGER.error("init K8sClient error for config:" + configFile, e);
        }
    }

    public String getJobManagerAddress(String namespace, String pipelineName, int retryTimes, long retryBackoff) {
        while (retryTimes > 0) {
            retryTimes--;
            try {
                try (KubernetesClient client = new DefaultKubernetesClient(config)) {
                    String masterHost = client.getMasterUrl().getHost();
                    Service svc = client.services()
                            .inNamespace(StrUtil.isEmpty(namespace) ? "default" : namespace)
                            .withName(pipelineName + "-rest")
                            .get();

                    Integer port = null;
                    if (CollUtil.isNotEmpty(svc.getSpec().getPorts())) {
                        port = svc.getSpec().getPorts().get(0).getNodePort();
                    }
                    if (StrUtil.isNotEmpty(masterHost) && port != null) {
                        return String.format("%s:%d", masterHost, port);
                    }
                }
            } catch (Exception e) {
                LOGGER.info("get k8s JobManagerAddress failed, will retry: {}, reason:{}", retryTimes, e.getMessage());
                try {
                    Thread.sleep(retryBackoff);
                } catch (InterruptedException ignore) {
                }
            }
        }
        return null;
    }
}
