package com.eoi.jax.web.core.common.constant;

import cn.hutool.core.util.StrUtil;

public enum TableStorageModeEnum implements ICodeEnum {
    SINGLE("SINGLE", "单指标值表"),
    MULTI("MULTI", "多指标值表");

    private final String code;
    private final String name;

    TableStorageModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String code() {
        return code;
    }

    public String getName() {
        return name;
    }

    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }

    public static TableStorageModeEnum fromString(String code) {
        for (TableStorageModeEnum value : TableStorageModeEnum.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static TableStorageModeEnum fromName(String name) {
        for (TableStorageModeEnum value : TableStorageModeEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }


    public static boolean in(String code, TableStorageModeEnum... more) {
        for (TableStorageModeEnum item : more) {
            if (item.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
