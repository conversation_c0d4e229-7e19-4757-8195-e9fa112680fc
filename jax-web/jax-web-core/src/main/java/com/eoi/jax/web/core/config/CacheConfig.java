package com.eoi.jax.web.core.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> zsc
 * @create 2023/4/24 15:06
 */
@EnableCaching
@Configuration
public class CacheConfig {

    @Resource(name = "caffeineCacheThreadPool")
    private Executor cacheExecutor;

    @Bean
    public Caffeine<Object, Object> caffeineCache() {
        return Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterWrite(5, TimeUnit.MINUTES)
                // 初始的缓存空间大小
                .initialCapacity(300)
                // 使用自定义线程池
                .executor(cacheExecutor)
                // 缓存的最大条数
                .maximumSize(3000);
    }

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCaffeine(caffeineCache());
        // 不缓存空值
        caffeineCacheManager.setAllowNullValues(false);
        return caffeineCacheManager;
    }


}

