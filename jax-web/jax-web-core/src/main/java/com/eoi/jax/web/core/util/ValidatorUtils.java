package com.eoi.jax.web.core.util;


import com.eoi.jax.web.core.common.exception.ParamValidationException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * hibernate-validator校验工具类
 *
 * <AUTHOR>
 */
public class ValidatorUtils {
    private static Validator validator;

    static {
        validator = Validation.buildDefaultValidatorFactory().getValidator();
    }

    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     * @throws RuntimeException 校验不通过，则报RRException异常
     */
    public static void validateEntity(Object object, Class<?>... groups)
            throws ParamValidationException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            String errorMsg = constraintViolations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(", ", "[", "]"));
            throw new ParamValidationException(errorMsg);
        }
    }

    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @throws RuntimeException 校验不通过，则报RRException异常
     */
    public static void validateEntity(Object object)
            throws ParamValidationException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object);
        if (!constraintViolations.isEmpty()) {
            String errorMsg = constraintViolations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(", ", "[", "]"));
            throw new ParamValidationException(errorMsg);
        }
    }
}
