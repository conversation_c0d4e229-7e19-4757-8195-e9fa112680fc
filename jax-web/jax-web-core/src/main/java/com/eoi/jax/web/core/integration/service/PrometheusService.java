package com.eoi.jax.web.core.integration.service;

import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryRangeReq;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryRangeResp;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryReq;
import com.eoi.jax.web.core.integration.model.prometheus.PrometheusQueryResp;

import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/20
 **/
public interface PrometheusService {

    /**
     * 查询瞬时指标
     *
     * @param req
     * @param baseUrl
     * @return
     */
    PrometheusQueryResp query(PrometheusQueryReq req, String baseUrl);

    /**
     * 查询范围指标
     *
     * @param req
     * @param baseUrl
     * @return
     */
    PrometheusQueryRangeResp queryRange(PrometheusQueryRangeReq req, String baseUrl);
    /**
     * 查询瞬时指标
     *
     * @param req
     * @param baseUrl
     * @param desc
     * @return
     */
    PrometheusQueryResp query(PrometheusQueryReq req, String baseUrl, String desc);

    /**
     * 查询范围指标
     *
     * @param req
     * @param baseUrl
     * @param desc
     * @return
     */
    PrometheusQueryRangeResp queryRange(PrometheusQueryRangeReq req, String baseUrl, String desc);

    /**
     *  查某个指标的 Instant值, 并只取其 first MetricValue.value数组的值返回;
     * @param query
     * @param promUrl
     * @param timeSec
     * @return
     */
    Object queryInstantValueAsResponse(String query, String promUrl, Long timeSec);

    /**
     * 查某个指标的 Instant值, 并只取其 first MetricValue.value数组的值返回;
     * @param query
     * @param promUrl
     * @param timeSec
     * @param step
     * @return
     */
    Object queryInstantValueAsResponse(String query, String promUrl, Long timeSec, Long step);

    /**
     * 查某个指标的 Range 区间值, 并只取其 first MetricValue.values 返回;
     *
     * @param promUrl
     * @param rangeParams
     * @return
     */
    List<Object[]> queryRangeValuesAsResp(String promUrl, PrometheusQueryRangeReq rangeParams);


}
