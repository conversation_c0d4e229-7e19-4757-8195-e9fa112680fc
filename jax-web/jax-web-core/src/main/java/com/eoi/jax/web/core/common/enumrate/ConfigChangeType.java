package com.eoi.jax.web.core.common.enumrate;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ICodeEnum;

/**
 * <AUTHOR>
 * @date 2023/8/2
 */
public enum ConfigChangeType implements ICodeEnum {
    ADDED("ADDED", "新增"),
    MODIFIED("MODIFIED", "修改"),
    DELETED("DELETED", "删除");

    private final String code;

    private final String message;

    ConfigChangeType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ConfigChangeType fromString(String code) {
        for (ConfigChangeType value : ConfigChangeType.values()) {
            if (value.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getMessageByCode(String code) {
        for (ConfigChangeType value : ConfigChangeType.values()) {
            if (value.equals(code)) {
                return value.message;
            }
        }
        return null;
    }

    /**
     * code
     *
     * @return code
     */
    @Override
    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    /**
     * Indicates whether some other object is "equal to" this one.
     *
     * @param code code
     * @return
     */
    @Override
    public boolean equals(String code) {
        return StrUtil.equalsIgnoreCase(code, code());
    }
}
