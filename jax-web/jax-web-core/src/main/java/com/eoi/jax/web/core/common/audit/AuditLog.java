package com.eoi.jax.web.core.common.audit;

import com.eoi.jax.web.core.common.permission.ProjectPermission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023/4/12
 */
@ProjectPermission
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface AuditLog {
    /**  分类 **/
    String category();
    /** 模块名称 **/
    String module() default "";
    /** 功能名称 **/
    String function() default "";
    /** 功能详情 **/
    String code() default "";
    /** 操作行为 **/
    OpActionEnum opAction() default OpActionEnum.NONE;
}
