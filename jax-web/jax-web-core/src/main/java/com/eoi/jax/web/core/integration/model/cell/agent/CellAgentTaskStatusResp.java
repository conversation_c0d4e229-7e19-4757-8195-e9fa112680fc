package com.eoi.jax.web.core.integration.model.cell.agent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/27
 */
public class CellAgentTaskStatusResp {

    private Long agentId;
    private String hostname;
    private String ip;
    private Integer connection;
    private String agentStatus;
    private Long lastHbTime;
    private List<CellTaskStatus> taskStatusList;


    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getConnection() {
        return connection;
    }

    public void setConnection(Integer connection) {
        this.connection = connection;
    }

    public String getAgentStatus() {
        return agentStatus;
    }

    public void setAgentStatus(String agentStatus) {
        this.agentStatus = agentStatus;
    }

    public Long getLastHbTime() {
        return lastHbTime;
    }

    public void setLastHbTime(Long lastHbTime) {
        this.lastHbTime = lastHbTime;
    }

    public List<CellTaskStatus> getTaskStatusList() {
        return taskStatusList;
    }

    public void setTaskStatusList(List<CellTaskStatus> taskStatusList) {
        this.taskStatusList = taskStatusList;
    }
}
