package com.eoi.jax.web.core.client.grafana;

/**
 * <AUTHOR>
 * @Date 2024/11/29
 */
public class GrafanaClickhouseDatasource extends BaseGrafanaDatasource {
    private String uid;
    private String name;
    private String type = "grafana-clickhouse-datasource";
    private String access = "proxy";
    private boolean basicAuth = false;
    private boolean isDefault = false;
    private Data jsonData = new Data();
    private Secure secureJsonData = new Secure();

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccess() {
        return access;
    }

    public void setAccess(String access) {
        this.access = access;
    }

    public boolean isBasicAuth() {
        return basicAuth;
    }

    public void setBasicAuth(boolean basicAuth) {
        this.basicAuth = basicAuth;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public Data getJsonData() {
        return jsonData;
    }

    public void setJsonData(Data jsonData) {
        this.jsonData = jsonData;
    }

    public Secure getSecureJsonData() {
        return secureJsonData;
    }

    public void setSecureJsonData(Secure secureJsonData) {
        this.secureJsonData = secureJsonData;
    }

    public void setHost(String host) {
        this.jsonData.host = host;
    }

    public void setPort(Integer port) {
        this.jsonData.port = port;
    }

    public void setUsername(String username) {
        this.jsonData.username = username;
    }

    public void setPassword(String password) {
        this.secureJsonData.password = password;
    }

    public static class Data {
        private String host;
        private Integer port;
        private String protocol = "native";
        private String username;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getProtocol() {
            return protocol;
        }

        public void setProtocol(String protocol) {
            this.protocol = protocol;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }
    }

    public static class Secure {
        private String password;

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}
