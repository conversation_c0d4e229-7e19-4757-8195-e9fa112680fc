package com.eoi.jax.web.core.integration.model.nacos;

import cn.hutool.core.util.RandomUtil;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 */
public class NacosBaseRequestInfo implements Serializable {
    @NotBlank(message = "请求url不能为空")
    private String baseUrl;

    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "密码不能为空")
    private String password;

    public NacosBaseRequestInfo(String baseUrl, String username, String password) {
        this.baseUrl = baseUrl;
        this.username = username;
        this.password = password;
    }

    public NacosBaseRequestInfo(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public NacosBaseRequestInfo() {
    }

    public String getBaseUrl() {
        if (baseUrl == null) {
            return null;
        }
        if (baseUrl.contains(",")) {
            String[] urls = baseUrl.split(",");
            return urls[RandomUtil.randomInt(0, urls.length)].trim();
        }
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
