package com.eoi.jax.web.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
@EnableWebSocket
@ConfigurationProperties(prefix = "jax.websocket")
public class WebSocketConfig {
    public static final int DEFAULT_BUFFER_SIZE = 1024 * 1024;

    private Integer bufferSize = DEFAULT_BUFFER_SIZE;

    public Integer getBufferSize() {
        return bufferSize;
    }

    public WebSocketConfig setBufferSize(Integer bufferSize) {
        this.bufferSize = bufferSize;
        return this;
    }

    @Bean
    public ServerEndpointExporter serverEndpoint() {
        return new ServerEndpointExporter();
    }

    @Bean
    public ServletServerContainerFactoryBean createServletServerContainerFactoryBean(
            @Value("${jax.websocket.bufferSize}") Integer bufferSize
    ) {
        if (bufferSize == null || bufferSize <= 0) {
            bufferSize = DEFAULT_BUFFER_SIZE;
        }
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxTextMessageBufferSize(bufferSize);
        container.setMaxBinaryMessageBufferSize(bufferSize);
        return container;
    }
}
