package com.eoi.jax.web.data.modeling;

import com.eoi.jax.web.core.common.enumrate.ClickhouseEngineEnum;
import com.eoi.jax.web.core.common.enumrate.TableDeployDeleteTypeEnum;
import com.eoi.jax.web.core.common.enumrate.TableDeployStatusEnum;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.config.JaxConfig;
import com.eoi.jax.web.core.sqlparser.clickhouse.parser.ClickHouseLexer;
import com.eoi.jax.web.core.sqlparser.clickhouse.parser.ClickHouseParser;
import com.eoi.jax.web.core.util.GeneralSqlParserUtil;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.impl.ClickhouseModelDeployServiceImpl;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.clickhouse.ClickhouseModelSetting;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy.DeployScriptReq;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy.GenerateDefaultScriptReq;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.Field;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/1/12
 */
public class ClickhouseDeployTest {
    ClickhouseModelDeployServiceImplMock deployService = new ClickhouseModelDeployServiceImplMock();
    ClickhouseModelDeployErrorServiceImplMock deployErrorService = new ClickhouseModelDeployErrorServiceImplMock();

    @Before
    public void before() throws NoSuchFieldException, IllegalAccessException {
        Class<?> clazz = ClickhouseModelDeployServiceImpl.class;
        Field nameField = clazz.getDeclaredField("jaxConfig");
        nameField.setAccessible(true);
        JaxConfig jaxConfig = new JaxConfig();
        jaxConfig.setClickhouseLocalSuffix("_local");
        nameField.set(deployService, jaxConfig);
        nameField.set(deployErrorService, jaxConfig);
    }

    @Test
    public void testCreateTable() {
        // 生成sql
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateDefaultReq();
        defaultScriptReq.setActionType("CREATE");
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        // 生成sql,没有识别到分布式表,不影响，没有用到
        String script2 = deployErrorService.generateDefaultScript(defaultScriptReq);
        Assert.assertEquals(script2, script);
        // 发布
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains(
                "ENGINE = Distributed(`default_cluster`, `trade_metric`, `ht_sysInfo2_local`, rand());"
        ));
        // 重新发布 历史数据 TODO
        // 未设置分布式表 TODO
        // 强行修改本地表表名
        script = "${本地表名=test2_local}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        System.out.println("deployErrMsg: " + deployScriptReq.getTableDeployResp().getErrMsg());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains(
                "ENGINE = Distributed(`default_cluster`, `trade_metric`, `test2_local`, rand());"
        ));
    }

    @Test
    public void testCreateTableError() {
        // 解析表名失败的情况
        GenerateDefaultScriptReq defaultScriptReq = deployErrorService.generateDefaultReq();
        defaultScriptReq.setActionType("CREATE");
        String script = deployErrorService.generateDefaultScript(defaultScriptReq);
        DeployScriptReq deployScriptReq = deployErrorService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        System.out.println(" ============================ ");
        System.out.println(deployScriptReq.getTableDeployResp().getErrMsg());
        System.out.println(" ============================ ");
        Assert.assertTrue(TableDeployStatusEnum.FAILURE.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployResp().getErrMsg().contains(ClickhouseModelDeployServiceImpl.WARN_TEXT));
    }

    @Test
    public void testEngine() {
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateDefaultReq();
        defaultScriptReq.setActionType("REBUILD");
        ((ClickhouseModelSetting) defaultScriptReq.getSettingModel()).setEngine(ClickhouseEngineEnum.MYSQL_ENGINE.code());
        ((ClickhouseModelSetting) defaultScriptReq.getSettingModel()).setDistributed(false);
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("DeployLog():" + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
    }

    @Test
    public void testIncreaseTable() {
        // 生成sql
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateDefaultReq();
        defaultScriptReq.setActionType("INCREMENT");
        defaultScriptReq.getColumnDeployList().remove(1);
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" ======================================= ");
        // 生成sql,没有解析到分布式表, 需要在sql里提示异常信息
        String script2 = deployErrorService.generateDefaultScript(defaultScriptReq);
        System.out.println(script2);
        Assert.assertNotEquals(script2, script);
        Assert.assertTrue(script2.contains(ClickhouseModelDeployServiceImpl.WARN_TEXT));
        // 发布
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("DeployLog():" + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains(
                " Distributed(`default_cluster`, `trade_metric`, `ht_sysInfo2_local`, rand());"
        ));
        // 强行修改本地表表名,修改成功
        script = "${本地表名=test2_local}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains(
                "执行sql:CREATE TABLE IF NOT EXISTS `trade_metric`.`ht_sysInfo2` ON CLUSTER `default_cluster` AS `trade_metric`.`test2_local` ENGINE = Distributed(`default_cluster`, `trade_metric`, `test2_local`, rand());"
        ));

        // 重新发布 历史数据 TODO
        ((ClickhouseModelSetting) defaultScriptReq.getSettingModel()).setDistributed(null);
    }


    @Test
    public void testRebuildTable() {
        // 生成sql
        System.out.println(" =================正常情况====================== ");
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateDefaultReq();
        defaultScriptReq.setActionType("REBUILD");
        ((ClickhouseModelSetting) defaultScriptReq.getSettingModel()).setDatabase("rebuild_database");
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" ==================正常发布===================== ");
        // 发布
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains(
                " Distributed(`default_cluster`, `rebuild_database`, `ht_sysInfo2_local`, rand());"
        ));
        System.out.println(" =================异常情况：没有解析到分布式表====================== ");
        // 生成sql,没有解析到分布式表, 需要在sql里提示异常信息
        String script2 = deployErrorService.generateDefaultScript(defaultScriptReq);
        System.out.println(script2);
        Assert.assertNotEquals(script2, script);
        Assert.assertTrue(script2.contains(ClickhouseModelDeployServiceImpl.WARN_TEXT));
        System.out.println(" =================异常发布：没有解析到分布式表====================== ");
        // 生成sql,没有解析到分布式表, 需要在sql里提示异常信息
        script = "${删除的本地表名=xxx}\n" + script2;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        System.out.println("deployErrMsg: " + deployScriptReq.getTableDeployResp().getErrMsg());
        Assert.assertTrue(TableDeployStatusEnum.FAILURE.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployResp().getErrMsg().contains(ClickhouseModelDeployServiceImpl.WARN_TEXT));
        System.out.println(" ==================强行修改本地表表名===================== ");
        script = "${删除的本地表名=xxx}\n${本地表名=yyy}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains(
                "执行sql:CREATE TABLE IF NOT EXISTS `rebuild_database`.`ht_sysInfo2` ON CLUSTER `default_cluster` AS `rebuild_database`.`yyy` ENGINE = Distributed(`default_cluster`, `rebuild_database`, `yyy`, rand())"
        ));
    }

    @Test
    public void testDELETETable() {
        // 生成sql
        System.out.println(" =======================逻辑删除================ ");
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateDefaultReq();
        defaultScriptReq.setActionType("DELETE");
        defaultScriptReq.setDeleteType(TableDeployDeleteTypeEnum.LOGIC.code());
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =========================逻辑删除发布============== ");
        // 发布
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(Common.isEmpty(deployScriptReq.getTableDeployHistoryResp().getDeployLog()));
        System.out.println(" =========================物理删除============== ");
        defaultScriptReq.setDeleteType(TableDeployDeleteTypeEnum.PHYSICAL.code());
        String physicalScript = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        Assert.assertEquals(physicalScript, script);
        System.out.println(" =========================物理删除发布============== ");
        DeployScriptReq deployScriptReq1 = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq1);
        System.out.println("deployLog:\n " + deployScriptReq1.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq1.getTableDeployResp().getStatus()));
        Assert.assertTrue(!Common.isEmpty(deployScriptReq1.getTableDeployHistoryResp().getDeployLog()));
        System.out.println(" =========================本地表解析失败============== ");
        // 生成sql,即使解析失败，由于可以修改，所以不做提示
        String script2 = deployErrorService.generateDefaultScript(defaultScriptReq);
        System.out.println(script2);
        Assert.assertNotEquals(script2, script);
        Assert.assertTrue(!script2.contains(ClickhouseModelDeployServiceImpl.WARN_TEXT));
        System.out.println(" ======================================= ");
    }

    @Test
    public void testIdxCreateTable() {
        System.out.println(" =================正常情况====================== ");
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateIdxDefaultReq();
        defaultScriptReq.setActionType("CREATE");
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =================正常发布====================== ");
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_value_local`, rand())"));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_series_local`, rand())"));
        System.out.println(" =================异常情况====================== ");
        script = deployErrorService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =================异常发布====================== ");
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        Assert.assertTrue(TableDeployStatusEnum.FAILURE.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployResp().getErrMsg().contains("${维度表本地表名=}"));
        script = "${维度表本地表名=xxx}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        Assert.assertTrue(TableDeployStatusEnum.FAILURE.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployResp().getErrMsg().contains("${指标表本地表名=}"));
        script = "${维度表本地表名=xxx}\n${指标表本地表名=yyy}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        System.out.println("deployErrMsg: " + deployScriptReq.getTableDeployResp().getErrMsg());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `xxx`, rand())"));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `yyy`, rand())"));
    }


    @Test
    public void testIdxIncrementTable() {
        System.out.println(" =================正常情况====================== ");
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateIdxDefaultReq();
        defaultScriptReq.setActionType("INCREMENT");
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =================正常发布====================== ");
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_value_local`, rand())"));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_series_local`, rand())"));
        System.out.println(" =================异常情况====================== ");
        script = deployErrorService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =================异常发布====================== ");
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        Assert.assertTrue(TableDeployStatusEnum.FAILURE.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployResp().getErrMsg().contains("${维度表本地表名=}"));
        script = "${维度表本地表名=xxx}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        Assert.assertTrue(TableDeployStatusEnum.FAILURE.equals(deployScriptReq.getTableDeployResp().getStatus()));
        System.out.println("deployErrMsg: " + deployScriptReq.getTableDeployResp().getErrMsg());
        Assert.assertTrue(deployScriptReq.getTableDeployResp().getErrMsg().contains("${指标表本地表名=}"));
        script = "${维度表本地表名=xxx}\n${指标表本地表名=yyy}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `xxx`, rand())"));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `yyy`, rand())"));
    }

    @Test
    public void testIdxRebuildTable() {
        System.out.println(" =================正常情况====================== ");
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateIdxDefaultReq();
        defaultScriptReq.setActionType("REBUILD");
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =================正常发布====================== ");
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_value_local`, rand())"));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_series_local`, rand())"));
        System.out.println(" =================异常情况====================== ");
        script = deployErrorService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =================异常发布====================== ");
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        Assert.assertTrue(TableDeployStatusEnum.FAILURE.equals(deployScriptReq.getTableDeployResp().getStatus()));
        System.out.println("deployErrMsg: " + deployScriptReq.getTableDeployResp().getErrMsg());
        Assert.assertTrue(deployScriptReq.getTableDeployResp().getErrMsg().contains("${删除的维度表本地表名=}"));
        script = "${删除的维度表本地表名=xxx}\n${维度表本地表名=xxx}\n${删除的指标表本地表名=yyy}\n${指标表本地表名=yyy}\n" + script;
        deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployErrorService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `xxx`, rand())"));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `yyy`, rand())"));
    }


    @Test
    public void testIdxRebuild2Table() {
        System.out.println(" =================dim从有到无====================== ");
        GenerateDefaultScriptReq defaultScriptReq = deployService.generateIdxDefaultReq();
        defaultScriptReq.setDimTbName(null);
        defaultScriptReq.setActionType("REBUILD");
        String script = deployService.generateDefaultScript(defaultScriptReq);
        System.out.println(script);
        System.out.println(" =================正常发布====================== ");
        DeployScriptReq deployScriptReq = deployService.generateDefaultDeployReq(defaultScriptReq, script);
        deployService.deployScript(deployScriptReq);
        System.out.println("deployLog:\n " + deployScriptReq.getTableDeployHistoryResp().getDeployLog());
        Assert.assertTrue(TableDeployStatusEnum.SUCCESS.equals(deployScriptReq.getTableDeployResp().getStatus()));
        Assert.assertTrue(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_value_local`, rand())"));
        Assert.assertFalse(deployScriptReq.getTableDeployHistoryResp().getDeployLog().contains("Distributed(`default_cluster`, `trade_metric`, `dws_yy_single_metric_clone1_series_local`, rand())"));
    }


    @Test
    public void testParseSql() {
        String sql = "CREATE TABLE IF NOT EXISTS `default`.dwd_cmdb_object_import_history_local on cluster abc\n" +
                "(\n" +
                "    `date` Date COMMENT '同步日期',\n" +
                "    `source` String COMMENT '数据源标识',\n" +
                "    `exec_time` DateTime COMMENT '执行时间',\n" +
                "    `exists` Int32 COMMENT '执行成功与否',\n" +
                "    `success_object_rows` Nullable(Int32) COMMENT '导入成功的对象数量',\n" +
                "    `success_relation_rows` Nullable(Int32) COMMENT '导入成功的关系数量'\n" +
                ")\n" +
                "ENGINE = ReplicatedMergeTree('/clickhouse/tables/{cluster}/default/ods_cmdb_object_local/{shard}', '{replica}')\n" +
                "PARTITION BY date\n" +
                "PRIMARY KEY (date, source)\n" +
                "ORDER BY (date, source)\n" +
                "SETTINGS index_granularity = 8192";
        String sql2 = "CREATE TABLE aa.bb AS  aa.cc engine = ReplicatedMergeTree('/clickhouse/tables/{cluster}/default/ods_cmdb_object_local/{shard}', '{replica}','aa')";
        System.out.println(GeneralSqlParserUtil.addClusterClause(sql,"default_cluster"));
        System.out.println(GeneralSqlParserUtil.removeReplicatedZkPath(sql2));
        System.out.println(GeneralSqlParserUtil.modifyDatabase(sql,"abc"));
        System.out.println(GeneralSqlParserUtil.addExistsClause(sql));
    }


    public static class ClickhouseModelDeployErrorServiceImplMock extends ClickhouseModelDeployServiceImplMock {
        public String getLocalTbName(Long dsId, String database, String table) {
            return null;
        }

        public String parseTbNameFromSql(String sql) {
            return null;
        }
    }

}
