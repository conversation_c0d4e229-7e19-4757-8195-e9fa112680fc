package com.eoi.jax.web.data.modeling;

import com.eoi.jax.web.data.modeling.model.layer.WarehouseLayerCreateReq;
import com.eoi.jax.web.data.modeling.model.layer.WarehouseLayerResp;
import com.eoi.jax.web.data.modeling.model.layer.WarehouseLayerUpdateReq;
import com.eoi.jax.web.data.modeling.service.WarehouseLayerService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
public class WarehouseLayerServiceTest {
    @Autowired
    private WarehouseLayerService warehouseLayerService;

    @Test
    public void test001() {
        // 新增
        WarehouseLayerCreateReq req = new WarehouseLayerCreateReq();
        req.setName("单元测试1");
        req.setCode("unit_layer_aaaa01");
        req.setCatalog("DS");
        req.setTbType("ODS");
        req.setNameEn("aaa");
        WarehouseLayerResp createResp = warehouseLayerService.create(req);
        Assert.assertNotNull(createResp);

        // 获取
        warehouseLayerService.get(createResp.getId());

        // 更新分层
        WarehouseLayerUpdateReq updateReq = new WarehouseLayerUpdateReq();
        updateReq.setId(createResp.getId());
        updateReq.setName("单元测试1");
        updateReq.setCode("unit_layer_aaaa02");
        updateReq.setNameEn("aaa");
        WarehouseLayerResp updateResp = warehouseLayerService.update(updateReq);
        Assert.assertNotNull(updateResp);
        Assert.assertEquals(updateResp.getCode(), updateReq.getCode());
        // 删除
        warehouseLayerService.delete(updateResp.getId());
    }
}
