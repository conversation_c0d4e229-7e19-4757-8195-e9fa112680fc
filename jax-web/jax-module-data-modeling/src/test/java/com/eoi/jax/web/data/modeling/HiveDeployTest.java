package com.eoi.jax.web.data.modeling;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.enumrate.ColumnTypeEnum;
import com.eoi.jax.web.core.common.enumrate.TableDeployActionTypeEnum;
import com.eoi.jax.web.core.common.enumrate.TableDeployDeployTypeEnum;
import com.eoi.jax.web.core.common.enumrate.TableDeployStatusEnum;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.impl.HiveModelDeployServiceImpl;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.impl.HiveSqlColumnChangeApplier;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy.GenerateDefaultScriptReq;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.hive.HiveModelSetting;
import com.eoi.jax.web.data.modeling.model.columndeploy.ColumnDeployResp;
import com.eoi.jax.web.ingestion.enumrate.DatasourceConnectionPlatformEnum;
import com.eoi.jax.web.repository.entity.TbColumnDeploy;
import com.eoi.jax.web.repository.entity.TbColumnDict;
import com.eoi.jax.web.repository.entity.TbDatasource;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import com.eoi.jax.web.repository.service.TbColumnDeployService;
import com.eoi.jax.web.repository.service.TbColumnDictService;
import com.eoi.jax.web.repository.service.TbDatasourceService;
import com.eoi.jax.web.repository.service.TbTableDeployService;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.eoi.jax.web.data.modeling.AbstractColumnChangesApplierTest.buildSimpleCDR;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
@Transactional
public class HiveDeployTest {

    @Autowired
    private TbDatasourceService tbDatasourceService;

    @Autowired
    private TbTableDeployService tbTableDeployService;

    @Autowired
    private TbColumnDeployService tbColumnDeployService;

    @Autowired
    private TbColumnDictService tbColumnDictService;

    @Autowired
    private HiveModelDeployServiceImpl hiveModelDeployService;

    @After
    public void after() {
        // 清除数据库修改
        tbDatasourceService.remove(new LambdaQueryWrapper<>());
        tbTableDeployService.remove(new LambdaQueryWrapper<>());
        tbColumnDeployService.remove(new LambdaQueryWrapper<>());
        tbColumnDictService.remove(new LambdaQueryWrapper<>());
    }


    private void createTbDatasource() {
        TbDatasource tbDatasource = new TbDatasource();
        tbDatasource.setId(1L);
        tbDatasource.setCode("hivetest2");
        tbDatasource.setName("hivetest2");
        tbDatasource.setPlatform(DatasourceConnectionPlatformEnum.HIVE.code());
        tbDatasource.setStatus("MANUAL");
        tbDatasource.setConnectMode("NORMAL");
        tbDatasource.setDescription(null);
        tbDatasource.setRegisterCenterId(null);
        tbDatasource.setSetting("{\"hiveJdbcPassword\":\"\",\"driver\":\"\",\"hiveJdbcUser\":\"root\",\"hiveJdbcUrl\":\"**************************\",\"clusterId\":1136985132925952}");
        tbDatasource.setIsDeleted(0);
        tbDatasource.setCreateTime(new Date());
        tbDatasource.setUpdateTime(new Date());
        tbDatasource.setCreateUser(null);
        tbDatasource.setUpdateUser(null);
        tbDatasource.setConnectDetail(null);
        tbDatasource.setLastConnectTime(null);
        tbDatasourceService.save(tbDatasource);
    }

    private void createTbTableDeploy() {
        TbTableDeploy tbTableDeploy = new TbTableDeploy();
        tbTableDeploy.setId(1L);
        tbTableDeploy.setPlatform(DatasourceConnectionPlatformEnum.HIVE.code());
        tbTableDeploy.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        tbTableDeploy.setActionType(TableDeployActionTypeEnum.CREATE.code());
        tbTableDeploy.setScript(null);
        tbTableDeploy.setBusinessFlowId(1L);
        tbTableDeploy.setName("model_test");
        tbTableDeploy.setVersion(1);
        tbTableDeploy.setStatus(TableDeployStatusEnum.SUCCESS.code());
        tbTableDeploy.setLastDeployTime(null);
        tbTableDeploy.setErrMsg(null);
        tbTableDeploy.setDsId(1L);
        tbTableDeploy.setSetting("{\"database\":\"default\"}");
        tbTableDeploy.setTbId(1L);
        tbTableDeploy.setTbName("model_test");
        tbTableDeploy.setTbAlias("model_test");
        tbTableDeploy.setLifecycle(null);
        tbTableDeploy.setDescription(null);
        tbTableDeploy.setIsDeleted(0);
        tbTableDeploy.setCreateTime(new Date());
        tbTableDeploy.setUpdateTime(new Date());
        tbTableDeploy.setCreateUser(null);
        tbTableDeploy.setUpdateUser(null);
        tbTableDeployService.save(tbTableDeploy);
    }


    private void createColumnDeploy() {
        TbColumnDeploy tbColumnDeploy1 = buildTbColumnDeploy(1L, "ID", ColumnTypeEnum.BIGINT.code(), "系统id", null);
        TbColumnDeploy tbColumnDeploy2 = buildTbColumnDeploy(2L, "UID", ColumnTypeEnum.STRING.code(), "用户id", 1L);
        TbColumnDeploy tbColumnDeploy3 = buildTbColumnDeploy(3L, "NAME", ColumnTypeEnum.STRING.code(), "姓名", 3L);
        TbColumnDeploy tbColumnDeploy4 = buildTbColumnDeploy(4L, "HEIGHT", ColumnTypeEnum.DECIMAL.code(), "身高", 2L);
        TbColumnDeploy tbColumnDeploy5 = buildTbColumnDeploy(5L, "CN_NAME", ColumnTypeEnum.STRING.code(), "中文姓名", null);
        TbColumnDeploy tbColumnDeploy6 = buildTbColumnDeploy(6L, "CREATE_TIME", ColumnTypeEnum.TIMESTAMP.code(), "创建时间", null);
        tbColumnDeployService.saveBatch(CollUtil.newArrayList(tbColumnDeploy1, tbColumnDeploy2, tbColumnDeploy3, tbColumnDeploy4, tbColumnDeploy5, tbColumnDeploy6));
    }


    private TbColumnDeploy buildTbColumnDeploy(Long colId, String colName, String colType, String colDisplay, Long dicId) {
        TbColumnDeploy tbColumnDeploy = new TbColumnDeploy();
        tbColumnDeploy.setId(colId);
        tbColumnDeploy.setTableDeployId(1L);
        tbColumnDeploy.setColId(colId);
        tbColumnDeploy.setColType(colType);
        tbColumnDeploy.setColName(colName);
        tbColumnDeploy.setColDisplay(colDisplay);
        tbColumnDeploy.setDescription("");
        tbColumnDeploy.setIsPrimaryKey(0);
        tbColumnDeploy.setIsNotNull(0);
        tbColumnDeploy.setDicId(dicId);
        tbColumnDeploy.setDicSetting("");
        tbColumnDeploy.setIsDeleted(0);
        tbColumnDeploy.setCreateTime(new Date());
        tbColumnDeploy.setUpdateTime(new Date());
        return tbColumnDeploy;
    }

    private void createColumnDict() {
        TbColumnDict tbColumnDict1 = buildTbColumnDict(1L, "uid", "STRING", "20", "");
        TbColumnDict tbColumnDict2 = buildTbColumnDict(2L, "height", "DECIMAL", "12", "2");
        TbColumnDict tbColumnDict3 = buildTbColumnDict(3L, "name", "STRING", "128", "2");
        tbColumnDictService.saveBatch(CollUtil.newArrayList(tbColumnDict1, tbColumnDict2, tbColumnDict3));
    }


    private TbColumnDict buildTbColumnDict(Long id, String code, String colType, String colLength, String numPrecision) {
        TbColumnDict tbColumnDict = new TbColumnDict();
        tbColumnDict.setId(id);
        tbColumnDict.setGroupId(id);
        tbColumnDict.setNo("F0000" + id);
        tbColumnDict.setCode(code);
        tbColumnDict.setName(code);
        tbColumnDict.setNameEn(code);
        tbColumnDict.setColType(colType);
        tbColumnDict.setColLength(colLength);
        tbColumnDict.setNumPrecision(numPrecision);
        tbColumnDict.setDefaultValue(null);
        tbColumnDict.setBizDef(null);
        tbColumnDict.setEnumId(null);
        tbColumnDict.setIsDeleted(0);
        tbColumnDict.setCreateTime(new Date());
        tbColumnDict.setUpdateTime(new Date());
        tbColumnDict.setCreateUser(null);
        tbColumnDict.setUpdateUser(null);
        return tbColumnDict;
    }


    @Test
    public void testCreateTable() throws Exception {
        createTbDatasource();
        createColumnDict();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.CREATE.code());
        req.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        req.setSettingModel(new HiveModelSetting());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);

        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), 3L));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        columnDeployList.add(buildSimpleCDR(5L, "CN_NAME", "中文姓名", ColumnTypeEnum.STRING.code(), null, null));
        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("bca43fd93e593992", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));

    }



    @Test
    public void testCreateTable1() throws Exception {
        createTbDatasource();
        createColumnDict();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.CREATE.code());
        req.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);
        req.setSettingModel(new HiveModelSetting());

        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), 3L));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        columnDeployList.add(buildSimpleCDR(5L, "CN_NAME", "中文姓名", ColumnTypeEnum.STRING.code(), null, null));
        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));
        columnDeployList.add(buildSimpleCDR(7L, "REMARK", "复杂类型测试", ColumnTypeEnum.COMPLEX.code(), null, null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("46c3d1a5a665a630", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));

    }


    @Test
    public void testAssociationTable() throws Exception {
        createTbDatasource();
        createColumnDict();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.ASSOCIATION.code());
        req.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);
        req.setSettingModel(new HiveModelSetting());
        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), 3L));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        columnDeployList.add(buildSimpleCDR(5L, "CN_NAME", "中文姓名", ColumnTypeEnum.STRING.code(), null, null));
        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("bca43fd93e593992", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));

    }


    @Test
    public void testRebuildTable() throws Exception {
        createTbDatasource();
        createColumnDict();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.REBUILD.code());
        req.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        req.setSettingModel(new HiveModelSetting());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);

        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), 3L));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        columnDeployList.add(buildSimpleCDR(5L, "CN_NAME", "中文姓名", ColumnTypeEnum.STRING.code(), null, null));
        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("1330e1a3e5ab49c7", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));

    }


    @Test
    public void testIncrementColumnAdd() {
        createTbDatasource();
        createColumnDict();
        createTbTableDeploy();
        createColumnDeploy();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setTbId(1L);
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.INCREMENT.code());
        req.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);
        req.setSettingModel(new HiveModelSetting());

        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), 3L));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        columnDeployList.add(buildSimpleCDR(5L, "CN_NAME", "中文姓名", ColumnTypeEnum.STRING.code(), null, null));
        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));
        // 添加
        columnDeployList.add(buildSimpleCDR(7L, "AGE", "年龄", ColumnTypeEnum.INT.code(), "6", null));
        columnDeployList.add(buildSimpleCDR(8L, "WEIGHT", "体重", ColumnTypeEnum.INT.code(), "6", null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("aee66cf05ec3a3b3", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));

    }


    @Test
    public void testIncrementColumnAddModify() {
        createTbDatasource();
        createColumnDict();
        createTbTableDeploy();
        createColumnDeploy();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setTbId(1L);
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.INCREMENT.code());
        req.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);
        req.setSettingModel(new HiveModelSetting());


        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        // 修改字段长度 128 ==> 64
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), "64", null));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        // 修改字段名称CN_NAME ==> NICK_NAME
        columnDeployList.add(buildSimpleCDR(5L, "NICK_NAME", "昵称", ColumnTypeEnum.STRING.code(), null, null));
        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));
        // 添加字段
        columnDeployList.add(buildSimpleCDR(7L, "introduction", "简介", ColumnTypeEnum.STRING.code(), "66666", null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("854f5797c90514da", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));
    }


    @Test
    public void testIncrementColumnRemove() {
        createTbDatasource();
        createColumnDict();
        createTbTableDeploy();
        createColumnDeploy();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setTbId(1L);
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.INCREMENT.code());
        req.setDeployType(TableDeployDeployTypeEnum.TABLE.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);
        req.setSettingModel(new HiveModelSetting());
        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), 3L));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        columnDeployList.add(buildSimpleCDR(5L, "CN_NAME", "中文姓名", ColumnTypeEnum.STRING.code(), null, null));
//        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("1ca3801635e02390", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));
    }


    @Test
    public void testCreateView() throws Exception {
        createTbDatasource();
        createColumnDict();

        GenerateDefaultScriptReq req = new GenerateDefaultScriptReq();
        req.setDsId(1L);
        req.setActionType(TableDeployActionTypeEnum.CREATE.code());
        req.setDeployType(TableDeployDeployTypeEnum.VIEW.code());
        req.setTbName("model_test");
        req.setTbAlias(req.getTbName());
        List<ColumnDeployResp> columnDeployList = new ArrayList<>();
        req.setColumnDeployList(columnDeployList);
        req.setSettingModel(new HiveModelSetting());

        columnDeployList.add(buildSimpleCDR(1L, "ID", "系统id", ColumnTypeEnum.BIGINT.code(), null, null));
        columnDeployList.add(buildSimpleCDR(2L, "UID", "用户id", ColumnTypeEnum.STRING.code(), 1L));
        columnDeployList.add(buildSimpleCDR(3L, "NAME", "姓名", ColumnTypeEnum.STRING.code(), 3L));
        columnDeployList.add(buildSimpleCDR(4L, "HEIGHT", "身高", ColumnTypeEnum.DECIMAL.code(), 2L));
        columnDeployList.add(buildSimpleCDR(5L, "CN_NAME", "中文姓名", ColumnTypeEnum.STRING.code(), null, null));
        columnDeployList.add(buildSimpleCDR(6L, "CREATE_TIME", "创建时间", ColumnTypeEnum.TIMESTAMP.code(), null, null));

        String hql = hiveModelDeployService.generateDefaultScript(req);
        System.out.println(hql.replaceAll("\t", "    "));
        MD5 md5 = MD5.create();
        Assert.assertEquals("227586d66c34516c", md5.digestHex16(hql.getBytes(StandardCharsets.UTF_8)));

    }



    @Test
    public void testColumnChanges() throws Exception {
        List<ColumnDeployResp> oldColumns = new ArrayList<>();
        List<ColumnDeployResp> newColumns = new ArrayList<>();
        oldColumns.add(buildSimpleCDR(1L, "ID", "唯一ID", "INT", null, null));
        oldColumns.add(buildSimpleCDR(2L, "NAME", "姓名", "VARCHAR", "200", null));
        oldColumns.add(buildSimpleCDR(3L, "CN_NAME", "中文姓名", "STRING", null, null));
        oldColumns.add(buildSimpleCDR(4L, "EN_NAME", "英文姓名", "STRING", null, null));

        newColumns.add(buildSimpleCDR(1L, "ID", "唯一ID", "INT", null, null));
        newColumns.add(buildSimpleCDR(5L, "BIRTHDAY", "生日", "DATETIME", null, null));
        newColumns.add(buildSimpleCDR(3L, "NAME", "中文姓名，原中文名", "STRING", null, null));
        newColumns.add(buildSimpleCDR(6L, "SEX", "性别", "STRING", null, null));
        newColumns.add(buildSimpleCDR(4L, "EN_NAME", "英文姓名", "STRING", null, null));


        HiveSqlColumnChangeApplier applier = new HiveSqlColumnChangeApplier("test", oldColumns, newColumns);
        applier.applyChanges();
        System.out.println(applier.toSql());
    }
}
