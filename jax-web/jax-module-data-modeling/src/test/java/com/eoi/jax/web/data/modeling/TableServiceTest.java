package com.eoi.jax.web.data.modeling;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.constant.LayerCatalog;
import com.eoi.jax.web.core.common.constant.TableType;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.data.modeling.model.column.ColumnCreateReq;
import com.eoi.jax.web.data.modeling.model.table.TableCreateReq;
import com.eoi.jax.web.data.modeling.model.table.TableResp;
import com.eoi.jax.web.data.modeling.service.*;
import com.eoi.jax.web.repository.entity.TbBusinessCategory;
import com.eoi.jax.web.repository.entity.TbTable;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration(classes = {SpringUnitTestConfig.class})
@AutoConfigureMockMvc
@Transactional
public class TableServiceTest {
    @Autowired
    private TableService tableService;
    @Autowired
    private TableTestConfig tableTestConfig;
    @Autowired
    private BusinessCategoryService businessCategoryService;
    @Autowired
    private JaxRepository jaxRepository;
    @Autowired
    private WarehouseLayerService layerService;
    @Autowired
    private DataDomainService dataDomainService;
    @Autowired
    private MeasureUnitService measureUnitService;

    @Before
    public void prepare() {
        tableTestConfig.prepare();
    }

    /**
     * 明细表表的增删改查
     */
    @Test
    public void test001() {
        String str = "{\"tbType\": \"DWD\"," +
                "  \"storagePolicy\": \"mi\"," +
                "  \"tbName\": \"ss_detail\"," +
                "  \"tbAlias\": \"xx明细表\"," +
                "  \"lifecycle\": 6," +
                "  \"columns\": [" +
                "    {" +
                "      \"colType\": \"INT\"," +
                "      \"colName\": \"id\"," +
                "      \"colDisplay\": \"id\"," +
                "      \"isPrimaryKey\": true," +
                "      \"isNotNull\": true," +
                "      \"colCatalog\": \"PROPERTIES\"" +
                "    }," +
                "    {" +
                "      \"colType\": \"STRING\"," +
                "      \"colName\": \"name\"," +
                "      \"colDisplay\": \"名称\"," +
                "      \"isPrimaryKey\": false," +
                "      \"isNotNull\": false," +
                "      \"colCatalog\": \"PROPERTIES\"" +
                "    }" +
                "  ]" +
                "}";
        TableCreateReq createReq = JsonUtil.decode(str, TableCreateReq.class);
        createReq.setLayerId(layerService.list(Arrays.asList(LayerCatalog.COMMON_DATA_MODEL.code()),
                TableType.DIM.code()).get(0).getId());
        TbBusinessCategory bizLeaf = jaxRepository.businessCategory().getOne(new LambdaQueryWrapper<TbBusinessCategory>()
                .eq(TbBusinessCategory::getIsLeaf, 1).last("limit 1"));
        createReq.setBizId(bizLeaf.getId());
        createReq.setDomId(dataDomainService.all().get(0).getId());
        ColumnCreateReq columnCreateReq = createReq.getColumns().get(1);
        columnCreateReq.setUnitId(measureUnitService.all().get(0).getId());
        TableResp tableResp = tableService.create(createReq);
        TableResp getResp = tableService.get(tableResp.getId());
        Assert.assertNotNull(getResp);
        Assert.assertTrue(StrUtil.isNotBlank(getResp.getTbName()));
        Assert.assertTrue(CollUtil.isNotEmpty(getResp.getColumns()));
        Assert.assertTrue(StrUtil.isNotBlank(getResp.getLayerName()));
        Assert.assertTrue(StrUtil.isNotBlank(getResp.getBizName()));
        Assert.assertTrue(StrUtil.isNotBlank(getResp.getDomName()));
        Assert.assertTrue(StrUtil.isNotBlank(getResp.getColumns().get(1).getUnitName()));

        TbTable table = new TbTable();
        BeanUtil.copyProperties(getResp, table);
        table.setDimTbId(1111111111111111L);
        table.setDimTbName("dim_tb_name");
        table.setIdxTbName("idx_tb_name");
        table.setStorageMode("SINGLE");
        table.setRelateDim(1);
        jaxRepository.table().updateById(table);

        // 分层的关联引用
        List<UsageResp> layerUsages = layerService.usage(createReq.getLayerId());
        Assert.assertTrue(CollUtil.isNotEmpty(layerUsages));
        Optional<UsageResp> tb = layerUsages.stream().filter(x -> createReq.getTbType().equals(x.getRelatedObjType()) &&
                x.getRelatedObjName().equals(createReq.getTbName())).findAny();
        Assert.assertTrue(tb.isPresent());


        // 业务分类的关联引用
        List<UsageResp> bizUsage = businessCategoryService.usage(createReq.getBizId());
        Assert.assertTrue(CollUtil.isNotEmpty(bizUsage));
        Assert.assertTrue(bizUsage.stream().filter(x -> createReq.getTbType().equals(x.getRelatedObjType()) &&
                x.getRelatedObjName().equals(createReq.getTbName())).findAny().isPresent());

        // 数据域的关联引用
        List<UsageResp> domUsage = dataDomainService.usage(createReq.getDomId());
        Assert.assertTrue(CollUtil.isNotEmpty(domUsage));
        Assert.assertTrue(domUsage.stream().filter(x -> createReq.getTbType().equals(x.getRelatedObjType()) &&
                x.getRelatedObjName().equals(createReq.getTbName())).findAny().isPresent());

        tableService.delete(tableResp.getId());

    }

}
