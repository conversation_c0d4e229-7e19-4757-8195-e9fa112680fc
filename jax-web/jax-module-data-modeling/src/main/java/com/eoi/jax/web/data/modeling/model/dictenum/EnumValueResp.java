package com.eoi.jax.web.data.modeling.model.dictenum;

import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.repository.entity.TbEnumValue;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Author: yaru.ma
 * Date: 2022/11/18 15:09
 */
public class EnumValueResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbEnumValue> {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "编号取值")
    private String enumValue;

    @Schema(description = "编码名称")
    private String name;

    @Schema(description = "英文名称")
    private String nameEn;

    @Schema(description = "编码含义")
    private String description;

    @Schema
    private Date createTime;

    @Schema
    private Long createUser;

    @Schema
    private Date updateTime;

    @Schema
    private Long updateUser;

    public String getEnumValue() {
        return enumValue;
    }

    public void setEnumValue(String enumValue) {
        this.enumValue = enumValue;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public EnumValueResp fromEntity(TbEnumValue entity) {
        return copyFrom(entity);
    }

}
