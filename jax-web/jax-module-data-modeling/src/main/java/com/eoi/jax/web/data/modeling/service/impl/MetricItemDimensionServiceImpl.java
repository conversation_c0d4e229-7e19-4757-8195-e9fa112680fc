package com.eoi.jax.web.data.modeling.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.core.service.BaseService;
import com.eoi.jax.web.data.modeling.model.metricdimension.MetricDimensionResp;
import com.eoi.jax.web.data.modeling.model.metricitemdim.MetricItemDimensionCreateReq;
import com.eoi.jax.web.data.modeling.model.metricitemdim.MetricItemDimensionQueryReq;
import com.eoi.jax.web.data.modeling.model.metricitemdim.MetricItemDimensionResp;
import com.eoi.jax.web.data.modeling.model.metricitemdim.MetricItemDimensionUpdateReq;
import com.eoi.jax.web.data.modeling.service.MetricItemDimensionService;
import com.eoi.jax.web.repository.entity.TbMetricItemDimension;
import com.eoi.jax.web.repository.search.result.MetricDimensionResult;
import com.eoi.jax.web.repository.service.TbMetricDimensionService;
import com.eoi.jax.web.repository.service.TbMetricItemDimensionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
@Service
public class MetricItemDimensionServiceImpl extends BaseService<
        TbMetricItemDimensionService,
        TbMetricItemDimension,
        MetricItemDimensionResp,
        MetricItemDimensionCreateReq,
        MetricItemDimensionUpdateReq,
        MetricItemDimensionQueryReq> implements MetricItemDimensionService {

    @Autowired
    private TbMetricItemDimensionService tbMetricItemDimensionService;


    public MetricItemDimensionServiceImpl(@Autowired TbMetricItemDimensionService tbMetricItemDimensionService) {
        super(tbMetricItemDimensionService);
    }

    @Override
    public MetricItemDimensionResp create(MetricItemDimensionCreateReq req) {
        return super.create(req);
    }

    @Override
    public MetricItemDimensionResp update(MetricItemDimensionUpdateReq req) {
        return super.update(req);
    }

    /**
     * 获取
     *
     * @param id
     * @return
     */
    @Override
    public MetricItemDimensionResp get(Long id) {
        MetricItemDimensionResp resp = super.get(id);

        return resp;
    }


    @Override
    public void deleteByMetricItemId(Long metricItemId) {
        LambdaQueryWrapper<TbMetricItemDimension> wrapper = new LambdaQueryWrapper();
        wrapper.eq(TbMetricItemDimension::getMetricId, metricItemId);
        tbMetricItemDimensionService.remove(wrapper);
    }

    @Override
    public void addRelation(Long metricItemId, Set<Long> dimIds) {
        Assert.notNull(metricItemId, "metricItemId不能为空");
        Assert.notNull(dimIds, "指标维度ids不能为空");
        // 删除之前老的关系
        deleteByMetricItemId(metricItemId);
        if (CollUtil.isEmpty(dimIds)) {
            return;
        }
        // 批量插入
        List<TbMetricItemDimension> tagRelationList = dimIds.stream().map(it -> {
            TbMetricItemDimension relation = new TbMetricItemDimension();
            relation.setMetricId(metricItemId);
            relation.setMetricDimensionId(it);
            ModelBeanUtil.setCreateDefaultValue(relation);
            return relation;
        }).collect(Collectors.toList());
        tbMetricItemDimensionService.saveBatch(tagRelationList);
    }

    /**
     * 分页查询
     *
     * @param req
     * @return
     */
    @Override
    public Paged<MetricItemDimensionResp> query(MetricItemDimensionQueryReq req) {
        Paged<MetricItemDimensionResp> respPaged = super.query(req);
        return respPaged;
    }

    @Override
    public MetricItemDimensionResp delete(Long id) {
        List<UsageResp> usageRespList = usage(id);
        if (usageRespList.size() > 0) {
            throw new BizException(ResponseCode.DELETE_IN_USE, "使用中，不能删除");
        }
        return super.delete(id);
    }

    @Resource
    private TbMetricDimensionService tbMetricDimensionService;

    @Override
    public List<MetricDimensionResp> getByMetricItemId(Long metricItemId) {
        return tbMetricDimensionService.getByMetricItemId(metricItemId).stream()
                .map(x -> new MetricDimensionResp().fromEntity(x)).collect(Collectors.toList());
    }

    @Override
    public List<MetricDimensionResp> getByMetricItemId(List<Long> metricItemIdList) {
        List<List<Long>> splitList = CollUtil.split(metricItemIdList, 500);
        List<MetricDimensionResult> resultList = new ArrayList<>();
        for (List<Long> list : splitList) {
            List<MetricDimensionResult> metricDimensionList = tbMetricDimensionService.getByMetricItemId(list);
            if (CollUtil.isNotEmpty(metricDimensionList)) {
                resultList.addAll(metricDimensionList);
            }
        }
        return resultList.stream()
            .map(x -> new MetricDimensionResp().fromEntity(x)).collect(Collectors.toList());
    }

    @Override
    public List<UsageResp> usageByEntity(TbMetricItemDimension entity) {
        List<UsageResp> result = new ArrayList<>();
        Long id = entity.getId();

        return result;
    }


}
