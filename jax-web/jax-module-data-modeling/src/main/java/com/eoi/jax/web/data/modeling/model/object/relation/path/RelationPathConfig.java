package com.eoi.jax.web.data.modeling.model.object.relation.path;

import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/11/20
 * @Desc:
 **/
public class RelationPathConfig {

    /**
     * nodeType : TAG
     * nodeId : tag-1
     * parentNodeId : tag-1
     * tags : [{"objectId":"1859152637363201","objectType":"OBJECT"}]
     * sourceId : tag-1
     * targetId : tag-2
     * relations : [{"relationId":"1830901215693824"}]
     */

    private String nodeType;
    private String nodeId;
    private String parentNodeId;
    private String sourceId;
    private String targetId;
    private List<TagsBean> tags;
    private List<RelationsBean> relations;


    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getParentNodeId() {
        return parentNodeId;
    }

    public void setParentNodeId(String parentNodeId) {
        this.parentNodeId = parentNodeId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public List<TagsBean> getTags() {
        return tags;
    }

    public void setTags(List<TagsBean> tags) {
        this.tags = tags;
    }

    public List<RelationsBean> getRelations() {
        return relations;
    }

    public void setRelations(List<RelationsBean> relations) {
        this.relations = relations;
    }

    public static class TagsBean {
        /**
         * objectId : 1859152637363201
         * objectType : OBJECT
         */

        private Long objectId;
        private String objectType;

        public Long getObjectId() {
            return objectId;
        }

        public void setObjectId(Long objectId) {
            this.objectId = objectId;
        }

        public String getObjectType() {
            return objectType;
        }

        public void setObjectType(String objectType) {
            this.objectType = objectType;
        }
    }

    public static class RelationsBean {
        /**
         * relationId : 1830901215693824
         */

        private Long relationId;

        public Long getRelationId() {
            return relationId;
        }

        public void setRelationId(Long relationId) {
            this.relationId = relationId;
        }
    }
}
