package com.eoi.jax.web.data.modeling.model.time;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbTimePeriod;

/**
 * @Author: <PERSON>
 * @Date: 2022/11/21
 **/
public class TimePeriodQuerySortReq implements ISortReq<TbTimePeriod> {

    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbTimePeriod> order(QueryWrapper<TbTimePeriod> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbTimePeriod::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbTimePeriod::getUpdateTime);
        return wrapper;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
