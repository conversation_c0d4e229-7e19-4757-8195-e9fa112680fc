package com.eoi.jax.web.data.modeling.service;

import com.eoi.jax.web.core.service.IBaseProjectAuthService;
import com.eoi.jax.web.data.modeling.model.cloumndict.ColumnDictResp;
import com.eoi.jax.web.data.modeling.model.dictgroup.DictGroupCreateReq;
import com.eoi.jax.web.data.modeling.model.dictgroup.DictGroupQueryReq;
import com.eoi.jax.web.data.modeling.model.dictgroup.DictGroupResp;
import com.eoi.jax.web.data.modeling.model.dictgroup.DictGroupUpdateReq;
import com.eoi.jax.web.repository.entity.TbDictGroup;
import com.eoi.jax.web.repository.service.TbDictGroupService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 字段标准-标准集
 *
 * @Author: yaru.ma
 * @Date: 2022/11/21
 */
public interface DictGroupService extends IBaseProjectAuthService<
        TbDictGroupService,
        TbDictGroup,
        DictGroupResp,
        DictGroupCreateReq,
        DictGroupUpdateReq,
        DictGroupQueryReq> {

    /**
     * 查询标准集下的标准
     *
     * @param groupId
     * @return
     */
    List<ColumnDictResp> listChildByGroupId(Long groupId);

    /**
     * 删除目录下的所有标准集
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByFolderId(List<Long> id);
}
