package com.eoi.jax.web.data.modeling.model.cmdbobject;

/**
 * <AUTHOR>
 * @date 2024/5/14
 */
public class CmdbObjectRelationResp {

    private String tag;

    private String objectTableName;

    private String vid;

    private String objectId;

    private String objectName;

    private String relationType;

    private String relationTypeDesc;

    private String direction;


    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getObjectTableName() {
        return objectTableName;
    }

    public void setObjectTableName(String objectTableName) {
        this.objectTableName = objectTableName;
    }

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public String getRelationType() {
        return relationType;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType;
    }

    public String getRelationTypeDesc() {
        return relationTypeDesc;
    }

    public void setRelationTypeDesc(String relationTypeDesc) {
        this.relationTypeDesc = relationTypeDesc;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
}
