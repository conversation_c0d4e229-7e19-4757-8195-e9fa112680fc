package com.eoi.jax.web.data.modeling.model.metricset;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbMetricSet;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Map;


/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class MetricSetCreateReq implements ICreateModel<TbMetricSet> {

    @Schema(description = "id")
    private Long id;


    @Schema(description = "指标集名称")
    @NotBlank(message = "指标集名称不能为空")
    private String name;

    @Schema(description = "指标集编码")
    private String code;

    @Schema(description = "模型对象id")
    private Long objId;

    @Schema(description = "模型分类id")
    private Long categoryId;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "来源平台标识")
    private String sourcePlatformCode;

    @Schema(description = "来源平台元数据信息")
    private Map<String, Object> sourcePlatformMeta;

    @Override
    public TbMetricSet toEntity() {
        TbMetricSet entity = toEntity(new TbMetricSet());
        entity.setSourcePlatformMeta(JsonUtil.encode(sourcePlatformMeta));
        if (id != null && id.longValue() > 0L) {
            entity.setId(id);
        }
        entity.setId(id);
        return entity;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public String getSourcePlatformCode() {
        return sourcePlatformCode;
    }

    public void setSourcePlatformCode(String sourcePlatformCode) {
        this.sourcePlatformCode = sourcePlatformCode;
    }

    public Map<String, Object> getSourcePlatformMeta() {
        return sourcePlatformMeta;
    }

    public void setSourcePlatformMeta(Map<String, Object> sourcePlatformMeta) {
        this.sourcePlatformMeta = sourcePlatformMeta;
    }
}
