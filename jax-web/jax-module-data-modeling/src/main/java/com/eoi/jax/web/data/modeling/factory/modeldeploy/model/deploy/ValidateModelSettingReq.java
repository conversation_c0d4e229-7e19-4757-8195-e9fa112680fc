package com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy;

import com.eoi.jax.web.data.modeling.factory.modeldeploy.ITableDeploySettingModel;
import com.eoi.jax.web.data.modeling.model.columndeploy.ColumnDeployResp;
import com.eoi.jax.web.data.modeling.model.tabledeploy.TableDeployResp;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2023/1/6 10:25
 */
public class ValidateModelSettingReq {

    @Schema(description = "数据源类型：KAFKA, ELASTICSEARCH, CLICKHOUSE, MYSQL等")
    private String platform;

    @Schema(description = "数据源id")
    private Long dsId;

    @Schema(description = "发布类型：TABLE，VIEW")
    private String deployType;

    @Schema(description = "发布动作类型：CREATE|REBUILD|INCREMENT|ASSOCIATION")
    private String actionType;

    @Schema(description = "删除模型类型: LOGIC|PHYSICAL")
    private String deleteType;

    @Schema(description = "发布名称")
    private String name;

    @Schema(description = "明细表id")
    private Long tbId;

    @Schema(description = "http请求上传的setting")
    private ITableDeploySettingModel settingModel;

    @Schema(description = "数据库已有的setting（更新的时候需要校验某些值是否被更改了）")
    private ITableDeploySettingModel dbSettingModel;

    @Schema(description = "http请求上传的脚本")
    private String script;

    @Schema(description = "数据库已有的脚本（更新的时候需要校验某些值是否被更改了）")
    private String dbScript;

    @Schema(description = "数据库最近一次发布成功的版本")
    private TableDeployResp lastSuccessTableDeploy;

    @Schema(description = "表名")
    private String tbName;

    @Schema(description = "表中文名")
    private String tbAlias;

    @Schema(description = "生命周期/天")
    private Long lifecycle;

    @Schema(description = "字段列表")
    private List<ColumnDeployResp> columnDeployList;

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Long getDsId() {
        return dsId;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

    public String getDeployType() {
        return deployType;
    }

    public void setDeployType(String deployType) {
        this.deployType = deployType;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public ITableDeploySettingModel getDbSettingModel() {
        return dbSettingModel;
    }

    public String getDeleteType() {
        return deleteType;
    }

    public void setDeleteType(String deleteType) {
        this.deleteType = deleteType;
    }

    public void setDbSettingModel(ITableDeploySettingModel dbSettingModel) {
        this.dbSettingModel = dbSettingModel;
    }

    public TableDeployResp getLastSuccessTableDeploy() {
        return lastSuccessTableDeploy;
    }

    public void setLastSuccessTableDeploy(TableDeployResp lastSuccessTableDeploy) {
        this.lastSuccessTableDeploy = lastSuccessTableDeploy;
    }

    public ITableDeploySettingModel getSettingModel() {
        return settingModel;
    }

    public void setSettingModel(ITableDeploySettingModel settingModel) {
        this.settingModel = settingModel;
    }

    public String getDbScript() {
        return dbScript;
    }

    public void setDbScript(String dbScript) {
        this.dbScript = dbScript;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getTbAlias() {
        return tbAlias;
    }

    public void setTbAlias(String tbAlias) {
        this.tbAlias = tbAlias;
    }

    public Long getLifecycle() {
        return lifecycle;
    }

    public void setLifecycle(Long lifecycle) {
        this.lifecycle = lifecycle;
    }

    public List<ColumnDeployResp> getColumnDeployList() {
        return columnDeployList;
    }

    public void setColumnDeployList(List<ColumnDeployResp> columnDeployList) {
        this.columnDeployList = columnDeployList;
    }
}
