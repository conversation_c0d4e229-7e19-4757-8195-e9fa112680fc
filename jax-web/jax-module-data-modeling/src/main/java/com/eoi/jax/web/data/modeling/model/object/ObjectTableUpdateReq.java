package com.eoi.jax.web.data.modeling.model.object;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbObjectTable;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 对象模型
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */

public class ObjectTableUpdateReq implements IUpdateModel<TbObjectTable> {


    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 父分类Id
     */
    @Schema(description = "父分类Id")
    @NotNull(message = "对象分类不能为空不能为空")
    private Long categoryId;


    /**
     * 对象模型中文名
     */
    @Schema(description = "对象模型中文名")
    @NotNull(message = "对象模型中文名不能为空")
    private String name;

    /**
     * 对象模型英文名
     */
    @Schema(description = "对象模型英文名")
    private String nameEn;
    /**
     * 表名称
     */
    @Schema(description = "对象模型表名称")
    private String tbName;
    /**
     * 对象类型标识
     */
    @Schema(description = "对象类型标识")
    @NotNull(message = "对象类型标识不能为空")
    private String typeCode;


    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    @Schema(description = "来源平台标识")
    private String sourcePlatformCode;

    @Schema(description = "来源平台元数据信息")
    private Map<String, Object> sourcePlatformMeta;


    /**
     * 数仓分层id
     */
    @Schema(description = "数仓分层id")
    private Long layerId;


    /**
     * 业务分类id
     */
    @Schema(description = "业务分类id")
    private Long bizId;


    /**
     * 数据域id
     */
    @Schema(description = "数据域id")
    private Long domId;


    /**
     * 业务过程id
     */
    @Schema(description = "业务过程id")
    private Long procId;
    /**
     * 字段列表
     */
    @Schema(description = "字段列表")
    private List<ObjectTableColumn> columns;

    /**
     * 标签信息
     */
    @Schema(description = "标签信息")
    private List<String> tags;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSourcePlatformCode() {
        return sourcePlatformCode;
    }

    public void setSourcePlatformCode(String sourcePlatformCode) {
        this.sourcePlatformCode = sourcePlatformCode;
    }

    public Map<String, Object> getSourcePlatformMeta() {
        return sourcePlatformMeta;
    }

    public void setSourcePlatformMeta(Map<String, Object> sourcePlatformMeta) {
        this.sourcePlatformMeta = sourcePlatformMeta;
    }

    public Long getLayerId() {
        return layerId;
    }

    public void setLayerId(Long layerId) {
        this.layerId = layerId;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Long getProcId() {
        return procId;
    }

    public void setProcId(Long procId) {
        this.procId = procId;
    }

    public Long getDomId() {
        return domId;
    }

    public void setDomId(Long domId) {
        this.domId = domId;
    }

    public List<ObjectTableColumn> getColumns() {
        return columns;
    }

    public void setColumns(List<ObjectTableColumn> columns) {
        this.columns = columns;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return "ObjectTableUpdateReq{" +
                "id=" + id +
                ", categoryId=" + categoryId +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", tbName='" + tbName + '\'' +
                ", typeCode='" + typeCode + '\'' +
                ", description='" + description + '\'' +
                ", layerId=" + layerId +
                ", bizId=" + bizId +
                ", domId=" + domId +
                ", procId=" + procId +
                ", columns=" + columns +
                ", tags=" + tags +
                '}';
    }

    @Override
    public TbObjectTable toEntity(TbObjectTable entityOld) {
        TbObjectTable entityNew = IUpdateModel.super.toEntity(entityOld);
        entityNew.setSourcePlatformMeta(JsonUtil.encode(sourcePlatformMeta));
        return entityNew;
    }
}
