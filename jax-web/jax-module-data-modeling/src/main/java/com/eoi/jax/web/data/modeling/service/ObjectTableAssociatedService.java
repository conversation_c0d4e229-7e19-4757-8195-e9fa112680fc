package com.eoi.jax.web.data.modeling.service;

import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/9/7
 * @Desc:
 **/
public interface ObjectTableAssociatedService {
    /**
     * 根据上级id 和上级id的 path构建当前节点parentPath
     * @param parentId
     * @param parentPath
     * @return
     */
    String getParentPath(Long parentId, String parentPath);

    /**
     * 将parentPath转换为名称
     * @param categoryPath
     * @return
     */
    String getParentPathName(String categoryPath);

    /**
     * 批量获取父类名称
     * @param categoryPath
     * @return
     */
    List<String> getParentPathName(List<String> categoryPath);
}
