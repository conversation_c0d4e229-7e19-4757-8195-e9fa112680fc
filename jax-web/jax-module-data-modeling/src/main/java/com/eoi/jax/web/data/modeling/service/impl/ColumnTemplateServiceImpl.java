package com.eoi.jax.web.data.modeling.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.ColumnTemplatePlatformEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.BaseAuthExcelService;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.excel.SheetResp;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.data.modeling.model.modelrepository.*;
import com.eoi.jax.web.data.modeling.service.ColumnTemplateDetailService;
import com.eoi.jax.web.data.modeling.service.ColumnTemplateService;
import com.eoi.jax.web.repository.entity.TbColumnTemplate;
import com.eoi.jax.web.repository.service.TbColumnTemplateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: yaru.ma
 * @Date: 2023/4/10
 */
@Service
public class ColumnTemplateServiceImpl extends BaseAuthExcelService<
        TbColumnTemplateService,
        TbColumnTemplate,
        ColumnTemplateResp,
        ColumnTemplateCreateReq,
        ColumnTemplateUpdateReq,
        ColumnTemplateQuery,
        ColumnTemplateExcelModel> implements ColumnTemplateService {

    @Autowired
    private JaxRepository jaxRepository;

    @Autowired
    private ColumnTemplateDetailService detailService;

    public ColumnTemplateServiceImpl(@Autowired TbColumnTemplateService dao) {
        super(dao);
    }

    @Override
    public void whenCreate(ColumnTemplateCreateReq req, TbColumnTemplate entity) {
        super.whenCreate(req, entity);
        entity.setPlatform(CollUtil.emptyIfNull(req.getPlatform()).stream().collect(Collectors.joining(",")));
        entity.setIsBuiltIn(0);
        validate(entity);
        detailService.savaTemplateDetail(req.getColumns(), entity.getId());
    }

    @Override
    public void whenUpdate(ColumnTemplateUpdateReq req, TbColumnTemplate entity) {
        if (entity.getIsBuiltIn() == 1) {
            throw new BizException(ResponseCode.MODIFY_BUILTIN);
        }
        entity.setPlatform(CollUtil.emptyIfNull(req.getPlatform()).stream().collect(Collectors.joining(",")));
        validate(entity);
        super.whenUpdate(req, entity);
        detailService.savaTemplateDetail(req.getColumns(), entity.getId());
    }

    @Override
    public ColumnTemplateResp marshallingRespFrom(TbColumnTemplate entity) {
        ColumnTemplateResp columnTemplateResp = super.marshallingRespFrom(entity);
        List<String> platforms = Collections.EMPTY_LIST;
        if (StrUtil.isNotBlank(entity.getPlatform())) {
            platforms = Arrays.asList(entity.getPlatform().split(","));
        }
        columnTemplateResp.setPlatform(platforms);
        columnTemplateResp.setColumns(detailService.getColumnsInTemplateIds(Arrays.asList(entity.getId())).get(entity.getId()));
        return columnTemplateResp;
    }

    @Override
    public List<ColumnTemplateResp> marshallingRespListFrom(List<TbColumnTemplate> entityList) {
        List<Long> templateIds = entityList.stream().map(TbColumnTemplate::getId).collect(Collectors.toList());
        Map<Long, List<TemplateDetailResp>> modelCols = detailService.getColumnsInTemplateIds(templateIds);
        return entityList.stream().map(entity -> {
            ColumnTemplateResp resp = new ColumnTemplateResp().fromEntity(entity);
            resp.setColumns(modelCols.get(resp.getId()));
            resp.setPlatform(Arrays.asList(StrUtil.nullToEmpty(entity.getPlatform()).split(",")));
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ColumnTemplateResp> all(String platform, String name) {
        ColumnTemplateQuery query = new ColumnTemplateQuery();
        query.getFilter().setName(name);
        query.getFilter().setPlatform(platform);
        return all(query);
    }

    @Override
    public void whenDelete(TbColumnTemplate entity) {
        if (entity.getIsBuiltIn() == 1) {
            throw new BizException(ResponseCode.DELETE_BUILTIN);
        }
        super.whenDelete(entity);
        detailService.savaTemplateDetail(null, entity.getId());
    }

    @Override
    public List<ColumnTemplateResp> deleteBulk(List<Long> ids) {
        List<ColumnTemplateResp> result = marshallingRespListFrom(jaxRepository.colTemplate().listByIds(ids));
        if (CollUtil.isNotEmpty(ids)) {
            ids.forEach(this::delete);
        }
        return result;

    }

    @Override
    public String sheetName() {
        return "字段模板";
    }

    @Override
    public void verifyData(List<ColumnTemplateExcelModel> importRows) {
        importRows.stream().filter(x -> StrUtil.isBlank(x.getName()))
                .forEach(x -> x.setFailedReason("导入失败, 模板名称不能为空"));
        // 3.校验唯一(全部)
        List<String> codeAll = this.jaxRepository.colTemplate()
                .list().stream().map(TbColumnTemplate::getName).collect(Collectors.toList());
        // 3.校验唯一（权限内）
        Map<String, Long> codes = this.jaxRepository.colTemplate()
                .selectListWithProjectAuth(null)
                .stream().distinct().collect(Collectors.toMap(TbColumnTemplate::getName, TbColumnTemplate::getId, (x, y) -> x));
        codeAll.removeAll(codes.keySet());
        importRows.stream().filter(x -> codeAll.contains(x.getName()))
                .forEach(x -> x.setFailedReason("无权限修改"));
        importRows.stream().filter(x -> codes.containsKey(x.getName()))
                .forEach(x -> {
                    x.setId(codes.get(x.getName()));
                    x.setCoverField("name");
                });
    }

    @Override
    public void saveRows(Boolean skip, List<ColumnTemplateExcelModel> importRows) {
        saveRows(skip, importRows,
                (x -> {
                    ColumnTemplateCreateReq createReq = x.copyTo(new ColumnTemplateCreateReq());
                    createReq.setColumns(x.getImportDetails().stream()
                            .map(y -> y.copyTo(new TemplateDetailCreateReq()))
                            .collect(Collectors.toList()));
                    createReq.setPlatform(Arrays.asList(StrUtil.nullToEmpty(x.getPlatform()).split(",")));
                    return createReq;
                }), (x -> {
                    ColumnTemplateUpdateReq updateReq = x.copyTo(new ColumnTemplateUpdateReq());
                    updateReq.setColumns(x.getImportDetails().stream()
                            .map(y -> y.copyTo(new TemplateDetailCreateReq()))
                            .collect(Collectors.toList()));
                    updateReq.setPlatform(Arrays.asList(StrUtil.nullToEmpty(x.getPlatform()).split(",")));
                    return updateReq;
                }));
        importRows.stream().filter(x -> !x.isSuccess())
                .flatMap(x -> CollUtil.emptyIfNull(x.getImportDetails()).stream())
                .forEach(x -> x.setFailedReason("所属字段模板导入失败"));
    }

    @Override
    public List<ColumnTemplateExcelModel> exportData(ImportExcelReq req) {
        List<TbColumnTemplate> tbColumnTemplates = getExportColumnTemplates(req);
        return tbColumnTemplates.stream().map(x -> {
            ColumnTemplateExcelModel templateExcelModel = new ColumnTemplateExcelModel();
            ModelBeanUtil.copyBean(x, templateExcelModel);
            return templateExcelModel;
        }).collect(Collectors.toList());
    }

    @Override
    public List<TbColumnTemplate> getExportColumnTemplates(ImportExcelReq req) {
        QueryWrapper<TbColumnTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TbColumnTemplate::getIsBuiltIn, 0)
                .in(CollUtil.isNotEmpty(req.getIds()), TbColumnTemplate::getId, req.getIds());
        if (StringUtils.isNotEmpty(req.getCondition())) {
            ColumnTemplateQuery exportReq = JsonUtil.decode(req.getCondition(), ColumnTemplateQuery.class);
            exportReq.getFilter().where(queryWrapper);
            exportReq.getSort().order(queryWrapper);
        }
        List<TbColumnTemplate> tbColumnTemplates = jaxRepository.colTemplate().selectListWithProjectAuth(queryWrapper);
        return tbColumnTemplates;
    }

    @Override
    public void dealWithSheetRelation(List<SheetResp> sheets) {
        List<ColumnTemplateExcelModel> templateList = (List) sheets.stream()
                .filter(x -> x.getSheetName().equals(sheetName())).findAny().get().getRows();
        List<ColumnTemplateDetailExcelModel> columnList = (List) sheets.stream()
                .filter(x -> !x.getSheetName().equals(sheetName())).findAny().get().getRows();
        List<String> successTemplateNames = templateList.stream()
                .filter(AbstractExcelRowResp::isSuccess)
                .map(x -> x.getName()).collect(Collectors.toList());
        columnList.stream().filter(AbstractExcelRowResp::isSuccess)
                .filter(x -> !successTemplateNames.contains(x.getTemplateName()))
                .forEach(x -> x.setFailedReason("所属字段模板不在导入范围或导入失败"));
        Map<String, List<ColumnTemplateDetailExcelModel>> enumValMap = columnList.stream()
                .filter(AbstractExcelRowResp::isSuccess)
                .collect(Collectors.groupingBy(ColumnTemplateDetailExcelModel::getTemplateName));
        // 字段设置和表一起更新
        templateList.stream().filter(AbstractExcelRowResp::isSuccess)
                .forEach(x -> x.setImportDetails(CollUtil.emptyIfNull(enumValMap.get(x.getName()))));
    }

    private void validate(TbColumnTemplate entity) {
        if (StrUtil.isNotBlank(entity.getPlatform())) {
            List<ColumnTemplatePlatformEnum> platformEnums = Arrays.stream(entity.getPlatform().split(","))
                    .map(ColumnTemplatePlatformEnum::valueOf)
                    .distinct().filter(ObjectUtil::isNotNull).collect(Collectors.toList());
            entity.setPlatform(platformEnums.stream().map(ColumnTemplatePlatformEnum::name)
                    .collect(Collectors.joining(",")));
        }
        // 重名校验
        long count = jaxRepository.colTemplate().count(new LambdaQueryWrapper<TbColumnTemplate>()
                .eq(TbColumnTemplate::getName, entity.getName())
                .ne(entity.getId() != null, TbColumnTemplate::getId, entity.getId()));
        if (count > 0) {
            throw new BizException(ResponseCode.FOUND_MULTIPLE_NAME);
        }
    }
}
