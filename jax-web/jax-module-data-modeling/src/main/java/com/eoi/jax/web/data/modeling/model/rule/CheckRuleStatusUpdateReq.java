package com.eoi.jax.web.data.modeling.model.rule;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbCheckRule;
import io.swagger.v3.oas.annotations.media.Schema;

public class CheckRuleStatusUpdateReq implements IUpdateModel<TbCheckRule> {

    @Schema(hidden = true)
    private Long id;

    @Schema(description = "是否开启")
    private Boolean isEnabled;

    @Schema(description = "是否是默认规则")
    private Boolean isDefaultRule;

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Boolean enabled) {
        this.isEnabled = enabled;
    }

    public Boolean getIsDefaultRule() {
        return isDefaultRule;
    }

    public void setIsDefaultRule(Boolean defaultRule) {
        this.isDefaultRule = defaultRule;
    }
}
