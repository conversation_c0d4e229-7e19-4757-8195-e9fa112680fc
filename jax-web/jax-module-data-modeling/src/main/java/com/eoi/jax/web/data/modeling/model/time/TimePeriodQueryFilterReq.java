package com.eoi.jax.web.data.modeling.model.time;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbTimePeriod;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: <PERSON>
 * @Date: 2022/11/21
 **/
public class TimePeriodQueryFilterReq implements IFilterReq<TbTimePeriod> {

    @Schema(description = "搜索关键字，目前针对英文缩写、中文名、英文名字段生效，忽略大小写")
    private String searchKeyword;

    @Schema(description = "周期粒度")
    private String unit;


    @Override
    public QueryWrapper<TbTimePeriod> where(QueryWrapper<TbTimePeriod> wrapper) {
        wrapper.lambda().eq(StringUtils.isNoneEmpty(unit), TbTimePeriod::getUnit, unit);
        if (StringUtils.isNoneEmpty(searchKeyword)) {
            wrapper.lambda().and(r -> r.like(TbTimePeriod::getName, searchKeyword)
                    .or().like(TbTimePeriod::getCode, searchKeyword)
                    .or().like(TbTimePeriod::getNameEn, searchKeyword));
        }
        return wrapper;
    }

    public String getSearchKeyword() {
        return searchKeyword;
    }

    public void setSearchKeyword(String searchKeyword) {
        this.searchKeyword = searchKeyword;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }



}
