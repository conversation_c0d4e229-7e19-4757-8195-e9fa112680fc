package com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy;

import com.eoi.jax.web.data.modeling.factory.modeldeploy.ITableDeploySettingModel;
import com.eoi.jax.web.data.modeling.model.columndeploy.ColumnDeployResp;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR> zsc
 * @create 2022/12/30 17:38
 */
public class GenerateDefaultScriptReq {
    @Schema(description = "数据源id")
    private Long dsId;

    @Schema(description = "发布类型：TABLE，VIEW")
    private String deployType;

    @Schema(description = "发布动作类型：CREATE|REBUILD|INCREMENT|ASSOCIATION")
    private String actionType;

    @Schema(description = "删除模型类型: LOGIC|PHYSICAL")
    private String deleteType;

    @Schema(description = "发布名称")
    private String name;

    @Schema(description = "明细表id")
    private Long tbId;

    @Schema(description = "数据库独有配置")
    private ITableDeploySettingModel settingModel;

    @Schema(description = "表名")
    private String tbName;

    @Schema(description = "表中文名")
    private String tbAlias;

    @Schema(description = "生命周期/天")
    private Long lifecycle;

    /**
     * 存储模式
     */
    private String storageMode;

    /**
     * 指标值表名
     */
    private String idxTbName;

    /**
     * 维度表名
     */
    private String dimTbName;

    /**
     * 维度表id
     */
    private Long dimTbId;

    private String tbType;

    private Integer relateDim;

    @Schema(description = "字段列表")
    private List<ColumnDeployResp> columnDeployList;

    public Long getDsId() {
        return dsId;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

    public String getDeployType() {
        return deployType;
    }

    public void setDeployType(String deployType) {
        this.deployType = deployType;
    }

    public String getActionType() {
        return actionType;
    }

    public String getTbType() {
        return tbType;
    }

    public void setTbType(String tbType) {
        this.tbType = tbType;
    }

    public String getDeleteType() {
        return deleteType;
    }

    public void setDeleteType(String deleteType) {
        this.deleteType = deleteType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTbId() {
        return tbId;
    }

    public String getStorageMode() {
        return storageMode;
    }

    public Integer getRelateDim() {
        return relateDim;
    }

    public void setRelateDim(Integer relateDim) {
        this.relateDim = relateDim;
    }

    public Long getDimTbId() {
        return dimTbId;
    }

    public void setDimTbId(Long dimTbId) {
        this.dimTbId = dimTbId;
    }

    public void setStorageMode(String storageMode) {
        this.storageMode = storageMode;
    }

    public String getIdxTbName() {
        return idxTbName;
    }

    public void setIdxTbName(String idxTbName) {
        this.idxTbName = idxTbName;
    }

    public String getDimTbName() {
        return dimTbName;
    }

    public void setDimTbName(String dimTbName) {
        this.dimTbName = dimTbName;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public ITableDeploySettingModel getSettingModel() {
        return settingModel;
    }

    public void setSettingModel(ITableDeploySettingModel settingModel) {
        this.settingModel = settingModel;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getTbAlias() {
        return tbAlias;
    }

    public void setTbAlias(String tbAlias) {
        this.tbAlias = tbAlias;
    }

    public Long getLifecycle() {
        return lifecycle;
    }

    public void setLifecycle(Long lifecycle) {
        this.lifecycle = lifecycle;
    }

    public List<ColumnDeployResp> getColumnDeployList() {
        return columnDeployList;
    }

    public void setColumnDeployList(List<ColumnDeployResp> columnDeployList) {
        this.columnDeployList = columnDeployList;
    }
}
