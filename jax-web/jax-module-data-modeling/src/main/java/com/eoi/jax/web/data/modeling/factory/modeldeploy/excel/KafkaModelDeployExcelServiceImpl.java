package com.eoi.jax.web.data.modeling.factory.modeldeploy.excel;

import com.eoi.jax.web.core.common.enumrate.TableDeployPlatformEnum;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.excel.KafkaModelDeployExcel;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.kafka.KafkaModelSetting;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: yaru.ma
 * @Date: 2024/5/28
 */
@Service
public class KafkaModelDeployExcelServiceImpl extends AbstractModelDeployExcelService<KafkaModelDeployExcel> {

    private static Logger log = LoggerFactory.getLogger(KafkaModelDeployExcelServiceImpl.class);

    @Override
    public String sheetName() {
        return "kafka模型发布";
    }

    @Override
    public List<KafkaModelDeployExcel> importSqlOrSettings(List<KafkaModelDeployExcel> excelModels) {
        successRows(excelModels).forEach(row -> {
            KafkaModelSetting kafkaModelSetting = new KafkaModelSetting();
            kafkaModelSetting.setTopicName(row.getTbName());
            kafkaModelSetting.setPartition(row.getPartition());
            kafkaModelSetting.setRetention(row.getRetention());
            kafkaModelSetting.setReplication(row.getReplication());
            kafkaModelSetting.setMessageType(row.getMessageType());
            kafkaModelSetting.setMaxMessageBytes(row.getMaxMessageBytes());
            kafkaModelSetting.setRetentionUnit(row.getRetentionUnit());
            kafkaModelSetting.setParamMap(JsonUtil.decode2Map(row.getAdvancedParamStr()));
            row.setParamMap(kafkaModelSetting);
        });
        return excelModels;
    }

    @Override
    TableDeployPlatformEnum platformEnum() {
        return TableDeployPlatformEnum.KAFKA;
    }

    @Override
    public void exportSqlOrSettings(List<KafkaModelDeployExcel> importRows) {
        for (KafkaModelDeployExcel importRow : importRows) {
            KafkaModelSetting setting = (KafkaModelSetting) importRow.getParamMap();
            importRow.setAdvancedParamStr(JsonUtil.encode(setting.getParamMap()));
            importRow.setPartition(setting.getPartition());
            importRow.setRetention(setting.getRetention());
            importRow.setReplication(setting.getReplication());
            importRow.setMessageType(setting.getMessageType());
            importRow.setMaxMessageBytes(setting.getMaxMessageBytes());
            importRow.setRetentionUnit(setting.getRetentionUnit());
        }
    }
}
