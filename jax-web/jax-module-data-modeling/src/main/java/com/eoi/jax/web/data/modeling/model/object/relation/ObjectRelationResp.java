package com.eoi.jax.web.data.modeling.model.object.relation;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.repository.entity.TbObjectRelation;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * <p>
 * 对象关系
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@OpPrimaryKey(name = "id")
@OpPrimaryName(name = "name")
public class ObjectRelationResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbObjectRelation> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long id;

    /**
     * 源对象模型id
     */
    @Schema(description = "源对象模型id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long sourceObjId;

    /**
     * 目标对象模型id
     */
    @Schema(description = "目标对象模型id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long targetObjId;

    /**
     * 对象模型关系类型
     */
    @Schema(description = "对象模型关系类型")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long relationTypeId;
    /**
     * 源对象模型分类
     */
    @Schema(description = "源对象模型分类")
    private String sourceObjType;
    /**
     * 目标对象模型分类
     */
    @Schema(description = "目标对象模型分类")
    private String targetObjType;
    /**
     * 逻辑删除标志位
     */
    @Schema(description = "逻辑删除标志位")
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long createUser;

    /**
     * 更新人id
     */
    @Schema(description = "更新人id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSourceObjId() {
        return sourceObjId;
    }

    public void setSourceObjId(Long sourceObjId) {
        this.sourceObjId = sourceObjId;
    }

    public Long getTargetObjId() {
        return targetObjId;
    }

    public void setTargetObjId(Long targetObjId) {
        this.targetObjId = targetObjId;
    }

    public Long getRelationTypeId() {
        return relationTypeId;
    }

    public void setRelationTypeId(Long relationTypeId) {
        this.relationTypeId = relationTypeId;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getSourceObjType() {
        return sourceObjType;
    }

    public void setSourceObjType(String sourceObjType) {
        this.sourceObjType = sourceObjType;
    }

    public String getTargetObjType() {
        return targetObjType;
    }

    public void setTargetObjType(String targetObjType) {
        this.targetObjType = targetObjType;
    }

    @Override
    public String toString() {
        return "ObjectRelationResp{" +
                "id=" + id +
                ", sourceObjId=" + sourceObjId +
                ", targetObjId=" + targetObjId +
                ", relationTypeId=" + relationTypeId +
                ", sourceObjType=" + sourceObjType +
                ", targetObjType=" + targetObjType +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                '}';
    }
}
