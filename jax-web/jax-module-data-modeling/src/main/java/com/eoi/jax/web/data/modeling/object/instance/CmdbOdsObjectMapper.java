package com.eoi.jax.web.data.modeling.object.instance;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/11
 */
public interface CmdbOdsObjectMapper {


    /**
     * 查询最近的3天日期
     * @param cmdbObjectInfo
     * @param source
     * @return
     */
    List<String> selectLatelyDate(@Param("cmdb") CmdbObjectInfo cmdbObjectInfo,
                                  @Param("source") String source);

    /**
     * 统计对象实例数量
     * @param cmdbObjectInfo
     * @param source
     * @param startDate
     * @param endDate
     * @return
     */
    List<CmdbInstanceCount> countByTag(@Param("cmdb") CmdbObjectInfo cmdbObjectInfo,
                                       @Param("source") String source,
                                       @Param("startDate") String startDate,
                                       @Param("endDate") String endDate);


}
