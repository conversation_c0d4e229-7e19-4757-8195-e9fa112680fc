package com.eoi.jax.web.data.modeling.model.tabledeployhistory;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbTableDeployHistory;

/**
 * @Author: zsc
 * @Date: 2022/01/03
 */
public class TableDeployHistoryQueryReq extends BaseQueryReq<TbTableDeployHistory> {

    private TableDeployHistoryFilterReq filter = new TableDeployHistoryFilterReq();
    private TableDeployHistorySortReq sort = new TableDeployHistorySortReq();

    @Override
    public TableDeployHistoryFilterReq getFilter() {
        return filter;
    }

    public void setFilter(TableDeployHistoryFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public TableDeployHistorySortReq getSort() {
        return sort;
    }

    public void setSort(TableDeployHistorySortReq sort) {
        this.sort = sort;
    }
}
