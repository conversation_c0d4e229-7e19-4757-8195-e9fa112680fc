package com.eoi.jax.web.data.modeling.model.metricnindsource;

import cn.hutool.core.collection.CollUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbMetricBindSource;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class MetricBindSourceUpdateReq implements IUpdateModel<TbMetricBindSource> {

    @Schema(description = "id")
    private Long id;

    /**
     * 指标项id
     */
    @NotNull(message = "指标项id不能为空")
    @Schema(description = "指标项id")
    private Long metricId;

    /**
     * 指标来源id
     */
    @NotNull(message = "指标项id不能为空")
    @Schema(description = "指标来源id")
    private Long metricSourceId;

    /**
     * 指标匹配关键字
     */
    @NotBlank(message = "指标匹配关键字不能为空")
    @Schema(description = "指标匹配关键字")
    private String bindKeyword;

    /**
     * 维度提取的规则JSON
     */
    @Schema(description = "维度提取的规则")
    private List<DimensionRuleExtractionReq> dimensionRule;


    @Override
    public TbMetricBindSource toEntity(TbMetricBindSource tbMetricBindSource) {
        TbMetricBindSource entity = IUpdateModel.super.toEntity(tbMetricBindSource);
        entity.setDimensionRule(JsonUtil.encode(CollUtil.defaultIfEmpty(this.getDimensionRule(), CollUtil.newArrayList())));
        return entity;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMetricId() {
        return metricId;
    }

    public void setMetricId(Long metricId) {
        this.metricId = metricId;
    }

    public Long getMetricSourceId() {
        return metricSourceId;
    }

    public void setMetricSourceId(Long metricSourceId) {
        this.metricSourceId = metricSourceId;
    }

    public String getBindKeyword() {
        return bindKeyword;
    }

    public void setBindKeyword(String bindKeyword) {
        this.bindKeyword = bindKeyword;
    }

    public List<DimensionRuleExtractionReq> getDimensionRule() {
        return dimensionRule;
    }

    public void setDimensionRule(List<DimensionRuleExtractionReq> dimensionRule) {
        this.dimensionRule = dimensionRule;
    }
}
