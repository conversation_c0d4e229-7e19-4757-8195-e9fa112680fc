package com.eoi.jax.web.data.modeling.model.adjunct;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbAdjunct;

/**
 * @Author: <PERSON>
 * @Date: 2022/11/21
 **/
public class AdjunctQueryReq extends BaseQueryReq<TbAdjunct> {

    private AdjunctQueryFilterReq filter = new AdjunctQueryFilterReq();
    private AdjunctQuerySortReq sort = new AdjunctQuerySortReq();

    @Override
    public IFilterReq<TbAdjunct> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbAdjunct> getSort() {
        return sort;
    }


    public void setFilter(AdjunctQueryFilterReq filter) {
        this.filter = filter;
    }

    public void setSort(AdjunctQuerySortReq sort) {
        this.sort = sort;
    }

}
