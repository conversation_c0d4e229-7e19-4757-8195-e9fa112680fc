package com.eoi.jax.web.data.modeling.model.object.relation;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbObjectRelation;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 对象关系
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */

public class ObjectRelationCreateReq implements ICreateModel<TbObjectRelation> {
    /**
     * 源对象模型id
     */
    @Schema(description = "源对象模型id")
    private Long sourceObjId;


    /**
     * 目标对象模型id
     */
    @Schema(description = "目标对象模型id")
    private Long targetObjId;
    /**
     * 源对象模型分类
     */
    @Schema(description = "源对象模型分类")
    private String sourceObjType;
    /**
     * 目标对象模型分类
     */
    @Schema(description = "目标对象模型分类")
    private String targetObjType;


    /**
     * 对象模型关系类型
     */
    @Schema(description = "对象模型关系类型")
    private Long relationTypeId;

    public Long getSourceObjId() {
        return sourceObjId;
    }

    public void setSourceObjId(Long sourceObjId) {
        this.sourceObjId = sourceObjId;
    }

    public Long getTargetObjId() {
        return targetObjId;
    }

    public void setTargetObjId(Long targetObjId) {
        this.targetObjId = targetObjId;
    }

    public Long getRelationTypeId() {
        return relationTypeId;
    }

    public void setRelationTypeId(Long relationTypeId) {
        this.relationTypeId = relationTypeId;
    }

    public String getSourceObjType() {
        return sourceObjType;
    }

    public void setSourceObjType(String sourceObjType) {
        this.sourceObjType = sourceObjType;
    }

    public String getTargetObjType() {
        return targetObjType;
    }

    public void setTargetObjType(String targetObjType) {
        this.targetObjType = targetObjType;
    }

    @Override
    public String toString() {
        return "ObjectRelationCreateReq{" +
                "sourceObjId=" + sourceObjId +
                ", targetObjId=" + targetObjId +
                ", sourceObjType=" + sourceObjType +
                ", targetObjType=" + targetObjType +
                ", relationTypeId=" + relationTypeId +
                '}';
    }

    @Override
    public TbObjectRelation toEntity() {
        TbObjectRelation entity = ICreateModel.super.toEntity(new TbObjectRelation());
        return entity;
    }
}
