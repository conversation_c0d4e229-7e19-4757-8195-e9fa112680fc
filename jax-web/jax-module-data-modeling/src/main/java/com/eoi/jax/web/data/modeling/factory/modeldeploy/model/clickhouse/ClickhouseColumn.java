package com.eoi.jax.web.data.modeling.factory.modeldeploy.model.clickhouse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.enumrate.CkColTypeEnum;
import com.eoi.jax.web.core.common.enumrate.ColumnTypeEnum;
import com.eoi.jax.web.core.model.IModel;
import com.eoi.jax.web.data.modeling.model.columndeploy.ColumnDeployResp;
import com.eoi.jax.web.repository.entity.TbColumnDeploy;
import io.swagger.v3.oas.annotations.media.Schema;

import static com.eoi.jax.web.core.common.enumrate.CkColTypeEnum.*;

/**
 * @Author: yaru.ma
 * @Date: 2023/1/10
 */
public class ClickhouseColumn implements IModel {

    @Schema(description = "字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN")
    private ColumnTypeEnum type;

    @Schema(description = "ck的字段类型")
    private CkColTypeEnum ckType;

    @Schema(description = "ck的字段类型")
    private String colTypeStr;

    @Schema(description = "字段名")
    private String colName;

    @Schema(description = "显示名")
    private String colDisplay;

    @Schema(description = "字段描述")
    private String description;

    @Schema(description = "是否主键")
    private Boolean isPrimaryKey;

    @Schema(description = "是否不为空")
    private Boolean isNotNull;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "长度")
    private Integer colLength;

    @Schema(description = "精度")
    private Integer numPrecision;

    public ClickhouseColumn(ColumnDeployResp o) {
        copyFrom(o);
        type = ColumnTypeEnum.fromString(o.getColType());
        ckType = from(type, colLength);
        generateCkType("--col1 Int8,col2 String,...");
    }

    public ClickhouseColumn(TbColumnDeploy o) {
        copyFrom(o);
        type = ColumnTypeEnum.fromString(o.getColType());
        ckType = from(type, colLength);
        generateCkType("...");
    }

    public ClickhouseColumn() {
    }

    public String colSql() {
        String sql = getColName() + " " + ((getIsNotNull() || CK_NESTED == ckType) ? colTypeStr : "Nullable(" + colTypeStr + ")");
        if (StrUtil.isNotBlank(defaultValue)) {
            if (ckType == CK_STRING) {
                defaultValue = "'" + defaultValue + "'";
            }
            sql += " DEFAULT " + defaultValue;
        }
        if (StrUtil.isNotBlank(colDisplay)) {
            sql += " COMMENT '" + colDisplay + "'";
        }
        return sql;
    }

    private void generateCkType(String nestedSubType) {
        switch (ckType) {
            case CK_NESTED:
                colTypeStr = String.format(ckType.code(), nestedSubType);
                break;
            case CK_DATETIME64:
                colTypeStr = String.format(CK_DATETIME64.code(), ObjectUtil.defaultIfNull(numPrecision, 3));
                break;
            case CK_DECIMAL32:
            case CK_DECIMAL64:
            case CK_DECIMAL128:
            case CK_DECIMAL256:
                numPrecision = ObjectUtil.defaultIfNull(numPrecision, 4);
                colTypeStr = String.format(ckType.code(), numPrecision);
                break;
            default:
                colTypeStr = ckType.code();
                break;

        }
    }

    public ColumnTypeEnum getType() {
        return type;
    }

    public void setType(ColumnTypeEnum type) {
        this.type = type;
    }

    public CkColTypeEnum getCkType() {
        return ckType;
    }

    public void setCkType(CkColTypeEnum ckType) {
        this.ckType = ckType;
    }

    public String getColTypeStr() {
        return colTypeStr;
    }

    public void setColTypeStr(String colTypeStr) {
        this.colTypeStr = colTypeStr;
    }

    public String getColName() {
        if (!colName.startsWith("`")) {
            colName = String.format("`%s`", colName);
        }
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsPrimaryKey() {
        return ObjectUtil.isNotNull(isPrimaryKey) ? isPrimaryKey : false;
    }

    public void setIsPrimaryKey(Boolean primaryKey) {
        this.isPrimaryKey = primaryKey;
    }

    /**
     * 默认为不勾选，即false
     *
     * @return
     */
    public Boolean getIsNotNull() {
        return ObjectUtil.isNotNull(isNotNull) ? isNotNull : false;
    }

    public void setIsNotNull(Boolean notNull) {
        this.isNotNull = notNull;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Integer getColLength() {
        return colLength;
    }

    public void setColLength(Integer colLength) {
        this.colLength = colLength;
    }

    public Integer getNumPrecision() {
        return numPrecision;
    }

    public void setNumPrecision(Integer numPrecision) {
        this.numPrecision = numPrecision;
    }
}
