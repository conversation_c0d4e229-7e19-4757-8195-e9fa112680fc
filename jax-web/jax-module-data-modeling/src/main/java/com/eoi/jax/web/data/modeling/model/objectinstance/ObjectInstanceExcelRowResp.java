package com.eoi.jax.web.data.modeling.model.objectinstance;

import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Map;
import java.util.Optional;

/**
 * @Author: yaru.ma
 * @Date: 2024/5/14
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ObjectInstanceExcelRowResp extends AbstractExcelRowResp {

    public ObjectInstanceExcelRowResp(Map<String, Object> properties) {
        this.properties = properties;
    }

    public ObjectInstanceExcelRowResp() {
    }

    private String tag;

    private String vid;

    private Map<String, Object> properties;

    private String svid;

    private String dvid;

    private String relationType;

    public String getSvid() {
        return svid;
    }

    public void setSvid(String svid) {
        this.svid = svid;
    }

    public String getDvid() {
        return dvid;
    }

    public void setDvid(String dvid) {
        this.dvid = dvid;
    }

    public String getRelationType() {
        return relationType;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType;
    }

    @JsonAnyGetter
    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map properties) {
        this.properties = properties;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getVid() {
        if (vid != null || properties == null) {
            return vid;
        }
        return Optional.ofNullable(properties.get("objectId")).map(String::valueOf).orElse(null);
    }

    public void setVid(String vid) {
        this.vid = vid;
    }

    public ObjectInstanceExcelRowResp copyFrom(Object t) {
        ModelBeanUtil.copyBean(t, this);
        return this;
    }
}
