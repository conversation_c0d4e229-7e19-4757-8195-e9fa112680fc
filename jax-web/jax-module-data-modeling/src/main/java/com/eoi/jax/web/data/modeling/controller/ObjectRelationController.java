package com.eoi.jax.web.data.modeling.controller;

import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpParameters;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.modeling.model.object.relation.ObjectRelationBatchCreateReq;
import com.eoi.jax.web.data.modeling.model.object.relation.ObjectRelationCreateReq;
import com.eoi.jax.web.data.modeling.model.object.relation.ObjectRelationResp;
import com.eoi.jax.web.data.modeling.model.object.relation.ObjectRelationUpdateReq;
import com.eoi.jax.web.data.modeling.model.object.relation.gplot.ObjectRelationGplotReq;
import com.eoi.jax.web.data.modeling.service.ObjectRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * @Author: tangy
 * @Date: 2023/9/4
 * @Desc:
 **/
@RestController
public class ObjectRelationController implements V2Controller {
    @Resource
    private ObjectRelationService objectRelationService;


    @Operation(summary = "关系模型全部查询")
    @GetMapping("data-modeling/object-relation/list")
    public Response list() {
        return Response.success(objectRelationService.all());
    }

    @Operation(summary = "根据关系模型id查询详情")
    @GetMapping("data-modeling/object-relation/{id}")
    public Response<ObjectRelationResp> get(@Parameter(description = "关系模型主键id", required = true)
                                            @PathVariable("id") Long id) {
        return Response.success(objectRelationService.get(id));
    }


    @AuditLog(category = "维度建模", opAction = OpActionEnum.CREATE,
            module = "关系模型", function = "关系类型", code = "create")
    @Operation(summary = "关系模型创建")
    @PostMapping("data-modeling/object-relation")
    public Response<ObjectRelationResp> create(@OpPrimaryName(name = "name") @OpParameters
                                               @Valid @RequestBody ObjectRelationCreateReq req) {
        return Response.success(objectRelationService.create(req));
    }


    @Operation(summary = "关系模型拓扑查询")
    @PostMapping("data-modeling/object-relation/gplot")
    public Response getObjectCategoryGplot(@Valid @RequestBody ObjectRelationGplotReq req) {
        return Response.success(objectRelationService.getObjectRelationGplot(req));
    }


    @AuditLog(category = "维度建模", opAction = OpActionEnum.UPDATE,
            module = "关系模型", function = "关系类型", code = "update")
    @Operation(summary = "关系模型更新")
    @PutMapping("data-modeling/object-relation/{id}")
    public Response<ObjectRelationResp> update(
            @OpPrimaryKey @Parameter(description = "关系模型主键id", required = true) @PathVariable("id") Long id,
            @OpParameters @Valid @RequestBody ObjectRelationUpdateReq req) {
        req.setId(id);
        return Response.success(objectRelationService.update(req));
    }


    @AuditLog(category = "维度建模", opAction = OpActionEnum.DELETE,
            module = "关系模型", function = "关系类型", code = "delete")
    @Operation(summary = "关系模型删除")
    @DeleteMapping("data-modeling/object-relation/{id}")
    public Response<ObjectRelationResp> delete(
            @OpPrimaryKey(name = "id")
            @Parameter(description = "关系模型主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(objectRelationService.delete(id));
    }


    @AuditLog(category = "维度建模", opAction = OpActionEnum.CREATE, module = "关系模型", function = "关系类型", code = "batchAdd")
    @Operation(summary = "关系模型批量创建")
    @PostMapping("data-modeling/object-relation/batch-add")
    public Response batchAdd(@OpParameters
                             @Valid @RequestBody ObjectRelationBatchCreateReq req) {
        objectRelationService.batchSaveOrUpdate(req);
        return Response.success("批量新增成功");
    }
}
