package com.eoi.jax.web.data.modeling.factory.modeldeploy;

import com.eoi.jax.web.data.modeling.factory.ModelDeployServiceFactory;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.impl.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 */
@Component
public class ModelDeployServiceManager {

    @Autowired
    private ClickhouseModelDeployServiceImpl clickhouseModelDeployService;

    @Autowired
    private ElasticsearchModelDeployServiceImpl elasticsearchModelDeployService;

    @Autowired
    private KafkaModelDeployServiceImpl kafkaModelDeployService;

    @Autowired
    private MysqlModelDeployServiceImpl mysqlModelDeployService;

    @Autowired
    private HiveModelDeployServiceImpl hiveModelDeployService;

    @Autowired
    private NebulaModelDeployServiceImpl nebulaModelDeployService;

    @PostConstruct
    public void initFactory() {
        ModelDeployServiceFactory.setMysqlModelDeployService(mysqlModelDeployService);
        ModelDeployServiceFactory.setElasticsearchModelDeployService(elasticsearchModelDeployService);
        ModelDeployServiceFactory.setKafkaModelDeployService(kafkaModelDeployService);
        ModelDeployServiceFactory.setClickhouseModelDeployService(clickhouseModelDeployService);
        ModelDeployServiceFactory.setHiveModelDeployService(hiveModelDeployService);
        ModelDeployServiceFactory.setNebulaModelDeployService(nebulaModelDeployService);
    }
}
