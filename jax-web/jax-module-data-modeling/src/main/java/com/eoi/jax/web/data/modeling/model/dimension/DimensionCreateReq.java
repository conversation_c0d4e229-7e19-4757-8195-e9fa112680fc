package com.eoi.jax.web.data.modeling.model.dimension;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbDimension;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public class DimensionCreateReq implements ICreateModel<TbDimension> {

    @Schema(description = "分层id")
    private Long layerId;

    @Schema(description = "业务分类id")
    private Long bizId;

    @Schema(description = "数据域id")
    private Long domId;

    @Schema(description = "数据集市id")
    private Long martId;

    @Schema(description = "主题域id")
    private Long subjId;

    @Schema(description = "英文缩写")
    private String code;

    @Schema(description = "中文名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    /**
     * 标签信息
     */
    @Schema(description = "标签信息")
    private List<String> tags;

    @Override
    public TbDimension toEntity() {
        TbDimension tbDimension = toEntity(new TbDimension());
        tbDimension.setDescription(StrUtil.nullToEmpty(tbDimension.getDescription()));
        return tbDimension;
    }

    public Long getLayerId() {
        return layerId;
    }

    public void setLayerId(Long layerId) {
        this.layerId = layerId;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Long getDomId() {
        return domId;
    }

    public void setDomId(Long domId) {
        this.domId = domId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getMartId() {
        return martId;
    }

    public void setMartId(Long martId) {
        this.martId = martId;
    }

    public Long getSubjId() {
        return subjId;
    }

    public void setSubjId(Long subjId) {
        this.subjId = subjId;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }
}
