package com.eoi.jax.web.data.modeling.model.dashboard;

/**
 * <AUTHOR>
 * @Date 2024/12/2
 */
public class GrafanaEnv {
    private String grafanaUrl;
    private String grafanaAuthMode;
    private String storageDashboardUrl;
    private String storageDashboardTokenUrl;

    public String getGrafanaUrl() {
        return grafanaUrl;
    }

    public void setGrafanaUrl(String grafanaUrl) {
        this.grafanaUrl = grafanaUrl;
    }

    public String getGrafanaAuthMode() {
        return grafanaAuthMode;
    }

    public void setGrafanaAuthMode(String grafanaAuthMode) {
        this.grafanaAuthMode = grafanaAuthMode;
    }

    public String getStorageDashboardUrl() {
        return storageDashboardUrl;
    }

    public void setStorageDashboardUrl(String storageDashboardUrl) {
        this.storageDashboardUrl = storageDashboardUrl;
    }

    public String getStorageDashboardTokenUrl() {
        return storageDashboardTokenUrl;
    }

    public void setStorageDashboardTokenUrl(String storageDashboardTokenUrl) {
        this.storageDashboardTokenUrl = storageDashboardTokenUrl;
    }
}
