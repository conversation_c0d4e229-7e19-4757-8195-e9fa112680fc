package com.eoi.jax.web.data.modeling.model.metricitem;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbMetricItem;
import com.eoi.jax.web.repository.search.query.MetricItemListParam;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class MetricItemQueryFilterReq extends MetricItemListParam implements IFilterReq<TbMetricItem> {

    @Override
    public QueryWrapper<TbMetricItem> where(QueryWrapper<TbMetricItem> wrapper) {
        // 分页查询实际使用com.eoi.jax.web.repository.service.queryItemList(MetricItemListParam)
        throw new RuntimeException("not support");
    }
}
