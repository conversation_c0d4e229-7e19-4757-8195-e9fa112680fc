package com.eoi.jax.web.data.modeling.model.excel;

import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 导入命名字典
 *
 * @Author: yaru.ma
 * @Date: 2022/11/23
 */
public class EnumValueModel extends AbstractExcelRowResp {

    private Long enumId;

    @Excel(name = "数据字典-英文名称*", no = 0)
    @Schema(description = "数据字典-英文名称")
    private String enumNameEn;

    @Excel(name = "代码名称*", no = 1)
    @Schema(description = "代码名称")
    private String enumValue;

    @Excel(name = "编码名称", no = 2)
    @Schema(description = "编码名称")
    private String name;

    @Excel(name = "代码含义", no = 4)
    @Schema(description = "代码含义")
    private String description;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEnumNameEn() {
        return enumNameEn;
    }

    public void setEnumNameEn(String enumNameEn) {
        this.enumNameEn = enumNameEn;
    }

    public String getEnumValue() {
        return enumValue;
    }

    public void setEnumValue(String enumValue) {
        this.enumValue = enumValue;
    }

    public Long getEnumId() {
        return enumId;
    }

    public void setEnumId(Long enumId) {
        this.enumId = enumId;
    }

}
