package com.eoi.jax.web.data.modeling.service;

import com.eoi.jax.web.core.excel.IBaseExcelService;
import com.eoi.jax.web.data.modeling.model.metricnindsource.*;
import com.eoi.jax.web.repository.entity.TbMetricBindSource;
import com.eoi.jax.web.repository.service.TbMetricBindSourceService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public interface MetricBindSourceService extends IBaseExcelService<
        TbMetricBindSourceService,
        TbMetricBindSource,
        MetricBindSourceResp,
        MetricBindSourceCreateReq,
        MetricBindSourceUpdateReq,
        MetricBindSourceQueryReq,
        MetricBindSourceModel> {


    /**
     * 所有数据
     *
     * @param metricId
     * @return
     */
    List<MetricBindSourceResp> all(Long metricId);

    /**
     * 维度规则提取测试
     *
     * @param req
     * @return
     */
    String extractionRuleDebug(DimensionRuleExtractionDebugReq req);

}
