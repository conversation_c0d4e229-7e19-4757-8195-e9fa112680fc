package com.eoi.jax.web.data.modeling.model.comm;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * DomainFilter
 *
 * @param <T>
 * <AUTHOR>
 * @date 2022/11/18 15:24
*/
public class CommEntityFilter<T> implements IFilterReq<T> {

    private String name;
    private String code;
    private String description;

    private Long createUser;
    private Long updateUser;

    private Date createTime;
    private Date updateTime;

    private List<ColumnFilter> filters;

    /**
     * 把ColumnFilter转成QueryWrapper
     *
     * @param wrapper
     * @return
     */
    @Override
    public QueryWrapper<T> where(QueryWrapper<T> wrapper) {
        wrapper.like(StringUtils.isNoneEmpty(name), "name", name);
        wrapper.like(StringUtils.isNoneEmpty(code), "code", code);
        wrapper.like(StringUtils.isNoneEmpty(description), "description", description);
        wrapper.eq(checkNullOrValidate(createUser), "create_user", createUser);
        wrapper.eq(checkNullOrValidate(updateUser), "update_user", updateUser);
        wrapper.ge(checkNullOrValidate(createTime), "create_time", createTime);
        wrapper.le(checkNullOrValidate(updateTime), "update_time", updateTime);

        if (null != filters && !filters.isEmpty()) {
            for (ColumnFilter filter: filters) {
                String key = filter.getColumn();
                Object filterValue = filter.getFilterValue();
                if (null != key && !key.isEmpty() && null != filterValue) {
                    SqlCompareOperator compareOp = filter.getCompareOp();
                    if (null == compareOp) {
                        compareOp = SqlCompareOperator.EQ;
                    }
                    switch (compareOp) {
                        case EQ:
                            wrapper.eq(key, filterValue);
                            break;
                        case GT:
                            wrapper.gt(key, filterValue);
                            break;
                        case GE:
                            wrapper.ge(key, filterValue);
                            break;
                        case LT:
                            wrapper.lt(key, filterValue);
                            break;
                        case LE:
                            wrapper.le(key, filterValue);
                            break;
                        case LIKE:
                            wrapper.like(key, filterValue);
                            break;
                        default:
                            wrapper.eq(key, filterValue);
                            break;
                    }
                }
            }
        }

        return wrapper;
    }

    public boolean checkNullOrValidate(Object field) {
        if (null == field) {
            return false;
        }
        return true;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<ColumnFilter> getFilters() {
        return filters;
    }

    public void setFilters(List<ColumnFilter> filters) {
        this.filters = filters;
    }
}
