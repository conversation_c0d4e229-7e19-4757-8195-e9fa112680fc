package com.eoi.jax.web.data.modeling.model.databusiness;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbDataDomainBusinessCategory;

public class DataDomainBusinessCategoryFilterReq implements IFilterReq<TbDataDomainBusinessCategory> {
    @Override
    public QueryWrapper<TbDataDomainBusinessCategory> where(QueryWrapper<TbDataDomainBusinessCategory> wrapper) {
        return wrapper;
    }
}
