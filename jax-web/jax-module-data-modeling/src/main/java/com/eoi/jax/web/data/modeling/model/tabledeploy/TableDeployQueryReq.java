package com.eoi.jax.web.data.modeling.model.tabledeploy;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class TableDeployQueryReq extends BaseQueryReq<TbTableDeploy> {

    @Schema(description = "查询条件")
    private TableDeployQueryFilterReq filter = new TableDeployQueryFilterReq();

    @Schema(description = "排序条件")
    private TableDeployQuerySortReq sort = new TableDeployQuerySortReq();

    @Override
    public IFilterReq<TbTableDeploy> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbTableDeploy> getSort() {
        return sort;
    }
}
