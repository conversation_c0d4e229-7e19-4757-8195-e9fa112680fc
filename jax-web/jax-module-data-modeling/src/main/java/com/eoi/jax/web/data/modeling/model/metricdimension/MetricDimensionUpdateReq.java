package com.eoi.jax.web.data.modeling.model.metricdimension;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbMetricDimension;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class MetricDimensionUpdateReq implements IUpdateModel<TbMetricDimension> {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "维度名称")
    @NotBlank(message = "维度名称不能为空")
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "字段名称")
    @NotBlank(message = "字段名称不能为空")
    private String code;

    @Schema(description = "是否是关键维度")
    @NotNull(message = "是否是关键维度不能为空")
    private Boolean isKeyDimension;

    @Override
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getIsKeyDimension() {
        return isKeyDimension;
    }

    public void setIsKeyDimension(Boolean keyDimension) {
        isKeyDimension = keyDimension;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
