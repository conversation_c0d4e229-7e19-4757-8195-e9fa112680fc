package com.eoi.jax.web.data.modeling.model.namedict;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbNameDict;


public class NameDictSortReq implements ISortReq<TbNameDict> {

    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbNameDict> order(QueryWrapper<TbNameDict> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbNameDict::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbNameDict::getUpdateTime);
        return wrapper;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
