package com.eoi.jax.web.data.modeling.model.domain;

import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbDataDomain;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
public class DataDomainUpdateReq implements IUpdateModel<TbDataDomain> {
    @Schema(hidden = true)
    private Long id;
    @OpPrimaryName
    @Schema(description = "业务名称")
    private String name;
    @Schema(description = "英文缩写")
    private String code;
    @Schema(description = "备注信息")
    private String description;
    @Schema(description = "英文名")
    private String nameEn;
    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

}
