package com.eoi.jax.web.data.modeling.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.ConfigChangeType;
import com.eoi.jax.web.core.common.enumrate.SystemConfigEnum;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.SystemConfigHolder;
import com.eoi.jax.web.core.config.config.ObjectInstanceCkWideTableConfig;
import com.eoi.jax.web.core.integration.model.ck.ClickhouseJdbcInfo;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectCount;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectDetailReq;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectDetailResp;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectRelation;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectRelationResp;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectReq;
import com.eoi.jax.web.data.modeling.model.cmdbobject.ObjectRelationInstanceCountReq;
import com.eoi.jax.web.data.modeling.model.object.category.ObjectCategoryTree;
import com.eoi.jax.web.data.modeling.model.object.category.ObjectCategoryTreeFilter;
import com.eoi.jax.web.data.modeling.model.object.column.ObjectColumnResp;
import com.eoi.jax.web.data.modeling.object.instance.CmdbInstanceCount;
import com.eoi.jax.web.data.modeling.object.instance.CmdbObjectAllMapper;
import com.eoi.jax.web.data.modeling.object.instance.CmdbObjectInfo;
import com.eoi.jax.web.data.modeling.object.instance.CmdbObjectRelationAllMapper;
import com.eoi.jax.web.data.modeling.object.instance.CmdbOdsObjectMapper;
import com.eoi.jax.web.data.modeling.object.instance.CmdbOdsObjectRelationMapper;
import com.eoi.jax.web.data.modeling.service.ColumnDeployService;
import com.eoi.jax.web.data.modeling.service.ObjectCategoryService;
import com.eoi.jax.web.data.modeling.service.ObjectColumnService;
import com.eoi.jax.web.data.modeling.service.ObjectInstanceWideTableService;
import com.eoi.jax.web.ingestion.service.ClickhouseService;
import com.eoi.jax.web.ingestion.service.DatasourceService;
import com.eoi.jax.web.repository.entity.TbObjectRelationType;
import com.eoi.jax.web.repository.entity.TbObjectTable;
import com.eoi.jax.web.repository.service.TbObjectRelationTypeService;
import com.eoi.jax.web.repository.service.TbObjectTableService;
import com.eoi.jax.web.repository.service.TbTableDeployService;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.ibatis.exceptions.PersistenceException;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.managed.ManagedTransactionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Service
public class ObjectInstanceWideTableServiceImpl implements ObjectInstanceWideTableService {

    private static final Logger LOG = LoggerFactory.getLogger(ObjectInstanceWideTableServiceImpl.class);

    private static final Set<String> EXCLUDE_COLUMNS = CollUtil.newHashSet("objectId", "objectStatus", "objectName", "objectType");

    private static final Pattern TABLE_NOT_FOUND_PATTERN = Pattern.compile("Table\\s+.*\\s+does\\s+not\\s+exist", Pattern.CASE_INSENSITIVE);

    @Resource
    private ObjectCategoryService objectCategoryService;

    @Resource
    private TbObjectTableService tbObjectTableService;

    @Resource
    private SystemConfigHolder systemConfigHolder;

    @Resource
    private DatasourceService datasourceService;

    @Resource
    private ClickhouseService clickhouseService;

    @Resource
    private ColumnDeployService columnDeployService;

    @Resource
    private ObjectColumnService objectColumnService;

    @Resource
    private TbTableDeployService tbTableDeployService;

    @Resource
    private TbObjectRelationTypeService tbObjectRelationTypeService;


    private Triple<SqlSessionFactory, CmdbObjectInfo, HikariDataSource> sqlSessionFactoryPair;

    @PostConstruct
    public void init() {
        systemConfigHolder.addChangeListener(SystemConfigEnum.OBJECT_INSTANCE_CK_WIDE_TABLE, event -> {
            LOG.info("收到CK宽表配置变化:{}", JsonUtil.encode(event));
            Map<String, Object> newConfig = event.getNewConfigMap();
            if (ConfigChangeType.DELETED.equals(event.getChangeType()) || MapUtil.isEmpty(newConfig)) {
                this.close();
            } else if (ConfigChangeType.ADDED.equals(event.getChangeType()) || ConfigChangeType.MODIFIED.equals(event.getChangeType())) {
                // 停止旧的redis连接
                this.close();
                // 新建
                buildSqlSessionFactory(newConfig);
            }
        });
    }


    private synchronized void buildSqlSessionFactory(Map<String, Object> configMap) {
        try {
            ObjectInstanceCkWideTableConfig wideTableConfig = BeanUtil.mapToBean(configMap, ObjectInstanceCkWideTableConfig.class, false);
            wideTableConfig.validate();
            LOG.info("开始构建CK宽表查询sqlSessionFactory:{}", wideTableConfig);
            ClickhouseJdbcInfo clickhouseJdbcInfo = clickhouseService.getClickhouseJdbcInfo(wideTableConfig.getDsId());

            HikariConfig config = new HikariConfig();
            config.setPoolName("ck-object-instance-pool");
            config.setDriverClassName("com.clickhouse.jdbc.ClickHouseDriver");
            config.setJdbcUrl(clickhouseJdbcInfo.getMultiEndpointJdbcUrl());
            config.setUsername(clickhouseJdbcInfo.getCkUsername());
            config.setPassword(clickhouseJdbcInfo.getCkPassword());
            config.setConnectionTestQuery("SELECT 1;");
            config.setMaximumPoolSize(16);
            config.setMinimumIdle(0);
            config.setIdleTimeout(30000);
            Properties properties = new Properties();
//        properties.setProperty("socket_timeout", "30000")
            properties.setProperty("autoCommit", "true");
            // properties.setProperty("compress_algorith, "gzip")
            properties.setProperty("compress", "0");
            properties.putAll(clickhouseJdbcInfo.getJdbcProp());
            config.setDataSourceProperties(properties);
            HikariDataSource hikariDataSource = new HikariDataSource(config);

            Environment environment = new Environment("ck-object-instance-",
                new ManagedTransactionFactory(), hikariDataSource);
            Configuration configuration = new Configuration(environment);
            configuration.addMapper(CmdbObjectAllMapper.class);
            configuration.addMapper(CmdbObjectRelationAllMapper.class);
            configuration.addMapper(CmdbOdsObjectMapper.class);
            configuration.addMapper(CmdbOdsObjectRelationMapper.class);
            configuration.setCallSettersOnNulls(true);
            SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(configuration);
            DateTime usedPartition = DateUtil.parse(wideTableConfig.getUsedPartition(), "yyyyMMddHHmmss");
            sqlSessionFactoryPair = Triple.of(sqlSessionFactory, new CmdbObjectInfo(
                    wideTableConfig.getCmdbOdsTable(),
                    wideTableConfig.getCmdbOdsRelationTable(),
                    wideTableConfig.getCmdbWideTable(),
                    wideTableConfig.getCmdbRelationWideTable(),
                    DateUtil.format(usedPartition, "yyyy-MM-dd HH:mm:ss")
                ),
                hikariDataSource);
            LOG.info("构建CK宽表查询sqlSessionFactory成功，配置:{}", wideTableConfig);
        } catch (IllegalArgumentException e) {
            LOG.error("'系统管理'-'维度建模'-'对象实例CK宽表配置'配置不正确，{}", e.getMessage());
        } catch (Exception e) {
            LOG.error("对象实例查询构建sqlSessionFactory异常", e);
        }
    }


    private synchronized void close() {
        if (Objects.isNull(sqlSessionFactoryPair)) {
            return;
        }
        HikariDataSource hikariDataSource = sqlSessionFactoryPair.getRight();
        sqlSessionFactoryPair = null;
        IoUtil.close(hikariDataSource);
    }


    private SqlSession getSession() {
        if (Objects.isNull(sqlSessionFactoryPair)) {
            throw new BizException(ResponseCode.FAILED.getCode(), "请检查系统管理-维度建模-对象实例CK宽表配置是否正确");
        }
        return sqlSessionFactoryPair.getLeft().openSession(false);
    }


    public List<ObjectCategoryTree> treeListWithObjectTable(ObjectCategoryTreeFilter categoryFilter) {
        List<ObjectCategoryTree> objectCategoryTreeList =
            objectCategoryService.treeListWithObjectTable(categoryFilter);

        Map<String, Long> map = new HashMap<>(4);
        try (SqlSession session = getSession()) {
            CmdbObjectAllMapper mapper = session.getMapper(CmdbObjectAllMapper.class);
            CmdbObjectInfo cmdbObjectInfo = getCmdbObjectInfo(null);
            List<CmdbObjectCount> cmdbObjectCountList = mapper.countByTag(cmdbObjectInfo);
            map = cmdbObjectCountList.stream().collect(Collectors.toMap(CmdbObjectCount::getTag, CmdbObjectCount::getCount));
        } catch (PersistenceException e) {
            if (TABLE_NOT_FOUND_PATTERN.matcher(e.getMessage()).find()) {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例CK宽表不存在,请检查CK宽表配置是否正确,以及模型是否发布成功");
            } else {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例查询异常", e);
            }
        }
        for (ObjectCategoryTree objectCategoryTree : objectCategoryTreeList) {
            countObjectInstance(objectCategoryTree, map);
        }
        return objectCategoryTreeList;
    }


    public void countObjectInstance(ObjectCategoryTree categoryTree, Map<String, Long> map) {
        if (CollUtil.isEmpty(categoryTree.getChildren())) {
            int count = map.getOrDefault(categoryTree.getCode(), 0L).intValue();
            categoryTree.setCount(count);
        } else {
            int count = 0;
            for (ObjectCategoryTree childTree : categoryTree.getChildren()) {
                countObjectInstance(childTree, map);
                count += childTree.getCount();
            }
            categoryTree.setCount(count);
        }
    }


    @Override
    public List<ObjectColumnResp> listColumnDeploy(Long objectId) {
        List<ObjectColumnResp> objectColumnRespList = objectColumnService.listByObjectTableId(objectId);
        if (CollUtil.isEmpty(objectColumnRespList)) {
            return null;
        }
        return objectColumnRespList.stream().filter(it -> !EXCLUDE_COLUMNS.contains(it.getColName())).collect(Collectors.toList());
    }

    @Override
    public Paged<Map<String, Object>> query(CmdbObjectReq condition) {
        TbObjectTable objectTable = tbObjectTableService.getById(condition.getObjectId());
        if (Objects.isNull(objectTable)) {
            throw new BizException(ResponseCode.FAILED.getCode(), "对象模型不存在");
        }
        List<ObjectColumnResp> objectColumnRespList = listColumnDeploy(condition.getObjectId());

        condition.setTag(objectTable.getTbName());
        try (SqlSession session = getSession()) {
            CmdbObjectAllMapper mapper = session.getMapper(CmdbObjectAllMapper.class);

            CmdbObjectInfo cmdbObjectInfo = getCmdbObjectInfo(objectColumnRespList);
            Long count = mapper.countByCondition(cmdbObjectInfo, condition);
            List<Map<String, Object>> mapList = mapper.selectByCondition(cmdbObjectInfo, condition,
                condition.getPage() * condition.getSize(), condition.getSize());

            return new Paged<>(count, mapList);
        } catch (PersistenceException e) {
            if (TABLE_NOT_FOUND_PATTERN.matcher(e.getMessage()).find()) {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例CK宽表不存在,请检查CK宽表配置是否正确,以及模型是否发布成功");
            } else {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例查询异常", e);
            }
        }
    }

    public CmdbObjectDetailResp getByVid(CmdbObjectDetailReq req) {
        List<TbObjectTable> objectTableList = tbObjectTableService.selectByTableNameList(CollUtil.newArrayList(req.getTag()));
        if (CollUtil.isEmpty(objectTableList)) {
            return null;
        }
        List<ObjectColumnResp> columnRespList = listColumnDeploy(objectTableList.get(0).getId());

        CmdbObjectDetailResp resp = new CmdbObjectDetailResp();
        resp.setColumnList(columnRespList);
        try (SqlSession session = getSession()) {
            CmdbObjectInfo cmdbObjectInfo = getCmdbObjectInfo(columnRespList);
            CmdbObjectAllMapper mapper = session.getMapper(CmdbObjectAllMapper.class);
            resp.setData(mapper.selectByVid(cmdbObjectInfo, req.getVid()));
        } catch (PersistenceException e) {
            if (TABLE_NOT_FOUND_PATTERN.matcher(e.getMessage()).find()) {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例CK宽表不存在,请检查CK宽表配置是否正确,以及模型是否发布成功");
            } else {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例查询异常", e);
            }
        }
        return resp;
    }

    @Override
    public List<CmdbObjectRelationResp> objectInstanceRelation(String vid) {
        List<CmdbObjectRelation> cmdbObjectRelationList = null;
        try (SqlSession session = getSession()) {
            CmdbObjectInfo cmdbObjectInfo = getCmdbObjectInfo(null);
            CmdbObjectRelationAllMapper mapper = session.getMapper(CmdbObjectRelationAllMapper.class);
            // 获取所有一度关系边
            cmdbObjectRelationList = mapper.selectOnceEdge(cmdbObjectInfo, vid);
            if (CollUtil.isEmpty(cmdbObjectRelationList)) {
                return CollUtil.newLinkedList();
            }
            CmdbObjectAllMapper cmdbObjectAllMapper = session.getMapper(CmdbObjectAllMapper.class);
            List<String> vidList = cmdbObjectRelationList.stream().map(it -> CollUtil.newArrayList(it.getSvid(), it.getDvid()))
                .flatMap(List::stream).distinct().collect(Collectors.toList());
            // 根据vid查询对象基本信息
            List<Map<String, Object>> cmdbObjectBaseInfoList = cmdbObjectAllMapper.selectBaseInfoByVidList(cmdbObjectInfo, vidList);
            // 根据vid分类
            Map<String, Map<String, Object>> cmdbObjectBaseInfoMap = cmdbObjectBaseInfoList.stream()
                .collect(Collectors.toMap(it -> it.get("vid").toString(), it -> it));
            // 查询所有对象模型信息
            Map<String, String> objectTableMap = queryObjectTableByTagList(cmdbObjectBaseInfoList);

            // 查询对象模型关系类型
            List<TbObjectRelationType> tbObjectRelationTypeList = tbObjectRelationTypeService.listByCode(cmdbObjectRelationList.stream()
                .map(CmdbObjectRelation::getType).collect(Collectors.toSet()));
            Map<String, String> relationTypeMap = tbObjectRelationTypeList.stream()
                .collect(Collectors.toMap(TbObjectRelationType::getCode, TbObjectRelationType::getName));

            return cmdbObjectRelationList.stream().map(it -> {
                CmdbObjectRelationResp resp = new CmdbObjectRelationResp();
                String infoVid;
                String direction;
                if (vid.equals(it.getSvid())) {
                    direction = "OUT";
                    infoVid = it.getDvid();
                } else {
                    direction = "IN";
                    infoVid = it.getSvid();
                }
                resp.setVid(infoVid);
                Map<String, Object> cmdbObjectBaseInfo = cmdbObjectBaseInfoMap.get(infoVid);
                if (MapUtil.isNotEmpty(cmdbObjectBaseInfo)) {
                    String tag = cmdbObjectBaseInfo.get("tag").toString();
                    resp.setTag(tag);
                    resp.setObjectTableName(objectTableMap.getOrDefault(tag, tag));
                    resp.setObjectId(cmdbObjectBaseInfo.get("objectId").toString());
                    resp.setObjectName(cmdbObjectBaseInfo.get("objectName").toString());
                }
                resp.setRelationType(it.getType());
                resp.setRelationTypeDesc(relationTypeMap.getOrDefault(it.getType(), it.getType()));
                resp.setDirection(direction);
                return resp;
            }).collect(Collectors.toList());

        } catch (PersistenceException e) {
            if (TABLE_NOT_FOUND_PATTERN.matcher(e.getMessage()).find()) {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例CK宽表不存在,请检查CK宽表配置是否正确,以及模型是否发布成功");
            } else {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例查询异常", e);
            }
        }
    }


    private Map<String, String> queryObjectTableByTagList(List<Map<String, Object>> cmdbObjectList) {
        if (CollUtil.isEmpty(cmdbObjectList)) {
            return MapUtil.newHashMap();
        }
        List<String> tagList = cmdbObjectList.stream().map(it -> it.get("tag").toString()).distinct().collect(Collectors.toList());
        List<List<String>> splitList = CollUtil.split(tagList, 100);
        return splitList.stream().map(it -> tbObjectTableService.selectByTableNameList(it))
            .flatMap(Collection::stream).collect(Collectors.toMap(TbObjectTable::getTbName, TbObjectTable::getName));

    }

    @Override
    public List<CmdbInstanceCount> objectRelationInstanceCount(ObjectRelationInstanceCountReq req) {
        List<CmdbInstanceCount> resultList = new ArrayList<>();
        String source = String.format("%s-%s", req.getSourceType().toLowerCase(), req.getConfigId());
        String startDate = req.getStartDate();
        String endDate = req.getEndDate();
        try (SqlSession session = getSession()) {
            CmdbObjectInfo cmdbObjectInfo = getCmdbObjectInfo(null);
            CmdbOdsObjectMapper cmdbOdsObjectMapper = session.getMapper(CmdbOdsObjectMapper.class);
            if (StrUtil.isAllBlank(req.getStartDate(), req.getEndDate())) {
                List<String> dateList = cmdbOdsObjectMapper.selectLatelyDate(cmdbObjectInfo, source);
                if (CollUtil.isEmpty(dateList)) {
                    return CollUtil.newArrayList();
                }
                startDate = dateList.get(dateList.size() - 1);
                endDate = dateList.get(0);
            }
            List<CmdbInstanceCount> objectInstanceCountList = cmdbOdsObjectMapper.countByTag(cmdbObjectInfo, source, startDate, endDate);
            if (CollUtil.isNotEmpty(objectInstanceCountList)) {
                resultList.addAll(objectInstanceCountList.stream()
                    .peek(it -> it.setInstanceType("OBJECT_INSTANCE")).collect(Collectors.toList()));
            }

            CmdbOdsObjectRelationMapper cmdbOdsObjectRelationMapper = session.getMapper(CmdbOdsObjectRelationMapper.class);
            List<CmdbInstanceCount> relationInstanceCountList =
                cmdbOdsObjectRelationMapper.countByType(cmdbObjectInfo, source, startDate, endDate);
            if (CollUtil.isNotEmpty(relationInstanceCountList)) {
                resultList.addAll(relationInstanceCountList.stream()
                    .peek(it -> it.setInstanceType("RELATION_INSTANCE")).collect(Collectors.toList()));
            }
        } catch (PersistenceException e) {
            if (TABLE_NOT_FOUND_PATTERN.matcher(e.getMessage()).find()) {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例CK宽表不存在,请检查CK宽表配置是否正确,以及模型是否发布成功");
            } else {
                throw new BizException(ResponseCode.FAILED.getCode(), "对象实例,关系实例统计条数查询异常", e);
            }
        }
        return resultList;
    }

    private CmdbObjectInfo getCmdbObjectInfo(List<ObjectColumnResp> objectColumnRespList) {
        CmdbObjectInfo cmdbObjectInfo = sqlSessionFactoryPair.getMiddle();
        CmdbObjectInfo newCmdbObjectInfo = new CmdbObjectInfo();
        newCmdbObjectInfo.setCmdbOdsTable(cmdbObjectInfo.getCmdbOdsTable());
        newCmdbObjectInfo.setCmdbOdsRelationTable(cmdbObjectInfo.getCmdbOdsRelationTable());
        newCmdbObjectInfo.setCmdbWideTable(cmdbObjectInfo.getCmdbWideTable());
        newCmdbObjectInfo.setCmdbRelationWideTable(cmdbObjectInfo.getCmdbRelationWideTable());
        newCmdbObjectInfo.setUsedPartition(cmdbObjectInfo.getUsedPartition());
        if (CollUtil.isNotEmpty(objectColumnRespList)) {
            newCmdbObjectInfo.setColumnList(objectColumnRespList.stream().map(ObjectColumnResp::getColName).collect(Collectors.toList()));
        }
        return newCmdbObjectInfo;
    }


}
