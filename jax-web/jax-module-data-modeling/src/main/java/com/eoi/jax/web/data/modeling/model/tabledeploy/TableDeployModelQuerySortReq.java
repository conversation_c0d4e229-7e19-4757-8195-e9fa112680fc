package com.eoi.jax.web.data.modeling.model.tabledeploy;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbTableDeploy;

/**
 * <AUTHOR>
 * @date 2023/2/2
 */
public class TableDeployModelQuerySortReq implements ISortReq<TbTableDeploy> {


    @Override
    public QueryWrapper<TbTableDeploy> order(QueryWrapper<TbTableDeploy> wrapper) {
        return wrapper;
    }

}
