package com.eoi.jax.web.data.modeling.factory.modeldeploy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.client.elasticsearch.ElasticsearchClient;
import com.eoi.jax.web.core.common.constant.ElasticsearchConstant;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.enumrate.*;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.core.util.ExceptionUtil;
import com.eoi.jax.web.core.util.ValidatorUtils;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.AbstractModelDeployService;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy.DeployScriptReq;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy.GenerateDefaultScriptReq;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy.GenerateExistTableScriptReq;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.deploy.ValidateModelSettingReq;
import com.eoi.jax.web.core.client.elasticsearch.ElasticsearchColumnSetting;
import com.eoi.jax.web.data.modeling.factory.modeldeploy.model.elasticsearch.ElasticsearchModelSetting;
import com.eoi.jax.web.data.modeling.model.columndeploy.ColumnDeployResp;
import com.eoi.jax.web.data.modeling.model.tabledeploy.TableDeployResp;
import com.eoi.jax.web.data.modeling.model.tabledeployhistory.TableDeployHistoryResp;
import com.eoi.jax.web.ingestion.factory.datasource.model.ElasticsearchConnection;
import com.eoi.jax.web.repository.entity.TbDatasource;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zsc
 * @create 2022/12/30 15:40
 */
@Service
public class ElasticsearchModelDeployServiceImpl extends AbstractModelDeployService {

    @Resource
    private JaxRepository jaxRepository;

    @Override
    public ElasticsearchModelSetting generateDefaultSetting(GenerateDefaultScriptReq req) {
        ElasticsearchModelSetting model = generateDefaultModel(req.getTbName(), req.getTbId());
        Map<Long, ElasticsearchColumnSetting> deployedColSettingMap = CollUtil.emptyIfNull(model.getColumnDeployList())
                .stream().collect(Collectors.toMap(ElasticsearchColumnSetting::getColId, x -> x, (x, y) -> x));
        List<ElasticsearchColumnSetting> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getColumnDeployList())) {
            for (ColumnDeployResp column : req.getColumnDeployList()) {
                ElasticsearchColumnSetting deployedCol = deployedColSettingMap.get(column.getColId());
                if (deployedCol != null && deployedCol.getColName().equals(column.getColName())
                        && deployedCol.getColType().equals(column.getColType())) {
                    list.add(deployedCol);
                } else {
                    list.add(generateDefaultModelColumn(column));
                }
            }
        }
        model.setColumnDeployList(list);
        return model;
    }

    @Override
    public ElasticsearchModelSetting generateExistTableSetting(GenerateExistTableScriptReq req) {
        ElasticsearchModelSetting model = generateDefaultModel(req.getTbName(), req.getTbId());
        List<ElasticsearchColumnSetting> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getColumnDeployList())) {
            for (ColumnDeployResp column : req.getColumnDeployList()) {
                list.add(generateDefaultModelColumn(column));
            }
        }
        model.setColumnDeployList(list);
        return model;
    }

    /**
     * 注意：
     * 1. elasticsearch的发布，生成的是index的模版template
     * 2. 根据index模版template的特性，它不支持也没有必要增量更新，所以每次发布都全量覆盖更好
     * 3. 当数据源或者模型名（模型名即template名）发生变化时，自动删除失效的旧template
     *
     * @param req
     */
    @Override
    public void deployScript(DeployScriptReq req) {
        TableDeployResp tableDeploy = req.getTableDeployResp();
        try {
            logTableDeployHistory(req.getTableDeployHistoryResp(), "deploy elasticsearch");
            deleteExpiredElasticsearchTemplate(req);
            createElasticsearchTemplate(req);
            tableDeploy.setStatus(TableDeployStatusEnum.SUCCESS.code());
            logTableDeployHistory(req.getTableDeployHistoryResp(), "deploy elasticsearch success");
        } catch (Exception e) {
            String errMsg = ExceptionUtil.stackTrace(e);
            tableDeploy.setStatus(TableDeployStatusEnum.FAILURE.code());
            tableDeploy.setErrMsg(errMsg);
            logTableDeployHistory(req.getTableDeployHistoryResp(), "deploy elasticsearch failed " + errMsg);
        }
    }

    @Override
    public ElasticsearchModelSetting getTableDeploySettingModelInstance(Map<String, Object> paramMap) {
        if (paramMap != null) {
            return BeanUtil.mapToBean(paramMap, ElasticsearchModelSetting.class, false);
        }
        return new ElasticsearchModelSetting();
    }

    private void createElasticsearchTemplate(DeployScriptReq req) {
        if (TableDeployActionTypeEnum.DELETE.code().equals(req.getTableDeployResp().getActionType())) {
            return;
        }
        String templateName = generateTemplateName(req.getTableDeployResp());
        logTableDeployHistory(req.getTableDeployHistoryResp(), "generate elasticsearch template " + templateName);
        Map<String, Object> settingMap = JsonUtil.decode2Map(req.getTableDeployResp().getSetting());
        ElasticsearchModelSetting setting = getTableDeploySettingModelInstance(settingMap);
        Map<String, Object> template = setting.getTemplate();
        logTableDeployHistory(req.getTableDeployHistoryResp(), "template setting "
                + JsonUtil.encode(template));
        TbDatasource tbDatasource = jaxRepository.datasourceService().getById(req.getTableDeployResp().getDsId());
        ElasticsearchConnection datasource = JsonUtil.decode(tbDatasource.getSetting(), ElasticsearchConnection.class);
        ElasticsearchClient elasticsearchClient = new ElasticsearchClient(
                datasource.getAddress(),
                datasource.getUsername(),
                datasource.getPassword()
        );
        logTableDeployHistory(req.getTableDeployHistoryResp(), "update elasticsearch template " + templateName
                + " on " + datasource.getAddress());
        elasticsearchClient.putTemplate(templateName, template);
    }

    private void deleteExpiredElasticsearchTemplate(DeployScriptReq req) {
        if (!needDeleteExpiredTemplate(req)) {
            return;
        }
        TableDeployResp currentTableDeploy = req.getTableDeployResp();
        if (TableDeployActionTypeEnum.DELETE.code().equals(currentTableDeploy.getActionType())) {
            if (TableDeployDeleteTypeEnum.PHYSICAL.code().equals(currentTableDeploy.getDeleteType())) {
                String templateName = generateTemplateName(currentTableDeploy);
                logTableDeployHistory(req.getTableDeployHistoryResp(), "found expired elasticsearch template " + templateName);
                TbDatasource tbDatasource = jaxRepository.datasourceService().getById(currentTableDeploy.getDsId());
                ElasticsearchConnection datasource = JsonUtil.decode(tbDatasource.getSetting(), ElasticsearchConnection.class);
                ElasticsearchClient elasticsearchClient = new ElasticsearchClient(
                        datasource.getAddress(),
                        datasource.getUsername(),
                        datasource.getPassword()
                );
                logTableDeployHistory(req.getTableDeployHistoryResp(), "delete expired elasticsearch template " + templateName
                        + " on " + datasource.getAddress());
                if (elasticsearchClient.existsTemplate(templateName)) {
                    elasticsearchClient.deleteTemplate(templateName);
                }
            }
        } else {
            TableDeployResp lastTableDeploy = currentTableDeploy.getLastSuccessTableDeploy();
            String templateName = generateTemplateName(lastTableDeploy);
            logTableDeployHistory(req.getTableDeployHistoryResp(), "found expired elasticsearch template " + templateName);
            TbDatasource tbDatasource = jaxRepository.datasourceService().getById(lastTableDeploy.getDsId());
            ElasticsearchConnection datasource = JsonUtil.decode(tbDatasource.getSetting(), ElasticsearchConnection.class);
            ElasticsearchClient elasticsearchClient = new ElasticsearchClient(
                    datasource.getAddress(),
                    datasource.getUsername(),
                    datasource.getPassword()
            );
            logTableDeployHistory(req.getTableDeployHistoryResp(), "delete expired elasticsearch template " + templateName
                    + " on " + datasource.getAddress());
            if (elasticsearchClient.existsTemplate(templateName)) {
                elasticsearchClient.deleteTemplate(templateName);
            }
        }
    }

    private boolean needDeleteExpiredTemplate(DeployScriptReq req) {
        if (TableDeployActionTypeEnum.DELETE.code().equals(req.getTableDeployResp().getActionType())) {
            return true;
        }
        if (req.getTableDeployResp().getLastSuccessTableDeploy() == null) {
            // 没有发布记录
            return false;
        }
        if (req.getTableDeployResp().getDsId().longValue() != req.getTableDeployResp().getLastSuccessTableDeploy().getDsId().longValue()) {
            // 发布的目标数据源已发生变化
            return true;
        }
        if (!StrUtil.equals(req.getTableDeployResp().getTbName(), req.getTableDeployResp().getLastSuccessTableDeploy().getTbName())) {
            // 发布的目标模型名已发生变化
            return true;
        }
        return false;
    }

    private String generateTemplateName(TableDeployResp deploy) {
        String name = ElasticsearchConstant.TEMPLATE_NAME_FIX_PREFIX + deploy.getTbName() + "_" + deploy.getTbId();
        // elasticsearch的template名字禁止使用大写字母和特殊字符
        return Common.encodeToSafeString(name.toLowerCase());
    }

    private ElasticsearchModelSetting generateDefaultModel(String tbName, Long tbId) {
        ElasticsearchModelSetting model = new ElasticsearchModelSetting();
        TableDeployResp deployResp = getLastSuccessTableDeploy(tbId);
        if (deployResp != null) {
            model = BeanUtil.mapToBean(deployResp.getSettingMap(), ElasticsearchModelSetting.class, true);
        } else {
            model.setTemplateIndexPrefix(tbName);
            model.setTemplateIndexMiddle(ElasticsearchConstant.DEFAULT_TEMPLATE_INDEX_MIDDLE);
            model.setTemplateIndexSuffix(ElasticsearchConstant.DEFAULT_TEMPLATE_INDEX_SUFFIX);
            model.setIndexShards(ElasticsearchConstant.DEFAULT_TEMPLATE_INDEX_SHARDS);
            model.setIndexReplicas(ElasticsearchConstant.DEFAULT_TEMPLATE_INDEX_REPLICAS);
            model.setDynamicMapping(ElasticsearchDynamicMappingEnum.TRUE.code());
            model.setIndexNameStrategy(ElasticsearchIndexNameStrategyEnum.DAY.code());
            String indexNamePattern = ElasticsearchIndexNameStrategyEnum.DAY.genIndexNamePattern(tbName);
            model.setIndexNamePatterns(new ArrayList<>(Collections.singletonList(indexNamePattern)));
            model.setTimeFieldType("EVENT_TIME");
        }
        return model;
    }

    private ElasticsearchColumnSetting generateDefaultModelColumn(ColumnDeployResp column) {
        ElasticsearchColumnSetting setting = new ElasticsearchColumnSetting();
        setting.setColId(column.getColId());
        setting.setColName(column.getColName());
        setting.setColType(column.getColType());
        setting.setPropertyName(column.getColName());
        ElasticsearchPropertyTypeEnum typeEnum = ElasticsearchPropertyTypeEnum.from(
                ColumnTypeEnum.fromString(column.getColType()));
        setting.setPropertyType(typeEnum.code());
        if (ElasticsearchPropertyTypeEnum.DATE == typeEnum) {
            setting.setDatetimeFormat(new ArrayList<>(Arrays.asList(
                    ElasticsearchDateFormatEnum.STRICT_DATE_OPTIONAL_TIME.code(),
                    ElasticsearchDateFormatEnum.EPOCH_MILLIS.code())
            ));
        }
        return setting;
    }

    private void logTableDeployHistory(TableDeployHistoryResp history, String msg) {
        String deployLog = history.getDeployLog();
        if (StrUtil.isEmpty(deployLog)) {
            deployLog = msg;
        } else {
            deployLog = deployLog + "\n" + msg;
        }
        history.setDeployLog(deployLog);
    }

    /**
     * 默认校验数据源独有配置参数
     *
     * @param req
     */
    @Override
    public void validate(ValidateModelSettingReq req) {
        if (req.getSettingModel() == null) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "配置不能为空");
        }
        ValidatorUtils.validateEntity(req.getSettingModel());

        ElasticsearchModelSetting elasticsearchModelSetting = (ElasticsearchModelSetting) req.getSettingModel();

        Assert.notBlank(elasticsearchModelSetting.getTemplateIndexPrefix(), "索引名模板前缀不能为空");
        Assert.notBlank(elasticsearchModelSetting.getTemplateIndexMiddle(), "索引名模板连接不能为空");
        Assert.notBlank(elasticsearchModelSetting.getTemplateIndexSuffix(), "索引名模板后缀不能为空");

        Assert.notNull(elasticsearchModelSetting.getIndexShards(), "分片数不能为空");
        Assert.notNull(elasticsearchModelSetting.getIndexReplicas(), "副本数不能为空");

        Assert.notBlank(elasticsearchModelSetting.getDynamicMapping(), "动态字段策略不能为空");
        Assert.notBlank(elasticsearchModelSetting.getIndexNameStrategy(), "索引名生成策略不能为空");
        Assert.notNull(elasticsearchModelSetting.getIndexNamePatterns(), "规则定义不能为空");

    }
}
