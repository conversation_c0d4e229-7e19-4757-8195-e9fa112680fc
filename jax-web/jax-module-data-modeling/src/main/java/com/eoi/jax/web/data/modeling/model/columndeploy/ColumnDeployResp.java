package com.eoi.jax.web.data.modeling.model.columndeploy;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.data.modeling.model.cloumndict.ColumnDictResp;
import com.eoi.jax.web.repository.entity.TbColumnDeploy;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Author: zsc
 * @Date: 2022/01/03
 */
public class ColumnDeployResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbColumnDeploy> {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "数据模型发布表id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long tableDeployId;

    @Schema(description = "字段id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long colId;

    @Schema(description = "字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN")
    private String colType;

    @Schema(description = "字段名")
    private String colName;

    @Schema(description = "显示名")
    private String colDisplay;

    @Schema(description = "表单类型")
    private String formType;

    @Schema(description = "字段用途：DIM 维度表字段，IDX 指标值表字段")
    private String colUsage;

    @Schema(description = "字段描述")
    private String description;

    @Schema(description = "是否主键")
    private Boolean isPrimaryKey;

    @Schema(description = "是否不为空")
    private Boolean isNotNull;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "长度")
    private String colLength;

    @Schema(description = "精度")
    private String numPrecision;

    @Schema(description = "字段标准id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long dicId;

    @Schema(description = "字段标准配置，json字符串")
    private String dicSetting;

    @Schema(description = "字段标准")
    private ColumnDictResp dict;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getTableDeployId() {
        return tableDeployId;
    }

    public String getColUsage() {
        return colUsage;
    }

    public void setColUsage(String colUsage) {
        this.colUsage = colUsage;
    }

    public void setTableDeployId(Long tableDeployId) {
        this.tableDeployId = tableDeployId;
    }

    public Long getColId() {
        return colId;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getDescription() {
        return description;
    }

    public ColumnDictResp getDict() {
        return dict;
    }

    public void setDict(ColumnDictResp dict) {
        this.dict = dict;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsPrimaryKey() {
        return isPrimaryKey;
    }

    public void setIsPrimaryKey(Boolean primaryKey) {
        isPrimaryKey = primaryKey;
    }

    public Boolean getIsNotNull() {
        return isNotNull;
    }

    public void setIsNotNull(Boolean notNull) {
        isNotNull = notNull;
    }

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public String getDicSetting() {
        return dicSetting;
    }

    public void setDicSetting(String dicSetting) {
        this.dicSetting = dicSetting;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getColLength() {
        return colLength;
    }

    public void setColLength(String colLength) {
        this.colLength = colLength;
    }

    public String getNumPrecision() {
        return numPrecision;
    }

    public void setNumPrecision(String numPrecision) {
        this.numPrecision = numPrecision;
    }
}
