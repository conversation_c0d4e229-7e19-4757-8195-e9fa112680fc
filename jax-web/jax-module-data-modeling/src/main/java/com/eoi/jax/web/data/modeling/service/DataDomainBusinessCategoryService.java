package com.eoi.jax.web.data.modeling.service;

import com.eoi.jax.web.core.service.IBaseService;
import com.eoi.jax.web.data.modeling.model.business.DataDomainBusinessCategoryResp;
import com.eoi.jax.web.data.modeling.model.databusiness.DataDomainBusinessCategoryCreateReq;
import com.eoi.jax.web.data.modeling.model.databusiness.DataDomainBusinessCategoryQueryReq;
import com.eoi.jax.web.data.modeling.model.databusiness.DataDomainBusinessCategoryUpdateReq;
import com.eoi.jax.web.data.modeling.model.databusiness.TbDataDomainBusinessCategoryResp;
import com.eoi.jax.web.repository.entity.TbDataDomain;
import com.eoi.jax.web.repository.entity.TbDataDomainBusinessCategory;
import com.eoi.jax.web.repository.service.TbDataDomainBusinessCategoryService;

import java.util.List;

public interface DataDomainBusinessCategoryService extends IBaseService<
        TbDataDomainBusinessCategoryService,
        TbDataDomainBusinessCategory,
        TbDataDomainBusinessCategoryResp,
        DataDomainBusinessCategoryCreateReq,
        DataDomainBusinessCategoryUpdateReq,
        DataDomainBusinessCategoryQueryReq> {

    /**
     * 查询业务分类下的数据域,过掉掉指定的domain主键id
     *
     * @param bizId
     * @return
     */
    List<TbDataDomain> listFilterDataDomains(Long bizId);

    /**
     * 业务分类下的数据域
     *
     * @param bizId
     * @return
     */
    List<DataDomainBusinessCategoryResp> listDataDomains(Long bizId);

    /**
     * 一键创建业务分类和数据域的关联，已有的关联记录不在重复创建
     *
     * @param bizId
     * @return
     */
    List<TbDataDomainBusinessCategoryResp> createUnRelation(Long bizId);

}
