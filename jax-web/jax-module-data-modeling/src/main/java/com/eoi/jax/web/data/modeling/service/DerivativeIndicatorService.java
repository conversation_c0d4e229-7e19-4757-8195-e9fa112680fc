package com.eoi.jax.web.data.modeling.service;

import com.eoi.jax.web.core.excel.IBaseAuthExcelService;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.data.modeling.model.excel.DerivativeIndicatorModel;
import com.eoi.jax.web.data.modeling.model.indicator.*;
import com.eoi.jax.web.repository.entity.TbDerivativeIndicator;
import com.eoi.jax.web.repository.service.TbDerivativeIndicatorService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/11/21
 **/
public interface DerivativeIndicatorService extends IBaseAuthExcelService<
        TbDerivativeIndicatorService,
        TbDerivativeIndicator,
        DerivativeIndicatorResp,
        DerivativeIndicatorCreateReq,
        DerivativeIndicatorUpdateReq,
        DerivativeIndicatorQueryReq,
        DerivativeIndicatorModel> {

    /**
     * 派生指标树
     *
     * @param catalog
     * @param searchType
     * @return
     */
    List<IndicatorNode> tree(String catalog, String searchType);


    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    List<DerivativeIndicatorResp> deleteBulk(List<Long> ids);


    /**
     * 批量查询派生指标关联引用情况
     *
     * @param ids
     * @return
     */
    List<UsageResp> usageByBulk(List<Long> ids);

    /**
     * 规则校验-英文缩写及中文指标名
     *
     * @param req
     * @return
     */
    Object checkName(DerivativeIndicatorCreateReq req);
}
