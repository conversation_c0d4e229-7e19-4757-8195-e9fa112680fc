package com.eoi.jax.web.data.modeling.controller;

import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpParameters;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.modeling.model.busproc.BusinessProcessCreateReq;
import com.eoi.jax.web.data.modeling.model.busproc.BusinessProcessQuery;
import com.eoi.jax.web.data.modeling.model.busproc.BusinessProcessUpdateReq;
import com.eoi.jax.web.data.modeling.service.BusinessProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * BusProcessValueController 业务流程(标签), 属于某个Domain(标签)的值(枚举?)
 *
 * @param:
 * @author: allenhe888
 * @date: 2022/11/15 15:53
 */
@RestController
public class BusinessProcessController implements V2Controller {

    private static final String CATEGORY_NAME = "数据建模";
    private static final String MODEL_NAME = "数仓规划";
    private static final String FUNCTION_NAME = "业务过程";
    private static final String CODE = "businessProcess";

    @Autowired
    private BusinessProcessService webService;

    @Operation(summary = "查询某ID的BusinessProcess业务流程(标签值)")
    @GetMapping("data/modeling/bus-process/{id}")
    public Response get(@Parameter(required = true) @PathVariable("id") Long id) {
        return Response.success(webService.get(id));
    }

    @Operation(summary = "创建一个新的BusinessProcess业务流程(标签值)")
    @PostMapping("data/modeling/bus-process")
    @AuditLog(category = CATEGORY_NAME, opAction = OpActionEnum.CREATE, module = MODEL_NAME, function = FUNCTION_NAME, code = CODE)
    public Response create(@OpParameters @RequestBody BusinessProcessCreateReq req) {
        return Response.success(webService.create(req));
    }

    @Operation(summary = "更新某ID的BusinessProcess业务流程(标签值)")
    @PutMapping("data/modeling/bus-process/{id}")
    @AuditLog(category = CATEGORY_NAME, opAction = OpActionEnum.UPDATE, module = MODEL_NAME, function = FUNCTION_NAME, code = CODE)
    public Response update(
            @OpPrimaryKey @Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
            @OpParameters @RequestBody BusinessProcessUpdateReq req
    ) {
        req.setId(id);
        return Response.success(webService.update(req));
    }

    @Operation(summary = "删除指定ID的BusinessProcess业务流程(标签值)")
    @DeleteMapping("data/modeling/bus-process/{id}")
    @AuditLog(category = CATEGORY_NAME, opAction = OpActionEnum.DELETE, module = MODEL_NAME, function = FUNCTION_NAME, code = CODE)
    public Response delete(
            @OpPrimaryKey @OpParameters @Parameter(description = "主键id", required = true) @PathVariable("id") Long id
    ) {
        return Response.success(webService.delete(id));
    }

    @Operation(summary = "按过滤条件查询BusinessProcess业务流程(标签值)分页查询")
    @PostMapping("data/modeling/bus-process/query")
    public Response query(@RequestBody BusinessProcessQuery query) {
        return Response.success(webService.query(query));
    }


    @Operation(summary = "查询所有的BusinessProcess业务流程(标签值)")
    @GetMapping("data/modeling/bus-process/list")
    public Response listAll() {
        return Response.success(webService.all());
    }

    @Operation(summary = "业务过程使用情况")
    @GetMapping("data/modeling/bus-process/{id}/usage")
    public Response usage(
            @Parameter(description = "主键id", required = true) @PathVariable("id") Long id
    ) {
        return Response.success(webService.usage(id));
    }
}
