package com.eoi.jax.web.data.modeling.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.LayerCatalog;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.constant.TableStorageModeEnum;
import com.eoi.jax.web.core.common.constant.TableType;
import com.eoi.jax.web.core.common.enumrate.*;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.common.exception.ParamValidationException;
import com.eoi.jax.web.core.common.util.Common;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.excel.AbstractExcelRowResp;
import com.eoi.jax.web.core.excel.BaseAuthExcelService;
import com.eoi.jax.web.core.excel.ImportExcelReq;
import com.eoi.jax.web.core.excel.SheetResp;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.core.model.lifecycle.TableEntityDeployEvent;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.data.modeling.model.adjunct.AdjunctResp;
import com.eoi.jax.web.data.modeling.model.column.ColumnCreateReq;
import com.eoi.jax.web.data.modeling.model.column.ColumnExcelModel;
import com.eoi.jax.web.data.modeling.model.column.ColumnQueryReq;
import com.eoi.jax.web.data.modeling.model.rule.RuleRunResp;
import com.eoi.jax.web.data.modeling.model.table.*;
import com.eoi.jax.web.data.modeling.model.time.TimePeriodResp;
import com.eoi.jax.web.data.modeling.service.CheckRuleService;
import com.eoi.jax.web.data.modeling.service.ColumnService;
import com.eoi.jax.web.data.modeling.service.DimensionService;
import com.eoi.jax.web.data.modeling.service.TableService;
import com.eoi.jax.web.ingestion.enumrate.TagTypeEnum;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeQueryFilterReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeQueryReq;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeResp;
import com.eoi.jax.web.ingestion.model.businessflowtree.BusinessFlowTreeUpdateReq;
import com.eoi.jax.web.ingestion.service.BusinessFlowTreeService;
import com.eoi.jax.web.ingestion.service.TagRelationService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.search.query.VTableQueryParam;
import com.eoi.jax.web.repository.search.result.TableQueryResult;
import com.eoi.jax.web.repository.search.result.VTable;
import com.eoi.jax.web.repository.service.*;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.eoi.jax.web.core.common.constant.LayerCatalog.APPLICATION_DATA_SERVICE;
import static com.eoi.jax.web.core.common.constant.LayerCatalog.COMMON_DATA_MODEL;
import static com.eoi.jax.web.core.common.constant.TableType.*;
import static com.eoi.jax.web.ingestion.enumrate.TagRelationRecordTypeEnum.TB_TABLE;
import static com.eoi.jax.web.ingestion.service.impl.StorageCkServiceImpl.*;

/**
 * @Author: yaru.ma
 * @Date: 2022/11/28
 */
@Service
public class TableServiceImpl extends BaseAuthExcelService<
        TbTableService,
        TbTable,
        TableResp,
        TableCreateReq,
        TableUpdateReq,
        TableQueryReq,
        TableExcelModel> implements TableService {


    private static final String FLOW_TREE_TYPE = BusinessFlowTreeReferenceTypeEnum.TABLE_DEPLOY.code();
    private static final String PARENT_FLOW_TREE_TYPE = BusinessFlowTreeReferenceTypeEnum.TABLE.code();

    @Autowired
    private TbTableService tbTableService;

    @Autowired
    private ColumnService columnService;

    @Autowired
    private TbColumnService tbColumnService;

    @Autowired
    private CheckRuleService ruleService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private JaxRepository jaxRepository;

    @Autowired
    private TbStorageCkService tbStorageCkService;

    @Autowired
    private BusinessFlowTreeService businessFlowTreeService;

    @Autowired
    private TbTableDeployService tbTableDeployService;

    @Autowired
    private TbMetricItemService tbMetricItemService;

    @Autowired
    private TbIngestionService tbIngestionService;

    @Autowired
    private TbIngestionJobService tbIngestionJobService;

    @Autowired
    private TbRealtimeCheckTaskService tbRealtimeCheckTaskService;

    @Autowired
    private TbStorageEsService tbStorageEsService;

    @Autowired
    private TbStorageHiveService tbStorageHiveService;

    @Resource
    private TagRelationService tagRelationService;
    @Autowired
    private TbTableDeployHistoryService tbTableDeployHistoryService;

    public TableServiceImpl(TbTableService dao) {
        super(dao);
    }

    @Override
    public void whenCreate(TableCreateReq req, TbTable entity) {
        super.whenCreate(req, entity);
        verifyReq(entity);
        checkUniCode(entity);
        checkIdxTableColumns(entity, req.getColumns());
        RuleRunResp ruleRunResp = ruleService.checkTableNameByRule(entity);
        ruleService.checkObjectInRule(ruleRunResp);
        entity.setIsOnline(1);
        columnService.savaTable(entity, req.getColumns());
        dealTagAdd(entity.getId(), req.getTags());
    }

    @Override
    public RuleRunResp checkTableName(TableCreateReq req) {
        TbTable table = req.toEntity();
        verifyReq(table);
        return ruleService.checkTableNameByRule(table);
    }

    @Override
    public void whenUpdate(TableUpdateReq req, TbTable entity) {
        super.whenUpdate(req, entity);
        verifyReq(entity);
        TbTable tb = tbTableService.selectByIdWithProjectAuth(entity.getId(), new BizException(ResponseCode.ID_NOT_EXISTS));
        entity.setTbType(tb.getTbType());
        checkUniCode(entity);
        checkIdxTableColumns(entity, req.getColumns());
        RuleRunResp ruleRunResp = ruleService.checkTableNameByRule(entity);
        ruleService.checkObjectInRule(ruleRunResp);
        columnService.savaTable(entity, req.getColumns());
        dealTagAdd(entity.getId(), req.getTags());
        // 更新流程树的名称
        BusinessFlowTreeResp treeNode = businessFlowTreeService.getOne(BusinessFlowTreeReferenceTypeEnum.TABLE.code(), req.getId());
        if (treeNode != null) {
            treeNode.setName(req.getTbName());
            BusinessFlowTreeUpdateReq updateReq = new BusinessFlowTreeUpdateReq();
            BeanUtil.copyProperties(treeNode, updateReq);
            businessFlowTreeService.update(updateReq);
        }
    }

    @Override
    public TableResp update(TableUpdateReq req) {
        TableResp update = super.update(req);
        // 数仓分层变化引起生命周期变化
        ContextHolder.publishEvent(new TableEntityDeployEvent(TableDeployActionTypeEnum.INCREMENT,
                jaxRepository.table().getById(req.getId())));
        return update;
    }

    @Override
    public void whenDelete(TbTable entity) {
        super.whenDelete(entity);
        columnService.deleteTable(entity.getId());
        // 删除模型发布记录,历史，字段记录
        List<TbTableDeploy> deploys = tbTableDeployService.deleteByTableId(entity.getId());
        tbTableDeployHistoryService.deleteByTableId(entity.getId());
        dealTagAdd(entity.getId(), null);
        // 删除业务流程树节点
        if (deploys != null && deploys.size() > 0) {
            for (TbTableDeploy deploy : deploys) {
                deleteBusinessFlowTree(deploy);
            }
        }
    }

    @Override
    public TableResp delete(Long id) {
        TbTable table = getEntityWithProjectAuth(id);
        TableResp resp = super.delete(id);
        ContextHolder.publishEvent(new TableEntityDeployEvent(TableDeployActionTypeEnum.DELETE, table));
        return resp;
    }

    /**
     * 处理标签
     *
     * @param id
     * @param tags
     */
    private void dealTagAdd(Long id, List<String> tags) {
        if (CollUtil.isEmpty(tags)) {
            tagRelationService.removeByRecordIdAndRecordType(id, TB_TABLE);
            return;
        }
        tagRelationService.saveTagAndTagRelation(TagTypeEnum.TABLE, tags, TB_TABLE, id);
    }

    /**
     * 删除模型发布表的业务流程树节点
     *
     * @param entity
     */
    private void deleteBusinessFlowTree(TbTableDeploy entity) {
        Long parentReferenceId = entity.getTbId();
        BusinessFlowTreeResp treeResp = businessFlowTreeService.getOne(PARENT_FLOW_TREE_TYPE, parentReferenceId);
        BusinessFlowTreeQueryReq query = new BusinessFlowTreeQueryReq();
        BusinessFlowTreeQueryFilterReq filter = new BusinessFlowTreeQueryFilterReq();
        filter.setParentId(treeResp.getId());
        query.setFilter(filter);
        Paged<BusinessFlowTreeResp> paged = businessFlowTreeService.query(query);
        if (paged.getList() == null || paged.getList().size() <= 1) {
            businessFlowTreeService.delete(PARENT_FLOW_TREE_TYPE, parentReferenceId);
        }
        Long referenceId = entity.getId();
        businessFlowTreeService.delete(FLOW_TREE_TYPE, referenceId);
    }

    @Override
    public TableResp get(Long id) {
        super.get(id);
        VTable vTables = jaxRepository.table().queryTableById(id);
        TableResp tableResp = new TableResp().fromEntity(vTables);
        tableResp.setAdjList(CollUtil.emptyIfNull(tableResp.getAdjIds())
                .stream().map(adj -> jaxRepository.adj().getById(adj))
                .filter(ObjectUtil::isNotNull)
                .map(x -> (AdjunctResp) new AdjunctResp().fromEntity(x))
                .collect(Collectors.toList()));
        tableResp.setPeriodList(CollUtil.emptyIfNull(tableResp.getPeriodIds())
                .stream().map(time -> jaxRepository.timePeriodService().getById(time))
                .filter(ObjectUtil::isNotNull)
                .map(x -> (TimePeriodResp) new TimePeriodResp().fromEntity(x))
                .collect(Collectors.toList()));
        tableResp.setColumns(columnService.getTableColumns(id));
        // 版本号
        TbTableDeploy deploy = jaxRepository.tableDeployService().getOne(new LambdaQueryWrapper<TbTableDeploy>()
                .eq(TbTableDeploy::getTbId, id)
                .orderByDesc(TbTableDeploy::getVersion)
                .last("limit 1"));
        tableResp.setVersion(ObjectUtil.isNotNull(deploy) ? deploy.getVersion() : null);
        // tag
        tableResp.setTags(tagRelationService.getTagByRecordIdAndRecordType(id, TB_TABLE));
        return tableResp;
    }

    @SuppressWarnings("all")
    @Override
    public List<UsageResp> usageByEntity(TbTable entity) {
        List<UsageResp> resps = new ArrayList<>();
        // 1.维度表,可能被被表详情-关联粒度关联
        if (TableType.DIM.equals(entity.getTbType())) {
            List<Long> tableIds = jaxRepository.column().list(new LambdaQueryWrapper<TbColumn>()
                            .eq(TbColumn::getDimTbId, entity.getId()).select(TbColumn::getTbId))
                    .stream().map(TbColumn::getTbId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tableIds)) {
                resps.addAll(jaxRepository.table().listByIds(tableIds)
                        .stream().map(x -> new UsageResp().fromEntity(entity, x))
                        .collect(Collectors.toList()));
            }
        }
        // 2. 已发布的不能删除，如果有删除动作成功的则可以删除
        List<TbTableDeploy> deploys = jaxRepository.tableDeployService()
                .list(new LambdaQueryWrapper<TbTableDeploy>().eq(TbTableDeploy::getTbId, entity.getId()));
        if (deploys != null && deploys.size() > 0) {
            boolean isDeleted = false;
            for (TbTableDeploy deploy : deploys) {
                if (TableDeployActionTypeEnum.DELETE.code().equals(deploy.getActionType())
                        && TableDeployStatusEnum.SUCCESS.code().equals(deploy.getStatus())) {
                    isDeleted = true;
                }
            }
            if (!isDeleted) {
                resps.addAll(deploys.stream().map(x -> new UsageResp().fromEntity(entity, x))
                        .collect(Collectors.toList()));
            }
        }

        // 数据集成相关表
        resps.addAll(tbIngestionService
                .list(new LambdaQueryWrapper<TbIngestion>().eq(TbIngestion::getTbId, entity.getId()))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        resps.addAll(tbIngestionJobService
                .list(new LambdaQueryWrapper<TbIngestionJob>().eq(TbIngestionJob::getTbId, entity.getId()))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        // 实时任务检查
        resps.addAll(tbRealtimeCheckTaskService
                .list(new LambdaQueryWrapper<TbRealtimeCheckTask>().eq(TbRealtimeCheckTask::getTbId, entity.getId()))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));

        // ck任务
        resps.addAll(tbStorageCkService
                .list(new LambdaQueryWrapper<TbStorageCk>().eq(TbStorageCk::getCkTbId, entity.getId()).or().
                        eq(TbStorageCk::getDimTbId, entity.getId()).or().
                        eq(TbStorageCk::getIdxTbId, entity.getId()).or().
                        eq(TbStorageCk::getKafkaTbId, entity.getId())
                )
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));

        // es任务
        resps.addAll(tbStorageEsService
                .list(new LambdaQueryWrapper<TbStorageEs>().eq(TbStorageEs::getEsTbId, entity.getId()).or().
                        eq(TbStorageEs::getKafkaTbId, entity.getId())
                )
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));

        // hive任务
        resps.addAll(tbStorageHiveService
                .list(new LambdaQueryWrapper<TbStorageHive>().eq(TbStorageHive::getHiveTbId, entity.getId()).or().
                        eq(TbStorageHive::getKafkaTbId, entity.getId())
                )
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));

        // 被指标项关联不能删除
        resps.addAll(tbMetricItemService
                .list(new LambdaQueryWrapper<TbMetricItem>().eq(TbMetricItem::getIdxId, entity.getId()))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));

        // 指标表的维度表
        resps.addAll(tbTableService
                .list(new LambdaQueryWrapper<TbTable>().eq(TbTable::getDimTbId, entity.getId()))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        return resps;
    }

    @Override
    public List<TableResp> listTable(List<String> layerCatalogs, List<String> tbTypes, Long bizId, Long busProcessId) {
        return tbTableService.selectListWithProjectAuth(new LambdaQueryWrapper<TbTable>()
                        .in(CollUtil.isNotEmpty(layerCatalogs), TbTable::getLayerCatalog, layerCatalogs)
                        .in(CollUtil.isNotEmpty(tbTypes), TbTable::getTbType, tbTypes)
                        .eq(ObjectUtil.isNotNull(bizId), TbTable::getBizId, bizId)
                        .eq(ObjectUtil.isNotNull(busProcessId), TbTable::getProcId, busProcessId))
                .stream().map(x -> (TableResp) new TableResp().fromEntity(x))
                .collect(Collectors.toList());
    }

    public static void checkIdxTableColumns(TbTable entity, List<ColumnCreateReq> columns) {
        if (!TableType.IDX.code().equals(entity.getTbType())) {
            return;
        }
        if (columns == null || columns.size() < 1) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "指标表的必填字段不能为空");
        }

        Set<String> idxTableContainsColumns = new HashSet<>();
        Set<String> dimTableContainsColumns = new HashSet<>();
        for (ColumnCreateReq column : columns) {
            if (ColumnUsageEnum.fromString(column.getColUsage()) == null) {
                throw new ParamValidationException("字段 " + column.getColName() + "必须指定colUsage属性为DIM、IDX中的一种");
            }
            if (ColumnUsageEnum.DIM.code().equals(column.getColUsage())) {
                if (DIM_TABLE_NEED_COLUMNS.contains(column.getColName())) {
                    dimTableContainsColumns.add(column.getColName());
                } else {
                    if (ColumnTypeEnum.isNumber(column.getColType())) {
                        throw new ParamValidationException("指标维度表字段 " + column.getColName() + "必须为非数值类型");
                    }
                }
            }
            if (ColumnUsageEnum.IDX.code().equals(column.getColUsage())) {
                if (TableStorageModeEnum.SINGLE.code().equals(entity.getStorageMode())) {
                    if (SINGLE_IDX_TABLE_NEED_COLUMNS.contains(column.getColName())) {
                        idxTableContainsColumns.add(column.getColName());
                    } else {
                        if (!ColumnTypeEnum.isNumber(column.getColType())) {
                            throw new ParamValidationException("指标值表字段 " + column.getColName() + "必须为数值类型");
                        }
                    }
                } else {
                    if (MULTI_IDX_TABLE_NEED_COLUMNS.contains(column.getColName())) {
                        idxTableContainsColumns.add(column.getColName());
                    } else {
                        if (!ColumnTypeEnum.isNumber(column.getColType())) {
                            throw new ParamValidationException("指标值表字段 " + column.getColName() + "必须为数值类型");
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(entity.getDimTbName()) && dimTableContainsColumns.size() != DIM_TABLE_NEED_COLUMNS.size()) {
            throw new ParamValidationException("指标维度表必须包含以下字段：" + DIM_TABLE_NEED_COLUMNS);
        }
        if (TableStorageModeEnum.SINGLE.code().equals(entity.getStorageMode())) {
            if (idxTableContainsColumns.size() != SINGLE_IDX_TABLE_NEED_COLUMNS.size()) {
                throw new ParamValidationException("单指标值表必须包含以下字段：" + DIM_TABLE_NEED_COLUMNS);
            }
        } else {
            if (idxTableContainsColumns.size() != MULTI_IDX_TABLE_NEED_COLUMNS.size()) {
                throw new ParamValidationException("多指标值表必须包含以下字段：" + MULTI_IDX_TABLE_NEED_COLUMNS);
            }
        }
    }

    private void verifyReq(TbTable entity) {
        if (!Common.verifyTbName(entity.getTbName()) || entity.getTbName().endsWith("_")) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "表名需以小写字母开头,非下划线结尾,支持数字字母下划线或连字符组合!");
        }

        if (TableType.IDX.code().equals(entity.getTbType())) {
            checkIdxTable(entity);

        } else {
            TbWarehouseLayer layer = jaxRepository.layer().selectByIdWithProjectAuth(entity.getLayerId(),
                    new BizException(ResponseCode.ID_NOT_EXISTS));
            entity.setLayerCatalog(LayerCatalog.fromString(layer.getCatalog()).code());
            if (ObjectUtil.isNull(entity.getLayerId()) || StrUtil.isBlank(entity.getTbAlias())
                    || StrUtil.isBlank(entity.getTbName())) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "必填项校验失败");
            }
            if (ObjectUtil.isNotNull(entity.getProcId())) {
                TbBusinessProcess proc = jaxRepository.businessProcessService().selectByIdWithProjectAuth(entity.getProcId(),
                        new BizException(ResponseCode.ID_NOT_EXISTS));
                entity.setDomId(proc.getDomId());
            }
            if (ObjectUtil.isNotNull(entity.getSubjId())) {
                TbDataSubject subject = jaxRepository.dataSubject().selectByIdWithProjectAuth(entity.getSubjId(),
                        new BizException(ResponseCode.ID_NOT_EXISTS));
                entity.setMartId(subject.getMartId());
            }
            if (ObjectUtil.isNotNull(entity.getMartId())) {
                TbDataMart mart = jaxRepository.dataMart().selectByIdWithProjectAuth(entity.getMartId(),
                        new BizException(ResponseCode.ID_NOT_EXISTS));
                entity.setBizId(mart.getBizId());
            }
            if (APPLICATION_DATA_SERVICE.equals(entity.getLayerCatalog())) {
                if (ObjectUtil.isNull(entity.getMartId()) && ObjectUtil.isNull(entity.getSubjId())) {
                    throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "集市/主题未选择");
                }
            } else {
                if (ObjectUtil.isNull(entity.getDomId())) {
                    throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "业务过程/数据域未选择");
                }
            }
        }
    }

    @SuppressWarnings("all")
    private void checkIdxTable(TbTable entity) {
        if (entity.getRelateDim() == null) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "relateDim字段必填");
        }
        if (entity.getRelateDim() > 0) {
            entity.setDimTbName("");
            if (entity.getDimTbId() == null) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "关联维度表时dimTbId字段必填");
            }
        } else {
            entity.setDimTbId(null);
        }
        if (ObjectUtil.isNull(entity.getLayerId()) || StrUtil.isBlank(entity.getTbAlias())
                || StrUtil.isBlank(entity.getTbName()) || entity.getDomId() == null || entity.getBizId() == null
                || StringUtils.isBlank(entity.getStorageMode())) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "必填项校验失败");
        }
        if (TableStorageModeEnum.fromString(entity.getStorageMode()) == null) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "存储模型不支持：" + entity.getStorageMode());
        }
        if (StringUtils.isBlank(entity.getPeriodIds()) || "[]".equals(entity.getPeriodIds())) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "时间周期不能为空");
        }
        // 值表和维度表名称必填。
        TbWarehouseLayer layer = jaxRepository.layer().selectByIdWithProjectAuth(entity.getLayerId(),
                new BizException(ResponseCode.ID_NOT_EXISTS));
        entity.setLayerCatalog(LayerCatalog.fromString(layer.getCatalog()).code());
        if (!LayerCatalog.COMMON_DATA_MODEL.code().equals(entity.getLayerCatalog())) {
            throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "只能选择公共层");
        }
        if (entity.getDimTbId() == null) {
            if (StringUtils.isBlank(entity.getIdxTbName()) || !Common.verifyTbName(entity.getIdxTbName()) ||
                    entity.getIdxTbName().endsWith("_")) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "指标值表名需以小写字母开头,非下划线结尾,支持数字字母下划线或连字符组合!");
            }
            if (StringUtils.isBlank(entity.getDimTbName()) || !Common.verifyTbName(entity.getDimTbName()) ||
                    entity.getDimTbName().endsWith("_")) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "指标维度表名需以小写字母开头,非下划线结尾,支持数字字母下划线或连字符组合!");
            }
            if (entity.getIdxTbName().equals(entity.getDimTbName())) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "指标表和维度表名称不能相同");
            }
            List<TbTable> tbTables = tbTableService.getByTableName(entity.getIdxTbName());
            if (tbTables != null && tbTables.size() > 0) {
                for (TbTable table : tbTables) {
                    if (table.getId().longValue() != entity.getId().longValue()) {
                        throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                                "指标值表名称[" + entity.getIdxTbName() + "]已存在于其他模型：" + table.getTbAlias());
                    }
                }
            }
            tbTables = tbTableService.getByTableName(entity.getDimTbName());
            if (tbTables != null && tbTables.size() > 0) {
                for (TbTable table : tbTables) {
                    if (table.getId().longValue() != entity.getId().longValue()) {
                        throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                                "指标维度表名称[" + entity.getDimTbName() + "]已存在于其他模型：" + table.getTbAlias());
                    }
                }
            }
        } else {
            if (StringUtils.isBlank(entity.getIdxTbName()) || !Common.verifyTbName(entity.getIdxTbName())
                    || entity.getIdxTbName().endsWith("_")) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "指标值表名需以小写字母开头,非下划线结尾,支持数字字母下划线或连字符组合!");
            }

            if (entity.getDimTbId().longValue() == entity.getId()) {
                throw new BizException(ResponseCode.FAILED.getCode(), "关联维度表不能是自己");
            }
            // 如果他被关联过，那么不允许修改
            if (entity.getId() != null) {
                List<TbTableDeploy> relateList = tbTableDeployService.getLastSuccessRelateDimTableDeploy(entity.getId());
                if (relateList != null && relateList.size() > 0) {
                    throw new BizException(ResponseCode.FAILED.getCode(), "模型表已被关联，不能更改关联模型");
                }
                List<TbTable> tbTableList = tbTableService.list(new LambdaQueryWrapper<TbTable>().eq(TbTable::getDimTbId, entity.getId()));
                if (tbTableList != null && tbTableList.size() > 0) {
                    throw new BizException(ResponseCode.FAILED.getCode(), "模型表已被关联，不能更改关联模型");
                }
            }

            TbTableDeploy t = tbTableDeployService.getLastSuccessTableDeploy(entity.getDimTbId());
            if (t == null) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                        "关联的维度表[" + entity.getDimTbId() + "]不存在或者未发布");
            }
            if (!TableType.IDX.code().equals(t.getTbType())) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                        "关联的维度表类型[" + t.getTbAlias() + "]必须是指标表");
            }
            if (t.getDimTbId() != null) {
                throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                        "关联的维度表[" + t.getTbAlias() + "]已关联其他维度表");
            }
            List<TbTable> tbTables = tbTableService.getByTableName(entity.getIdxTbName());
            if (tbTables != null && tbTables.size() > 0) {
                for (TbTable table : tbTables) {
                    if (table.getId().longValue() != entity.getId().longValue()) {
                        throw new BizException(ResponseCode.INVALID_PARAM.getCode(),
                                "指标值表名称[" + entity.getIdxTbName() + "]已存在于其他模型：" + table.getTbAlias());
                    }
                }
            }
        }

    }

    @Override
    public Paged<TableColumnResp> getRelateTable(ColumnQueryReq req) {
        IPage<TbColumn> paged = tbColumnService.pageWithProjectAuth(req.page(), req.query());
        List<Long> tbIds = paged.getRecords().stream().map(TbColumn::getTbId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tbIds)) {
            return new Paged<>();
        }
        Map<Long, TbTable> tableMap = jaxRepository.table()
                .selectListWithProjectAuth(new LambdaQueryWrapper<TbTable>().in(TbTable::getId, tbIds))
                .stream().distinct().collect(Collectors.toMap(TbTable::getId, table -> table, (k1, k2) -> k1));
        return new Paged<>(paged.getTotal(), paged.getRecords().stream()
                .map(x -> {
                    TableColumnResp tableColumnResp = new TableColumnResp().copyFrom(x);
                    tableColumnResp.setId(tableMap.get(x.getTbId()).getId());
                    tableColumnResp.setTbName(tableMap.get(x.getTbId()).getTbName());
                    tableColumnResp.setTbAlias(tableMap.get(x.getTbId()).getTbAlias());
                    return tableColumnResp;
                }).collect(Collectors.toList())
        );
    }

    @Override
    public boolean hasRelateDimTable(Long tbId) {
        List<TbTableDeploy> tbTableDeploys = tbTableDeployService.getLastSuccessRelateDimTableDeploy(tbId);
        return tbTableDeploys != null && tbTableDeploys.size() > 0;
    }

    @Override
    public void offlineTable(Long tbId) {
        TbTable entity = tbTableService.selectByIdWithProjectAuth(tbId);
        if (entity == null) {
            throw new BizException(ResponseCode.NOT_EXIST.getCode(), "模型表不存在");
        }
        // 如果没有已发布过，那么不允许下线
        List<TbTableDeploy> tableDeployList = tbTableDeployService.
                list(new LambdaQueryWrapper<TbTableDeploy>().eq(TbTableDeploy::getTbId, tbId));
        if (tableDeployList == null || tableDeployList.size() == 0) {
            throw new BizException(ResponseCode.FAILED.getCode(),
                    "模型表未发布，无法下线");
        }
        for (TbTableDeploy deploy : tableDeployList) {
            if (!TableDeployStatusEnum.SUCCESS.code().equals(deploy.getStatus())) {
                throw new BizException(ResponseCode.FAILED.getCode(),
                        "模型表存在未发布成功的发布记录");
            }
        }
        List<UsageResp> usageResps = offlineUsage(entity);
        if (usageResps.size() > 0) {
            throw new BizException(ResponseCode.OFFLINE_IN_USE);
        }
        tbTableService.update(new LambdaUpdateWrapper<TbTable>().set(TbTable::getIsOnline, 0).eq(TbTable::getId, tbId));
        // 更新所有发布版本的isOnline
        tbTableDeployService.update(new LambdaUpdateWrapper<TbTableDeploy>().set(TbTableDeploy::getIsOnline, 0).eq(TbTableDeploy::getTbId,
                tbId));
        ContextHolder.publishEvent(new TableEntityDeployEvent(TableDeployActionTypeEnum.DELETE, entity));
    }

    @Override
    public List<UsageResp> offlineUsage(Long tbId) {
        TbTable entity = tbTableService.selectByIdWithProjectAuth(tbId, new BizException(ResponseCode.ID_NOT_EXISTS));
        return offlineUsage(entity);
    }

    private List<UsageResp> offlineUsage(TbTable entity) {
        // 过滤掉发布的模型
        List<UsageResp> usageResps = usageByEntity(entity).stream()
                .filter(x -> !x.getRelatedObjType().endsWith("Deploy")).collect(Collectors.toList());

        return usageResps;
    }

    private void checkUniCode(TbTable entity) {
        List<TbTable> tbTables = tbTableService.getByTableName(entity.getTbName());
        if (tbTables != null && tbTables.size() > 0) {
            for (TbTable table : tbTables) {
                if (table.getId().longValue() != entity.getId().longValue()) {
                    throw new BizException(ResponseCode.INVALID_PARAM.getCode(), "表名称已存在与其他模型：" + table.getTbAlias());
                }
            }
        }
    }

    @Override
    public String sheetName() {
        return "维度建模";
    }

    @Override
    public List<TableExcelModel> example() {
        return new ArrayList<>();
    }

    @SuppressWarnings("all")
    @Override
    public void verifyData(List<TableExcelModel> importRows) {
        Map<String, Long> businessCatagoryMap = jaxRepository.businessCategory().selectListWithProjectAuth(null)
                .stream().collect(Collectors.toMap(TbBusinessCategory::getName, TbBusinessCategory::getId, (x, y) -> x));
        Map<String, Long> domainMap = jaxRepository.dataDomainService().selectListWithProjectAuth(null)
                .stream().collect(Collectors.toMap(TbDataDomain::getName, TbDataDomain::getId, (x, y) -> x));
        Map<String, TbWarehouseLayer> layerMap = jaxRepository.layer().selectListWithProjectAuth(null)
                .stream().collect(Collectors.toMap(TbWarehouseLayer::getName, x -> x, (x, y) -> x));
        Map<String, Long> martMap = jaxRepository.dataMart().selectListWithProjectAuth(null).stream()
                .collect(Collectors.toMap(TbDataMart::getName, TbDataMart::getId, (x, y) -> x));
        Map<String, Long> subjectMap = jaxRepository.dataSubject().selectListWithProjectAuth(null).stream()
                .collect(Collectors.toMap(TbDataSubject::getName, TbDataSubject::getId, (x, y) -> x));
        Map<String, Long> dimensionMap = jaxRepository.dimension().selectListWithProjectAuth(null).stream()
                .collect(Collectors.toMap(TbDimension::getName, TbDimension::getId, (x, y) -> x));
        Map<String, Long> procMap = jaxRepository.businessProcessService().selectListWithProjectAuth(null).stream()
                .collect(Collectors.toMap(TbBusinessProcess::getName, TbBusinessProcess::getId, (x, y) -> x));
        Map<String, Long> timePeriodMap = jaxRepository.timePeriodService().list().stream()
                .collect(Collectors.toMap(TbTimePeriod::getName, TbTimePeriod::getId, (x, y) -> x));
        Map<String, Long> dimTbMap = tbTableDeployService
                .getLastSuccessList(TableDeployPlatformEnum.CLICKHOUSE.code())
                .stream().filter(x -> TableType.IDX.code().equals(x.getTbType()))
                .filter(x -> x.getRelateDim() == null || x.getRelateDim() < 1)
                .collect(Collectors.toMap(TbTableDeploy::getTbName, TbTableDeploy::getTbId, (x, y) -> x));

        // 1.翻译为code
        importRows.forEach(row -> {
            TbWarehouseLayer layer = layerMap.get(row.getLayerName());
            TableType tableType = TableType.fromString(row.getTbType());
            if (ObjectUtil.isNull(layer) || ObjectUtil.isNull(tableType) ||
                    (!layer.getTbType().equals(tableType.code()) && !IDX.code().equals(row.getTbType()))) {
                row.setFailedReason("分层/表类型不正确");
                return;
            }
            row.setBizId(businessCatagoryMap.get(row.getBizName()));
            row.setDomId(domainMap.get(row.getDomName()));
            row.setLayerId(layer.getId());
            row.setLayerCatalog(layer.getCatalog());
            row.setMartId(martMap.get(row.getMartName()));
            row.setSubjId(subjectMap.get(row.getSubjName()));
            row.setDimId(dimensionMap.get(row.getDimName()));
            row.setProcId(procMap.get(row.getProcName()));
            if (StringUtils.isNotBlank(row.getPeriodNames())) {
                List<Long> periodIds = new LinkedList<>();
                row.setPeriodIds(periodIds);
                String[] names = row.getPeriodNames().split(",");
                for (String s : names) {
                    Long id = timePeriodMap.get(s);
                    if (id == null) {
                        row.setFailedReason("时间周期【" + s + "】不存在");
                        break;
                    }
                    periodIds.add(id);
                }
            }
            if (IDX.code().equals(row.getTbType())) {
                row.setDimTbId(dimTbMap.get(row.getRelateDimTbName()));
                List<ColumnCreateReq> columns = row.getColumns();
                if (columns == null) {
                    columns = new LinkedList<>();
                    row.setColumns(columns);
                }
                columns.addAll(getDefaultIdxValueColumns(row.getStorageMode()));
                if (row.getRelateDim() != null && !row.getRelateDim()) {
                    columns.addAll(getDefaultDimColumns());
                }
            }
            // 2.必填项校验
            verifyRow(row);
        });
        // 3.唯一项校验
        // 除维度之外
        Map<String, Long> tbNameInAuth = this.tbTableService.selectListWithProjectAuth(null).stream()
                .collect(Collectors.toMap(TbTable::getTbName, TbTable::getId, (x, y) -> y));
        // 权限校验
        List<String> nameNoAuth = this.tbTableService.list(new LambdaQueryWrapper<TbTable>().select(TbTable::getTbName))
                .stream().map(TbTable::getTbName).distinct().collect(Collectors.toList());
        nameNoAuth.removeAll(tbNameInAuth.keySet());
        importRows.stream().filter(x -> nameNoAuth.contains(x.getTbName()))
                .forEach(x -> x.setFailedReason("无权限更新"));
        importRows.stream().filter(x -> !DIMENSION.equals(x.getTbType()))
                .filter(x -> tbNameInAuth.containsKey(x.getTbName()))
                .forEach(row -> {
                    row.setId(tbNameInAuth.get(row.getTbName()));
                    row.setCoverField("tbName");
                });
        // 维度单独校验
        dimensionService.verifyData(importRows);
    }

    private void verifyRow(TableExcelModel row) {
        if (!Common.verifyTbName(row.getTbName()) || StrUtil.isBlank(row.getTbAlias())) {
            row.setFailedReason("表名未通过校验");
        }
        if (APPLICATION_DATA_SERVICE.equals(row.getLayerCatalog())) {
            if (ObjectUtil.isNull(row.getMartId()) && ObjectUtil.isNull(row.getSubjId())) {
                row.setFailedReason("数据集市/主题域未填");
            }
        }
        if (in(row.getTbType(), DWD, ODS) && ObjectUtil.isNull(row.getProcId())) {
            row.setFailedReason("业务过程未填");
        }
        if (COMMON_DATA_MODEL.equals(row.getLayerCatalog())
                && in(row.getTbType(), DWS, DIM, DIMENSION)
                && ObjectUtil.isNull(row.getDomId())) {
            row.setFailedReason("数据域未填");
        }
    }

    @Override
    public void dealWithSheetRelation(List<SheetResp> sheets) {
        SheetResp tableSheet = sheets.stream().filter(x -> x.getSheetName().equals(sheetName())).findAny().get();
        SheetResp columnSheet = sheets.stream().filter(x -> !x.getSheetName().equals(sheetName())).findAny().get();
        List<ColumnExcelModel> colRows = (List) columnSheet.getRows();
        List<TableExcelModel> tableRows = (List) tableSheet.getRows();
        List<String> successTableNames = tableRows.stream()
                .filter(AbstractExcelRowResp::isSuccess)
                .map(x -> x.getTbName()).collect(Collectors.toList());
        colRows.stream().filter(AbstractExcelRowResp::isSuccess)
                .filter(x -> !successTableNames.contains(x.getTbName()))
                .forEach(x -> x.setFailedReason("所属表不在导入范围、没权限或导入失败"));
        Map<String, List<ColumnExcelModel>> tableColsMap = colRows.stream()
                .filter(AbstractExcelRowResp::isSuccess)
                .collect(Collectors.groupingBy(ColumnExcelModel::getTbName));
        // 字段设置和表一起更新
        tableRows.stream().filter(AbstractExcelRowResp::isSuccess)
                .forEach(x -> x.setImportColumns(CollUtil.emptyIfNull(tableColsMap.get(x.getTbName()))));

    }

    @Override
    public void saveRows(Boolean skip, List<TableExcelModel> importRows) {
        dimensionService.saveRows(skip, importRows);
        saveRows(skip, importRows.stream().filter(x -> !DIMENSION.equals(x.getTbType()))
                        .collect(Collectors.toList()),
                (x -> {
                    TableCreateReq createReq = x.copyTo(new TableCreateReq());
                    List<ColumnCreateReq> columnCreateReqs = CollUtil.emptyIfNull(x.getImportColumns()).stream()
                            .map(y -> y.copyTo(new ColumnCreateReq())).collect(Collectors.toList());
                    columnCreateReqs.addAll(0, CollUtil.emptyIfNull(createReq.getColumns()));
                    createReq.setColumns(columnCreateReqs);
                    createReq.setTags(Common.split(StrUtil.nullToEmpty(x.getTags()), ","));
                    return createReq;
                }),
                (x -> {
                    TableUpdateReq updateReq = x.copyTo(new TableUpdateReq());
                    List<ColumnCreateReq> columnCreateReqs = CollUtil.emptyIfNull(x.getImportColumns()).stream()
                            .map(y -> y.copyTo(new ColumnCreateReq())).collect(Collectors.toList());
                    columnCreateReqs.addAll(0, CollUtil.emptyIfNull(updateReq.getColumns()));
                    updateReq.setColumns(columnCreateReqs);
                    updateReq.setTags(Common.split(StrUtil.nullToEmpty(x.getTags()), ","));
                    return updateReq;
                }));
        // 若表导入失败，字段也导入失败
        importRows.stream()
                .filter(x -> !x.isSuccess())
                .forEach(failedTb -> {
                    if (CollUtil.isNotEmpty(failedTb.getImportColumns())) {
                        for (ColumnExcelModel importColumn : failedTb.getImportColumns()) {
                            if (importColumn.isSuccess()) {
                                importColumn.setFailedReason("所属表导入失败");
                            }
                        }
                    }
                });

    }

    @Override
    public List<TableExcelModel> exportData(ImportExcelReq req) {
        List<VTable> vTables = jaxRepository.table().queryTables(null);
        VTableQueryParam queryParam = new VTableQueryParam();
        if (req.getIds() != null && req.getIds().size() > 0) {
            vTables = vTables.stream().filter(item -> req.getIds().contains(item.getId())).collect(Collectors.toList());
        } else if (StringUtils.isNoneEmpty(req.getCondition())) {
            TableQueryReq tableQueryReq = JSONUtil.toBean(req.getCondition(), TableQueryReq.class);
            BeanUtils.copyProperties(tableQueryReq.getFilter(), queryParam);
            if (tableQueryReq.getSort() != null) {
                TableSortReq sortReq = (TableSortReq) tableQueryReq.getSort();
                queryParam.setSortItems(sortReq.getCreateTime(), sortReq.getUpdateTime());
            }
            // 如果查询是维度表这个地方筛选才有意义，否则数据都不应该导出
            if (!tableQueryReq.getFilter().getDimension()) {
                Page<TableQueryResult> result2 = tbTableService.queryByCondition(
                        new Page<>(0, 10000), queryParam);
                List<Long> ids = result2.getRecords().stream().map(item -> item.getId()).collect(Collectors.toList());
                vTables = vTables.stream().filter(item -> ids.contains(item.getId())).collect(Collectors.toList());
            } else {
                vTables.clear();
            }
        }


        Map<Long, String> tableMap = vTables
                .stream().collect(Collectors.toMap(VTable::getId, VTable::getTbName, (x, y) -> x));
        Map<Long, String> timePeriodMap = jaxRepository.timePeriodService().list()
                .stream().collect(Collectors.toMap(TbTimePeriod::getId, TbTimePeriod::getName, (x, y) -> x));
        Map<Long, String> adjMap = jaxRepository.adj().list()
                .stream().collect(Collectors.toMap(TbAdjunct::getId, TbAdjunct::getName, (x, y) -> x));
        List<TableExcelModel> result = vTables.stream().map(tb -> {
            TableExcelModel excelModel = new TableExcelModel();
            BeanUtils.copyProperties(tb, excelModel);
            excelModel.setAdjIds(JsonUtil.decode(tb.getAdjIds(), new TypeReference<List<Long>>() {
            }));
            excelModel.setPeriodIds(JsonUtil.decode(tb.getPeriodIds(), new TypeReference<List<Long>>() {
            }));
            excelModel.setAdjNames(CollUtil.emptyIfNull(excelModel.getAdjIds()).stream()
                    .map(adjMap::get).collect(Collectors.joining(",")));
            excelModel.setPeriodNames(CollUtil.emptyIfNull(excelModel.getPeriodIds()).stream()
                    .map(timePeriodMap::get).collect(Collectors.joining(",")));
            if (IDX.code().equals(tb.getTbType())) {
                excelModel.setRelateDimTbName(tableMap.get(tb.getDimTbId()));
                excelModel.setRelateDim(tb.getRelateDim() != null && tb.getRelateDim() == 1 ? true : false);
            }
            return excelModel;
        }).collect(Collectors.toList());
        result.addAll(dimensionService.exportData(req));
        Map<Long, List<String>> recordTagMap = tagRelationService.getTagByRecordIdListAndRecordType(
                result.stream().map(TableExcelModel::getId).collect(Collectors.toList()),
                TB_TABLE);
        result.forEach(x -> x.setTags(String.join(",", recordTagMap.getOrDefault(x.getId(), Collections.EMPTY_LIST))));
        return result;
    }

    public static List<ColumnCreateReq> getDefaultIdxValueColumns(String storageMode) {
        List<ColumnCreateReq> columns = new LinkedList<>();
        ColumnCreateReq serialId = new ColumnCreateReq();
        serialId.setColType("BIGINT");
        serialId.setColName("__series_id");
        serialId.setColDisplay("指标唯一id");
        serialId.setColAction("default");
        serialId.setColUsage("IDX");
        serialId.setDescription("指标唯一id");
        serialId.setIsPrimaryKey(true);
        serialId.setIsNotNull(true);
        serialId.setColCatalog("DIMENSION");
        columns.add(serialId);

        ColumnCreateReq timestamp = new ColumnCreateReq();
        timestamp.setColType("DATETIME");
        timestamp.setColName("timestamp");
        timestamp.setColDisplay("指标时间");
        timestamp.setColAction("default");
        timestamp.setColUsage("IDX");
        timestamp.setDescription("指标时间");
        timestamp.setIsPrimaryKey(true);
        timestamp.setIsNotNull(true);
        timestamp.setColCatalog("DIMENSION");
        columns.add(timestamp);

        if (TableStorageModeEnum.SINGLE.code().equals(storageMode)) {
            ColumnCreateReq value = new ColumnCreateReq();
            value.setColType("DOUBLE");
            value.setColName("value");
            value.setColDisplay("指标值");
            value.setColAction("default");
            value.setColUsage("IDX");
            value.setDescription("指标值");
            value.setIsPrimaryKey(false);
            value.setIsNotNull(true);
            value.setColCatalog("DIMENSION");
            columns.add(value);
        }

        return columns;
    }

    public static List<ColumnCreateReq> getDefaultDimColumns() {
        List<ColumnCreateReq> columns = new LinkedList<>();
        ColumnCreateReq serialId = new ColumnCreateReq();
        serialId.setColType("BIGINT");
        serialId.setColName("__series_id");
        serialId.setColDisplay("指标唯一id");
        serialId.setColAction("default");
        serialId.setColUsage("DIM");
        serialId.setDescription("指标唯一id");
        serialId.setIsPrimaryKey(true);
        serialId.setIsNotNull(true);
        serialId.setColCatalog("DIMENSION");
        columns.add(serialId);

        ColumnCreateReq mgmtId = new ColumnCreateReq();
        mgmtId.setColType("BIGINT");
        mgmtId.setColName("__mgmt_id");
        mgmtId.setColDisplay("指标管理id");
        mgmtId.setColAction("default");
        mgmtId.setColUsage("DIM");
        mgmtId.setDescription("指标管理id");
        mgmtId.setIsPrimaryKey(false);
        mgmtId.setIsNotNull(true);
        mgmtId.setColCatalog("DIMENSION");
        columns.add(mgmtId);

        ColumnCreateReq labels = new ColumnCreateReq();
        labels.setColType("STRING");
        labels.setColName("labels");
        labels.setColDisplay("指标标签和标签值");
        labels.setColAction("default");
        labels.setColUsage("DIM");
        labels.setDescription("指标标签和标签值");
        labels.setIsPrimaryKey(false);
        labels.setIsNotNull(true);
        labels.setColCatalog("DIMENSION");
        columns.add(labels);

        ColumnCreateReq name = new ColumnCreateReq();
        name.setColType("STRING");
        name.setColName("__name__");
        name.setColDisplay("指标名称");
        name.setColAction("default");
        name.setColUsage("DIM");
        name.setDescription("指标名称");
        name.setIsPrimaryKey(false);
        name.setIsNotNull(true);
        name.setColCatalog("DIMENSION");
        columns.add(name);

        return columns;
    }

    @Override
    public Paged<TableResp> query(TableQueryReq req) {
        VTableQueryParam queryParam = new VTableQueryParam();
        BeanUtils.copyProperties(req.getFilter(), queryParam);

        TableSortReq sortReq = (TableSortReq) req.getSort();
        queryParam.setSortItems(sortReq.getCreateTime(), sortReq.getUpdateTime());
        Page<TableQueryResult> result = tbTableService.queryByCondition(
                new Page<>(req.page().getCurrent(), req.getSize()), queryParam);
        Map<Long, List<String>> recordTagMap = tagRelationService.getTagByRecordIdListAndRecordType(
                result.getRecords().stream().map(TableQueryResult::getId).collect(Collectors.toList()),
                TB_TABLE);
        return new Paged<>(result.getTotal(), result.getRecords().stream()
                .map(r -> {
                    TableResp resp = new TableResp();
                    ModelBeanUtil.copyBean(r, resp);
                    resp.setTags(recordTagMap.getOrDefault(resp.getId(), new ArrayList<>()));
                    return resp;
                }).collect(Collectors.toList())
        );
    }
}
