package com.eoi.jax.web.data.modeling.model.tabledeploy;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.common.enumrate.TableDeployStatusEnum;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/2
 */
public class TableDeployModelQueryFilterReq implements IFilterReq<TbTableDeploy> {

    @Schema(description = "网关Id, 通过网关绑定的数据源Id和模型使用的数据源Id进行关联")
    private Long cellId;

    @Schema(description = "数仓分层Id")
    private Long layerId;

    @NotBlank(message = "模型类型不能为空")
    @Schema(description = "模型类型， 取值为KAFKA, ELASTICSEARCH, CLICKHOUSE, MYSQL, HIVE", required = true)
    private String deployPlatform;

    @Schema(description = "模型发布状态, 取值为UNDEPLOYED, SUCCESS, FAILURE, DEPLOYING ,默认为'发布成功'状态")
    private String deployStatus = TableDeployStatusEnum.SUCCESS.code();

    @Schema(description = "分层类型，取值为DS, WH, APP")
    private String layerCatalog;

    @Schema(description = "查询关键字")
    private String searchKeyword;

    @Schema(description = "不包含的表类型")
    private List<String> neTbTypeList;

    @Schema(description = "发布的类型, TABLE, VIEW, TAG, EDGE")
    private List<String> deployTypeList;

    @Override
    public QueryWrapper<TbTableDeploy> where(QueryWrapper<TbTableDeploy> wrapper) {
        return wrapper;
    }


    public Long getCellId() {
        return cellId;
    }

    public void setCellId(Long cellId) {
        this.cellId = cellId;
    }

    public Long getLayerId() {
        return layerId;
    }

    public void setLayerId(Long layerId) {
        this.layerId = layerId;
    }

    public List<String> getDeployPlatformList() {
        if (StrUtil.isBlank(deployPlatform)) {
            return null;
        }
        return StrUtil.splitTrim(deployPlatform, ',');
    }

    public String getDeployPlatform() {
        return deployPlatform;
    }

    public void setDeployPlatform(String deployPlatform) {
        this.deployPlatform = deployPlatform;
    }

    public String getDeployStatus() {
        return deployStatus;
    }

    public void setDeployStatus(String deployStatus) {
        this.deployStatus = deployStatus;
    }

    public String getLayerCatalog() {
        return layerCatalog;
    }

    public void setLayerCatalog(String layerCatalog) {
        this.layerCatalog = layerCatalog;
    }

    public String getSearchKeyword() {
        return searchKeyword;
    }

    public void setSearchKeyword(String searchKeyword) {
        this.searchKeyword = searchKeyword;
    }

    public List<String> getNeTbTypeList() {
        return neTbTypeList;
    }

    public void setNeTbTypeList(List<String> neTbTypeList) {
        this.neTbTypeList = neTbTypeList;
    }

    public List<String> getDeployTypeList() {
        return deployTypeList;
    }

    public void setDeployTypeList(List<String> deployTypeList) {
        this.deployTypeList = deployTypeList;
    }
}
