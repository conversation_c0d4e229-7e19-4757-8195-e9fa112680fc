package com.eoi.jax.web.data.modeling.model.excel;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.data.modeling.model.comm.CommEntitySort;
import com.eoi.jax.web.repository.entity.TbImportHistory;

/**
 * @Author: yaru.ma
 * @Date: 2023/2/19
 */
public class ImportQuery extends BaseQueryReq<TbImportHistory> {
    private ImportFilterReq filter = new ImportFilterReq();
    private CommEntitySort<TbImportHistory> sort = new CommEntitySort();

    @Override
    public ImportFilterReq getFilter() {
        return filter;
    }

    public void setFilter(ImportFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public CommEntitySort<TbImportHistory> getSort() {
        return sort;
    }

    public void setSort(CommEntitySort<TbImportHistory> sort) {
        this.sort = sort;
    }
}
