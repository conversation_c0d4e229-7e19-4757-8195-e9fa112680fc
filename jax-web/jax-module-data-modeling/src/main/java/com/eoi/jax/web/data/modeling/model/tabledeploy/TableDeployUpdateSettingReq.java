package com.eoi.jax.web.data.modeling.model.tabledeploy;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class TableDeployUpdateSettingReq implements IUpdateModel<TbTableDeploy> {
    @Schema(description = "模型发布表id")
    private Long id;

    @Schema(description = "脚本")
    private String script;

    @Schema(description = "不同数据库独有配置，json字符串")
    private Map<String, Object> settingMap = new LinkedHashMap<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public Map<String, Object> getSettingMap() {
        return settingMap;
    }

    public void setSettingMap(Map<String, Object> settingMap) {
        this.settingMap = settingMap;
    }
}
