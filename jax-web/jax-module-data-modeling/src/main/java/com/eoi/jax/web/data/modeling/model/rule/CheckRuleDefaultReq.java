package com.eoi.jax.web.data.modeling.model.rule;

import io.swagger.v3.oas.annotations.media.Schema;

public class CheckRuleDefaultReq {

    @Schema(description = "分层ID")
    private Long layerId;

    @Schema(description = "规则类型：TABLE_NAME表名、METRIC_NAME中文名称、METRIC_CODE英文缩写")
    private String ruleType;

    @Schema(description = "规则id,为空说明置空")
    private Long ruleId;

    public Long getLayerId() {
        return layerId;
    }

    public void setLayerId(Long layerId) {
        this.layerId = layerId;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }
}
