package com.eoi.jax.web.data.modeling.model.tabledeploy;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbTableDeploy;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class TableDeployUpdateReq implements IUpdateModel<TbTableDeploy> {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "发布名称")
    @NotBlank(message = "发布名称不能为空")
    private String name;

    @Override
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
