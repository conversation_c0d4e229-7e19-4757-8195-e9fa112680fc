package com.eoi.jax.web.data.modeling.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.service.BaseService;
import com.eoi.jax.web.data.modeling.model.tabledeployhistory.TableDeployHistoryCreateReq;
import com.eoi.jax.web.data.modeling.model.tabledeployhistory.TableDeployHistoryQueryReq;
import com.eoi.jax.web.data.modeling.model.tabledeployhistory.TableDeployHistoryResp;
import com.eoi.jax.web.data.modeling.model.tabledeployhistory.TableDeployHistoryUpdateReq;
import com.eoi.jax.web.data.modeling.service.TableDeployHistoryService;
import com.eoi.jax.web.repository.entity.TbTableDeployHistory;
import com.eoi.jax.web.repository.service.TbTableDeployHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9
 */
@Service
public class TableDeployHistoryServiceImpl extends BaseService<
        TbTableDeployHistoryService,
        TbTableDeployHistory,
        TableDeployHistoryResp,
        TableDeployHistoryCreateReq,
        TableDeployHistoryUpdateReq,
        TableDeployHistoryQueryReq> implements TableDeployHistoryService {

    @Autowired
    private TbTableDeployHistoryService tbTableDeployHistoryService;

    public TableDeployHistoryServiceImpl(@Autowired TbTableDeployHistoryService daoService) {
        super(daoService);
    }


    @Override
    public List<TableDeployHistoryResp> getByTdId(Long tdId) {
        QueryWrapper<TbTableDeployHistory> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(TbTableDeployHistory::getTableDeployId, tdId);
        wrapper.lambda().eq(TbTableDeployHistory::getIsDeleted, 0);
        wrapper.lambda().orderByDesc(TbTableDeployHistory::getId);
        List<TbTableDeployHistory> list = tbTableDeployHistoryService.list(wrapper);
        List<TableDeployHistoryResp> respList = new LinkedList<>();
        if (list != null && list.size() > 0) {
            for (TbTableDeployHistory history : list) {
                respList.add(new TableDeployHistoryResp().fromEntity(history));
            }
        }
        return respList;
    }
}
