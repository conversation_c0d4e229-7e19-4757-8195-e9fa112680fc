package com.eoi.jax.web.data.modeling.model.object.column;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbObjectColumn;

/**
 * <p>
 * 对象模型字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public class ObjectColumnQuerySortReq implements ISortReq<TbObjectColumn> {


    /**
     * id
     */
    private String id;

    /**
     * 模型Id
     */
    private String objId;

    /**
     * 列序号
     */
    private String colNo;

    /**
     * 字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN
     */
    private String colType;

    /**
     * 字段名
     */
    private String colName;

    /**
     * 显示名
     */
    private String colDisplay;

    /**
     * 备注信息
     */
    private String description;

    /**
     * 是否为主键
     */
    private String isPrimaryKey;

    /**
     * 非空
     */
    private String isNotNull;

    /**
     * 字段类别，PROPERTY属性、DIMENSION维度、UNIT度量
     */
    private String colCatalog;

    /**
     * 度量单位
     */
    private String unitId;

    /**
     * 来源字段标准
     */
    private String dicId;

    /**
     * 关联标准代码
     */
    private String enumId;

    /**
     * 关键字段
     */
    private String isKeyField;

    /**
     * 标识字段
     */
    private String isIdentificationField;

    /**
     * 字段可允许的操作，default表示默认字段不能被删除。
     */
    private String colAction;

    /**
     * 逻辑删除标志位
     */
    private String isDeleted;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建人id
     */
    private String createUser;

    /**
     * 更新人id
     */
    private String updateUser;

    @Override
    public QueryWrapper<TbObjectColumn> order(QueryWrapper<TbObjectColumn> wrapper) {
        wrapper.lambda().orderBy(isOrder(id), isAsc(id), TbObjectColumn::getId);
        wrapper.lambda().orderBy(isOrder(objId), isAsc(objId), TbObjectColumn::getObjId);
        wrapper.lambda().orderBy(isOrder(colNo), isAsc(colNo), TbObjectColumn::getColNo);
        wrapper.lambda().orderBy(isOrder(colType), isAsc(colType), TbObjectColumn::getColType);
        wrapper.lambda().orderBy(isOrder(colName), isAsc(colName), TbObjectColumn::getColName);
        wrapper.lambda().orderBy(isOrder(colDisplay), isAsc(colDisplay), TbObjectColumn::getColDisplay);
        wrapper.lambda().orderBy(isOrder(description), isAsc(description), TbObjectColumn::getDescription);
        wrapper.lambda().orderBy(isOrder(isPrimaryKey), isAsc(isPrimaryKey), TbObjectColumn::getIsPrimaryKey);
        wrapper.lambda().orderBy(isOrder(isNotNull), isAsc(isNotNull), TbObjectColumn::getIsNotNull);
        wrapper.lambda().orderBy(isOrder(colCatalog), isAsc(colCatalog), TbObjectColumn::getColCatalog);
        wrapper.lambda().orderBy(isOrder(unitId), isAsc(unitId), TbObjectColumn::getUnitId);
        wrapper.lambda().orderBy(isOrder(dicId), isAsc(dicId), TbObjectColumn::getDicId);
        wrapper.lambda().orderBy(isOrder(enumId), isAsc(enumId), TbObjectColumn::getEnumId);
        wrapper.lambda().orderBy(isOrder(isKeyField), isAsc(isKeyField), TbObjectColumn::getIsKeyField);
        wrapper.lambda().orderBy(isOrder(isIdentificationField), isAsc(isIdentificationField), TbObjectColumn::getIsIdentificationField);
        wrapper.lambda().orderBy(isOrder(colAction), isAsc(colAction), TbObjectColumn::getColAction);
        wrapper.lambda().orderBy(isOrder(isDeleted), isAsc(isDeleted), TbObjectColumn::getIsDeleted);
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbObjectColumn::getCreateTime);
        wrapper.lambda().orderBy(isOrder(updateTime), isAsc(updateTime), TbObjectColumn::getUpdateTime);
        wrapper.lambda().orderBy(isOrder(createUser), isAsc(createUser), TbObjectColumn::getCreateUser);
        wrapper.lambda().orderBy(isOrder(updateUser), isAsc(updateUser), TbObjectColumn::getUpdateUser);
        return wrapper;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }

    public String getColNo() {
        return colNo;
    }

    public void setColNo(String colNo) {
        this.colNo = colNo;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIsPrimaryKey() {
        return isPrimaryKey;
    }

    public void setIsPrimaryKey(String isPrimaryKey) {
        this.isPrimaryKey = isPrimaryKey;
    }

    public String getIsNotNull() {
        return isNotNull;
    }

    public void setIsNotNull(String isNotNull) {
        this.isNotNull = isNotNull;
    }

    public String getColCatalog() {
        return colCatalog;
    }

    public void setColCatalog(String colCatalog) {
        this.colCatalog = colCatalog;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getDicId() {
        return dicId;
    }

    public void setDicId(String dicId) {
        this.dicId = dicId;
    }

    public String getEnumId() {
        return enumId;
    }

    public void setEnumId(String enumId) {
        this.enumId = enumId;
    }

    public String getIsKeyField() {
        return isKeyField;
    }

    public void setIsKeyField(String isKeyField) {
        this.isKeyField = isKeyField;
    }

    public String getIsIdentificationField() {
        return isIdentificationField;
    }

    public void setIsIdentificationField(String isIdentificationField) {
        this.isIdentificationField = isIdentificationField;
    }

    public String getColAction() {
        return colAction;
    }

    public void setColAction(String colAction) {
        this.colAction = colAction;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }


    @Override
    public String toString() {
        return "TbObjectColumn{" +
                "id=" + id +
                ", objId=" + objId +
                ", colNo=" + colNo +
                ", colType=" + colType +
                ", colName=" + colName +
                ", colDisplay=" + colDisplay +
                ", description=" + description +
                ", isPrimaryKey=" + isPrimaryKey +
                ", isNotNull=" + isNotNull +
                ", colCatalog=" + colCatalog +
                ", unitId=" + unitId +
                ", dicId=" + dicId +
                ", enumId=" + enumId +
                ", isKeyField=" + isKeyField +
                ", isIdentificationField=" + isIdentificationField +
                ", colAction=" + colAction +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                "}";
    }

}
