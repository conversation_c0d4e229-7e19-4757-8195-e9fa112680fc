package com.eoi.jax.web.data.modeling.model.metricset;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.repository.entity.TbMetricSet;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class MetricSetResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbMetricSet> {
    @Schema(description = "主键id")
    @OpPrimaryKey
    private Long id;

    @Schema(description = "数据中心名称")
    @OpPrimaryName
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "来源平台标识")
    private String sourcePlatformCode;

    @Schema(description = "来源平台元数据信息")
    private Map<String, Object> sourcePlatformMeta;

    @Schema(description = "对象模型id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long objId;

    @Schema(description = "对象模型名称")
    private String objName;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;

    @JsonSerialize(using = LongStringSerializer.class)
    @Schema(description = "对象分类id")
    private Long categoryId;

    private Long itemCount;

    private String objNameEn;

    private String objTbName;

    private String objTypeCode;

    @Override
    public MetricSetResp fromEntity(TbMetricSet tbMetricSet) {
        IRespModel.super.fromEntity(tbMetricSet);
        setSourcePlatformMeta(JsonUtil.decode2Map(tbMetricSet.getSourcePlatformMeta()));
        return this;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSourcePlatformCode() {
        return sourcePlatformCode;
    }

    public void setSourcePlatformCode(String sourcePlatformCode) {
        this.sourcePlatformCode = sourcePlatformCode;
    }

    public Map<String, Object> getSourcePlatformMeta() {
        return sourcePlatformMeta;
    }

    public void setSourcePlatformMeta(Map<String, Object> sourcePlatformMeta) {
        this.sourcePlatformMeta = sourcePlatformMeta;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public String getObjName() {
        return objName;
    }

    public void setObjName(String objName) {
        this.objName = objName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Long getItemCount() {
        return itemCount;
    }

    public void setItemCount(Long itemCount) {
        this.itemCount = itemCount;
    }

    public String getObjNameEn() {
        return objNameEn;
    }

    public void setObjNameEn(String objNameEn) {
        this.objNameEn = objNameEn;
    }

    public String getObjTbName() {
        return objTbName;
    }

    public void setObjTbName(String objTbName) {
        this.objTbName = objTbName;
    }

    public String getObjTypeCode() {
        return objTypeCode;
    }

    public void setObjTypeCode(String objTypeCode) {
        this.objTypeCode = objTypeCode;
    }
}
