package com.eoi.jax.web.data.modeling.model.measureunit;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbMeasureUnit;


public class MeasureUnitQueryReq extends BaseQueryReq<TbMeasureUnit> {

    private MeasureUnitFilterReq filter = new MeasureUnitFilterReq();
    private MeasureUnitSortReq sort = new MeasureUnitSortReq();

    @Override
    public MeasureUnitFilterReq getFilter() {
        return filter;
    }

    public void setFilter(MeasureUnitFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public MeasureUnitSortReq getSort() {
        return sort;
    }

    public void setSort(MeasureUnitSortReq sort) {
        this.sort = sort;
    }
}
