package com.eoi.jax.web.data.modeling.service;

import com.eoi.jax.web.core.service.IBaseProjectAuthService;
import com.eoi.jax.web.data.modeling.model.subject.DataSubjectCreateReq;
import com.eoi.jax.web.data.modeling.model.subject.DataSubjectQuery;
import com.eoi.jax.web.data.modeling.model.subject.DataSubjectResp;
import com.eoi.jax.web.data.modeling.model.subject.DataSubjectUpdateReq;
import com.eoi.jax.web.repository.entity.TbDataSubject;
import com.eoi.jax.web.repository.service.TbDataSubjectService;

import java.util.List;

/**
 * DomainLabelService
 *
 * <AUTHOR>
 * @date 2022/11/16 11:39
 */
public interface DataSubjectService extends IBaseProjectAuthService<
        TbDataSubjectService,
        TbDataSubject,
        DataSubjectResp,
        DataSubjectCreateReq,
        DataSubjectUpdateReq,
        DataSubjectQuery> {

    /**
     * 获取主题域分类树
     *
     * @return
     */
    List<DataSubjectResp> listTree();

    /**
     * 根据条件搜索主题域
     *
     * @param martId
     * @return
     */
    List<DataSubjectResp> listSubject(Long martId);

    /**
     * 查询树的所有根节点
     *
     * @return List
     */
    List<DataSubjectResp> listTreeRoot();

    /**
     * 指定指定节点的 子节点
     *
     * @param parentId
     * @return
     */
    List<DataSubjectResp> listTreeChild(Long parentId);
}
