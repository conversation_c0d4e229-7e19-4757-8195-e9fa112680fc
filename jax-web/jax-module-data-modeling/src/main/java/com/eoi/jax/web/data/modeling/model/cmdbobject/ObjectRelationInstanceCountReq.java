package com.eoi.jax.web.data.modeling.model.cmdbobject;

import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @date 2024/9/11
 */
public class ObjectRelationInstanceCountReq {

    @Schema(description = "类型, 蓝鲸(blueking)，优维")
    @NotBlank(message = "类型不能为空")
    private String sourceType;

    @Schema(description = "配置id")
    @NotNull(message = "配置id不能为空")
    private Long configId;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
