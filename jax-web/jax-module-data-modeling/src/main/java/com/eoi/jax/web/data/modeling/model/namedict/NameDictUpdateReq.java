package com.eoi.jax.web.data.modeling.model.namedict;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbNameDict;
import io.swagger.v3.oas.annotations.media.Schema;


public class NameDictUpdateReq implements IUpdateModel<TbNameDict> {

    @Schema(hidden = true)
    private Long id;

    @Schema(description = "英文缩写")
    private String code;

    @Schema(description = "中文名称")
    private String name;

    @Schema(description = "英文名称")
    private String nameEn;

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
}
