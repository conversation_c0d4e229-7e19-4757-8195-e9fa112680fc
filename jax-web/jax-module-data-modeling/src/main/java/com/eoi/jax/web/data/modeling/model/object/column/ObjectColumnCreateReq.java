package com.eoi.jax.web.data.modeling.model.object.column;

import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbObjectColumn;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 对象模型字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */

public class ObjectColumnCreateReq implements ICreateModel<TbObjectColumn> {

    /**
     * 列序号
     */
    @Schema(description = "列序号")
    private Integer colNo;

    /**
     * 字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN
     */
    @Schema(description = "字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN")
    private String colType;


    /**
     * 字段名
     */
    @Schema(description = "字段名")
    private String colName;


    /**
     * 显示名
     */
    @Schema(description = "显示名")
    private String colDisplay;

    /**
     * 表单类型
     */
    @Schema(description = "表单类型")
    private String formType;


    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String description;


    /**
     * 是否为主键
     */
    @Schema(description = "是否为主键")
    private Boolean isPrimaryKey;


    /**
     * 非空
     */
    @Schema(description = "非空")
    private Boolean isNotNull;


    /**
     * 字段类别，PROPERTY属性、DIMENSION维度、UNIT度量
     */
    @Schema(description = "字段类别，PROPERTY属性、DIMENSION维度、UNIT度量")
    private String colCatalog;


    /**
     * 度量单位
     */
    @Schema(description = "度量单位")
    private Long unitId;


    /**
     * 来源字段标准
     */
    @Schema(description = "来源字段标准")
    private Long dicId;


    /**
     * 关联标准代码
     */
    @Schema(description = "关联标准代码")
    private Long enumId;


    /**
     * 关键字段
     */
    @Schema(description = "关键字段")
    private Boolean isKeyField;


    /**
     * 标识字段
     */
    @Schema(description = "标识字段")
    private Boolean isIdentificationField;


    /**
     * 字段可允许的操作，default表示默认字段不能被删除。
     */
    @Schema(description = "字段可允许的操作，default表示默认字段不能被删除。")
    private String colAction;

    @Schema(description = "模型Id", hidden = true)
    @JsonSerialize(using = LongStringSerializer.class)
    private Long objId;

    public Integer getColNo() {
        return colNo;
    }

    public void setColNo(Integer colNo) {
        this.colNo = colNo;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay == null ? "" : colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsPrimaryKey() {
        return isPrimaryKey == null ? Boolean.FALSE : isPrimaryKey;
    }

    public void setIsPrimaryKey(Boolean isPrimaryKey) {
        this.isPrimaryKey = isPrimaryKey;
    }

    public Boolean getIsNotNull() {
        return isNotNull == null ? Boolean.FALSE : isNotNull;
    }

    public void setIsNotNull(Boolean isNotNull) {
        this.isNotNull = isNotNull;
    }

    public String getColCatalog() {
        return colCatalog;
    }

    public void setColCatalog(String colCatalog) {
        this.colCatalog = colCatalog;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public Long getEnumId() {
        return enumId;
    }

    public void setEnumId(Long enumId) {
        this.enumId = enumId;
    }

    public Boolean getIsKeyField() {
        return isKeyField == null ? Boolean.FALSE : isKeyField;
    }

    public void setIsKeyField(Boolean isKeyField) {
        this.isKeyField = isKeyField;
    }

    public Boolean getIsIdentificationField() {
        return isIdentificationField;
    }

    public void setIsIdentificationField(Boolean isIdentificationField) {
        this.isIdentificationField = isIdentificationField;
    }

    public String getColAction() {
        return colAction;
    }

    public void setColAction(String colAction) {
        this.colAction = colAction;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    @Override
    public String toString() {
        return "TbObjectColumn{" +
                ", colNo=" + colNo +
                ", colType=" + colType +
                ", colName=" + colName +
                ", colDisplay=" + colDisplay +
                ", description=" + description +
                ", isPrimaryKey=" + isPrimaryKey +
                ", isNotNull=" + isNotNull +
                ", colCatalog=" + colCatalog +
                ", unitId=" + unitId +
                ", dicId=" + dicId +
                ", enumId=" + enumId +
                ", isKeyField=" + isKeyField +
                ", isIdentificationField=" + isIdentificationField +
                ", colAction=" + colAction +
                "}";
    }

    @Override
    public TbObjectColumn toEntity() {
        TbObjectColumn entity = ICreateModel.super.toEntity(new TbObjectColumn());
        return entity;
    }
}
