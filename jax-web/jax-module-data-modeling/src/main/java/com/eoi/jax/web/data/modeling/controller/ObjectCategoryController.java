package com.eoi.jax.web.data.modeling.controller;

import com.eoi.jax.web.core.common.audit.*;
import com.eoi.jax.web.core.common.enumrate.ObjectCategoryTypeEnum;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.modeling.model.object.category.*;
import com.eoi.jax.web.data.modeling.service.ObjectCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * @Author: tangy
 * @Date: 2023/9/4
 * @Desc: 对象模型分类管理
 **/
@RestController
public class ObjectCategoryController implements V2Controller {

    @Resource
    private ObjectCategoryService objectCategoryService;

    @Operation(summary = "对象模型分页查询")
    @PostMapping("data-modeling/object-category/query")
    public Response query(@Valid @RequestBody ObjectCategoryQueryReq req) {
        return Response.success(objectCategoryService.query(req));
    }

    @Operation(summary = "根据对象模型分类id查询详情")
    @GetMapping("data-modeling/object-category/{id}")
    public Response<ObjectCategoryResp> get(@Parameter(description = "对象模型分类主键id", required = true)
                                            @PathVariable("id") Long id) {
        return Response.success(objectCategoryService.get(id));
    }


    @AuditLog(category = "维度建模", opAction = OpActionEnum.CREATE,
            module = "对象模型", function = "对象模型分类", code = "create")
    @Operation(summary = "对象模型分类创建")
    @PostMapping("data-modeling/object-category")
    public Response<ObjectCategoryResp> create(@OpPrimaryName(name = "name") @OpParameters
                                               @Valid @RequestBody ObjectCategoryCreateReq req) {
        if (req.getType() == null) {
            req.setType(ObjectCategoryTypeEnum.basic.code());
        }
        return Response.success(objectCategoryService.create(req));
    }


    @AuditLog(category = "对象模型管理", opAction = OpActionEnum.UPDATE,
            module = "对象模型", function = "对象模型分类", code = "update")
    @Operation(summary = "对象模型分类更新")
    @PutMapping("data-modeling/object-category/{id}")
    public Response<ObjectCategoryResp> update(
            @OpPrimaryKey @Parameter(description = "对象模型分类主键id", required = true) @PathVariable("id") Long id,
            @OpParameters @Valid @RequestBody ObjectCategoryUpdateReq req) {
        req.setId(id);
        return Response.success(objectCategoryService.update(req));
    }


    @AuditLog(category = "维度建模", opAction = OpActionEnum.DELETE,
            module = "对象模型", function = "对象模型分类", code = "delete")
    @Operation(summary = "对象模型分类删除")
    @DeleteMapping("data-modeling/object-category/{id}")
    public Response<ObjectCategoryResp> delete(
            @OpPrimaryKey(name = "id")
            @Parameter(description = "对象模型分类主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(objectCategoryService.delete(id));
    }

    /**
     * @return
     */
    @Operation(summary = "获取对象模型分类列表以及分组下的对象模型分类(只统计分类/分组)")
    @PostMapping("data-modeling/object-category/tree")
    public Response<List<ObjectCategoryTree>> apiTreeQuery(@RequestBody(required = false) ObjectCategoryTreeFilter categoryFilter) {
        return Response.success(objectCategoryService.treeList(categoryFilter));
    }

    @AuditLog(category = "维度建模", opAction = OpActionEnum.MOVE,
            module = "对象模型", function = "对象模型分类", code = "move")
    @Operation(summary = "对象模型分类移动")
    @PutMapping("data-modeling/object-category/{id}/move")
    public Response move(
            @OpPrimaryKey(name = "id")
            @Parameter(description = "对象模型分类主键id", required = true) @PathVariable("id") Long id,
            @RequestBody ObjectCategoryMoveReq moveReq) {
        if ("OBJECT".equals(moveReq.getType())) {
            objectCategoryService.moveObject(id, moveReq);
        } else {
            objectCategoryService.move(id, moveReq);
        }
        return Response.success("移动数据成功");
    }
}
