package com.eoi.jax.web.data.modeling.model.columndeploy;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbColumnDeploy;
import io.swagger.v3.oas.annotations.media.Schema;
/**
 * @Author: zsc
 * @Date: 2022/01/03
 */
public class ColumnDeployCreateReq implements ICreateModel<TbColumnDeploy> {

    @Schema(description = "数据模型发布表id")
    private Long tableDeployId;

    @Schema(description = "字段id")
    private Long colId;

    @Schema(description = "字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN")
    private String colType;

    @Schema(description = "字段名")
    private String colName;

    @Schema(description = "显示名")
    private String colDisplay;

    @Schema(description = "表单类型")
    private String formType;

    @Schema(description = "字段用途：DIM 维度表字段，IDX 指标值表字段")
    private String colUsage;

    @Schema(description = "字段描述")
    private String description;

    @Schema(description = "是否主键")
    private Boolean isPrimaryKey;

    @Schema(description = "是否不为空")
    private Boolean isNotNull;

    @Schema(description = "字段标准id")
    private Long dicId;

    @Schema(description = "字段标准配置，json字符串")
    private String dicSetting;

    @Override
    public TbColumnDeploy toEntity() {
        return toEntity(new TbColumnDeploy());
    }

    public Long getTableDeployId() {
        return tableDeployId;
    }

    public void setTableDeployId(Long tableDeployId) {
        this.tableDeployId = tableDeployId;
    }

    public Long getColId() {
        return colId;
    }

    public String getColUsage() {
        return colUsage;
    }

    public void setColUsage(String colUsage) {
        this.colUsage = colUsage;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsPrimaryKey() {
        return isPrimaryKey;
    }

    public void setIsPrimaryKey(Boolean primaryKey) {
        isPrimaryKey = primaryKey;
    }

    public Boolean getIsNotNull() {
        return isNotNull;
    }

    public void setIsNotNull(Boolean notNull) {
        isNotNull = notNull;
    }

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public String getDicSetting() {
        return dicSetting;
    }

    public void setDicSetting(String dicSetting) {
        this.dicSetting = dicSetting;
    }
}
