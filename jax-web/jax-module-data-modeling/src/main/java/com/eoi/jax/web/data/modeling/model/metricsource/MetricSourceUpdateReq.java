package com.eoi.jax.web.data.modeling.model.metricsource;

import cn.hutool.core.map.MapUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbMetricSource;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class MetricSourceUpdateReq implements IUpdateModel<TbMetricSource> {


    @Schema(description = "id")
    private Long id;

    /**
     * 来源名称
     */
    @NotBlank(message = "来源名称不能为空")
    @Schema(description = "来源名称")
    private String name;

    /**
     * 来源类型，zabbix，蓝鲸
     */
    @NotBlank(message = "来源名称不能为空")
    @Schema(description = "来源类型")
    private String type;

    /**
     * 配置(JSON)
     */
    @Schema(description = "配置(JSON)")
    private Map<String, Object> config;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    @Override
    public TbMetricSource toEntity(TbMetricSource tbMetricSource) {
        TbMetricSource entity = IUpdateModel.super.toEntity(tbMetricSource);
        if (MapUtil.isNotEmpty(this.getConfig())) {
            entity.setConfig(JsonUtil.encode(this.getConfig()));
        }
        return entity;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
