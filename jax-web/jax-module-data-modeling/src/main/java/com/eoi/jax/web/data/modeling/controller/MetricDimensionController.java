package com.eoi.jax.web.data.modeling.controller;

import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.modeling.model.metricdimension.MetricDimensionCreateReq;
import com.eoi.jax.web.data.modeling.model.metricdimension.MetricDimensionQueryReq;
import com.eoi.jax.web.data.modeling.model.metricdimension.MetricDimensionUpdateReq;
import com.eoi.jax.web.data.modeling.service.MetricDimensionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 * @Desc: 指标项
 **/
@RestController
public class MetricDimensionController implements V2Controller {

    @Autowired
    private MetricDimensionService metricDimensionService;

    @Operation(summary = "指标维度全部查询")
    @GetMapping("data-modeling/metric/dimension/list")
    public Response list() {
        return Response.success(metricDimensionService.all());
    }

    @Operation(summary = "指标维度分页查询")
    @PostMapping("data-modeling/metric/dimension/query")
    public Response query(@RequestBody MetricDimensionQueryReq req) {
        return Response.success(metricDimensionService.query(req));
    }

    @Operation(summary = "根据id查询详情")
    @GetMapping("data-modeling/metric/dimension/{id}")
    public Response get(@Parameter(description = "指标维度id", required = true) @PathVariable("id") Long id) {
        return Response.success(metricDimensionService.get(id));
    }

    @Operation(summary = "指标维度创建")
    @PostMapping("data-modeling/metric/dimension")
    @AuditLog(category = "指标维度管理", opAction = OpActionEnum.CREATE, module = "指标维度管理", function = "指标维度管理", code = "metricDimension")
    public Response create(@Valid @RequestBody MetricDimensionCreateReq req) {
        return Response.success(metricDimensionService.create(req));
    }

    @Operation(summary = "指标维度更新")
    @PutMapping("data-modeling/metric/dimension/{id}")
    @AuditLog(category = "指标维度管理", opAction = OpActionEnum.UPDATE, module = "指标维度管理", function = "指标维度管理", code = "metricDimension")
    public Response update(@OpPrimaryKey(name = "id") @Parameter(description = "指标维度主键id", required = true) @PathVariable("id") Long id,
                           @Valid @RequestBody MetricDimensionUpdateReq req) {
        req.setId(id);
        return Response.success(metricDimensionService.update(req));
    }

    @Operation(summary = "指标维度删除")
    @DeleteMapping("data-modeling/metric/dimension/{id}")
    @AuditLog(category = "指标维度管理", opAction = OpActionEnum.DELETE, module = "指标维度管理", function = "指标维度管理", code = "metricDimension")
    public Response delete(@OpPrimaryKey(name = "id") @Parameter(description = "指标维度主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(metricDimensionService.delete(id));
    }

    @Operation(summary = "指标维度查询关联数据")
    @GetMapping("data-modeling/metric/dimension/{id}/usage")
    public Response usage(@Parameter(description = "指标维度主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(metricDimensionService.usage(id));
    }
}
