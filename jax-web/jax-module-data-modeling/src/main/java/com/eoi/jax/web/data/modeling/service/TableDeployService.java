package com.eoi.jax.web.data.modeling.service;

import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.modeling.model.tabledeploy.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public interface TableDeployService {

    /**
     * 快速创建模型表并发布到对应数据源
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    TableDeployResp fastCreateAndDeploy(TableDeployFastCreateReq req);

    /**
     * 分页查询
     *
     * @param req
     * @return
     */
    Paged<TableDeployResp> query(TableDeployQueryReq req);

    /**
     * 查询所有发布项
     *
     * @param req
     * @return
     */
    List<TableDeployResp> all(TableDeployQueryAllReq req);

    /**
     * 根据tableId查询所有发布项
     *
     * @param tableId
     * @return
     */
    List<TableDeployResp> queryByTableId(Long tableId);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    TableDeployResp get(Long id);


    /**
     * 创建
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    TableDeployResp create(TableDeployCreateReq req);

    /**
     * 先删除后创建
     *
     * @param tableId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    TableDeployResp recreate(Long tableId);

    /**
     * 判断是否可以创建
     *
     * @param req
     * @return
     */
    boolean preCreateCheck(TableDeployPreCreateCheckReq req);

    /**
     * 更新
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    TableDeployResp update(TableDeployUpdateReq req);

    /**
     * 只更新个别配置
     *
     * @param req
     * @param targetStatus
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    TableDeployResp updateSetting(TableDeployUpdateSettingReq req, String targetStatus);

    /**
     * 导入使用
     * 创建一阶段发布并更新sql或settings
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    void createDeployAndUpdateSettings(TableDeployCreateUpdateSettingReq req);


    /**
     * 获取最后一条发布成功的记录
     *
     * @param tbId
     * @return
     */
    TableDeployResp getLastSuccessTableDeploy(long tbId);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    TableDeployResp delete(Long id);

    /**
     * 全量查询
     *
     * @return
     */
    List<TableDeployResp> all();

    /**
     * 发布脚本
     *
     * @param req
     * @return
     */
    TableDeployResp deployScript(TableDeployScriptReq req);


    /**
     * 获取当前模型表运行执行的动作类型
     *
     * @param req
     * @return
     */
    List<String> getAllowDeployActions(AllowDeployActionsReq req);

    /**
     * 根据cellId查询发布的kafka模型
     *
     * @param req
     * @return
     */
    @Deprecated
    Paged<TableDeployResp> queryByCondition(TableDeployIngestionQueryReq req);

    /**
     * 根据条件查询模型
     *
     * @param req
     * @return
     */
    Paged<TableDeployResp> queryByCondition(TableDeployModelQueryReq req);

    /**
     * 模型表发布的历史版本
     *
     * @param tbId
     * @return
     */
    List<TableDeployResp> queryModelHistory(Long tbId);


    /**
     * 获取最新发布成功的模型列表
     *
     * @param platform
     * @return
     */
    List<TableDeployResp> getLastSuccessList(String platform);

    /**
     * 获取最新发布成功的模型列表
     *
     * @param platform
     * @param tbType
     * @param distinct true的时候只返回非关联维度的指标表，false时返回所有指标表
     * @return
     */
    List<TableDeployResp> getLastSuccessIdxList(String platform, String tbType, boolean distinct);

    /**
     * 旧数据迁移到业务流程树
     *
     * @param refresh
     */
    void oldDataMigration(boolean refresh);
}
