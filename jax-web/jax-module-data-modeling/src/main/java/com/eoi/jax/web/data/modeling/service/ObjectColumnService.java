package com.eoi.jax.web.data.modeling.service;

import com.eoi.jax.web.core.excel.IBaseExcelService;
import com.eoi.jax.web.core.model.Paged;
import com.eoi.jax.web.data.modeling.model.excel.ObjectColumnExcelModel;
import com.eoi.jax.web.data.modeling.model.object.column.ObjectColumnCreateReq;
import com.eoi.jax.web.data.modeling.model.object.column.ObjectColumnQueryReq;
import com.eoi.jax.web.data.modeling.model.object.column.ObjectColumnResp;
import com.eoi.jax.web.data.modeling.model.object.column.ObjectColumnUpdateReq;
import com.eoi.jax.web.repository.entity.TbObjectColumn;
import com.eoi.jax.web.repository.service.TbObjectColumnService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-05
 */
public interface ObjectColumnService extends IBaseExcelService<
        TbObjectColumnService,
        TbObjectColumn,
        ObjectColumnResp,
        ObjectColumnCreateReq,
        ObjectColumnUpdateReq,
        ObjectColumnQueryReq,
        ObjectColumnExcelModel> {

    /**
     * 分页查询
     *
     * @param req
     * @return
     */
    Paged<ObjectColumnResp> query(ObjectColumnQueryReq req);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    ObjectColumnResp get(Long id);

    /**
     * 根据id列表查询
     * @param idList
     * @return
     */
    List<ObjectColumnResp> listById(List<Long> idList);

    /**
     * 创建
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    ObjectColumnResp create(ObjectColumnCreateReq req);


    /**
     * 更新
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    ObjectColumnResp update(ObjectColumnUpdateReq req);


    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    ObjectColumnResp delete(Long id);

    /**
     * 全量查询
     *
     * @return
     */
    List<ObjectColumnResp> all();

    /**
     * 批量删除
     *
     * @param oldColumnIds
     */
    void removeBatchByIds(List<Long> oldColumnIds);

    /**
     * 批量新增或更新
     *
     * @param objectColumns
     */
    void saveOrUpdateBatch(List<TbObjectColumn> objectColumns);

    /**
     * 根据模型id获取列
     *
     * @param objectTableId
     * @return
     */
    List<ObjectColumnResp> listByObjectTableId(Long objectTableId);

    /**
     * 根据对象模型Id删除列
     * @param objectTableId
     */
    void deleteObjectTable(Long objectTableId);
}
