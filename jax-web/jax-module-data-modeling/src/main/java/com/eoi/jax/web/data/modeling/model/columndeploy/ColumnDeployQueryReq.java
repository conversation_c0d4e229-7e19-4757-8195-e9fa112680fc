package com.eoi.jax.web.data.modeling.model.columndeploy;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbColumnDeploy;

import jakarta.validation.Valid;

/**
 * @Author: zsc
 * @Date: 2022/01/03
 */
public class ColumnDeployQueryReq extends BaseQueryReq<TbColumnDeploy> {

    @Valid
    private ColumnDeployFilterReq filter = new ColumnDeployFilterReq();
    private ColumnDeploySortReq sort = new ColumnDeploySortReq();

    @Override
    public ColumnDeployFilterReq getFilter() {
        return filter;
    }

    public void setFilter(ColumnDeployFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public ColumnDeploySortReq getSort() {
        return sort;
    }

    public void setSort(ColumnDeploySortReq sort) {
        this.sort = sort;
    }
}
