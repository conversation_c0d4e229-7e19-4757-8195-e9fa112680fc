package com.eoi.jax.web.data.modeling.model.rule;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IModel;
import com.eoi.jax.web.repository.entity.TbDerivativeIndicator;
import com.eoi.jax.web.repository.entity.TbTable;
import com.eoi.jax.web.repository.search.result.VDerivativeIndicator;
import com.eoi.jax.web.repository.search.result.VTable;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.List;

/**
 * @Author: yaru.ma
 * @Date: 2022/12/5
 */
public class RuleRunReq implements IModel {

    // 指标名称相关
    private Long periodId;
    private List<Long> adjIds;
    private Long atomicId;
    // 模型名称相关
    private Long bizId;
    private Long domId;
    private Long procId;
    private Long martId;
    private Long subjId;
    private Long dimId;
    private String storagePolicy;

    private String periodCode;
    private String periodName;
    private String adjName;
    private String adjCode;
    private String atomicCode;
    private String atomicName;
    private String bizCode;
    private String domCode;
    private String procCode;
    private String martCode;
    private String subjCode;
    private String dimCode;

    public RuleRunReq(Object o) {
        IModel.super.copyFrom(o);
        if (o instanceof TbDerivativeIndicator) {
            setAdjIds(JsonUtil.decode(((TbDerivativeIndicator) o).getAdjIds(), new TypeReference<List<Long>>() {
            }));
        } else if (o instanceof VDerivativeIndicator) {
            setAdjIds(JsonUtil.decode(((VDerivativeIndicator) o).getAdjIds(), new TypeReference<List<Long>>() {
            }));
        } else if (o instanceof VTable) {
            setAdjIds(JsonUtil.decode(((VTable) o).getAdjIds(), new TypeReference<List<Long>>() {
            }));
        } else if (o instanceof TbTable) {
            setAdjIds(JsonUtil.decode(((TbTable) o).getAdjIds(), new TypeReference<List<Long>>() {
            }));
        }
    }

    public String getPeriodCode() {
        return periodCode;
    }

    public void setPeriodCode(String periodCode) {
        this.periodCode = periodCode;
    }

    public String getPeriodName() {
        return periodName;
    }

    public void setPeriodName(String periodName) {
        this.periodName = periodName;
    }

    public String getAtomicCode() {
        return atomicCode;
    }

    public void setAtomicCode(String atomicCode) {
        this.atomicCode = atomicCode;
    }

    public String getAtomicName() {
        return atomicName;
    }

    public void setAtomicName(String atomicName) {
        this.atomicName = atomicName;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public List<Long> getAdjIds() {
        return adjIds;
    }

    public void setAdjIds(List<Long> adjIds) {
        this.adjIds = adjIds;
    }

    public Long getAtomicId() {
        return atomicId;
    }

    public void setAtomicId(Long atomicId) {
        this.atomicId = atomicId;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Long getDomId() {
        return domId;
    }

    public void setDomId(Long domId) {
        this.domId = domId;
    }

    public Long getProcId() {
        return procId;
    }

    public void setProcId(Long procId) {
        this.procId = procId;
    }

    public Long getMartId() {
        return martId;
    }

    public void setMartId(Long martId) {
        this.martId = martId;
    }

    public Long getSubjId() {
        return subjId;
    }

    public void setSubjId(Long subjId) {
        this.subjId = subjId;
    }

    public Long getDimId() {
        return dimId;
    }

    public void setDimId(Long dimId) {
        this.dimId = dimId;
    }

    public String getStoragePolicy() {
        return storagePolicy;
    }

    public void setStoragePolicy(String storagePolicy) {
        this.storagePolicy = storagePolicy;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getAdjName() {
        return adjName;
    }

    public void setAdjName(String adjName) {
        this.adjName = adjName;
    }

    public String getAdjCode() {
        return adjCode;
    }

    public void setAdjCode(String adjCode) {
        this.adjCode = adjCode;
    }

    public String getDomCode() {
        return domCode;
    }

    public void setDomCode(String domCode) {
        this.domCode = domCode;
    }

    public String getProcCode() {
        return procCode;
    }

    public void setProcCode(String procCode) {
        this.procCode = procCode;
    }

    public String getMartCode() {
        return martCode;
    }

    public void setMartCode(String martCode) {
        this.martCode = martCode;
    }

    public String getSubjCode() {
        return subjCode;
    }

    public void setSubjCode(String subjCode) {
        this.subjCode = subjCode;
    }

    public String getDimCode() {
        return dimCode;
    }

    public void setDimCode(String dimCode) {
        this.dimCode = dimCode;
    }


}
