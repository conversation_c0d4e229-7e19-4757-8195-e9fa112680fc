package com.eoi.jax.web.data.modeling.model.dimension;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbDimension;


public class DimensionQueryReq extends BaseQueryReq<TbDimension> {

    private DimensionFilterReq filter = new DimensionFilterReq();
    private DimensionSortReq sort = new DimensionSortReq();

    @Override
    public DimensionFilterReq getFilter() {
        return filter;
    }

    public void setFilter(DimensionFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public DimensionSortReq getSort() {
        return sort;
    }

    public void setSort(DimensionSortReq sort) {
        this.sort = sort;
    }
}
