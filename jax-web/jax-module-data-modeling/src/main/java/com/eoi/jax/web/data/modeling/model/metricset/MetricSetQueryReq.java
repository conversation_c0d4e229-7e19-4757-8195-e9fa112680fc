package com.eoi.jax.web.data.modeling.model.metricset;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbMetricSet;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class MetricSetQueryReq extends BaseQueryReq<TbMetricSet> {

    private MetricSetQueryFilterReq filter = new MetricSetQueryFilterReq();
    private MetricSetQuerySortReq sort = new MetricSetQuerySortReq();

    @Override
    public IFilterReq<TbMetricSet> getFilter() {
        return filter;
    }

    @Override
    public ISortReq<TbMetricSet> getSort() {
        return sort;
    }
}
