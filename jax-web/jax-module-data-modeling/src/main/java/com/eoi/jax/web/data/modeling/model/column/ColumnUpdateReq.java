package com.eoi.jax.web.data.modeling.model.column;

import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbColumn;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @Author: yaru.ma
 * @Date: 2022/11/29
 */
public class ColumnUpdateReq implements IUpdateModel<TbColumn> {

    private Long id;

    @Schema(description = "表类型，ODS贴源表、DIM维度表、DWD明细表、DWS汇总表、ADS应用表，来自所属表")
    private String tbType;

    @Schema(description = "字段类型，TIMESTAMP、BIGINT、DOUBLE、DECIMAL、STRING、DATETIME、DATE、INT、BOOLEAN")
    private String colType;

    @Schema(description = "字段名")
    private String colName;

    @Schema(description = "字段用途：DIM 维度表字段，IDX 指标值表字段")
    private String colUsage;

    @Schema(description = "显示名")
    private String colDisplay;

    @Schema(description = "表单类型")
    private String formType;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "是否主键")
    private Boolean isPrimaryKey;

    @Schema(description = "是否不为空")
    private Boolean isNotNull;

    @Schema(description = "字段类型")
    private String colCatalog;

    @Schema(description = "字段允许的操作，前端可通过这个字段来渲染是否允许删除等操作")
    private String colAction;

    @Schema(description = "度量单位id")
    private Long unitId;

    @Schema(description = "字段标准id")
    private Long dicId;

    @Schema(description = "标准代码id")
    private Long enumId;

    @Schema(description = "关联指标类型：ATOMIC、DERIV、DIM")
    private String indicatorType;

    @Schema(description = "时间周期id")
    private Long periodId;

    @Schema(description = "修饰词id")
    private List<Long> adjIds;

    @Schema(description = "原子指标")
    private Long atomicId;

    @Schema(description = "派生指标id")
    private Long dervId;

    @Schema(description = "维度表id")
    private Long dimTbId;

    @Schema(description = "维度表字段id")
    private Long dimColId;

    @Override
    public TbColumn toEntity(TbColumn tbColumn) {
        IUpdateModel.super.toEntity(tbColumn);
        tbColumn.setAdjIds(JsonUtil.encode(adjIds));
        return tbColumn;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTbType() {
        return tbType;
    }

    public void setTbType(String tbType) {
        this.tbType = tbType;
    }

    public String getColType() {
        return colType;
    }

    public String getColUsage() {
        return colUsage;
    }

    public void setColUsage(String colUsage) {
        this.colUsage = colUsage;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColAction() {
        return colAction;
    }

    public void setColAction(String colAction) {
        this.colAction = colAction;
    }

    public String getColDisplay() {
        return colDisplay;
    }

    public void setColDisplay(String colDisplay) {
        this.colDisplay = colDisplay;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsPrimaryKey() {
        return isPrimaryKey;
    }

    public void setIsPrimaryKey(Boolean primaryKey) {
        this.isPrimaryKey = primaryKey;
    }

    public Boolean getIsNotNull() {
        return isNotNull;
    }

    public void setIsNotNull(Boolean notNull) {
        this.isNotNull = notNull;
    }

    public String getColCatalog() {
        return colCatalog;
    }

    public void setColCatalog(String colCatalog) {
        this.colCatalog = colCatalog;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getDicId() {
        return dicId;
    }

    public void setDicId(Long dicId) {
        this.dicId = dicId;
    }

    public Long getEnumId() {
        return enumId;
    }

    public void setEnumId(Long enumId) {
        this.enumId = enumId;
    }

    public String getIndicatorType() {
        return indicatorType;
    }

    public void setIndicatorType(String indicatorType) {
        this.indicatorType = indicatorType;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getAtomicId() {
        return atomicId;
    }

    public void setAtomicId(Long atomicId) {
        this.atomicId = atomicId;
    }

    public Long getDervId() {
        return dervId;
    }

    public void setDervId(Long dervId) {
        this.dervId = dervId;
    }

    public List<Long> getAdjIds() {
        return adjIds;
    }

    public void setAdjIds(List<Long> adjIds) {
        this.adjIds = adjIds;
    }

    public Long getDimTbId() {
        return dimTbId;
    }

    public void setDimTbId(Long dimTbId) {
        this.dimTbId = dimTbId;
    }

    public Long getDimColId() {
        return dimColId;
    }

    public void setDimColId(Long dimColId) {
        this.dimColId = dimColId;
    }
}
