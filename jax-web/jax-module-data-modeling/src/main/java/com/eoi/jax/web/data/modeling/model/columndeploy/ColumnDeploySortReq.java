package com.eoi.jax.web.data.modeling.model.columndeploy;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbColumnDeploy;

/**
 * @Author: zsc
 * @Date: 2022/01/03
 */
public class ColumnDeploySortReq implements ISortReq<TbColumnDeploy> {

    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbColumnDeploy> order(QueryWrapper<TbColumnDeploy> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbColumnDeploy::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbColumnDeploy::getUpdateTime);
        return wrapper;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
