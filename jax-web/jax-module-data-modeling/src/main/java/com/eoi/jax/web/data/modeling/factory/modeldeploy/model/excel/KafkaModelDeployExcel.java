package com.eoi.jax.web.data.modeling.factory.modeldeploy.model.excel;

import com.eoi.jax.web.core.excel.Excel;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * @Author: yaru.ma
 * @Date: 2024/5/21
 */
public class KafkaModelDeployExcel extends ModelDeployExcel {

    private String topicName;

    @Excel(name = "分区数", no = 11)
    @NotNull(message = "分区数不能为空")
    private Integer partition;

    @Excel(name = "副本数", no = 12)
    @NotNull(message = "副本数不能为空")
    private Short replication;

    @Excel(name = "数据保留时长", no = 13)
    @NotNull(message = "数据保留时长不能为空")
    private Long retention;

    @Excel(name = "时长单位", no = 14, selects = {"天", "小时"}, selectEns = {"DAY", "HOUR"})
    @NotNull(message = "数据保留时长单位不能为空")
    private String retentionUnit;

    @Excel(name = "单消息最大长度", no = 15, selects = {"缺省值", "1M", "10M"}, selectEns = {"DEFAULT", "1M", "10M"})
    @NotNull(message = "单消息最大长度不能为空")
    private String maxMessageBytes;


    @Excel(name = "消息体格式", no = 16, selects = {"JSON", "AVRO", "PB", "一般文本", "二进制"}, selectEns = {"JSON", "AVRO", "PB", "TEXT", "BINARY"})
    @NotBlank(message = "消息体格式不能为空")
    @Pattern(regexp = "^JSON|AVRO|PB|TEXT|BINARY$", message = "消息体格式不存在")
    private String messageType;

    @Excel(name = "高级参数", no = 19)
    private String advancedParamStr;

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public Integer getPartition() {
        return partition;
    }

    public void setPartition(Integer partition) {
        this.partition = partition;
    }

    public Short getReplication() {
        return replication;
    }

    public void setReplication(Short replication) {
        this.replication = replication;
    }

    public Long getRetention() {
        return retention;
    }

    public void setRetention(Long retention) {
        this.retention = retention;
    }

    public String getRetentionUnit() {
        return retentionUnit;
    }

    public void setRetentionUnit(String retentionUnit) {
        this.retentionUnit = retentionUnit;
    }

    public String getMaxMessageBytes() {
        return maxMessageBytes;
    }

    public void setMaxMessageBytes(String maxMessageBytes) {
        this.maxMessageBytes = maxMessageBytes;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getAdvancedParamStr() {
        return advancedParamStr;
    }

    public void setAdvancedParamStr(String advancedParamStr) {
        this.advancedParamStr = advancedParamStr;
    }

}
