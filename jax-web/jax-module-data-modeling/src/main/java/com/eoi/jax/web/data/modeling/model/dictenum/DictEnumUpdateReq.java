package com.eoi.jax.web.data.modeling.model.dictenum;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbDictEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Description:
 *
 * @Author: yaru.ma
 * Date: 2022/11/18 15:09
 */
public class DictEnumUpdateReq implements IUpdateModel<TbDictEnum> {

    @Schema(description = "标准代码编号")
    private Long id;

    @Schema(description = "编号")
    private String no;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "英文名称")
    private String nameEn;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "目录id")
    private Long folderId;

    private List<EnumValueCreateReq> enumValues;

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getFolderId() {
        return folderId;
    }

    public void setFolderId(Long folderId) {
        this.folderId = folderId;
    }

    public List<EnumValueCreateReq> getEnumValues() {
        return enumValues;
    }

    public void setEnumValues(List<EnumValueCreateReq> enumValues) {
        this.enumValues = enumValues;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
