package com.eoi.jax.web.data.modeling.model.metricnindsource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.repository.entity.TbMetricBindSource;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class MetricBindSourceFilterReq implements IFilterReq<TbMetricBindSource> {

    @Schema(description = "指标id")
    private Long metricId;

    /**
     * 生成where条件
     *
     * @param wrapper
     * @return
     */
    @Override
    public QueryWrapper<TbMetricBindSource> where(QueryWrapper<TbMetricBindSource> wrapper) {
        wrapper.lambda().eq(Objects.nonNull(metricId), TbMetricBindSource::getMetricId, metricId);
        return wrapper;
    }


    public Long getMetricId() {
        return metricId;
    }

    public void setMetricId(Long metricId) {
        this.metricId = metricId;
    }
}
