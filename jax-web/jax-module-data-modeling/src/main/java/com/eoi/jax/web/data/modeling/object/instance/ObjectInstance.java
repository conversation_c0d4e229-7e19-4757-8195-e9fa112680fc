package com.eoi.jax.web.data.modeling.object.instance;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2024/2/5
 */
public class ObjectInstance {

    private String objectTable;

    private LinkedHashMap<String, Object> props;

    public ObjectInstance() {
    }

    public ObjectInstance(String objectTable, LinkedHashMap<String, Object> props) {
        this.objectTable = objectTable;
        this.props = props;
    }

    public String getObjectTable() {
        return objectTable;
    }

    public void setObjectTable(String objectTable) {
        this.objectTable = objectTable;
    }

    public LinkedHashMap<String, Object> getProps() {
        return props;
    }

    public void setProps(LinkedHashMap<String, Object> props) {
        this.props = props;
    }
}
