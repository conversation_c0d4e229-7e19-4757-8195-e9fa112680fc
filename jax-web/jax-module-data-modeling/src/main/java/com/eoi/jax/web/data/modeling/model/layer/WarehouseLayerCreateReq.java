package com.eoi.jax.web.data.modeling.model.layer;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbWarehouseLayer;
import io.swagger.v3.oas.annotations.media.Schema;

public class WarehouseLayerCreateReq implements ICreateModel<TbWarehouseLayer> {

    @Schema(description = "中文名")
    private String name;

    @Schema(description = "英文缩写")
    private String code;

    @Schema(description = "英文名")
    private String nameEn;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "分类归属：DS贴源层、WH公共层、APP应用层")
    private String catalog;

    @Schema(description = "模型类型：ODS贴源表、DIMENSION维度、DIM维度表、DWD明细表、DWS汇总表、ADS应用表")
    private String tbType;

    @Override
    public TbWarehouseLayer toEntity() {
        TbWarehouseLayer layer = toEntity(new TbWarehouseLayer());
        layer.setIsBuiltIn(0);
        return layer;
    }

    public String getName() {
        return name;
    }

    public WarehouseLayerCreateReq setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WarehouseLayerCreateReq setCode(String code) {
        this.code = code;
        return this;
    }

    public String getNameEn() {
        return nameEn;
    }

    public WarehouseLayerCreateReq setNameEn(String nameEn) {
        this.nameEn = nameEn;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public WarehouseLayerCreateReq setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getCatalog() {
        return catalog;
    }

    public WarehouseLayerCreateReq setCatalog(String catalog) {
        this.catalog = catalog;
        return this;
    }

    public String getTbType() {
        return tbType;
    }

    public WarehouseLayerCreateReq setTbType(String tbType) {
        this.tbType = tbType;
        return this;
    }
}
