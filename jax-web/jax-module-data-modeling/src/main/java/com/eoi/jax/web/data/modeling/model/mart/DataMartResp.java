package com.eoi.jax.web.data.modeling.model.mart;

import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.common.audit.OpPrimaryName;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IProjectAuthModel;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.ProjectAuthRespModel;
import com.eoi.jax.web.data.modeling.model.subject.DataSubjectResp;
import com.eoi.jax.web.repository.entity.TbDataMart;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DataMartResp
 *
 * <AUTHOR>
 * @date 2022/11/22 17:51
 */
@OpPrimaryKey(name = "id")
@OpPrimaryName(name = "name")
public class DataMartResp extends ProjectAuthRespModel
        implements IRespModel<TbDataMart>, IProjectAuthModel, IUserInfoExtensionModel, IMartSubjectTreeNode {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "中文名")
    private String name;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "唯一标识Code")
    private String code;

    @Schema(description = "业务ID")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long bizId;

    @Schema(description = "集市类型")
    private String martType;

    @Schema(description = "父ID")
    private Long parentId;

    @Schema(description = "父路径")
    private String parentPath;
    @Schema(description = "根ID")
    private Long rootId;
    @Schema(description = "叶子节点")
    private Boolean isLeaf;
    @Schema(description = "业务分类名称")
    private String bizName;
    @Schema(description = "子节点")
    private List<IMartSubjectTreeNode> children;

    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "创建人id")
    private Long createUser;
    @Schema(description = "更新时间")
    private Date updateTime;
    @Schema(description = "更新人id")
    private Long updateUser;
    private String nodeType = "dataMart";

    public void setMartChildren(List<DataMartResp> children) {
        if (children == null) {
            this.children = null;
        } else {
            this.children = children.stream().map(x -> (IMartSubjectTreeNode) x).collect(Collectors.toList());
        }
    }

    public void addSubjectChildren(List<DataSubjectResp> children) {
        if (children == null) {
            return;
        }
        List<IMartSubjectTreeNode> list = children.stream().map(x -> (IMartSubjectTreeNode) x).collect(Collectors.toList());
        if (this.children == null) {
            this.children = list;
        } else {
            this.children.addAll(list);
        }
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public String getMartType() {
        return martType;
    }

    public void setMartType(String martType) {
        this.martType = martType;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentPath() {
        return parentPath;
    }

    public void setParentPath(String parentPath) {
        this.parentPath = parentPath;
    }

    public Long getRootId() {
        return rootId;
    }

    public void setRootId(Long rootId) {
        this.rootId = rootId;
    }

    public Boolean getLeaf() {
        return isLeaf;
    }

    public void setLeaf(Boolean leaf) {
        isLeaf = leaf;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public List<IMartSubjectTreeNode> getChildren() {
        return children == null ? new ArrayList<>() : children;
    }

    public void setChildren(List<IMartSubjectTreeNode> children) {
        this.children = children;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Boolean leaf) {
        this.isLeaf = leaf;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }
}
