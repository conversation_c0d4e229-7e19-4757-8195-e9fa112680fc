package com.eoi.jax.web.data.modeling.model.metricsource;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbMetricSource;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class MetricSourceSortReq implements ISortReq<TbMetricSource> {

    private String createTime;

    private String updateTime;

    /**
     * 生成order排序
     *
     * @param wrapper
     * @return
     */
    @Override
    public QueryWrapper<TbMetricSource> order(QueryWrapper<TbMetricSource> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbMetricSource::getCreateTime);
        wrapper.lambda().orderBy(isOrder(updateTime), isAsc(updateTime), TbMetricSource::getUpdateTime);
        return wrapper;
    }


    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
