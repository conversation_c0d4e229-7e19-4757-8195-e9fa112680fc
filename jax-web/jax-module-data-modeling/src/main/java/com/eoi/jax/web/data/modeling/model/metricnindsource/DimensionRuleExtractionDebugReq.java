package com.eoi.jax.web.data.modeling.model.metricnindsource;

import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class DimensionRuleExtractionDebugReq extends DimensionRuleExtractionReq {

    @NotBlank(message = "测试Json不能为空")
    @Schema(description = "测试Json")
    private String testJson;

    public String getTestJson() {
        return testJson;
    }

    public void setTestJson(String testJson) {
        this.testJson = testJson;
    }
}
