package com.eoi.jax.web.data.modeling.model.metricsource;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.repository.entity.TbMetricSource;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class MetricSourceQueryReq extends BaseQueryReq<TbMetricSource> {


    private MetricSourceFilterReq filter = new MetricSourceFilterReq();

    private MetricSourceSortReq sort = new MetricSourceSortReq();

    /**
     * 过滤条件
     *
     * @return
     */
    @Override
    public MetricSourceFilterReq getFilter() {
        return filter;
    }

    /**
     * 排序条件
     *
     * @return
     */
    @Override
    public MetricSourceSortReq getSort() {
        return sort;
    }

    public void setFilter(MetricSourceFilterReq filter) {
        this.filter = filter;
    }

    public void setSort(MetricSourceSortReq sort) {
        this.sort = sort;
    }
}
