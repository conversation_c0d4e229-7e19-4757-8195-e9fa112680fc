package com.eoi.jax.web.data.modeling.model.object.relation.path;

import com.eoi.jax.web.core.model.IUpdateModel;
import com.eoi.jax.web.repository.entity.TbObjectRelationPath;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */

public class ObjectRelationPathUpdateReq implements IUpdateModel<TbObjectRelationPath> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 查询名称
     */
    @Schema(description = "查询名称")
    @NotNull(message = "查询名称不能为空")
    private String queryName;

    /**
     * 路径类型
     */
    @Schema(description = "路径类型:ObjectRelationPathTypeEnum:CORRELATION,应用系统调用关系;HOMOLOGY,应用系统同源关系")
    @NotNull(message = "查询路径类型能为空")
    private String queryPathType;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String remark;

    /**
     * 查询路规则
     */
    @Schema(description = "查询路规则")
    private List<Map<String, Object>> queryPathRule;

    /**
     * 状态(1,启用；0,停用)
     */
    @Schema(description = "状态(true,启用；false,停用)")
    private Boolean status;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQueryName() {
        return queryName;
    }

    public void setQueryName(String queryName) {
        this.queryName = queryName;
    }

    public String getQueryPathType() {
        return queryPathType;
    }

    public void setQueryPathType(String queryPathType) {
        this.queryPathType = queryPathType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<Map<String, Object>> getQueryPathRule() {
        return queryPathRule;
    }

    public void setQueryPathRule(List<Map<String, Object>> queryPathRule) {
        this.queryPathRule = queryPathRule;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "TbObjectRelationQuery{" +
                "id=" + id +
                ", queryName=" + queryName +
                ", queryPathType=" + queryPathType +
                ", remark=" + remark +
                ", queryPathRule=" + queryPathRule +
                ", status=" + status +
                "}";
    }

    @Override
    public TbObjectRelationPath toEntity(TbObjectRelationPath entityOld) {
        TbObjectRelationPath entityNew = IUpdateModel.super.toEntity(entityOld);
        return entityNew;
    }
}
