package com.eoi.jax.web.data.modeling.model.column;

import com.eoi.jax.web.core.model.BaseQueryReq;
import com.eoi.jax.web.core.model.IFilterReq;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbColumn;
/**
 * @Author: <PERSON>
 * @Date: 2022/12/2
 * @Desc:
 **/
public class ColumnQueryReq extends BaseQueryReq<TbColumn> {

    private ColumnFilterReq filter = new ColumnFilterReq();
    private ColumnSortReq sort = new ColumnSortReq();

    @Override
    public IFilterReq<TbColumn> getFilter() {
        return filter;
    }

    public void setFilter(ColumnFilterReq filter) {
        this.filter = filter;
    }

    @Override
    public ISortReq<TbColumn> getSort() {
        return sort;
    }

    public void setSort(ColumnSortReq sort) {
        this.sort = sort;
    }
}
