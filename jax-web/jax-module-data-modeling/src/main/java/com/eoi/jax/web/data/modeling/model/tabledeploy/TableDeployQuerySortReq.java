package com.eoi.jax.web.data.modeling.model.tabledeploy;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eoi.jax.web.core.model.ISortReq;
import com.eoi.jax.web.repository.entity.TbTableDeploy;

/**
 * @Author: zsc
 * @Date: 2022/12/08
 **/
public class TableDeployQuerySortReq implements ISortReq<TbTableDeploy> {

    private String createTime;
    private String updateTime;

    @Override
    public QueryWrapper<TbTableDeploy> order(QueryWrapper<TbTableDeploy> wrapper) {
        wrapper.lambda().orderBy(isOrder(createTime), isAsc(createTime), TbTableDeploy::getCreateTime)
                .orderBy(isOrder(updateTime), isAsc(updateTime), TbTableDeploy::getUpdateTime);
        return wrapper;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
