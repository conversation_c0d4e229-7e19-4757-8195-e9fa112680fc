package com.eoi.jax.web.data.modeling.model.domain;

import com.eoi.jax.web.core.common.util.ModelBeanUtil;
import com.eoi.jax.web.core.config.LongStringSerializer;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.data.modeling.model.busproc.BusinessProcessResp;
import com.eoi.jax.web.repository.entity.TbDataDomain;
import com.eoi.jax.web.repository.entity.VDataDomainBusinessProcess;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.List;

public class DataDomainBusProcessResp extends UserExtensionRespModel implements IUserInfoExtensionModel,
        IRespModel<VDataDomainBusinessProcess> {

    @Schema(description = "数据域id")
    @JsonSerialize(using = LongStringSerializer.class)
    private Long id;

    @Schema(description = "数据域code")
    private String code;

    @Schema(description = "数据域中文名")
    private String name;

    @Schema(description = "数据域英文名")
    private String nameEn;

    @Schema(description = "数据域描述")
    private String description;

    @Schema(description = "数据域是否删除")
    private Integer isDeleted;

    @Schema(description = "数据域创建时间")
    private Date createTime;

    @Schema(description = "数据域更新时间")
    private Date updateTime;

    @Schema(description = "数据域创建用户id")
    private Long createUser;

    @Schema(description = "数据域更新用户id")
    private Long updateUser;

    @Schema(description = "数据域对应的业务过程")
    private List<BusinessProcessResp> children;

    @Schema(description = "节点类型")
    private String nodeType = "dataDomain";

    public DataDomainBusProcessResp fromEntity(VDataDomainBusinessProcess t) {
        return (DataDomainBusProcessResp) copyFrom(t);
    }

    public DataDomainBusProcessResp fromEntity(TbDataDomain t) {
        ModelBeanUtil.copyBean(t, this);
        return this;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public List<BusinessProcessResp> getChildren() {
        return children;
    }

    public void setChildren(List<BusinessProcessResp> process) {
        this.children = process;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }
}
