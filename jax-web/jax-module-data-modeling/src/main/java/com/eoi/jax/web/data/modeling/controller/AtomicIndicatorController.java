package com.eoi.jax.web.data.modeling.controller;

import com.eoi.jax.web.core.common.audit.AuditLog;
import com.eoi.jax.web.core.common.audit.OpActionEnum;
import com.eoi.jax.web.core.common.audit.OpParameters;
import com.eoi.jax.web.core.common.audit.OpPrimaryKey;
import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.modeling.model.indicator.AtomicIndicatorCreateReq;
import com.eoi.jax.web.data.modeling.model.indicator.AtomicIndicatorQueryReq;
import com.eoi.jax.web.data.modeling.model.indicator.AtomicIndicatorUpdateReq;
import com.eoi.jax.web.data.modeling.service.AtomicIndicatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/11/21
 * @Desc: 原子指标
 **/
@RestController
public class AtomicIndicatorController implements V2Controller {

    private static final String CATEGORY_NAME = "数据建模";
    private static final String MODEL_NAME = "数据指标";
    private static final String FUNCTION_NAME = "原子指标";
    private static final String CODE = "atomicIndicator";

    @Autowired
    private AtomicIndicatorService atomicIndicatorService;


    @Operation(summary = "原子指标全部查询")
    @GetMapping("data/modeling/atomic-indicator/list")
    public Response list() {
        return Response.success(atomicIndicatorService.all());
    }


    @Operation(summary = "分页查询原子指标")
    @PostMapping("data/modeling/atomic-indicator/query")
    public Response query(@RequestBody AtomicIndicatorQueryReq req) {
        return Response.success(atomicIndicatorService.query(req));
    }

    @Operation(summary = "查询原子指标详情")
    @GetMapping("data/modeling/atomic-indicator/{id}")
    public Response get(@Parameter(description = "主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(atomicIndicatorService.get(id));
    }

    @Operation(summary = "创建原子指标")
    @PostMapping("data/modeling/atomic-indicator")
    @AuditLog(category = CATEGORY_NAME, opAction = OpActionEnum.CREATE, module = MODEL_NAME, function = FUNCTION_NAME, code = CODE)
    public Response create(@OpParameters @RequestBody AtomicIndicatorCreateReq req) {
        return Response.success(atomicIndicatorService.create(req));
    }

    @Operation(summary = "更新原子指标")
    @PutMapping("data/modeling/atomic-indicator/{id}")
    @AuditLog(category = CATEGORY_NAME, opAction = OpActionEnum.UPDATE, module = MODEL_NAME, function = FUNCTION_NAME, code = CODE)
    public Response update(@OpPrimaryKey @Parameter(description = "主键id", required = true) @PathVariable("id") Long id,
                           @OpParameters @RequestBody AtomicIndicatorUpdateReq req) {
        req.setId(id);
        return Response.success(atomicIndicatorService.update(req));
    }

    @Operation(summary = "删除原子指标")
    @DeleteMapping("data/modeling/atomic-indicator/{id}")
    @AuditLog(category = CATEGORY_NAME, opAction = OpActionEnum.DELETE, module = MODEL_NAME, function = FUNCTION_NAME, code = CODE)
    public Response delete(@OpPrimaryKey @OpParameters @Parameter(description = "主键id", required = true) @PathVariable("id") Long id) {
        return Response.success(atomicIndicatorService.delete(id));
    }

    @Operation(summary = "原子指标树")
    @GetMapping("data/modeling/atomic-indicator/tree/{searchType}")
    public Response tree(@Parameter(description = "查询类型", example = "bizType/dataDomainType")
                         @PathVariable(value = "searchType", required = false) String searchType) {
        return Response.success(atomicIndicatorService.tree(searchType));
    }

    @Operation(summary = "关联数据使用情况")
    @GetMapping("data/modeling/atomic-indicator/{id}/usage")
    public Response usage(
            @Parameter(description = "主键id", required = true) @PathVariable("id") Long id
    ) {
        return Response.success(atomicIndicatorService.usage(id));
    }

    @Operation(summary = "原子指标使用情况（批量）")
    @GetMapping("data/modeling/atomic-indicator/bulk/usage")
    public Response usageByBulk(
            @Parameter(description = "原子指标id", required = true) @RequestParam("id") List<Long> ids
    ) {
        return Response.success(atomicIndicatorService.usageByBulk(ids));
    }

    @Operation(summary = "批量删除原子指标")
    @DeleteMapping("data/modeling/atomic-indicator/bulk")
    public Response deleteBulk(
            @OpParameters @Parameter(description = "原子指标id", required = true) @RequestParam("id") List<Long> ids
    ) {
        return Response.success(atomicIndicatorService.deleteBulk(ids));
    }
}
