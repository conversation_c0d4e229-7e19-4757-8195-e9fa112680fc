package com.eoi.jax.web.data.modeling.controller;

import com.eoi.jax.web.core.controller.V2Controller;
import com.eoi.jax.web.core.model.Response;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectDetailReq;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectRelationReq;
import com.eoi.jax.web.data.modeling.model.cmdbobject.CmdbObjectReq;
import com.eoi.jax.web.data.modeling.model.cmdbobject.ObjectRelationInstanceCountReq;
import com.eoi.jax.web.data.modeling.model.object.category.ObjectCategoryTree;
import com.eoi.jax.web.data.modeling.model.object.category.ObjectCategoryTreeFilter;
import com.eoi.jax.web.data.modeling.model.object.column.ObjectColumnResp;
import com.eoi.jax.web.data.modeling.service.ObjectInstanceWideTableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@RestController
public class ObjectInstanceWideTableController implements V2Controller {

    @Resource
    private ObjectInstanceWideTableService objectInstanceWideTableService;

    /**
     * @return
     */
    @Operation(summary = "获取对象模型分类列表以及分组下的对象模型分类(包含模型数据)")
    @PostMapping("data-modeling/cmdb-object/tree")
    public Response<List<ObjectCategoryTree>> treeObjectQuery(@RequestBody(required = false)
                                                              ObjectCategoryTreeFilter categoryFilter) {
        return Response.success(objectInstanceWideTableService.treeListWithObjectTable(categoryFilter));
    }

    @Operation(summary = "获取对象模型最后一次发布的成功字段")
    @GetMapping("data-modeling/cmdb-object/deploy-column/{objId}")
    public Response<List<ObjectColumnResp>> listColumnDeploy(
        @Parameter(description = "对象模型id", required = true) @PathVariable("objId") Long objId) {
        return Response.success(objectInstanceWideTableService.listColumnDeploy(objId));
    }


    @Operation(summary = "根据条件查询对象模型实例数据")
    @PostMapping("data-modeling/cmdb-object/query")
    public Response query(@RequestBody CmdbObjectReq condition) {
        return Response.success(objectInstanceWideTableService.query(condition));
    }


    @Operation(summary = "根据vid查询对象模型实例数据")
    @PostMapping("data-modeling/cmdb-object/detail")
    public Response getByVid(@RequestBody CmdbObjectDetailReq req) {
        return Response.success(objectInstanceWideTableService.getByVid(req));
    }


    @Deprecated
    @Operation(summary = "【过时】根据vid查询对象模型实例关联关系")
    @GetMapping("data-modeling/cmdb-object/relation/{vid}")
    public Response objectInstanceRelation(@PathVariable("vid") String vid) {
        return Response.success(objectInstanceWideTableService.objectInstanceRelation(vid));
    }


    @Operation(summary = "根据vid查询对象模型实例关联关系")
    @PostMapping("data-modeling/cmdb-object/relation")
    public Response objectInstanceRelationV2(@Validated @RequestBody CmdbObjectRelationReq req) {
        return Response.success(objectInstanceWideTableService.objectInstanceRelation(req.getVid()));
    }

    @Operation(summary = "查询ods表同步的对象模型实例和关系实例数量")
    @PostMapping("data-modeling/cmdb-object/instance-count")
    public Response objectRelationInstanceCount(@Validated @RequestBody ObjectRelationInstanceCountReq req) {
        return Response.success(objectInstanceWideTableService.objectRelationInstanceCount(req));
    }

}
