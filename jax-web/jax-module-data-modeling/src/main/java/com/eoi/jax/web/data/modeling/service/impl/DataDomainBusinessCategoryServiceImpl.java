package com.eoi.jax.web.data.modeling.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eoi.jax.web.core.common.ContextHolder;
import com.eoi.jax.web.core.common.constant.ResponseCode;
import com.eoi.jax.web.core.common.exception.BizException;
import com.eoi.jax.web.core.model.UsageResp;
import com.eoi.jax.web.core.repository.JaxRepository;
import com.eoi.jax.web.core.service.BaseService;
import com.eoi.jax.web.data.modeling.model.business.DataDomainBusinessCategoryResp;
import com.eoi.jax.web.data.modeling.model.databusiness.DataDomainBusinessCategoryCreateReq;
import com.eoi.jax.web.data.modeling.model.databusiness.DataDomainBusinessCategoryQueryReq;
import com.eoi.jax.web.data.modeling.model.databusiness.DataDomainBusinessCategoryUpdateReq;
import com.eoi.jax.web.data.modeling.model.databusiness.TbDataDomainBusinessCategoryResp;
import com.eoi.jax.web.data.modeling.service.BusinessCategoryService;
import com.eoi.jax.web.data.modeling.service.DataDomainBusinessCategoryService;
import com.eoi.jax.web.repository.entity.*;
import com.eoi.jax.web.repository.mapper.VDataDomainBusinessCategoryExample;
import com.eoi.jax.web.repository.service.TbDataDomainBusinessCategoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DataDomainBusinessCategoryServiceImpl extends BaseService<
        TbDataDomainBusinessCategoryService,
        TbDataDomainBusinessCategory,
        TbDataDomainBusinessCategoryResp,
        DataDomainBusinessCategoryCreateReq,
        DataDomainBusinessCategoryUpdateReq,
        DataDomainBusinessCategoryQueryReq>
        implements DataDomainBusinessCategoryService {

    @Resource
    private JaxRepository jaxRepository;
    @Resource
    private BusinessCategoryService businessCategoryService;

    public DataDomainBusinessCategoryServiceImpl(@Autowired TbDataDomainBusinessCategoryService service) {
        super(service);
    }

    /**
     * 查询所有数据域并且根据bizId过滤
     *
     * @param bizId
     * @return
     */
    @Override
    public List<TbDataDomain> listFilterDataDomains(Long bizId) {
        businessCategoryService.getWithProjectAuth(bizId);
        List<TbDataDomain> domainList = jaxRepository.dataDomainService().selectListWithProjectAuth(null);
        LambdaQueryWrapper<TbDataDomainBusinessCategory> wrapper = new LambdaQueryWrapper<TbDataDomainBusinessCategory>()
                .eq(TbDataDomainBusinessCategory::getBizId, bizId);

        List<TbDataDomainBusinessCategory> domainBusinessList = jaxRepository.dataDomainBusinessCategoryService().list(wrapper);
        if (CollectionUtils.isNotEmpty(domainBusinessList)) {
            List<Long> domIdList = domainBusinessList.stream().map(TbDataDomainBusinessCategory::getDomId).collect(Collectors.toList());
            domainList = domainList.stream().filter(domain -> !domIdList.contains(domain.getId())).collect(Collectors.toList());
        }
        return domainList;
    }

    /**
     * 查询业务分类下关联的数据域
     *
     * @param bizId
     * @return
     */
    @Override
    public List<DataDomainBusinessCategoryResp> listDataDomains(Long bizId) {
        if (ObjectUtil.isNull(bizId)) {
            return jaxRepository.dataDomainService().selectListWithProjectAuth(null)
                    .stream().map(domain -> {
                        DataDomainBusinessCategoryResp resp = new DataDomainBusinessCategoryResp();
                        resp.setDomId(domain.getId());
                        resp.setDomName(domain.getName());
                        resp.setDomCode(domain.getCode());
                        return resp;
                    }).collect(Collectors.toList());
        } else {
            businessCategoryService.getWithProjectAuth(bizId);
            VDataDomainBusinessCategoryExample example = new VDataDomainBusinessCategoryExample();
            example.setBizId(bizId);
            List<VDataDomainBusinessCategory> list = jaxRepository.vDomainCategory()
                    .selectList(example, ContextHolder.getCurrentProjectId());
            return list.stream().map(i ->
                    (DataDomainBusinessCategoryResp) new DataDomainBusinessCategoryResp().fromEntity(i)
            ).collect(Collectors.toList());
        }
    }

    /**
     * 一键创建业务分类和数据域的关联，已有的关联记录不在重复创建
     *
     * @param bizId
     * @return
     */
    public List<TbDataDomainBusinessCategoryResp> createUnRelation(Long bizId) {
        if (Objects.isNull(bizId)) {
            throw new BizException(ResponseCode.INVALID_PARAM);
        }
        // validate bizId
        jaxRepository.businessCategory().selectByIdWithProjectAuth(bizId, new BizException(ResponseCode.ID_NOT_EXISTS));
        // all data domain
        List<TbDataDomain> domainList = jaxRepository.dataDomainService().selectListWithProjectAuth(null);
        if (CollectionUtils.isEmpty(domainList)) {
            return null;
        }

        LambdaQueryWrapper<TbDataDomainBusinessCategory> queryWrapper = new LambdaQueryWrapper<TbDataDomainBusinessCategory>()
                .eq(TbDataDomainBusinessCategory::getBizId, bizId);
        List<TbDataDomainBusinessCategoryResp> domainBusinessList = super.all(queryWrapper);
        List<Long> filterDomIdList = domainBusinessList.stream()
                .map(TbDataDomainBusinessCategoryResp::getDomId).collect(Collectors.toList());

        List<DataDomainBusinessCategoryCreateReq> createList = domainList.stream()
                .filter(domain -> !filterDomIdList.contains(domain.getId()))
                .map(domain -> {
                    DataDomainBusinessCategoryCreateReq createReq = new DataDomainBusinessCategoryCreateReq();
                    createReq.setBizId(bizId);
                    createReq.setDomId(domain.getId());
                    return createReq;
                }).collect(Collectors.toList());

        return super.create(createList);
    }

    @Override
    public List<UsageResp> usageByEntity(TbDataDomainBusinessCategory domainBusinessCategory) {
        List<UsageResp> result = new ArrayList<>();
        VDataDomainBusinessCategoryExample query = new VDataDomainBusinessCategoryExample();
        query.setId(domainBusinessCategory.getId());
        List<VDataDomainBusinessCategory> domainCategory = jaxRepository.vDomainCategory()
                .selectList(query, ContextHolder.getCurrentProjectId());
        if (CollUtil.isEmpty(domainCategory)) {
            throw new BizException(ResponseCode.ID_NOT_EXISTS);
        }
        VDataDomainBusinessCategory entity = domainCategory.get(0);
        Long domId = entity.getDomId();
        Long bizId = entity.getBizId();
        // 1. 检查数据建模（维度）依赖
        result.addAll(jaxRepository.dimension().list(new LambdaQueryWrapper<TbDimension>()
                        .eq(TbDimension::getDomId, domId)
                        .eq(TbDimension::getBizId, bizId))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        // 2. 检查数据建模（维度表、明细表、汇总表、应用表）依赖
        result.addAll(jaxRepository.table().list(new LambdaQueryWrapper<TbTable>()
                        .eq(TbTable::getDomId, domId)
                        .eq(TbTable::getBizId, bizId))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        // 3. 检查数据指标-修饰词依赖
        result.addAll(jaxRepository.adj().list(new LambdaQueryWrapper<TbAdjunct>()
                        .eq(TbAdjunct::getDomId, domId)
                        .eq(TbAdjunct::getBizId, bizId))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        // 4. 检查数据指标-原子指标依赖
        result.addAll(jaxRepository.atomicIndicator().list(new LambdaQueryWrapper<TbAtomicIndicator>()
                        .eq(TbAtomicIndicator::getDomId, domId)
                        .eq(TbAtomicIndicator::getBizId, bizId))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        // 5. 检查数据指标-派生指标依赖
        result.addAll(jaxRepository.derivativeIndicator().list(new LambdaQueryWrapper<TbDerivativeIndicator>()
                        .eq(TbDerivativeIndicator::getDomId, domId)
                        .eq(TbDerivativeIndicator::getBizId, bizId))
                .stream().map(x -> new UsageResp().fromEntity(entity, x))
                .collect(Collectors.toList()));
        return result;
    }
}
