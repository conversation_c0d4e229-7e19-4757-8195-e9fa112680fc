package com.eoi.jax.web.data.modeling.schedule;

import com.eoi.jax.web.ingestion.service.RepositoryService;
import com.eoi.jax.web.repository.entity.TbDatasource;
import com.eoi.jax.web.repository.service.TbDatasourceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/2
 */
@Component
public class JaxTableRegisterToClickhouseScheduler {
    private static final Logger LOGGER = LoggerFactory.getLogger(JaxTableRegisterToClickhouseScheduler.class);
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private TbDatasourceService tbDatasourceService;

    @XxlJob("jaxTableRegisterToClickhouse")
    public void jaxTableRegisterToClickhouse() {
        try {
            XxlJobHelper.log("开始注册中台表到Clickhouse");
            List<TbDatasource> list = tbDatasourceService.list();
            for (TbDatasource ds : list) {
                try {
                    List<String> tables = repositoryService.registerCkUrlTable(ds.getId());
                    if (!tables.isEmpty()) {
                        XxlJobHelper.log("[" + ds.getId() + "-" + ds.getCode() + "-" + ds.getName() +
                                "] 注册中台表到成功，共注册" + tables.size() + "张表: " + tables);
                    }
                } catch (Exception e) {
                    XxlJobHelper.log("[" + ds.getId() + "-" + ds.getCode() + "-" + ds.getName()
                            + "] 注册中台表到失败");
                }
            }
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.log("同步数据源到配置的Grafana失败");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        }
    }
}
