package com.eoi.jax.web.data.modeling.model.tabledeployhistory;

import com.eoi.jax.web.core.model.ICreateModel;
import com.eoi.jax.web.repository.entity.TbTableDeployHistory;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: zsc
 * @Date: 2022/01/03
 */
public class TableDeployHistoryCreateReq implements ICreateModel<TbTableDeployHistory> {

    @Schema(description = "数据模型发布表id")
    @NotNull
    private Long tableDeployId;

    @Schema(description = "数据源类型：KAFKA, ELASTICSEARCH, CLICKHOUSE, MYSQL等")
    @NotBlank(message = "数据源类型不能为空")
    private String platform;

    @Schema(description = "发布类型：TABLE，VIEW")
    @NotNull(message = "发布类型不能为空")
    @Pattern(regexp = "^TABLE|VIEW$", message = "发布类型不存在")
    private String deployType;

    @Schema(description = "发布动作类型：CREATE|REBUILD|INCREMENT|ASSOCIATION")
    @NotNull(message = "发布动作不能为空")
    @Pattern(regexp = "^CREATE|REBUILD|INCREMENT|ASSOCIATION$", message = "发布动作不存在")
    private String actionType;

    @Schema(description = "删除模型类型：LOGIC|PHYSICAL")
    @Pattern(regexp = "^LOGIC|PHYSICAL$", message = "删除模型类型不存在")
    private String deleteType;

    @Schema(description = "业务过程id")
    @NotNull(message = "业务过程不能为空")
    private Long businessFlowId;

    @Schema(description = "发布名称")
    @NotBlank(message = "发布名称不能为空")
    private String name;

    @Schema(description = "数据源id")
    @NotNull(message = "数据源id不能为空")
    private Long dsId;

    @Schema(description = "不同数据库独有配置，json字符串")
    private Map<String, Object> settingMap = new LinkedHashMap<>();

    @Schema(description = "明细表id")
    @NotNull(message = "明细表id不能为空")
    private Long tbId;

    @Schema(description = "存储模型*：SINGLE 单值模型 | MULTI 多值模型")
    private String storageMode;

    @Schema(description = "值表名称")
    private String idxTbName;

    @Schema(description = "维度表名称")
    private String dimTbName;

    @Schema(description = "关联维度表id")
    private Long dimTbId;

    @Schema(description = "是否有关联模型")
    private Integer relateDim;

    @Schema(description = "备注信息")
    private String description;

    @Schema(description = "脚本")
    private String script;

    @Schema(description = "发布版本")
    @NotNull(message = "发布版本不能为空")
    private Integer version;

    @Schema(description = "发布状态，UNDEPLOYED-未发布，SUCCESS-发布成功，FAILURE-发布失败，DEPLOYING-发布中")
    private String status;

    @Schema(description = "错误信息")
    private String errMsg;

    @Schema(description = " 最后发布时间")
    private Date lastDeployTime;

    @Schema(description = "不同数据库独有配置，json字符串")
    private String setting;

    @Schema(description = "表名")
    private String tbName;

    @Schema(description = "表中文名")
    private String tbAlias;

    @Schema(description = "生命周期/天")
    private Long lifecycle;

    @Schema(description = "发布日志")
    private String deployLog;

    @Override
    public TbTableDeployHistory toEntity() {
        return toEntity(new TbTableDeployHistory());
    }

    public String getDeployLog() {
        return deployLog;
    }

    public void setDeployLog(String deployLog) {
        this.deployLog = deployLog;
    }

    public Long getTableDeployId() {
        return tableDeployId;
    }

    public void setTableDeployId(Long tableDeployId) {
        this.tableDeployId = tableDeployId;
    }

    public String getPlatform() {
        return platform;
    }

    public String getDeleteType() {
        return deleteType;
    }

    public void setDeleteType(String deleteType) {
        this.deleteType = deleteType;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Date getLastDeployTime() {
        return lastDeployTime;
    }

    public void setLastDeployTime(Date lastDeployTime) {
        this.lastDeployTime = lastDeployTime;
    }

    public String getDeployType() {
        return deployType;
    }

    public void setDeployType(String deployType) {
        this.deployType = deployType;
    }

    public Integer getRelateDim() {
        return relateDim;
    }

    public void setRelateDim(Integer relateDim) {
        this.relateDim = relateDim;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getStorageMode() {
        return storageMode;
    }

    public void setStorageMode(String storageMode) {
        this.storageMode = storageMode;
    }

    public String getIdxTbName() {
        return idxTbName;
    }

    public void setIdxTbName(String idxTbName) {
        this.idxTbName = idxTbName;
    }

    public String getDimTbName() {
        return dimTbName;
    }

    public void setDimTbName(String dimTbName) {
        this.dimTbName = dimTbName;
    }

    public Long getDimTbId() {
        return dimTbId;
    }

    public void setDimTbId(Long dimTbId) {
        this.dimTbId = dimTbId;
    }

    public Long getBusinessFlowId() {
        return businessFlowId;
    }

    public void setBusinessFlowId(Long businessFlowId) {
        this.businessFlowId = businessFlowId;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getDsId() {
        return dsId;
    }

    public void setDsId(Long dsId) {
        this.dsId = dsId;
    }

    public Map<String, Object> getSettingMap() {
        return settingMap;
    }

    public void setSettingMap(Map<String, Object> settingMap) {
        this.settingMap = settingMap;
    }

    public Long getTbId() {
        return tbId;
    }

    public void setTbId(Long tbId) {
        this.tbId = tbId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSetting() {
        return setting;
    }

    public void setSetting(String setting) {
        this.setting = setting;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getTbAlias() {
        return tbAlias;
    }

    public void setTbAlias(String tbAlias) {
        this.tbAlias = tbAlias;
    }

    public Long getLifecycle() {
        return lifecycle;
    }

    public void setLifecycle(Long lifecycle) {
        this.lifecycle = lifecycle;
    }
}
