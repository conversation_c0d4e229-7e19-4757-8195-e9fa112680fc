package com.eoi.jax.web.data.modeling.model.metricsource;

import cn.hutool.core.util.StrUtil;
import com.eoi.jax.web.core.common.util.JsonUtil;
import com.eoi.jax.web.core.model.IRespModel;
import com.eoi.jax.web.core.model.IUserInfoExtensionModel;
import com.eoi.jax.web.core.model.UserExtensionRespModel;
import com.eoi.jax.web.repository.entity.TbMetricSource;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
public class MetricSourceResp extends UserExtensionRespModel implements IUserInfoExtensionModel, IRespModel<TbMetricSource> {

    @Schema(description = "id")
    private Long id;

    /**
     * 来源名称
     */
    @Schema(description = "来源名称")
    private String name;

    /**
     * 来源类型，zabbix，蓝鲸
     */
    @Schema(description = "来源类型")
    private String type;

    /**
     * 配置(JSON)
     */
    @Schema(description = "配置(JSON)")
    private Map<String, Object> config;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人id")
    private Long createUser;

    @Schema(description = "更新人id")
    private Long updateUser;


    /**
     * response from entity
     *
     * @param tbMetricSource
     * @return
     */
    @Override
    public MetricSourceResp fromEntity(TbMetricSource tbMetricSource) {
        MetricSourceResp resp = IRespModel.super.fromEntity(tbMetricSource);
        if (StrUtil.isNotBlank(tbMetricSource.getConfig())) {
            resp.setConfig(JsonUtil.decode(tbMetricSource.getConfig(), new TypeReference<Map<String, Object>>() {
            }));
        }
        return resp;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getCreateUser() {
        return createUser;
    }

    @Override
    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    @Override
    public Long getUpdateUser() {
        return updateUser;
    }

    @Override
    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }
}
