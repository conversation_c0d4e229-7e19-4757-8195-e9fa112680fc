package com.eoi.jax.base.param;

/**
 * <AUTHOR>
 * @date 2023/2/23
 */
public class ParameterMeta {
    private int apiVersion = 2;
    private String name;
    private String label;
    private DataType[] type;
    private String description;
    private boolean optional;
    private String defaultValue;
    private String placeholder;
    private String[] candidates;
    private String[] recommendations;
    private InputType inputType;
    private String range;
    private String regex;
    private String requireCondition;
    private String availableCondition;
    private String scope;
    private ParameterMeta listParameter;
    private ParameterMeta[] objectParameters;
    private String trueLabel;
    private String falseLabel;
    private boolean objectTitle;

    public int getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(int apiVersion) {
        this.apiVersion = apiVersion;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public DataType[] getType() {
        return type;
    }

    public void setType(DataType[] type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isOptional() {
        return optional;
    }

    public void setOptional(boolean optional) {
        this.optional = optional;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }

    public String[] getCandidates() {
        return candidates;
    }

    public void setCandidates(String[] candidates) {
        this.candidates = candidates;
    }

    public String[] getRecommendations() {
        return recommendations;
    }

    public void setRecommendations(String[] recommendations) {
        this.recommendations = recommendations;
    }

    public InputType getInputType() {
        return inputType;
    }

    public void setInputType(InputType inputType) {
        this.inputType = inputType;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public String getRegex() {
        return regex;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }

    public String getRequireCondition() {
        return requireCondition;
    }

    public void setRequireCondition(String requireCondition) {
        this.requireCondition = requireCondition;
    }

    public String getAvailableCondition() {
        return availableCondition;
    }

    public void setAvailableCondition(String availableCondition) {
        this.availableCondition = availableCondition;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public ParameterMeta getListParameter() {
        return listParameter;
    }

    public void setListParameter(ParameterMeta listParameter) {
        this.listParameter = listParameter;
    }

    public ParameterMeta[] getObjectParameters() {
        return objectParameters;
    }

    public void setObjectParameters(ParameterMeta[] objectParameters) {
        this.objectParameters = objectParameters;
    }

    public String getTrueLabel() {
        return trueLabel;
    }

    public ParameterMeta setTrueLabel(String trueLabel) {
        this.trueLabel = trueLabel;
        return this;
    }

    public String getFalseLabel() {
        return falseLabel;
    }

    public ParameterMeta setFalseLabel(String falseLabel) {
        this.falseLabel = falseLabel;
        return this;
    }

    public boolean isObjectTitle() {
        return objectTitle;
    }

    public ParameterMeta setObjectTitle(boolean objectTitle) {
        this.objectTitle = objectTitle;
        return this;
    }
}
