package com.eoi.jax.base;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/23
 */
public final class ReflectUtil {

    public static List<Field> listFields(Class<?> beanClass, boolean withSuperClassFields) {
        if (beanClass == null) {
            return new ArrayList<>();
        }
        List<Field> list = new ArrayList<>();
        Class<?> superClass = beanClass.getSuperclass();
        if (withSuperClassFields && superClass != null) {
            list.addAll(listFields(superClass, true));
        }
        Field[] declaredFields = beanClass.getDeclaredFields();
        Collections.addAll(list, declaredFields);
        return list;
    }

}
