package com.eoi.jax.lifecycle.executor.client;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.eoi.jax.lifecycle.executor.exception.JaxClientException;
import com.eoi.jax.lifecycle.executor.exception.LifecycleCode;
import com.eoi.jax.lifecycle.executor.model.*;
import com.eoi.jax.lifecycle.executor.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/11/1
 */
public class JaxAdminClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(JaxAdminClient.class);
    public static final String API_EXECUTOR_REGISTER = "/lifecycle/executor/register";
    public static final String API_EXECUTOR_UNREGISTER = "/lifecycle/executor/unregister";
    public static final String API_EXECUTOR_HEARTBEAT = "/lifecycle/executor/heartbeat";
    public static final String API_JOB_START = "/lifecycle/job/%s/start";
    public static final String API_JOB_CALLBACK_STOP = "/lifecycle/job/%s/callback/stop";
    public static final String API_JOB_CALLBACK_SUCCESS = "/lifecycle/job/%s/callback/success";
    public static final String API_JOB_CALLBACK_FAILED = "/lifecycle/job/%s/callback/failed";
    public static final String API_TASK_CALLBACK_START = "/lifecycle/job/%s/task/%s/callback/start";
    public static final String API_TASK_CALLBACK_STOP = "/lifecycle/job/%s/task/%s/callback/stop";
    public static final String API_TASK_CALLBACK_SUCCESS = "/lifecycle/job/%s/task/%s/callback/success";
    public static final String API_TASK_CALLBACK_FAILED = "/lifecycle/job/%s/task/%s/callback/failed";
    public static final String API_CALLBACK_LOG = "/lifecycle/log/callback";
    private static final int TIMEOUT = 5000;
    private final List<String> addresses;
    private final String appKey;
    private final String secretKey;

    public JaxAdminClient(List<String> addresses, String appKey, String secretKey) {
        this.addresses = addresses;
        this.appKey = appKey;
        this.secretKey = secretKey;
    }

    public Map<String, Object> register(ExecutorInfo executor) {
        return apiCall(
                (String address) -> post(url(address, API_EXECUTOR_REGISTER), executor),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> unregister(ExecutorInfo executor) {
        return apiCall(
                (String address) -> post(url(address, API_EXECUTOR_UNREGISTER), executor),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> heartbeat(ExecutorInfo executor) {
        return apiCall(
                (String address) -> post(url(address, API_EXECUTOR_HEARTBEAT), executor),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> startJob(LifecycleJob job) {
        return apiCall(
                (String address) -> post(url(address, String.format(API_JOB_START, job.getJobId())), job),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> jobStopped(LifecycleJob job) {
        return apiCall(
                (String address) -> post(url(address, String.format(API_JOB_CALLBACK_STOP, job.getJobId())), job),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> jobSuccess(LifecycleJob job) {
        return apiCall(
                (String address) -> post(url(address, String.format(API_JOB_CALLBACK_SUCCESS, job.getJobId())), job),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> jobFailed(LifecycleJobFailure job) {
        return apiCall(
                (String address) -> post(url(address, String.format(API_JOB_CALLBACK_FAILED, job.getJobId())), job),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> taskStarted(LifecycleTask task) {
        return apiCall(
                (String address) -> post(
                        url(address, String.format(API_TASK_CALLBACK_START, task.getJobId(), task.getTaskId())), task),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> taskStopped(LifecycleTask task) {
        return apiCall(
                (String address) -> post(
                        url(address, String.format(API_TASK_CALLBACK_STOP, task.getJobId(), task.getTaskId())), task),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> taskSuccess(LifecycleTaskResult task) {
        return apiCall(
                (String address) -> post(
                        url(address, String.format(API_TASK_CALLBACK_SUCCESS, task.getJobId(), task.getTaskId())), task),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> taskFailed(LifecycleTaskFailure task) {
        return apiCall(
                (String address) -> post(
                        url(address, String.format(API_TASK_CALLBACK_FAILED, task.getJobId(), task.getTaskId())), task),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    public Map<String, Object> sendLogs(LifecycleLogs logs) {
        return apiCall(
                (String address) -> post(url(address, API_CALLBACK_LOG), logs),
                new TypeReference<JaxResponse<Map<String, Object>>>() {
                }
        );
    }

    private String url(String address, String api) {
        return address + api;
    }

    private HttpRequest post(String url, Object param) {
        HttpRequest request = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .header("appKey", appKey)
                .header("secretKey", secretKey)
                .timeout(TIMEOUT);
        if (param != null) {
            request.body(JsonUtil.encode(param));
        }
        return request;
    }

    public <T> T apiCall(RequestBuilder requestBuilder, TypeReference<JaxResponse<T>> respTypeRef) {
        JaxResponse<T> jaxResponse = jaxCall(requestBuilder, respTypeRef);
        if (!jaxResponse.isOk()) {
            String error = "jax api failed, jax response: " + JsonUtil.encode(jaxResponse);
            throw new JaxClientException(LifecycleCode.JAX_CLIENT_FAILED, new RuntimeException(error));
        }
        return jaxResponse.getData();
    }

    public <T> JaxResponse<T> jaxCall(RequestBuilder requestBuilder, TypeReference<JaxResponse<T>> respTypeRef) {
        Exception last = null;
        for (int i = 0; i < addresses.size(); i++) {
            String address = addresses.get(i);
            HttpRequest request = requestBuilder.build(address);
            String url = request.getUrl();
            try {
                HttpResponse response = request.execute();
                if (!response.isOk()) {
                    String error = "http call jax failed, http request url: " + url
                            + ", http response status: " + response.getStatus()
                            + ", http response body: " + response.body();
                    throw new HttpException(error);
                }
                return JsonUtil.decode(response.body(), respTypeRef);
            } catch (Exception ex) {
                last = ex;
                String nextInfo = "";
                if (i < addresses.size() - 1) {
                    nextInfo = ", will retry next address: " + addresses.get(i + 1);
                }
                LOGGER.warn("call jax http api {} failed" + nextInfo, url, ex);
            }
        }
        throw new JaxClientException(LifecycleCode.JAX_CLIENT_FAILED, last);
    }

    interface RequestBuilder {

        /**
         * build http request
         *
         * @param address
         * @return
         */
        HttpRequest build(String address);
    }
}
