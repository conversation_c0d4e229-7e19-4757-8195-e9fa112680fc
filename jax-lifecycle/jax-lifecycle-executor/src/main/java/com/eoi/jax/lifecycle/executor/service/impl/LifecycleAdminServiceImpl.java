package com.eoi.jax.lifecycle.executor.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.eoi.jax.lifecycle.api.JobContext;
import com.eoi.jax.lifecycle.api.TaskContext;
import com.eoi.jax.lifecycle.exception.TaskException;
import com.eoi.jax.lifecycle.executor.client.JaxAdminClient;
import com.eoi.jax.lifecycle.executor.config.AppConfig;
import com.eoi.jax.lifecycle.executor.core.LifecycleVersion;
import com.eoi.jax.lifecycle.executor.exception.LifecycleCode;
import com.eoi.jax.lifecycle.executor.exception.LifecycleException;
import com.eoi.jax.lifecycle.executor.model.*;
import com.eoi.jax.lifecycle.executor.service.LifecycleAdminService;
import com.eoi.jax.lifecycle.executor.util.JsonUtil;
import com.eoi.jax.lifecycle.executor.util.MetricUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/10/29
 */
@Service
public class LifecycleAdminServiceImpl implements LifecycleAdminService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LifecycleAdminServiceImpl.class);

    @Autowired
    private AppConfig appConfig;

    @Override
    public JaxAdminClient getAdminClient() {
        return new JaxAdminClient(
                appConfig.getLifecycle().getAdmin().getAddresses(),
                appConfig.getLifecycle().getAdmin().getAppKey(),
                appConfig.getLifecycle().getAdmin().getSecretKey()
        );
    }

    @Override
    public ExecutorInfo getExecutorInfo(ExecutorInfo executor) {
        try {
            String listenAddress = appConfig.getServer().getListenHostPort();
            executor.setExecutorAddress(listenAddress);
            executor.setExecutorTime(System.currentTimeMillis());
            executor.setExecutorVersion(
                    LifecycleVersion.getGitBuildVersion() + "@" + LifecycleVersion.getGitCommitShortId()
            );
            return executor;
        } catch (Exception ex) {
            throw new LifecycleException(LifecycleCode.JAX_CLIENT_FAILED, ex);
        }
    }

    @Override
    public void registerExecutor() {
        LOGGER.info("注册执行器");
        ExecutorInfo executor = getExecutorInfo(new ExecutorInfo());
        getAdminClient().register(executor);
        LOGGER.info("注册执行器成功: " + JsonUtil.encode(executor));
    }

    @Override
    public void unregisterExecutor() {
        LOGGER.info("注销执行器");
        ExecutorInfo executor = getExecutorInfo(new ExecutorInfo());
        getAdminClient().unregister(executor);
        LOGGER.info("注销执行器成功: " + JsonUtil.encode(executor));
    }

    @Override
    public void heartbeat() {
        LOGGER.info("执行器心跳");
        HeartbeatInfo info = new HeartbeatInfo();
        getExecutorInfo(info);
        Metric metric = MetricUtil.get();
        info.setMetric(metric);
        getAdminClient().heartbeat(info);
        LOGGER.info("执行器心跳成功: " + JsonUtil.encode(info));
    }

    @Override
    public void startJob(Long jobId) {
        LOGGER.info("[job-{}] 尝试锁定任务", jobId);
        LifecycleJob job = new LifecycleJob();
        getExecutorInfo(job);
        job.setJobId(jobId);
        getAdminClient().startJob(job);
        LOGGER.info("[job-{}] 成功锁定任务", jobId);
    }

    @Override
    public void jobStart(JobContext jobCtx) {
        Long jobId = jobCtx.getJobId();
        LOGGER.trace("[job-{}] job started", jobId);
    }

    @Override
    public void jobStop(JobContext jobCtx) {
        Long jobId = jobCtx.getJobId();
        LOGGER.info("[job-{}] [STOPPED] 执行器上报job状态", jobId);
        try {
            LifecycleJob job = new LifecycleJob();
            getExecutorInfo(job);
            job.setJobId(jobId);
            getAdminClient().jobStopped(job);
        } catch (Exception ex) {
            LOGGER.error("[job-{}] [STOPPED] 执行器上报job状态失败", jobId, ex);
        }
    }

    @Override
    public void jobSuccess(JobContext jobCtx) {
        Long jobId = jobCtx.getJobId();
        LOGGER.info("[job-{}] [SUCCESS] 执行器上报job状态", jobId);
        try {
            LifecycleJob job = new LifecycleJob();
            getExecutorInfo(job);
            job.setJobId(jobId);
            getAdminClient().jobSuccess(job);
        } catch (Exception ex) {
            LOGGER.error("[job-{}] [SUCCESS] 执行器上报job状态失败", jobId, ex);
        }
    }

    @Override
    public void jobFailed(JobContext jobCtx, TaskException failure) {
        Long jobId = jobCtx.getJobId();
        LOGGER.error("[job-{}] [FAILED] 执行器上报job状态", jobId);
        try {
            LifecycleJobFailure job = new LifecycleJobFailure();
            getExecutorInfo(job);
            job.setJobId(jobId);
            job.setError(failure.code().name());
            job.setMessage(failure.message());
            job.setStackTrace(ExceptionUtil.stacktraceToString(failure));
            getAdminClient().jobFailed(job);
        } catch (Exception ex) {
            LOGGER.error("[job-{}] [FAILED] 执行器上报job状态失败", jobId, ex);
        }
    }

    @Override
    public void taskStart(JobContext jobCtx, TaskContext taskCtx) {
        Long jobId = jobCtx.getJobId();
        Long taskId = taskCtx.getTaskId();
        String taskName = taskCtx.getTaskName();
        LOGGER.info("[job-{}-{}-{}] [RUNNING] 执行器上报task状态", jobId, taskName, taskId);
        try {
            LifecycleTask task = new LifecycleTask();
            getExecutorInfo(task);
            task.setJobId(jobId);
            task.setTaskId(taskId);
            getAdminClient().taskStarted(task);
        } catch (Exception ex) {
            LOGGER.error("[job-{}-{}-{}] [RUNNING] 执行器上报task状态失败", jobId, taskName, taskId, ex);
        }
    }

    @Override
    public void taskStop(JobContext jobCtx, TaskContext taskCtx) {
        Long jobId = jobCtx.getJobId();
        Long taskId = taskCtx.getTaskId();
        String taskName = taskCtx.getTaskName();
        LOGGER.info("[job-{}-{}-{}] [STOPPED] 执行器上报task状态", jobId, taskName, taskId);
        try {
            LifecycleTask task = new LifecycleTask();
            getExecutorInfo(task);
            task.setJobId(jobId);
            task.setTaskId(taskId);
            getAdminClient().taskStopped(task);
        } catch (Exception ex) {
            LOGGER.error("[job-{}-{}-{}] [STOPPED] 执行器上报task状态失败", jobId, taskName, taskId, ex);
        }
    }

    @Override
    public void taskSuccess(JobContext jobCtx, TaskContext taskCtx) {
        Long jobId = jobCtx.getJobId();
        Long taskId = taskCtx.getTaskId();
        String taskName = taskCtx.getTaskName();
        LOGGER.info("[job-{}-{}-{}] [SUCCESS] 执行器上报task状态", jobId, taskName, taskId);
        try {
            LifecycleTaskResult task = new LifecycleTaskResult();
            getExecutorInfo(task);
            task.setJobId(jobId);
            task.setTaskId(taskId);
            task.setResult(taskCtx.getResult().toMap());
            getAdminClient().taskSuccess(task);
        } catch (Exception ex) {
            LOGGER.error("[job-{}-{}-{}] [SUCCESS] 执行器上报task状态失败", jobId, taskName, taskId, ex);
        }
    }

    @Override
    public void taskFailed(JobContext jobCtx, TaskContext taskCtx, TaskException failure) {
        Long jobId = jobCtx.getJobId();
        Long taskId = taskCtx.getTaskId();
        String taskName = taskCtx.getTaskName();
        LOGGER.error("[job-{}-{}-{}] [FAILED] 执行器上报task状态", jobId, taskName, taskId);
        try {
            LifecycleTaskFailure task = new LifecycleTaskFailure();
            getExecutorInfo(task);
            task.setJobId(jobId);
            task.setTaskId(taskId);
            task.setError(failure.code().name());
            task.setMessage(failure.message());
            task.setStackTrace(ExceptionUtil.stacktraceToString(failure));
            getAdminClient().taskFailed(task);
        } catch (Exception ex) {
            LOGGER.error("[job-{}-{}-{}] [FAILED] 执行器上报task状态失败", jobId, taskName, taskId, ex);
        }
    }
}
