package com.eoi.jax.lifecycle.executor.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2024/10/31
 */
@Configuration
public class AppConfig {
    public static final String JAX_LIFECYCLE_EXECUTOR_LOG_DIR = "JAX_LIFECYCLE_EXECUTOR_LOG_DIR";

    @Autowired
    private ServerConfig server;
    @Autowired
    private LifecycleConfig lifecycle;

    public ServerConfig getServer() {
        return server;
    }

    public LifecycleConfig getLifecycle() {
        return lifecycle;
    }
}
