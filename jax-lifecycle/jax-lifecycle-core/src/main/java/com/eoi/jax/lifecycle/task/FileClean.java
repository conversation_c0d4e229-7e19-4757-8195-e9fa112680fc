package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.api.ILifecycleTaskExecutor;
import com.eoi.jax.lifecycle.api.JobContext;
import com.eoi.jax.lifecycle.api.TaskContext;
import com.eoi.jax.lifecycle.core.BaseTaskExecutor;
import com.eoi.jax.lifecycle.exception.ErrorCode;
import com.eoi.jax.lifecycle.exception.TaskException;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @Date 2024/11/5
 */
public class File<PERSON>lean
        extends BaseTaskExecutor<FileCleanConfig, FileCleanResult>
        implements ILifecycleTaskExecutor<FileCleanConfig, FileCleanResult> {
    @Override
    public void stop() {
        // todo
    }

    @Override
    public FileCleanResult exec(
            JobContext job,
            TaskContext task,
            FileCleanConfig config,
            long timeoutMs
    ) throws TaskException {
        log("开始清理文件: " + config.getCleanFilePath());
        Path path = Paths.get(config.getCleanFilePath());
        if (!Files.exists(path)) {
            FileCleanResult result = new FileCleanResult();
            result.setCleanFilePath(config.getCleanFilePath());
            return result;
        }
        if (!Files.isRegularFile(path)) {
            throw new TaskException(ErrorCode.TASK_FAILED, "清理目标不是文件: " + config.getCleanFilePath());
        }
        if (!Files.isWritable(path)) {
            throw new TaskException(ErrorCode.TASK_FAILED, "没有权限删除文件: " + config.getCleanFilePath());
        }
        try {
            FileCleanResult result = new FileCleanResult();
            result.setCleanFilePath(config.getCleanFilePath());
            result.setCleanFileBytes(Files.size(path));
            Files.delete(path);
            return result;
        } catch (Exception e) {
            throw new TaskException(ErrorCode.TASK_FAILED, "清理文件失败: " + config.getCleanFilePath(), e);
        }
    }
}
