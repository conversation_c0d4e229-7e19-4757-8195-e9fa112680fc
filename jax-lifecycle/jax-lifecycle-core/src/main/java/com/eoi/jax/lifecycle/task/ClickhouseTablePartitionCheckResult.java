package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.core.IMapTaskResult;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 */
public class ClickhouseTablePartitionCheckResult implements IMapTaskResult {
    private boolean clickhousePartitionExists;
    private String clickhouseNode;
    private String clickhouseDatabase;
    private String clickhouseTable;
    private String clickhousePartition;
    private Long clickhousePartitionBytesOnDisk;
    private Long clickhousePartitionDataCompressedBytes;
    private Long clickhousePartitionDataUncompressedBytes;

    public boolean getClickhousePartitionExists() {
        return clickhousePartitionExists;
    }

    public void setClickhousePartitionExists(boolean clickhousePartitionExists) {
        this.clickhousePartitionExists = clickhousePartitionExists;
    }

    public String getClickhouseNode() {
        return clickhouseNode;
    }

    public void setClickhouseNode(String clickhouseNode) {
        this.clickhouseNode = clickhouseNode;
    }

    public String getClickhouseDatabase() {
        return clickhouseDatabase;
    }

    public void setClickhouseDatabase(String clickhouseDatabase) {
        this.clickhouseDatabase = clickhouseDatabase;
    }

    public String getClickhouseTable() {
        return clickhouseTable;
    }

    public void setClickhouseTable(String clickhouseTable) {
        this.clickhouseTable = clickhouseTable;
    }

    public String getClickhousePartition() {
        return clickhousePartition;
    }

    public void setClickhousePartition(String clickhousePartition) {
        this.clickhousePartition = clickhousePartition;
    }

    public Long getClickhousePartitionBytesOnDisk() {
        return clickhousePartitionBytesOnDisk;
    }

    public void setClickhousePartitionBytesOnDisk(Long clickhousePartitionBytesOnDisk) {
        this.clickhousePartitionBytesOnDisk = clickhousePartitionBytesOnDisk;
    }

    public Long getClickhousePartitionDataCompressedBytes() {
        return clickhousePartitionDataCompressedBytes;
    }

    public void setClickhousePartitionDataCompressedBytes(Long clickhousePartitionDataCompressedBytes) {
        this.clickhousePartitionDataCompressedBytes = clickhousePartitionDataCompressedBytes;
    }

    public Long getClickhousePartitionDataUncompressedBytes() {
        return clickhousePartitionDataUncompressedBytes;
    }

    public void setClickhousePartitionDataUncompressedBytes(Long clickhousePartitionDataUncompressedBytes) {
        this.clickhousePartitionDataUncompressedBytes = clickhousePartitionDataUncompressedBytes;
    }
}
