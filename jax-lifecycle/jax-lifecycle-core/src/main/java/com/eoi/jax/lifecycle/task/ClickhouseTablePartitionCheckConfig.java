package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.annotation.Required;
import com.eoi.jax.lifecycle.core.IMapTaskConfig;
import com.eoi.jax.lifecycle.core.IValidateConfig;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 */
public class ClickhouseTablePartitionCheckConfig
        extends ClickhouseTableCheckConfig
        implements IMapTaskConfig, IValidateConfig {
    @Required
    private String clickhouseLocalTable;
    @Required
    private String clickhousePartition;

    public String getClickhouseLocalTable() {
        return clickhouseLocalTable;
    }

    public void setClickhouseLocalTable(String clickhouseLocalTable) {
        this.clickhouseLocalTable = clickhouseLocalTable;
    }

    public String getClickhousePartition() {
        return clickhousePartition;
    }

    public void setClickhousePartition(String clickhousePartition) {
        this.clickhousePartition = clickhousePartition;
    }
}
