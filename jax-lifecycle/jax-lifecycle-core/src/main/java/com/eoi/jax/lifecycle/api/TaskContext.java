package com.eoi.jax.lifecycle.api;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/10/14
 */
public class TaskContext implements Serializable {
    private Long jobId;
    private Long taskId;
    private String task;
    private String taskName;
    private Long timeoutMs;
    private ITaskConfig config;
    private ITaskResult result;
    private Boolean success;

    public Long getJobId() {
        return this.jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getTaskId() {
        return this.taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTask() {
        return this.task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public String getTaskName() {
        return this.taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Long getTimeoutMs() {
        return timeoutMs;
    }

    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }

    public ITaskConfig getConfig() {
        return this.config;
    }

    public void setConfig(ITaskConfig config) {
        this.config = config;
    }

    public ITaskResult getResult() {
        return this.result;
    }

    public void setResult(ITaskResult result) {
        this.result = result;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
