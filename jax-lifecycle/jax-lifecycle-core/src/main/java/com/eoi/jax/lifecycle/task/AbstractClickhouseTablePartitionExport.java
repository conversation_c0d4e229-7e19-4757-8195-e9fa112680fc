package com.eoi.jax.lifecycle.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.eoi.jax.lifecycle.api.ILifecycleTaskExecutor;
import com.eoi.jax.lifecycle.api.ITaskConfig;
import com.eoi.jax.lifecycle.api.ITaskResult;
import com.eoi.jax.lifecycle.api.JobContext;
import com.eoi.jax.lifecycle.core.BaseTaskExecutor;
import com.eoi.jax.lifecycle.exception.ErrorCode;
import com.eoi.jax.lifecycle.exception.ProcessException;
import com.eoi.jax.lifecycle.exception.TaskException;
import com.eoi.jax.lifecycle.exception.TaskInvalidException;
import com.eoi.jax.lifecycle.util.*;

import java.io.IOException;
import java.nio.file.FileStore;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

/**
 * @param <C> 参数
 * @param <R> 结果
 * <AUTHOR>
 * @Date 2024/12/6
 */
public abstract class AbstractClickhouseTablePartitionExport<C extends ITaskConfig, R extends ITaskResult>
        extends BaseTaskExecutor<C, R>
        implements ILifecycleTaskExecutor<C, R> {

    public ClickhouseDatasourceCheckResult getClickhouseDatasourceCheckResult(JobContext job) {
        ClickhouseDatasourceCheckResult dsResult = job.getTaskResult(ClickhouseDatasourceCheckResult.class);
        if (dsResult == null) {
            throw new TaskException(ErrorCode.TASK_DEPEND_FAILED, "未找到Clickhouse数据源检查任务的结果");
        }
        return dsResult;
    }

    public ClickhouseTablePartitionCheckResult getClickhouseTablePartitionCheckResult(JobContext job) {
        ClickhouseTablePartitionCheckResult partitionResult =
                job.getTaskResult(ClickhouseTablePartitionCheckResult.class);
        if (partitionResult == null) {
            throw new TaskException(ErrorCode.TASK_DEPEND_FAILED, "未找到Clickhouse分区检查任务的结果");
        }
        if (partitionResult.getClickhousePartitionBytesOnDisk() == null
                || partitionResult.getClickhousePartitionDataCompressedBytes() == null
                || partitionResult.getClickhousePartitionDataUncompressedBytes() == null) {
            throw new TaskException(ErrorCode.TASK_DEPEND_FAILED, "未找到Clickhouse分区大小");
        }
        return partitionResult;
    }

    public ProcessArgs exportArgs(
            String node,
            Integer port,
            String userName,
            String password,
            String exportSql,
            Map<String, String> exportSettings
    ) {
        ProcessArgs arguments = LifecycleTool.clickhouseClientArgs(node, port, userName, password, exportSql);
        arguments.add(ProcessArg.of("--multiquery"));
        arguments.add(ProcessArg.of("--multiline"));
        if (CollUtil.isNotEmpty(exportSettings)) {
            for (Map.Entry<String, String> entry : exportSettings.entrySet()) {
                arguments.add(ProcessArg.of("--" + entry.getKey() + "=" + entry.getValue()));
            }
        }
        return arguments;
    }

    public ClickhouseTablePartitionExportResult exportSql(
            ProcessEasy process,
            long timeoutMs,
            ProcessArgs arguments,
            String outputDir,
            String outputFilePath,
            String outputFileName
    ) {
        long startMs = System.currentTimeMillis();

        log("执行命令: " + process.getProgram() + " " + arguments);
        try {
            int existValue = process.exec(arguments, timeoutMs);
            long endMs = System.currentTimeMillis();
            long duration = endMs - startMs;
            log("Clickhouse分区数据导出耗时: " + duration + "毫秒, 进程结束返回值: " + existValue);
            if (existValue != 0) {
                throw new ProcessException(
                        "exec failed [" + process.getProgram() + " " + arguments + "] exist value " + existValue);
            }
            long exportBytes = Files.size(Paths.get(outputFilePath));
            if (exportBytes > 1024 && duration > 1000) {
                log("Clickhouse分区数据导出速度: " + EasyUtil.formatBytes(exportBytes * 1000 / duration) + "/秒");
            }
            ClickhouseTablePartitionExportResult result = new ClickhouseTablePartitionExportResult();
            result.setClickhouseExportDir(outputDir);
            result.setClickhouseExportPath(outputFilePath);
            result.setClickhouseExportFile(outputFileName);
            result.setClickhouseExportBytes(exportBytes);
            return result;
        } catch (Throwable ex) {
            throw new TaskException(ErrorCode.TASK_FAILED, "导出分区失败", ex);
        }
    }

    public void checkOutputDir(String outputDir, String outputFilePath, Long needSpace) throws TaskException {
        if (!EasyUtil.isValidatedPath(outputDir)) {
            throw new TaskException(ErrorCode.TASK_INVALID, "导出文件目录不合法: " + outputDir);
        }
        if (!EasyUtil.isValidatedPath(outputFilePath)) {
            throw new TaskException(ErrorCode.TASK_INVALID, "导出文件路径不合法: " + outputFilePath);
        }
        Path dir = Paths.get(outputDir);
        if (!Files.exists(dir)) {
            try {
                Files.createDirectories(dir);
            } catch (IOException e) {
                throw new TaskException(ErrorCode.TASK_FAILED, "导出目录不可写: " + outputDir, e);
            }
        } else if (!Files.isDirectory(dir)) {
            throw new TaskException(ErrorCode.TASK_FAILED, "导出目录不是目录: " + outputDir);
        }
        if (!Files.isWritable(dir)) {
            throw new TaskException(ErrorCode.TASK_FAILED, "导出目录不可写: " + outputDir);
        }
        long usableSpace;
        try {
            FileStore fileStore = Files.getFileStore(dir);
            usableSpace = fileStore.getUsableSpace();
        } catch (IOException e) {
            throw new TaskException(ErrorCode.TASK_FAILED, "导出目录无法计算剩余空间: " + outputDir, e);
        }
        String usableSpaceReadable = EasyUtil.formatBytes(usableSpace) + "(" + usableSpace + " bytes)";
        log("磁盘剩余空间: " + usableSpaceReadable);
        String needSpaceReadable = EasyUtil.formatBytes(needSpace) + "(" + needSpace + " bytes)";
        log("预计导出文件需要磁盘空间大小: " + needSpaceReadable);
        if (usableSpace < needSpace) {
            throw new TaskException(ErrorCode.TASK_FAILED, "导出目录空间不足: " + outputDir
                    + ", 剩余空间 " + usableSpaceReadable + ", 需要 " + needSpaceReadable);
        } else {
            log("磁盘剩余空间充足, 开始导出");
        }
        if (FileUtil.exist(outputFilePath)) {
            try {
                log("检测到导出文件已存在，清理文件: " + outputFilePath);
                FileUtil.del(outputFilePath);
            } catch (Exception e) {
                throw new TaskException(ErrorCode.TASK_FAILED, "清理文件失败: " + outputFilePath, e);
            }
        }
    }

    public String toyyyymdd(String date) {
        if (date.length() == 8 && date.matches("\\d+")) {
            return date;
        }
        if (date.length() == 10 && date.matches("\\d+-\\d+-\\d+")) {
            return date;
        }
        throw new TaskInvalidException("分区格式只支持yyyyMMdd或yyyy-MM-dd: " + date);
    }
}
