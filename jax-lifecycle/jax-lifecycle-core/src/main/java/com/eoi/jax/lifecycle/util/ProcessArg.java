package com.eoi.jax.lifecycle.util;

/**
 * <AUTHOR>
 * @Date 2024/11/15
 */
public class ProcessArg {
    private String value;
    private boolean isSecret;

    public static ProcessArg of(String value) {
        ProcessArg arg = new ProcessArg();
        arg.setValue(value);
        arg.setIsSecret(false);
        return arg;
    }

    public static ProcessArg of(String value, boolean isSecret) {
        ProcessArg arg = new ProcessArg();
        arg.setValue(value);
        arg.setIsSecret(isSecret);
        return arg;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean getIsSecret() {
        return isSecret;
    }

    public void setIsSecret(boolean isSecret) {
        this.isSecret = isSecret;
    }

    @Override
    public String toString() {
        if (isSecret) {
            return SecretMaskUtil.mask(value);
        }
        return value;
    }
}
