package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.annotation.Required;
import com.eoi.jax.lifecycle.core.IMapTaskConfig;
import com.eoi.jax.lifecycle.core.IValidateConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/14
 */
public class ClickhouseDatasourceCheckConfig implements IMapTaskConfig, IValidateConfig {

    @Required
    private List<String> clickhouseNodes;
    @Required
    private Integer clickhousePort;
    @Required
    private Integer clickhouseHttpPort;
    @Required
    private String clickhouseUsername;
    @Required
    private String clickhousePassword;
    private Map<String, String> clickhouseJdbcPros;

    public List<String> getClickhouseNodes() {
        return clickhouseNodes;
    }

    public void setClickhouseNodes(List<String> clickhouseNodes) {
        this.clickhouseNodes = clickhouseNodes;
    }

    public Integer getClickhousePort() {
        return clickhousePort;
    }

    public void setClickhousePort(Integer clickhousePort) {
        this.clickhousePort = clickhousePort;
    }

    public Integer getClickhouseHttpPort() {
        return clickhouseHttpPort;
    }

    public void setClickhouseHttpPort(Integer clickhouseHttpPort) {
        this.clickhouseHttpPort = clickhouseHttpPort;
    }

    public String getClickhouseUsername() {
        return clickhouseUsername;
    }

    public void setClickhouseUsername(String clickhouseUsername) {
        this.clickhouseUsername = clickhouseUsername;
    }

    public String getClickhousePassword() {
        return clickhousePassword;
    }

    public void setClickhousePassword(String clickhousePassword) {
        this.clickhousePassword = clickhousePassword;
    }

    public Map<String, String> getClickhouseJdbcPros() {
        return clickhouseJdbcPros;
    }

    public void setClickhouseJdbcPros(Map<String, String> clickhouseJdbcPros) {
        this.clickhouseJdbcPros = clickhouseJdbcPros;
    }
}
