package com.eoi.jax.lifecycle.util;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/10/15
 */
public class JdbcEasy {
    private String url;
    private String username;
    private String password;
    private Connection connection;
    private boolean stop = false;
    private boolean running = false;

    public JdbcEasy(String url, String username, String password) {
        this.url = url;
        this.username = username;
        this.password = password;
    }

    public String getUrl() {
        return url;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public Connection getConnection() {
        return connection;
    }

    public boolean isStop() {
        return stop;
    }

    public boolean isRunning() {
        return running;
    }

    public void close() throws SQLException {
        stop = true;
        if (running && connection != null && !connection.isClosed()) {
            connection.close();
        }
    }

    public List<Map<String, String>> query(String sql) throws SQLException {
        List<Map<String, String>> result = new ArrayList<>();
        try (Connection connTmp = DriverManager.getConnection(url, username, password)) {
            running = true;
            connection = connTmp;
            try (Statement preparedStatement = connTmp.createStatement()) {
                try (ResultSet resultSet = preparedStatement.executeQuery(sql)) {
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    List<String> columnNames = new LinkedList<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        String columnLabel = metaData.getColumnLabel(i);
                        columnNames.add(columnLabel == null ? columnName : columnLabel);
                    }
                    while (!stop && resultSet.next()) {
                        Map<String, String> row = new LinkedHashMap<>();
                        for (int i = 1; i <= columnCount; i++) {
                            Object object = resultSet.getObject(i);
                            String columnName = columnNames.get(i - 1);
                            String columnValue = object == null ? null : String.valueOf(object);
                            row.put(columnName, columnValue);
                        }
                        result.add(row);
                    }
                }
            }
        } finally {
            connection = null;
            running = false;
        }
        return result;
    }
}
