package com.eoi.jax.lifecycle.util;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @Date 2024/10/16
 */
public class ProcessOutputStream extends OutputStream {
    private ByteArrayOutputStream buffer = new ByteArrayOutputStream(4096);
    private ProcessOutputReader reader;

    public ProcessOutputStream(ProcessOutputReader reader) {
        this.reader = reader;
    }

    public void write(int b) {
        if (b != 13 && b != 10) {
            this.buffer.write(b);
        } else {
            String line = this.buffer.toString();
            this.reader.readLine(line);
            this.buffer.reset();
        }

    }

    public void write(byte[] b, int off, int len) {
        for (int i = off; i < len; ++i) {
            this.write(b[i]);
        }
    }
}
