package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.api.JobContext;
import com.eoi.jax.lifecycle.api.TaskContext;
import com.eoi.jax.lifecycle.exception.TaskException;

/**
 * <AUTHOR>
 * @Date 2024/10/30
 */
public class ClickhouseTablePartitionCheckNotExist extends AbstractClickhouseTablePartitionCheck {
    @Override
    public ClickhouseTablePartitionCheckResult exec(
            JobContext job,
            TaskContext task,
            ClickhouseTablePartitionCheckConfig config,
            long timeoutMs
    ) throws TaskException {
        return exec(job, task, config, timeoutMs, false);
    }
}
