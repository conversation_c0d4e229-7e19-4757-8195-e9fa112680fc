package com.eoi.jax.lifecycle.core;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.eoi.jax.lifecycle.api.*;
import com.eoi.jax.lifecycle.exception.TaskException;
import com.eoi.jax.lifecycle.exception.TaskInterruptedException;
import com.eoi.jax.lifecycle.exception.TaskTimeoutException;
import com.eoi.jax.lifecycle.util.SecretMaskUtil;
import org.joda.time.DateTime;

import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @param <C> 参数
 * @param <R> 结果
 * <AUTHOR>
 * @Date 2024/10/15
 */
public abstract class BaseTaskExecutor<C extends ITaskConfig, R extends ITaskResult>
        implements ILifecycleTaskExecutor<C, R> {
    private ILifecycleLogger logger;
    private Long jobId;
    private Long taskId;
    private String taskName;
    private boolean running = false;
    private long startTime;
    private long endTime;
    private boolean stopped = false;
    private boolean success = true;
    private CompletableFuture<TaskFutureResult<R>> future;

    public ILifecycleLogger getLogger() {
        return logger;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public boolean isRunning() {
        return this.running;
    }

    public long getStartTime() {
        return this.startTime;
    }

    public long getEndTime() {
        return this.endTime;
    }

    public boolean isStopped() {
        return stopped;
    }

    public boolean isSuccess() {
        return success;
    }

    public void log(String message) {
        this.logger.log(this.getClass(), this.jobId, this.taskId, this.taskName, formatLog(message));
    }

    public void log(String message, Throwable exception) {
        this.logger.log(this.getClass(), this.jobId, this.taskId, this.taskName, formatLog(message, exception));
    }

    public void logOutput(String message) {
        this.logger.log(this.getClass(), this.jobId, this.taskId, this.taskName, formatOutput(message));
    }

    private String formatLog(String message) {
        return new DateTime().toString("yyyy-MM-dd HH:mm:ss")
                + " [" + getTaskName() + "] "
                + " [thread-" + Thread.currentThread().getId() + "] "
                + message;
    }

    private String formatLog(String message, Throwable exception) {
        return new DateTime().toString("yyyy-MM-dd HH:mm:ss")
                + " [" + getTaskName() + "] "
                + " [thread-" + Thread.currentThread().getId() + "] "
                + message
                + "\n" + ExceptionUtil.stacktraceToString(exception);
    }

    private String formatOutput(String message) {
        return "[" + JaxLifecycleEnv.getCurrentUser() + "(thread-" + Thread.currentThread().getId() + ")]# " + message;
    }

    @Override
    public void close() {
        log("准备终止任务");
        this.stop();
        this.stopped = true;
        if (this.running && this.future != null) {
            log("开始终止任务");
            this.future.cancel(true);
        } else {
            log("任务未运行, 直接结束");
        }
    }

    @Override
    public R execute(JobContext job, TaskContext task, C config, long timeoutMs) throws TaskException {
        this.startTime = System.currentTimeMillis();
        this.jobId = task.getJobId();
        this.taskId = task.getTaskId();
        this.taskName = task.getTaskName();
        log("开始执行任务, 参数: " + JSONUtil.toJsonStr(SecretMaskUtil.mask(config)));
        try {
            config.validate();
        } catch (TaskException ex) {
            log("任务参数校验失败" + JSONUtil.toJsonStr(SecretMaskUtil.mask(config)), ex);
            throw TaskException.causeBy(ex);
        }

        R result = null;
        try {
            result = executeOrTimeout(job, task, config, timeoutMs);
            task.setSuccess(true);
            this.endTime = System.currentTimeMillis();
            long duration = this.endTime - this.startTime;
            log("任务执行成功, 耗时: " + duration + "ms, 结果: " + JSONUtil.toJsonStr(SecretMaskUtil.mask(result)));
            success = true;
        } catch (Throwable ex) {
            success = false;
            task.setSuccess(false);
            this.endTime = System.currentTimeMillis();
            long duration = this.endTime - this.startTime;
            if (stopped) {
                log("任务执行被终止, 耗时: " + duration + "ms, 错误: ", ex);
                throw new TaskInterruptedException("任务执行被终止", ex);
            }
            long cost = System.currentTimeMillis() - this.startTime;
            if (cost >= timeoutMs) {
                log("任务执行超时, 耗时: " + duration + "ms, 错误: ", ex);
                throw new TaskTimeoutException("任务执行超时" + timeoutMs + "ms", ex);
            }
            log("任务执行失败, 耗时: " + duration + "ms, 错误: ", ex);
            throw TaskException.causeBy(ex);
        } finally {
            if (!success) {
                try {
                    log("开始清理任务");
                    this.close();
                } catch (Throwable e) {
                    log("清理任务失败", e);
                }
            }
            this.running = false;
        }
        return result;
    }

    @Override
    public void registerLogger(ILifecycleLogger logger) {
        this.logger = logger;
    }

    public R executeOrTimeout(JobContext job, TaskContext task, C config, long timeoutMs) throws TaskException {
        this.future = CompletableFuture.supplyAsync(() -> {
            this.running = true;
            R result = null;
            try {
                result = this.exec(job, task, config, timeoutMs);
            } catch (Throwable ex) {
                TaskException exception = TaskException.causeBy(ex);
                return new TaskFutureResult<>(exception);
            } finally {
                this.running = false;
            }
            return new TaskFutureResult<>(result);
        });
        TaskFutureResult<R> result = null;
        try {
            result = this.future.get(timeoutMs, TimeUnit.MILLISECONDS);
        } catch (InterruptedException | CancellationException ex) {
            throw new TaskInterruptedException("任务执行被终止", ex);
        } catch (TimeoutException ex) {
            throw new TaskTimeoutException("任务执行超时" + timeoutMs + "ms", ex);
        } catch (Throwable ex) {
            throw TaskException.causeBy(ex);
        }
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            throw result.getException();
        }
    }

    /**
     * 停止任务
     */
    public abstract void stop();

    /**
     * 执行任务
     * 执行成功时, 返回任务结果
     * 执行失败时, 抛出TaskException, 注意必须将所有程序异常归类为TaskException
     *
     * @param job       Job上下文, 包含任务列表
     * @param task      Task上下文, Job上下文的任务列表中的一个
     * @param config    任务配置
     * @param timeoutMs 超时时间, 单位ms。如果内部使用了异步处理, 可以使用该配置来控制超时
     * @return 任务结果
     * @throws TaskException 执行异常
     */
    public abstract R exec(JobContext job, TaskContext task, C config, long timeoutMs) throws TaskException;
}
