package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.annotation.Required;
import com.eoi.jax.lifecycle.core.IMapTaskConfig;
import com.eoi.jax.lifecycle.core.IValidateConfig;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 */
public class HiveDatasourceCheckConfig implements IMapTaskConfig, IValidateConfig {
    @Required
    private String hiveJdbcUrl;
    private String hiveUsername;
    private String hivePassword;

    public String getHiveJdbcUrl() {
        return hiveJdbcUrl;
    }

    public void setHiveJdbcUrl(String hiveJdbcUrl) {
        this.hiveJdbcUrl = hiveJdbcUrl;
    }

    public String getHiveUsername() {
        return hiveUsername;
    }

    public void setHiveUsername(String hiveUsername) {
        this.hiveUsername = hiveUsername;
    }

    public String getHivePassword() {
        return hivePassword;
    }

    public void setHivePassword(String hivePassword) {
        this.hivePassword = hivePassword;
    }
}
