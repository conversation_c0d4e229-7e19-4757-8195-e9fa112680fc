package com.eoi.jax.lifecycle.util;

import cn.hutool.core.exceptions.ExceptionUtil;
import org.apache.commons.exec.ExecuteWatchdog;
import org.apache.commons.exec.Watchdog;
import org.joda.time.DateTime;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Date 2024/11/8
 */
public class ProcessWatch extends ExecuteWatchdog {
    private final ThreadPoolExecutor pool;
    private ProcessOutputReader reader;
    private Process process;
    private boolean timeout;
    private boolean forceKilled;

    public ProcessWatch(long timeout, ProcessOutputReader reader) {
        super(timeout);
        this.reader = reader;
        this.pool = create();
    }

    private ThreadPoolExecutor create() {
        return new ThreadPoolExecutor(
                1,
                1,
                0L,
                java.util.concurrent.TimeUnit.MILLISECONDS,
                new java.util.concurrent.LinkedBlockingQueue<Runnable>(1),
                r -> new Thread(r, "lifecycle-execute-thread"),
                new ThreadPoolExecutor.AbortPolicy()
        );
    }

    @Override
    public synchronized void start(Process processToMonitor) {
        this.process = processToMonitor;
        super.start(processToMonitor);
    }

    @Override
    public synchronized void timeoutOccured(Watchdog w) {
        this.timeout = true;
        super.timeoutOccured(w);
        output("process timeout occurred " + new DateTime());
        try {
            pool.execute(() -> {
                try {
                    long start = System.currentTimeMillis();
                    while (System.currentTimeMillis() - start < 5000) {
                        Thread.sleep(1000);
                        if (!process.isAlive()) {
                            return;
                        }
                    }
                    output("destroy process forcibly " + new DateTime());
                    process.destroyForcibly();
                    forceKilled = true;
                } catch (Throwable ex) {
                    output("destroy process forcibly failed" + new DateTime(), ex);
                }
            });
        } catch (Throwable ex) {
            output("destroy process forcibly failed" + new DateTime(), ex);
        }
    }

    public boolean isTimeout() {
        return timeout;
    }

    public boolean isForceKilled() {
        return forceKilled;
    }

    private void output(String message) {
        if (reader != null) {
            reader.readLine(message);
        }
    }

    private void output(String message, Throwable ex) {
        output(message + "\n" + ExceptionUtil.stacktraceToString(ex));
    }
}
