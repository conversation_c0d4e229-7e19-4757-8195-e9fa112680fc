package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.core.IMapTaskResult;

/**
 * <AUTHOR>
 * @Date 2024/11/5
 */
public class FileCleanResult implements IMapTaskResult {
    private String cleanFilePath;
    private Long cleanFileBytes;

    public String getCleanFilePath() {
        return cleanFilePath;
    }

    public void setCleanFilePath(String cleanFilePath) {
        this.cleanFilePath = cleanFilePath;
    }

    public Long getCleanFileBytes() {
        return cleanFileBytes;
    }

    public void setCleanFileBytes(Long cleanFileBytes) {
        this.cleanFileBytes = cleanFileBytes;
    }
}
