package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.api.JobContext;
import com.eoi.jax.lifecycle.api.TaskContext;
import com.eoi.jax.lifecycle.exception.ErrorCode;
import com.eoi.jax.lifecycle.exception.TaskException;
import com.eoi.jax.lifecycle.exception.TaskInterruptedException;
import com.eoi.jax.lifecycle.exception.TaskTimeoutException;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2024/10/22
 */
public class ClickhouseTableCheckTest {
    private long jobId = 2;

    protected ClickhouseTableCheckConfig config() {
        ClickhouseTableCheckConfig config = new ClickhouseTableCheckConfig();
        config.setClickhouseNodes(new ArrayList<>(UnitTestEnv.ckNodes));
        config.setClickhouseUsername(UnitTestEnv.ckUser);
        config.setClickhousePassword(UnitTestEnv.ckPassword);
        config.setClickhousePort(UnitTestEnv.ckPort);
        config.setClickhouseHttpPort(UnitTestEnv.ckHttpPort);
        config.setClickhouseCluster(UnitTestEnv.ckCluster);
        config.setClickhouseJdbcPros(new HashMap<>(UnitTestEnv.ckJdbcPros));
        config.setClickhouseDatabase(UnitTestEnv.ckDb);
        config.setClickhouseTable(UnitTestEnv.ckTable);
        return config;
    }

    protected ClickhouseTableCheck executor() {
        ClickhouseTableCheck executor = new ClickhouseTableCheck();
        executor.registerLogger(SystemOutPrintLogger.INSTANCE);
        return executor;
    }

    private void prepareJobContext(JobContext job) {
        TaskContext firstTaskContext = new TaskContext();
        ClickhouseDatasourceCheckResult firstTaskResult = new ClickhouseDatasourceCheckResult();
        firstTaskResult.setClickhouseNode(UnitTestEnv.ckNodes.get(UnitTestEnv.ckNodes.size() - 1));
        firstTaskContext.setResult(firstTaskResult);
        job.getTasks().add(0, firstTaskContext);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void test1() {
        long taskId = 1;
        String taskName = "test" + taskId;
        ClickhouseTableCheckConfig config = config();
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        prepareJobContext(job);
        ClickhouseTableCheck executor = executor();
        ClickhouseTableCheckResult result = executor.execute(job, task, config, 10000);
        Assert.assertTrue(result.getClickhouseTableExists());
        Assert.assertEquals(config.getClickhouseDatabase(), result.getClickhouseDatabase());
        Assert.assertEquals(config.getClickhouseTable(), result.getClickhouseTable());
        Assert.assertTrue(executor.getStartTime() != 0);
        Assert.assertTrue(executor.getEndTime() != 0);
        Assert.assertFalse(executor.isRunning());
        Assert.assertFalse(executor.isStopped());
        Assert.assertTrue(executor.isSuccess());
    }

    /**
     * 测试失败情况
     */
    @Test
    public void test2() {
        long taskId = 2;
        String taskName = "test" + taskId;
        ClickhouseTableCheckConfig config = config();
        config.setClickhouseTable("table_not_exit_just_for_test");
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        prepareJobContext(job);
        ClickhouseTableCheck executor = executor();
        Exception ex = null;
        try {
            executor.execute(job, task, config, 10000);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof TaskException);
        Assert.assertEquals(ErrorCode.CLICKHOUSE_TABLE_NOT_EXISTS, ((TaskException) ex).code());
        Assert.assertFalse(executor.isSuccess());
    }

    /**
     * 测试没有上游结果
     */
    @Test
    public void test3() {
        long taskId = 3;
        String taskName = "test" + taskId;
        ClickhouseTableCheckConfig config = config();
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        ClickhouseTableCheck executor = executor();
        Exception ex = null;
        try {
            executor.execute(job, task, config, 10000);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof TaskException);
        Assert.assertEquals(ErrorCode.TASK_DEPEND_FAILED, ((TaskException) ex).code());
    }

    /**
     * 测试终止任务
     */
    @Test
    public void test4() {
        long taskId = 4;
        String taskName = "test" + taskId;
        ClickhouseTableCheckConfig config = config();
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        prepareJobContext(job);
        ClickhouseTableCheck executor = executor();
        executor.setValidateSql(executor.getValidateSql() + ";SELECT sleep(3);SELECT 3;");
        long start = System.currentTimeMillis();
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                executor.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
        Exception ex = null;
        try {
            executor.execute(job, task, config, 10000);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof TaskInterruptedException);
        long cost = System.currentTimeMillis() - start;
        Assert.assertTrue(cost >= 2000);
        Assert.assertTrue(cost < 10000);
        Assert.assertTrue(executor.getStartTime() != 0);
        Assert.assertTrue(executor.getEndTime() != 0);
        Assert.assertFalse(executor.isRunning());
        Assert.assertTrue(executor.isStopped());
    }

    /**
     * 测试任务超时
     */
    @Test
    public void test5() {
        long taskId = 5;
        String taskName = "test" + taskId;
        ClickhouseTableCheckConfig config = config();
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        prepareJobContext(job);
        ClickhouseTableCheck executor = executor();
        executor.setValidateSql(executor.getValidateSql()
                + ";SELECT sleep(3);SELECT 3;SELECT sleep(3);SELECT 6;SELECT sleep(3);SELECT 9;SELECT sleep(3);SELECT 12;");
        long start = System.currentTimeMillis();
        Exception ex = null;
        try {
            executor.execute(job, task, config, 10000);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof TaskTimeoutException);
        long cost = System.currentTimeMillis() - start;
        Assert.assertTrue(cost >= 10000);
        Assert.assertTrue(cost < 11000);
    }

    /**
     * 测试特殊表名
     */
    @Test
    public void test6() {
        long taskId = 6;
        String taskName = "test" + taskId;
        ClickhouseTableCheckConfig config = config();
        config.setClickhouseTable(" lifecycle_test_ck_sp");
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        prepareJobContext(job);
        ClickhouseTableCheck executor = executor();
        ClickhouseTableCheckResult result = executor.execute(job, task, config, 10000);
        Assert.assertTrue(result.getClickhouseTableExists());
        Assert.assertEquals(config.getClickhouseDatabase(), result.getClickhouseDatabase());
        Assert.assertEquals(config.getClickhouseTable(), result.getClickhouseTable());
        Assert.assertTrue(executor.getStartTime() != 0);
        Assert.assertTrue(executor.getEndTime() != 0);
        Assert.assertFalse(executor.isRunning());
        Assert.assertFalse(executor.isStopped());
        Assert.assertTrue(executor.isSuccess());
    }
}
