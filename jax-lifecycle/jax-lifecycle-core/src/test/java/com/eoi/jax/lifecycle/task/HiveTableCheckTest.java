package com.eoi.jax.lifecycle.task;

import com.eoi.jax.lifecycle.api.JobContext;
import com.eoi.jax.lifecycle.api.TaskContext;
import com.eoi.jax.lifecycle.exception.ErrorCode;
import com.eoi.jax.lifecycle.exception.TaskException;
import com.eoi.jax.lifecycle.exception.TaskInterruptedException;
import com.eoi.jax.lifecycle.exception.TaskTimeoutException;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 */
public class HiveTableCheckTest {
    private long jobId = 5;
    protected HiveTableCheckConfig config() {
        HiveTableCheckConfig config = new HiveTableCheckConfig();
        config.setHiveJdbcUrl(UnitTestEnv.hiveJdbcUrl);
        config.setHiveDatabase(UnitTestEnv.hiveDatabase);
        config.setHiveTable(UnitTestEnv.hiveTable);
        config.setHiveUsername(UnitTestEnv.hiveUsername);
        config.setHivePassword(UnitTestEnv.hivePassword);
        return config;
    }

    protected HiveTableCheck executor() {
        HiveTableCheck executor = new HiveTableCheck();
        executor.registerLogger(SystemOutPrintLogger.INSTANCE);
        return executor;
    }

    /**
     * 测试正常情况
     */
    @Test
    public void test1() {
        long taskId = 1;
        String taskName = "test" + taskId;
        HiveTableCheckConfig config = config();
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        HiveTableCheck executor = executor();
        HiveTableCheckResult result = executor.execute(job, task, config, 20000);
        Assert.assertTrue(result.getHiveTableExists());
        Assert.assertEquals(config.getHiveTable(), result.getHiveTable());
        Assert.assertEquals(config.getHiveDatabase(), result.getHiveDatabase());
    }

    /**
     * 测试异常情况
     */
    @Test
    public void test2() {
        long taskId = 2;
        String taskName = "test" + taskId;
        HiveTableCheckConfig config = config();
        config.setHiveJdbcUrl("************************************");
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        HiveTableCheck executor = executor();
        Throwable ex = null;
        try {
            executor.execute(job, task, config, 20000);
        } catch (Throwable e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof TaskException);
        Assert.assertEquals(ErrorCode.HIVE_TABLE_NOT_EXISTS, ((TaskException) ex).code());
    }

    /**
     * 测试终止任务
     */
    @Test
    public void test3() {
        long taskId = 3;
        String taskName = "test" + taskId;
        HiveTableCheckConfig config = config();
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        HiveTableCheck executor = executor();
        long start = System.currentTimeMillis();
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                executor.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
        Exception ex = null;
        try {
            executor.execute(job, task, config, 20000);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof TaskInterruptedException);
        long cost = System.currentTimeMillis() - start;
        Assert.assertTrue(cost >= 2000);
        Assert.assertTrue(cost < 3000);
    }

    /**
     * 测试任务超时
     */
    @Test
    public void test4() {
        long taskId = 4;
        String taskName = "test" + taskId;
        HiveTableCheckConfig config = config();
        TaskContext task = UnitTestEnv.taskContext(jobId, taskId, taskName, config, null);
        JobContext job = UnitTestEnv.jobContext(task);
        HiveTableCheck executor = executor();
        long start = System.currentTimeMillis();
        Exception ex = null;
        try {
            executor.execute(job, task, config, 2000);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof TaskTimeoutException);
        long cost = System.currentTimeMillis() - start;
        Assert.assertTrue(cost >= 2000);
        Assert.assertTrue(cost < 3000);
    }
}
