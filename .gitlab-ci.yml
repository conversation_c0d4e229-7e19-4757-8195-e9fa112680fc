stages:
  - check
  - deploy

default: # 默认镜像
  image: hub.eoitek.net/third/devops-tools-jdk21:1.3.1

variables:
  GIT_SUBMODULE_STRATEGY: recursive
  FTP_USERNAME: "eoideveloper"
  FTP_PASSWORD: "EoiFTPdeveloper2025"
  FTP_URL:
    description: "ftp上传地址"
    value: "ftp://*************/Product_Release/16.JaxSuper/dev"
  DEPLOY_MODE:
    description: "发布模式，0:代码检查(默认)；1:上传到FTP服务器；2:上传docker hub镜像；3:上传Small到FTP服务器；4:发布开发环境175"
    value: "0"

code-check:
  stage: check
  script:
    - git --version
    - mvn --version
    - mvn validate
    - mvn checkstyle:check
    - mvn compile pmd:check
  rules:
    - if: $DEPLOY_MODE == "0"
  tags:
    - jax

upload-ftp:
  stage: deploy
  script:
    - make upload-ftp
  rules:
    - if: $DEPLOY_MODE == "1"
  tags:
    - jax-build

upload-image:
  stage: deploy
  script:
    - make docker-image-all
  rules:
    - if: $DEPLOY_MODE == "2"
  tags:
    - jax-build

upload-ftp-small:
  stage: deploy
  script:
    - make upload-ftp-small
  rules:
    - if: $DEPLOY_MODE == "3"
  tags:
    - jax-build

upload-image-small:
  stage: deploy
  script:
    - make docker-image-small
  rules:
    - if: $DEPLOY_MODE == "4"
  tags:
    - jax-build

deploy-dev175:
  stage: deploy
  needs:
    - job: upload-image-small
  script:
    - bash docker-run.sh
  rules:
    - if: $DEPLOY_MODE == "4"
  tags:
    - jax-deploy