{"extends": "./src/.umi/tsconfig.json", "compilerOptions": {"rootDir": ".", "baseUrl": ".", "outDir": "dist", "sourceMap": true, "typeRoots": ["./node_modules/@types", "./types", "./src"], "strictFunctionTypes": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "noImplicitAny": false}, "include": ["src/**/*", ".*", "*.ts", "config/**/*", "types/**/*", "**/types/**/*"], "exclude": ["node_modules", "build", "dist"]}