#!/bin/sh

echo '容器工作目录------->'

DIR=$(cd "$(dirname "$0")" && pwd)

echo $DIR

echo '停止旧容器------->'

docker stop jax-super-web1
docker stop jax-super-web2

echo '删除旧容器------->'

docker rm -f jax-super-web1
docker rm -f jax-super-web2

echo '删除旧镜像------->'

docker rmi hub.eoitek.net/jax/jax-super-web

echo '容器重启------->'

docker run --name jax-super-web1 -d -p 9901:9901 -p 19991:19991 --privileged=true \
    -v $DIR/data1:/data \
    -v $DIR/logs1:/jax/logs \
    -v $DIR/jax/jar_dir:/jax/jax/jar_dir \
    -v $DIR/jax/application1.yml:/jax/jax/application.yml \
    -v $DIR/marayarn:/jax/marayarn \
    -v $DIR/hadoop:/jax/hadoop \
    -v $DIR/hadoopdev:/jax/hadoopdev \
    -v $DIR/spark-2.4.8:/jax/spark-2.4.8 \
    -v $DIR/flink-1.9.3:/jax/flink-1.9.3 \
    -v $DIR/flink-1.12.5:/jax/flink-1.12.5 \
    -v $DIR/flink-1.13.2:/jax/flink-1.13.2 \
    -e JAX_JAVA_OPTS="-Xmx2048m -Xms2048m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/heap.hprof -XX:-UseGCOverheadLimit" \
    $(cat /etc/hosts | grep -v "^$" | grep -v '#' |awk -F ' ' '{print "--add-host "$2":"$1}') \
    hub.eoitek.net/jax/jax-super-web:latest


docker run --name jax-super-web2 -d -p 9902:9902 -p 19992:19992 --privileged=true \
    -v $DIR/data2:/data \
    -v $DIR/logs2:/jax/logs \
    -v $DIR/jax/jar_dir:/jax/jax/jar_dir \
    -v $DIR/jax/application2.yml:/jax/jax/application.yml \
    -v $DIR/marayarn:/jax/marayarn \
    -v $DIR/hadoop:/jax/hadoop \
    -v $DIR/hadoopdev:/jax/hadoopdev \
    -v $DIR/spark-2.4.8:/jax/spark-2.4.8 \
    -v $DIR/flink-1.9.3:/jax/flink-1.9.3 \
    -v $DIR/flink-1.12.5:/jax/flink-1.12.5 \
    -v $DIR/flink-1.13.2:/jax/flink-1.13.2 \
    -e JAX_JAVA_OPTS="-Xmx2048m -Xms2048m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/heap.hprof -XX:-UseGCOverheadLimit" \
    $(cat /etc/hosts | grep -v "^$" | grep -v '#' |awk -F ' ' '{print "--add-host "$2":"$1}') \
    hub.eoitek.net/jax/jax-super-web:latest

echo '容器重启完成 !'

docker ps
