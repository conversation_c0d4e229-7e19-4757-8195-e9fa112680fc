{"name": "devops", "displayName": "智能运维中台", "version": "1.11", "private": true, "author": "lisheng.ma <<EMAIL>>", "serviceName": "", "scripts": {"build": "umi build", "circular": "node scripts/show-circular-dependencies.js", "commit": "npx cz", "dev": "umi dev", "dumi": "cross-env APP_ROOT=dumi dumi dev", "dumi-build": "cross-env APP_ROOT=dumi dumi build", "postinstall": "umi setup", "lint": "eslint --fix", "lint-staged": "lint-staged", "lint:fixJs": "eslint --fix --ext .js,.ts,.tsx ./src", "prepare": "husky install", "setup": "umi setup", "start": "npm run dev", "test": "cross-env TS_NODE_TRANSPILE_ONLY=yes jest --passWithNoTests"}, "lint-staged": {"src/**/*.{less,css,scss}": ["stylelint --fix"], "src/**/*.{md,json}": ["prettier --write"], "src/**/*.{ts,tsx}": ["eslint --max-warnings 0 --fix"], "src/**/*.{js,jsx}": ["eslint --fix"]}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "resolutions": {"react-error-overlay": "6.0.9"}, "dependencies": {"@ant-design/cssinjs": "^1.6.1", "@ant-design/icons": "^5.0.1", "@ant-design/plots": "^1.2.5", "@antv/ava": "^3.4.1", "@antv/data-wizard": "^2.0.4", "@antv/g2plot": "^2.4.29", "@antv/layout": "^0.3.17", "@antv/s2": "^1.55.8", "@antv/s2-react": "^1.47.0", "@antv/x6": "1.31.x", "@antv/x6-react-shape": "1.3.x", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@eoi/signature": "^1.0.7", "@json2csv/plainjs": "^7.0.6", "@tanstack/react-query": "^4.26.1", "@uiw/codemirror-extensions-color": "^4.19.9", "@uiw/codemirror-extensions-langs": "^4.19.9", "@uiw/react-codemirror": "^4.21.8", "@uiw/react-split": "^5.8.10", "allotment": "^1.18.1", "antd": "5.17.x", "asciidoctor.js": "^1.5.9", "bignumber.js": "^9.1.2", "blueimp-md5": "^2.19.0", "classnames": "^2.3.2", "dayjs": "^1.11.7", "dumi": "^2.2.1", "elkjs": "^0.9.3", "github-markdown-css": "^5.2.0", "hash.js": "^1.1.7", "highlight.js": "^11.7.0", "immer": "^10.1.1", "jsdiff": "^1.1.1", "json-as-xlsx": "^2.5.6", "json-bigint": "^1.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "marked": "^4.2.12", "moment": "^2.29.4", "platform": "^1.3.6", "pubsub-js": "^1.9.4", "ramda": "^0.28.0", "rc-virtual-list": "^3.14.8", "react": "^18.2.0", "react-activation": "^0.12.2", "react-codemirror-merge": "^4.21.21", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-highlight-words": "^0.20.0", "react-resizable": "^3.0.5", "reflect-metadata": "^0.1.13", "resize-observer-polyfill": "^1.5.1", "sortablejs": "^1.15.0", "sql-formatter": "^12.2.0", "tailwind-merge": "^1.12.0", "umi": "^4.0.59", "umi-plugin-keep-alive": "0.0.1-beta.35", "umi-request": "^1.4.0", "use-immer": "^0.10.0", "xterm": "^5.1.0", "xterm-addon-fit": "^0.7.0", "xterm-addon-search": "^0.11.0", "zustand": "^4.3.6"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@meoc/eslint-config-format": "^0.1.1", "@testing-library/jest-dom": "^5", "@testing-library/react": "^14", "@trivago/prettier-plugin-sort-imports": "^4.1.1", "@types/jest": "^29", "@types/lodash-es": "^4.17.7", "@types/marked": "^4.0.8", "@types/node": "^18.15.3", "@types/pubsub-js": "^1.8.3", "@types/ramda": "^0.28.23", "@types/react": "^18.0.33", "@types/sortablejs": "^1.15.1", "@types/testing-library__jest-dom": "^5.14.5", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "autoprefixer": "^10.4.14", "commitizen": "^4.3.0", "cross-env": "^7", "cz-customizable": "^7.0.0", "eslint": "^8.36.0", "eslint-config-prettier": "^8.7.0", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-simple-import-sort": "^10.0.0", "git-cmd-repo-info": "^1.0.7", "gulp": "^4.0.2", "gulp-json-editor": "^2.5.7", "husky": "^8.0.3", "jest": "^29", "jest-environment-jsdom": "^29", "jsdom-worker": "^0.3.0", "lint-staged": "^13.2.0", "madge": "^6.0.0", "msw": "^1.1.0", "postcss": "^8.4.21", "postcss-less": "^6.0.0", "prettier": "^2.8.4", "react-error-overlay": "6.0.11", "stylelint": "^14.4.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^30.0.1", "stylelint-less": "^1.0.6", "tailwindcss": "^3.2.7", "ts-node": "^10", "tslib": "^2.5.0", "typescript": "^4.9.5", "web-worker": "^1.3.0", "yargs": "^17.7.2"}, "engines": {"node": ">=14.0.0"}, "publishConfig": {"access": "public"}, "msw": {"workerDirectory": "public"}, "optionalDependencies": {"@esbuild/linux-x64": "*"}}