import { Config, configUmiAlias, createConfig } from 'umi/test'

const config = createConfig({
  target: 'browser',
  jsTransformer: 'esbuild',
  // config opts for esbuild , it will pass to esbuild directly
  jsTransformerOpts: { jsx: 'automatic' }
})

export default async () => {
  try {
    return (await configUmiAlias({
      ...config,
      transform: {
        ...config.transform,
        '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
          '<rootDir>/fileTransformer.js'
      },
      setupFilesAfterEnv: ['<rootDir>/jest-setup.ts', 'jsdom-worker'],
      collectCoverageFrom: [
        'src/**/*.{ts,js,tsx,jsx}',
        '!src/.umi/**',
        '!src/.umi-test/**',
        '!src/.umi-production/**'
      ]
      // if you require some es-module npm package, please uncomment below line and insert your package name
      // transformIgnorePatterns: ['node_modules/(?!.*(lodash-es|your-es-pkg-name)/)']
    })) as Config.InitialOptions
  } catch (e) {
    console.log(e)
    throw e
  }
}
